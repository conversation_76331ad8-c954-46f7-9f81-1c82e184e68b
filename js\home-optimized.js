/**
 * Home Page Optimized Bundle
 * Combines critical home page functionality for better performance
 * This file should be loaded with high priority
 */

(function() {
    'use strict';

    // Performance monitoring
    const perfStart = performance.now();
    
    // Shared utilities
    const utils = {
        // Throttle function for performance
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            }
        },

        // Debounce function for performance
        debounce: function(func, wait, immediate) {
            let timeout;
            return function() {
                const context = this, args = arguments;
                const later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },

        // Check if element is in viewport
        isInViewport: function(element, threshold = 0.1) {
            const rect = element.getBoundingClientRect();
            const windowHeight = window.innerHeight || document.documentElement.clientHeight;
            const windowWidth = window.innerWidth || document.documentElement.clientWidth;
            
            return (
                rect.top <= windowHeight * (1 + threshold) &&
                rect.bottom >= -windowHeight * threshold &&
                rect.left <= windowWidth * (1 + threshold) &&
                rect.right >= -windowWidth * threshold
            );
        },

        // Intersection Observer with fallback
        createObserver: function(callback, options = {}) {
            if ('IntersectionObserver' in window) {
                return new IntersectionObserver(callback, {
                    rootMargin: options.rootMargin || '50px 0px',
                    threshold: options.threshold || 0.1,
                    ...options
                });
            }
            return null;
        }
    };

    // Enhanced Toggle Slider (optimized version)
    const ToggleSlider = {
        init: function() {
            const toggleOptions = document.querySelectorAll('.modern-toggle-slider .toggle-option');
            if (toggleOptions.length === 0) return;

            // Position slider indicator
            const positionSliderIndicator = (index) => {
                const sliderIndicator = document.querySelector('.slider-indicator');
                if (!sliderIndicator) return;

                if (index === 0) {
                    sliderIndicator.style.left = '3px';
                    sliderIndicator.style.right = 'auto';
                } else {
                    sliderIndicator.style.left = 'auto';
                    sliderIndicator.style.right = '3px';
                }
            };

            // Initialize active state
            let activeIndex = 0;
            toggleOptions.forEach((option, index) => {
                if (option.classList.contains('active')) {
                    activeIndex = index;
                }
            });

            // Set initial position
            requestAnimationFrame(() => {
                positionSliderIndicator(activeIndex);
            });

            // Add click handlers
            toggleOptions.forEach((option, index) => {
                option.addEventListener('click', function() {
                    if (this.classList.contains('active')) return;

                    // Update active states
                    toggleOptions.forEach(opt => opt.classList.remove('active'));
                    document.querySelectorAll('.tab-pane').forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });

                    this.classList.add('active');
                    positionSliderIndicator(index);

                    // Show target pane
                    const target = this.getAttribute('data-target');
                    const targetPane = document.querySelector(target);
                    if (targetPane) {
                        setTimeout(() => {
                            targetPane.classList.add('show', 'active');
                        }, 100);
                    }
                });
            });
        }
    };

    // Optimized Animation Controller
    const AnimationController = {
        animatedElements: new Set(),
        
        init: function() {
            this.setupScrollAnimations();
            this.setupHoverEffects();
        },

        setupScrollAnimations: function() {
            const elements = document.querySelectorAll('[data-anime]');
            if (elements.length === 0) return;

            const observer = utils.createObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.animatedElements.has(entry.target)) {
                        this.animatedElements.add(entry.target);
                        this.triggerAnimation(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            });

            if (observer) {
                elements.forEach(el => observer.observe(el));
            } else {
                // Fallback for browsers without Intersection Observer
                elements.forEach(el => {
                    if (utils.isInViewport(el)) {
                        this.triggerAnimation(el);
                    }
                });
            }
        },

        triggerAnimation: function(element) {
            const animeData = element.getAttribute('data-anime');
            if (!animeData) return;

            try {
                const config = JSON.parse(animeData);
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
                element.style.transition = `all ${config.duration || 600}ms ${config.easing || 'ease-out'}`;
                
                // Handle child animations
                if (config.el === 'childs') {
                    const children = element.children;
                    Array.from(children).forEach((child, index) => {
                        const delay = (config.delay || 0) + (index * (config.staggervalue || 100));
                        setTimeout(() => {
                            child.style.opacity = '1';
                            child.style.transform = 'translateY(0)';
                            child.style.transition = `all ${config.duration || 600}ms ${config.easing || 'ease-out'}`;
                        }, delay);
                    });
                }
            } catch (e) {
                console.warn('Invalid anime data:', animeData);
            }
        },

        setupHoverEffects: function() {
            // Optimized hover effects for cards and buttons
            const hoverElements = document.querySelectorAll('.hover-box, .btn, .metric-card');
            
            hoverElements.forEach(element => {
                element.addEventListener('mouseenter', this.handleHoverEnter);
                element.addEventListener('mouseleave', this.handleHoverLeave);
            });
        },

        handleHoverEnter: function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.2s ease-out';
        },

        handleHoverLeave: function() {
            this.style.transform = 'translateY(0)';
        }
    };

    // Performance-optimized initialization
    const initializeHomePage = () => {
        // Initialize components in order of priority
        ToggleSlider.init();
        AnimationController.init();
        
        // Log performance
        const perfEnd = performance.now();
        console.log(`Home page optimized bundle loaded in ${(perfEnd - perfStart).toFixed(2)}ms`);
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeHomePage);
    } else {
        initializeHomePage();
    }

    // Expose utilities for other scripts
    window.HomePageUtils = utils;

})();
