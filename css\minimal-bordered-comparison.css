/* Ultra-minimal bordered comparison design */

.future-comparison {
    padding: 100px 0;
    background-color: #fafafa;
    position: relative;
}

.comparison-container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section header */
.minimal-header {
    text-align: center;
    margin-bottom: 80px;
}

.minimal-header .tag {
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    color: #e958a1;
    margin-bottom: 15px;
    display: inline-block;
}

.minimal-header h2 {
    font-size: 42px;
    font-weight: 700;
    line-height: 1.2;
    color: #333;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
}

.minimal-header h2 span {
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.minimal-header p {
    font-size: 18px;
    line-height: 1.6;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Comparison cards */
.comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 80px;
}

.comparison-card {
    background: white;
    border-radius: 12px;
    padding: 40px;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid #eaeaea;
}

/* Traditional card */
.comparison-card.traditional {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
}

.comparison-card.traditional:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
}

/* Adzeta card with gradient border on hover */
.comparison-card.adzeta {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
    position: relative;
}

.comparison-card.adzeta:before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 12px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.comparison-card.adzeta:hover {
    box-shadow: 0 10px 30px rgba(233, 88, 161, 0.15);
    transform: translateY(-5px);
    border-color: transparent;
}

.comparison-card.adzeta:hover:before {
    opacity: 1;
}

.comparison-card.adzeta:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    border-radius: 11px;
    z-index: -1;
}

.card-label {
    display: inline-block;
    font-size: 13px;
    font-weight: 600;
    padding: 6px 15px;
    border-radius: 30px;
    margin-bottom: 15px;
}

.traditional-label {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.adzeta-label {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.recommended-tag {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 5px 12px;
    border-radius: 20px;
}

.card-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.card-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
}

/* Feature list */
.feature-list {
    margin-bottom: 0;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.feature-item:last-child {
    margin-bottom: 0;
}

.feature-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.feature-icon.negative {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.feature-icon.positive {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.feature-text {
    font-size: 16px;
    color: #555;
    line-height: 1.5;
}

/* Results section */
.results-section {
    background: white;
    border-radius: 12px;
    padding: 60px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
    text-align: center;
    border: 1px solid #eaeaea;
}

.results-title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

.results-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    margin-bottom: 0;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 48px;
    font-weight: 700;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .comparison-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .results-section {
        padding: 40px 30px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

@media (max-width: 767px) {
    .future-comparison {
        padding: 60px 0;
    }
    
    .minimal-header {
        margin-bottom: 50px;
    }
    
    .minimal-header h2 {
        font-size: 32px;
    }
    
    .comparison-card {
        padding: 30px;
    }
    
    .card-title {
        font-size: 22px;
    }
    
    .stat-value {
        font-size: 36px;
    }
}
