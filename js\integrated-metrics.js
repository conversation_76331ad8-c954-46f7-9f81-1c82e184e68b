// Integrated Metrics Visualization
document.addEventListener('DOMContentLoaded', function() {
    // Check if metrics section exists
    if (!document.querySelector('.metrics-section')) return;

    // Initialize on scroll
    window.addEventListener('scroll', checkAndInitialize);

    // Also check on initial load
    setTimeout(checkAndInitialize, 500);

    // Counter animation function
    function animateCounters() {
        const counters = document.querySelectorAll('.counter-number');
        const speed = 1000; // Animation duration in ms

        counters.forEach(counter => {
            const target = parseFloat(counter.getAttribute('data-target'));
            const unit = counter.getAttribute('data-unit') || '';
            let count = 0;
            const increment = target / (speed / 16); // 60fps

            const updateCount = () => {
                if (count < target) {
                    count += increment;
                    // Don't exceed the target
                    if (count > target) count = target;

                    // Format the display based on unit
                    if (unit === 'x') {
                        counter.textContent = Math.floor(count) + unit;
                    } else if (unit === '%') {
                        counter.textContent = Math.floor(count) + unit;
                    } else {
                        counter.textContent = Math.floor(count) + unit;
                    }

                    requestAnimationFrame(updateCount);
                } else {
                    counter.textContent = target + unit;
                }
            };

            updateCount();
        });
    }

    // Initialize mini charts in metric cards
    function initMiniCharts() {
        // ROAS Chart - Upward trend (purple gradient)
        const roasCtx = document.getElementById('roas-chart').getContext('2d');
        const roasGradient = roasCtx.createLinearGradient(0, 0, 0, 100);
        roasGradient.addColorStop(0, 'rgba(143, 118, 245, 0.5)');
        roasGradient.addColorStop(1, 'rgba(143, 118, 245, 0.1)');

        new Chart(roasCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', ''],
                datasets: [{
                    data: [10, 15, 25, 30, 45, 60],
                    borderColor: '#8f76f5',
                    backgroundColor: roasGradient,
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutQuart'
                }
            }
        });

        // CAC Chart - Downward trend (pink gradient)
        const cacCtx = document.getElementById('cac-chart').getContext('2d');
        const cacGradient = cacCtx.createLinearGradient(0, 0, 0, 100);
        cacGradient.addColorStop(0, 'rgba(233, 88, 161, 0.5)');
        cacGradient.addColorStop(1, 'rgba(233, 88, 161, 0.1)');

        new Chart(cacCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', ''],
                datasets: [{
                    data: [60, 50, 40, 35, 25, 20],
                    borderColor: '#e958a1',
                    backgroundColor: cacGradient,
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutQuart'
                }
            }
        });

        // Revenue Chart - Upward trend (purple gradient)
        const revenueCtx = document.getElementById('revenue-chart').getContext('2d');
        const revenueGradient = revenueCtx.createLinearGradient(0, 0, 0, 100);
        revenueGradient.addColorStop(0, 'rgba(143, 118, 245, 0.5)');
        revenueGradient.addColorStop(1, 'rgba(143, 118, 245, 0.1)');

        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', ''],
                datasets: [{
                    data: [15, 25, 30, 40, 50, 65],
                    borderColor: '#8f76f5',
                    backgroundColor: revenueGradient,
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutQuart'
                }
            }
        });

        // Ad Waste Chart - Downward trend (pink gradient)
        const adWasteCtx = document.getElementById('ad-waste-chart').getContext('2d');
        const adWasteGradient = adWasteCtx.createLinearGradient(0, 0, 0, 100);
        adWasteGradient.addColorStop(0, 'rgba(233, 88, 161, 0.5)');
        adWasteGradient.addColorStop(1, 'rgba(233, 88, 161, 0.1)');

        new Chart(adWasteCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', ''],
                datasets: [{
                    data: [70, 60, 45, 35, 25, 15],
                    borderColor: '#e958a1',
                    backgroundColor: adWasteGradient,
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: false },
                    y: { display: false }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // Initialize comparison bar chart
    function initComparisonChart() {
        const comparisonCtx = document.getElementById('comparison-chart').getContext('2d');
        new Chart(comparisonCtx, {
            type: 'bar',
            data: {
                labels: ['Traditional', 'Adzeta AI'],
                datasets: [{
                    label: 'ROAS Comparison',
                    data: [1, 4],
                    backgroundColor: [
                        'rgba(149, 165, 166, 0.7)',
                        'rgba(143, 118, 245, 0.7)'
                    ],
                    borderColor: [
                        'rgba(149, 165, 166, 1)',
                        'rgba(143, 118, 245, 1)'
                    ],
                    borderWidth: 1,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y + 'x ROAS';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + 'x';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // Initialize donut chart
    function initDonutChart() {
        const donutCtx = document.getElementById('donut-chart').getContext('2d');
        new Chart(donutCtx, {
            type: 'doughnut',
            data: {
                labels: ['Wasted Spend', 'Effective Spend'],
                datasets: [{
                    data: [42, 58],
                    backgroundColor: [
                        'rgba(233, 88, 161, 0.7)',
                        'rgba(143, 118, 245, 0.7)'
                    ],
                    borderColor: [
                        'rgba(233, 88, 161, 1)',
                        'rgba(143, 118, 245, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    // Check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
        );
    }

    // Check if metrics section is in viewport and initialize
    function checkAndInitialize() {
        const metricsSection = document.querySelector('.metrics-section');

        if (metricsSection && isInViewport(metricsSection)) {
            // Initialize all charts
            initMiniCharts();
            initComparisonChart();
            initDonutChart();

            // Animate counters
            animateCounters();

            // Remove scroll listener once initialized
            window.removeEventListener('scroll', checkAndInitialize);
        }
    }
});
