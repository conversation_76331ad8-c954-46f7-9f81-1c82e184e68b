/* Cache Status Indicator Styles */

.cache-status-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.status-light {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
    position: relative;
    animation: pulse 2s infinite;
}

.status-active {
    background: linear-gradient(45deg, #28a745, #20c997);
    box-shadow: 0 0 15px rgba(40, 167, 69, 0.5);
}

.status-inactive {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    box-shadow: 0 0 15px rgba(220, 53, 69, 0.5);
    animation: pulse-warning 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 15px rgba(40, 167, 69, 0.5);
    }
    50% {
        box-shadow: 0 0 25px rgba(40, 167, 69, 0.8);
    }
    100% {
        box-shadow: 0 0 15px rgba(40, 167, 69, 0.5);
    }
}

@keyframes pulse-warning {
    0% {
        box-shadow: 0 0 15px rgba(220, 53, 69, 0.5);
    }
    50% {
        box-shadow: 0 0 25px rgba(220, 53, 69, 0.8);
    }
    100% {
        box-shadow: 0 0 15px rgba(220, 53, 69, 0.5);
    }
}

/* Master Cache Toggle Styles */
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.form-check-input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Cache File List Styles */
.list-group-item {
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    border-left-color: #007bff;
    background-color: #f8f9fa;
}

/* Cache Statistics Cards */
.stat-card {
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

/* Cache Actions */
.cache-actions .btn {
    transition: all 0.3s ease;
}

.cache-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Performance Mode Cards */
.setting-card {
    transition: all 0.3s ease;
}

.setting-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

/* Feature Status Icons */
.feature-status i.fa-check-circle {
    font-size: 1.2em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .cache-status-indicator {
        margin-top: 10px;
    }
    
    .status-light {
        width: 16px;
        height: 16px;
    }
    
    .cache-actions .btn {
        margin-bottom: 10px;
        width: 100%;
    }
}

/* Loading states */
.btn[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Cache disabled overlay */
.cache-disabled-overlay {
    position: relative;
}

.cache-disabled-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    z-index: 1;
    pointer-events: none;
}

/* Badge styles */
.badge {
    font-size: 0.75em;
    padding: 0.35em 0.65em;
}

/* Alert enhancements */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}
