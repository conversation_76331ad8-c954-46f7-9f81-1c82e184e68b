/* AdZeta Admin Panel - Apple-Inspired Professional Design */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=SF+Pro+Display:wght@300;400;500;600;700&display=swap');

:root {
    /* Apple-inspired Color System */
    --primary-color: #007AFF;
    --primary-dark: #0056CC;
    --primary-light: #4DA2FF;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --danger-color: #FF3B30;
    --info-color: #5AC8FA;

    /* Neutral Palette */
    --white: #FFFFFF;
    --gray-50: #F9F9F9;
    --gray-100: #F2F2F7;
    --gray-200: #E5E5EA;
    --gray-300: #D1D1D6;
    --gray-400: #C7C7CC;
    --gray-500: #AEAEB2;
    --gray-600: #8E8E93;
    --gray-700: #636366;
    --gray-800: #48484A;
    --gray-900: #1C1C1E;
    --black: #000000;

    /* Legacy compatibility */
    --dark-color: var(--gray-800);
    --light-color: var(--gray-50);
    --border-color: var(--gray-200);
    --text-color: var(--gray-800);
    --text-muted: var(--gray-600);

    /* Layout */
    --sidebar-width: 280px;
    --header-height: 56px; /* Reduced from 64px */

    /* Border Radius */
    --radius-xs: 4px;
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-2xl: 24px;

    /* Typography */
    --font-family-primary: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    /* Shadows */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);

    /* Transitions */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);

    /* Blur Effects */
    --blur-sm: blur(4px);
    --blur-md: blur(8px);
    --blur-lg: blur(16px);
}

/* Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    background-color: var(--gray-50);
    margin: 0;
    padding: 0;
    color: var(--text-color);
    line-height: 1.6;
    font-size: 14px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Apple-Inspired Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 12px;
    pointer-events: none;
}

.notification {
    pointer-events: auto;
    min-width: 320px;
    max-width: 400px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: var(--blur-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    transform: translateY(-100%);
    opacity: 0;
}

.notification-content {
    display: flex;
    align-items: flex-start;
    padding: 16px 20px;
    gap: 12px;
}

.notification-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 12px;
    margin-top: 2px;
}

.notification-message {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    color: var(--gray-800);
}

.notification-close {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 4px;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    font-size: 12px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* Notification Types */
.notification-success .notification-icon {
    background: var(--success-color);
    color: white;
}

.notification-warning .notification-icon {
    background: var(--warning-color);
    color: white;
}

.notification-danger .notification-icon {
    background: var(--danger-color);
    color: white;
}

.notification-info .notification-icon {
    background: var(--info-color);
    color: white;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
}

/* Login Page */
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.login-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    padding: 3rem;
    width: 100%;
    max-width: 420px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.brand-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
    margin: 0 auto 1.5rem;
}

/* Admin Layout */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

/* Apple-Inspired Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: var(--blur-md);
    border-right: 1px solid var(--gray-200);
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    transition: all var(--transition-normal);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar.collapsed .brand-text,
.sidebar.collapsed .nav-link-text,
.sidebar.collapsed .user-details {
    opacity: 0;
    visibility: hidden;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .brand-logo {
    display: none; /* Hide logo when collapsed */
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem;
}

.sidebar.collapsed .sidebar-header {
    padding: 1rem 0.5rem; /* Reduce padding when collapsed */
    text-align: center;
}

.sidebar.collapsed .nav-link i {
    margin-right: 0;
}

.sidebar.collapsed .sidebar-brand .brand-icon {
    margin-right: 0;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--primary-color);
    color: white;
    position: relative;
}

.sidebar-toggle {
    position: absolute;
    top: 1.5rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.sidebar.collapsed .sidebar-toggle {
    right: 50%;
    transform: translateX(50%);
}

.sidebar.collapsed .sidebar-toggle:hover {
    transform: translateX(50%) scale(1.05);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    font-weight: 600;
    font-size: 1.25rem;
}

.sidebar-brand .brand-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-weight: 700;
    flex-shrink: 0;
}

.sidebar-brand .brand-text {
    transition: all 0.3s ease;
    white-space: nowrap;
}

.nav-link-text {
    transition: all 0.3s ease;
    white-space: nowrap;
}

.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
}

.nav-item {
    margin: 0 1rem 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-weight: 500;
    font-size: 14px;
    position: relative;
    margin: 2px 0;
}

.nav-link:hover {
    background: var(--gray-100);
    color: var(--gray-900);
    transform: translateX(2px);
}

.nav-link.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
    transform: translateX(4px);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: var(--primary-color);
    border-radius: 0 2px 2px 0;
}

.nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    flex: 1;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

.main-content.sidebar-collapsed {
    margin-left: 70px;
}

.content-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: var(--blur-md);
    border-bottom: 1px solid var(--gray-200);
    padding: 16px 24px; /* Reduced from 24px 32px */
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
    min-height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.content-header h1 {
    margin: 0;
    font-size: 1.5rem; /* Reduced from default */
    font-weight: 600;
    color: var(--gray-900);
    line-height: 1.2;
}

.content-header .breadcrumb {
    margin: 0;
    background: none;
    padding: 0;
    font-size: 0.875rem;
}

.content-header .page-description {
    margin: 0;
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 400;
}

.content-body {
    padding: 2rem;
}

/* Apple-Inspired Cards */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: var(--blur-sm);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    margin-bottom: 24px;
    transition: all var(--transition-fast);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-body {
    padding: 24px;
}

.card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);
    letter-spacing: -0.01em;
}

/* Apple-Inspired Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-fast);
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
    font-family: var(--font-family-primary);
    letter-spacing: -0.01em;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    color: var(--text-muted);
    border-color: var(--border-color);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--light-color);
    border-color: var(--text-muted);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 0.875rem 1.75rem;
    font-size: 1rem;
}

/* Apple-Inspired Forms */
.form-control {
    display: block;
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    transition: all var(--transition-fast);
    font-size: 14px;
    font-family: var(--font-family-primary);
    color: var(--gray-900);
    line-height: 1.4;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
    background: var(--white);
}

.form-control::placeholder {
    color: var(--gray-500);
    font-weight: 400;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--gray-900);
    font-size: 14px;
    letter-spacing: -0.01em;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: #fafbfc;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background: #f8fafc;
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-success {
    background: #dcfce7;
    color: #166534;
}

.badge-warning {
    background: #fef3c7;
    color: #92400e;
}

.badge-secondary {
    background: #f3f4f6;
    color: #374151;
}

/* Spinner */
.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #f3f4f6;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Post Editor Styles */
.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.search-preview {
    font-family: arial, sans-serif;
}

.search-title {
    color: #1a0dab;
    font-size: 18px;
    line-height: 1.3;
    margin-bottom: 2px;
}

.search-url {
    font-size: 14px;
    margin-bottom: 2px;
}

.search-description {
    color: #545454;
    font-size: 14px;
    line-height: 1.4;
}

.editor-footer {
    position: fixed;
    bottom: 0;
    left: var(--sidebar-width);
    right: 0;
    background: white;
    border-top: 1px solid var(--border-color);
    padding: 1rem 2rem;
    z-index: 100;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-footer.sidebar-collapsed {
    left: 70px;
}

.editor-footer.hidden {
    transform: translateY(100%);
}

.footer-toggle {
    position: fixed;
    bottom: 1rem;
    right: 2rem;
    background: var(--primary-color);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    transition: all 0.2s ease;
    z-index: 101;
}

.footer-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

/* ===== NEW FULLSCREEN EDITOR UX ===== */

.fullscreen-editor-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 1000 !important; /* Lower z-index so modals appear above */
    background: white !important;
    display: flex !important;
    flex-direction: column !important;
}

.fullscreen-floating-toolbar {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
    z-index: 10000 !important;
}

.fullscreen-content-area {
    flex: 1 !important;
    padding: 60px 40px 40px 40px !important;
    overflow-y: auto !important;
    display: flex !important;
    justify-content: center !important;
}

.fullscreen-content-area #editorjs {
    width: 100% !important;
    max-width: 900px !important;
    min-height: calc(100vh - 140px) !important;
}

.fullscreen-content-area .codex-editor {
    min-height: calc(100vh - 200px) !important;
}

.fullscreen-content-area .codex-editor__redactor {
    padding-bottom: 100px !important;
    min-height: calc(100vh - 250px) !important;
}

.fullscreen-content-area .ce-block {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

.fullscreen-content-area .ce-block__content,
.fullscreen-content-area .ce-paragraph,
.fullscreen-content-area .ce-header {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Hide other elements when in fullscreen */
body.editor-fullscreen-active .sidebar,
body.editor-fullscreen-active .navbar,
body.editor-fullscreen-active .main-content {
    display: none !important;
}

/* Fullscreen escape hint */
.fullscreen-editor-overlay::before {
    content: 'Press ESC to exit fullscreen';
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10001;
    opacity: 0;
    animation: fadeInOut 3s ease-in-out;
}

/* Ensure modals appear above fullscreen */
.modal {
    z-index: 10050 !important;
}

.modal-backdrop {
    z-index: 10040 !important;
}

/* AI Generation Modal specific */
#aiGenerationModal {
    z-index: 10060 !important;
}

#aiGenerationModal .modal-backdrop {
    z-index: 10050 !important;
}

/* Source view styles */
.source-view-header {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 0.375rem 0.375rem 0 0;
    font-size: 0.875rem;
}

#sourceEditor {
    border-radius: 0 0 0.375rem 0.375rem;
    border-top: none;
    resize: vertical;
    font-family: 'Courier New', Monaco, 'Cascadia Code', monospace;
    font-size: 14px;
    line-height: 1.5;
    tab-size: 2;
}

/* AI Generation Modal styles */
#aiGenerationModal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

#aiGenerationModal .alert {
    margin-bottom: 0;
}

#aiGenerationModal .form-label {
    font-weight: 600;
    color: var(--gray-700);
}

#aiGenerationModal .form-text {
    font-size: 0.8rem;
    color: var(--gray-600);
}

/* Content editor toolbar enhancements */
.content-editor-toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    border-radius: 0.375rem 0.375rem 0 0;
}

.content-editor-toolbar .btn {
    border-radius: 0.25rem;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    transition: all 0.2s ease;
}

.content-editor-toolbar .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-editor-toolbar .btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.content-editor-toolbar .btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.content-editor-toolbar .btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: #000;
}

.content-editor-toolbar .btn-warning:hover {
    background-color: #ffb84d;
    border-color: #ffb84d;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 149, 0, 0.3);
}

.content-editor-toolbar .btn-outline-secondary {
    border-color: var(--gray-400);
    color: var(--gray-600);
}

.content-editor-toolbar .btn-outline-secondary:hover {
    background-color: var(--gray-600);
    border-color: var(--gray-600);
    color: white;
}

/* AI Generation status animations */
@keyframes aiGenerating {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

.ai-generating {
    animation: aiGenerating 1.5s ease-in-out infinite;
}

/* Source view syntax highlighting (basic) */
#sourceEditor {
    color: var(--gray-800);
    background-color: #fafafa;
}

#sourceEditor:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Template Selector Styles */
.template-selector {
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.template-categories {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.template-categories .btn {
    border-radius: 20px;
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
}

.template-categories .btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.template-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.template-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
    transform: translateY(-2px);
}

.template-card.selected {
    border-color: var(--primary-color);
    background: #f0f4ff;
}

.template-preview {
    text-align: center;
    margin-bottom: 1rem;
}

.template-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    font-size: 1.5rem;
}

.template-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.template-description {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.template-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.template-features .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.template-actions {
    display: flex;
    gap: 0.5rem;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content-body {
        padding: 1rem;
    }

    .editor-footer {
        left: 0;
        padding: 1rem;
    }

    .editor-footer .d-flex {
        flex-direction: column;
        gap: 1rem;
    }

    .editor-footer .d-flex:last-child {
        flex-direction: row;
        justify-content: center;
    }

    /* Mobile sidebar header improvements */
    .sidebar-header {
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .sidebar-brand {
        flex: 1;
        min-width: 0; /* Allow text to truncate */
    }

    .sidebar-brand .brand-icon {
        width: 32px;
        height: 32px;
        margin-right: 0.5rem;
        flex-shrink: 0;
    }

    .sidebar-brand .brand-text {
        font-size: 1rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .sidebar-toggle {
        position: static;
        margin-left: 0.5rem;
        flex-shrink: 0;
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .sidebar-toggle:hover {
        transform: scale(1.05);
    }

    /* Ensure collapsed state works on mobile */
    .sidebar.collapsed .sidebar-header {
        justify-content: center;
        padding: 1rem 0.5rem;
    }

    .sidebar.collapsed .sidebar-toggle {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 0;
    }

    .sidebar.collapsed .sidebar-toggle:hover {
        transform: translateY(-50%) scale(1.05);
    }
}

/* Extra small devices (phones, less than 480px) */
@media (max-width: 479px) {
    .sidebar-header {
        padding: 0.75rem;
    }

    .sidebar-brand .brand-icon {
        width: 28px;
        height: 28px;
        margin-right: 0.4rem;
    }

    .sidebar-brand .brand-text {
        font-size: 0.9rem;
    }

    .sidebar-toggle {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    /* Ensure content header is also responsive */
    .content-header {
        padding: 12px 16px;
    }

    .content-header h1 {
        font-size: 1.25rem;
    }

    /* Mobile menu toggle button in content header */
    .mobile-menu-toggle {
        margin-right: 0.75rem;
        padding: 0.5rem;
        border-radius: 6px;
        border: 1px solid var(--gray-300);
        background: white;
        color: var(--gray-700);
        transition: all 0.2s ease;
    }

    .mobile-menu-toggle:hover {
        background: var(--gray-100);
        border-color: var(--gray-400);
        color: var(--gray-900);
    }

    .mobile-menu-toggle:focus {
        box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.25);
        outline: none;
    }

    /* Mobile sidebar backdrop */
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar.show + .sidebar-backdrop,
    .sidebar-backdrop.show {
        opacity: 1;
        visibility: visible;
    }

    /* Improve sidebar z-index on mobile */
    .sidebar.show {
        z-index: 1001;
    }
}

/* Media Library Styles */
.upload-dropzone {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
    background: var(--light-color);
}

.upload-dropzone:hover,
.upload-dropzone.drag-over {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

.media-item {
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.media-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.media-item.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

.media-thumbnail {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
}

.media-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.media-item.selected .media-overlay {
    opacity: 1;
}

.media-overlay i {
    color: white;
    font-size: 1.5rem;
}

.media-info {
    padding: 0.5rem;
}

.media-name {
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-size {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.featured-image-container {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    background: var(--light-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.featured-image-container:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.featured-image-container.has-image {
    border-style: solid;
    border-color: var(--success-color);
    background: white;
    padding: 0;
}

.featured-image-preview {
    position: relative;
    border-radius: 6px;
    overflow: hidden;
}

.featured-image-preview img {
    width: 100%;
    height: auto;
    max-height: 200px;
    object-fit: cover;
}

.featured-image-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: flex;
    gap: 0.25rem;
}

.featured-image-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Media Manager Styles */
.media-manager-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

.media-toolbar {
    background: var(--light-color);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.bulk-actions-bar {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

.media-grid-item {
    position: relative;
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.media-grid-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.media-grid-item.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

.media-checkbox {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 0.25rem;
}

.media-thumbnail {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
}

.media-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.media-grid-item:hover .media-thumbnail img {
    transform: scale(1.05);
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-grid-item:hover .media-overlay {
    opacity: 1;
}

.media-actions {
    display: flex;
    gap: 0.5rem;
}

.media-actions .btn {
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.media-info {
    padding: 1rem;
}

.media-name {
    font-weight: 500;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.5rem;
}

.media-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.media-list-thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
}

.media-list-name {
    cursor: pointer;
    font-weight: 500;
}

.media-list-name:hover {
    color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }

    .media-toolbar .row > div {
        margin-bottom: 0.5rem;
    }

    .bulk-actions-bar .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Users Management Styles */
.users-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

.users-toolbar {
    background: var(--light-color);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
}

.user-name-cell {
    min-width: 150px;
}

.users-container .table th {
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 2px solid var(--border-color);
}

.users-container .table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.users-container .table tbody tr:hover {
    background-color: var(--light-color);
}

/* User Profile Styles */
.user-profile-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    border-radius: 8px 8px 0 0;
}

.user-profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid white;
    object-fit: cover;
}

.user-profile-info h3 {
    margin-bottom: 0.5rem;
}

.user-profile-meta {
    opacity: 0.9;
    font-size: 0.875rem;
}

.user-stats {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
}

.user-stat {
    text-align: center;
}

.user-stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    display: block;
}

.user-stat-label {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* User Form Styles */
.user-form-section {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.user-form-section h5 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.avatar-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.avatar-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
}

.bio-editor {
    min-height: 120px;
    resize: vertical;
}

/* Role and Permission Styles */
.role-card {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.role-card:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.role-card.selected {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

.role-card h6 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.permission-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.permission-list li {
    padding: 0.25rem 0;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.permission-list li i {
    color: var(--success-color);
    margin-right: 0.5rem;
}

/* Modal z-index fixes */
.modal {
    z-index: 1050 !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

.modal.show {
    z-index: 1055 !important;
}

.modal.show .modal-backdrop {
    z-index: 1040 !important;
}

/* Media library modal should appear above user modals */
.media-library-modal {
    z-index: 1080 !important;
}

.media-library-modal .modal-dialog {
    z-index: 1081 !important;
    position: relative;
}

.media-library-backdrop {
    z-index: 1075 !important;
}

/* Ensure media library content is always on top */
#mediaLibraryModal {
    z-index: 1080 !important;
}

#mediaLibraryModal .modal-content {
    z-index: 1082 !important;
    position: relative;
}

/* User modal specific styles */
#userModal {
    z-index: 1055 !important;
}

#userModal .modal-backdrop {
    z-index: 1045 !important;
}

#userProfileModal {
    z-index: 1055 !important;
}

#userProfileModal .modal-backdrop {
    z-index: 1045 !important;
}

/* Ensure media library appears above everything */
.media-library-overlay {
    z-index: 1060 !important;
}

/* Fix modal backdrop issues */
.modal-backdrop.show {
    opacity: 0.5;
    z-index: 1040 !important;
}

/* Ensure modal content is above backdrop */
.modal-dialog {
    z-index: 1060 !important;
    position: relative;
}

/* Loading state for save button */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Avatar upload area improvements */
.avatar-upload-area {
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.avatar-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.avatar-upload-area:active {
    transform: translateY(0);
}

/* Responsive adjustments for users */
@media (max-width: 768px) {
    .users-toolbar .row > div {
        margin-bottom: 0.5rem;
    }

    .user-stats {
        gap: 1rem;
    }

    .users-container .table {
        font-size: 0.875rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
    }

    /* Mobile modal adjustments */
    .modal-dialog {
        margin: 0.5rem;
    }

    .user-form-section {
        margin-bottom: 1rem;
    }
}

/* Apple-Inspired Enhancements */

/* Smooth scrollbars */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
    transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Focus states */
*:focus {
    outline: none;
}

*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Selection styles */
::selection {
    background: var(--primary-color);
    color: white;
}

/* Typography enhancements */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: 600;
    letter-spacing: -0.02em;
    color: var(--gray-900);
}

/* Improved table styles */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 16px 20px;
}

.table td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--gray-200);
}

.table tbody tr:hover {
    background: var(--gray-50);
}

/* Enhanced badges */
.badge {
    font-family: var(--font-family-primary);
    font-weight: 500;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Micro-interactions */
.btn, .nav-link, .card {
    will-change: transform;
}

/* High-tech glass effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--blur-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Professional spacing */
.section-spacing {
    margin-bottom: 32px;
}

.content-spacing {
    margin-bottom: 24px;
}

.element-spacing {
    margin-bottom: 16px;
}

/* Apple-style status indicators */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-online {
    background: var(--success-color);
    box-shadow: 0 0 0 2px rgba(52, 199, 89, 0.2);
}

.status-offline {
    background: var(--gray-400);
}

.status-busy {
    background: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(255, 59, 48, 0.2);
}

/* Modern input groups */
.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group .form-control {
    border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.input-group .btn {
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
    border-left: none;
}

/* AI Assistant Styles */
.ai-suggestions {
    max-height: 400px;
    overflow-y: auto;
}

/* AI Workflow Styles */
.ai-workflow {
    border-left: 3px solid var(--primary-color);
    padding-left: 0.75rem;
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(0, 122, 255, 0.02));
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.ai-step {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: 0.75rem;
    background: var(--gray-50);
    transition: all var(--transition-fast);
}

.ai-step:hover {
    border-color: var(--primary-color);
    background: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.ai-step .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.ai-step .badge.bg-success {
    background-color: var(--success-color) !important;
}

.ai-step .badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.ai-step .badge.bg-info {
    background-color: var(--info-color) !important;
}

.suggestion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    margin-bottom: 0.5rem;
    background-color: var(--gray-50);
    transition: all var(--transition-fast);
}

.suggestion-item:hover {
    background-color: var(--gray-100);
    border-color: var(--gray-300);
    transform: translateY(-1px);
}

.suggestion-content {
    flex: 1;
    margin-right: 1rem;
}

.tag-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-suggestion {
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid var(--gray-300) !important;
    background-color: var(--gray-100) !important;
    color: var(--gray-700) !important;
}

.tag-suggestion:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
    transform: translateY(-1px);
}

.ai-assistant-panel .card {
    border: 1px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(0, 122, 255, 0.02));
}

.ai-assistant-panel .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-bottom: none;
}

.ai-assistant-panel .card-body {
    padding: 1rem;
}

.api-key-row {
    background-color: var(--gray-50);
    transition: all var(--transition-fast);
    border: 1px solid var(--gray-200);
}

.api-key-row:hover {
    background-color: var(--gray-100);
    border-color: var(--gray-300);
}

.seo-analysis {
    font-size: 0.9rem;
}

.seo-score {
    text-align: center;
    padding: 1rem;
    border-radius: var(--radius-md);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.recommendations ul {
    padding-left: 0;
}

.recommendations li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.recommendations li:last-child {
    border-bottom: none;
}

/* AI Loading States */
.ai-loading {
    position: relative;
    overflow: hidden;
}

.ai-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.2), transparent);
    animation: ai-shimmer 1.5s infinite;
}

@keyframes ai-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* AI Button Styles */
[data-ai-action] {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-fast);
}

[data-ai-action]:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

[data-ai-action] .fa-robot {
    color: var(--primary-color);
    transition: all var(--transition-fast);
}

[data-ai-action]:hover .fa-robot {
    color: var(--primary-dark);
    transform: scale(1.1);
}

[data-ai-action]:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* AI Settings Styles */
.ai-settings-container .form-range {
    margin: 0.5rem 0;
    accent-color: var(--primary-color);
}

.ai-settings-container .form-text {
    font-size: 0.8rem;
    color: var(--gray-600);
}

.usage-stats-section .card {
    border: none;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.usage-stats-section .card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.usage-stats-section .card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* AI Modal Styles */
.modal .ai-assistant-panel {
    margin-bottom: 0;
}

.modal .ai-assistant-panel .card {
    border: none;
    background: transparent;
}

.modal .ai-assistant-panel .card-body {
    padding: 0;
}

/* Responsive AI Features */
@media (max-width: 768px) {
    .suggestion-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .suggestion-content {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .ai-assistant-panel .row {
        margin: 0;
    }

    .ai-assistant-panel .col-md-6 {
        padding: 0.25rem;
    }

    .tag-suggestions {
        justify-content: center;
    }

    .api-key-row .row {
        flex-direction: column;
    }

    .api-key-row .col-md-1,
    .api-key-row .col-md-2,
    .api-key-row .col-md-3,
    .api-key-row .col-md-6 {
        margin-bottom: 0.5rem;
    }
}

/* ===== EDITOR.JS MINIMAL STYLING ===== */

/* Respect Editor.js default behavior with NO extra spacing */
.codex-editor {
    min-height: 300px;
    position: relative;
}

.codex-editor__redactor {
    padding-bottom: 100px;
    position: relative;
    /* Remove all left padding - let Editor.js handle it naturally */
}

/* NO extra padding for editor container */
#editorjs {
    position: relative;
    /* Let Editor.js handle spacing naturally */
}

/* Reset any card padding overrides */
.card-body {
    padding: 1.25rem; /* Bootstrap default only */
}

/* Ensure sidebar elements use normal spacing */
.sidebar .card-body,
.seo-panel .card-body,
.right-sidebar .card-body {
    padding: 1.25rem !important;
}

/* Gentle enhancement for plus button visibility */
.ce-toolbar__plus {
    transition: all 0.2s ease;
}

.ce-toolbar__plus:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

/* Ensure toolbar appears on hover (Editor.js default behavior) */
.codex-editor .ce-block:hover .ce-toolbar {
    opacity: 1;
    visibility: visible;
}

/* Better visual feedback for toolbar */
.ce-toolbar {
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

/* Enhance toolbar visibility without breaking default behavior */
.codex-editor .ce-block--focused .ce-toolbar,
.codex-editor .ce-toolbar--opened {
    opacity: 1;
    visibility: visible;
}

/* Gentle toolbar positioning - work with Editor.js defaults */
.ce-toolbar {
    /* Let Editor.js handle positioning naturally */
    z-index: 3;
}

/* Ensure plus button is accessible */
.ce-toolbar__plus {
    /* Let Editor.js handle positioning naturally */
}

/* Custom block styling */
.styled-card {
    border-radius: 12px;
    padding: 24px;
    margin: 16px 0;
    box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);
    transition: all 0.2s ease;
}

.styled-card:hover {
    box-shadow: 0 4px 16px rgba(43, 11, 58, 0.15);
}

.statistics-display {
    text-align: center;
    padding: 24px;
    border-radius: 12px;
    margin: 16px 0;
    transition: all 0.2s ease;
}

.statistics-display:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.cta-block {
    border-radius: 16px;
    padding: 48px;
    margin: 32px 0;
    box-shadow: 0 8px 32px rgba(43, 11, 58, 0.2);
    transition: all 0.2s ease;
}

.cta-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(43, 11, 58, 0.25);
}

/* Editor.js block controls */
.styled-card-controls,
.statistics-controls,
.cta-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    padding: 8px;
    background: #f5f5f5;
    border-radius: 8px;
    font-size: 12px;
    flex-wrap: wrap;
}

.styled-card-controls button,
.statistics-controls button,
.cta-controls button {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
}

.styled-card-controls button:hover,
.statistics-controls button:hover,
.cta-controls button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Highlight tool styling */
.adzeta-highlight {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.adzeta-highlight:hover {
    opacity: 0.8;
}

/* Editor.js inline toolbar */
.ce-inline-toolbar {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Editor.js block settings */
.ce-settings {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Editor.js popover */
.ce-popover {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* ===== CASE STUDY EDITOR STYLES ===== */

/* Case Study Editor Container */
.case-study-editor {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 100%;
    margin: 0 auto;
}

/* Editor Header */
.editor-header {
    background: linear-gradient(135deg, var(--primary-color), #4f46e5);
    color: white;
    padding: 2rem;
    border-radius: 12px 12px 0 0;
}

.editor-header h2 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
}

.editor-status {
    margin-top: 0.5rem;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.saved {
    background: rgba(34, 197, 94, 0.2);
    color: #16a34a;
}

.status-indicator.unsaved {
    background: rgba(251, 191, 36, 0.2);
    color: #d97706;
}

/* Editor Actions */
.editor-actions {
    display: flex;
    gap: 0.5rem;
}

.editor-actions .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

/* Horizontal Tab Navigation */
.editor-tabs-container {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 0 1rem;
    overflow-x: auto;
    scrollbar-width: thin;
}

.editor-tabs {
    display: flex;
    gap: 0;
    margin: 0;
    padding: 0;
    list-style: none;
    border-bottom: none;
}

.editor-tabs .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #64748b;
    text-decoration: none;
    border: none;
    border-bottom: 3px solid transparent;
    background: transparent;
    font-weight: 500;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    white-space: nowrap;
    border-radius: 0;
    min-width: fit-content;
}

.editor-tabs .nav-link:hover {
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
    transform: none;
}

.editor-tabs .nav-link.active {
    color: var(--primary-color);
    background: white;
    border-bottom-color: var(--primary-color);
    transform: none;
    box-shadow: none;
}

.editor-tabs .nav-link.active::before {
    display: none;
}

.editor-tabs .nav-link i {
    margin-right: 0.375rem;
    width: auto;
    font-size: 0.75rem;
}

/* Editor Content Area */
.editor-content {
    padding: 1.5rem;
    min-height: 500px;
    max-width: 100%;
    overflow-x: auto;
}

/* Editor Sections */
.editor-section {
    display: none;
}

.editor-section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.editor-section h4 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e2e8f0;
}

/* Form Styling within Editor */
.editor-section .form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.editor-section .form-control,
.editor-section .form-select {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.editor-section .form-control:focus,
.editor-section .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Compact spacing for better fit */
.editor-section .row {
    margin-bottom: 0.75rem;
}

.editor-section .mb-3 {
    margin-bottom: 0.75rem !important;
}

.editor-section .mb-4 {
    margin-bottom: 1rem !important;
}

.editor-section h4 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.editor-section h5 {
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    color: #374151;
    font-weight: 600;
}

.editor-section .form-label {
    margin-bottom: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.editor-section .form-control,
.editor-section .form-select {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 6px;
}

.editor-section textarea.form-control {
    min-height: 80px;
}

.editor-section .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

/* Responsive Design for Case Study Editor */
@media (max-width: 1200px) {
    .editor-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.7rem;
    }

    .editor-tabs .nav-link i {
        margin-right: 0.25rem;
        font-size: 0.7rem;
    }

    .editor-content {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .editor-header {
        padding: 1rem;
    }

    .editor-tabs-container {
        padding: 0 0.5rem;
        overflow-x: auto;
    }

    .editor-tabs {
        min-width: max-content;
        gap: 0;
    }

    .editor-tabs .nav-link {
        padding: 0.5rem 0.5rem;
        font-size: 0.65rem;
        min-width: auto;
    }

    .editor-tabs .nav-link i {
        margin-right: 0.2rem;
        font-size: 0.65rem;
    }

    .editor-content {
        padding: 1rem;
    }

    .editor-actions {
        flex-direction: column;
    }

    .editor-actions .btn {
        width: 100%;
    }
}

/* Hide scrollbars for tabs on webkit browsers */
.editor-tabs-container::-webkit-scrollbar {
    height: 4px;
}

.editor-tabs-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.editor-tabs-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.editor-tabs-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Case Studies Grid Styling */
.case-studies-container {
    padding: 1rem 0;
}

.case-study-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.case-study-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.case-study-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.case-study-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.case-study-card:hover .case-study-image img {
    transform: scale(1.05);
}

.case-study-status {
    position: absolute;
    top: 12px;
    right: 12px;
}

.case-study-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.badge-outline-primary {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.result-metric {
    padding: 0.5rem;
    background: rgba(59, 130, 246, 0.05);
    border-radius: 8px;
}

.metric-value {
    font-size: 1.25rem;
    font-weight: 700;
}

.metric-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.empty-state {
    background: #f8fafc;
    border-radius: 12px;
    padding: 3rem 2rem;
}

/* Navigation Menu Enhancements */
.nav-item .nav-link[data-view="add-case-study"] {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    margin-bottom: 0.5rem;
}

.nav-item .nav-link[data-view="add-case-study"]:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateX(4px);
}

.nav-item .nav-link[data-view="add-case-study"].active {
    background: linear-gradient(135deg, #059669, #047857);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== FULLSCREEN EDITOR ===== */

/* Hide other elements when in fullscreen */
body.fullscreen-active .sidebar,
body.fullscreen-active .navbar,
body.fullscreen-active .main-content > *:not(.fullscreen-editor) {
    display: none !important;
}

/* Fullscreen escape hint */
.fullscreen-editor::before {
    content: 'Press ESC to exit fullscreen';
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10001;
    opacity: 0;
    animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    20%, 80% { opacity: 1; }
}


