/* Minimal Comparison Section Styling */

.minimal-comparison-section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    background-color: #FAFAFA;
}

.minimal-comparison-section .container {
    max-width: 1140px;
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
}

.section-header .section-tag {
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    color: #e958a1;
    margin-bottom: 15px;
    display: inline-block;
}

.section-header h2 {
    font-size: 42px;
    font-weight: 700;
    line-height: 1.2;
    color: #333;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
}

.section-header h2 span {
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: 18px;
    line-height: 1.6;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Comparison Cards Container */
.comparison-cards {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 80px;
}

/* Comparison Card */
.comparison-card {
    flex: 1;
    max-width: 500px;
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    position: relative;
    overflow: hidden;
}

.comparison-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
}

.comparison-card.featured {
    box-shadow: 0 15px 50px rgba(233, 88, 161, 0.15);
}

.comparison-card.featured:hover {
    box-shadow: 0 25px 60px rgba(233, 88, 161, 0.2);
}

/* Card Header */
.card-header {
    margin-bottom: 30px;
    position: relative;
}

.card-tag {
    display: inline-block;
    font-size: 13px;
    font-weight: 600;
    padding: 6px 15px;
    border-radius: 30px;
    margin-bottom: 15px;
}

.comparison-card.traditional .card-tag {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.comparison-card.adzeta .card-tag {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.card-header h3 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.card-header p {
    font-size: 16px;
    color: #666;
    margin: 0;
}

/* Recommended Badge */
.recommended-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 5px 12px;
    border-radius: 20px;
}

/* Feature List */
.feature-list {
    margin-bottom: 30px;
}

.feature-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    font-size: 16px;
    color: #555;
}

.feature-list li:last-child {
    margin-bottom: 0;
}

.feature-list li i {
    margin-right: 12px;
    font-size: 16px;
    margin-top: 3px;
}

.feature-list li i.positive {
    color: #4CAF50;
}

.feature-list li i.negative {
    color: #F44336;
}

/* Card Footer */
.card-footer {
    text-align: center;
}

.card-button {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
}

.comparison-card.traditional .card-button {
    background: #f8f9fa;
    color: #555;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.comparison-card.traditional .card-button:hover {
    background: #e9ecef;
}

.comparison-card.adzeta .card-button {
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.2);
}

.comparison-card.adzeta .card-button:hover {
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    transform: translateY(-3px);
}

/* VS Badge */
.vs-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: white;
    color: #333;
    font-weight: 700;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

/* Results Section */
.results-section {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    padding: 60px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
}

.results-section h3 {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 40px;
}

.stats-row {
    display: flex;
    justify-content: space-around;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.stat-item {
    padding: 0 20px;
    margin-bottom: 30px;
}

.stat-value {
    font-size: 48px;
    font-weight: 700;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 16px;
    color: #666;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    padding: 15px 30px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-weight: 600;
    font-size: 16px;
    border-radius: 30px;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.2);
}

.cta-button i {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    color: white;
}

.cta-button:hover i {
    transform: translateX(5px);
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.active {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-left.active {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-right.active {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.9);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scale-in.active {
    opacity: 1;
    transform: scale(1);
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .section-header h2 {
        font-size: 36px;
    }
    
    .comparison-cards {
        flex-direction: column;
        align-items: center;
    }
    
    .comparison-card {
        width: 100%;
        max-width: 500px;
        margin-bottom: 50px;
    }
    
    .vs-badge {
        position: relative;
        margin: -25px auto 25px;
        transform: none;
        left: auto;
        top: auto;
    }
    
    .results-section {
        padding: 40px 20px;
    }
}

@media (max-width: 767px) {
    .minimal-comparison-section {
        padding: 60px 0;
    }
    
    .section-header h2 {
        font-size: 30px;
    }
    
    .section-header p {
        font-size: 16px;
    }
    
    .comparison-card {
        padding: 30px;
    }
    
    .card-header h3 {
        font-size: 22px;
    }
    
    .stat-value {
        font-size: 36px;
    }
    
    .cta-button {
        padding: 12px 25px;
        font-size: 15px;
    }
}
