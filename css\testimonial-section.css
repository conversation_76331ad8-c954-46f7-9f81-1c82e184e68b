/* Mobile-friendly Testimonial Section with Profile Pic and Case Study Link */
.testimonial-section {
    margin-top: 60px;
    margin-bottom: 40px;
    position: relative;
    padding-top: 30px;
}

.testimonial-section:before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    border-radius: 3px;
}

.mobile-testimonial {
    padding: 40px;
    border-radius: 12px;
    background-color: #ffffff;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.03);
    max-width: 900px;
    margin: 0 auto;
}

.mobile-testimonial .testimonial-content {
    display: grid;
    grid-template-columns: 180px 1fr;
    gap: 40px;
    align-items: start;
}

.mobile-testimonial .testimonial-left {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.mobile-testimonial .testimonial-right {
    position: relative;
}

.mobile-testimonial .testimonial-quote {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    margin-bottom: 30px;
    font-weight: 400;
    font-style: italic;
    position: relative;
}

.mobile-testimonial .testimonial-profile {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    width: 100%;
}

.mobile-testimonial .profile-pic {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 15px;
    background-color: #f8f8f8;
}

.mobile-testimonial .profile-pic img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.mobile-testimonial .testimonial-author {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.mobile-testimonial .testimonial-position {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.mobile-testimonial .testimonial-company {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.mobile-testimonial .testimonial-company img {
    height: 16px;
    margin-right: 8px;
    opacity: 0.9;
}

.mobile-testimonial .testimonial-metrics {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 25px;
}

.mobile-testimonial .metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.mobile-testimonial .metric-item:last-child {
    border-bottom: none;
}

.mobile-testimonial .metric-label {
    color: #666;
    font-size: 14px;
    font-weight: 400;
}

.mobile-testimonial .metric-value {
    font-weight: 600;
    font-size: 16px;
    line-height: 1;
}

.mobile-testimonial .metric-value.positive {
    color: #f45888;
}

.mobile-testimonial .case-study-link {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: #f45888;
    text-decoration: none;
    transition: all 0.2s ease;
    border-bottom: 1px solid transparent;
}

.mobile-testimonial .case-study-link:hover {
    color: #e958a1;
    border-bottom-color: #e958a1;
}

.mobile-testimonial .case-study-link i {
    margin-left: 6px;
    font-size: 12px;
    transition: transform 0.2s ease;
}

.mobile-testimonial .case-study-link:hover i {
    transform: translateX(3px);
}

@media (max-width: 991px) {
    .mobile-testimonial .testimonial-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .mobile-testimonial .testimonial-left {
        display: grid;
        grid-template-columns: auto 1fr;
        grid-template-areas:
            "pic author"
            "pic position"
            "pic company"
            "link link";
        column-gap: 15px;
        row-gap: 5px;
        align-items: center;
    }

    .mobile-testimonial .profile-pic {
        grid-area: pic;
        margin-bottom: 0;
    }

    .mobile-testimonial .testimonial-author {
        grid-area: author;
    }

    .mobile-testimonial .testimonial-position {
        grid-area: position;
    }

    .mobile-testimonial .testimonial-company {
        grid-area: company;
        margin-bottom: 0;
    }

    .mobile-testimonial .case-study-link {
        grid-area: link;
        margin-top: 15px;
    }
}

@media (max-width: 767px) {
    .mobile-testimonial {
        padding: 25px;
    }

    .mobile-testimonial .testimonial-quote {
        font-size: 15px;
        margin-bottom: 20px;
    }

    .mobile-testimonial .profile-pic {
        width: 50px;
        height: 50px;
    }

    .mobile-testimonial .testimonial-author {
        font-size: 15px;
    }

    .mobile-testimonial .testimonial-position {
        font-size: 13px;
    }

    .mobile-testimonial .testimonial-company {
        font-size: 12px;
    }

    .mobile-testimonial .testimonial-metrics {
        gap: 8px;
    }

    .mobile-testimonial .metric-item {
        padding: 8px 0;
    }

    .mobile-testimonial .metric-label {
        font-size: 13px;
    }

    .mobile-testimonial .metric-value {
        font-size: 15px;
    }

    .mobile-testimonial .case-study-link {
        font-size: 13px;
    }
}
