/* Google Value Challenges Section - Modern Apple-Inspired Design */

.google-value-challenges {
    position: relative;
    background: #f5f5f7; /* White background for contrast with previous section */
    padding: 80px 0 100px;
    overflow: hidden;
}


/* Section heading styles */
.challenges-heading {
    position: relative;
    margin-bottom: 60px;
}

.challenges-heading h3 {
    font-weight: 700;
    margin-bottom: 15px;
}

/* Challenge block container */
.challenge-block {
    position: relative;
    margin-bottom: 80px;
    z-index: 1;
    background: #fffdfc; /* Slightly blue-tinted background */
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.04);
    padding: 40px;
      border: 1px solid rgba(0, 0, 0, 0.03);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.challenge-block:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.06);
}

/* Text content for challenge blocks */
.challenge-content {
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    position: relative;
    z-index: 2;
}

.challenge-content h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    letter-spacing: -0.02em;
    line-height: 1.3;
    color: #1a1a1a;
}

.challenge-content p {
    font-size: 15px;
    line-height: 1.7;
    color: #555;
    margin-bottom: 0;
    font-weight: 400;
    letter-spacing: -0.2px;
}

/* Visual elements for challenge blocks */
.challenge-visual {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    overflow: hidden;
    z-index: 2;
}

.visual-header {
    text-align: left;
    padding-left: 10px;
}

.visual-header h3 {
    color: #2c2e3c;
}

.visual-header p {
    color: #555;
    max-width: 90%;
}

/* Apple-inspired visual enhancements */
.challenge-visual object,
.challenge-visual img {
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.challenge-visual object:hover,
.challenge-visual img:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
}

/* Cold Start Graph Styling */
.cold-start-graph {
    width: 100%;
    max-width: 400px;
    height: 300px;
    position: relative;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    padding: 20px;
}

.graph-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.graph-axis {
    position: absolute;
    background: rgba(0, 0, 0, 0.1);
}

.x-axis {
    bottom: 40px;
    left: 50px;
    width: calc(100% - 60px);
    height: 1px;
}

.y-axis {
    bottom: 40px;
    left: 50px;
    width: 1px;
    height: calc(100% - 60px);
}

.axis-label {
    position: absolute;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
}

.x-label {
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
}

.y-label {
    top: 50%;
    left: 15px;
    transform: translateY(-50%) rotate(-90deg);
    transform-origin: left center;
}

.target-line {
    position: absolute;
    top: 80px;
    left: 50px;
    width: calc(100% - 60px);
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
    border-top: 1px dashed rgba(0, 0, 0, 0.2);
}

.target-label {
    position: absolute;
    top: 65px;
    right: 15px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
}

.graph-line {
    position: absolute;
    bottom: 40px;
    left: 50px;
    height: 1px;
    background: none;
}

.google-line {
    width: calc(100% - 60px);
    background: linear-gradient(90deg, transparent, #4a9eff);
    clip-path: path('M0,120 Q60,100 120,70 T240,40');
    height: 120px;
}

.adzeta-line {
    width: calc(100% - 60px);
    background: linear-gradient(90deg, transparent, #e958a1);
    clip-path: path('M0,120 Q40,60 80,30 T240,10');
    height: 120px;
}

.line-label {
    position: absolute;
    font-size: 12px;
    font-weight: 500;
}

.google-label {
    bottom: 170px;
    right: 20px;
    color: #4a9eff;
}

.adzeta-label {
    bottom: 200px;
    right: 20px;
    color: #e958a1;
}

/* Missing Signals Infographic Styling */
.missing-signals-visual {
    width: 100%;
    max-width: 400px;
    height: 300px;
    position: relative;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.data-sources {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 30px;
}

.data-source {
    text-align: center;
    width: 30%;
}

.data-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 10px;
    background: #f5f5f7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #2c2e3c;
    font-size: 20px;
}

.data-label {
    font-size: 12px;
    color: #2c2e3c;
    font-weight: 500;
}

.platform-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    position: relative;
    margin-top: 20px;
}

.adzeta-container {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
}

.google-container {
    width: 100px;
    height: 100px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.google-logo {
    width: 60px;
    height: auto;
}

.data-arrow {
    position: absolute;
    width: 80px;
    height: 2px;
}

.basic-arrow {
    top: 50%;
    left: 20%;
    width: 60%;
    background: rgba(0, 0, 0, 0.1);
}

.basic-arrow::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.2);
}

.full-arrow {
    top: 0;
    left: 20%;
    width: 60%;
    background: linear-gradient(90deg, #e958a1, #8f76f5);
}

.full-arrow::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #8f76f5;
}

/* Timeline insights */
.timeline-insights {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    margin-bottom: 20px;
}

.timeline-insights-row {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
    margin-top: 20px;
    margin-bottom: 20px;
}

.insight-box {
    background-color: rgba(233, 88, 161, 0.05);
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    border-left: 3px solid #E83E8C;
    flex: 1;
}

.insight-box.insight-google {
    border-left: 3px solid #4285F4;
    background-color: rgba(66, 133, 244, 0.05);
}

.insight-box.insight-adzeta {
    border-left: 3px solid #E83E8C;
    background-color: rgba(233, 88, 161, 0.05);
}

.insight-title {
    font-weight: 600;
    font-size: 15px;
    margin-bottom: 8px;
    color: #E83E8C;
}

.insight-box.insight-google .insight-title {
    color: #4285F4;
}

.insight-desc {
    font-size: 13px;
    color: #555;
    line-height: 1.5;
}

/* Value indicators at Day 7 */
.value-indicator {
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    z-index: 5;
    transform: translate(-50%, -50%);
}

.dollar-sign-indicator {
    font-size: 14px;
    font-weight: 600;
    color: #777;
}

/* Adzeta AI icon */
.adzeta-ai-icon {
    position: absolute;
    top: 15%;
    left: 10%;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(233, 88, 161, 0.2);
    z-index: 5;
    border: 1px solid rgba(233, 88, 161, 0.2);
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .google-value-challenges {
        padding: 60px 0 80px;
    }

    .challenge-block {
        margin-bottom: 60px;
        padding: 30px;
    }

    .challenge-block::before {
        top: -5%;
        left: 25%;
        width: 45%;
    }

    .challenge-block::after {
        background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 60%);
    }

    .challenge-visual {
        margin-top: 30px;
    }

    .timeline-insights {
        justify-content: center;
    }

    .timeline-insights-row {
        flex-direction: column;
        gap: 15px;
    }

    .insight-box {
        width: 100%;
    }
}

@media (max-width: 767px) {
    .google-value-challenges {
        padding: 50px 0 50px;
    }

    .challenge-block {
        padding: 25px;
        margin-bottom: 40px;
    }

    .challenge-block::before {
        top: -5%;
        left: 20%;
        width: 60%;
    }

    .challenge-block::after {
        background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 50%);
    }

    .challenge-content h3 {
        font-size: 22px;
    }
}

@media (max-width: 575px) {
    .challenge-block {
        padding: 20px;
        border-radius: 16px;
    }
}
