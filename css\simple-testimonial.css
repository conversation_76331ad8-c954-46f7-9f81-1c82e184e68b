/* Simple, Clean Testimonial Design */
.testimonial-section {
    margin: 40px 0;
    position: relative;
}

.simple-testimonial {
    width: 100%;
    margin: 0 auto;
    background-color: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
    padding: 40px;
    position: relative;
    overflow: hidden;
}

.testimonial-content {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 50px;
}

.testimonial-left {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-template-areas:
        "pic info"
        "link link";
    column-gap: 20px;
    align-items: center;
}

.profile-pic {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f8f8f8;
    border: 3px solid #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    grid-area: pic;
}

.profile-pic img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    grid-area: info;
    display: flex;
    flex-direction: column;
}

.testimonial-author {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
}

.testimonial-position {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.testimonial-company {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
    margin-bottom: 0;
    padding: 6px 12px;
    background-color: #f9f9f9;
    border-radius: 20px;
    display: inline-flex;
    width: fit-content;
}

.testimonial-company img {
    height: 16px;
    margin-right: 8px;
    opacity: 0.9;
}

.case-study-link {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: #f45888;
    text-decoration: none;
    transition: all 0.2s ease;
    grid-area: link;
    margin-top: 25px;
    width: fit-content;
    position: relative;
}

.case-study-link:hover {
    color: #e03a6e;
}

.case-study-link img {
    transition: transform 0.2s ease;
}

.case-study-link:hover img {
    transform: translate(2px, -2px);
}


.testimonial-right {
    display: flex;
    flex-direction: column;
}

.testimonial-quote {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    margin-bottom: 30px;
    font-weight: 400;
    font-style: italic;
    position: relative;
}

.testimonial-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 30px;
    position: relative;
}

.testimonial-metrics:before {
    content: "";
    position: absolute;
    top: -15px;
    left: 0;
    right: 0;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.06);
}

.metric-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.metric-label {
    font-size: 13px;
    color: #666;
    margin-bottom: 6px;
    font-weight: 400;
    letter-spacing: 0.2px;
}

.metric-value {
    font-size: 28px;
    font-weight: 600;
    color: #f45888;
    line-height: 1;
}

@media (max-width: 991px) {
    .testimonial-metrics {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    .metric-value {
        font-size: 24px;
    }
}

@media (max-width: 991px) {
    .testimonial-content {
        grid-template-columns: 240px 1fr;
        gap: 30px;
    }
}

@media (max-width: 767px) {
    .simple-testimonial {
        padding: 30px;
    }

    .testimonial-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .testimonial-left {
        grid-template-columns: auto 1fr;
        grid-template-areas:
            "pic info"
            "link link";
        column-gap: 15px;
    }

    .profile-pic {
        width: 70px;
        height: 70px;
    }

    .case-study-link {
        margin-top: 20px;
        width: auto;
        justify-content: center;
    }

    .testimonial-quote {
        font-size: 15px;
        margin-bottom: 25px;
    }

    .testimonial-metrics {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-top: 25px;
    }

    .testimonial-metrics:before {
        top: -12px;
    }

    .metric-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }

    .metric-item:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .metric-label {
        margin-bottom: 0;
        font-size: 13px;
    }

    .metric-value {
        font-size: 22px;
    }
}

@media (max-width: 480px) {
    .testimonial-left {
        grid-template-columns: 1fr;
        grid-template-areas:
            "pic"
            "info"
            "link";
        justify-items: center;
        text-align: center;
        row-gap: 15px;
    }

    .profile-info {
        align-items: center;
    }

    .testimonial-company {
        justify-content: center;
    }
}
