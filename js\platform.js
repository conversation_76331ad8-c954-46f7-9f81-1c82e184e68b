/**
 * Predictive AI Showcase - Interactive Animations
 * Handles the interactive elements and SVG animations for the AI showcase section
 * Supports both tab (desktop) and accordion (mobile) layouts
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the showcase
    initPredictiveAIShowcase();

    // Initialize accordion debug functionality (disabled in production)
    // initAccordionDebug();

    // Handle window resize to switch between tab and accordion layouts
    window.addEventListener('resize', handleLayoutChange);

    // Initial layout setup
    handleLayoutChange();

    // iOS Safari specific fixes
    initIOSSafariFixes();

    // Bootstrap accordion fix is handled in main.js

    // Fix for duplicate content issue - ensure only one panel is visible at a time
    function cleanupPanels() {
        const isMobile = window.innerWidth <= 1024;
        if (isMobile) {
            // On mobile, make sure only the active accordion panel is visible
            const accordionPanels = document.querySelectorAll('.accordion-panel');
            const activeHeader = document.querySelector('.accordion-header.active');

            accordionPanels.forEach(panel => {
                panel.style.display = 'none';
            });

            if (activeHeader) {
                const targetId = activeHeader.getAttribute('data-target') + '-accordion';
                const targetPanel = document.getElementById(targetId);
                if (targetPanel) {
                    targetPanel.style.display = 'block';
                }
            }

            // Hide desktop panels
            document.querySelector('.process-content').style.display = 'none';
        }
    }

    // Run cleanup after a short delay to ensure everything is loaded
    setTimeout(cleanupPanels, 100);
});

/**
 * Initialize the Predictive AI Showcase section
 */
function initPredictiveAIShowcase() {
    // Get all process steps (tabs)
    const steps = document.querySelectorAll('.process-step');
    const panels = document.querySelectorAll('.process-panel');

    // Get all accordion headers (mobile)
    const accordionHeaders = document.querySelectorAll('.accordion-header');
    const accordionPanels = document.querySelectorAll('.accordion-panel');

    // Add click event to each tab step
    steps.forEach(step => {
        step.addEventListener('click', function() {
            // Get the target panel ID
            const targetId = this.getAttribute('data-target');

            // Remove active class from all steps and add to current
            steps.forEach(s => s.classList.remove('active'));
            this.classList.add('active');

            // Hide all panels and show the target panel
            panels.forEach(panel => {
                panel.classList.remove('active');
            });

            const targetPanel = document.getElementById(targetId);
            targetPanel.classList.add('active');

            // Initialize the appropriate animation
            initAnimation(targetId);
        });
    });

    // Add click event to each accordion header
    accordionHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetPanel = document.getElementById(targetId + '-accordion');

            // Toggle active class on the clicked header
            const isActive = this.classList.contains('active');

            // Close all accordion panels first
            accordionHeaders.forEach(h => h.classList.remove('active'));
            accordionPanels.forEach(p => {
                p.style.display = 'none';
            });

            // If the clicked panel wasn't active, open it
            if (!isActive) {
                this.classList.add('active');

                // Make sure only the correct panel is displayed
                accordionPanels.forEach(p => {
                    p.style.display = 'none';
                });

                if (targetPanel) {
                    targetPanel.style.display = 'block';

                    // Initialize the animation for this panel
                    const animationId = targetId.replace('-accordion', '');
                    initAnimation(animationId);

                    // Removed auto-scroll behavior for better UX
                    // scrollToAccordionContent(this, targetPanel);
                }
            }
        });
    });

    // Initialize the first animation (data flow)
    if (steps.length > 0) {
        steps[0].classList.add('active');
        if (panels.length > 0) {
            panels[0].classList.add('active');
            initAnimation('data-panel');
        }
    }

    // If on mobile or tablet, initialize the first accordion panel
    if (window.innerWidth <= 1024 && accordionHeaders.length > 0) {
        accordionHeaders[0].classList.add('active');
        if (accordionPanels.length > 0) {
            accordionPanels[0].style.display = 'block';
        }
    }
}

/**
 * Enhanced Safari iOS accordion fix for ANALYZE section
 * Prevents content from opening upwards and ensures proper positioning
 * @param {Element} header - The clicked accordion header
 * @param {Element} panel - The target panel to show
 */
function scrollToAccordionContent(header, panel) {
    // Only apply to iOS Safari on mobile
    const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    const isMobileViewport = window.innerWidth <= 768;

    if (!isIOSSafari || !isMobileViewport) {
        return; // Skip for desktop and non-iOS browsers
    }

    // Safari iOS accordion fix applied

    // Store current scroll position to prevent unwanted jumping
    const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Apply Safari-specific CSS class for enhanced styling
    document.body.classList.add('safari-accordion-fix');

    // Enhanced scroll adjustment with multiple strategies
    setTimeout(() => {
        const headerRect = header.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const headerHeight = header.offsetHeight;

        // Strategy 1: Prevent upward opening by ensuring header is in upper portion
        if (headerRect.top > viewportHeight * 0.6) {
            const targetScroll = currentScrollTop + (headerRect.top - viewportHeight * 0.2);

            window.scrollTo({
                top: Math.max(0, targetScroll),
                behavior: 'smooth'
            });
        }

        // Strategy 2: Force proper panel positioning
        if (panel) {
            panel.style.position = 'relative';
            panel.style.top = '0';
            panel.style.transform = 'translateZ(0)';
            panel.style.webkitTransform = 'translateZ(0)';
        }

        // Strategy 3: Ensure header stays in place
        header.style.position = 'relative';
        header.style.zIndex = '2';
        header.style.transform = 'translateZ(0)';
        header.style.webkitTransform = 'translateZ(0)';

    }, 100); // Slightly longer delay for better stability

    // Additional fix: Prevent iOS rubber band effect interference
    setTimeout(() => {
        const showcaseElement = document.querySelector('.predictive-ai-showcase');
        if (showcaseElement) {
            showcaseElement.style.webkitOverflowScrolling = 'auto';
            showcaseElement.style.overflow = 'visible';
        }
    }, 150);
}

/**
 * Handle layout changes between desktop (tabs) and mobile (accordion)
 */
function handleLayoutChange() {
    const isMobile = window.innerWidth <= 1024;
    const accordionPanels = document.querySelectorAll('.accordion-panel');
    const processContent = document.querySelector('.process-content');
    const processPanels = document.querySelectorAll('.process-panel');

    if (isMobile) {
        // On mobile, hide desktop panels and show mobile accordion
        if (processContent) {
            processContent.style.display = 'none';
        }

        // Hide all accordion panels first
        accordionPanels.forEach(panel => {
            panel.style.display = 'none';
        });

        // Show only the active accordion panel
        const activeHeader = document.querySelector('.accordion-header.active');
        if (activeHeader) {
            const targetId = activeHeader.getAttribute('data-target') + '-accordion';
            const targetPanel = document.getElementById(targetId);
            if (targetPanel) {
                targetPanel.style.display = 'block';
            }
        } else if (document.querySelectorAll('.accordion-header').length > 0) {
            // If no active header, activate the first one
            const firstHeader = document.querySelectorAll('.accordion-header')[0];
            firstHeader.classList.add('active');

            const targetId = firstHeader.getAttribute('data-target') + '-accordion';
            const targetPanel = document.getElementById(targetId);
            if (targetPanel) {
                targetPanel.style.display = 'block';
            }
        }
    } else {
        // On desktop, hide all accordion panels and show desktop tabs
        accordionPanels.forEach(panel => {
            panel.style.display = 'none';
        });

        if (processContent) {
            processContent.style.display = 'block';
        }

        // Show only the active process panel
        processPanels.forEach(panel => {
            panel.style.display = 'none';
        });

        const activeStep = document.querySelector('.process-step.active');
        if (activeStep) {
            const targetId = activeStep.getAttribute('data-target');
            const targetPanel = document.getElementById(targetId);
            if (targetPanel) {
                targetPanel.style.display = 'block';
            }
        } else if (processPanels.length > 0) {
            // If no active panel, show the first one
            processPanels[0].style.display = 'block';
        }
    }
}

/**
 * Initialize the appropriate animation based on the panel ID
 * @param {string} panelId - The ID of the panel to animate
 */
function initAnimation(panelId) {
    // Initialize the appropriate animation
    switch(panelId) {
        case 'data-panel':
            restartSvgAnimation('data-flow-animation.svg');
            break;
        case 'modeling-panel':
            restartSvgAnimation('modeling-animation.svg');
            break;
        case 'prediction-panel':
            restartSvgAnimation('prediction-animation.svg');
            break;
        case 'learning-panel':
            restartSvgAnimation('learning-animation.svg');
            break;
    }
}

/**
 * Restart SVG animations by reloading the SVG
 * @param {string} svgFileName - The filename of the SVG to restart
 */
function restartSvgAnimation(svgFileName) {
    // Find all SVG objects with this filename
    const svgObjects = document.querySelectorAll(`object[data*="${svgFileName}"]`);

    svgObjects.forEach(svgObject => {
        // Get the current src
        const currentSrc = svgObject.getAttribute('data');

        // Force reload by adding/removing a query parameter
        const hasQuery = currentSrc.indexOf('?') !== -1;
        const newSrc = hasQuery
            ? currentSrc.replace(/\?reload=\d+/, `?reload=${Date.now()}`)
            : `${currentSrc}?reload=${Date.now()}`;

        // Update the src to force a reload
        svgObject.setAttribute('data', newSrc);
    });
}

/**
 * Animate the Data Flow panel - now handled by the SVG's built-in SMIL animations
 */
function animateDataFlow() {
    console.log('Data Flow animation is now handled by SMIL animations in the SVG');
}

/**
 * Animate the Modeling panel - now handled by the SVG's built-in SMIL animations
 */
function animateModeling() {
    console.log('Modeling animation is now handled by SMIL animations in the SVG');
}

/**
 * Animate the Prediction panel - now handled by the SVG's built-in SMIL animations
 */
function animatePrediction() {
    console.log('Prediction animation is now handled by SMIL animations in the SVG');
}

/**
 * Animate the Learning panel - now handled by the SVG's built-in SMIL animations
 */
function animateLearning() {
    console.log('Learning animation is now handled by SMIL animations in the SVG');
}

/**
 * Enhanced iOS Safari fixes for accordion behavior in ANALYZE section
 */
function initIOSSafariFixes() {
    const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    const isMobileViewport = window.innerWidth <= 768;

    if (isIOSSafari && isMobileViewport) {
        // Initializing iOS Safari fixes for ANALYZE section accordion

        // Apply Safari-specific body class
        document.body.classList.add('safari-ios-device');

        // Fix for iOS Safari scroll momentum
        document.body.style.webkitOverflowScrolling = 'touch';

        // Prevent zoom on input focus
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                input.style.fontSize = '16px';
            });
            input.addEventListener('blur', () => {
                input.style.fontSize = '';
            });
        });

        // Enhanced accordion fixes for ANALYZE section
        const analyzeAccordionHeaders = document.querySelectorAll('.predictive-ai-showcase .accordion-header');

        analyzeAccordionHeaders.forEach(header => {
            // Prevent default touch behavior that causes upward opening
            header.addEventListener('touchstart', function(e) {
                // Store the current scroll position
                this.dataset.scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            }, { passive: true });

            header.addEventListener('touchend', function(e) {
                // Prevent any unwanted scroll jumping
                const storedScrollTop = parseInt(this.dataset.scrollTop || '0');
                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

                // If scroll position changed significantly, restore it
                if (Math.abs(currentScrollTop - storedScrollTop) > 50) {
                    setTimeout(() => {
                        window.scrollTo(0, storedScrollTop);
                    }, 10);
                }
            }, { passive: true });

            // Enhanced click handling for Safari
            header.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Store current scroll position
                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

                // Apply Safari layout fix class
                document.body.classList.add('safari-layout-fix');

                // Force proper positioning
                this.style.position = 'relative';
                this.style.transform = 'translateZ(0)';
                this.style.webkitTransform = 'translateZ(0)';

                // Trigger the original click handler after positioning fixes
                setTimeout(() => {
                    // Manually trigger the accordion toggle
                    const targetId = this.getAttribute('data-target');
                    const targetPanel = document.getElementById(targetId + '-accordion');

                    if (targetPanel) {
                        // Close all other panels first
                        const allHeaders = document.querySelectorAll('.predictive-ai-showcase .accordion-header');
                        const allPanels = document.querySelectorAll('.predictive-ai-showcase .accordion-panel');

                        allHeaders.forEach(h => h.classList.remove('active'));
                        allPanels.forEach(p => p.style.display = 'none');

                        // Open the target panel
                        this.classList.add('active');
                        targetPanel.style.display = 'block';

                        // Apply positioning fixes to the panel
                        targetPanel.style.position = 'relative';
                        targetPanel.style.top = '0';
                        targetPanel.style.transform = 'translateZ(0)';
                        targetPanel.style.webkitTransform = 'translateZ(0)';

                        // Initialize animation
                        const animationId = targetId.replace('-accordion', '');
                        initAnimation(animationId);

                        // Removed auto-scroll for normal accordion behavior
                        // scrollToAccordionContent(this, targetPanel);
                    }
                }, 50);

                return false;
            });
        });

        // Fix for iOS Safari viewport issues
        const analyzeSection = document.querySelector('.predictive-ai-showcase');
        if (analyzeSection) {
            analyzeSection.style.webkitOverflowScrolling = 'auto';
            analyzeSection.style.isolation = 'isolate';
        }

        // Prevent iOS rubber band effect from interfering with accordion
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('.predictive-ai-showcase .accordion-panel')) {
                // Allow normal scrolling within accordion panels
                return;
            }
        }, { passive: true });
    }
}

/**
 * Accordion Functionality Debug Script (DISABLED IN PRODUCTION)
 * Moved from platform.php for better organization
 * Commented out to prevent any debug styling issues
 */
/*
function initAccordionDebug() {
    // Debug functionality disabled in production
    console.log('Debug mode disabled in production');
}
*/

// Bootstrap accordion scroll fix is now handled in main.js
