/* Ultra-minimal thin comparison design with working mobile tabs */

.thin-comparison {
    padding: 80px 0;
    background-color: #fafafa;
    position: relative;
    overflow: hidden;
}

/* Subtle background elements */
.bg-gradient-blob {
    position: absolute;
    width: 800px;
    height: 800px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(233, 88, 161, 0.02) 0%, rgba(143, 118, 245, 0.01) 50%, rgba(0, 0, 0, 0) 70%);
    z-index: 0;
    filter: blur(50px);
}

.blob-1 {
    top: -400px;
    right: -200px;
}

.blob-2 {
    bottom: -300px;
    left: -200px;
}

.comparison-container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section header */
.comparison-header {
    text-align: center;
    margin-bottom: 50px;
}

.comparison-tag {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    color: #e958a1;
    margin-bottom: 15px;
}

.comparison-tag::before {
    content: '';
    display: inline-block;
    width: 25px;
    height: 1px;
    background-color: #e958a1;
    margin-right: 10px;
}

.comparison-title {
    font-size: 42px;
    font-weight: 700;
    line-height: 1.2;
    color: #333;
    margin-bottom: 20px;
}

.comparison-title span {
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.comparison-subtitle {
    font-size: 18px;
    line-height: 1.6;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Mobile tabs */
.comparison-tabs {
    margin-bottom: 30px;
}

.nav-tabs.comparison-nav {
    border: none;
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    background-color: rgba(240, 240, 245, 0.5);
    padding: 5px;
    border-radius: 30px;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

.nav-tabs.comparison-nav .nav-item {
    margin: 0;
}

.nav-tabs.comparison-nav .nav-link {
    border: none;
    padding: 10px 25px;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    border-radius: 25px;
    transition: all 0.3s ease;
    background-color: transparent;
}

.nav-tabs.comparison-nav .nav-link.active {
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.2);
}

/* Tab content */
.tab-content.comparison-content {
    position: relative;
}

/* Cards */
.comparison-card {
    background-color: white;
    border-radius: 12px;
    padding: 35px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    border: 1px solid rgba(240, 240, 245, 0.8);
}

/* Traditional card */
.comparison-card.traditional {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
}

.comparison-card.traditional:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transform: translateY(-5px);
}

/* Adzeta card */
.comparison-card.adzeta {
    box-shadow: 0 5px 20px rgba(233, 88, 161, 0.03);
    position: relative;
}

.comparison-card.adzeta:before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 12px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.comparison-card.adzeta:hover {
    box-shadow: 0 10px 30px rgba(233, 88, 161, 0.08);
    transform: translateY(-5px);
    border-color: transparent;
}

.comparison-card.adzeta:hover:before {
    opacity: 1;
}

.comparison-card.adzeta:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    border-radius: 11px;
    z-index: -1;
}

/* Card header */
.card-header {
    margin-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.card-badge {
    display: inline-flex;
    align-items: center;
    font-size: 13px;
    font-weight: 600;
    padding: 6px 15px;
    border-radius: 30px;
    margin-bottom: 20px;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.card-icon i {
    font-size: 24px;
}

.traditional-badge {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.traditional-icon {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.adzeta-badge {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.adzeta-icon {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.recommended-tag {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 5px 12px;
    border-radius: 20px;
}

.card-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.card-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 0;
}

/* Feature list */
.feature-list {
    margin-top: 30px;
    flex-grow: 1;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.feature-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.feature-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.feature-icon.negative {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.feature-icon.positive {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.feature-text {
    font-size: 16px;
    color: #555;
    line-height: 1.5;
}

/* Results section */
.results-section {
    background: white;
    border-radius: 12px;
    padding: 50px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
    text-align: center;
    border: 1px solid rgba(240, 240, 245, 0.8);
    position: relative;
    overflow: hidden;
}

.results-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
}

.results-title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.stat-item {
    text-align: center;
    position: relative;
}

.stat-value {
    font-size: 48px;
    font-weight: 700;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 15px;
    position: relative;
}

.stat-label {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .thin-comparison {
        padding: 60px 0;
    }
    
    .comparison-header {
        margin-bottom: 30px;
    }
    
    .comparison-title {
        font-size: 32px;
    }
    
    .comparison-card {
        padding: 25px;
    }
    
    .card-title {
        font-size: 22px;
    }
    
    .results-section {
        padding: 30px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stat-item:not(:last-child) {
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .stat-value {
        font-size: 36px;
    }
}
