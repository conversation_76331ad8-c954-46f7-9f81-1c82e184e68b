<?php

namespace AdZetaAdmin\Services;

class EditorJSParser
{
    private $templateClasses = [
        'professional-enhanced' => [
            'paragraph' => 'content-paragraph mb-4',
            'header' => [
                1 => 'display-4 fw-bold mb-4 text-primary',
                2 => 'h2 fw-semibold mb-3 section-heading',
                3 => 'h3 fw-medium mb-3 subsection-heading',
                4 => 'h4 mb-2',
                5 => 'h5 mb-2',
                6 => 'h6 mb-2'
            ],
            'list' => 'styled-list mb-4',
            'list_item' => 'list-item mb-2',
            'quote' => 'professional-quote blockquote border-start border-primary border-4 ps-4 py-3 mb-4 bg-light',
            'code' => 'code-block bg-dark text-light p-3 rounded mb-4',
            'delimiter' => 'section-divider text-center my-5',
            'image' => 'content-image mb-4',
            'table' => 'table table-striped table-hover mb-4'
        ]
    ];

    private $currentTemplate = 'professional-enhanced';

    /**
     * Parse Editor.js JSON blocks to HTML with template-aware classes
     */
    public function parseToHTML(array $blocks, string $template = 'professional-enhanced'): string
    {
        $this->currentTemplate = $template;
        $html = '';

        foreach ($blocks as $block) {
            $html .= $this->renderBlock($block);
        }

        return $html;
    }

    /**
     * Parse Editor.js JSON blocks to plain text
     */
    public function parseToText(array $blocks): string
    {
        $text = '';

        foreach ($blocks as $block) {
            $text .= $this->extractText($block) . "\n\n";
        }

        return trim($text);
    }

    /**
     * Get template-specific CSS classes
     */
    private function getTemplateClasses(string $element, $level = null): string
    {
        $templateClasses = $this->templateClasses[$this->currentTemplate] ?? [];

        if ($level !== null && isset($templateClasses[$element][$level])) {
            return $templateClasses[$element][$level];
        }

        return $templateClasses[$element] ?? '';
    }

    /**
     * Generate excerpt from Editor.js blocks
     */
    public function generateExcerpt(array $blocks, int $length = 160): string
    {
        $text = $this->parseToText($blocks);

        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }

    /**
     * Render a single block to HTML
     */
    private function renderBlock(array $block): string
    {
        $type = $block['type'] ?? '';
        $data = $block['data'] ?? [];

        switch ($type) {
            case 'paragraph':
                return $this->renderParagraph($data);

            case 'header':
                return $this->renderHeader($data);

            case 'list':
                return $this->renderList($data);

            case 'image':
                return $this->renderImage($data);

            case 'quote':
                return $this->renderQuote($data);

            case 'code':
                return $this->renderCode($data);

            case 'delimiter':
                return $this->renderDelimiter($data);

            case 'table':
                return $this->renderTable($data);

            case 'embed':
                return $this->renderEmbed($data);

            case 'linkTool':
                return $this->renderLinkTool($data);

            case 'raw':
                return $this->renderRaw($data);

            case 'checklist':
                return $this->renderChecklist($data);

            // Custom AdZeta blocks
            case 'styledCard':
                return $this->renderStyledCard($data);

            case 'statisticsTool':
                return $this->renderStatistics($data);

            case 'ctaTool':
                return $this->renderCTA($data);

            default:
                return $this->renderUnknown($block);
        }
    }

    /**
     * Render paragraph block with template classes
     */
    private function renderParagraph(array $data): string
    {
        $text = $data['text'] ?? '';

        if (empty($text)) {
            return '';
        }

        // Decode HTML entities to restore formatting tags
        $text = $this->decodeHTMLEntities($text);

        // Get template-specific classes
        $classes = $this->getTemplateClasses('paragraph');

        return "<p class=\"{$classes}\">{$text}</p>\n";
    }

    /**
     * Render header block with template classes
     */
    private function renderHeader(array $data): string
    {
        $text = $data['text'] ?? '';
        $level = $data['level'] ?? 2;

        // Ensure level is between 1-6
        $level = max(1, min(6, $level));

        // Decode HTML entities to restore formatting tags
        $text = $this->decodeHTMLEntities($text);

        // Get template-specific classes for this header level
        $classes = $this->getTemplateClasses('header', $level);

        return "<h{$level} class=\"{$classes}\">{$text}</h{$level}>\n";
    }

    /**
     * Render list block
     */
    private function renderList(array $data): string
    {
        $style = $data['style'] ?? 'unordered';
        $items = $data['items'] ?? [];

        if (empty($items)) {
            return '';
        }

        $tag = $style === 'ordered' ? 'ol' : 'ul';
        $html = "<{$tag}>\n";

        foreach ($items as $item) {
            // Decode HTML entities to restore formatting tags in list items
            $decodedItem = $this->decodeHTMLEntities($item);
            $html .= "<li>{$decodedItem}</li>\n";
        }

        $html .= "</{$tag}>\n";

        return $html;
    }

    /**
     * Render image block
     */
    private function renderImage(array $data): string
    {
        $url = $data['file']['url'] ?? '';
        $caption = $data['caption'] ?? '';
        $withBorder = $data['withBorder'] ?? false;
        $withBackground = $data['withBackground'] ?? false;
        $stretched = $data['stretched'] ?? false;

        if (empty($url)) {
            return '';
        }

        $classes = [];
        if ($withBorder) $classes[] = 'with-border';
        if ($withBackground) $classes[] = 'with-background';
        if ($stretched) $classes[] = 'stretched';

        $classAttr = !empty($classes) ? ' class="' . implode(' ', $classes) . '"' : '';

        $html = "<figure{$classAttr}>\n";
        $html .= "<img src=\"{$url}\" alt=\"{$caption}\">\n";

        if (!empty($caption)) {
            $html .= "<figcaption>{$caption}</figcaption>\n";
        }

        $html .= "</figure>\n";

        return $html;
    }

    /**
     * Render quote block
     */
    private function renderQuote(array $data): string
    {
        $text = $data['text'] ?? '';
        $caption = $data['caption'] ?? '';
        $alignment = $data['alignment'] ?? 'left';

        $html = "<blockquote class=\"align-{$alignment}\">\n";
        $html .= "<p>{$text}</p>\n";

        if (!empty($caption)) {
            $html .= "<cite>{$caption}</cite>\n";
        }

        $html .= "</blockquote>\n";

        return $html;
    }

    /**
     * Render code block
     */
    private function renderCode(array $data): string
    {
        $code = $data['code'] ?? '';

        return "<pre><code>" . htmlspecialchars($code) . "</code></pre>\n";
    }

    /**
     * Render delimiter block
     */
    private function renderDelimiter(array $data): string
    {
        return "<hr>\n";
    }

    /**
     * Render table block
     */
    private function renderTable(array $data): string
    {
        $content = $data['content'] ?? [];
        $withHeadings = $data['withHeadings'] ?? false;

        if (empty($content)) {
            return '';
        }

        $html = "<table>\n";

        foreach ($content as $rowIndex => $row) {
            $isHeader = $withHeadings && $rowIndex === 0;
            $tag = $isHeader ? 'th' : 'td';

            $html .= "<tr>\n";

            foreach ($row as $cell) {
                $html .= "<{$tag}>{$cell}</{$tag}>\n";
            }

            $html .= "</tr>\n";
        }

        $html .= "</table>\n";

        return $html;
    }

    /**
     * Render embed block
     */
    private function renderEmbed(array $data): string
    {
        $service = $data['service'] ?? '';
        $source = $data['source'] ?? '';
        $embed = $data['embed'] ?? '';
        $width = $data['width'] ?? 580;
        $height = $data['height'] ?? 320;
        $caption = $data['caption'] ?? '';

        if (empty($embed)) {
            return '';
        }

        $html = "<figure class=\"embed embed-{$service}\">\n";
        $html .= "<div class=\"embed-container\">\n";
        $html .= $embed;
        $html .= "</div>\n";

        if (!empty($caption)) {
            $html .= "<figcaption>{$caption}</figcaption>\n";
        }

        $html .= "</figure>\n";

        return $html;
    }

    /**
     * Render link tool block
     */
    private function renderLinkTool(array $data): string
    {
        $link = $data['link'] ?? '';
        $meta = $data['meta'] ?? [];

        $title = $meta['title'] ?? $link;
        $description = $meta['description'] ?? '';
        $image = $meta['image']['url'] ?? '';

        $html = "<div class=\"link-tool\">\n";

        if (!empty($image)) {
            $html .= "<div class=\"link-tool__image\">\n";
            $html .= "<img src=\"{$image}\" alt=\"{$title}\">\n";
            $html .= "</div>\n";
        }

        $html .= "<div class=\"link-tool__content\">\n";
        $html .= "<h3 class=\"link-tool__title\"><a href=\"{$link}\" target=\"_blank\">{$title}</a></h3>\n";

        if (!empty($description)) {
            $html .= "<p class=\"link-tool__description\">{$description}</p>\n";
        }

        $html .= "<span class=\"link-tool__anchor\">{$link}</span>\n";
        $html .= "</div>\n";
        $html .= "</div>\n";

        return $html;
    }

    /**
     * Render raw HTML block
     */
    private function renderRaw(array $data): string
    {
        $html = $data['html'] ?? '';

        // Basic security: strip script tags
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);

        return $html . "\n";
    }

    /**
     * Render checklist block
     */
    private function renderChecklist(array $data): string
    {
        $items = $data['items'] ?? [];

        if (empty($items)) {
            return '';
        }

        $html = "<ul class=\"checklist\">\n";

        foreach ($items as $item) {
            $text = $item['text'] ?? '';
            $checked = $item['checked'] ?? false;
            $checkedClass = $checked ? ' checked' : '';
            $checkedAttr = $checked ? ' checked' : '';

            $html .= "<li class=\"checklist__item{$checkedClass}\">\n";
            $html .= "<input type=\"checkbox\"{$checkedAttr} disabled>\n";
            $html .= "<span>{$text}</span>\n";
            $html .= "</li>\n";
        }

        $html .= "</ul>\n";

        return $html;
    }

    /**
     * Render unknown block type
     */
    private function renderUnknown(array $block): string
    {
        $type = $block['type'] ?? 'unknown';

        // For development, show the block data
        if (defined('ADZETA_DEBUG') && ADZETA_DEBUG) {
            return "<!-- Unknown block type: {$type} -->\n<pre>" . htmlspecialchars(json_encode($block, JSON_PRETTY_PRINT)) . "</pre>\n";
        }

        return "<!-- Unknown block type: {$type} -->\n";
    }

    /**
     * Extract text content from a block
     */
    private function extractText(array $block): string
    {
        $type = $block['type'] ?? '';
        $data = $block['data'] ?? [];

        switch ($type) {
            case 'paragraph':
                return strip_tags($data['text'] ?? '');

            case 'header':
                return strip_tags($data['text'] ?? '');

            case 'list':
                $items = $data['items'] ?? [];
                return implode('. ', array_map('strip_tags', $items));

            case 'quote':
                $text = strip_tags($data['text'] ?? '');
                $caption = strip_tags($data['caption'] ?? '');
                return $text . ($caption ? " - {$caption}" : '');

            case 'code':
                return $data['code'] ?? '';

            case 'table':
                $content = $data['content'] ?? [];
                $text = '';
                foreach ($content as $row) {
                    $text .= implode(' ', array_map('strip_tags', $row)) . '. ';
                }
                return trim($text);

            case 'checklist':
                $items = $data['items'] ?? [];
                $text = '';
                foreach ($items as $item) {
                    $text .= strip_tags($item['text'] ?? '') . '. ';
                }
                return trim($text);

            default:
                return '';
        }
    }

    /**
     * Get reading time estimate from blocks
     */
    public function getReadingTime(array $blocks): int
    {
        $text = $this->parseToText($blocks);
        $wordCount = str_word_count($text);

        // Average reading speed: 200 words per minute
        return max(1, ceil($wordCount / 200));
    }

    /**
     * Render styled card block
     */
    private function renderStyledCard(array $data): string
    {
        $content = $data['content'] ?? '';
        $backgroundColor = $data['backgroundColor'] ?? '#E6D8F2';
        $textColor = $data['textColor'] ?? '#2B0B3A';
        $borderColor = $data['borderColor'] ?? '#FF4081';

        return "<div class='styled-card' style='background: {$backgroundColor}; color: {$textColor}; border-left: 4px solid {$borderColor}; border-radius: 12px; padding: 24px; margin: 16px 0; box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);'>{$content}</div>\n";
    }

    /**
     * Render statistics block
     */
    private function renderStatistics(array $data): string
    {
        $value = $data['value'] ?? '100';
        $unit = $data['unit'] ?? '%';
        $label = $data['label'] ?? 'Statistic';
        $color = $data['color'] ?? '#FF4081';

        return "<div class='statistics-display' style='text-align: center; padding: 24px; background: #F5F5F5; border-radius: 12px; margin: 16px 0; border: 2px solid {$color};'><div class='stat-value' style='font-size: 3rem; font-weight: 700; color: {$color}; line-height: 1; margin-bottom: 8px;'>{$value}<span style='font-size: 2rem; margin-left: 4px;'>{$unit}</span></div><div class='stat-label' style='font-size: 1rem; color: #2B0B3A; font-weight: 500;'>{$label}</div></div>\n";
    }

    /**
     * Render CTA block
     */
    private function renderCTA(array $data): string
    {
        $title = $data['title'] ?? 'Ready to Get Started?';
        $description = $data['description'] ?? 'Transform your marketing today';
        $buttonText = $data['buttonText'] ?? 'Start Free Trial';
        $buttonUrl = $data['buttonUrl'] ?? '/demo';
        $style = $data['style'] ?? 'gradient';
        $alignment = $data['alignment'] ?? 'center';

        // Determine background based on style
        $background = '';
        $textColor = 'white';
        $descColor = '#E6D8F2';

        switch ($style) {
            case 'gradient':
                $background = 'background: linear-gradient(135deg, #2B0B3A, #FF4081);';
                break;
            case 'solid-purple':
                $background = 'background: #2B0B3A;';
                break;
            case 'solid-pink':
                $background = 'background: #FF4081;';
                break;
            case 'outline':
                $background = 'background: white; border: 2px solid #FF4081;';
                $textColor = '#2B0B3A';
                $descColor = '#666';
                break;
        }

        return "<div class='cta-block' style='{$background} border-radius: 16px; padding: 48px; margin: 32px 0; text-align: {$alignment}; box-shadow: 0 8px 32px rgba(43, 11, 58, 0.2);'><h3 style='color: {$textColor}; font-size: 2rem; margin-bottom: 16px; margin-top: 0;'>{$title}</h3><p style='color: {$descColor}; font-size: 1.2rem; margin-bottom: 24px; max-width: 600px; margin-left: auto; margin-right: auto;'>{$description}</p><a href='{$buttonUrl}' class='cta-button' style='display: inline-block; background: white; color: #2B0B3A; padding: 16px 32px; border-radius: 8px; text-decoration: none; font-weight: 700; font-size: 1.1rem; box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);'>{$buttonText}</a></div>\n";
    }

    /**
     * Get word count from blocks
     */
    public function getWordCount(array $blocks): int
    {
        $text = $this->parseToText($blocks);
        return str_word_count($text);
    }

    /**
     * Decode HTML entities to restore formatting tags
     * Converts &lt;strong&gt; back to <strong>, etc.
     */
    private function decodeHTMLEntities(string $text): string
    {
        if (empty($text)) {
            return '';
        }

        // Decode HTML entities to restore formatting tags
        $decoded = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Additional decoding for common cases that might be double-encoded
        $decoded = str_replace(
            ['&amp;lt;', '&amp;gt;', '&amp;amp;'],
            ['&lt;', '&gt;', '&amp;'],
            $decoded
        );

        // Final decode
        $decoded = html_entity_decode($decoded, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $decoded;
    }
}
