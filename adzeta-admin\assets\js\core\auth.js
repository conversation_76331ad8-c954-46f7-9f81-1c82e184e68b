/**
 * AdZeta Admin Panel - Authentication Module
 * Handles user login, logout, and session management
 */

window.AdZetaAuth = {
    // Initialize authentication module
    init() {
        this.bindEvents();
        console.log('Authentication module initialized');
    },

    // Bind event listeners
    bindEvents() {
        // Login form submission
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', this.handleLogout.bind(this));
        }

        // Enter key on password field
        const passwordField = document.getElementById('password');
        if (passwordField) {
            passwordField.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleLogin(e);
                }
            });
        }

        // Clear error on input
        const usernameField = document.getElementById('username');
        const inputs = [usernameField, passwordField];
        
        inputs.forEach(input => {
            if (input) {
                input.addEventListener('input', () => {
                    window.AdZetaApp.hideLoginError();
                });
            }
        });
    },

    // Handle login form submission
    async handleLogin(event) {
        event.preventDefault();
        
        // Get form data
        const credentials = this.getLoginCredentials();
        
        // Validate input
        if (!this.validateCredentials(credentials)) {
            return;
        }

        // Attempt login
        const result = await window.AdZetaApp.login(credentials);
        
        if (result.success) {
            console.log('Login successful');
            // Clear form
            this.clearLoginForm();
        } else {
            console.error('Login failed:', result.error);
        }
    },

    // Handle logout
    handleLogout(event) {
        event.preventDefault();
        
        if (confirm('Are you sure you want to logout?')) {
            window.AdZetaApp.logout();
            console.log('User logged out');
        }
    },

    // Get login credentials from form
    getLoginCredentials() {
        return {
            username: document.getElementById('username').value.trim(),
            password: document.getElementById('password').value,
            remember: document.getElementById('remember').checked
        };
    },

    // Validate login credentials
    validateCredentials(credentials) {
        if (!credentials.username) {
            window.AdZetaApp.showLoginError('Please enter your username');
            document.getElementById('username').focus();
            return false;
        }

        if (!credentials.password) {
            window.AdZetaApp.showLoginError('Please enter your password');
            document.getElementById('password').focus();
            return false;
        }

        if (credentials.username.length < 3) {
            window.AdZetaApp.showLoginError('Username must be at least 3 characters long');
            document.getElementById('username').focus();
            return false;
        }

        if (credentials.password.length < 6) {
            window.AdZetaApp.showLoginError('Password must be at least 6 characters long');
            document.getElementById('password').focus();
            return false;
        }

        return true;
    },

    // Clear login form
    clearLoginForm() {
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        document.getElementById('remember').checked = false;
        window.AdZetaApp.hideLoginError();
    },

    // Check if user is authenticated
    isAuthenticated() {
        return window.AdZetaApp.state.isAuthenticated;
    },

    // Get current user
    getCurrentUser() {
        return window.AdZetaApp.state.user;
    },

    // Get authentication token
    getToken() {
        return window.AdZetaApp.state.token;
    },

    // Refresh authentication token
    async refreshToken() {
        try {
            const response = await window.AdZetaApp.apiRequest('/auth/refresh', {
                method: 'POST'
            });

            if (response.success) {
                // Update token
                window.AdZetaApp.state.token = response.token;
                localStorage.setItem('adzeta_token', response.token);
                
                console.log('Token refreshed successfully');
                return true;
            } else {
                throw new Error('Token refresh failed');
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            
            // If refresh fails, logout user
            window.AdZetaApp.logout();
            return false;
        }
    },

    // Check token expiration
    isTokenExpired() {
        const token = this.getToken();
        if (!token) return true;

        try {
            // Decode JWT token (basic check)
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            
            return payload.exp < currentTime;
        } catch (error) {
            console.error('Error checking token expiration:', error);
            return true;
        }
    },

    // Auto-refresh token before expiration
    setupTokenRefresh() {
        const token = this.getToken();
        if (!token) return;

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const expirationTime = payload.exp * 1000;
            const currentTime = Date.now();
            const timeUntilExpiration = expirationTime - currentTime;
            
            // Refresh token 5 minutes before expiration
            const refreshTime = timeUntilExpiration - (5 * 60 * 1000);
            
            if (refreshTime > 0) {
                setTimeout(() => {
                    this.refreshToken();
                }, refreshTime);
            }
        } catch (error) {
            console.error('Error setting up token refresh:', error);
        }
    },

    // Handle authentication errors
    handleAuthError(error) {
        console.error('Authentication error:', error);
        
        if (error.status === 401) {
            // Unauthorized - logout user
            window.AdZetaApp.logout();
            window.AdZetaApp.showNotification('Your session has expired. Please login again.', 'warning');
        } else if (error.status === 403) {
            // Forbidden
            window.AdZetaApp.showNotification('You do not have permission to perform this action.', 'danger');
        } else {
            // Other errors
            window.AdZetaApp.showNotification('An authentication error occurred. Please try again.', 'danger');
        }
    },

    // Validate session
    async validateSession() {
        if (!this.isAuthenticated()) {
            return false;
        }

        try {
            const response = await window.AdZetaApp.apiRequest('/auth/validate', {
                method: 'GET'
            });

            if (response.success) {
                return true;
            } else {
                throw new Error('Session validation failed');
            }
        } catch (error) {
            console.error('Session validation error:', error);
            this.handleAuthError(error);
            return false;
        }
    },

    // Setup periodic session validation
    setupSessionValidation() {
        // Validate session every 15 minutes
        setInterval(() => {
            if (this.isAuthenticated()) {
                this.validateSession();
            }
        }, 15 * 60 * 1000);
    },

    // Handle page visibility change
    handleVisibilityChange() {
        if (document.visibilityState === 'visible' && this.isAuthenticated()) {
            // Page became visible, validate session
            this.validateSession();
        }
    },

    // Initialize session management
    initSessionManagement() {
        // Setup token refresh
        this.setupTokenRefresh();
        
        // Setup session validation
        this.setupSessionValidation();
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
        
        console.log('Session management initialized');
    }
};
