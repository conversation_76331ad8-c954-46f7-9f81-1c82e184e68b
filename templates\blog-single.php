<?php
/**
 * Blog Single Post Template
 * Displays individual blog post
 */

// Include header
include __DIR__ . '/../header.php';
?>

<main class="main-content">
    <!-- Blog Post Section -->
    <section class="blog-post-section py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <article class="blog-post">
                        <!-- Post Header -->
                        <header class="blog-post-header mb-4">
                            <h1 class="blog-post-title"><?php echo htmlspecialchars($post['title']); ?></h1>

                            <div class="blog-post-meta">
                                <span class="blog-date">
                                    Published on <?php echo formatDate($post['published_at'], 'F j, Y'); ?>
                                </span>
                                <?php if ($post['reading_time']): ?>
                                    <span class="blog-reading-time">
                                        • <?php echo $post['reading_time']; ?> min read
                                    </span>
                                <?php endif; ?>
                            </div>
                        </header>

                        <!-- Featured Image -->
                        <?php if ($post['featured_image']): ?>
                            <div class="blog-post-image mb-4">
                                <img src="<?php echo htmlspecialchars($post['featured_image']); ?>"
                                     alt="<?php echo htmlspecialchars($post['title']); ?>"
                                     class="img-fluid rounded">
                            </div>
                        <?php endif; ?>

                        <!-- Post Content -->
                        <div class="blog-post-content">
                            <?php
                            // Process content through EditorJSParser if content_blocks are available
                            if (!empty($post['content_blocks']) && is_array($post['content_blocks'])) {
                                // Use EditorJSParser to render custom blocks
                                require_once __DIR__ . '/../adzeta-admin/src/Services/EditorJSParser.php';
                                $parser = new \AdZetaAdmin\Services\EditorJSParser();

                                // Extract blocks from Editor.js format
                                $blocks = $post['content_blocks']['blocks'] ?? $post['content_blocks'];

                                if (is_array($blocks) && !empty($blocks)) {
                                    echo $parser->parseToHTML($blocks);
                                } else {
                                    // Fallback to regular content
                                    echo $post['content'];
                                }
                            } else {
                                // Fallback to regular content
                                echo $post['content'];
                            }
                            ?>
                        </div>

                        <!-- Post Footer -->
                        <footer class="blog-post-footer mt-5 pt-4 border-top">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="share-buttons">
                                        <span class="share-label">Share this post:</span>
                                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode($seo->canonicalUrl ?? ''); ?>&text=<?php echo urlencode($post['title']); ?>"
                                           target="_blank" class="btn btn-sm btn-outline-primary">
                                            Twitter
                                        </a>
                                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode($seo->canonicalUrl ?? ''); ?>"
                                           target="_blank" class="btn btn-sm btn-outline-primary">
                                            LinkedIn
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <a href="/blog" class="btn btn-outline-secondary">
                                        ← Back to Blog
                                    </a>
                                </div>
                            </div>
                        </footer>
                    </article>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Posts Section -->
    <section class="related-posts-section py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h3 class="text-center mb-4">Related Posts</h3>
                    <div class="text-center">
                        <a href="/blog" class="btn btn-primary">View All Posts</a>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<?php
// Include footer
include __DIR__ . '/../footer.php';
?>
