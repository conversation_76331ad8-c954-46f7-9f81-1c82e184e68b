<?php

namespace AdZetaAdmin\API;

use AdZetaAdmin\Core\JWTAuth;

/**
 * Base API Controller
 * Provides common functionality for all API controllers
 */
class BaseController
{
    protected $auth;
    protected $db;

    public function __construct()
    {
        global $admin_db;
        $this->db = $admin_db;
        $this->auth = new JWTAuth();
    }

    /**
     * Get request data (JSON or form data)
     */
    protected function getRequestData()
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';

        if (strpos($contentType, 'application/json') !== false) {
            $input = file_get_contents('php://input');
            return json_decode($input, true) ?: [];
        }

        return $_POST;
    }

    /**
     * Get query parameters
     */
    protected function getQueryParams()
    {
        return $_GET;
    }

    /**
     * Get authorization header
     */
    protected function getAuthHeader()
    {
        $headers = getallheaders();
        return $headers['Authorization'] ?? $headers['authorization'] ?? null;
    }

    /**
     * Extract JWT token from authorization header
     */
    protected function getTokenFromHeader()
    {
        $authHeader = $this->getAuthHeader();

        if ($authHeader && strpos($authHeader, 'Bearer ') === 0) {
            return substr($authHeader, 7);
        }

        return null;
    }

    /**
     * Require authentication for this endpoint
     */
    protected function requireAuth()
    {
        $token = $this->getTokenFromHeader();

        if (!$token) {
            $this->error('Authorization token required', 401);
            exit;
        }

        $payload = $this->auth->verifyToken($token);

        if (!$payload) {
            $this->error('Invalid or expired token', 401);
            exit;
        }

        return $payload;
    }

    /**
     * Require specific permission
     */
    protected function requirePermission($permission)
    {
        $payload = $this->requireAuth();

        // Check if user has permission
        if ($payload['role'] !== 'admin') {
            $rolePermissions = [
                'editor' => [
                    'view_dashboard', 'manage_posts', 'create_posts', 'view_posts',
                    'manage_seo', 'manage_media', 'upload_media'
                ],
                'author' => [
                    'view_dashboard', 'create_posts', 'view_posts', 'upload_media'
                ]
            ];

            $userPermissions = $rolePermissions[$payload['role']] ?? [];

            if (!in_array($permission, $userPermissions)) {
                $this->error('Insufficient permissions', 403);
                exit;
            }
        }

        return $payload;
    }

    /**
     * Send success response
     */
    protected function success($data = [], $statusCode = 200)
    {
        http_response_code($statusCode);

        $response = [
            'success' => true,
            'timestamp' => date('c'),
        ];

        if (is_array($data)) {
            $response = array_merge($response, $data);
        } else {
            $response['data'] = $data;
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * Send error response
     */
    protected function error($message, $statusCode = 400, $details = null)
    {
        http_response_code($statusCode);

        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => date('c'),
        ];

        if ($details !== null) {
            $response['details'] = $details;
        }

        if (ADZETA_DEBUG) {
            $response['debug'] = [
                'request_method' => $_SERVER['REQUEST_METHOD'],
                'request_uri' => $_SERVER['REQUEST_URI'],
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ];
        }

        echo json_encode($response, JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * Validate required fields
     */
    protected function validateRequired($data, $fields)
    {
        $missing = [];

        foreach ($fields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing[] = $field;
            }
        }

        if (!empty($missing)) {
            $this->error('Missing required fields: ' . implode(', ', $missing), 400);
        }
    }

    /**
     * Sanitize input data (preserves HTML in content fields)
     */
    protected function sanitize($data, $preserveHTML = false)
    {
        if (is_array($data)) {
            return array_map(function($item) use ($preserveHTML) {
                return $this->sanitize($item, $preserveHTML);
            }, $data);
        }

        // Don't sanitize HTML if preserveHTML is true
        if ($preserveHTML) {
            return trim($data);
        }

        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Sanitize post data while preserving HTML in content fields
     */
    protected function sanitizePostData($data)
    {
        // Fields that should preserve HTML/JSON content without encoding
        $htmlFields = ['content', 'excerpt', 'meta_description', 'og_description', 'content_blocks'];
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (in_array($key, $htmlFields)) {
                // Preserve HTML/JSON in content fields (no htmlspecialchars)
                $sanitized[$key] = $this->sanitizeHTML($value);
            } else {
                // Regular sanitization for other fields
                $sanitized[$key] = $this->sanitize($value);
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize HTML content while preserving formatting tags
     * Also handles JSON content (like content_blocks) without modification
     */
    protected function sanitizeHTML($content)
    {
        if (empty($content)) {
            return '';
        }

        // If content looks like JSON, don't modify it (for content_blocks field)
        $trimmedContent = trim($content);
        if (is_string($content) && !empty($trimmedContent) &&
            ($trimmedContent[0] === '{' || $trimmedContent[0] === '[')) {
            // Validate JSON and return as-is if valid
            $decoded = json_decode($trimmedContent, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $trimmedContent; // Return original JSON without modification
            }
        }

        // For HTML content, remove dangerous tags but keep formatting
        $content = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $content);
        $content = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $content);
        $content = preg_replace('/<iframe[^>]*>.*?<\/iframe>/is', '', $content);
        $content = preg_replace('/<object[^>]*>.*?<\/object>/is', '', $content);
        $content = preg_replace('/<embed[^>]*>/i', '', $content);

        // Keep allowed formatting tags: strong, em, b, i, u, a, code, span, etc.
        // This preserves HTML formatting while removing dangerous content

        return trim($content);
    }

    /**
     * Paginate results
     */
    protected function paginate($query, $params = [], $page = 1, $perPage = 20)
    {
        $page = max(1, (int)$page);
        $perPage = min(100, max(1, (int)$perPage));
        $offset = ($page - 1) * $perPage;

        // Get total count
        $countQuery = "SELECT COUNT(*) FROM ($query) as count_table";
        $total = $this->db->fetchColumn($countQuery, $params);

        // Get paginated results
        $paginatedQuery = $query . " LIMIT $perPage OFFSET $offset";
        $data = $this->db->fetchAll($paginatedQuery, $params);

        return [
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => (int)$total,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
    }
}
