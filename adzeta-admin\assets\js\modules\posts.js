/**
 * AdZeta Admin Panel - Posts Management Module
 * Handles blog posts listing, filtering, and management
 */

window.AdZetaPosts = {
    // Posts data
    data: {
        posts: [],
        categories: [],
        loading: false,
        filters: {
            search: '',
            status: '',
            category: ''
        },
        pagination: {
            page: 1,
            limit: 20,
            total: 0
        }
    },

    // Initialize posts module
    init() {
        this.bindEvents();
        console.log('Posts module initialized');
    },

    // Bind event listeners
    bindEvents() {
        // Search input
        const searchInput = document.getElementById('postsSearch');
        if (searchInput) {
            searchInput.addEventListener('input', 
                window.AdZetaApp.debounce(this.handleSearch.bind(this), 500)
            );
        }

        // Filter dropdowns
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', this.handleStatusFilter.bind(this));
        }

        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', this.handleCategoryFilter.bind(this));
        }

        // Refresh button
        const refreshBtn = document.getElementById('refreshPostsBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', this.refresh.bind(this));
        }

        // Bulk mode toggle
        const bulkModeBtn = document.getElementById('bulkModeBtn');
        if (bulkModeBtn) {
            bulkModeBtn.addEventListener('click', this.toggleBulkMode.bind(this));
        }

        // Select all checkbox
        const selectAllCheckbox = document.getElementById('selectAllPosts');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', this.handleSelectAll.bind(this));
        }
    },

    // Load posts data
    async load() {
        if (this.data.loading) return;

        console.log('Loading posts data...');

        try {
            this.data.loading = true;
            this.showLoading();

            // Load categories and posts
            await Promise.all([
                this.loadCategories(),
                this.loadPosts()
            ]);

            this.render();
            console.log('Posts loaded successfully');
        } catch (error) {
            console.error('Error loading posts:', error);
            this.showError('Failed to load posts');
        } finally {
            this.data.loading = false;
        }
    },

    // Load categories for filter
    async loadCategories() {
        try {
            const response = await window.AdZetaApp.apiRequest('/categories');

            if (response.success) {
                this.data.categories = response.categories || [];
            } else {
                throw new Error(response.message || 'Failed to load categories');
            }

            this.renderCategoryFilter();
        } catch (error) {
            console.error('Error loading categories:', error);
            throw error;
        }
    },

    // Load posts with current filters
    async loadPosts() {
        try {
            // Build query parameters
            const params = new URLSearchParams();
            if (this.data.filters.search) params.append('search', this.data.filters.search);
            if (this.data.filters.status) params.append('status', this.data.filters.status);
            if (this.data.filters.category) params.append('category', this.data.filters.category);
            params.append('page', this.data.pagination.page);
            params.append('limit', this.data.pagination.limit);

            console.log('Loading posts with params:', params.toString());
            const response = await window.AdZetaApp.apiRequest(`/posts?${params.toString()}`);
            console.log('Posts API response:', response);

            if (response.success) {
                this.data.posts = response.posts || [];
                this.data.pagination.total = response.pagination?.total || 0;
                console.log('Loaded posts:', this.data.posts.length);
            } else {
                throw new Error(response.message || 'Failed to load posts');
            }
        } catch (error) {
            console.error('Error loading posts:', error);
            throw error;
        }
    },

    // Apply current filters to posts
    applyFilters(posts) {
        let filtered = [...posts];

        // Search filter
        if (this.data.filters.search) {
            const search = this.data.filters.search.toLowerCase();
            filtered = filtered.filter(post => 
                post.title.toLowerCase().includes(search) ||
                post.excerpt.toLowerCase().includes(search) ||
                post.author.toLowerCase().includes(search)
            );
        }

        // Status filter
        if (this.data.filters.status) {
            filtered = filtered.filter(post => post.status === this.data.filters.status);
        }

        // Category filter
        if (this.data.filters.category) {
            filtered = filtered.filter(post => post.category === this.data.filters.category);
        }

        return filtered;
    },

    // Render posts view
    render() {
        this.renderPostsTable();
        this.renderPagination();
    },

    // Render category filter dropdown
    renderCategoryFilter() {
        const categoryFilter = document.getElementById('categoryFilter');
        if (!categoryFilter || !this.data.categories) return;

        const options = this.data.categories.map(category => 
            `<option value="${category.name}">${category.name} (${category.post_count})</option>`
        ).join('');

        categoryFilter.innerHTML = `
            <option value="">All Categories</option>
            ${options}
        `;
    },

    // Render posts table
    renderPostsTable() {
        const tbody = document.getElementById('postsTableBody');
        if (!tbody) return;

        console.log('Rendering posts table with', this.data.posts.length, 'posts');

        if (this.data.posts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No posts found. <a href="#" onclick="AdZetaPostEditor.createNew()">Create your first post</a></p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.data.posts.map(post => `
            <tr>
                <td>
                    <input type="checkbox" class="post-checkbox" value="${post.id}">
                </td>
                <td>
                    <div>
                        <strong>${post.title || 'Untitled'}</strong>
                        <div class="text-muted small">${post.excerpt || 'No excerpt available'}</div>
                        <div class="text-muted small mt-1">
                            <i class="fas fa-eye me-1"></i>${post.view_count || 0} views
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge ${this.getStatusBadgeClass(post.status)}">
                        ${post.status || 'draft'}
                    </span>
                </td>
                <td>
                    <span class="text-muted">${post.category_name || 'Uncategorized'}</span>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="progress me-2" style="width: 60px; height: 8px;">
                            <div class="progress-bar ${this.getSEOProgressClass(post.seo_score || 0)}"
                                 style="width: ${post.seo_score || 0}%"></div>
                        </div>
                        <small class="text-muted">${post.seo_score || 0}%</small>
                    </div>
                </td>
                <td class="text-muted">
                    ${window.AdZetaApp.formatDate(post.updated_at)}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="AdZetaPosts.editPost(${post.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="AdZetaPosts.preview(${post.id})" title="Preview">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="AdZetaPosts.delete(${post.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    // Render pagination
    renderPagination() {
        // TODO: Implement pagination if needed
    },

    // Handle search input
    handleSearch(event) {
        this.data.filters.search = event.target.value;
        this.loadPosts().then(() => this.render());
    },

    // Handle status filter change
    handleStatusFilter(event) {
        this.data.filters.status = event.target.value;
        this.loadPosts().then(() => this.render());
    },

    // Handle category filter change
    handleCategoryFilter(event) {
        this.data.filters.category = event.target.value;
        this.loadPosts().then(() => this.render());
    },

    // Handle select all checkbox
    handleSelectAll(event) {
        const checkboxes = document.querySelectorAll('.post-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = event.target.checked;
        });
    },

    // Toggle bulk mode
    toggleBulkMode() {
        // TODO: Implement bulk operations
        window.AdZetaApp.showNotification('Bulk mode coming soon!', 'info');
    },

    // Edit post with proper navigation handling
    editPost(postId) {
        console.log(`Editing post ${postId} - updating navigation state to Add Post`);

        // Update URL first to reflect the edit action
        window.AdZetaNavigation.updateURLWithAction('posts', 'edit', postId);

        // Set "Add Post" as active when editing (since editing is similar to creating)
        window.AdZetaNavigation.updateActiveNavLink('add-post');

        // Open the post editor
        if (window.AdZetaPostEditor) {
            window.AdZetaPostEditor.edit(postId);
        } else {
            console.error('AdZetaPostEditor not available');
        }
    },

    // Preview post
    preview(postId) {
        window.open(`/blog/preview/${postId}`, '_blank');
    },

    // Delete post
    async delete(postId) {
        if (!confirm('Are you sure you want to delete this post?')) {
            return;
        }

        try {
            // Make real API call to delete from database
            const response = await window.AdZetaApp.apiRequest(`/posts/${postId}`, {
                method: 'DELETE'
            });

            if (response.success) {
                // Remove from local data only after successful API call
                this.data.posts = this.data.posts.filter(post => post.id !== postId);

                this.render();
                window.AdZetaApp.showNotification('Post deleted successfully', 'success');
            } else {
                throw new Error(response.message || 'Failed to delete post');
            }
        } catch (error) {
            console.error('Error deleting post:', error);
            window.AdZetaApp.showNotification('Failed to delete post: ' + error.message, 'danger');
        }
    },

    // Get status badge class
    getStatusBadgeClass(status) {
        const classes = {
            published: 'badge-success',
            draft: 'badge-warning',
            archived: 'badge-secondary'
        };
        return classes[status] || 'badge-secondary';
    },

    // Get SEO progress bar class
    getSEOProgressClass(score) {
        if (score >= 80) return 'bg-success';
        if (score >= 60) return 'bg-info';
        if (score >= 40) return 'bg-warning';
        return 'bg-danger';
    },

    // Show loading state
    showLoading() {
        const container = document.getElementById('postsView');
        if (container) {
            container.innerHTML = `
                <!-- Search and Filters -->
                <div class="search-filters mb-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" id="postsSearch" class="form-control"
                                       placeholder="Search posts by title, content, or author...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <select id="statusFilter" class="form-select">
                                <option value="">All Status</option>
                                <option value="published">Published</option>
                                <option value="draft">Draft</option>
                                <option value="archived">Archived</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="categoryFilter" class="form-select">
                                <option value="">All Categories</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button id="bulkModeBtn" class="btn btn-outline-secondary">
                                    <i class="fas fa-check-square me-1"></i>
                                    Bulk
                                </button>
                                <button id="refreshPostsBtn" class="btn btn-outline-primary">
                                    <i class="fas fa-sync me-1"></i>
                                    Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Posts Table -->
                <div class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAllPosts">
                                        </th>
                                        <th>Title</th>
                                        <th>Status</th>
                                        <th>Category</th>
                                        <th>SEO Score</th>
                                        <th>Updated</th>
                                        <th width="120">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="postsTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="spinner mb-3"></div>
                                            <p class="text-muted">Loading posts...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }
    },

    // Show error message
    showError(message) {
        const container = document.getElementById('postsView');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                    <button class="btn btn-outline-danger btn-sm ms-3" onclick="AdZetaPosts.load()">
                        <i class="fas fa-sync me-1"></i>
                        Retry
                    </button>
                </div>
            `;
        }
    },

    // Refresh posts
    async refresh() {
        await this.load();
        window.AdZetaApp.showNotification('Posts refreshed successfully', 'success');
    }
};
