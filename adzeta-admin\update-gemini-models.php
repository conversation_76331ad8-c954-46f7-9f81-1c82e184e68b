<?php
/**
 * Update Gemini Models for Existing Installations
 * Run this script to update to the latest Gemini models
 */

require_once __DIR__ . '/bootstrap.php';

try {
    $db = $GLOBALS['admin_db'];
    
    echo "🚀 Updating Gemini Model Configuration...\n\n";
    
    // Check current model setting
    $currentSetting = $db->fetch(
        "SELECT setting_value FROM settings WHERE setting_key = ?",
        ['ai_gemini_model']
    );
    
    if ($currentSetting) {
        echo "📋 Current model: {$currentSetting['setting_value']}\n";
        
        // Update to latest model if still using old default
        if ($currentSetting['setting_value'] === 'gemini-1.5-flash') {
            $db->execute(
                "UPDATE settings SET setting_value = ? WHERE setting_key = ?",
                ['gemini-2.5-flash', 'ai_gemini_model']
            );
            echo "✅ Updated default model from gemini-1.5-flash to gemini-2.5-flash\n";
        } else {
            echo "ℹ️  Model setting is already customized, keeping current selection\n";
        }
    } else {
        // Insert new setting if it doesn't exist
        $db->insert('settings', [
            'setting_key' => 'ai_gemini_model',
            'setting_value' => 'gemini-2.5-flash',
            'setting_type' => 'string',
            'description' => 'Selected Gemini model for AI content generation'
        ]);
        echo "✅ Added new Gemini model setting (default: gemini-2.5-flash)\n";
    }
    
    echo "\n🎯 Available Models:\n";
    echo "   🚀 gemini-2.5-flash (Latest & Fastest - Recommended)\n";
    echo "   🏆 gemini-2.5-pro (Most Advanced)\n";
    echo "   ✨ gemini-2.0-flash (Next-Gen)\n";
    echo "   ✅ gemini-1.5-flash (Proven & Reliable)\n";
    echo "   🔧 gemini-1.5-pro (Advanced)\n";
    echo "   📊 gemini-1.0-pro (Stable)\n";
    
    echo "\n💡 Recommendations:\n";
    echo "   • Gemini 2.5 Flash: Best overall choice for most content\n";
    echo "   • Gemini 2.5 Pro: For complex analysis and research\n";
    echo "   • Gemini 2.0 Flash: Great balance of speed and capabilities\n";
    
    echo "\n🎉 Update complete! Visit AI Settings to change model if needed.\n";
    echo "📍 URL: http://localhost/adzeta-admin/?view=ai-settings\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
