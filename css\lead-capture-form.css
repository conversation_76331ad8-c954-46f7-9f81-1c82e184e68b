/* Lead Capture Form Styles */

/* Form container styling */
.contact-form-style-03, .contact-form-style-05 {
    border-radius: 12px !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08) !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-form-style-03:hover, .contact-form-style-05:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12) !important;
}

/* Form fields styling */
.form-control {
    border-radius: 8px !important;
    padding: 12px 15px !important;
    transition: all 0.3s ease;
    font-size: 15px !important;
    height: auto !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    background-color: #fff !important;
}

.form-control:focus {
    border-color: #ff8cc6 !important;
    box-shadow: 0 0 0 3px rgba(255, 140, 198, 0.1) !important;
}

.form-control::placeholder {
    color: #999 !important;
    font-size: 14px;
    font-weight: 400;
}

/* Dropdown styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23ff8cc6' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: right 15px center !important;
    padding-right: 40px !important;
}

/* Icon box styling for benefits */
.icon-box-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.benefit-item:hover .icon-box-circle {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.text-gradient-base-color {
    background: linear-gradient(45deg, #f45888, #ff8cc6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

/* Select dropdown styling */
select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23999' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px !important;
}

/* Submit button styling */
.btn-gradient-base-color,
.btn-gradient-pink-orange {
    background: linear-gradient(45deg, #f45888, #ff8cc6) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
    padding: 15px 25px !important;
    transition: all 0.3s ease !important;
    border-radius: 50px !important;
}

.btn-gradient-base-color:hover,
.btn-gradient-pink-orange:hover {
    background: linear-gradient(45deg, #ff8cc6, #f45888) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 20px rgba(244, 88, 136, 0.2) !important;
}

.btn-rounded {
    border-radius: 50px !important;
}

/* Contact box styling */
.contact-box {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
}

.contact-avatar img {
    border: 3px solid rgba(255, 140, 198, 0.2);
    transition: transform 0.3s ease;
}

.contact-box:hover .contact-avatar img {
    transform: scale(1.05);
}

/* Required field indicator */
.form-control.required {
    border-left: 3px solid #ff8cc6 !important;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .contact-form-style-03, .contact-form-style-05 {
        padding: 25px !important;
    }

    .form-control {
        font-size: 14px !important;
    }

    .icon-box-circle {
        width: 40px;
        height: 40px;
    }
}

/* Animation for form submission */
@keyframes formSubmit {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

.btn-gradient-base-color:active {
    animation: formSubmit 0.3s ease;
}

/* Section background enhancement */
.bg-spring-wood {
    background: linear-gradient(135deg, #f8f6f3 0%, #f5f5f5 100%);
    position: relative;
    overflow: hidden;
}

.bg-spring-wood::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23f45888' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
}
