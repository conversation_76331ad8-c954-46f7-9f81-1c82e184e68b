/**
 * Performance Monitor for AdZeta Home Page
 * Tracks and reports performance metrics for optimization validation
 */

(function() {
    'use strict';

    const PerformanceMonitor = {
        metrics: {
            scriptLoadTimes: {},
            lazyLoadEvents: [],
            performanceMarks: {},
            errors: []
        },

        init: function() {
            this.trackPageLoad();
            this.trackScriptLoading();
            this.trackLazyLoading();
            this.trackErrors();
            this.setupReporting();
        },

        trackPageLoad: function() {
            // Track critical performance metrics
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const paint = performance.getEntriesByType('paint');
                    
                    const metrics = {
                        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
                        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
                        totalPageLoad: navigation.loadEventEnd - navigation.fetchStart
                    };

                    this.metrics.pageLoad = metrics;
                    this.logMetrics('Page Load Metrics', metrics);
                }, 100);
            });
        },

        trackScriptLoading: function() {
            // Monitor script loading performance
            const scripts = document.querySelectorAll('script[src]');
            
            scripts.forEach(script => {
                const src = script.src;
                const startTime = performance.now();
                
                script.addEventListener('load', () => {
                    const loadTime = performance.now() - startTime;
                    this.metrics.scriptLoadTimes[src] = loadTime;
                    console.log(`✅ Script loaded: ${src.split('/').pop()} (${loadTime.toFixed(2)}ms)`);
                });

                script.addEventListener('error', () => {
                    this.metrics.errors.push({
                        type: 'script_load_error',
                        src: src,
                        timestamp: Date.now()
                    });
                    console.error(`❌ Script failed to load: ${src}`);
                });
            });
        },

        trackLazyLoading: function() {
            // Track lazy loading events
            const originalConsoleLog = console.log;
            console.log = (...args) => {
                const message = args.join(' ');
                if (message.includes('Script loaded:') || message.includes('lazy')) {
                    this.metrics.lazyLoadEvents.push({
                        message: message,
                        timestamp: performance.now()
                    });
                }
                originalConsoleLog.apply(console, args);
            };
        },

        trackErrors: function() {
            // Track JavaScript errors
            window.addEventListener('error', (event) => {
                this.metrics.errors.push({
                    type: 'javascript_error',
                    message: event.message,
                    filename: event.filename,
                    line: event.lineno,
                    column: event.colno,
                    timestamp: Date.now()
                });
            });

            // Track unhandled promise rejections
            window.addEventListener('unhandledrejection', (event) => {
                this.metrics.errors.push({
                    type: 'promise_rejection',
                    reason: event.reason,
                    timestamp: Date.now()
                });
            });
        },

        setupReporting: function() {
            // Create performance report after page load
            window.addEventListener('load', () => {
                setTimeout(() => {
                    this.generateReport();
                }, 2000); // Wait for lazy loading to complete
            });

            // Add manual report trigger
            window.getPerformanceReport = () => this.generateReport();
        },

        generateReport: function() {
            const report = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                metrics: this.metrics,
                recommendations: this.getRecommendations()
            };

            console.group('🚀 AdZeta Performance Report');
            console.log('📊 Metrics:', this.metrics);
            console.log('💡 Recommendations:', report.recommendations);
            console.log('📋 Full Report:', report);
            console.groupEnd();

            return report;
        },

        getRecommendations: function() {
            const recommendations = [];
            
            // Check page load performance
            if (this.metrics.pageLoad?.totalPageLoad > 3000) {
                recommendations.push('Consider further optimizing page load time (currently > 3s)');
            }

            // Check script loading
            const slowScripts = Object.entries(this.metrics.scriptLoadTimes)
                .filter(([src, time]) => time > 500)
                .map(([src]) => src.split('/').pop());
            
            if (slowScripts.length > 0) {
                recommendations.push(`Slow loading scripts detected: ${slowScripts.join(', ')}`);
            }

            // Check errors
            if (this.metrics.errors.length > 0) {
                recommendations.push(`${this.metrics.errors.length} errors detected - check console for details`);
            }

            // Check lazy loading
            if (this.metrics.lazyLoadEvents.length === 0) {
                recommendations.push('No lazy loading events detected - verify implementation');
            }

            if (recommendations.length === 0) {
                recommendations.push('✅ Performance looks good! All optimizations working correctly.');
            }

            return recommendations;
        },

        logMetrics: function(title, data) {
            console.group(`📈 ${title}`);
            Object.entries(data).forEach(([key, value]) => {
                const formattedValue = typeof value === 'number' ? `${value.toFixed(2)}ms` : value;
                console.log(`${key}: ${formattedValue}`);
            });
            console.groupEnd();
        }
    };

    // Initialize performance monitoring
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => PerformanceMonitor.init());
    } else {
        PerformanceMonitor.init();
    }

    // Expose for debugging
    window.PerformanceMonitor = PerformanceMonitor;

})();
