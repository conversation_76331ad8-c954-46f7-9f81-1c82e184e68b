-- Migration: Add missing CTA fields to case_studies table
-- Date: 2025-06-30
-- Description: Add missing CTA description, button text, and button URL fields

-- Add cta_description column
ALTER TABLE case_studies
ADD COLUMN IF NOT EXISTS cta_description LONGTEXT DEFAULT NULL
AFTER cta_title;

-- Add cta_button_text column
ALTER TABLE case_studies
ADD COLUMN IF NOT EXISTS cta_button_text VARCHAR(100) DEFAULT NULL
AFTER cta_description;

-- Add cta_button_url column
ALTER TABLE case_studies
ADD COLUMN IF NOT EXISTS cta_button_url VARCHAR(500) DEFAULT NULL
AFTER cta_button_text;

-- Verify the columns were added
SELECT 'Migration completed. CTA fields added to case_studies table.' as status;
