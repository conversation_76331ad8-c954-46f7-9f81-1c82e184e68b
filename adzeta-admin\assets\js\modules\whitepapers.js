/**
 * Whitepapers Module
 * Manages whitepaper content with professional templates
 */

const AdZetaWhitepapers = {
    state: {
        whitepapers: [],
        currentWhitepaper: null,
        loading: false,
        filters: {
            status: 'all',
            category: 'all',
            search: ''
        }
    },

    // Initialize whitepapers module
    init() {
        console.log('Whitepapers module initialized');
        this.bindEvents();
        this.loadWhitepapers();
    },

    // Bind events
    bindEvents() {
        // Navigation will trigger this
        document.addEventListener('viewChanged', (e) => {
            if (e.detail.view === 'whitepapers') {
                this.render();
            }
        });
    },

    // Load whitepapers from API
    async loadWhitepapers() {
        this.state.loading = true;
        this.updateLoadingState();

        try {
            const response = await fetch('/adzeta-admin/api/whitepapers.php');
            const result = await response.json();

            if (result.success) {
                this.state.whitepapers = result.data;
                this.render();
            } else {
                throw new Error(result.message || 'Failed to load whitepapers');
            }
        } catch (error) {
            console.error('Failed to load whitepapers:', error);
            window.AdZetaApp.showNotification('Failed to load whitepapers', 'danger');
            
            // Show empty state with sample data
            this.state.whitepapers = this.getSampleWhitepapers();
            this.render();
        } finally {
            this.state.loading = false;
            this.updateLoadingState();
        }
    },

    // Get sample whitepapers for demo
    getSampleWhitepapers() {
        return [
            {
                id: 1,
                title: 'The Complete Guide to Predictive Customer Lifetime Value',
                description: 'Comprehensive guide to implementing predictive LTV strategies for maximum ROI',
                category: 'Strategy',
                pages: 24,
                downloads: 1250,
                status: 'published',
                featured_image: '/assets/images/whitepapers/predictive-ltv-guide.jpg',
                created_at: '2024-01-20',
                url_slug: '/whitepapers/predictive-customer-lifetime-value-guide',
                file_url: '/assets/downloads/predictive-ltv-guide.pdf'
            },
            {
                id: 2,
                title: 'AI-Powered Marketing Attribution: Best Practices 2024',
                description: 'Latest trends and best practices in AI-powered marketing attribution',
                category: 'Technology',
                pages: 18,
                downloads: 890,
                status: 'published',
                featured_image: '/assets/images/whitepapers/ai-attribution-guide.jpg',
                created_at: '2024-01-15',
                url_slug: '/whitepapers/ai-powered-marketing-attribution-2024',
                file_url: '/assets/downloads/ai-attribution-guide.pdf'
            },
            {
                id: 3,
                title: 'Cross-Channel Campaign Optimization Framework',
                description: 'Framework for optimizing campaigns across multiple channels',
                category: 'Framework',
                pages: 32,
                downloads: 0,
                status: 'draft',
                featured_image: '/assets/images/whitepapers/cross-channel-optimization.jpg',
                created_at: '2024-01-10',
                url_slug: '/whitepapers/cross-channel-campaign-optimization',
                file_url: '/assets/downloads/cross-channel-optimization.pdf'
            }
        ];
    },

    // Render whitepapers view
    render() {
        const container = document.getElementById('whitepapersView');
        if (!container) return;

        // Update page header
        this.updatePageHeader();

        // Render content
        container.innerHTML = `
            <div class="whitepapers-container">
                <!-- Filters and Actions -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="whitepapers-filters">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="Search whitepapers..." 
                                       value="${this.state.filters.search}" onchange="AdZetaWhitepapers.handleSearchChange(this)">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" onchange="AdZetaWhitepapers.handleStatusFilter(this)">
                                    <option value="all">All Status</option>
                                    <option value="published" ${this.state.filters.status === 'published' ? 'selected' : ''}>Published</option>
                                    <option value="draft" ${this.state.filters.status === 'draft' ? 'selected' : ''}>Draft</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" onchange="AdZetaWhitepapers.handleCategoryFilter(this)">
                                    <option value="all">All Categories</option>
                                    <option value="strategy" ${this.state.filters.category === 'strategy' ? 'selected' : ''}>Strategy</option>
                                    <option value="technology" ${this.state.filters.category === 'technology' ? 'selected' : ''}>Technology</option>
                                    <option value="framework" ${this.state.filters.category === 'framework' ? 'selected' : ''}>Framework</option>
                                    <option value="research" ${this.state.filters.category === 'research' ? 'selected' : ''}>Research</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="whitepapers-actions">
                        <button class="btn btn-primary" onclick="AdZetaWhitepapers.createWhitepaper()">
                            <i class="fas fa-plus me-2"></i>
                            New Whitepaper
                        </button>
                    </div>
                </div>

                <!-- Whitepapers Grid -->
                <div class="whitepapers-grid">
                    ${this.renderWhitepapersGrid()}
                </div>
            </div>
        `;
    },

    // Render whitepapers grid
    renderWhitepapersGrid() {
        const filteredWhitepapers = this.getFilteredWhitepapers();

        if (filteredWhitepapers.length === 0) {
            return `
                <div class="empty-state text-center py-5">
                    <i class="fas fa-file-pdf fa-4x text-muted mb-4"></i>
                    <h4>No Whitepapers Found</h4>
                    <p class="text-muted mb-4">Create your first whitepaper to share valuable insights and research.</p>
                    <button class="btn btn-primary" onclick="AdZetaWhitepapers.createWhitepaper()">
                        <i class="fas fa-plus me-2"></i>
                        Create Whitepaper
                    </button>
                </div>
            `;
        }

        return `
            <div class="row">
                ${filteredWhitepapers.map(whitepaper => `
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card whitepaper-card h-100">
                            <div class="whitepaper-image">
                                <img src="${whitepaper.featured_image || '/assets/images/placeholder-whitepaper.jpg'}" 
                                     class="card-img-top" alt="${whitepaper.title}">
                                <div class="whitepaper-status">
                                    <span class="badge ${whitepaper.status === 'published' ? 'badge-success' : 'badge-warning'}">
                                        ${whitepaper.status}
                                    </span>
                                </div>
                                <div class="whitepaper-category">
                                    <span class="badge badge-primary">${whitepaper.category}</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="whitepaper-meta mb-2">
                                    <small class="text-muted">${this.formatDate(whitepaper.created_at)}</small>
                                    <span class="ms-2">
                                        <i class="fas fa-file-alt me-1"></i>
                                        ${whitepaper.pages} pages
                                    </span>
                                </div>
                                <h5 class="card-title">${whitepaper.title}</h5>
                                <p class="text-muted mb-3">${whitepaper.description}</p>
                                <div class="whitepaper-stats mb-3">
                                    <div class="d-flex justify-content-between text-center">
                                        <div class="stat-item">
                                            <div class="stat-value text-primary fw-bold">${whitepaper.downloads}</div>
                                            <div class="stat-label small text-muted">Downloads</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value text-success fw-bold">${whitepaper.pages}</div>
                                            <div class="stat-label small text-muted">Pages</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-value text-info fw-bold">${whitepaper.status === 'published' ? 'Live' : 'Draft'}</div>
                                            <div class="stat-label small text-muted">Status</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm btn-outline-primary" onclick="AdZetaWhitepapers.editWhitepaper(${whitepaper.id})">
                                        <i class="fas fa-edit me-1"></i>
                                        Edit
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="AdZetaWhitepapers.previewWhitepaper(${whitepaper.id})">
                                        <i class="fas fa-eye me-1"></i>
                                        Preview
                                    </button>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="AdZetaWhitepapers.downloadWhitepaper(${whitepaper.id})">
                                                <i class="fas fa-download me-2"></i>Download PDF
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="AdZetaWhitepapers.duplicateWhitepaper(${whitepaper.id})">
                                                <i class="fas fa-copy me-2"></i>Duplicate
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="AdZetaWhitepapers.viewAnalytics(${whitepaper.id})">
                                                <i class="fas fa-chart-bar me-2"></i>Analytics
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="AdZetaWhitepapers.deleteWhitepaper(${whitepaper.id})">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    // Get filtered whitepapers
    getFilteredWhitepapers() {
        return this.state.whitepapers.filter(whitepaper => {
            const matchesSearch = !this.state.filters.search || 
                whitepaper.title.toLowerCase().includes(this.state.filters.search.toLowerCase()) ||
                whitepaper.description.toLowerCase().includes(this.state.filters.search.toLowerCase());
            
            const matchesStatus = this.state.filters.status === 'all' || 
                whitepaper.status === this.state.filters.status;
            
            const matchesCategory = this.state.filters.category === 'all' || 
                whitepaper.category.toLowerCase() === this.state.filters.category;

            return matchesSearch && matchesStatus && matchesCategory;
        });
    },

    // Update page header
    updatePageHeader() {
        const pageTitle = document.getElementById('pageTitle');
        const pageSubtitle = document.getElementById('pageSubtitle');
        
        if (pageTitle) {
            pageTitle.innerHTML = '<i class="fas fa-file-pdf me-2"></i>Whitepapers';
        }
        
        if (pageSubtitle) {
            pageSubtitle.textContent = 'Create and manage professional whitepapers and research documents';
        }
    },

    // Update loading state
    updateLoadingState() {
        // Implementation for loading states
    },

    // Format date
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    // Handle search change
    handleSearchChange(input) {
        this.state.filters.search = input.value;
        this.render();
    },

    // Handle status filter
    handleStatusFilter(select) {
        this.state.filters.status = select.value;
        this.render();
    },

    // Handle category filter
    handleCategoryFilter(select) {
        this.state.filters.category = select.value;
        this.render();
    },

    // Create new whitepaper
    createWhitepaper() {
        // Navigate to whitepaper editor
        window.AdZetaApp.showNotification('Whitepaper editor will open with professional template', 'info');
        console.log('Creating new whitepaper');
    },

    // Edit whitepaper
    editWhitepaper(id) {
        const whitepaper = this.state.whitepapers.find(wp => wp.id === id);
        if (whitepaper) {
            console.log('Editing whitepaper:', whitepaper.title);
            window.AdZetaApp.showNotification(`Editing: ${whitepaper.title}`, 'info');
        }
    },

    // Preview whitepaper
    previewWhitepaper(id) {
        const whitepaper = this.state.whitepapers.find(wp => wp.id === id);
        if (whitepaper) {
            window.open(whitepaper.url_slug, '_blank');
        }
    },

    // Download whitepaper
    downloadWhitepaper(id) {
        const whitepaper = this.state.whitepapers.find(wp => wp.id === id);
        if (whitepaper) {
            window.open(whitepaper.file_url, '_blank');
        }
    },

    // Duplicate whitepaper
    duplicateWhitepaper(id) {
        const whitepaper = this.state.whitepapers.find(wp => wp.id === id);
        if (whitepaper) {
            console.log('Duplicating whitepaper:', whitepaper.title);
            window.AdZetaApp.showNotification(`Duplicating: ${whitepaper.title}`, 'info');
        }
    },

    // View analytics
    viewAnalytics(id) {
        const whitepaper = this.state.whitepapers.find(wp => wp.id === id);
        if (whitepaper) {
            console.log('Viewing analytics for:', whitepaper.title);
            window.AdZetaApp.showNotification(`Analytics for: ${whitepaper.title}`, 'info');
        }
    },

    // Delete whitepaper
    deleteWhitepaper(id) {
        const whitepaper = this.state.whitepapers.find(wp => wp.id === id);
        if (whitepaper && confirm(`Are you sure you want to delete "${whitepaper.title}"?`)) {
            console.log('Deleting whitepaper:', whitepaper.title);
            window.AdZetaApp.showNotification(`Deleted: ${whitepaper.title}`, 'success');
            
            // Remove from state
            this.state.whitepapers = this.state.whitepapers.filter(wp => wp.id !== id);
            this.render();
        }
    }
};
