/**
 * Modern Toggle Slider
 * Handles the comparison section toggle functionality
 */

(function() {
    'use strict';

    const ModernToggleSlider = {
        init: function() {
            console.log('DOM loaded, initializing toggle slider');

            // Get toggle options
            const toggleOptions = document.querySelectorAll('.modern-toggle-slider .toggle-option');
            console.log('Found toggle options:', toggleOptions.length);

            if (toggleOptions.length === 0) {
                console.warn('No toggle options found');
                return;
            }

            this.setupSlider(toggleOptions);
        },

        setupSlider: function(toggleOptions) {
            // Function to position the slider indicator
            const positionSliderIndicator = (index) => {
                const sliderIndicator = document.querySelector('.slider-indicator');
                if (!sliderIndicator) {
                    console.error('Slider indicator not found');
                    return;
                }

                // Position the indicator based on the index (0 for first tab, 1 for second tab)
                if (index === 0) {
                    // First tab - position at left
                    sliderIndicator.style.left = '3px';
                    sliderIndicator.style.right = 'auto';
                    console.log('Moving indicator to first position');
                } else {
                    // Second tab - position at right
                    sliderIndicator.style.left = 'auto';
                    sliderIndicator.style.right = '3px';
                    console.log('Moving indicator to second position');
                }
            };

            // Initialize the indicator position based on the active tab
            let activeIndex = 0;
            toggleOptions.forEach((option, index) => {
                if (option.classList.contains('active')) {
                    activeIndex = index;
                }
            });

            // Delay the initial positioning slightly to ensure all elements are fully rendered
            setTimeout(() => {
                positionSliderIndicator(activeIndex);
                console.log('Initial positioning of slider indicator to index:', activeIndex);
            }, 100);

            // Add click event listeners
            toggleOptions.forEach((option, index) => {
                option.addEventListener('click', function() {
                    // Skip if already active
                    if (this.classList.contains('active')) return;

                    console.log('Toggle option clicked:', this.getAttribute('data-target'));

                    // Remove active class from all options and tab panes
                    document.querySelectorAll('.modern-toggle-slider .toggle-option').forEach(opt => {
                        opt.classList.remove('active');
                    });

                    document.querySelectorAll('.tab-pane').forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });

                    // Add active class to clicked option
                    this.classList.add('active');

                    // Position the slider indicator
                    positionSliderIndicator(index);

                    // Show the corresponding tab pane with a slight delay for the slider animation
                    const target = this.getAttribute('data-target');
                    const targetPane = document.querySelector(target);

                    if (targetPane) {
                        // Small delay to allow the slider animation to complete
                        setTimeout(() => {
                            targetPane.classList.add('show', 'active');
                            console.log('Activated tab pane:', target);
                        }, 100);
                    } else {
                        console.error('Tab pane not found:', target);
                    }
                });
            });
        },

        // Method to programmatically switch tabs
        switchTab: function(index) {
            const toggleOptions = document.querySelectorAll('.modern-toggle-slider .toggle-option');
            if (toggleOptions[index]) {
                toggleOptions[index].click();
            }
        },

        // Method to get current active tab
        getActiveTab: function() {
            const activeOption = document.querySelector('.modern-toggle-slider .toggle-option.active');
            if (activeOption) {
                return Array.from(document.querySelectorAll('.modern-toggle-slider .toggle-option')).indexOf(activeOption);
            }
            return -1;
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => ModernToggleSlider.init());
    } else {
        ModernToggleSlider.init();
    }

    // Expose for external use
    window.ModernToggleSlider = ModernToggleSlider;

})();
