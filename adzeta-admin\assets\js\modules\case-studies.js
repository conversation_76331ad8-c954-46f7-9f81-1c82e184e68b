/**
 * Case Studies Module
 * Manages case study content with fixed template design
 */

const AdZetaCaseStudies = {
    state: {
        caseStudies: [],
        currentCaseStudy: null,
        loading: false,
        filters: {
            status: 'all',
            industry: 'all',
            search: ''
        }
    },

    // Initialize case studies module
    init() {
        console.log('Case Studies module initialized');
        this.bindEvents();
        this.loadCaseStudies();
    },

    // Bind events
    bindEvents() {
        // Navigation will trigger this
        document.addEventListener('viewChanged', (e) => {
            if (e.detail.view === 'case-studies') {
                this.render();
            }
        });
    },

    // Load case studies from API
    async loadCaseStudies() {
        this.state.loading = true;
        this.updateLoadingState();

        try {
            // Build query parameters
            const params = new URLSearchParams();
            if (this.state.filters.status !== 'all') {
                params.append('status', this.state.filters.status);
            }
            if (this.state.filters.industry !== 'all') {
                params.append('industry', this.state.filters.industry);
            }
            if (this.state.filters.search) {
                params.append('search', this.state.filters.search);
            }

            const url = `/adzeta-admin/api/case-studies${params.toString() ? '?' + params.toString() : ''}`;
            const response = await window.AdZetaApp.apiRequest(url);

            if (response.success) {
                this.state.caseStudies = response.case_studies || [];
                this.render();
            } else {
                throw new Error(response.message || 'Failed to load case studies');
            }
        } catch (error) {
            console.error('Failed to load case studies:', error);
            window.AdZetaApp.showNotification('Failed to load case studies: ' + error.message, 'danger');

            // Show empty state
            this.state.caseStudies = [];
            this.render();
        } finally {
            this.state.loading = false;
            this.updateLoadingState();
        }
    },

    // Load case study statistics
    async loadStats() {
        try {
            const response = await window.AdZetaApp.apiRequest('/case-studies/stats');
            if (response.success) {
                return response;
            }
        } catch (error) {
            console.error('Failed to load case study stats:', error);
        }
        return { total: 0, published: 0, draft: 0, archived: 0, by_industry: {} };
    },

    // Render case studies view
    render() {
        const container = document.getElementById('case-studiesView');
        if (!container) return;

        // Update page header
        this.updatePageHeader();

        // Render content
        container.innerHTML = `
            <div class="case-studies-container">
                <!-- Filters and Actions -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="case-studies-filters">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="Search case studies..." 
                                       value="${this.state.filters.search}" onchange="AdZetaCaseStudies.handleSearchChange(this)">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" onchange="AdZetaCaseStudies.handleStatusFilter(this)">
                                    <option value="all">All Status</option>
                                    <option value="published" ${this.state.filters.status === 'published' ? 'selected' : ''}>Published</option>
                                    <option value="draft" ${this.state.filters.status === 'draft' ? 'selected' : ''}>Draft</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" onchange="AdZetaCaseStudies.handleIndustryFilter(this)">
                                    <option value="all">All Industries</option>
                                    <option value="healthcare" ${this.state.filters.industry === 'healthcare' ? 'selected' : ''}>Healthcare</option>
                                    <option value="technology" ${this.state.filters.industry === 'technology' ? 'selected' : ''}>Technology</option>
                                    <option value="ecommerce" ${this.state.filters.industry === 'ecommerce' ? 'selected' : ''}>E-commerce</option>
                                    <option value="finance" ${this.state.filters.industry === 'finance' ? 'selected' : ''}>Finance</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="case-studies-actions">
                        <button class="btn btn-primary" onclick="AdZetaCaseStudies.createCaseStudy()">
                            <i class="fas fa-plus me-2"></i>
                            New Case Study
                        </button>
                    </div>
                </div>

                <!-- Case Studies Grid -->
                <div class="case-studies-grid">
                    ${this.renderCaseStudiesGrid()}
                </div>
            </div>
        `;
    },

    // Render case studies grid
    renderCaseStudiesGrid() {
        const filteredCaseStudies = this.getFilteredCaseStudies();

        if (filteredCaseStudies.length === 0) {
            return `
                <div class="empty-state text-center py-5">
                    <i class="fas fa-chart-line fa-4x text-muted mb-4"></i>
                    <h4>No Case Studies Found</h4>
                    <p class="text-muted mb-4">Create your first case study to showcase client success stories.</p>
                    <button class="btn btn-primary" onclick="AdZetaCaseStudies.createCaseStudy()">
                        <i class="fas fa-plus me-2"></i>
                        Create Case Study
                    </button>
                </div>
            `;
        }

        return `
            <div class="row">
                ${filteredCaseStudies.map(caseStudy => `
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card case-study-card h-100">
                            <div class="case-study-image">
                                <img src="${caseStudy.featured_image || '/assets/images/placeholder-case-study.jpg'}" 
                                     class="card-img-top" alt="${caseStudy.title}">
                                <div class="case-study-status">
                                    <span class="badge ${caseStudy.status === 'published' ? 'badge-success' : 'badge-warning'}">
                                        ${caseStudy.status}
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="case-study-meta mb-2">
                                    <span class="badge badge-outline-primary">${caseStudy.industry}</span>
                                    <small class="text-muted ms-2">${this.formatDate(caseStudy.created_at)}</small>
                                </div>
                                <h5 class="card-title">${caseStudy.title}</h5>
                                <p class="text-muted mb-3">${caseStudy.client}</p>
                                <div class="case-study-results mb-3">
                                    <div class="row text-center">
                                        ${Object.entries(caseStudy.results).slice(0, 2).map(([key, value]) => `
                                            <div class="col-6">
                                                <div class="result-metric">
                                                    <div class="metric-value text-primary fw-bold">${value}</div>
                                                    <div class="metric-label small text-muted">${this.formatMetricLabel(key)}</div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-sm btn-outline-primary" onclick="AdZetaCaseStudies.editCaseStudy(${caseStudy.id})">
                                        <i class="fas fa-edit me-1"></i>
                                        Edit
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="AdZetaCaseStudies.previewCaseStudy(${caseStudy.id})">
                                        <i class="fas fa-eye me-1"></i>
                                        Preview
                                    </button>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="AdZetaCaseStudies.duplicateCaseStudy(${caseStudy.id})">
                                                <i class="fas fa-copy me-2"></i>Duplicate
                                            </a></li>
                                            <li><a class="dropdown-item" href="#" onclick="AdZetaCaseStudies.exportCaseStudy(${caseStudy.id})">
                                                <i class="fas fa-download me-2"></i>Export
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="AdZetaCaseStudies.deleteCaseStudy(${caseStudy.id})">
                                                <i class="fas fa-trash me-2"></i>Delete
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    // Get filtered case studies
    getFilteredCaseStudies() {
        return this.state.caseStudies.filter(caseStudy => {
            const matchesSearch = !this.state.filters.search || 
                caseStudy.title.toLowerCase().includes(this.state.filters.search.toLowerCase()) ||
                caseStudy.client.toLowerCase().includes(this.state.filters.search.toLowerCase());
            
            const matchesStatus = this.state.filters.status === 'all' || 
                caseStudy.status === this.state.filters.status;
            
            const matchesIndustry = this.state.filters.industry === 'all' || 
                caseStudy.industry.toLowerCase() === this.state.filters.industry;

            return matchesSearch && matchesStatus && matchesIndustry;
        });
    },

    // Update page header
    updatePageHeader() {
        const pageTitle = document.getElementById('pageTitle');
        const pageSubtitle = document.getElementById('pageSubtitle');
        
        if (pageTitle) {
            pageTitle.innerHTML = '<i class="fas fa-chart-line me-2"></i>Case Studies';
        }
        
        if (pageSubtitle) {
            pageSubtitle.textContent = 'Showcase client success stories and results';
        }
    },

    // Update loading state
    updateLoadingState() {
        // Implementation for loading states
    },

    // Format date
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    // Format metric label
    formatMetricLabel(key) {
        return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    },

    // Handle search change
    handleSearchChange(input) {
        this.state.filters.search = input.value;
        this.render();
    },

    // Handle status filter
    handleStatusFilter(select) {
        this.state.filters.status = select.value;
        this.render();
    },

    // Handle industry filter
    handleIndustryFilter(select) {
        this.state.filters.industry = select.value;
        this.render();
    },

    // Create new case study
    createCaseStudy() {
        console.log('Creating new case study');

        // Update URL and navigation
        window.AdZetaNavigation.updateURLWithAction('case-studies', 'new');
        window.AdZetaNavigation.updateActiveNavLink('case-studies');

        // Open the case study editor
        if (window.AdZetaCaseStudyEditor) {
            window.AdZetaCaseStudyEditor.createNew();
        } else {
            console.error('AdZetaCaseStudyEditor not available');
            window.AdZetaApp.showNotification('Case study editor not loaded', 'danger');
        }
    },

    // Edit case study
    editCaseStudy(id) {
        const caseStudy = this.state.caseStudies.find(cs => cs.id === id);
        if (caseStudy) {
            console.log('Editing case study:', caseStudy.title);

            // Update URL and navigation
            window.AdZetaNavigation.updateURLWithAction('case-studies', 'edit', id);
            window.AdZetaNavigation.updateActiveNavLink('case-studies');

            // Open the case study editor
            if (window.AdZetaCaseStudyEditor) {
                window.AdZetaCaseStudyEditor.edit(id);
            } else {
                console.error('AdZetaCaseStudyEditor not available');
                window.AdZetaApp.showNotification('Case study editor not loaded', 'danger');
            }
        } else {
            window.AdZetaApp.showNotification('Case study not found', 'danger');
        }
    },

    // Preview case study
    previewCaseStudy(id) {
        const caseStudy = this.state.caseStudies.find(cs => cs.id === id);
        if (caseStudy) {
            window.open(caseStudy.url_slug, '_blank');
        }
    },

    // Duplicate case study
    duplicateCaseStudy(id) {
        const caseStudy = this.state.caseStudies.find(cs => cs.id === id);
        if (caseStudy) {
            console.log('Duplicating case study:', caseStudy.title);
            window.AdZetaApp.showNotification(`Duplicating: ${caseStudy.title}`, 'info');
        }
    },

    // Export case study
    exportCaseStudy(id) {
        const caseStudy = this.state.caseStudies.find(cs => cs.id === id);
        if (caseStudy) {
            console.log('Exporting case study:', caseStudy.title);
            window.AdZetaApp.showNotification(`Exporting: ${caseStudy.title}`, 'info');
        }
    },

    // Delete case study
    async deleteCaseStudy(id) {
        const caseStudy = this.state.caseStudies.find(cs => cs.id === id);
        if (!caseStudy) {
            window.AdZetaApp.showNotification('Case study not found', 'danger');
            return;
        }

        if (!confirm(`Are you sure you want to delete "${caseStudy.title}"? This action cannot be undone.`)) {
            return;
        }

        try {
            const response = await window.AdZetaApp.apiRequest(`/case-studies/${id}`, {
                method: 'DELETE'
            });

            if (response.success) {
                window.AdZetaApp.showNotification(`Deleted: ${caseStudy.title}`, 'success');

                // Remove from state and re-render
                this.state.caseStudies = this.state.caseStudies.filter(cs => cs.id !== id);
                this.render();
            } else {
                throw new Error(response.message || 'Failed to delete case study');
            }
        } catch (error) {
            console.error('Delete failed:', error);
            window.AdZetaApp.showNotification('Delete failed: ' + error.message, 'danger');
        }
    }
};
