/* Apple-inspired Case Study Card Component */
.case-study-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.03);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.case-study-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.04);
}

.case-study-card:hover .cta-link {
    color: #f45888;
}

.case-study-card:hover .cta-link::after {
    transform: translateX(4px);
}

/* Card image container */
.card-image-container {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.case-study-card:hover .card-image {
    transform: scale(1.03);
}

/* Category tag */
.category-tag {
    position: absolute;
    top: 16px;
    left: 16px;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0.5px;
    color: #333;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    text-transform: uppercase;
}

/* Card content */
.card-content {
    padding: 24px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

/* Card title */
.card-title {
    font-size: 20px;
    font-weight: 600;
    color: #111;
    margin-bottom: 16px;
    line-height: 1.3;
}

/* Metric highlight */
.metric-highlight {
    margin-bottom: 20px;
}

.metric-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #666;
    margin-bottom: 4px;
    letter-spacing: 0.5px;
}

.metric-value {
    font-size: 32px;
    font-weight: 700;
    line-height: 1.1;
    background: linear-gradient(135deg, #ff6a8d 0%, #f45888 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.metric-value.reduction {
    background: linear-gradient(135deg, #6a8caf 0%, #4a7ab5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Description */
.description {
    font-size: 15px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 24px;
    flex-grow: 1;
}

/* Client testimonial */
.client-testimonial {
    display: flex;
    align-items: center;
    margin-top: auto;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.client-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 12px;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.client-info {
    display: flex;
    flex-direction: column;
}

.client-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.client-title {
    font-size: 13px;
    color: #666;
    line-height: 1.3;
}

/* CTA link */
.cta-link {
    display: inline-flex;
    align-items: center;
    margin-top: 20px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.cta-link::after {
    content: "→";
    margin-left: 6px;
    transition: transform 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .card-image-container {
        height: 180px;
    }
    
    .card-content {
        padding: 20px;
    }
    
    .card-title {
        font-size: 18px;
    }
    
    .metric-value {
        font-size: 28px;
    }
    
    .description {
        font-size: 14px;
    }
}
