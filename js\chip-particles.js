/**
 * Simple Particles Animation
 * Creates animated particles flowing from the center of the image
 */

document.addEventListener('DOMContentLoaded', function() {
    // Find the particles container
    const particlesContainer = document.querySelector('.particles-container');
    if (!particlesContainer) return;

    // Function to create and animate a particle
    const createParticle = () => {
        // Create particle element
        const particle = document.createElement('div');
        particle.className = 'particle';

        // Random angle (0-360 degrees)
        const angle = Math.random() * 360;
        const radians = angle * (Math.PI / 180);

        // Find the center of the image (approximately where the chip would be)
        const centerX = particlesContainer.offsetWidth * 0.5;
        const centerY = particlesContainer.offsetHeight * 0.45; // Slightly above center

        // Calculate end position based on angle
        const distance = Math.min(particlesContainer.offsetWidth, particlesContainer.offsetHeight) * (0.3 + Math.random() * 0.4); // 30-70% of container
        const endX = centerX + Math.cos(radians) * distance;
        const endY = centerY + Math.sin(radians) * distance;

        // Set initial position
        particle.style.left = `${centerX}px`;
        particle.style.top = `${centerY}px`;

        // Random color variation with brand colors
        const colors = ['#ff6a8d', '#e958a1', '#d15ec7', '#8f76f5', '#ff6a8d', '#e958a1']; // More weight to primary colors
        const randomColor = colors[Math.floor(Math.random() * colors.length)];
        particle.style.backgroundColor = randomColor;

        // Random size variation
        const size = 2 + Math.random() * 4; // 2-6px
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        // Random speed variation
        const duration = 0.8 + Math.random() * 1.2; // 0.8-2s (faster)
        particle.style.animationDuration = `${duration}s`;

        // Add to container
        particlesContainer.appendChild(particle);

        // Create intermediate points for a more natural path
        const keyframes = [
            { left: `${centerX}px`, top: `${centerY}px`, opacity: 0, transform: 'scale(0)' },
            { opacity: 0.8, transform: 'scale(1)', offset: 0.1 },
        ];

        // Add some curve to the path for more organic movement
        const numPoints = Math.floor(2 + Math.random() * 2); // 2-3 intermediate points
        for (let i = 1; i <= numPoints; i++) {
            const pointDistance = (distance * i) / (numPoints + 1);

            // Add slight curve to the path
            const perpDistance = Math.random() * 15 - 7.5; // -7.5 to 7.5px perpendicular offset
            const perpX = -Math.sin(radians) * perpDistance;
            const perpY = Math.cos(radians) * perpDistance;

            const curvedX = centerX + Math.cos(radians) * pointDistance + perpX;
            const curvedY = centerY + Math.sin(radians) * pointDistance + perpY;

            keyframes.push({
                left: `${curvedX}px`,
                top: `${curvedY}px`,
                opacity: 0.7,
                transform: 'scale(1)',
                offset: 0.1 + (0.7 * i / (numPoints + 1))
            });
        }

        // Add final keyframes
        keyframes.push(
            { left: `${endX}px`, top: `${endY}px`, opacity: 0.6, transform: 'scale(1)', offset: 0.8 },
            { left: `${endX}px`, top: `${endY}px`, opacity: 0, transform: 'scale(0.5)' }
        );

        const animation = particle.animate(keyframes, {
            duration: duration * 1000,
            easing: 'ease-out',
            fill: 'forwards'
        });

        // Remove particle after animation completes
        animation.onfinish = () => {
            particle.remove();
        };
    };

    // Create particles at regular intervals
    const startParticleAnimation = () => {
        // Create initial batch of particles
        for (let i = 0; i < 12; i++) {
            setTimeout(() => {
                createParticle();
            }, i * 100);
        }

        // Continue creating particles at intervals with varying frequency
        let particleInterval;
        const createParticlesWithVaryingFrequency = () => {
            // Create 1-3 particles at once for a more dynamic effect
            const particleCount = 1 + Math.floor(Math.random() * 3);
            for (let i = 0; i < particleCount; i++) {
                setTimeout(() => {
                    createParticle();
                }, i * 50);
            }

            // Vary the interval between particle bursts (150-300ms)
            const nextInterval = 150 + Math.floor(Math.random() * 150);
            particleInterval = setTimeout(createParticlesWithVaryingFrequency, nextInterval);
        };

        createParticlesWithVaryingFrequency();

        // Add event listener to create more particles on hover
        const chipImageContainer = document.querySelector('.chip-image-container');
        if (chipImageContainer) {
            chipImageContainer.addEventListener('mouseenter', () => {
                // Create a burst of particles on hover
                for (let i = 0; i < 15; i++) {
                    setTimeout(() => {
                        createParticle();
                    }, i * 50);
                }
            });
        }
    };

    // Start animation when element is in viewport
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                startParticleAnimation();
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.2 });

    observer.observe(particlesContainer);
});
