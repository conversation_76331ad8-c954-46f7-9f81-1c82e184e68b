<?php

namespace AdZetaAdmin\API;

use AdZetaAdmin\Models\CaseStudy;

/**
 * Case Studies API Controller
 * Handles all case study related API endpoints
 */
class CaseStudiesController extends BaseController
{
    private $caseStudy;

    public function __construct()
    {
        parent::__construct();
        $this->caseStudy = new CaseStudy($this->db);
    }

    /**
     * Get all case studies
     * GET /api/case-studies
     */
    public function index()
    {
        try {
            $filters = [];
            
            // Get query parameters
            if (!empty($_GET['status'])) {
                $filters['status'] = $_GET['status'];
            }
            
            if (!empty($_GET['industry'])) {
                $filters['industry'] = $_GET['industry'];
            }
            
            if (!empty($_GET['search'])) {
                $filters['search'] = $_GET['search'];
            }
            
            if (!empty($_GET['limit'])) {
                $filters['limit'] = (int)$_GET['limit'];
            }

            $caseStudies = $this->caseStudy->getAll($filters);

            // Process results_data JSON for each case study
            foreach ($caseStudies as &$study) {
                if (!empty($study['results_data'])) {
                    $study['results_data'] = json_decode($study['results_data'], true);
                }
            }

            return $this->success([
                'case_studies' => $caseStudies,
                'total' => count($caseStudies)
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to fetch case studies: ' . $e->getMessage());
        }
    }

    /**
     * Get single case study
     * GET /api/case-studies/{id}
     */
    public function show($id)
    {
        try {
            $caseStudy = $this->caseStudy->getById($id);

            if (!$caseStudy) {
                return $this->error('Case study not found', 404);
            }

            // Process results_data JSON
            if (!empty($caseStudy['results_data'])) {
                $caseStudy['results_data'] = json_decode($caseStudy['results_data'], true);
            }

            return $this->success([
                'case_study' => $caseStudy
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to fetch case study: ' . $e->getMessage());
        }
    }

    /**
     * Create new case study
     * POST /api/case-studies
     */
    public function store()
    {
        $payload = $this->requirePermission('create_posts'); // Using same permission as posts
        $data = $this->getRequestData();

        $this->validateRequired($data, ['title', 'client_name']);

        try {
            // Sanitize data while preserving HTML in content fields
            $data = $this->sanitizeCaseStudyData($data);
            $data['author_id'] = $payload['user_id'];
            
            $caseStudyId = $this->caseStudy->create($data);
            $caseStudy = $this->caseStudy->getById($caseStudyId);

            // Process results_data JSON
            if (!empty($caseStudy['results_data'])) {
                $caseStudy['results_data'] = json_decode($caseStudy['results_data'], true);
            }

            return $this->success([
                'message' => 'Case study created successfully',
                'case_study' => $caseStudy
            ], 201);

        } catch (\Exception $e) {
            return $this->error('Failed to create case study: ' . $e->getMessage());
        }
    }

    /**
     * Update case study
     * PUT /api/case-studies/{id}
     */
    public function update($id)
    {
        $payload = $this->requirePermission('manage_posts'); // Using same permission as posts
        $data = $this->getRequestData();

        try {
            $existingCaseStudy = $this->caseStudy->getById($id);
            if (!$existingCaseStudy) {
                return $this->error('Case study not found', 404);
            }

            // Sanitize data while preserving HTML in content fields
            $data = $this->sanitizeCaseStudyData($data);
            
            $success = $this->caseStudy->update($id, $data);
            
            if ($success) {
                $caseStudy = $this->caseStudy->getById($id);
                
                // Process results_data JSON
                if (!empty($caseStudy['results_data'])) {
                    $caseStudy['results_data'] = json_decode($caseStudy['results_data'], true);
                }

                return $this->success([
                    'message' => 'Case study updated successfully',
                    'case_study' => $caseStudy
                ]);
            } else {
                return $this->error('Failed to update case study');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to update case study: ' . $e->getMessage());
        }
    }

    /**
     * Delete case study
     * DELETE /api/case-studies/{id}
     */
    public function delete($id)
    {
        $payload = $this->requirePermission('manage_posts'); // Using same permission as posts

        try {
            $existingCaseStudy = $this->caseStudy->getById($id);
            if (!$existingCaseStudy) {
                return $this->error('Case study not found', 404);
            }

            $success = $this->caseStudy->delete($id);
            
            if ($success) {
                return $this->success([
                    'message' => 'Case study deleted successfully'
                ]);
            } else {
                return $this->error('Failed to delete case study');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to delete case study: ' . $e->getMessage());
        }
    }

    /**
     * Auto-save case study
     * POST /api/case-studies/autosave
     */
    public function autosave()
    {
        $payload = $this->requirePermission('create_posts');
        $data = $this->getRequestData();

        try {
            $caseStudyId = $data['id'] ?? null;

            // Sanitize data while preserving HTML in content fields
            $data = $this->sanitizeCaseStudyData($data);
            $data['author_id'] = $payload['user_id'];

            if ($caseStudyId) {
                // Update existing case study
                $success = $this->caseStudy->update($caseStudyId, $data);
                $caseStudy = $this->caseStudy->getById($caseStudyId);
            } else {
                // Create new draft
                $data['status'] = 'draft';
                $caseStudyId = $this->caseStudy->create($data);
                $caseStudy = $this->caseStudy->getById($caseStudyId);
            }

            // Process results_data JSON
            if (!empty($caseStudy['results_data'])) {
                $caseStudy['results_data'] = json_decode($caseStudy['results_data'], true);
            }

            return $this->success([
                'message' => 'Auto-saved successfully',
                'case_study' => $caseStudy,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return $this->error('Auto-save failed: ' . $e->getMessage());
        }
    }

    /**
     * Get case study statistics
     * GET /api/case-studies/stats
     */
    public function getStats()
    {
        try {
            $stats = $this->caseStudy->getStats();
            
            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error('Failed to get case study statistics: ' . $e->getMessage());
        }
    }

    /**
     * Publish case study
     * POST /api/case-studies/{id}/publish
     */
    public function publish($id)
    {
        $payload = $this->requirePermission('manage_posts');

        try {
            $caseStudy = $this->caseStudy->getById($id);

            if (!$caseStudy) {
                return $this->error('Case study not found', 404);
            }

            $success = $this->caseStudy->update($id, [
                'status' => 'published',
                'published_at' => date('Y-m-d H:i:s')
            ]);

            if ($success) {
                return $this->success(['message' => 'Case study published successfully']);
            } else {
                return $this->error('Failed to publish case study');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to publish case study: ' . $e->getMessage());
        }
    }

    /**
     * Unpublish case study
     * POST /api/case-studies/{id}/unpublish
     */
    public function unpublish($id)
    {
        $payload = $this->requirePermission('manage_posts');

        try {
            $caseStudy = $this->caseStudy->getById($id);

            if (!$caseStudy) {
                return $this->error('Case study not found', 404);
            }

            $success = $this->caseStudy->update($id, [
                'status' => 'draft',
                'published_at' => null
            ]);

            if ($success) {
                return $this->success(['message' => 'Case study unpublished successfully']);
            } else {
                return $this->error('Failed to unpublish case study');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to unpublish case study: ' . $e->getMessage());
        }
    }

    /**
     * Get case study by slug (for frontend)
     * GET /api/case-studies/slug/{slug}
     */
    public function getBySlug($slug)
    {
        try {
            $caseStudy = $this->caseStudy->getBySlug($slug);

            if (!$caseStudy) {
                return $this->error('Case study not found', 404);
            }

            // Only return published case studies for public access
            if ($caseStudy['status'] !== 'published') {
                return $this->error('Case study not available', 404);
            }

            // Process results_data JSON
            if (!empty($caseStudy['results_data'])) {
                $caseStudy['results_data'] = json_decode($caseStudy['results_data'], true);
            }

            return $this->success([
                'case_study' => $caseStudy
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to fetch case study: ' . $e->getMessage());
        }
    }

    /**
     * Sanitize case study data while preserving HTML in content fields
     */
    private function sanitizeCaseStudyData($data)
    {
        // Fields that should preserve HTML
        $htmlFields = [
            'hero_description', 'highlight_1_content', 'highlight_2_content',
            'highlight_3_content', 'highlight_4_content', 'highlight_5_content',
            'highlight_6_content', 'challenge_description', 'solution_description',
            'methodology_description', 'outcomes_description', 'excerpt', 'custom_css'
        ];

        // Fields that should be plain text
        $textFields = [
            'title', 'client_name', 'industry', 'hero_title', 'hero_subtitle',
            'hero_badge_text', 'highlight_1_title', 'highlight_2_title',
            'highlight_3_title', 'highlight_4_title', 'highlight_5_title',
            'highlight_6_title', 'challenge_title', 'challenge_subtitle',
            'solution_title', 'methodology_title', 'outcomes_title',
            'meta_title', 'meta_description', 'meta_keywords', 'focus_keyword',
            'canonical_url', 'og_title', 'og_description'
        ];

        $sanitized = [];

        foreach ($data as $key => $value) {
            if (in_array($key, $htmlFields)) {
                // Allow HTML but sanitize dangerous content
                $sanitized[$key] = $this->sanitizeHtml($value);
            } elseif (in_array($key, $textFields)) {
                // Strip all HTML, handle null values
                $sanitized[$key] = $value !== null ? strip_tags($value) : '';
            } else {
                // Default: keep as is for other fields
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize HTML content
     */
    protected function sanitizeHtml($content)
    {
        if (empty($content)) {
            return $content;
        }

        // Allow basic HTML tags
        $allowedTags = '<p><br><strong><b><em><i><u><a><ul><ol><li><h1><h2><h3><h4><h5><h6><blockquote><div><span>';
        
        return strip_tags($content, $allowedTags);
    }
}
