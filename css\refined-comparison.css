/* Refined Comparison Section - Ultra Modern Design */

.refined-comparison {
    padding: 120px 0;
    background-color: #fafafa;
    position: relative;
    overflow: hidden;
}

/* Subtle background pattern */
.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(rgba(233, 88, 161, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
}

/* Section container */
.refined-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    position: relative;
    z-index: 1;
}

/* Section header with modern asymmetrical design */
.refined-header {
    margin-bottom: 80px;
    max-width: 800px;
}

.refined-tag {
    display: inline-block;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    color: #e958a1;
    margin-bottom: 20px;
    position: relative;
    padding-left: 30px;
}

.refined-tag::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 20px;
    height: 1px;
    background: #e958a1;
    transform: translateY(-50%);
}

.refined-title {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 25px;
    color: #333;
    letter-spacing: -0.5px;
}

.refined-title span {
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.refined-subtitle {
    font-size: 18px;
    line-height: 1.6;
    color: #666;
    max-width: 600px;
}

/* Comparison layout with offset design */
.comparison-layout {
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 100px;
}

/* Comparison row with cards */
.comparison-row {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    position: relative;
}

/* VS divider with modern design */
.vs-divider {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    z-index: 3;
    font-weight: 700;
    font-size: 16px;
    color: #333;
}

.vs-divider::after {
    content: '';
    position: absolute;
    top: -100px;
    bottom: -100px;
    left: 50%;
    width: 1px;
    background: linear-gradient(to bottom, 
        rgba(233, 88, 161, 0),
        rgba(233, 88, 161, 0.2),
        rgba(233, 88, 161, 0));
    z-index: -1;
}

/* Comparison cards with offset design */
.comparison-card {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    max-width: 500px;
}

.comparison-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
}

/* Traditional card styling */
.card-traditional {
    margin-right: 40px;
    z-index: 1;
}

/* Adzeta card styling with accent */
.card-adzeta {
    margin-left: 40px;
    z-index: 2;
}

.card-adzeta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #e958a1, #8f76f5);
}

/* Card badge */
.card-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 30px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 15px;
}

.card-traditional .card-badge {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.card-adzeta .card-badge {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

/* Recommended tag */
.recommended-tag {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 20px;
    letter-spacing: 0.5px;
}

/* Card header */
.card-header {
    margin-bottom: 25px;
}

.card-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.card-description {
    font-size: 15px;
    color: #666;
    line-height: 1.5;
}

/* Feature list with modern styling */
.feature-list {
    margin-bottom: 30px;
}

.feature-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    font-size: 15px;
    color: #555;
    line-height: 1.5;
}

.feature-list li:last-child {
    margin-bottom: 0;
}

.feature-icon {
    margin-right: 12px;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    margin-top: 2px;
}

.feature-icon.negative {
    background: rgba(244, 67, 54, 0.1);
    color: #F44336;
}

.feature-icon.positive {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

/* Card action */
.card-action {
    text-align: center;
}

.btn-card {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 15px;
    text-decoration: none;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
}

.card-traditional .btn-card {
    background: #f8f9fa;
    color: #555;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.card-traditional .btn-card:hover {
    background: #e9ecef;
}

.card-adzeta .btn-card {
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.2);
}

.card-adzeta .btn-card:hover {
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    transform: translateY(-3px);
}

/* Results section with modern card design */
.results-card {
    background: white;
    border-radius: 16px;
    padding: 50px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
    text-align: center;
    position: relative;
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
}

.results-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
}

.results-title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

.results-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
}

/* Stats with modern design */
.stats-grid {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.stat-box {
    position: relative;
}

.stat-value {
    font-size: 48px;
    font-weight: 700;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 10px;
    position: relative;
}

.stat-label {
    font-size: 15px;
    color: #666;
    font-weight: 500;
}

/* CTA button with modern design */
.btn-cta {
    display: inline-flex;
    align-items: center;
    padding: 15px 35px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-weight: 600;
    font-size: 16px;
    border-radius: 30px;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.2);
}

.btn-cta i {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.btn-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    color: white;
}

.btn-cta:hover i {
    transform: translateX(5px);
}

/* Animation classes */
.fade-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s cubic-bezier(0.165, 0.84, 0.44, 1), 
                transform 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.fade-up.active {
    opacity: 1;
    transform: translateY(0);
}

.fade-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: opacity 0.8s cubic-bezier(0.165, 0.84, 0.44, 1), 
                transform 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.fade-left.active {
    opacity: 1;
    transform: translateX(0);
}

.fade-right {
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.8s cubic-bezier(0.165, 0.84, 0.44, 1), 
                transform 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.fade-right.active {
    opacity: 1;
    transform: translateX(0);
}

.scale-up {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.8s cubic-bezier(0.165, 0.84, 0.44, 1), 
                transform 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.scale-up.active {
    opacity: 1;
    transform: scale(1);
}

/* Staggered animation delays */
.delay-1 {
    transition-delay: 0.1s;
}

.delay-2 {
    transition-delay: 0.2s;
}

.delay-3 {
    transition-delay: 0.3s;
}

.delay-4 {
    transition-delay: 0.4s;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .refined-comparison {
        padding: 80px 0;
    }
    
    .refined-title {
        font-size: 36px;
    }
    
    .comparison-row {
        flex-direction: column;
        margin-bottom: 60px;
    }
    
    .comparison-card {
        width: 100%;
        max-width: 500px;
        margin: 0 0 30px 0;
    }
    
    .card-traditional {
        margin-right: 0;
    }
    
    .card-adzeta {
        margin-left: 0;
    }
    
    .vs-divider {
        position: relative;
        margin: 20px auto;
        transform: none;
        left: auto;
        top: auto;
    }
    
    .vs-divider::after {
        display: none;
    }
    
    .results-card {
        padding: 40px 30px;
    }
}

@media (max-width: 767px) {
    .refined-comparison {
        padding: 60px 0;
    }
    
    .refined-header {
        margin-bottom: 50px;
    }
    
    .refined-title {
        font-size: 30px;
    }
    
    .refined-subtitle {
        font-size: 16px;
    }
    
    .comparison-card {
        padding: 30px;
    }
    
    .card-title {
        font-size: 20px;
    }
    
    .feature-list li {
        font-size: 14px;
    }
    
    .results-title {
        font-size: 24px;
    }
    
    .stat-value {
        font-size: 36px;
    }
    
    .btn-cta {
        padding: 12px 25px;
        font-size: 15px;
    }
}
