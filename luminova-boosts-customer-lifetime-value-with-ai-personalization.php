<?php include 'header-light.php'; ?>
        <!-- end header -->
        <!-- start section -->
        <section class="big-section position-relative ipad-top-space-margin overflow-hidden pb-0 ">
		  <link rel="stylesheet" href="css/dark-purple-theme.css?v=1.0" />
            <div class="light-professional-gradient-container opacity-dark">
                <div class="light-corner-gradient top-left"></div>
                <div class="light-corner-gradient top-right"></div>
                <div class="light-corner-gradient bottom-left"></div>
                <div class="light-corner-gradient bottom-right"></div>
                <div class="light-diagonal-gradient"></div>
                <div class="light-mesh-overlay"></div>
                <div class="light-vignette-overlay"></div>
            </div>
            <div class="container">
                <div class="row justify-content-between align-items-center position-relative row mt-5 mb-5 md-mt-0">
                    <div class="col-12 col-lg-5 col-md-6 sm-mb-40px sm-mt-30px text-center text-md-start">
                        <div class="mb-10px">
                         <!--    <span class="w-25px h-1px d-inline-block bg-base-color me-5px align-middle"></span> -->
                            <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">AI-DRIVEN E-COMMERCE SUCCESS</span>
                        </div>
                        <h1 class="alt-font fs-40 lg-fs-32 md-fs-30 sm-fs-28 lh-80 md-lh-60 sm-lh-50 d-block text-dark-gray fw-600 ls-minus-1px mb-15px">LumiNova Home Boosts Customer Lifetime Value by 72% with AdZeta's AI-Powered Personalization Engine</h1>
                        <p class="text-dark-gray opacity-5">LumiNova Home, a rapidly growing direct-to-consumer (DTC) brand specializing in smart home lighting solutions, partnered with AdZeta to overcome challenges in customer retention and personalized marketing. By implementing AdZeta's AI-driven personalization and predictive analytics, LumiNova Home achieved a 72% increase in Customer Lifetime Value (CLV) and significantly improved repeat purchase rates.</p>
                         <!--   <div class="elements-social social-icon-style-08">
                            <ul class="medium-icon dark">
                                <li class="mx-0"><a class="facebook" href="https://www.facebook.com" target="_blank"><i class="fa-brands fa-facebook-f"></i></a></li>
                                <li class="mx-0"><a class="twitter" href="https://www.twitter.com" target="_blank"><i class="fa-brands fa-twitter"></i></a></li>
                                <li class="mx-0"><a class="linkedin" href="https://www.linkedin.com" target="_blank"><i class="fa-brands fa-linkedin-in"></i></a></li>
                                <li class="mx-0"><a class="pinterest" href="https://www.pinterest.com" target="_blank"><i class="fa-brands fa-pinterest-p"></i></a></li>
                            </ul>
                        </div>-->
                    </div>
                    <div class="col-12 col-lg-7 col-md-6">
                        <img class="border-radius-8px" src="images/case-studies/luminova.png" alt="">
                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
        <!-- Highlights start section -->
        <section class="position-relative background-position-center-top big-section overflow-hidden">
		     <script type="text/javascript" src="js/equal-height.js"></script>
            <!-- Professional dark gradient container -->
            <div class="professional-gradient-container">
                <div class="corner-gradient top-left"></div>
                <div class="corner-gradient top-right"></div>
                <div class="corner-gradient bottom-left"></div>
                <div class="corner-gradient bottom-right"></div>
                <div class="diagonal-gradient"></div>
                <div class="mesh-overlay"></div>
                <div class="vignette-overlay"></div>
            </div>
            <!-- Section heading -->
            <div class="container position-relative mb-5">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-6 text-center">
                        <div class="mb-5px">
                            <!-- <span class="w-25px h-1px d-inline-block bg-base-color me-5px align-middle"></span> -->
                            <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">PROJECT HIGHLIGHTS</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Feature boxes -->
            <div class="container position-relative">
                <div class="row row-cols-1 row-cols-lg-3 row-cols-md-2 justify-content-center row-equal-height" data-anime='{"el": "childs", "translateY": [0, 0], "perspective": [1200,1200], "scale": [1.1, 1], "rotateX": [50, 0], "opacity": [0,0.7], "duration": 800, "delay": 200, "staggervalue": 300, "easing": "easeOutQuad" }'>
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-07 transition-inner-all">
                        <div class="hover-box dark-hover feature-box p-55px lg-p-30px overflow-hidden bg-black-pearl-blue-dark text-start box-shadow-double-large-hover">
                            <div class="feature-box-icon min-h-120px sm-min-h-100px mb-20 z-index-9">
                                <i class="bi bi-lightbulb icon-extra-large text-gradient-pink-orchid"></i>
                            </div>
                            <div class="feature-box-title fs-200 fw-600 text-white opacity-2 ls-minus-10px">01</div>
                            <div class="feature-box-content last-paragraph-no-margin">
                                <h3 class="d-inline-block text-white mb-5px fs-18">Client Profile: LumiNova Home</h3>
                                <p class="w-90 xl-w-100 lh-30 text-light-opacity">LumiNova Home is a dynamic DTC e-commerce brand offering innovative and stylish smart home lighting systems. Their product range includes ambient, security, and decorative smart lighting, catering to tech-savvy homeowners seeking convenience and modern aesthetics.</p>
                            </div>
                            <div class="feature-box-overlay bg-gradient-pink-orchid"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-07 transition-inner-all">
                        <div class="hover-box dark-hover feature-box p-55px lg-p-30px overflow-hidden bg-black-pearl-blue-dark text-start box-shadow-double-large-hover">
                            <div class="feature-box-icon min-h-120px sm-min-h-100px mb-20 z-index-9">
                                <i class="bi bi-question-circle icon-extra-large text-gradient-pink-orchid"></i>
                            </div>
                            <div class="feature-box-title fs-200 fw-600 text-white opacity-2 ls-minus-10px">02</div>
                            <div class="feature-box-content last-paragraph-no-margin">
                                <h3 class="d-inline-block text-white mb-5px fs-18">The Challenge: Scaling Personalization & Combating Cart Abandonment</h3>
                                <p class="w-90 xl-w-100 lh-30 text-light-opacity">Despite a growing customer base, LumiNova Home faced high cart abandonment rates (averaging 75%) and struggled to effectively personalize the customer journey at scale. This led to missed revenue opportunities and a lower-than-desired repeat purchase rate, impacting overall CLV.</p>
                            </div>
                            <div class="feature-box-overlay bg-gradient-pink-orchid"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-07 transition-inner-all">
                        <div class="hover-box dark-hover feature-box p-55px lg-p-30px overflow-hidden bg-black-pearl-blue-dark text-start box-shadow-double-large-hover">
                            <div class="feature-box-icon min-h-120px sm-min-h-100px mb-20 z-index-9">
                                <i class="bi bi-compass icon-extra-large text-gradient-pink-orchid"></i>
                            </div>
                            <div class="feature-box-title fs-200 fw-600 text-white opacity-2 ls-minus-10px">03</div>
                            <div class="feature-box-content last-paragraph-no-margin">
                                <h3 class="d-inline-block text-white mb-5px fs-18">Previous Marketing Strategy & Limitations</h3>
                                <p class="w-90 xl-w-100 lh-30 text-light-opacity">LumiNova Home's previous strategy relied on generic email marketing blasts and standard retargeting campaigns on Google and Meta. This one-size-fits-all approach failed to resonate with diverse customer segments, resulting in low engagement and suboptimal conversion rates for abandoned carts and repeat purchases.</p>
                            </div>
                            <div class="feature-box-overlay bg-gradient-pink-orchid"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-07 transition-inner-all">
                        <div class="hover-box dark-hover feature-box p-55px lg-p-30px overflow-hidden bg-black-pearl-blue-dark text-start box-shadow-double-large-hover">
                            <div class="feature-box-icon min-h-120px sm-min-h-100px mb-20 z-index-9">
                                <i class="bi bi-cpu icon-extra-large text-gradient-pink-orchid"></i>
                            </div>
                            <div class="feature-box-title fs-200 fw-600 text-white opacity-2 ls-minus-10px">04</div>
                            <div class="feature-box-content last-paragraph-no-margin">
                                <h3 class="d-inline-block text-white mb-5px fs-18">The AdZeta Solution: AI-Powered Personalization & Predictive Insights</h3>
                                <p class="w-90 xl-w-100 lh-30 text-light-opacity">AdZeta deployed its advanced AI engine to analyze customer behavior, predict churn likelihood, and identify optimal NBOs (Next Best Offers). This enabled LumiNova Home to deliver hyper-personalized experiences across email, on-site recommendations, and targeted advertising, specifically addressing cart recovery and customer loyalty.</p>
                            </div>
                            <div class="feature-box-overlay bg-gradient-pink-orchid"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-07 transition-inner-all">
                        <div class="hover-box dark-hover feature-box p-55px lg-p-30px overflow-hidden bg-black-pearl-blue-dark text-start box-shadow-double-large-hover">
                            <div class="feature-box-icon min-h-120px sm-min-h-100px mb-20 z-index-9">
                                <i class="bi bi-gear-wide-connected icon-extra-large text-gradient-pink-orchid"></i>
                            </div>
                            <div class="feature-box-title fs-200 fw-600 text-white opacity-2 ls-minus-10px">05</div>
                            <div class="feature-box-content last-paragraph-no-margin">
                                <h3 class="d-inline-block text-white mb-5px fs-18">Strategic Implementation & Execution</h3>
                                <p class="w-90 xl-w-100 lh-30 text-light-opacity">AdZeta integrated with LumiNova Home's e-commerce platform (Shopify) and CRM, creating a unified customer data view. AI models were trained to predict cart abandonment intent and identify high-potential repeat buyers. Automated, personalized email sequences and dynamic on-site content were triggered based on individual user behavior and predictive scores.</p>
                            </div>
                            <div class="feature-box-overlay bg-gradient-pink-orchid"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-07 transition-inner-all">
                        <div class="hover-box dark-hover feature-box p-55px lg-p-30px overflow-hidden bg-black-pearl-blue-dark text-start box-shadow-double-large-hover">
                            <div class="feature-box-icon min-h-120px sm-min-h-100px mb-20 z-index-9">
                                <i class="bi bi-trophy icon-extra-large text-gradient-pink-orchid"></i>
                            </div>
                            <div class="feature-box-title fs-200 fw-600 text-white opacity-2 ls-minus-10px">06</div>
                            <div class="feature-box-content last-paragraph-no-margin">
                                <h3 class="d-inline-block text-white mb-5px fs-18">Quantifiable Outcomes & Business Impact</h3>
                                <p class="w-90 xl-w-100 lh-30 text-light-opacity">The partnership resulted in a 28% reduction in cart abandonment rates and a 45% increase in repeat purchase frequency. Most notably, LumiNova Home achieved a 72% uplift in overall Customer Lifetime Value (CLV), demonstrating the power of AI-driven personalization.</p>
                            </div>
                            <div class="feature-box-overlay bg-gradient-pink-orchid"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                </div>
            </div>
        </section>
        <!-- end section -->
        <!-- start section - The Challenge -->
        <section class="overflow-hidden position-relative" >

    <div class="light-accent-gradient"></div>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-xl-6 col-lg-8 col-md-10 text-center margin-5-rem-bottom md-margin-3-rem-bottom">
                        <div class="mb-10px">
                           <!--  <span class="w-25px h-1px d-inline-block bg-base-color me-5px align-middle"></span> -->
                            <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">Understanding the Problem</span>
                        </div>
                        <h2 class="alt-font fw-600 text-dark-gray fs-40 lg-fs-32 md-fs-30 sm-fs-28 ls-minus-1px mb-20px">The Core Challenge: Generic Engagement & Lost Revenue in DTC E-commerce</h2>
                        <p class="w-80 mx-auto md-w-100 opacity-7">Many DTC e-commerce brands struggle to move beyond generic marketing tactics, leading to disengaged customers, high cart abandonment, and untapped CLV.</p>
                    </div>
                </div>
                <!-- Challenge comparison -->
                <div class="row justify-content-center margin-5-rem-bottom">
                    <div class="col-12">
                        <div class="modern-funnel-comparison p-5 border-radius-10px position-relative" style="overflow: visible;">
                            <!-- Comparison header -->

                            <!-- Center divider line -->
                            <div class="position-absolute" style="top: 120px; bottom: 40px; left: 50%; width: 1px; background: linear-gradient(to bottom, rgba(0,0,0,0.02), rgba(0,0,0,0.08), rgba(0,0,0,0.02));"></div>
                            <!-- Funnel comparison -->
                            <div class="row">
                                <!-- Left funnel (without) -->
                                <div class="col-6 position-relative">
                                    <div class="text-center mb-3">
                                        <h6 class="alt-font fw-600 text-dark-gray mb-0">E-commerce funnel</h6>
                                        <span class="text-medium opacity-7">without</span>
                                        <h6 class="alt-font font-weight-600 text-gradient-pink-orchid mb-0">AdZeta</h6>
                                    </div>
                                    <!-- Top funnel section -->
                                    <div class="mx-auto mb-2 funnel-stage funnel-top" style="width: 46%; height: 70px;">
                                        <svg width="100%" height="100%" viewBox="0 0 300 70" preserveAspectRatio="none">
                                            <defs>
                                                <linearGradient id="topFunnelGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                                    <stop offset="0%" stop-color="#ffffff" />
                                                    <stop offset="100%" stop-color="#f8f8f8" />
                                                </linearGradient>
                                            </defs>
                                            <path d="M262.5 0.5
                                                C271.5 0.5 278.13 8.92 276.01 17.67
                                                L266.42 57.27
                                                C264.91 63.51 259.33 67.9 252.91 67.9
                                                H47.09
                                                C40.67 67.9 35.09 63.51 33.58 57.27
                                                L23.99 17.67
                                                C21.87 8.92 28.5 0.5 37.5 0.5
                                                H262.5Z"
                                                fill="white"
                                                fill-opacity="0.8"
                                                stroke="#D7D3D3" />
                                            <!-- Add icon in the center of the funnel -->
                                            <g transform="translate(150, 35)" text-anchor="middle">
                                                <text font-family="FontAwesome" font-size="16" fill="#757575" dy=".35em">&#xf234;</text>
                                            </g>
                                        </svg>
                                    </div>
                                    <div class="text-center mb-2">
                                        <small class="text-muted fs-11">All Website Visitors</small>
                                    </div>
                                    <!-- Middle funnel section (highlighted) -->
                                    <div class="mx-auto mb-2 funnel-stage funnel-middle" style="width: 33%; height: 70px;">
                                        <svg width="100%" height="100%" viewBox="190 80 160 70" preserveAspectRatio="none">
                                            <defs>
                                                <linearGradient id="midFunnelGradient1" x1="0%" y1="0%" x2="0%" y2="100%">
                                                    <stop offset="0%" stop-color="#E0E0E0" />
                                                    <stop offset="100%" stop-color="#EEEEEE" />
                                                </linearGradient>
                                            </defs>
                                            <path d="M334.408 82.0996C343.404 82.0996 350.03 90.5141 347.921 99.2587L338.37 138.859C336.864 145.101 331.279 149.5 324.857 149.5H217.745C211.324 149.5 205.738 145.101 204.232 138.859L194.681 99.2587C192.572 90.5141 199.198 82.0996 208.194 82.0996H334.408Z"
                                                fill="#E0E0E0"
                                                stroke="#BDBDBD"
                                                stroke-width="1.5" />
                                            <!-- Add icon in the center of the funnel -->
                                            <g transform="translate(270, 115)" text-anchor="middle">
                                                <text font-family="FontAwesome" font-size="16" fill="#757575" dy=".35em">&#xf0e8;</text>
                                            </g>
                                        </svg>
                                    </div>
                                    <!-- Optimization event label -->
                                    <div class="funnel-label left-label position-absolute" style="right: 15px; top: 95px;">
                                        <div class="d-inline-flex align-items-center">
                                            <div style="background: linear-gradient(135deg, #9E9E9E, #757575); color: white; border-radius: 12px; padding: 10px 14px; font-size: 14px; line-height: 1.4; position: relative; box-shadow: 0 4px 15px rgba(158, 158, 158, 0.3);">
                                                <div style="font-weight: 400; opacity: 0.9; font-size: 12px;">Optimization event</div>
                                                <div style="font-weight: 600; letter-spacing: -0.5px;">Cart Addition</div>
                                                <!-- Triangle pointer -->
                                                <div class="position-absolute label-arrow label-arrow-right" style="left: -8px; top: 50%; transform: translateY(-50%);">
                                                    <svg width="8" height="16" viewBox="0 0 8 16">
                                                        <path d="M8,0 L0,8 L8,16 Z" fill="#9E9E9E" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Lower funnel section -->
                                    <div class="mx-auto mb-2 funnel-stage funnel-lower" style="width: 24%; height: 70px;">
                                        <svg width="100%" height="100%" viewBox="210 160 125 80" preserveAspectRatio="none">
                                            <defs>
                                                <linearGradient id="midFunnelGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                                    <stop offset="0%" stop-color="#ffffff" />
                                                    <stop offset="100%" stop-color="#f8f8f8" />
                                                </linearGradient>
                                            </defs>
                                            <path d="M317.229 162.5C326.238 162.5 332.867 170.939 330.733 179.692L321.08 219.292C319.562 225.518 313.984 229.9 307.576 229.9H235.029C228.62 229.9 223.042 225.518 221.525 219.292L211.872 179.692C209.738 170.939 216.367 162.5 225.376 162.5H317.229Z" fill="#E0E0E0" fill-opacity="0.9" stroke="#BDBDBD" stroke-width="1.5" />
                                        </svg>
                                    </div>
                                    <div class="text-center mb-2">
                                        <small class="text-muted fs-11">Cart Abandonment</small>
                                    </div>
                                    <!-- Bottom funnel section -->
                                    <div class="mx-auto funnel-stage funnel-bottom" style="width: 25%; height: 70px;">
                                        <svg width="100%" height="100%" viewBox="210 240 125 80" preserveAspectRatio="none">
                                            <defs>
                                                <linearGradient id="bottomFunnelGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                                    <stop offset="0%" stop-color="#ffffff" />
                                                    <stop offset="100%" stop-color="#f8f8f8" />
                                                </linearGradient>
                                            </defs>
                                            <path d="M299.044 244.1C308.047 244.1 314.675 252.527 312.553 261.276L302.948 300.876C301.436 307.11 295.855 311.5 289.44 311.5H253.166C246.751 311.5 241.17 307.11 239.658 300.876L230.053 261.276C227.931 252.527 234.559 244.1 243.562 244.1H299.044Z" fill="#BDBDBD" fill-opacity="0.9" stroke="#9E9E9E" stroke-width="1.5" />
                                        </svg>
                                    </div>
                                    <div class="text-center mb-2">
                                        <small class="text-muted fs-11">Low Repeat Purchases</small>
                                    </div>
                                </div>
                                <!-- Right funnel (with) -->
                                <div class="col-6 position-relative">
                                    <div class="text-center mb-3">
                                        <h6 class="alt-font fw-600 text-dark-gray mb-0">E-commerce funnel</h6>
                                        <span class="text-medium opacity-7">with</span>
                                        <h6 class="alt-font font-weight-600 text-gradient-pink-orchid mb-0">AdZeta</h6>
                                    </div>
                                    <!-- Top funnel section -->
                                    <div class="mx-auto mb-2 funnel-stage funnel-top" style="width: 46%; height: 70px;">
                                        <svg width="100%" height="100%" viewBox="0 0 215 70" preserveAspectRatio="none">
                                            <defs>
                                                <linearGradient id="topFunnelGradient2" x1="0%" y1="0%" x2="0%" y2="100%">
                                                    <stop offset="0%" stop-color="#ffffff" />
                                                    <stop offset="100%" stop-color="#f8f8f8" />
                                                </linearGradient>
                                            </defs>
                                            <path d="M187.897 0.5C196.897 0.5 203.525 8.92362 201.406 17.6713L191.817 57.2713C190.307 63.5077 184.724 67.9 178.308 67.9H34.8922C28.4755 67.9 22.8928 63.5077 21.3826 57.2713L11.7935 17.6713C9.67529 8.92361 16.3026 0.5 25.3031 0.5H187.897Z" fill="#FCE4EC" fill-opacity="0.9" stroke="#F8BBD0" stroke-width="1.5"/>
                                            <!-- Add icon in the center of the funnel -->
                                            <g transform="translate(100, 35)" text-anchor="middle">
                                                <text font-family="FontAwesome" font-size="16" fill="#EC407A" dy=".35em">&#xf0c0;</text>
                                            </g>
                                        </svg>
                                    </div>
                                    <div class="text-center mb-2">
                                        <small class="text-gradient-pink-orchid fs-11 fw-600">Personalized Traffic</small>
                                    </div>
                                    <!-- Middle funnel section -->
                                    <div class="mx-auto mb-2 funnel-stage funnel-middle" style="width: 33%; height: 70px;">
                                        <svg width="100%" height="100%" viewBox="190 80 160 70" preserveAspectRatio="none">
                                            <defs>
                                                <linearGradient id="midFunnelGradient2" x1="0%" y1="0%" x2="0%" y2="100%">
                                                    <stop offset="0%" stop-color="#ffffff" />
                                                    <stop offset="100%" stop-color="#f8f8f8" />
                                                </linearGradient>
                                            </defs>
                                            <path  d="M334.408 82.0996C343.404 82.0996 350.03 90.5141 347.921 99.2587L338.37 138.859C336.864 145.101 331.279 149.5 324.857 149.5H217.745C211.324 149.5 205.738 145.101 204.232 138.859L194.681 99.2587C192.572 90.5141 199.198 82.0996 208.194 82.0996H334.408Z" fill="#F8BBD0" fill-opacity="0.9" stroke="#F48FB1" stroke-width="1.5"/>
                                        </svg>
                                    </div>
                                    <!-- Lower funnel section -->
                                    <div class="mx-auto mb-2 funnel-stage funnel-lower" style="width: 24%; height: 70px;">
                                        <svg width="100%" height="100%" viewBox="210 160 125 80" preserveAspectRatio="none">
                                            <defs>
                                                <linearGradient id="lowerFunnelGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                                    <stop offset="0%" stop-color="#ffffff" />
                                                    <stop offset="100%" stop-color="#ffffff" />
                                                </linearGradient>
                                            </defs>
                                            <path d="M317.229 162.5C326.238 162.5 332.867 170.939 330.733 179.692L321.08 219.292C319.562 225.518 313.984 229.9 307.576 229.9H235.029C228.62 229.9 223.042 225.518 221.525 219.292L211.872 179.692C209.738 170.939 216.367 162.5 225.376 162.5H317.229Z" fill="#F48FB1" fill-opacity="0.9" stroke="#F06292" stroke-width="1.5" />
                                        </svg>
                                    </div>
                                    <div class="text-center mb-2">
                                        <small class="text-gradient-pink-orchid fs-11 fw-600">Cart Recovery</small>
                                    </div>
                                    <!-- Bottom funnel section (highlighted) with glow -->
                                    <div class="position-relative funnel-stage-highlight">
                                        <!-- Glow effect -->
                                        <!-- Bottom funnel shape -->
                                        <div class="mx-auto funnel-stage funnel-bottom" style="width: 25%; height: 70px; position: relative; z-index: 1;">
                                            <!-- Particle effects -->
                                            <div class="particles-container" style="position: absolute; width: 100%; height: 100%; overflow: hidden; z-index: 0;">
                                                <div class="particle particle-1"></div>
                                                <div class="particle particle-2"></div>
                                                <div class="particle particle-3"></div>
                                                <div class="particle particle-4"></div>
                                                <div class="particle particle-5"></div>
                                            </div>

                                            <svg style="width: 100%; height: 100%; position: relative; z-index: 1;" width="100%" height="100%" viewBox="210 240 125 80" preserveAspectRatio="none">
                                                <!-- Path for a trapezoid with more pronounced angles and rounded corners -->
                                                <path d="M299.044 244.1C308.047 244.1 314.675 252.527 312.553 261.276L302.948 300.876C301.436 307.11 295.855 311.5 289.44 311.5H253.166C246.751 311.5 241.17 307.11 239.658 300.876L230.053 261.276C227.931 252.527 234.559 244.1 243.562 244.1H299.044Z"
                                                    fill="#EC407A"
                                                    stroke="#E91E63"
                                                    stroke-width="1.5" />
                                                <!-- Add icon in the center of the funnel -->
                                                <g transform="translate(270, 280)" text-anchor="middle">
                                                    <text font-family="FontAwesome" font-size="16" fill="white" dy=".35em">&#xf155;</text>
                                                </g>
                                            </svg>
                                        </div>

                                        <!-- Optimization event label -->
                                        <div class="funnel-label right-label position-absolute" style="left: 50px; top: 10px;">
                                            <div class="d-inline-flex align-items-center">
                                                <div style="background: linear-gradient(135deg, #EC407A, #E91E63); color: white; border-radius: 12px; padding: 10px 14px; font-size: 14px; line-height: 1.4; position: relative; box-shadow: 0 4px 15px rgba(236, 64, 122, 0.3);">
                                                    <div style="font-weight: 400; opacity: 0.9; font-size: 12px;">Optimization event</div>
                                                    <div style="font-weight: 600; letter-spacing: -0.5px;">Customer CLV</div>
                                                    <!-- Triangle pointer -->
                                                    <div class="position-absolute label-arrow label-arrow-left" style="right: -8px; top: 50%; transform: translateY(-50%);">
                                                        <svg width="8" height="16" viewBox="0 0 8 16">
                                                            <path d="M0,0 L8,8 L0,16 Z" fill="#E91E63" />
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-center mt-2">
                                            <small class="text-gradient-pink-orchid fs-11 fw-600">High Repeat Purchases</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <style>
				 .opacity-light-1{opacity:0.5;}
				.solution-card {
				  background: rgba(255, 255, 255, 0.30); /* more transparent for techy vibe */
				  backdrop-filter: blur(20px);
				  -webkit-backdrop-filter: blur(20px);
				  border-radius: 20px;
				  border: 1px solid rgba(255, 255, 255, 0.18); /* subtle border like glass edge */
				  transition: transform 0.3s ease, box-shadow 0.3s ease;
				  position: relative;
				  z-index: 1;
				}
				.solution-card:hover {
				  transform: translateY(-4px);
				}

				/* High-tech animated border with moving light streak */
				.high-tech-border {
				  position: relative;
				  border: 1px solid rgba(255, 255, 255, 0.3);
				  border-radius: 20px;
				  overflow: hidden;
				}

				/* Light streak that moves along the border */
				.high-tech-border::before {
				  content: '';
				  position: absolute;
				  top: -1px;
				  left: -1px;
				  right: -1px;
				  bottom: -1px;
				  border: 1px solid transparent;
				  border-radius: 20px;
				  background: none;
				  z-index: 2;
				  pointer-events: none;
				  mask: linear-gradient(90deg, transparent, white, transparent);
				  -webkit-mask: linear-gradient(90deg, transparent, white, transparent);
				  mask-size: 200% 100%;
				  -webkit-mask-size: 200% 100%;
				  animation: light-streak 4s linear infinite;
				  box-shadow: 0 0 10px rgba(233, 88, 161, 0.8), 0 0 20px rgba(143, 118, 245, 0.5);
				}

				@keyframes light-streak {
				  0% {
					mask-position: 200% 0;
					-webkit-mask-position: 200% 0;
					border-color: rgba(233, 88, 161, 0.8);
				  }
				  33% {
					border-color: rgba(255, 93, 116, 0.8);
				  }
				  66% {
					border-color: rgba(143, 118, 245, 0.8);
				  }
				  100% {
					mask-position: -100% 0;
					-webkit-mask-position: -100% 0;
					border-color: rgba(233, 88, 161, 0.8);
				  }
				}

				/* Light particles that follow the streak */
				.high-tech-border .border-particle {
				  position: absolute;
				  width: 4px;
				  height: 4px;
				  border-radius: 50%;
				  background: #e958a1;
				  box-shadow: 0 0 8px #e958a1, 0 0 15px #e958a1;
				  z-index: 2;
				  opacity: 0;
				  pointer-events: none;
				}

				.outside-box-right svg {
				  width: 100%;
				  height: auto;
				  max-width: 419px;
				  margin-top:20px;
				}

				.list-style-03 li:before{ color: var(--dark-gray);font-weight:600;}
                    /* Basic animations */
                    @keyframes pulse {
                        0% { opacity: 0.6; }
                        50% { opacity: 1; }
                        100% { opacity: 0.6; }
                    }

                    @keyframes float {
                        0% { transform: translateY(0px); }
                        50% { transform: translateY(-5px); }
                        100% { transform: translateY(0px); }
                    }

                    @keyframes glow {
                        0% { filter: drop-shadow(0 0 2px rgba(236, 64, 122, 0.3)); }
                        50% { filter: drop-shadow(0 0 8px rgba(236, 64, 122, 0.6)); }
                        100% { filter: drop-shadow(0 0 2px rgba(236, 64, 122, 0.3)); }
                    }

                    @keyframes flow {
                        0% { transform: translateY(-5px); opacity: 0; }
                        50% { opacity: 1; }
                        100% { transform: translateY(20px); opacity: 0; }
                    }

                    /* Particle animations */
                    @keyframes particle-rise {
                        0% { transform: translate(0, 100%); opacity: 0; }
                        25% { opacity: 1; }
                        75% { opacity: 1; }
                        100% { transform: translate(var(--x-end), -100%); opacity: 0; }
                    }

                    /* Particle styles */
                    .particles-container {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        overflow: hidden;
                    }

                    .particle {
                        position: absolute;
                        bottom: 0;
                        width: 6px;
                        height: 6px;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.8);
                        box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
                        animation: particle-rise 3s ease-in-out infinite;
                    }

                    .particle-1 {
                        left: 20%;
                        --x-end: 10px;
                        animation-delay: 0s;
                    }

                    .particle-2 {
                        left: 40%;
                        --x-end: -5px;
                        animation-delay: 0.5s;
                    }

                    .particle-3 {
                        left: 60%;
                        --x-end: 7px;
                        animation-delay: 1s;
                    }

                    .particle-4 {
                        left: 30%;
                        --x-end: -8px;
                        animation-delay: 1.5s;
                    }

                    .particle-5 {
                        left: 70%;
                        --x-end: 5px;
                        animation-delay: 2s;
                    }

                    /* Funnel stage animations */
                    .funnel-stage {
                        transition: transform 0.3s ease;
                    }

                    .funnel-stage:hover {
                        transform: translateY(-2px);
                    }

                    /* Right side funnel animations */
                    .col-6:nth-child(2) .funnel-top {
                        animation: float 4s ease-in-out infinite;
                        position: relative;
                    }

                    .col-6:nth-child(2) .funnel-middle {
                        animation: float 4s ease-in-out infinite 0.5s;
                        position: relative;
                    }

                    .col-6:nth-child(2) .funnel-lower {
                        animation: float 4s ease-in-out infinite 1s;
                        position: relative;
                    }

                    /* Flow indicators for right funnel */
                    .col-6:nth-child(2) .funnel-top::after,
                    .col-6:nth-child(2) .funnel-middle::after,
                    .col-6:nth-child(2) .funnel-lower::after {
                        content: '';
                        position: absolute;
                        width: 8px;
                        height: 8px;
                        background: rgba(236, 64, 122, 0.7);
                        border-radius: 50%;
                        bottom: -5px;
                        left: 50%;
                        transform: translateX(-50%);
                        animation: flow 2s ease-in-out infinite;
                        z-index: 2;
                    }

                    .col-6:nth-child(2) .funnel-top::after {
                        animation-delay: 0s;
                    }

                    .col-6:nth-child(2) .funnel-middle::after {
                        animation-delay: 0.7s;
                    }

                    .col-6:nth-child(2) .funnel-lower::after {
                        animation-delay: 1.4s;
                    }

                    /* Highlight animation for bottom funnel */
                    .funnel-stage-highlight {
                        transition: transform 0.3s ease;
                    }

                    .funnel-stage-highlight:hover {
                        transform: translateY(-3px);
                    }

                    .funnel-bottom svg {
                        animation: glow 3s ease-in-out infinite;
                    }

                    /* Icon animations - only for right funnel */
                    .col-6:nth-child(2) .funnel-stage svg g text {
                        animation: pulse 3s ease-in-out infinite;
                    }

                    /* Label styles */
                    .funnel-label {
                        z-index: 10;
                        transition: all 0.3s ease;
                    }

                    /* Label animations - only for right label */
                    .right-label {
                        animation: float 5s ease-in-out infinite 0.5s;
                    }

                    /* Arrow pointer styles */
                    .label-arrow {
                        z-index: 11;
                        transition: all 0.3s ease;
                    }

                    .label-arrow-right {
                        right: -8px;
                    }

                    .label-arrow-left {
                        right: -8px;
                    }

                    /* Only animate the right funnel's arrow */
                    .right-label .label-arrow-left {
                        animation: arrow-pulse 2s ease-in-out infinite 0.5s;
                    }

                    @keyframes arrow-pulse {
                        0% { transform: translateY(-50%) scale(1); }
                        50% { transform: translateY(-50%) scale(1.2); }
                        100% { transform: translateY(-50%) scale(1); }
                    }

                    /* Default label styles for all screen sizes */
                    .left-label {
                        right: 15px;
                        top: 95px;
                    }

                    .right-label {
                        left: 50px;
                        top: 10px;
                    }

                    /* Large screens (desktops) */
                    @media (min-width: 1200px) {
                        .left-label .d-inline-flex > div {
                            padding: 12px 16px;
                            font-size: 15px;
                        }

                        .right-label .d-inline-flex > div {
                            padding: 12px 16px;
                            font-size: 15px;
                        }
                    }

                    /* Medium screens (tablets and small desktops) */
                    @media (max-width: 1199px) and (min-width: 768px) {
                        .left-label {
                            top: 80px !important;
							right: -35px !important;
                        }

                        .right-label {
                               left: -20px !important;
                        }

                        .left-label .d-inline-flex > div,
                        .right-label .d-inline-flex > div {
                            padding: 10px 14px;
                            font-size: 14px;
                        }
                    }

                    /* Small screens (tablets and large phones) */
                    @media (max-width: 767px) {
                        /* Scale down funnel sizes proportionally with different sizes for each stage */
                        .funnel-stage {
                            margin-left: auto !important;
                            margin-right: auto !important;
                        }

                        /* Top funnel (widest) */
                        .funnel-top {
                            width: 85% !important;
                            height: 55px !important;
                        }

                        /* Middle funnel */
                        .funnel-middle {
                            width: 70% !important;
                            height: 50px !important;
                        }

                        /* Lower funnel */
                        .funnel-lower {
                            width: 55% !important;
                            height: 45px !important;
                        }

                        /* Bottom funnel (narrowest) */
                        .funnel-bottom {
                            width: 45% !important;
                            height: 40px !important;
                        }

                        /* Adjust label positions */
                        .left-label {
                                   right: -55px !important;
								top: 130px !important;
                        }
                        .left-label .d-inline-flex > div {
                            padding: 8px 12px !important;
                            font-size: 12px !important;
                            max-width: 140px !important;
                            line-height: 1.3 !important;
                        }

                        .right-label {
                                left: -50px !important;
								top: 0px !important;
                        }
                        .right-label .d-inline-flex > div {
                            padding: 8px 12px !important;
                            font-size: 12px !important;
                            max-width: 140px !important;
                            line-height: 1.3 !important;
                        }

                        /* Adjust triangle directions and positions */
                        .left-label .label-arrow-right {
                            right: -8px !important;

                        }
                        .right-label .label-arrow-left {
                            right: -8px !important;

                        }

                        /* Adjust label content */
                        .funnel-label .d-inline-flex > div div:first-child {
                            font-size: 10px !important;
                        }

                        /* Adjust center divider */
                        .position-absolute[style*="left: 50%"] {
                            top: 100px !important;
                            bottom: 30px !important;
                        }
                    }

                    /* Extra small devices */
                    @media (max-width: 575px) {
                        .modern-funnel-comparison {
                            padding: 1rem !important;
                        }

                        /* Even smaller funnel sizes for extra small devices */
                        /* Top funnel (widest) */
                        .funnel-top {
                            width: 70% !important;
                            height: 45px !important;
                        }

                        /* Middle funnel */
                        .funnel-middle {
                            width: 50% !important;
                            height: 40px !important;
                        }

                        /* Lower funnel */
                        .funnel-lower {
                            width: 40% !important;
                            height: 40px !important;
                        }

                        /* Bottom funnel (narrowest) */
                        .funnel-bottom {
                            width: 40% !important;
                            height: 40px !important;
                        }

                        /* Adjust label positions for extra small screens */
                        .left-label {
                            right: -45px !important;
                            top: 120px !important;
                        }
                        .left-label .d-inline-flex > div {
                            padding: 5px 8px !important;
                            font-size: 10px !important;
                            max-width: 130px !important;
                            border-radius: 8px !important;
                        }

                        .right-label {
                            left: -40px !important;
                            top: 0px !important;
                        }
                        .right-label .d-inline-flex > div {
                            padding: 5px 8px !important;
                            font-size: 10px !important;
                            max-width: 130px !important;
                            border-radius: 8px !important;
                        }

                        /* Adjust label content for extra small screens */
                        .funnel-label .d-inline-flex > div div:first-child {
                            font-size: 9px !important;
                            margin-bottom: 2px !important;
                        }

                        .funnel-label .d-inline-flex > div div:last-child {
                            font-size: 10px !important;
                            letter-spacing: -0.3px !important;
                        }

                        /* Adjust triangle pointers */
                        .label-arrow svg {
                            width: 6px !important;
                            height: 12px !important;
                        }

                        /* Ensure arrows stay attached to labels */
                        .left-label .label-arrow-right {
                            left: -6px !important;
                        }
                        .right-label .label-arrow-left {
                            right: -6px !important;
                        }

                        /* Adjust spacing between funnel sections */
                        .mb-2 {
                            margin-bottom: 0.5rem !important;
                        }

                        /* Adjust header spacing */
                        .text-center.mb-3 {
                            margin-bottom: 0.75rem !important;
                        }
                        .text-center.mb-3 h6 {
                            font-size: 14px !important;
                        }
                    }

                    /* Very small screens (small phones) */
                    @media (max-width: 400px) {
                        /* Further adjust funnel sizes */
                        .funnel-top {
                            width: 95% !important;
                            height: 40px !important;
                        }

                        .funnel-middle {
                            width: 80% !important;
                            height: 35px !important;
                        }

                        .funnel-lower {
                            width: 65% !important;
                            height: 30px !important;
                        }

                        .funnel-bottom {
                            width: 55% !important;
                            height: 25px !important;
                        }

                        /* Adjust label positions for very small screens */
                        .left-label {
                                    right: -20px !important;
									top: 75px !important;
                        }

                        .right-label {
                            left: -50px !important;
                            top: -5px !important;
                        }

                        .left-label .d-inline-flex > div,
                        .right-label .d-inline-flex > div {
                            max-width: 80px !important;
                            padding: 4px 6px !important;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
                        }

                        /* Hide the "Optimization event" text on very small screens */
                        .funnel-label .d-inline-flex > div div:first-child {
                            display: none !important;
                        }

                        /* Ensure arrows stay attached to labels on very small screens */
                        .left-label .label-arrow-right {
                            right: -5px !important;
                        }
                        .right-label .label-arrow-left {
                            right: -5px !important;
                        }

                        /* Make arrows smaller on very small screens */
                        .label-arrow svg {
                            width: 5px !important;
                            height: 10px !important;
                        }
                    }
                </style>
                <!-- Challenge details -->
                <div class="row mt-5" >
                    <div class="col-12 appear">
                        <div class="row g-0 align-items-center text-center text-sm-start challenge-item">
                            <div class="col-12">
                                <div class="separator-line-1px w-100 bg-medium-gray opacity-2 bg-sliding-line"></div>
                            </div>
                            <div class="col-sm-1 text-center xs-mt-20px">
                                <div class="challenge-number">
                                    <span class="base-color-gradient fs-16 ls-minus-1px fw-600">01</span>
                                </div>
                            </div>
                            <div class="col-sm-3 offset-sm-1">
                                <span class="fw-600 text-dark-gray fs-20 ls-minus-1px">The High Cost of Cart Abandonment</span>
                            </div>
                            <div class="col-sm-7 p-35px sm-p-25px xs-p-20px xs-pt-10px last-paragraph-no-margin">
                                <p class="w-95 sm-w-100 lh-30 opacity-8">For online retailers like LumiNova Home, cart abandonment represents a significant loss of potential revenue. Without understanding the "why" behind abandonment and intervening effectively, valuable sales opportunities are consistently missed. Standard retargeting often proves insufficient.</p>
                            </div>
                        </div>
                        <div class="row g-0 align-items-center text-center text-sm-start challenge-item">
                            <div class="col-12">
                                <div class="separator-line-1px w-100 bg-medium-gray opacity-2 bg-sliding-line"></div>
                            </div>
                            <div class="col-sm-1 text-center xs-mt-20px">
                                <div class="challenge-number">
                                    <span class="base-color-gradient fs-16 ls-minus-1px fw-600">02</span>
                                </div>
                            </div>
                            <div class="col-sm-3 offset-sm-1">
                                <span class="fw-600 text-dark-gray fs-20 ls-minus-1px">The Personalization Gap at Scale</span>
                            </div>
                            <div class="col-sm-7 p-35px sm-p-25px xs-p-20px xs-pt-10px last-paragraph-no-margin">
                                <p class="w-95 sm-w-100 lh-30 opacity-8">As product catalogs grow and customer bases diversify, manually personalizing marketing messages and product recommendations becomes unfeasible. This leads to generic customer experiences that fail to drive conversions or foster loyalty.</p>
                            </div>
                        </div>
                        <div class="row g-0 align-items-center text-center text-sm-start challenge-item">
                            <div class="col-12">
                                <div class="separator-line-1px w-100 bg-medium-gray opacity-2 bg-sliding-line"></div>
                            </div>
                            <div class="col-sm-1 text-center xs-mt-20px">
                                <div class="challenge-number">
                                    <span class="base-color-gradient fs-16 ls-minus-1px fw-600">03</span>
                                </div>
                            </div>
                            <div class="col-sm-3 offset-sm-1">
                                <span class="fw-600 text-dark-gray fs-20 ls-minus-1px">Suboptimal Customer Retention & LTV</span>
                            </div>
                            <div class="col-sm-7 p-35px sm-p-25px xs-p-20px xs-pt-10px last-paragraph-no-margin">
                                <p class="w-95 sm-w-100 lh-30 opacity-8">Acquiring new customers is often more expensive than retaining existing ones. Without a robust strategy to encourage repeat purchases and build loyalty, e-commerce businesses leave significant CLV on the table, impacting long-term profitability.</p>
                            </div>
                            <div class="col-12">
                                <div class="separator-line-1px w-100 bg-medium-gray opacity-2 bg-sliding-line"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <style>
                    .challenge-item {
                    transition: all 0.3s ease;
                    }
                    .challenge-item:hover {
                    background-color: rgba(248, 248, 252, 0.5);
                    }
                    .challenge-number {
                    position: relative;
                    display: inline-block;
                    }

                    .challenge-item:hover .challenge-number:after {
                    width: 40px;
                    }
                </style>
            </div>
        </section>
        <!-- end section - The Challenge -->

        <!-- end section -->
        <!-- start section - The Plot -->
        <section class="pt-130px pb-130px lg-pt-90px lg-pb-90px md-pt-75px md-pb-75px position-relative overflow-hidden combined-gradient-text" style="background-color: #1f2136;">
<!-- Refined professional gradient background -->
<!-- Refined professional gradient background -->
 <!-- Modern dark base gradient -->
<!-- Layer 1: Dark professional base (purplish indigo) -->
  <div class="position-absolute" style="
    inset: 0;
    background: linear-gradient(135deg, #0A0913 0%, #1A142A 40%, #271E3D 75%, #341F53 100%);
    z-index: 0;
    pointer-events: none;
  "></div>

  <!-- Layer 2: Top-right soft purple glow -->
  <div class="position-absolute" style="
    inset: 0;
    background: radial-gradient(circle at 85% 20%, rgba(190, 130, 255, 0.18) 0%, transparent 60%);
    z-index: 1;
    pointer-events: none;
  "></div>

  <!-- Layer 3: Bottom-left pink accent glow -->
  <div class="position-absolute" style="
    inset: 0;
    background: radial-gradient(circle at 15% 85%, rgba(255, 105, 180, 0.15) 0%, transparent 60%);
    z-index: 2;
    pointer-events: none;
  "></div>

  <!-- Layer 4: Subtle mesh texture (light purple tone) -->
  <div class="position-absolute" style="
    inset: 0;
    background:
      linear-gradient(90deg, rgba(255, 160, 255, 0.03) 1px, transparent 1px),
      linear-gradient(0deg, rgba(255, 160, 255, 0.03) 1px, transparent 1px);
    background-size: 28px 28px;
    opacity: 0.25;
    z-index: 3;
    pointer-events: none;
  "></div>


            <div class="container">

				 <div class="row justify-content-center">
                    <div class="col-12 col-xl-6 col-lg-8 col-md-10 text-center mb-5 position-relative">
                        <div class="mb-10px">
                           <!--  <span class="w-25px h-1px d-inline-block bg-base-color me-5px align-middle"></span> -->
                            <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">ADZETA'S SOLUTION</span>
                        </div>
                        <h2 class="alt-font fw-600 text-white fs-40 lg-fs-32 md-fs-30 sm-fs-28 ls-minus-1px mb-20px">The Strategic Approach: How AdZeta Delivered for LumiNova Home</h2>
                    </div>
                </div>
                <div class="row align-items-center">
                    <!-- left content -->
                    <div class="col-12 col-lg-6 md-margin-50px-bottom">
                        <div class="animated-card mb-5">
                            <ul class="numbered-list">
                                <li>
                                    <span class="text-white fw-600 fs-18">Predictive Cart Abandonment Recovery</span>
                                    <p class="w-95 last-paragraph-no-margin text-white-transparent">AdZeta's AI analyzed real-time user behavior (time on page, items in cart, navigation patterns) to predict the likelihood of cart abandonment. For high-intent abandoners, personalized exit-intent pop-ups with tailored incentives or automated email reminders with dynamic product recommendations were triggered, significantly improving recovery rates.</p>
                                </li>
                                <li>
                                    <span class="text-white fw-600 fs-18">AI-Driven Customer Segmentation & NBO Engine</span>
                                    <p class="w-95 last-paragraph-no-margin text-white-transparent">Leveraging machine learning, AdZeta segmented LumiNova Home's customer base based on purchase history, browsing behavior, and engagement levels. This enabled the delivery of highly relevant Next Best Offers (NBOs) through personalized email campaigns and on-site product recommendations, encouraging upsells, cross-sells, and repeat purchases.</p>
                                </li>
                                <li>
                                    <span class="text-white fw-600 fs-18">Dynamic Content Personalization Across Channels</span>
                                    <p class="w-95 last-paragraph-no-margin text-white-transparent">AdZeta's platform facilitated the dynamic insertion of personalized content (product recommendations, special offers, messaging) within email templates and on website pages. This ensured that each customer received a unique and relevant experience, significantly boosting engagement and conversion rates.</p>
                                </li>
                                <li>
                                    <span class="text-white fw-600 fs-18">Continuous A/B Testing & Optimization</span>
                                    <p class="w-95 last-paragraph-no-margin text-white-transparent">The system continuously A/B tested different personalization strategies, messaging, and offer types. This data-driven approach allowed for ongoing optimization of campaigns to maximize cart recovery, repeat purchases, and overall CLV for LumiNova Home.</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- right content -->
                    <div class="col-12 col-lg-6">
                        <div class="outside-box-right position-relative text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 419 489" fill="none">
								<rect x="0.5" y="132.5" width="191" height="192" rx="13.5" fill="#FFF9F0" stroke="#A89F9F"/>
								<rect x="230.5" y="132.5" width="188" height="192" rx="13.5" fill="#FFF9F0" stroke="#A89F9F"/>

								 <!-- Left box text -->
								<text x="30" y="170" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#004050" text-anchor="start">Control Group</text>
								<text x="30" y="195" font-family="Inter, sans-serif" font-size="12" fill="#666666" text-anchor="start">Optimizing towards</text>
								<text x="30" y="215" font-family="Inter, sans-serif" font-size="12" fill="#666666" text-anchor="start">Business-as-usual</text>
								<text x="30" y="235" font-family="Inter, sans-serif" font-size="12" fill="#666666" text-anchor="start">event: Cart Addition</text>
								<text x="30" y="260" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#004050" text-anchor="start">CPA</text>

								    <!-- Right box text -->
									<text x="260" y="170" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#004050" text-anchor="start">Experiment Group</text>
									<text x="260" y="195" font-family="Inter, sans-serif" font-size="12" fill="#666666" text-anchor="start">Optimizing towards</text>
									<text x="260" y="215" font-family="Inter, sans-serif" font-size="12" fill="#666666" text-anchor="start">tROAS with</text>
									<text x="260" y="235" font-family="Inter, sans-serif" font-size="12" fill="#666666" text-anchor="start">predictions powered</text>
									<text x="260" y="255" font-family="Inter, sans-serif" font-size="12" fill="#666666" text-anchor="start">by AdZeta:</text>
									<text x="260" y="275" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#D8436E" text-anchor="start">Customer CLV &</text>
									<text x="260" y="295" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#D8436E" text-anchor="start">Personalization</text>

									<rect x="93" y="0" width="232" height="54" rx="14" fill="#FFF9F0"/>
								<!-- A/B Test title -->
								 <text x="210" y="35" font-family="Inter, sans-serif" font-size="16" font-weight="600" fill="#004050" text-anchor="middle">A/B Test</text>




								<path d="M210 54V70.4989C210 89.2766 225.223 104.499 244 104.499H294.999C310.74 104.499 323.5 117.259 323.5 133V133" stroke="#A89F9F"/>
								<path d="M210 388V387.501C210 368.723 225.223 353.501 244 353.501H294.999C310.74 353.501 323.5 340.741 323.5 325V325" stroke="#A89F9F"/>
								<path d="M210 385V390.499C210 405.963 197.464 418.499 182 418.499H169.501C157.074 418.499 147 428.573 147 441V441" stroke="#A89F9F"/>
								<path d="M210 385V390.499C210 405.963 222.536 418.499 238 418.499H250.499C262.926 418.499 273 428.573 273 441V441" stroke="#A89F9F"/>
								<path d="M210 54V70.4989C210 89.2766 194.778 104.499 176 104.499H124.501C108.76 104.499 96 117.259 96 133V133" stroke="#A89F9F"/>
								<path d="M210 388.5V387.501C210 368.723 194.778 353.501 176 353.501H124.501C108.76 353.501 96 340.741 96 325V325" stroke="#A89F9F"/>
								<rect x="119" y="489" width="60" height="60" rx="18" transform="rotate(-90 119 489)" fill="#FFF9F0"/>
								<path d="M160.604 456.665H159.644V456.616H148.92V461.382H155.654C154.672 464.157 152.032 466.148 148.92 466.148C144.972 466.148 141.77 462.947 141.77 458.999C141.77 455.051 144.972 451.85 148.92 451.85C150.742 451.85 152.401 452.538 153.663 453.661L157.034 450.29C154.905 448.307 152.059 447.084 148.92 447.084C142.339 447.084 137.004 452.419 137.004 458.999C137.004 465.579 142.339 470.914 148.92 470.914C155.501 470.914 160.836 465.579 160.836 458.999C160.836 458.2 160.754 457.42 160.604 456.665Z" fill="#FFC107"/>
								<path d="M138.379 453.453L142.294 456.324C143.353 453.702 145.919 451.85 148.921 451.85C150.743 451.85 152.402 452.538 153.664 453.661L157.034 450.29C154.906 448.307 152.06 447.084 148.921 447.084C144.344 447.084 140.375 449.668 138.379 453.453Z" fill="#E64A19"/>
								<path d="M148.921 470.917C151.998 470.917 154.795 469.739 156.91 467.824L153.222 464.703C152.025 465.609 150.538 466.151 148.921 466.151C145.821 466.151 143.19 464.175 142.198 461.417L138.312 464.411C140.285 468.269 144.29 470.917 148.921 470.917Z" fill="#388E3C"/>
								<path d="M160.606 456.666H159.646V456.616H148.922V461.382H155.656C155.184 462.715 154.327 463.864 153.221 464.701L153.223 464.7L156.911 467.821C156.65 468.058 160.838 464.957 160.838 458.999C160.838 458.2 160.756 457.42 160.606 456.666Z" fill="#1976D2"/>
								<rect x="240" y="429" width="60" height="60" rx="18" fill="#FFF9F0"/>
								<path d="M256.623 461.893C257.462 461.924 258.093 462.624 258.295 463.439C258.395 463.839 258.531 464.168 258.685 464.418C259.1 465.092 259.718 465.377 260.349 465.377C261.162 465.377 261.906 465.175 263.34 463.184C264.489 461.588 265.842 459.348 266.753 457.944L268.295 455.565C269.367 453.912 270.607 452.076 272.029 450.831C273.19 449.815 274.441 449.25 275.702 449.25C277.817 449.25 279.833 450.481 281.375 452.789C283.063 455.318 283.882 458.502 283.882 461.789C283.882 463.742 283.499 465.178 282.846 466.312C282.391 467.103 281.626 467.893 280.459 468.272C279.638 468.539 278.919 467.804 278.919 466.941C278.919 466.077 279.737 465.45 280.336 464.828C280.946 464.195 281.131 463.084 281.131 461.875C281.131 459.211 280.512 456.256 279.15 454.144C278.183 452.646 276.93 451.73 275.551 451.73C274.06 451.73 272.86 452.86 271.511 454.873C270.794 455.942 270.058 457.246 269.232 458.717L268.322 460.335C266.495 463.588 266.032 464.329 265.118 465.551C263.517 467.692 262.149 468.504 260.349 468.504C258.213 468.504 256.862 467.576 256.026 466.176C255.568 465.412 255.266 464.487 255.117 463.447C254.992 462.573 255.741 461.862 256.623 461.893Z" fill="#0081FB"/>
								<path d="M258.862 453.834C258.107 453.384 257.867 452.386 258.449 451.727C259.779 450.222 261.465 449.25 263.341 449.25C264.711 449.25 266.074 449.657 267.497 450.824C269.053 452.099 270.712 454.199 272.781 457.66L273.523 458.902C275.315 461.898 276.334 463.439 276.93 464.166C277.697 465.099 278.234 465.378 278.932 465.378C280.263 465.378 280.843 464.455 281.049 463.204C281.17 462.471 281.734 461.857 282.477 461.833C283.253 461.809 283.907 462.434 283.821 463.206C283.679 464.466 283.343 465.472 282.859 466.312C282.229 467.409 281 468.505 278.932 468.505C277.647 468.505 276.508 468.224 275.249 467.032C274.282 466.116 273.15 464.49 272.279 463.029L269.689 458.686C268.39 456.506 267.198 454.881 266.508 454.145C265.766 453.354 264.812 452.398 263.29 452.398C262.467 452.398 261.726 452.786 261.064 453.44C260.474 454.023 259.575 454.259 258.862 453.834Z" fill="url(#paint0_linear_59_845)"/>
								<path d="M263.301 450.849C263.288 451.709 262.543 452.384 261.811 452.834C261.189 453.216 260.628 453.828 260.123 454.594C258.885 456.47 258.127 459.264 258.127 461.948C258.127 462.32 258.155 462.663 258.203 462.974C258.334 463.818 258.12 464.793 257.407 465.264C256.654 465.761 255.629 465.525 255.376 464.659C255.13 463.816 255.008 462.864 255.008 461.836C255.008 458.736 255.855 455.505 257.467 453.01C258.569 451.305 260.047 449.995 261.75 449.484C262.592 449.231 263.315 449.97 263.301 450.849Z" fill="url(#paint1_linear_59_845)"/>
								<defs>
								<linearGradient id="paint0_linear_59_845" x1="261.281" y1="458.378" x2="281.162" y2="459.379" gradientUnits="userSpaceOnUse">
								<stop stop-color="#0064E1"/>
								<stop offset="0.4" stop-color="#0064E1"/>
								<stop offset="0.83" stop-color="#0073EE"/>
								<stop offset="1" stop-color="#0082FB"/>
								</linearGradient>
								<linearGradient id="paint1_linear_59_845" x1="259.167" y1="463.262" x2="259.167" y2="455.903" gradientUnits="userSpaceOnUse">
								<stop stop-color="#0082FB"/>
								<stop offset="1" stop-color="#0064E0"/>
								</linearGradient>
								</defs>
								</svg>

                        </div>
                    </div>
                </div>
            </div>
            <!-- Background decorations -->

        </section>
        <!-- end section - The Plot -->

        <!-- start section - The Results -->


  <!-- start section -->
       <section class="pb-5 md-pb-4 sm-pb-3" style="background-color:#f7f5f3;">

            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-6 md-mb-50px order-2 order-md-2 order-lg-1">
                        <div class="row">
                            <div class="col-sm-6 mt-30px xs-mt-0" data-bottom-top="transform: translateY(50px)" data-top-bottom="transform: translateY(-50px)" data-anime='{ "el": "childs", "translateY": [0, 0], "perspective": [1200,1200], "scale": [1.1, 1], "rotateX": [50, 0], "opacity": [0,0.7], "duration": 800, "delay": 200, "staggervalue": 300, "easing": "easeOutQuad" }'>
                                <!-- start features box item -->
                                <div class="bg-white border-radius-6px overflow-hidden box-shadow-double-large icon-with-text-style-05 transition-inner-all mb-30px">
                                    <div class="feature-box hover-box dark-hover last-paragraph-no-margin">
                                        <div class="content-slide-up content-scale pt-17 pb-17 ps-10 pe-10 sm-p-15">
										 <div class="feature-box-icon">
                                                <i class="line-icon-Money-Bag icon-extra-large mb-20px justify-content-center text-base-color" ></i>
                                            </div>
                                           <div class="feature-box-content">
                                                <h2 class="alt-font font-weight-600 letter-spacing-minus-1px margin-15px-bottom" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                                                    <span class="counter" data-speed="2000" data-to="28">28</span><span>%</span>
                                                </h2>
                                                <span class="d-inline-block alt-font text-dark-gray fs-20 md-fs-18 sm-fs-16 fw-600">Reduction in Cart Abandonment Rate</span>
                                                <p class="lh-28 md-lh-24 sm-lh-22 text-visible text-white w-80 mx-auto sm-w-100">Through predictive intervention strategies</p>
                                            </div>
                                           <div class="feature-box-overlay" style="background: linear-gradient(to right, #e958a1, #ff5d74);width:100%;height:100%;"></div>
                                        </div>
                                    </div>
                                </div>
                                <!-- start features box item -->
                                <!-- end features box item -->
                                <div class="bg-white border-radius-6px overflow-hidden box-shadow-double-large icon-with-text-style-05 transition-inner-all mb-30px">
                                    <div class="feature-box hover-box dark-hover last-paragraph-no-margin">
                                        <div class="content-slide-up content-scale pt-17 pb-17 ps-10 pe-10 sm-p-15">
                                            <div class="feature-box-icon">
                                                <i class="line-icon-Crown icon-extra-large mb-20px justify-content-center text-base-color"></i>
                                            </div>
                                             <div class="feature-box-content">
                                                <h2 class="alt-font font-weight-600 letter-spacing-minus-1px margin-15px-bottom" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                                                    <span class="counter" data-speed="2000" data-to="45">45</span><span>%</span>
                                                </h2>
                                                <span class="d-inline-block alt-font text-dark-gray fs-20 md-fs-18 sm-fs-16 fw-600">Increase in Repeat Purchase Rate</span>
                                                <p class="lh-28 md-lh-24 sm-lh-22 text-visible text-white w-80 mx-auto sm-w-100">Through personalized recommendations</p>
                                            </div>
                                           <div class="feature-box-overlay" style="background: linear-gradient(to right, #e958a1, #ff5d74);"></div>
                                        </div>
                                    </div>
                                </div>
                                <!-- end features box item -->
                            </div>
                            <div class="col-sm-6" data-bottom-top="transform: translateY(-50px)" data-top-bottom="transform: translateY(50px)" data-anime='{ "el": "childs", "translateY": [0, 0], "perspective": [1200,1200], "scale": [1.1, 1], "rotateX": [50, 0], "opacity": [0,0.7], "duration": 800, "delay": 200, "staggervalue": 300, "easing": "easeOutQuad" }'>
                                <!-- start features box item -->
                                <div class="bg-white border-radius-6px overflow-hidden box-shadow-double-large icon-with-text-style-05 transition-inner-all mb-30px">
                                    <div class="feature-box hover-box dark-hover last-paragraph-no-margin">
                                        <div class="content-slide-up content-scale pt-17 pb-17 ps-10 pe-10 sm-p-15">
                                            <div class="feature-box-icon">
                                                <i class="line-icon-Dollar-Sign icon-extra-large mb-20px justify-content-center text-base-color"></i>
                                            </div>
                                            <div class="feature-box-content">
                                                <h2 class="alt-font font-weight-600 letter-spacing-minus-1px margin-15px-bottom" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                                                    <span class="counter" data-speed="2000" data-to="72">72</span><span>%</span>
                                                </h2>
                                                <span class="d-inline-block alt-font text-dark-gray fs-20 md-fs-18 sm-fs-16 fw-600">Increase in Customer Lifetime Value (CLV)</span>
                                                <p class="lh-28 md-lh-24 sm-lh-22 text-visible text-white w-80 mx-auto sm-w-100">Through AI-powered personalization</p>
                                            </div>
                                            <div class="feature-box-overlay" style="background: linear-gradient(to right, #e958a1, #ff5d74);"></div>
                                        </div>
                                    </div>
                                </div>
                                <!-- start features box item -->
                                <!-- end features box item -->
                                <div class="bg-white border-radius-6px overflow-hidden box-shadow-double-large icon-with-text-style-05 transition-inner-all mt-30px">
                                    <div class="feature-box hover-box dark-hover last-paragraph-no-margin">
                                        <div class="content-slide-up content-scale pt-17 pb-17 ps-10 pe-10 sm-p-15">
                                            <div class="feature-box-icon">
                                                <i class="line-icon-Heart icon-extra-large mb-20px justify-content-center text-base-color"></i>
                                            </div>
                                             <div class="feature-box-content">
                                                <h2 class="alt-font font-weight-600 letter-spacing-minus-1px margin-15px-bottom text-base-color" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                                                    <span class="counter" data-speed="2000" data-to="35">35</span><span>%</span>
                                                </h2>
                                                <span class="d-inline-block alt-font text-dark-gray fs-20 md-fs-18 sm-fs-16 fw-600">Uplift in Email Marketing Conversion Rates</span>
                                                <p class="lh-28 md-lh-24 sm-lh-22 text-visible text-white w-80 mx-auto sm-w-100">For personalized campaigns</p>
                                            </div>
                                           <div class="feature-box-overlay" style="background: linear-gradient(to right, #e958a1, #ff5d74);"></div>
                                        </div>
                                    </div>
                                </div>
                                <!-- end features box item -->
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-5 offset-lg-1 text-center text-lg-start order-1 order-md-1 order-lg-2 mb-5" data-anime='{ "el": "childs", "translateX": [50, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 100, "easing": "easeOutQuad" }'>
                       <div class="mb-10px">
                           <!--  <span class="w-25px h-1px d-inline-block bg-base-color me-5px align-middle"></span> -->
                            <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">MEASURABLE IMPACT</span>
                        </div>
						<h2 class="alt-font fw-600 text-dark-gray ls-minus-1px fs-40 lg-fs-36 md-fs-32 sm-fs-28">Enhanced Loyalty, Reduced Churn, Significant CLV Growth</h2>
                        <p class="mb-25px">AdZeta's AI-powered personalization didn't just tweak metrics; it fundamentally improved how LumiNova Home engaged with its customers. By delivering relevant experiences at the right time, they fostered stronger customer relationships, leading to increased loyalty and substantial growth in profitability.</p>
                        <p class="mb-35px">The 72% increase in CLV demonstrates the transformative power of AI-driven personalization in e-commerce.</p>

                    </div>





                </div>
            </div>
        </section>
        <!-- end section -->














        <!-- start section - Client Testimonial -->
        <section style="background-color:#f7f5f3;" class="pt-0 pb-5 mt-n5 md-mt-n4 sm-mt-n3">
            <div class="container">

                <!-- Testimonial -->
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-10">
                        <div class="p-60px lg-p-40px md-p-30px border-radius-6px position-relative text-center">
                            <div class="d-block fs-20 md-fs-18 sm-fs-16 margin-30px-bottom text-dark-gray fw-600 line-height-32px md-lh-28 sm-lh-26">
                                <i class="fa-solid fa-quote-left margin-15px-right opacity-7 align-middle" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                                Before AdZeta, we were struggling to keep customers engaged beyond their first purchase, and our cart abandonment was a constant headache. AdZeta's AI platform has been a game-changer. The ability to predict and intervene in cart abandonment, coupled with truly personalized recommendations, has not only recovered significant revenue but has also built a more loyal customer base. Seeing a 72% jump in CLV has given us the confidence to invest more strategically in our growth.
                                <i class="fa-solid fa-quote-right margin-15px-left opacity-7 align-middle" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                            </div>
                            <div class="text-center mt-5">
                                <div class="d-inline-block mb-3">
                                    <img src="https://adzeta.io/images/case-studies/Sarah-Chen.jpg" alt="Client" class="w-80px h-80px rounded-circle border border-3 border-color-white box-shadow-small" data-no-retina="">
                                </div>
                                <div class="text-center">
                                    <span class="alt-font d-block fw-700 text-dark-gray fs-20 md-fs-18 sm-fs-16">Sarah Chen</span>
                                    <span class="d-block text-small md-fs-13 sm-fs-12 text-medium-grey">Founder & CEO, LumiNova Home</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Background decorations -->
        </section>
        <!-- end section - Client Testimonial -->

        <!-- start section - Call to Action -->

		<section class="big-section pt-0 pb-90px lg-pb-90px md-pb-75px cover-background position-relative background-position-center-bottom" style="background-color:#f7f5f3;">
            <div class="container px-4 px-sm-4">
                <div class="bg-white box-shadow-quadruple-large border-radius-6px p-40px lg-p-30px md-p-25px border-radius-20px">
                <div class="row justify-content-center">
                    <div class="col-12 col-xl-7 col-lg-8 col-md-8 text-center margin-5-rem-bottom md-margin-3-rem-bottom ">

							<h2 class="alt-font fw-600 text-dark-gray margin-20px-bottom fs-40 lg-fs-32 md-fs-30 sm-fs-28 ls-minus-1px">Ready to Build Similar Growth for Your E-commerce Business?</h2>
							<p class="w-80 mx-auto md-w-100 md-fs-15 sm-fs-14">If you're looking to move beyond generic marketing tactics and build a truly profitable, personalized customer experience that drives CLV growth, AdZeta's AI-powered personalization engine can help.</p>

                    </div>
                </div>
                <!-- CTA Buttons -->
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-10 text-center">
                        <a href="free-ad-audit.php" class="btn btn-large btn-gradient-pink-orange btn-round-edge margin-15px-right sm-margin-15px-bottom">Lets Talk <span style="margin-left:10px;"><svg width="12" height="12" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.36406 10.3334L0.664062 9.63338L8.96406 1.33337H4.06406V0.333374H10.6641V6.93337H9.66406V2.03337L1.36406 10.3334Z" fill="#fff"/>
</svg></span></a>
                    </div>
                </div>
                </div>
            </div>
			 <style>
            /* Fix for overlay not covering the entire box */
            .feature-box-overlay {
                width: 100% !important;
                height: 100% !important;
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                border-radius: 6px;
                z-index: 1;
            }

            /* Make the first box always have the pink background */
            .project-metrics .col-sm-6:first-child .feature-box-overlay {
                opacity: 1 !important;
                background: linear-gradient(to right, #e958a1, #ff5d74) !important;
            }

            /* Make text in first box white */
            .project-metrics .col-sm-6:first-child .feature-box-content h2,
            .project-metrics .col-sm-6:first-child .feature-box-content span,
            .project-metrics .col-sm-6:first-child .feature-box-content p,
            .project-metrics .col-sm-6:first-child .feature-box-icon i {
                color: white !important;
                -webkit-text-fill-color: white !important;
            }

            /* Semi-transparent white text for dark backgrounds */
            .text-white-transparent {
                color: rgba(255, 255, 255, 0.8) !important;
            }

            /* Animated card with gradient border - updated colors to match screenshot */
            .animated-card {
                --border-width: 1px;
                --radius: 20px;
                --angle: 0turn;
                --bg-color: #1E1A33; /* Darker purple background to match screenshot */

                position: relative;
                border-radius: var(--radius);
                border: var(--border-width) solid transparent;
                background-color: var(--bg-color);
                isolation: isolate;
                padding: 40px;
            }

            .animated-card::before {
                content: " ";
                position: absolute;
                inset: calc(var(--border-width) * -1);
                z-index: -1;
                border: inherit;
                border-radius: inherit;
                background-image: conic-gradient(from var(--angle), #2D1E4A 80%, #e958a1 88%, #e958a1 92%, #2D1E4A 100%);
                background-origin: border-box;
                -webkit-mask: linear-gradient(black, black) content-box,
                              linear-gradient(black, black);
                mask: linear-gradient(black, black) content-box,
                      linear-gradient(black, black);
                -webkit-mask-composite: xor;
                mask-composite: exclude;
                animation: rotate-gradient 4s linear infinite;
            }

            @property --angle {
                syntax: "<angle>";
                inherits: true;
                initial-value: 0turn;
            }

            @keyframes rotate-gradient {
                to {
                    --angle: 1turn;
                }
            }

            /* Fallback for browsers that don't support @property */
            @supports not (background: conic-gradient(from var(--angle), #3a1e5e 80%, #e958a1 88%)) {
                .animated-card::before {
                    background-image: conic-gradient(#3a1e5e 80%, #e958a1 88%, #e958a1 92%, #3a1e5e 100%);
                    animation: none;
                }
            }

            /* Numbered list styling - updated to match screenshot */
            .numbered-list {
                counter-reset: item;
                list-style-type: none;
                padding-left: 0;
            }

            .numbered-list li {
                position: relative;
                padding-left: 40px;
                margin-bottom: 30px;
                counter-increment: item;
            }

            .numbered-list li::before {
                content: "0" counter(item);
                position: absolute;
                left: 0;
                top: 0;
                color: #e958a1;
                font-weight: 600;
                font-size: 14px;
                opacity: 0.9;
            }
        </style>
        </section>
        <!-- end section - Call to Action -->
		  <script>
        /**
         * Feature Box Controller - Waits for jQuery to be loaded
         * Uses same smooth scroll approach as main.js (line 2883-2885)
         */

        // Wait for jQuery to be available
        function initFeatureBoxController() {
            if (typeof jQuery === 'undefined') {
                // jQuery not loaded yet, wait and try again
                setTimeout(initFeatureBoxController, 100);
                return;
            }

            // jQuery is available, initialize controller
            (function($) {
                'use strict';

            // Simplified configuration for smooth operation
            var CONFIG = {
                MOBILE_BREAKPOINT: 768,
                CENTER_TOLERANCE: 120,  // More lenient for less jitter
                CLICK_DEBOUNCE: 300,
                SCROLL_SPEED: 600,      // Faster for smoother feel
                SCROLL_THROTTLE: 150    // Throttle scroll events
            };

            // Minimal state management
            var state = {
                activeIndex: -1,
                featureBoxes: null,
                initialized: false,
                lastClickTime: 0,
                scrollTimeout: null
            };

            // Utility functions following theme patterns
            var utils = {
                isMobile: function() {
                    return window.innerWidth <= CONFIG.MOBILE_BREAKPOINT;
                },
                getViewportCenter: function() {
                    return $(window).height() * 0.5;
                },
                getElementCenter: function(element) {
                    var $el = $(element);
                    var offset = $el.offset();
                    return offset.top + ($el.outerHeight() / 2) - $(window).scrollTop();
                },
                isElementVisible: function(element) {
                    var $el = $(element);
                    var elementTop = $el.offset().top;
                    var elementBottom = elementTop + $el.outerHeight();
                    var viewportTop = $(window).scrollTop();
                    var viewportBottom = viewportTop + $(window).height();
                    return elementBottom > viewportTop && elementTop < viewportBottom;
                },
                // Get header height like main.js getHeaderHeight() function
                getHeaderHeight: function() {
                    var headerHeight = 0;
                    if ($('.header-top-bar').length) {
                        headerHeight = headerHeight + $('.header-top-bar').outerHeight();
                    }
                    if ($('header nav.navbar').length) {
                        headerHeight = headerHeight + $('header nav.navbar').outerHeight();
                    }
                    return headerHeight;
                }
            };

            // Core functionality following theme patterns
            var controller = {
                init: function() {
                    if (state.initialized) return;

                    try {
                        this.cacheElements();
                        if (!state.featureBoxes || state.featureBoxes.length === 0) {
                            console.info('No feature boxes found');
                            return;
                        }
                        this.bindEvents();
                        this.initialCheck();
                        state.initialized = true;
                        console.log('Feature box controller initialized with', state.featureBoxes.length, 'boxes');
                    } catch (error) {
                        console.error('Initialization failed:', error);
                    }
                },

                cacheElements: function() {
                    var $resultsSection = $('.col-lg-6.md-mb-50px.order-2.order-md-2.order-lg-1');
                    if ($resultsSection.length === 0) {
                        state.featureBoxes = [];
                        return;
                    }
                    state.featureBoxes = $resultsSection.find('.feature-box.hover-box.dark-hover').toArray();
                },

                activateBox: function(index) {
                    // Only activate on mobile to preserve desktop hover
                    if (!utils.isMobile()) return;
                    if (state.activeIndex === index) return;

                    try {
                        var self = this;
                        $(state.featureBoxes).each(function(i) {
                            var $box = $(this);
                            var $overlay = $box.find('.feature-box-overlay');
                            var $content = $box.find('.content-slide-up');
                            var isActive = (i === index);

                            $box.toggleClass('mobile-scroll-active', isActive);

                            if ($overlay.length) {
                                $overlay.css({
                                    'opacity': isActive ? '1' : '0',
                                    'transform': isActive ? 'translateY(0)' : 'translateY(100%)'
                                });
                            }

                            if ($content.length) {
                                $content.css('transform', 'translateY(0)');
                            }
                        });

                        state.activeIndex = index;
                        console.log('Activated box:', index);
                    } catch (error) {
                        console.error('Box activation failed:', error);
                    }
                },

                checkCenterAlignment: function() {
                    // Only run on mobile to avoid interfering with desktop hover
                    if (!utils.isMobile()) return;

                    try {
                        var viewportCenter = utils.getViewportCenter();
                        var closestIndex = -1;
                        var minDistance = Infinity;

                        $(state.featureBoxes).each(function(index) {
                            if (!utils.isElementVisible(this)) return;

                            var boxCenter = utils.getElementCenter(this);
                            var distance = Math.abs(boxCenter - viewportCenter);

                            if (distance < minDistance) {
                                minDistance = distance;
                                closestIndex = index;
                            }
                        });

                        // Only activate if significantly closer to center
                        if (closestIndex !== -1 && minDistance < CONFIG.CENTER_TOLERANCE) {
                            if (state.activeIndex !== closestIndex) {
                                controller.activateBox(closestIndex);
                            }
                        } else if (minDistance > CONFIG.CENTER_TOLERANCE * 2) {
                            if (state.activeIndex !== -1) {
                                controller.activateBox(-1);
                            }
                        }
                    } catch (error) {
                        console.error('Center alignment check failed:', error);
                    }
                },

                handleScroll: function() {
                    // Only handle scroll on mobile
                    if (!utils.isMobile()) return;

                    // Clear existing timeout
                    if (state.scrollTimeout) {
                        clearTimeout(state.scrollTimeout);
                    }

                    // Throttle scroll events to reduce jitter
                    state.scrollTimeout = setTimeout(function() {
                        controller.checkCenterAlignment();
                    }, CONFIG.SCROLL_THROTTLE);
                },

                handleClick: function(event) {
                    var currentTime = Date.now();
                    if (currentTime - state.lastClickTime < CONFIG.CLICK_DEBOUNCE) {
                        event.preventDefault();
                        return;
                    }
                    state.lastClickTime = currentTime;

                    try {
                        var $clickedBox = $(event.currentTarget);
                        var index = state.featureBoxes.indexOf(event.currentTarget);

                        if (index === -1) return;

                        event.preventDefault();
                        event.stopPropagation();

                        console.log('Feature box clicked:', index);

                        // Stop any scroll event handling during click scroll
                        if (state.scrollTimeout) {
                            clearTimeout(state.scrollTimeout);
                        }

                        // Activate immediately
                        this.activateBox(index);

                        // Simplified scroll calculation
                        var boxOffset = $clickedBox.offset().top;
                        var boxHeight = $clickedBox.outerHeight();
                        var viewportCenter = $(window).height() / 2;
                        var boxCenter = boxOffset + (boxHeight / 2);
                        var targetScroll = boxCenter - viewportCenter;

                        // Use smooth scroll with easing
                        $('html, body').stop().animate({
                            scrollTop: Math.max(0, targetScroll)
                        }, {
                            duration: CONFIG.SCROLL_SPEED,
                            easing: 'swing',
                            complete: function() {
                                console.log('Scroll completed');
                                // Brief delay before re-enabling scroll detection
                                setTimeout(function() {
                                    controller.checkCenterAlignment();
                                }, 200);
                            }
                        });

                    } catch (error) {
                        console.error('Click handler failed:', error);
                    }
                },

                resetMobileEffects: function() {
                    try {
                        // Stop any jQuery animations like theme's pattern
                        $('html, body').stop();

                        // Clear any pending scroll timeouts
                        if (state.scrollTimeout) {
                            clearTimeout(state.scrollTimeout);
                        }

                        // Reset visual effects only (don't interfere with desktop hover)
                        $(state.featureBoxes).each(function() {
                            var $box = $(this);
                            $box.removeClass('mobile-scroll-active');

                            var $overlay = $box.find('.feature-box-overlay');
                            var $content = $box.find('.content-slide-up');

                            // Only reset if we're switching to desktop
                            if (!utils.isMobile()) {
                                $overlay.css({
                                    'opacity': '',
                                    'transform': ''
                                });

                                $content.css('transform', '');
                            }
                        });

                        state.activeIndex = -1;
                    } catch (error) {
                        console.error('Reset mobile effects failed:', error);
                    }
                },

                bindEvents: function() {
                    console.log('Binding events to', state.featureBoxes.length, 'feature boxes');

                    // Scroll event only for mobile
                    $(window).on('scroll.featureBoxes', function() {
                        controller.handleScroll();
                    });

                    // Click events for feature boxes
                    $(state.featureBoxes).each(function(index, box) {
                        var $box = $(box);

                        // Add mobile-specific attributes
                        $box.attr('data-box-index', index);

                        // Click events for mobile scroll-to-center
                        $box.on('click.featureBoxes', function(e) {
                            // Only handle clicks on mobile
                            if (utils.isMobile()) {
                                controller.handleClick(e);
                            }
                        });

                        // DO NOT add hover events here - let the original CSS hover work
                        // The theme's existing hover effects will work naturally on desktop
                    });

                    // Resize event to switch between mobile/desktop modes
                    $(window).on('resize.featureBoxes', function() {
                        if (state.scrollTimeout) {
                            clearTimeout(state.scrollTimeout);
                        }

                        if (!utils.isMobile()) {
                            // Switching to desktop - reset mobile effects
                            controller.resetMobileEffects();
                        } else {
                            // Switching to mobile - start center alignment
                            state.scrollTimeout = setTimeout(function() {
                                controller.checkCenterAlignment();
                            }, 300);
                        }
                    });
                },

                initialCheck: function() {
                    setTimeout(function() {
                        controller.checkCenterAlignment();
                    }, 250);
                }
            };

                // Initialize when document is ready - following theme pattern
                $(document).ready(function() {
                    controller.init();
                });

            })(jQuery);
        }

        // Start the initialization process
        initFeatureBoxController();
        </script>

        <style>
        /* Mobile-only feature box interactions - don't interfere with desktop hover */
        @media (max-width: 768px) {
            .feature-box.hover-box.dark-hover {
                cursor: pointer !important;
                user-select: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                position: relative;
                z-index: 1;
            }

            /* Force natural heights on mobile to prevent equal-height script issues */
            .hover-box.feature-box {
                height: auto !important;
                min-height: auto !important;
            }

            /* Ensure row-equal-height doesn't force heights on mobile */
            .row-equal-height .hover-box.feature-box {
                height: auto !important;
                min-height: auto !important;
            }
        }

        /* Mobile scroll-triggered sequential hover effects */
        @media (max-width: 768px) {
            .feature-box.hover-box.dark-hover {
                touch-action: manipulation;
                position: relative;
                overflow: hidden;
            }

            .feature-box.hover-box.dark-hover .feature-box-overlay {
                position: absolute;
                top: 0; left: 0; right: 0; bottom: 0;
                width: 100% !important;
                height: 100% !important;
                opacity: 0;
                transform: translateY(100%);
                transition: opacity 0.25s ease, transform 0.25s ease;
                z-index: 1;
            }

            .feature-box.hover-box.dark-hover .content-slide-up {
                position: relative;
                z-index: 2;
                transition: transform 0.25s ease;
            }

            /* Active state - immediate activation */
            .feature-box.hover-box.dark-hover.mobile-scroll-active .feature-box-overlay {
                opacity: 1 !important;
                transform: translateY(0) !important;
                background: linear-gradient(to right, #e958a1, #ff5d74) !important;
            }

            .feature-box.hover-box.dark-hover.mobile-scroll-active .content-slide-up {
                transform: translateY(0) !important;
            }

            /* Make ALL elements white when active */
            .feature-box.hover-box.dark-hover.mobile-scroll-active .counter {
                color: white !important;
                -webkit-text-fill-color: white !important;
                background: none !important;
            }

            .feature-box.hover-box.dark-hover.mobile-scroll-active h2 {
                color: white !important;
                -webkit-text-fill-color: white !important;
                background: none !important;
            }

            /* Paragraph text - force visibility */
            .feature-box.hover-box.dark-hover.mobile-scroll-active p {
                color: white !important;
                opacity: 1 !important;
                visibility: visible !important;
                display: block !important;
            }

            .feature-box.hover-box.dark-hover.mobile-scroll-active .text-visible {
                color: white !important;
                opacity: 1 !important;
                visibility: visible !important;
            }

            .feature-box.hover-box.dark-hover.mobile-scroll-active .text-white {
                color: white !important;
                opacity: 1 !important;
            }

            .feature-box.hover-box.dark-hover.mobile-scroll-active .text-dark-gray,
            .feature-box.hover-box.dark-hover.mobile-scroll-active span {
                color: white !important;
            }

            .feature-box.hover-box.dark-hover.mobile-scroll-active .feature-box-icon i {
                color: white !important;
            }

            .feature-box.hover-box.dark-hover.mobile-scroll-active .text-base-color {
                color: white !important;
            }

            /* Override any light opacity classes */
            .feature-box.hover-box.dark-hover.mobile-scroll-active .text-light-opacity {
                color: white !important;
                opacity: 1 !important;
            }

            /* Ensure z-index for proper layering */
            .feature-box.hover-box.dark-hover.mobile-scroll-active .feature-box-content,
            .feature-box.hover-box.dark-hover.mobile-scroll-active .feature-box-icon {
                position: relative;
                z-index: 10;
            }

            /* Smooth transitions for all text elements */
            .feature-box.hover-box.dark-hover .counter,
            .feature-box.hover-box.dark-hover h2,
            .feature-box.hover-box.dark-hover span,
            .feature-box.hover-box.dark-hover p,
            .feature-box.hover-box.dark-hover i {
                transition: color 0.5s ease, -webkit-text-fill-color 0.5s ease;
            }
        }
        </style>
        <!-- start enhanced footer -->
		<?php include 'footer.php'; ?>