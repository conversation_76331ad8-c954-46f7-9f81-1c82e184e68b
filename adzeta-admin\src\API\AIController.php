<?php

namespace AdZetaAdmin\API;

use AdZetaAdmin\Services\GeminiAIService;

/**
 * AI API Controller
 * Handles all AI-related API endpoints
 */
class AIController extends BaseController
{
    private $aiService;

    public function __construct()
    {
        parent::__construct();
        $this->aiService = new GeminiAIService($this->db);
    }

    /**
     * Test AI connection
     */
    public function testConnection()
    {
        $this->requireAuth();

        try {
            $result = $this->aiService->testConnection();

            if ($result['success']) {
                return $this->success([
                    'message' => $result['message'],
                    'api_keys_count' => $result['api_keys_count']
                ]);
            } else {
                return $this->error($result['message'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Connection test failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate content
     */
    public function generateContent()
    {
        $this->requireAuth();

        $input = $this->getRequestData();

        if (empty($input['prompt'])) {
            return $this->error('Prompt is required', 400);
        }

        $options = [
            'temperature' => $input['temperature'] ?? 0.7,
            'maxOutputTokens' => $input['maxOutputTokens'] ?? 2048,
            'topP' => $input['topP'] ?? 0.8,
            'topK' => $input['topK'] ?? 40
        ];

        try {
            $result = $this->aiService->generateContent($input['prompt'], $options);

            if ($result['success']) {
                return $this->success([
                    'content' => $result['content'],
                    'usage' => $result['usage'] ?? null
                ]);
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Content generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate title suggestions
     */
    public function generateTitles()
    {
        $this->requireAuth();

        $input = $this->getRequestData();

        if (empty($input['topic'])) {
            return $this->error('Topic is required', 400);
        }

        $keywords = $input['keywords'] ?? [];
        $count = $input['count'] ?? 5;

        try {
            $result = $this->aiService->generateTitleSuggestions($input['topic'], $keywords, $count);

            if ($result['success']) {
                // Parse the numbered list into an array
                $titles = [];
                $lines = explode("\n", $result['content']);
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (preg_match('/^\d+\.\s*(.+)$/', $line, $matches)) {
                        $titles[] = trim($matches[1]);
                    }
                }

                return $this->success([
                    'titles' => $titles,
                    'raw_content' => $result['content']
                ]);
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Title generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate meta description
     */
    public function generateMetaDescription()
    {
        $this->requireAuth();

        $input = $this->getRequestData();

        if (empty($input['title']) || empty($input['content'])) {
            return $this->error('Title and content are required', 400);
        }

        $keywords = $input['keywords'] ?? [];

        try {
            $result = $this->aiService->generateMetaDescription($input['title'], $input['content'], $keywords);

            if ($result['success']) {
                return $this->success([
                    'meta_description' => trim($result['content'])
                ]);
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Meta description generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate content tags
     */
    public function generateTags()
    {
        $this->requireAuth();

        $input = $this->getRequestData();

        if (empty($input['title']) || empty($input['content'])) {
            return $this->error('Title and content are required', 400);
        }

        $maxTags = $input['maxTags'] ?? 10;

        try {
            $result = $this->aiService->generateTags($input['title'], $input['content'], $maxTags);

            if ($result['success']) {
                // Parse comma-separated tags
                $tags = array_map('trim', explode(',', $result['content']));
                $tags = array_filter($tags); // Remove empty tags

                return $this->success([
                    'tags' => $tags,
                    'raw_content' => $result['content']
                ]);
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Tag generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Analyze content for SEO
     */
    public function analyzeSEO()
    {
        $this->requireAuth();

        $input = $this->getRequestData();

        if (empty($input['title']) || empty($input['content'])) {
            return $this->error('Title and content are required', 400);
        }

        $metaDescription = $input['metaDescription'] ?? '';
        $focusKeyword = $input['focusKeyword'] ?? '';

        try {
            $result = $this->aiService->analyzeSEO($input['title'], $input['content'], $metaDescription, $focusKeyword);

            if ($result['success']) {
                // Try to parse JSON response
                $analysis = json_decode($result['content'], true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $this->success([
                        'analysis' => $analysis
                    ]);
                } else {
                    // Fallback to raw content if JSON parsing fails
                    return $this->success([
                        'analysis' => [
                            'raw_content' => $result['content']
                        ]
                    ]);
                }
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('SEO analysis failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate complete blog post with template structure
     */
    public function generateBlogPost()
    {
        $this->requireAuth();

        $input = $this->getRequestData();

        if (empty($input['topic'])) {
            return $this->error('Topic is required', 400);
        }

        try {
            require_once __DIR__ . '/../AI/BlogPostGenerator.php';
            $generator = new \AdZetaAdmin\AI\BlogPostGenerator($this->db);

            $result = $generator->generateBlogPost(
                $input['topic'],
                $input['template'] ?? 'professional-article',
                $input['options'] ?? []
            );

            if ($result['success']) {
                return $this->success([
                    'post_data' => $result['data'],
                    'message' => 'Blog post generated successfully'
                ]);
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Blog post generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate complete post with all SEO data, tags, and keywords
     */
    public function generateCompletePost()
    {
        $this->requireAuth();

        $input = $this->getRequestData();

        if (empty($input['topic'])) {
            return $this->error('Topic is required', 400);
        }

        try {
            require_once __DIR__ . '/../AI/CompletePostGenerator.php';
            $generator = new \AdZetaAdmin\AI\CompletePostGenerator($this->db);

            $result = $generator->generateCompletePost(
                $input['topic'],
                $input['template'] ?? 'professional-article',
                $input['options'] ?? []
            );

            if ($result['success']) {
                return $this->success([
                    'post_data' => $result['data'],
                    'message' => 'Complete post with all SEO data generated successfully'
                ]);
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Complete post generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate topic suggestions
     */
    public function generateTopicSuggestions()
    {
        $this->requireAuth();

        try {
            require_once __DIR__ . '/../AI/BlogPostGenerator.php';
            $generator = new \AdZetaAdmin\AI\BlogPostGenerator($this->db);

            $category = $_GET['category'] ?? null;
            $count = intval($_GET['count'] ?? 5);

            $suggestions = $generator->generateTopicSuggestions($category, $count);

            return $this->success([
                'suggestions' => $suggestions,
                'category' => $category,
                'count' => count($suggestions)
            ]);
        } catch (\Exception $e) {
            return $this->error('Topic suggestions failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get AI settings
     */
    public function getSettings()
    {
        $this->requirePermission('manage_settings');

        try {
            $settings = [];

            // Get API keys
            $apiKeysResult = $this->db->fetch(
                "SELECT setting_value FROM settings WHERE setting_key = ?",
                ['gemini_api_keys']
            );

            if ($apiKeysResult) {
                $apiKeys = json_decode($apiKeysResult['setting_value'], true) ?: [];
                // Return full API keys for admin settings page
                $settings['api_keys'] = array_map(function($key) {
                    return [
                        'id' => $key['id'] ?? uniqid(),
                        'name' => $key['name'] ?? 'Unnamed Key',
                        'is_active' => $key['is_active'] ?? true,
                        'api_key' => $key['api_key'] ?? '' // Show full API key for admin settings
                    ];
                }, $apiKeys);
            } else {
                $settings['api_keys'] = [];
            }

            // Get other AI settings
            $otherSettings = $this->db->fetchAll(
                "SELECT setting_key, setting_value, setting_type
                 FROM settings
                 WHERE setting_key LIKE 'ai_%' AND setting_key != 'gemini_api_keys'"
            );

            foreach ($otherSettings as $setting) {
                $settings[$setting['setting_key']] = $this->castSettingValue(
                    $setting['setting_value'],
                    $setting['setting_type']
                );
            }

            return $this->success(['settings' => $settings]);

        } catch (\Exception $e) {
            return $this->error('Failed to load AI settings: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update AI settings
     */
    public function updateSettings()
    {
        $this->requirePermission('manage_settings');

        $input = $this->getRequestData();

        try {
            $updated = 0;

            foreach ($input as $key => $value) {
                if ($key === 'api_keys') {
                    // Handle API keys separately
                    $apiKeys = [];
                    foreach ($value as $keyData) {
                        $apiKeys[] = [
                            'id' => $keyData['id'] ?? uniqid(),
                            'name' => $keyData['name'] ?? 'Unnamed Key',
                            'api_key' => $keyData['api_key'] ?? '',
                            'is_active' => $keyData['is_active'] ?? true
                        ];
                    }

                    // Update or insert API keys setting
                    $existing = $this->db->fetch(
                        "SELECT id FROM settings WHERE setting_key = ?",
                        ['gemini_api_keys']
                    );

                    if ($existing) {
                        $this->db->execute(
                            "UPDATE settings SET setting_value = ? WHERE setting_key = ?",
                            [json_encode($apiKeys), 'gemini_api_keys']
                        );
                    } else {
                        $this->db->insert('settings', [
                            'setting_key' => 'gemini_api_keys',
                            'setting_value' => json_encode($apiKeys),
                            'setting_type' => 'json',
                            'description' => 'Google Gemini API keys configuration'
                        ]);
                    }
                    $updated++;
                } else {
                    // Handle other AI settings
                    $settingKey = 'ai_' . $key;

                    $existing = $this->db->fetch(
                        "SELECT setting_type FROM settings WHERE setting_key = ?",
                        [$settingKey]
                    );

                    if ($existing) {
                        $stringValue = $this->convertToString($value, $existing['setting_type']);
                        $this->db->execute(
                            "UPDATE settings SET setting_value = ? WHERE setting_key = ?",
                            [$stringValue, $settingKey]
                        );
                        $updated++;
                    }
                }
            }

            return $this->success([
                'message' => "Updated {$updated} AI settings successfully",
                'updated_count' => $updated
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to update AI settings: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get usage statistics
     */
    public function getUsageStats()
    {
        $this->requireAuth();

        try {
            $stats = $this->aiService->getUsageStats();
            return $this->success(['stats' => $stats]);
        } catch (\Exception $e) {
            return $this->error('Failed to get usage stats: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Cast setting value to appropriate type
     */
    private function castSettingValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return (bool)$value;
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }

    /**
     * Convert value to string for storage
     */
    private function convertToString($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'number':
                return (string)$value;
            case 'json':
                return json_encode($value);
            default:
                return (string)$value;
        }
    }

    /**
     * Generate case study content
     * POST /ai/generate-case-study
     */
    public function generateCaseStudy()
    {
        $payload = $this->requirePermission('create_posts');
        $input = $this->getRequestData();

        $this->validateRequired($input, ['client_name', 'industry', 'challenge', 'solution']);

        try {
            $result = $this->aiService->generateCaseStudyContent(
                $input['client_name'],
                $input['industry'],
                $input['challenge'],
                $input['solution'],
                $input['results'] ?? []
            );

            if ($result['success']) {
                // Try to parse JSON response
                $content = $result['content'];
                $parsedContent = json_decode($content, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $this->success([
                        'case_study_data' => $parsedContent,
                        'raw_content' => $content,
                        'message' => 'Case study content generated successfully'
                    ]);
                } else {
                    return $this->success([
                        'raw_content' => $content,
                        'message' => 'Case study content generated (manual parsing required)'
                    ]);
                }
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Case study generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate case study section
     * POST /ai/generate-case-study-section
     */
    public function generateCaseStudySection()
    {
        $payload = $this->requirePermission('create_posts');
        $input = $this->getRequestData();

        $this->validateRequired($input, ['section_type', 'context']);

        try {
            $result = $this->aiService->generateCaseStudySection(
                $input['section_type'],
                $input['context']
            );

            if ($result['success']) {
                // Try to parse JSON response
                $content = $result['content'];
                $parsedContent = json_decode($content, true);

                if (json_last_error() === JSON_ERROR_NONE) {
                    return $this->success([
                        'section_data' => $parsedContent,
                        'raw_content' => $content,
                        'message' => 'Section content generated successfully'
                    ]);
                } else {
                    return $this->success([
                        'raw_content' => $content,
                        'message' => 'Section content generated (manual parsing required)'
                    ]);
                }
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Section generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate case study titles
     * POST /ai/generate-case-study-titles
     */
    public function generateCaseStudyTitles()
    {
        $payload = $this->requirePermission('create_posts');
        $input = $this->getRequestData();

        $this->validateRequired($input, ['client_name', 'industry', 'results']);

        try {
            $result = $this->aiService->generateCaseStudyTitles(
                $input['client_name'],
                $input['industry'],
                $input['results'],
                $input['count'] ?? 5
            );

            if ($result['success']) {
                return $this->success([
                    'titles' => $result['content'],
                    'message' => 'Case study titles generated successfully'
                ]);
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Title generation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Generate case study meta description
     * POST /ai/generate-case-study-meta
     */
    public function generateCaseStudyMeta()
    {
        $payload = $this->requirePermission('create_posts');
        $input = $this->getRequestData();

        $this->validateRequired($input, ['title', 'client_name', 'results']);

        try {
            $result = $this->aiService->generateCaseStudyMetaDescription(
                $input['title'],
                $input['client_name'],
                $input['results']
            );

            if ($result['success']) {
                return $this->success([
                    'meta_description' => trim($result['content']),
                    'message' => 'Meta description generated successfully'
                ]);
            } else {
                return $this->error($result['error'], 400);
            }
        } catch (\Exception $e) {
            return $this->error('Meta description generation failed: ' . $e->getMessage(), 500);
        }
    }
}
