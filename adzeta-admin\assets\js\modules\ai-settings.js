/**
 * AI Settings Module
 * Manages AI configuration and API keys
 */

window.AdZetaAISettings = {
    // State management
    state: {
        settings: {},
        apiKeys: [],
        loading: false,
        testingConnection: false
    },

    // Initialize AI settings module
    init() {
        console.log('AI Settings module initialized');
        this.bindEvents();
        this.loadSettings();
    },

    // Bind events
    bindEvents() {
        // Listen for navigation changes
        document.addEventListener('viewChanged', (e) => {
            if (e.detail.view === 'ai-settings') {
                console.log('AI Settings view activated, loading fresh settings...');
                this.loadSettings().then(() => {
                    this.render();
                });
            }
        });
    },

    // Load AI settings
    async loadSettings() {
        // Check authentication before loading
        if (!this.isUserAuthenticated()) {
            console.log('User not authenticated, cannot load AI settings');
            return;
        }

        this.state.loading = true;

        try {
            const response = await window.AdZetaApp.apiRequest('/ai/settings');
            if (response.success) {
                this.state.settings = response.settings;
                this.state.apiKeys = response.settings.api_keys || [];

                // Clean up API keys - remove any invalid entries
                this.state.apiKeys = this.state.apiKeys.filter(key => {
                    return key && (key.api_key || key.name) && typeof key === 'object';
                });

                console.log('AI Settings: Loaded settings successfully');
                console.log('AI Settings: API Keys after cleanup:', this.state.apiKeys);
                console.log('AI Settings: Full settings:', this.state.settings);
            } else {
                console.error('Failed to load AI settings:', response.error);
            }
        } catch (error) {
            console.error('Error loading AI settings:', error);
            if (this.isUserAuthenticated()) {
                window.AdZetaApp.showNotification('Failed to load AI settings', 'danger');
            }
        } finally {
            this.state.loading = false;
        }
    },

    // Render AI settings view
    render() {
        const container = document.getElementById('ai-settingsView');
        if (!container) {
            console.error('AI Settings container not found!');
            return;
        }

        container.innerHTML = `
            <div class="ai-settings-container">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-robot me-2 text-primary"></i>
                                    AI Integration Settings
                                </h5>
                                <button class="btn btn-outline-primary btn-sm" onclick="AdZetaAISettings.testConnection()">
                                    <i class="fas fa-plug me-1"></i>
                                    Test Connection
                                </button>
                            </div>
                            <div class="card-body">
                                ${this.renderAPIKeysSection()}
                                ${this.renderGeneralSettings()}
                                ${this.renderUsageStats()}
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-primary" onclick="AdZetaAISettings.saveSettings()">
                                    <i class="fas fa-save me-1"></i>
                                    Save Settings
                                </button>
                                <button class="btn btn-outline-secondary ms-2" onclick="AdZetaAISettings.loadSettings()">
                                    <i class="fas fa-refresh me-1"></i>
                                    Reload
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.bindFormEvents();
    },

    // Render API keys section
    renderAPIKeysSection() {
        return `
            <div class="api-keys-section mb-4">
                <h6 class="border-bottom pb-2 mb-3">
                    <i class="fas fa-key me-2"></i>
                    Google Gemini API Keys
                </h6>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Add multiple API keys for automatic failover when quota limits are reached.
                    Keys are tried in order until one succeeds.
                </div>

                <div id="apiKeysList">
                    ${this.state.apiKeys.filter(key => key && (key.api_key || key.name)).map((key, index) => this.renderAPIKeyRow(key, index)).join('')}
                    ${this.state.apiKeys.length === 0 ? '<p class="text-muted">No API keys configured. Click "Add API Key" to get started.</p>' : ''}
                </div>

                <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="AdZetaAISettings.addAPIKey()">
                    <i class="fas fa-plus me-1"></i>
                    Add API Key
                </button>
            </div>
        `;
    },

    // Render individual API key row
    renderAPIKeyRow(key, index) {
        console.log('Rendering API key row:', index, key);

        // Ensure key has required properties
        if (!key) {
            console.warn('Empty key object at index:', index);
            return '';
        }

        return `
            <div class="api-key-row mb-3 p-3 border rounded" data-index="${index}">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label class="form-label">Name</label>
                        <input type="text" class="form-control form-control-sm"
                               value="${this.escapeHtml(key.name || '')}"
                               onchange="AdZetaAISettings.updateAPIKey(${index}, 'name', this.value)">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control form-control-sm api-key-input"
                                   id="apiKey_${index}"
                                   value="${this.escapeHtml(key.api_key || '')}"
                                   placeholder="Enter your Gemini API key"
                                   data-original-key="${this.escapeHtml(key.api_key || '')}"
                                   data-is-masked="${key.api_key && (key.api_key.startsWith('***') || key.api_key === '******') ? 'true' : 'false'}"
                                   onchange="AdZetaAISettings.updateAPIKey(${index}, 'api_key', this.value)">
                            <button class="btn btn-outline-secondary btn-sm" type="button"
                                    onclick="AdZetaAISettings.toggleAPIKeyVisibility(${index})"
                                    title="Toggle API key visibility"
                                    data-target="apiKey_${index}">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        ${key.api_key && (key.api_key.startsWith('***') || key.api_key === '******') ? '<small class="text-muted">Key is saved (showing masked version)</small>' : ''}
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                   ${key.is_active ? 'checked' : ''}
                                   onchange="AdZetaAISettings.updateAPIKey(${index}, 'is_active', this.checked)">
                            <label class="form-check-label">Active</label>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-outline-danger btn-sm d-block"
                                onclick="AdZetaAISettings.removeAPIKey(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    },

    // Render general settings
    renderGeneralSettings() {
        return `
            <div class="general-settings-section mb-4">
                <h6 class="border-bottom pb-2 mb-3">
                    <i class="fas fa-cog me-2"></i>
                    General AI Settings
                </h6>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-brain me-1 text-primary"></i>
                                Gemini Model
                            </label>
                            <select class="form-select" onchange="AdZetaAISettings.updateSetting('gemini_model', this.value)">
                                <optgroup label="🚀 Latest Models (Recommended)">
                                    <option value="gemini-2.5-flash" ${this.state.settings.gemini_model === 'gemini-2.5-flash' ? 'selected' : ''}>Gemini 2.5 Flash (Latest)</option>
                                    <option value="gemini-2.5-pro" ${this.state.settings.gemini_model === 'gemini-2.5-pro' ? 'selected' : ''}>Gemini 2.5 Pro (Advanced)</option>
                                    <option value="gemini-2.0-flash" ${this.state.settings.gemini_model === 'gemini-2.0-flash' ? 'selected' : ''}>Gemini 2.0 Flash</option>
                                </optgroup>
                                <optgroup label="⚡ Stable Models">
                                    <option value="gemini-1.5-flash" ${this.state.settings.gemini_model === 'gemini-1.5-flash' ? 'selected' : ''}>Gemini 1.5 Flash</option>
                                    <option value="gemini-1.5-pro" ${this.state.settings.gemini_model === 'gemini-1.5-pro' ? 'selected' : ''}>Gemini 1.5 Pro</option>
                                    <option value="gemini-1.0-pro" ${this.state.settings.gemini_model === 'gemini-1.0-pro' ? 'selected' : ''}>Gemini 1.0 Pro</option>
                                </optgroup>
                            </select>
                            <div class="form-text">Choose model based on your needs</div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="AdZetaAISettings.showModelInfo()">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Model Comparison
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">Default Temperature</label>
                            <input type="range" class="form-range" min="0" max="1" step="0.1"
                                   value="${this.state.settings.default_temperature || 0.7}"
                                   oninput="AdZetaAISettings.updateSetting('default_temperature', this.value); this.nextElementSibling.textContent = this.value">
                            <small class="text-muted">0.7</small>
                            <div class="form-text">Controls creativity (0 = focused, 1 = creative)</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">Max Output Tokens</label>
                            <select class="form-select" onchange="AdZetaAISettings.updateSetting('max_output_tokens', this.value)">
                                <option value="1024" ${this.state.settings.max_output_tokens == 1024 ? 'selected' : ''}>1,024</option>
                                <option value="2048" ${this.state.settings.max_output_tokens == 2048 ? 'selected' : ''}>2,048</option>
                                <option value="4096" ${this.state.settings.max_output_tokens == 4096 ? 'selected' : ''}>4,096</option>
                                <option value="8192" ${this.state.settings.max_output_tokens == 8192 ? 'selected' : ''}>8,192</option>
                            </select>
                            <div class="form-text">Maximum length of AI responses</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       ${this.state.settings.auto_suggestions_enabled ? 'checked' : ''}
                                       onchange="AdZetaAISettings.updateSetting('auto_suggestions_enabled', this.checked)">
                                <label class="form-check-label">Enable Auto-Suggestions</label>
                            </div>
                            <div class="form-text">Show AI suggestions while typing</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox"
                                       ${this.state.settings.seo_analysis_enabled ? 'checked' : ''}
                                       onchange="AdZetaAISettings.updateSetting('seo_analysis_enabled', this.checked)">
                                <label class="form-check-label">Enable SEO Analysis</label>
                            </div>
                            <div class="form-text">Automatic SEO recommendations</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // Render usage statistics
    renderUsageStats() {
        return `
            <div class="usage-stats-section">
                <h6 class="border-bottom pb-2 mb-3">
                    <i class="fas fa-chart-bar me-2"></i>
                    Usage Statistics
                </h6>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">${this.state.apiKeys.filter(k => k.is_active).length}</h5>
                                <p class="card-text">Active API Keys</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">-</h5>
                                <p class="card-text">Requests Today</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h5 class="card-title">-</h5>
                                <p class="card-text">Success Rate</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // Add new API key
    addAPIKey() {
        const newKey = {
            id: 'key_' + Date.now(),
            name: 'API Key ' + (this.state.apiKeys.length + 1),
            api_key: '',
            is_active: true
        };

        this.state.apiKeys.push(newKey);
        this.render();
    },

    // Remove API key
    removeAPIKey(index) {
        if (confirm('Are you sure you want to remove this API key?')) {
            this.state.apiKeys.splice(index, 1);
            this.render();
        }
    },

    // Update API key
    updateAPIKey(index, field, value) {
        if (this.state.apiKeys[index]) {
            // If updating api_key field and the value is empty but we have a masked key, keep the original
            if (field === 'api_key' && !value && this.state.apiKeys[index].api_key && this.state.apiKeys[index].api_key.startsWith('***')) {
                // Don't update if user didn't provide a new key
                return;
            }
            this.state.apiKeys[index][field] = value;
        }
    },

    // Update general setting
    updateSetting(key, value) {
        this.state.settings[key] = value;
    },

    // Toggle API key visibility
    toggleAPIKeyVisibility(index) {
        console.log('Toggling visibility for API key index:', index);

        // Use the specific ID to target the correct input
        const input = document.getElementById(`apiKey_${index}`);
        const button = document.querySelector(`button[data-target="apiKey_${index}"] i.fas`);

        if (!input) {
            console.error('API key input not found with ID:', `apiKey_${index}`);
            return;
        }

        if (!button) {
            console.error('Toggle button not found for API key:', `apiKey_${index}`);
            return;
        }

        const isMasked = input.getAttribute('data-is-masked') === 'true';

        console.log('Current input type:', input.type, 'Is masked:', isMasked);

        if (input.type === 'password') {
            input.type = 'text';
            button.className = 'fas fa-eye-slash';

            // If it's a masked key, show a message
            if (isMasked) {
                input.title = 'This is a masked version of your saved API key. Enter a new key to replace it.';
            }
            console.log('Changed to text type');
        } else {
            input.type = 'password';
            button.className = 'fas fa-eye';
            input.title = '';
            console.log('Changed to password type');
        }
    },

    // Test AI connection
    async testConnection() {
        if (this.state.testingConnection) return;

        this.state.testingConnection = true;
        const button = document.querySelector('button[onclick="AdZetaAISettings.testConnection()"]');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Testing...';
        button.disabled = true;

        try {
            // Save current settings first
            await this.saveSettings(false);

            // Test connection
            const response = await window.AdZetaApp.apiRequest('/ai/test-connection', {
                method: 'POST'
            });

            if (response.success) {
                window.AdZetaApp.showNotification('AI connection successful!', 'success');
            } else {
                window.AdZetaApp.showNotification('Connection failed: ' + response.error, 'danger');
            }
        } catch (error) {
            window.AdZetaApp.showNotification('Connection test failed: ' + error.message, 'danger');
        } finally {
            this.state.testingConnection = false;
            button.innerHTML = originalText;
            button.disabled = false;
        }
    },

    // Save settings
    async saveSettings(showNotification = true) {
        try {
            const settingsData = {
                api_keys: this.state.apiKeys,
                gemini_model: this.state.settings.gemini_model || 'gemini-2.5-flash',
                default_template: this.state.settings.default_template || 'professional-enhanced',
                default_temperature: this.state.settings.default_temperature || 0.7,
                max_output_tokens: this.state.settings.max_output_tokens || 2048,
                auto_suggestions_enabled: this.state.settings.auto_suggestions_enabled || false,
                seo_analysis_enabled: this.state.settings.seo_analysis_enabled || false
            };

            const response = await window.AdZetaApp.apiRequest('/ai/settings', {
                method: 'POST',
                body: JSON.stringify(settingsData)
            });

            if (response.success) {
                if (showNotification) {
                    window.AdZetaApp.showNotification('AI settings saved successfully!', 'success');
                }

                // Reload AI assistant with new settings
                if (window.AdZetaAI) {
                    await window.AdZetaAI.loadSettings();
                }
            } else {
                throw new Error(response.error || 'Failed to save settings');
            }
        } catch (error) {
            console.error('Error saving AI settings:', error);
            if (showNotification) {
                window.AdZetaApp.showNotification('Failed to save AI settings: ' + error.message, 'danger');
            }
        }
    },

    // Show model information modal
    showModelInfo() {
        const modalHTML = `
            <div class="modal fade" id="modelInfoModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-brain me-2 text-primary"></i>
                                Gemini Model Comparison
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Model</th>
                                            <th>Best For</th>
                                            <th>Speed</th>
                                            <th>Quality</th>
                                            <th>Cost</th>
                                            <th>Context Window</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="table-warning">
                                            <td>
                                                <strong>Gemini 2.5 Flash</strong>
                                                <br><small class="text-muted">🚀 Latest & Fastest</small>
                                            </td>
                                            <td>
                                                • All content types<br>
                                                • Ultra-fast generation<br>
                                                • Best performance
                                            </td>
                                            <td><span class="badge bg-success">Ultra Fast</span></td>
                                            <td><span class="badge bg-success">Excellent</span></td>
                                            <td><span class="badge bg-success">Very Low</span></td>
                                            <td>2M tokens</td>
                                        </tr>
                                        <tr class="table-info">
                                            <td>
                                                <strong>Gemini 2.5 Pro</strong>
                                                <br><small class="text-muted">🏆 Most Advanced</small>
                                            </td>
                                            <td>
                                                • Complex reasoning<br>
                                                • Research & analysis<br>
                                                • Technical content
                                            </td>
                                            <td><span class="badge bg-primary">Fast</span></td>
                                            <td><span class="badge bg-success">Superior</span></td>
                                            <td><span class="badge bg-warning">Medium</span></td>
                                            <td>2M tokens</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <strong>Gemini 2.0 Flash</strong>
                                                <br><small class="text-muted">✨ Next-Gen</small>
                                            </td>
                                            <td>
                                                • Balanced performance<br>
                                                • Improved reasoning<br>
                                                • General content
                                            </td>
                                            <td><span class="badge bg-success">Very Fast</span></td>
                                            <td><span class="badge bg-primary">High</span></td>
                                            <td><span class="badge bg-success">Low</span></td>
                                            <td>1M tokens</td>
                                        </tr>
                                        <tr class="table-success">
                                            <td>
                                                <strong>Gemini 1.5 Flash</strong>
                                                <br><small class="text-muted">✅ Proven & Reliable</small>
                                            </td>
                                            <td>
                                                • Blog posts<br>
                                                • Quick content<br>
                                                • High-volume tasks
                                            </td>
                                            <td><span class="badge bg-success">Very Fast</span></td>
                                            <td><span class="badge bg-primary">High</span></td>
                                            <td><span class="badge bg-success">Low</span></td>
                                            <td>1M tokens</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <strong>Gemini 1.5 Pro</strong>
                                                <br><small class="text-muted">Advanced</small>
                                            </td>
                                            <td>
                                                • Complex analysis<br>
                                                • Research content<br>
                                                • Technical writing
                                            </td>
                                            <td><span class="badge bg-warning">Moderate</span></td>
                                            <td><span class="badge bg-success">Highest</span></td>
                                            <td><span class="badge bg-warning">Medium</span></td>
                                            <td>2M tokens</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <strong>Gemini 1.0 Pro</strong>
                                                <br><small class="text-muted">Stable</small>
                                            </td>
                                            <td>
                                                • General content<br>
                                                • Stable output<br>
                                                • Consistent results
                                            </td>
                                            <td><span class="badge bg-primary">Fast</span></td>
                                            <td><span class="badge bg-primary">Good</span></td>
                                            <td><span class="badge bg-primary">Low</span></td>
                                            <td>32K tokens</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="alert alert-info mt-3">
                                <h6><i class="fas fa-lightbulb me-2"></i>Recommendations:</h6>
                                <ul class="mb-0">
                                    <li><strong>Gemini 2.5 Flash</strong>: 🚀 Best overall choice - fastest, most efficient, latest features</li>
                                    <li><strong>Gemini 2.5 Pro</strong>: 🏆 For complex analysis, research, and advanced reasoning tasks</li>
                                    <li><strong>Gemini 2.0 Flash</strong>: ✨ Great balance of speed and improved capabilities</li>
                                    <li><strong>Gemini 1.5 Flash</strong>: ✅ Proven and reliable for standard blog content</li>
                                    <li><strong>Gemini 1.5 Pro</strong>: Advanced option for technical and complex content</li>
                                    <li><strong>Gemini 1.0 Pro</strong>: Stable choice for consistent, general content</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Note:</h6>
                                <p class="mb-0">Model selection affects both response quality and API costs. <strong>Gemini 2.5 Flash</strong> is now the recommended choice, offering the best combination of speed, quality, and cost-effectiveness with the latest AI capabilities.</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <a href="https://ai.google.dev/gemini-api/docs/models" target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-external-link-alt me-1"></i>
                                View Official Docs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('modelInfoModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('modelInfoModal'));
        modal.show();
    },

    // Bind form events
    bindFormEvents() {
        // Auto-save on input changes (debounced)
        let saveTimeout;
        const inputs = document.querySelectorAll('#aiSettingsView input, #aiSettingsView select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    this.saveSettings(false);
                }, 1000);
            });
        });
    },

    // Check if user is authenticated
    isUserAuthenticated() {
        // Primary check: AdZetaApp authentication state
        if (window.AdZetaApp && window.AdZetaApp.state && window.AdZetaApp.state.isAuthenticated) {
            console.log('AI Settings: User authenticated via AdZetaApp state');
            return true;
        }

        // Secondary check: Check if we're on the login page
        if (window.location.pathname.includes('login') || window.location.search.includes('login')) {
            console.log('AI Settings: On login page, user not authenticated');
            return false;
        }

        // Tertiary check: JWT token exists and admin dashboard is visible
        const token = localStorage.getItem('adzeta_token') || sessionStorage.getItem('adzeta_token');
        const adminDashboard = document.getElementById('adminDashboard');
        const loginPage = document.getElementById('loginPage');

        if (token && adminDashboard && adminDashboard.style.display !== 'none' &&
            loginPage && loginPage.style.display === 'none') {
            console.log('AI Settings: User authenticated via token and UI state');
            return true;
        }

        console.log('AI Settings: User not authenticated - no valid auth state found');
        return false;
    },

    // Utility function
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => window.AdZetaAISettings.init());
} else {
    window.AdZetaAISettings.init();
}
