/**
 * SEO Manager Module
 * Handles SEO management interface and functionality
 */

class SEOManager {
    constructor() {
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.searchQuery = '';
        this.isLoading = false;
        this.editingPage = null;
    }

    /**
     * Initialize SEO Manager
     */
    init() {
        console.log('Initializing SEO Manager...');
        this.render();
        this.bindEvents();
        this.loadPages();
        this.loadSettings();

        // Load initial tab content
        setTimeout(() => {
            console.log('Loading initial tab content...');
            // Check if sitemap tab is active and load its content
            const sitemapTab = document.getElementById('sitemap-tab');
            const robotsTab = document.getElementById('robots-tab');

            if (sitemapTab && sitemapTab.classList.contains('active')) {
                this.loadSitemapStatus();
            }
            if (robotsTab && robotsTab.classList.contains('active')) {
                this.loadRobotsContent();
            }
        }, 200);
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // This will be called by navigation.js when the SEO view is loaded
        console.log('SEO Manager events bound');
    }

    /**
     * Render SEO management interface
     */
    render() {
        const container = document.getElementById('seoView');

        if (!container) {
            console.error('SEO view container not found!');
            return;
        }

        console.log('Rendering SEO Manager interface...');

        container.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="h4 mb-1">SEO Management</h2>
                    <p class="text-muted mb-0">Manage SEO data for all pages and global settings</p>
                </div>
                <button class="btn btn-primary" id="addPageSEO">
                    <i class="fas fa-plus me-2"></i>Add Page SEO
                </button>
            </div>

            <!-- SEO Tabs -->
            <ul class="nav nav-tabs mb-4" id="seoTabs">
                <li class="nav-item">
                    <a class="nav-link active" id="pages-tab" data-bs-toggle="tab" href="#pages-content">
                        <i class="fas fa-file-alt me-2"></i>Page SEO
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="settings-tab" data-bs-toggle="tab" href="#settings-content">
                        <i class="fas fa-cog me-2"></i>Global Settings
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="sitemap-tab" data-bs-toggle="tab" href="#sitemap-content">
                        <i class="fas fa-sitemap me-2"></i>Sitemap Generator
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="robots-tab" data-bs-toggle="tab" href="#robots-content">
                        <i class="fas fa-robot me-2"></i>Robots.txt
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Pages Tab -->
                <div class="tab-pane fade show active" id="pages-content">
                    <!-- Search and Filters -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchPages" placeholder="Search pages...">
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <button class="btn btn-outline-secondary" id="refreshPages">
                                <i class="fas fa-sync-alt me-2"></i>Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Pages Table -->
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Page URL</th>
                                            <th>Page Title</th>
                                            <th>Meta Description</th>
                                            <th>Status</th>
                                            <th>Updated</th>
                                            <th width="120">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="pagesTableBody">
                                        <tr>
                                            <td colspan="6" class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="text-muted">
                            <span id="pagesInfo">Loading...</span>
                        </div>
                        <nav>
                            <ul class="pagination pagination-sm mb-0" id="pagesPagination">
                                <!-- Pagination will be generated here -->
                            </ul>
                        </nav>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings-content">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">Global SEO Settings</h5>
                                </div>
                                <div class="card-body">
                                    <form id="seoSettingsForm">
                                        <div id="settingsFormContent">
                                            <div class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-end mt-3">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>Save Settings
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">SEO Tips</h6>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-lightbulb me-2"></i>Best Practices</h6>
                                        <ul class="mb-0 ps-3">
                                            <li>Keep titles under 60 characters</li>
                                            <li>Meta descriptions should be 150-160 characters</li>
                                            <li>Use unique titles and descriptions for each page</li>
                                            <li>Include target keywords naturally</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <!-- Sitemap Generator Tab -->
                <div class="tab-pane fade" id="sitemap-content">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">XML Sitemap Generator</h5>
                                </div>
                                <div class="card-body">
                                    <div id="sitemapStatus" class="mb-4">
                                        <div class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading sitemap status...</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex gap-3 mb-4">
                                        <button type="button" class="btn btn-primary" id="generateSitemap">
                                            <i class="fas fa-sync-alt me-2"></i>Generate Sitemap
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="refreshSitemapStatus">
                                            <i class="fas fa-refresh me-2"></i>Refresh Status
                                        </button>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="fw-bold mb-3">Sitemap Settings</h6>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="includeBlogPosts" checked>
                                                <label class="form-check-label" for="includeBlogPosts">
                                                    Include Blog Posts
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="includeCaseStudies" checked>
                                                <label class="form-check-label" for="includeCaseStudies">
                                                    Include Case Studies
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="includeWhitepapers" checked>
                                                <label class="form-check-label" for="includeWhitepapers">
                                                    Include Whitepapers
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="fw-bold mb-3">Priority Settings</h6>
                                            <div class="mb-2">
                                                <label class="form-label small">Homepage Priority</label>
                                                <select class="form-select form-select-sm" id="homepagePriority">
                                                    <option value="1.0" selected>1.0 (Highest)</option>
                                                    <option value="0.9">0.9</option>
                                                    <option value="0.8">0.8</option>
                                                </select>
                                            </div>
                                            <div class="mb-2">
                                                <label class="form-label small">Blog Posts Priority</label>
                                                <select class="form-select form-select-sm" id="blogPriority">
                                                    <option value="0.8" selected>0.8</option>
                                                    <option value="0.7">0.7</option>
                                                    <option value="0.6">0.6</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Sitemap Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="small text-muted mb-3">
                                        <p><strong>What is a sitemap?</strong></p>
                                        <p>An XML sitemap helps search engines discover and index your content more efficiently. It lists all important pages on your website with metadata about when they were last updated.</p>

                                        <p><strong>Best Practices:</strong></p>
                                        <ul class="small">
                                            <li>Update your sitemap regularly</li>
                                            <li>Submit to Google Search Console</li>
                                            <li>Keep it under 50,000 URLs</li>
                                            <li>Include only canonical URLs</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Robots.txt Management Tab -->
                <div class="tab-pane fade" id="robots-content">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">Robots.txt Editor</h5>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-secondary" id="loadRobotsTemplate">
                                            <i class="fas fa-file-import me-1"></i>Load Template
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" id="validateRobots">
                                            <i class="fas fa-check-circle me-1"></i>Validate
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="robotsContent" class="form-label">Robots.txt Content</label>
                                        <textarea class="form-control font-monospace" id="robotsContent" rows="15"
                                                  placeholder="Loading robots.txt content..."></textarea>
                                        <div class="form-text">
                                            Edit your robots.txt file directly. Changes will be saved to the root directory.
                                        </div>
                                    </div>

                                    <div id="robotsValidation" class="mb-3" style="display: none;"></div>

                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-primary" id="saveRobots">
                                            <i class="fas fa-save me-2"></i>Save Robots.txt
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" id="previewRobots">
                                            <i class="fas fa-eye me-2"></i>Preview
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Quick Templates</h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2 mb-3">
                                        <button class="btn btn-outline-primary btn-sm" onclick="seoManager.loadRobotsTemplate('basic')">
                                            Basic Template
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" onclick="seoManager.loadRobotsTemplate('marketing')">
                                            Marketing Agency
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" onclick="seoManager.loadRobotsTemplate('strict')">
                                            Strict Template
                                        </button>
                                    </div>

                                    <div class="small text-muted">
                                        <p><strong>Robots.txt Guidelines:</strong></p>
                                        <ul class="small">
                                            <li>Use "User-agent: *" for all bots</li>
                                            <li>Disallow sensitive directories</li>
                                            <li>Include sitemap URL</li>
                                            <li>Test with Google Search Console</li>
                                        </ul>

                                        <p class="mt-3"><strong>Current Status:</strong></p>
                                        <div id="robotsCurrentStatus">
                                            <span class="badge bg-secondary">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page SEO Modal -->
            <div class="modal fade" id="pageSEOModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-search me-2"></i>
                                <span id="modalTitle">Add Page SEO</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="pageSEOForm">
                                <div id="pageSEOFormContent">
                                    <!-- Form content will be loaded here -->
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="savePageSEO">
                                <i class="fas fa-save me-2"></i>Save SEO Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        console.log('SEO Manager HTML rendered successfully');

        // Verify tabs are rendered and initialize Bootstrap tabs
        setTimeout(() => {
            const sitemapTab = document.getElementById('sitemap-tab');
            const robotsTab = document.getElementById('robots-tab');
            const sitemapContent = document.getElementById('sitemap-content');
            const robotsContent = document.getElementById('robots-content');

            console.log('Tab verification:', {
                sitemapTab: !!sitemapTab,
                robotsTab: !!robotsTab,
                sitemapContent: !!sitemapContent,
                robotsContent: !!robotsContent
            });

            // Initialize Bootstrap tabs if they exist
            if (sitemapTab && robotsTab) {
                console.log('Initializing Bootstrap tabs...');

                // Add Bootstrap tab functionality
                const tabElements = document.querySelectorAll('#seoTabs .nav-link');
                tabElements.forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();

                        // Remove active class from all tabs and content
                        document.querySelectorAll('#seoTabs .nav-link').forEach(t => t.classList.remove('active'));
                        document.querySelectorAll('.tab-pane').forEach(p => {
                            p.classList.remove('show', 'active');
                        });

                        // Add active class to clicked tab
                        tab.classList.add('active');

                        // Show corresponding content
                        const targetId = tab.getAttribute('href').substring(1);
                        const targetContent = document.getElementById(targetId);
                        if (targetContent) {
                            targetContent.classList.add('show', 'active');
                            console.log(`Activated tab: ${targetId}`);
                        }
                    });
                });
            }
        }, 100);

        // Bind events
        this.bindUIEvents();
    }

    /**
     * Bind UI events
     */
    bindUIEvents() {
        // Add page SEO button
        document.getElementById('addPageSEO')?.addEventListener('click', () => {
            this.showPageSEOModal();
        });

        // Search functionality
        document.getElementById('searchPages')?.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.currentPage = 1;
            this.loadPages();
        });

        // Refresh button
        document.getElementById('refreshPages')?.addEventListener('click', () => {
            this.loadPages();
        });

        // Save page SEO
        document.getElementById('savePageSEO')?.addEventListener('click', () => {
            this.savePageSEO();
        });

        // Settings form
        document.getElementById('seoSettingsForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettings();
        });

        // Sitemap events
        document.getElementById('generateSitemap')?.addEventListener('click', () => {
            this.generateSitemap();
        });

        document.getElementById('refreshSitemapStatus')?.addEventListener('click', () => {
            this.loadSitemapStatus();
        });

        // Robots.txt events
        document.getElementById('loadRobotsTemplate')?.addEventListener('click', () => {
            this.showRobotsTemplateDropdown();
        });

        document.getElementById('validateRobots')?.addEventListener('click', () => {
            this.validateRobots();
        });

        document.getElementById('saveRobots')?.addEventListener('click', () => {
            this.saveRobots();
        });

        document.getElementById('previewRobots')?.addEventListener('click', () => {
            this.previewRobots();
        });

        // Tab change events - use click events instead of Bootstrap tab events for better compatibility
        document.getElementById('sitemap-tab')?.addEventListener('click', () => {
            setTimeout(() => {
                if (document.getElementById('sitemap-tab').classList.contains('active')) {
                    this.loadSitemapStatus();
                }
            }, 100);
        });

        document.getElementById('robots-tab')?.addEventListener('click', () => {
            setTimeout(() => {
                if (document.getElementById('robots-tab').classList.contains('active')) {
                    this.loadRobotsContent();
                }
            }, 100);
        });
    }

    /**
     * Load pages with SEO data
     */
    async loadPages() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        const tbody = document.getElementById('pagesTableBody');
        
        try {
            const queryParams = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage,
                search: this.searchQuery
            });

            const response = await window.AdZetaApp.apiRequest(`/page-seo?${queryParams}`);

            if (response.success) {
                this.renderPagesTable(response.pages);
                this.renderPagination(response.pagination);
            } else {
                throw new Error(response.message || 'Failed to load pages');
            }
        } catch (error) {
            console.error('Error loading pages:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error loading pages: ${error.message}
                    </td>
                </tr>
            `;
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Render pages table
     */
    renderPagesTable(pages) {
        const tbody = document.getElementById('pagesTableBody');
        
        if (pages.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4 text-muted">
                        <i class="fas fa-search me-2"></i>
                        No pages found
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = pages.map(page => `
            <tr>
                <td>
                    <code class="text-primary">${this.escapeHtml(page.page_url)}</code>
                </td>
                <td>
                    <div class="fw-medium">${this.escapeHtml(page.page_title)}</div>
                </td>
                <td>
                    <div class="text-muted small" style="max-width: 300px;">
                        ${this.escapeHtml(page.meta_description || '').substring(0, 100)}${page.meta_description && page.meta_description.length > 100 ? '...' : ''}
                    </div>
                </td>
                <td>
                    <span class="badge ${page.is_active ? 'bg-success' : 'bg-secondary'}">
                        ${page.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>
                    <small class="text-muted">
                        ${new Date(page.updated_at).toLocaleDateString()}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="seoManager.editPage(${page.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="seoManager.deletePage(${page.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * Load SEO settings
     */
    async loadSettings() {
        try {
            const response = await window.AdZetaApp.apiRequest('/seo-settings');

            if (response.success) {
                this.renderSettingsForm(response.settings);
            }
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    /**
     * Render settings form
     */
    renderSettingsForm(settings) {
        const container = document.getElementById('settingsFormContent');
        
        container.innerHTML = Object.entries(settings).map(([key, setting]) => `
            <div class="mb-3">
                <label class="form-label">${this.formatSettingLabel(key)}</label>
                <input type="text" class="form-control" name="${key}" value="${this.escapeHtml(setting.value || '')}" 
                       placeholder="${this.escapeHtml(setting.description || '')}">
                <div class="form-text">${this.escapeHtml(setting.description || '')}</div>
            </div>
        `).join('');
    }

    /**
     * Show page SEO modal
     */
    showPageSEOModal(pageId = null) {
        this.editingPage = pageId;
        const modal = new bootstrap.Modal(document.getElementById('pageSEOModal'));
        const title = document.getElementById('modalTitle');
        
        title.textContent = pageId ? 'Edit Page SEO' : 'Add Page SEO';
        
        // Load form content
        this.renderPageSEOForm(pageId);
        
        modal.show();
    }

    /**
     * Render page SEO form
     */
    renderPageSEOForm(pageId = null) {
        const container = document.getElementById('pageSEOFormContent');
        
        container.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Page URL *</label>
                        <input type="text" class="form-control" name="page_url" placeholder="/page-url.php" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="is_active">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Page Title *</label>
                <input type="text" class="form-control" name="page_title" placeholder="Page Title" required>
                <div class="form-text">Recommended: 50-60 characters</div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Meta Description</label>
                <textarea class="form-control" name="meta_description" rows="3" placeholder="Brief description of the page"></textarea>
                <div class="form-text">Recommended: 150-160 characters</div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Meta Keywords</label>
                <input type="text" class="form-control" name="meta_keywords" placeholder="keyword1, keyword2, keyword3">
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Open Graph Title</label>
                        <input type="text" class="form-control" name="og_title" placeholder="Social media title">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Open Graph Image</label>
                        <input type="text" class="form-control" name="og_image" placeholder="/images/og-image.jpg">
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Open Graph Description</label>
                <textarea class="form-control" name="og_description" rows="2" placeholder="Social media description"></textarea>
            </div>
        `;
        
        // If editing, load existing data
        if (pageId) {
            this.loadPageSEOData(pageId);
        }
    }

    /**
     * Load existing page SEO data for editing
     */
    async loadPageSEOData(pageId) {
        try {
            const response = await window.AdZetaApp.apiRequest(`/page-seo/${pageId}`);

            if (response.success) {
                const page = response.page;
                const form = document.getElementById('pageSEOForm');

                // Populate form fields
                if (form) {
                    const fields = {
                        'page_url': page.page_url,
                        'page_title': page.page_title,
                        'meta_description': page.meta_description || '',
                        'meta_keywords': page.meta_keywords || '',
                        'og_title': page.og_title || '',
                        'og_description': page.og_description || '',
                        'og_image': page.og_image || '',
                        'is_active': page.is_active ? '1' : '0'
                    };

                    // Set form field values
                    Object.entries(fields).forEach(([name, value]) => {
                        const field = form.querySelector(`[name="${name}"]`);
                        if (field) {
                            field.value = value;
                        }
                    });
                }
            } else {
                this.showNotification('Failed to load page data: ' + response.message, 'error');
            }
        } catch (error) {
            console.error('Error loading page SEO data:', error);
            this.showNotification('Error loading page data', 'error');
        }
    }

    /**
     * Utility functions
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatSettingLabel(key) {
        return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    renderPagination(pagination) {
        // Implementation for pagination rendering
        const info = document.getElementById('pagesInfo');
        info.textContent = `Showing ${pagination.items_per_page * (pagination.current_page - 1) + 1}-${Math.min(pagination.items_per_page * pagination.current_page, pagination.total_items)} of ${pagination.total_items} pages`;
    }

    async editPage(pageId) {
        console.log('Editing page with ID:', pageId);
        this.showPageSEOModal(pageId);
    }

    async deletePage(pageId) {
        if (confirm('Are you sure you want to delete this page SEO data?')) {
            try {
                await window.AdZetaApp.apiRequest(`/page-seo/${pageId}`, {
                    method: 'DELETE'
                });
                this.loadPages();
                this.showNotification('Page SEO data deleted successfully', 'success');
            } catch (error) {
                this.showNotification('Error deleting page SEO data', 'error');
            }
        }
    }

    async savePageSEO() {
        const form = document.getElementById('pageSEOForm');
        const saveButton = document.getElementById('savePageSEO');

        if (!form) {
            this.showNotification('Form not found', 'error');
            return;
        }

        // Get form data
        const formData = new FormData(form);
        const data = {};

        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // Validate required fields
        if (!data.page_url || !data.page_title) {
            this.showNotification('Page URL and title are required', 'error');
            return;
        }

        // Show loading state
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
        saveButton.disabled = true;

        try {
            let response;

            if (this.editingPage) {
                // Update existing page
                response = await window.AdZetaApp.apiRequest(`/page-seo/${this.editingPage}`, {
                    method: 'PUT',
                    body: JSON.stringify(data)
                });
            } else {
                // Create new page
                response = await window.AdZetaApp.apiRequest('/page-seo', {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
            }

            if (response.success) {
                this.showNotification(response.message || 'Page SEO data saved successfully', 'success');

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('pageSEOModal'));
                if (modal) {
                    modal.hide();
                }

                // Reload pages list
                this.loadPages();

                // Reset editing state
                this.editingPage = null;
            } else {
                this.showNotification(response.message || 'Failed to save page SEO data', 'error');
            }
        } catch (error) {
            console.error('Error saving page SEO:', error);
            let errorMessage = 'Error saving page SEO data';

            if (error.response && error.response.data && error.response.data.message) {
                errorMessage = error.response.data.message;
            } else if (error.message) {
                errorMessage = error.message;
            }

            this.showNotification(errorMessage, 'error');
        } finally {
            // Restore button state
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
        }
    }

    async saveSettings() {
        const form = document.getElementById('seoSettingsForm');

        if (!form) {
            this.showNotification('Settings form not found', 'error');
            return;
        }

        // Get form data
        const formData = new FormData(form);
        const settings = {};

        // Convert FormData to settings object
        for (let [key, value] of formData.entries()) {
            settings[key] = value;
        }

        // Find submit button
        const submitButton = form.querySelector('button[type="submit"]');

        // Show loading state
        if (submitButton) {
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            submitButton.disabled = true;

            try {
                const response = await window.AdZetaApp.apiRequest('/seo-settings', {
                    method: 'POST',
                    body: JSON.stringify({ settings: settings })
                });

                if (response.success) {
                    this.showNotification('SEO settings saved successfully', 'success');
                } else {
                    this.showNotification(response.message || 'Failed to save settings', 'error');
                }
            } catch (error) {
                console.error('Error saving settings:', error);
                let errorMessage = 'Error saving SEO settings';

                if (error.response && error.response.data && error.response.data.message) {
                    errorMessage = error.response.data.message;
                } else if (error.message) {
                    errorMessage = error.message;
                }

                this.showNotification(errorMessage, 'error');
            } finally {
                // Restore button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

        notification.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);

        // Also log to console
        console.log(`${type}: ${message}`);
    }

    /**
     * Load sitemap status
     */
    async loadSitemapStatus() {
        console.log('Loading sitemap status...');

        try {
            const response = await window.AdZetaApp.apiRequest('/sitemap/status');
            console.log('Sitemap status response:', response);

            if (response.success) {
                // Handle both response.data and direct response structure
                const sitemapData = response.data || response;
                this.renderSitemapStatus(sitemapData);
            } else {
                console.warn('Sitemap status request failed:', response.message);
                this.renderSitemapStatus(null);
            }
        } catch (error) {
            console.error('Error loading sitemap status:', error);
            this.renderSitemapStatus(null);
        }
    }

    /**
     * Render sitemap status
     */
    renderSitemapStatus(data) {
        const container = document.getElementById('sitemapStatus');

        if (!data) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No sitemap found. Generate your first sitemap to get started.
                </div>
            `;
            return;
        }

        const statusBadge = data.exists ?
            '<span class="badge bg-success">Active</span>' :
            '<span class="badge bg-warning">Not Found</span>';

        container.innerHTML = `
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-1">Sitemap Status</h6>
                            <small class="text-muted">Current sitemap state</small>
                        </div>
                        ${statusBadge}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-1">Total URLs</h6>
                            <small class="text-muted">Pages in sitemap</small>
                        </div>
                        <span class="badge bg-primary">${data.url_count || 0}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-1">Last Generated</h6>
                            <small class="text-muted">Most recent update</small>
                        </div>
                        <small class="text-muted">${data.last_modified || 'Never'}</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-1">Sitemap URL</h6>
                            <small class="text-muted">Public sitemap location</small>
                        </div>
                        ${data.url ? `<a href="${data.url}" target="_blank" class="btn btn-sm btn-outline-primary">View</a>` : '<span class="text-muted">N/A</span>'}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Generate sitemap
     */
    async generateSitemap() {
        const button = document.getElementById('generateSitemap');
        const originalText = button.innerHTML;

        try {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';

            const settings = {
                include_blog_posts: document.getElementById('includeBlogPosts').checked,
                include_case_studies: document.getElementById('includeCaseStudies').checked,
                include_whitepapers: document.getElementById('includeWhitepapers').checked,
                homepage_priority: document.getElementById('homepagePriority').value,
                blog_priority: document.getElementById('blogPriority').value
            };

            const response = await window.AdZetaApp.apiRequest('/sitemap/generate', {
                method: 'POST',
                body: JSON.stringify({ settings })
            });

            if (response.success) {
                this.showNotification('Sitemap generated successfully!', 'success');
                this.loadSitemapStatus();
            } else {
                this.showNotification(response.message || 'Failed to generate sitemap', 'error');
            }
        } catch (error) {
            console.error('Error generating sitemap:', error);
            this.showNotification('Failed to generate sitemap', 'error');
        } finally {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    /**
     * Load robots.txt content
     */
    async loadRobotsContent() {
        console.log('Loading robots.txt content...');

        try {
            const response = await window.AdZetaApp.apiRequest('/robots/get');
            console.log('Robots.txt response:', response);
            console.log('Response type:', typeof response);
            console.log('Response keys:', Object.keys(response || {}));

            if (response && response.success) {
                const robotsTextarea = document.getElementById('robotsContent');
                if (robotsTextarea) {
                    // Handle different response structures
                    let robotsData;
                    if (response.data) {
                        robotsData = response.data;
                    } else if (response.content !== undefined) {
                        robotsData = response;
                    } else {
                        console.warn('Unexpected response structure:', response);
                        robotsData = { content: '', exists: false };
                    }

                    robotsTextarea.value = robotsData.content || '';
                    this.updateRobotsStatus(robotsData.exists || false);
                } else {
                    console.error('Robots content textarea not found!');
                }
            } else {
                console.warn('Robots.txt request failed:', response?.message || 'Unknown error');
                const robotsTextarea = document.getElementById('robotsContent');
                if (robotsTextarea) {
                    robotsTextarea.value = '';
                }
                this.updateRobotsStatus(false);
            }
        } catch (error) {
            console.error('Error loading robots.txt:', error);
            const robotsTextarea = document.getElementById('robotsContent');
            if (robotsTextarea) {
                robotsTextarea.value = '';
            }
            this.updateRobotsStatus(false);
        }
    }

    /**
     * Update robots.txt status
     */
    updateRobotsStatus(exists) {
        const statusContainer = document.getElementById('robotsCurrentStatus');
        statusContainer.innerHTML = exists ?
            '<span class="badge bg-success">File Exists</span>' :
            '<span class="badge bg-warning">File Missing</span>';
    }

    /**
     * Save robots.txt
     */
    async saveRobots() {
        const button = document.getElementById('saveRobots');
        const originalText = button.innerHTML;
        const content = document.getElementById('robotsContent').value;

        try {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

            const response = await window.AdZetaApp.apiRequest('/robots/save', {
                method: 'POST',
                body: JSON.stringify({ content })
            });

            if (response.success) {
                this.showNotification('Robots.txt saved successfully!', 'success');
                this.updateRobotsStatus(true);
            } else {
                this.showNotification(response.message || 'Failed to save robots.txt', 'error');
            }
        } catch (error) {
            console.error('Error saving robots.txt:', error);
            this.showNotification('Failed to save robots.txt', 'error');
        } finally {
            button.disabled = false;
            button.innerHTML = originalText;
        }
    }

    /**
     * Validate robots.txt
     */
    validateRobots() {
        const content = document.getElementById('robotsContent').value;
        const validationContainer = document.getElementById('robotsValidation');

        const issues = [];
        const lines = content.split('\n');

        // Basic validation
        let hasUserAgent = false;
        let hasSitemap = false;

        lines.forEach((line, index) => {
            const trimmedLine = line.trim();
            if (trimmedLine.toLowerCase().startsWith('user-agent:')) {
                hasUserAgent = true;
            }
            if (trimmedLine.toLowerCase().startsWith('sitemap:')) {
                hasSitemap = true;
            }

            // Check for common issues
            if (trimmedLine && !trimmedLine.startsWith('#') && !trimmedLine.includes(':')) {
                issues.push(`Line ${index + 1}: Invalid syntax - missing colon`);
            }
        });

        if (!hasUserAgent) {
            issues.push('Missing User-agent directive');
        }

        if (!hasSitemap) {
            issues.push('Consider adding Sitemap directive');
        }

        // Display validation results
        if (issues.length === 0) {
            validationContainer.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    Robots.txt validation passed! No issues found.
                </div>
            `;
        } else {
            validationContainer.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Validation Issues:</strong>
                    <ul class="mb-0 mt-2">
                        ${issues.map(issue => `<li>${issue}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        validationContainer.style.display = 'block';
    }

    /**
     * Preview robots.txt
     */
    previewRobots() {
        const content = document.getElementById('robotsContent').value;
        const baseUrl = window.location.origin;

        // Open preview in new window
        const previewWindow = window.open('', '_blank', 'width=600,height=400');
        previewWindow.document.write(`
            <html>
                <head>
                    <title>Robots.txt Preview</title>
                    <style>
                        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
                        .preview { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        .url { color: #666; margin-bottom: 10px; }
                        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow: auto; }
                    </style>
                </head>
                <body>
                    <div class="preview">
                        <div class="url">Preview URL: ${baseUrl}/robots.txt</div>
                        <pre>${content || '# No content'}</pre>
                    </div>
                </body>
            </html>
        `);
    }

    /**
     * Load robots.txt template
     */
    loadRobotsTemplate(type) {
        const templates = {
            basic: `# Basic robots.txt for AdZeta
User-agent: *
Disallow: /adzeta-admin/
Disallow: /api/
Disallow: /private/
Allow: /

Sitemap: ${window.location.origin}/sitemap.xml`,

            marketing: `# Marketing Agency robots.txt for AdZeta
User-agent: *
Disallow: /adzeta-admin/
Disallow: /api/
Disallow: /private/
Disallow: /temp/
Disallow: /cache/
Allow: /blog/
Allow: /case-studies/
Allow: /whitepapers/
Allow: /

# Allow Google to crawl everything
User-agent: Googlebot
Allow: /

# Block specific bots if needed
User-agent: AhrefsBot
Crawl-delay: 10

Sitemap: ${window.location.origin}/sitemap.xml`,

            strict: `# Strict robots.txt for AdZeta
User-agent: *
Disallow: /adzeta-admin/
Disallow: /api/
Disallow: /private/
Disallow: /temp/
Disallow: /cache/
Disallow: /vendor/
Disallow: /*.pdf$
Disallow: /*.doc$
Disallow: /*.docx$
Disallow: /*.zip$
Allow: /blog/
Allow: /case-studies/
Allow: /whitepapers/
Allow: /

# Specific rules for search engines
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

# Block aggressive crawlers
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

Sitemap: ${window.location.origin}/sitemap.xml`
        };

        if (templates[type]) {
            document.getElementById('robotsContent').value = templates[type];
            this.showNotification(`${type.charAt(0).toUpperCase() + type.slice(1)} template loaded`, 'info');
        }
    }

    /**
     * Show robots template dropdown (simple implementation)
     */
    showRobotsTemplateDropdown() {
        // For now, just show a simple prompt to select template
        const templateType = prompt('Select template type:\n1. basic\n2. marketing\n3. strict\n\nEnter template name:');
        if (templateType && ['basic', 'marketing', 'strict'].includes(templateType.toLowerCase())) {
            this.loadRobotsTemplate(templateType.toLowerCase());
        }
    }
}

// Initialize SEO Manager and make it globally available
window.seoManager = new SEOManager();

// Auto-initialize when DOM is ready (but don't render yet - wait for navigation)
document.addEventListener('DOMContentLoaded', function() {
    console.log('SEO Manager ready for initialization');
});
