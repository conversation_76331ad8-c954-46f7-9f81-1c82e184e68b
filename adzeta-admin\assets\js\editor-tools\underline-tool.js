/**
 * Underline Inline Tool for Editor.js
 * Based on official Editor.js documentation
 */

class UnderlineTool {
    static get isInline() {
        return true;
    }

    static get sanitize() {
        return {
            u: {} // Allow <u> tags, clean all attributes
        };
    }

    get state() {
        return this._state;
    }

    set state(state) {
        this._state = state;
        this.button.classList.toggle(this.api.styles.inlineToolButtonActive, state);
    }

    constructor({ api }) {
        this.api = api;
        this.button = null;
        this._state = false;
        this.tag = 'U';
        this.class = null;
    }

    /**
     * Create button for inline toolbar
     */
    render() {
        this.button = document.createElement('button');
        this.button.type = 'button';
        this.button.innerHTML = '<u>U</u>';
        this.button.classList.add(this.api.styles.inlineToolButton);

        return this.button;
    }

    /**
     * Wrap/unwrap selected text with <u> tags
     */
    surround(range) {
        if (this.state) {
            this.unwrap(range);
            return;
        }
        this.wrap(range);
    }

    /**
     * Wrap selection with <u> tag
     */
    wrap(range) {
        const selectedText = range.extractContents();
        const underlineElement = document.createElement(this.tag);

        underlineElement.appendChild(selectedText);
        range.insertNode(underlineElement);

        this.api.selection.expandToTag(underlineElement);
    }

    /**
     * Unwrap <u> tag
     */
    unwrap(range) {
        const underlineElement = this.api.selection.findParentTag(this.tag, this.class);
        const text = range.extractContents();

        underlineElement.remove();
        range.insertNode(text);
    }

    /**
     * Check if text is already underlined
     * @param {Selection} selection - current Selection
     */
    checkState(selection) {
        const underlineElement = this.api.selection.findParentTag(this.tag);
        this.state = !!underlineElement;
    }

    /**
     * Keyboard shortcut
     */
    static get shortcut() {
        return 'CMD+U';
    }
}

// Make available globally
window.UnderlineTool = UnderlineTool;
