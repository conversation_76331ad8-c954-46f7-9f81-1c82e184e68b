/* Pure Bootstrap Tabs - Mobile Responsive */

/* On mobile, stack tabs vertically like accordion */
@media (max-width: 991px) {
    .process-nav.nav-tabs {
        flex-direction: column;
        border-bottom: none;
    }
    
    .process-nav.nav-tabs .nav-item {
        margin-bottom: 8px;
    }
    
    .process-nav.nav-tabs .nav-link {
        border-radius: 8px;
        border: 1px solid rgba(233, 88, 161, 0.1);
        background: white;
        margin-bottom: 0;
        padding: 16px 20px;
        font-weight: 600;
        color: #333;
        text-align: left;
    }
    
    .process-nav.nav-tabs .nav-link.active {
        background: linear-gradient(to right, rgba(233, 88, 161, 0.1), rgba(255, 255, 255, 0.9));
        border-color: #e958a1;
        color: #e958a1;
    }
    
    .process-nav.nav-tabs .nav-link:hover {
        background: rgba(233, 88, 161, 0.05);
        border-color: rgba(233, 88, 161, 0.2);
    }
}
