/* Page Title Gradients CSS */

/* Cover background class */
.cover-background {
    position: relative;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    z-index: 0;
}

/* Professional gradient container */
.page-title-gradient-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    opacity: 0.7;
}

/* Corner gradients */
.corner-gradient {
    position: absolute;
    width: 40%;
    height: 40%;
    border-radius: 50%;
    opacity: 0.5;
    filter: blur(50px);
}

.corner-gradient.top-left {
    top: -10%;
    left: -10%;
    background: radial-gradient(circle, rgba(255, 140, 198, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
}

.corner-gradient.top-right {
    top: -10%;
    right: -10%;
    background: radial-gradient(circle, rgba(143, 118, 245, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
}

.corner-gradient.bottom-left {
    bottom: -10%;
    left: -10%;
    background: radial-gradient(circle, rgba(143, 118, 245, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
}

.corner-gradient.bottom-right {
    bottom: -10%;
    right: -10%;
    background: radial-gradient(circle, rgba(255, 140, 198, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
}

/* Diagonal gradient */
.diagonal-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(31, 33, 42, 0.5) 0%, rgba(31, 33, 42, 0.3) 100%);
    z-index: 0;
}

/* Mesh overlay */
.mesh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/subtle-mesh-pattern.png');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

/* Vignette overlay */
.vignette-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-shadow: inset 0 0 100px rgba(0, 0, 0, 0.5);
    z-index: 0;
}

/* Page title typography */
.page-title-big-typography {
    position: relative;
    padding: 120px 0;
    z-index: 1;
}

.page-title-big-typography .container {
    position: relative;
    z-index: 5;
}

.page-title-extra-small h1 {
    font-size: 18px;
    line-height: 1.2;
}

.page-title-extra-small h2 {
    font-size: 42px;
    line-height: 1.2;
}

.text-base-color {
    color: #ff8cc6;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .page-title-big-typography {
        padding: 100px 0;
    }

    .page-title-extra-small h2 {
        font-size: 36px;
    }
}

@media (max-width: 767px) {
    .page-title-big-typography {
        padding: 80px 0;
    }

    .page-title-extra-small h2 {
        font-size: 30px;
    }
}

@media (max-width: 575px) {
    .page-title-big-typography {
        padding: 60px 0;
    }

    .page-title-extra-small h1 {
        font-size: 16px;
    }

    .page-title-extra-small h2 {
        font-size: 24px;
    }
}
