/**
 * Minimal Workflow Animation and Interaction
 * Clean, modern design with subtle animations
 */

document.addEventListener('DOMContentLoaded', function() {
    initMinimalWorkflow();
});

function initMinimalWorkflow() {
    // Get all workflow cards
    const cards = document.querySelectorAll('.workflow-card-minimal');

    // Add hover effects and interactions
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Bring the card to the front
            this.style.zIndex = '30';

            // Highlight the corresponding connection path
            highlightConnectionPath(this);
        });

        card.addEventListener('mouseleave', function() {
            // Reset z-index
            this.style.zIndex = '10';

            // Reset connection path highlight
            resetConnectionPaths();
        });
    });

    // Add data flow dots
    addDataFlowDots();

    // Make feedback loop interactive
    const feedbackLoop = document.querySelector('.feedback-loop-minimal');
    if (feedbackLoop) {
        feedbackLoop.addEventListener('mouseenter', function() {
            const feedbackPath = document.querySelector('.feedback-path');
            if (feedbackPath) {
                feedbackPath.style.opacity = '1';
                feedbackPath.style.strokeWidth = '2.5';
                feedbackPath.style.strokeDasharray = '6,3';
            }
        });

        feedbackLoop.addEventListener('mouseleave', function() {
            const feedbackPath = document.querySelector('.feedback-path');
            if (feedbackPath) {
                feedbackPath.style.opacity = '0.7';
                feedbackPath.style.strokeWidth = '1.5';
                feedbackPath.style.strokeDasharray = '4,4';
            }
        });
    }
}

function highlightConnectionPath(card) {
    // Get the card's position in the workflow
    const cardIndex = Array.from(document.querySelectorAll('.workflow-card-minimal')).indexOf(card);

    // Get all connection paths
    const paths = document.querySelectorAll('.connection-lines path');

    // Dim all paths first
    paths.forEach(p => p.style.opacity = '0.3');

    // Calculate which paths to highlight based on card index
    // Each card has 3 paths (top, middle, bottom) except the feedback loop
    if (cardIndex < 5) { // 5 segments in the workflow (0-4)
        // Highlight the three paths for this segment
        const topPathIndex = cardIndex;
        const middlePathIndex = cardIndex + 5;
        const bottomPathIndex = cardIndex + 10;

        if (paths[topPathIndex]) {
            paths[topPathIndex].style.opacity = '1';
            paths[topPathIndex].style.strokeWidth = '2.5';
            paths[topPathIndex].style.strokeDasharray = '6,3';
        }

        if (paths[middlePathIndex]) {
            paths[middlePathIndex].style.opacity = '1';
            paths[middlePathIndex].style.strokeWidth = '2.5';
            paths[middlePathIndex].style.strokeDasharray = '6,3';
        }

        if (paths[bottomPathIndex]) {
            paths[bottomPathIndex].style.opacity = '1';
            paths[bottomPathIndex].style.strokeWidth = '2.5';
            paths[bottomPathIndex].style.strokeDasharray = '6,3';
        }
    }

    // Highlight feedback loop if it's the last card
    if (cardIndex === document.querySelectorAll('.workflow-card-minimal').length - 1) {
        const feedbackPath = document.querySelector('.feedback-path');
        if (feedbackPath) {
            feedbackPath.style.opacity = '1';
            feedbackPath.style.strokeWidth = '2.5';
            feedbackPath.style.strokeDasharray = '6,3';
        }
    }
}

function resetConnectionPaths() {
    // Get all connection paths
    const paths = document.querySelectorAll('.connection-lines path');

    // Reset all paths to their original state
    paths.forEach(path => {
        path.style.opacity = '0.7';
        path.style.strokeWidth = '1.5';
        path.style.strokeDasharray = '4,4';
    });
}

function addDataFlowDots() {
    const container = document.querySelector('.workflow-minimal-container');
    if (!container) return;

    // Top line dots
    // Client data dots - top line
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot client-dot';
        dot.style.animation = `flowDot1Top 2s infinite ${i * 1}s`;
        container.appendChild(dot);
    }

    // Adzeta platform dots - top line (first segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot adzeta-dot';
        dot.style.animation = `flowDot2Top 2s infinite ${i * 1 + 0.3}s`;
        container.appendChild(dot);
    }

    // Adzeta platform dots - top line (second segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot adzeta-dot';
        dot.style.animation = `flowDot3Top 2s infinite ${i * 1 + 0.6}s`;
        container.appendChild(dot);
    }

    // Google Ads dots - top line (first segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot google-dot';
        dot.style.animation = `flowDot4Top 2s infinite ${i * 1 + 0.9}s`;
        container.appendChild(dot);
    }

    // Google Ads dots - top line (second segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot results-dot';
        dot.style.animation = `flowDot5Top 2s infinite ${i * 1 + 1.2}s`;
        container.appendChild(dot);
    }

    // Middle line dots
    // Client data dots - middle line
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot client-dot';
        dot.style.animation = `flowDot1 2s infinite ${i * 1 + 0.2}s`;
        container.appendChild(dot);
    }

    // Adzeta platform dots - middle line (first segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot adzeta-dot';
        dot.style.animation = `flowDot2 2s infinite ${i * 1 + 0.5}s`;
        container.appendChild(dot);
    }

    // Adzeta platform dots - middle line (second segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot adzeta-dot';
        dot.style.animation = `flowDot3 2s infinite ${i * 1 + 0.8}s`;
        container.appendChild(dot);
    }

    // Google Ads dots - middle line (first segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot google-dot';
        dot.style.animation = `flowDot4 2s infinite ${i * 1 + 1.1}s`;
        container.appendChild(dot);
    }

    // Google Ads dots - middle line (second segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot results-dot';
        dot.style.animation = `flowDot5 2s infinite ${i * 1 + 1.4}s`;
        container.appendChild(dot);
    }

    // Bottom line dots
    // Client data dots - bottom line
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot client-dot';
        dot.style.animation = `flowDot1Bottom 2s infinite ${i * 1 + 0.4}s`;
        container.appendChild(dot);
    }

    // Adzeta platform dots - bottom line (first segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot adzeta-dot';
        dot.style.animation = `flowDot2Bottom 2s infinite ${i * 1 + 0.7}s`;
        container.appendChild(dot);
    }

    // Adzeta platform dots - bottom line (second segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot adzeta-dot';
        dot.style.animation = `flowDot3Bottom 2s infinite ${i * 1 + 1.0}s`;
        container.appendChild(dot);
    }

    // Google Ads dots - bottom line (first segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot google-dot';
        dot.style.animation = `flowDot4Bottom 2s infinite ${i * 1 + 1.3}s`;
        container.appendChild(dot);
    }

    // Google Ads dots - bottom line (second segment)
    for (let i = 0; i < 2; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot results-dot';
        dot.style.animation = `flowDot5Bottom 2s infinite ${i * 1 + 1.6}s`;
        container.appendChild(dot);
    }

    // Add feedback loop dots
    for (let i = 0; i < 3; i++) {
        const dot = document.createElement('div');
        dot.className = 'data-dot feedback-dot';
        dot.style.animation = `flowFeedback 6s infinite ${i * 2}s`;
        container.appendChild(dot);
    }
}
