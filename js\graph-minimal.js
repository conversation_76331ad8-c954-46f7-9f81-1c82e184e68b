/**
 * Enhanced JavaScript for Graph Component
 * Improved tooltip positioning, curve calculations, and animations
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize the graph
  initGraph();
});

function initGraph() {
  // Get the graph container
  const graphContainer = document.querySelector('.animated-graph-container');
  if (!graphContainer) return;

  // The graph HTML is now directly embedded in the page
  // Set up the interactions and scroll trigger
  setupGraphInteractions();
  setupScrollTrigger();
  
  // Update SVG paths for more convex/domed curves
  updateGraphCurves();
}

function updateGraphCurves() {
  // Get SVG paths
  const adzetaLine = document.querySelector('.adzeta-line');
  const traditionalLine = document.querySelector('.traditional-line');
  const adzetaArea = document.querySelector('.adzeta-area');
  const traditionalArea = document.querySelector('.traditional-area');
  const profitDifferenceArea = document.querySelector('.profit-difference-area');
  
  if (!adzetaLine || !traditionalLine) return;
  
  // Enhanced convex/domed curves for Adzeta line
  const adzetaPath = "M80,380 C140,340 200,280 260,230 " +
                    "C320,180 380,150 440,130 " +
                    "C500,115 560,100 620,90 " +
                    "C680,83 740,80 800,78 " + 
                    "C860,76 920,75 920,75";
  adzetaLine.setAttribute('d', adzetaPath);
  
  // Enhanced convex/domed curves for Traditional line
  const traditionalPath = "M80,380 C140,375 200,370 260,365 " +
                         "C320,360 380,350 440,340 " +
                         "C500,330 560,325 620,320 " +
                         "C680,315 740,310 800,308 " +
                         "C860,306 920,305 920,305";
  traditionalLine.setAttribute('d', traditionalPath);
  
  // Update area paths to match the new line paths
  if (adzetaArea) {
    adzetaArea.setAttribute('d', `${adzetaPath} L920,400 L80,400 Z`);
  }
  
  if (traditionalArea) {
    traditionalArea.setAttribute('d', `${traditionalPath} L920,400 L80,400 Z`);
  }
  
  if (profitDifferenceArea) {
    profitDifferenceArea.setAttribute('d', `${adzetaPath} L920,305 ${traditionalPath.split('M80,380 ')[1].split('').reverse().join('').replace(/C/g, ' C').split('').reverse().join('')} Z`);
  }
  
  // Update data point positions to match the new curves
  updateDataPointPositions();
}

function updateDataPointPositions() {
  // Get data points
  const dataPoints = document.querySelectorAll('.data-point');
  const dataPointContainers = document.querySelectorAll('.data-point-container');
  
  if (!dataPoints.length || !dataPointContainers.length) return;
  
  // New Y positions based on convex curves
  const yPositions = [70, 46, 34, 26, 22, 18];
  
  // Update SVG circle data points
  dataPoints.forEach((point, index) => {
    if (index >= yPositions.length) return;
    
    // Calculate position
    const xPercent = 14 + (index * 13);
    const yPercent = yPositions[index];
    
    // Convert percentage to SVG coordinates (assuming 1000x500 viewBox)
    const cx = 80 + ((920 - 80) * (xPercent / 100));
    const cy = 400 - ((400 - 80) * (yPercent / 100));
    
    // Update circle position
    point.setAttribute('cx', cx);
    point.setAttribute('cy', cy);
  });
  
  // Update HTML data point containers
  dataPointContainers.forEach((container, index) => {
    if (index >= yPositions.length) return;
    
    // Calculate position
    const xPercent = 14 + (index * 13);
    
    // Update container position
    container.style.left = `${xPercent}%`;
    container.style.top = `${yPositions[index]}%`;
  });
}

function setupGraphInteractions() {
  // Get elements
  const graphContainer = document.querySelector('.graph-container');
  const dataPoints = document.querySelectorAll('.data-point-container');
  const dataPointCircles = document.querySelectorAll('.data-point');
  const tooltip = document.querySelector('.graph-tooltip');
  const hoverArea = document.querySelector('.graph-hover-area');
  const valueUnlocked = document.querySelector('.value-unlocked');

  if (!graphContainer || !tooltip || !hoverArea) return;

  // Add tooltip functionality to data points
  dataPoints.forEach((point, index) => {
    point.addEventListener('mouseenter', function(e) {
      // Use mouse position for more accurate tooltip placement
      const x = e.clientX;
      const y = e.clientY;
      
      // Position tooltip using fixed positioning relative to viewport
      tooltip.style.left = `${x}px`;
      tooltip.style.top = `${y - 15}px`;
      tooltip.classList.add('visible');

      // Get data point label
      const label = point.querySelector('.data-point-label');
      if (label) {
        // Extract percentage from label
        const percentage = label.textContent;

        // Calculate month based on position
        const position = parseFloat(point.style.left) / 100;
        const month = Math.round(position * 12);

        // Calculate values based on the reference images
        const improvement = parseInt(percentage.replace('+', '').replace('%', ''));
        const adzetaValue = Math.round(40 + improvement);
        const traditionalValue = 40;  // Traditional stays at baseline
        const revenue = Math.round(improvement * 62.5); // Increased revenue factor

        // Update tooltip content
        tooltip.innerHTML = `
          <div class="tooltip-title">Month ${month}</div>
          <div class="tooltip-value">
            <span class="tooltip-label">Adzeta AI:</span>
            <span class="tooltip-adzeta">${adzetaValue}%</span>
          </div>
          <div class="tooltip-value">
            <span class="tooltip-label">Traditional:</span>
            <span class="tooltip-traditional">${traditionalValue}%</span>
          </div>
          <div class="tooltip-value">
            <span class="tooltip-label">Growth Advantage:</span>
            <span class="tooltip-advantage">${percentage}</span>
          </div>
          <div class="tooltip-value">
            <span class="tooltip-label">Added Revenue:</span>
            <span class="tooltip-revenue">$${revenue.toLocaleString()}</span>
          </div>
          <div class="tooltip-note">Based on $2,500 monthly ad spend</div>
        `;
      }

      // Highlight the corresponding data point circle
      if (dataPointCircles[index]) {
        dataPointCircles[index].setAttribute('r', '7');
        dataPointCircles[index].setAttribute('filter', 'url(#strongGlow)');
      }
      
      // Stop propagation to prevent hover area from catching the event
      e.stopPropagation();
    });

    point.addEventListener('mouseleave', function() {
      tooltip.classList.remove('visible');

      // Reset the data point circle
      if (dataPointCircles[index]) {
        dataPointCircles[index].setAttribute('r', '5');
        dataPointCircles[index].setAttribute('filter', 'url(#glow)');
      }
    });
    
    // Prevent mouseout issues by handling mousemove
    point.addEventListener('mousemove', function(e) {
      // Update tooltip position on move for more fluid experience
      const x = e.clientX;
      const y = e.clientY;
      
      tooltip.style.left = `${x}px`;
      tooltip.style.top = `${y - 15}px`;
      
      // Stop propagation
      e.stopPropagation();
    });
  });

  // Add hover functionality to graph area with improved positioning
  hoverArea.addEventListener('mousemove', function(e) {
    // Only show tooltip if not already showing for a data point
    if (tooltip.classList.contains('visible')) return;

    // Calculate position in graph coordinates
    const rect = hoverArea.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width;
    const y = 1 - (e.clientY - rect.top) / rect.height;

    if (x < 0 || x > 1 || y < 0 || y > 1) return;

    // Calculate values based on the improved curves
    const month = Math.round(x * 12);
    
    // Convex curve calculation for Adzeta (more dome-shaped)
    const adzetaBase = 40;
    const adzetaGrowth = Math.pow(x, 0.7) * 40; // Exponential growth with flattening
    const adzetaValue = Math.round((adzetaBase + adzetaGrowth) * y);
    
    // Traditional growth is more linear but slightly curved
    const traditionalBase = 40;
    const traditionalGrowth = x * 5; // Small linear growth
    const traditionalValue = Math.round((traditionalBase + traditionalGrowth) * y);
    
    const improvement = adzetaValue - traditionalValue;
    const revenue = Math.round(improvement * 62.5); // Increased revenue factor

    // Update tooltip content
    tooltip.innerHTML = `
      <div class="tooltip-title">Month ${month}</div>
      <div class="tooltip-value">
        <span class="tooltip-label">Adzeta AI:</span>
        <span class="tooltip-adzeta">${adzetaValue}%</span>
      </div>
      <div class="tooltip-value">
        <span class="tooltip-label">Traditional:</span>
        <span class="tooltip-traditional">${traditionalValue}%</span>
      </div>
      <div class="tooltip-value">
        <span class="tooltip-label">Growth Advantage:</span>
        <span class="tooltip-advantage">+${improvement}%</span>
      </div>
      <div class="tooltip-value">
        <span class="tooltip-label">Added Revenue:</span>
        <span class="tooltip-revenue">$${revenue.toLocaleString()}</span>
      </div>
      <div class="tooltip-note">Based on $2,500 monthly ad spend</div>
    `;

    // Position tooltip using fixed positioning
    tooltip.style.left = `${e.clientX}px`;
    tooltip.style.top = `${e.clientY - 15}px`;
    tooltip.classList.add('visible');
  });

  // Fix tooltip disappearance issues
  hoverArea.addEventListener('mouseleave', function() {
    // Small delay to prevent flicker
    setTimeout(() => {
      if (!tooltip.matches(':hover')) {
        tooltip.classList.remove('visible');
      }
    }, 50);
  });
  
  // Add document-level event listener to hide tooltip when clicking elsewhere
  document.addEventListener('click', function(e) {
    if (!tooltip.contains(e.target) && !hoverArea.contains(e.target)) {
      tooltip.classList.remove('visible');
    }
  });
}

function setupScrollTrigger() {
  const graphContainer = document.querySelector('.graph-container');
  if (!graphContainer) {
    console.error('Graph container not found');
    return;
  }

  // Create an intersection observer to trigger animations when the graph is visible
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // Start the animations
        graphContainer.classList.add('animate');

        // Unobserve after triggering
        observer.unobserve(graphContainer);

        console.log('Graph animation started');
      }
    });
  }, {
    // Improved threshold values
    threshold: window.innerWidth < 768 ? 0.3 : 0.2,
    // Add root margin to delay animation until more of the graph is visible
    rootMargin: window.innerWidth < 768 ? '-5% 0px' : '0px'
  });

  // Observe the container
  observer.observe(graphContainer);
}

// Add window resize handler to recalculate positions
window.addEventListener('resize', function() {
  // Throttle resize events
  if (this.resizeTimeout) clearTimeout(this.resizeTimeout);
  
  this.resizeTimeout = setTimeout(() => {
    updateDataPointPositions();
  }, 200);
});