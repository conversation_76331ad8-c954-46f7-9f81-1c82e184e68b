/**
 * AdZeta Admin Panel - SEO Analyzer Module
 * Real-time SEO analysis and optimization suggestions
 */

window.AdZetaSEOAnalyzer = {
    // SEO analysis data
    data: {
        currentAnalysis: null,
        isAnalyzing: false
    },

    // Initialize SEO analyzer
    init() {
        console.log('SEO Analyzer module initialized');
    },

    // Analyze content for SEO
    async analyzeContent(content) {
        if (this.data.isAnalyzing) return this.data.currentAnalysis;

        try {
            this.data.isAnalyzing = true;

            // Simulate API call with comprehensive analysis
            await window.AdZetaApp.delay(1000);

            const analysis = this.performAnalysis(content);
            this.data.currentAnalysis = analysis;

            return analysis;
        } catch (error) {
            console.error('SEO analysis failed:', error);
            throw error;
        } finally {
            this.data.isAnalyzing = false;
        }
    },

    // Perform comprehensive SEO analysis
    performAnalysis(content) {
        const analysis = {
            overall_score: 0,
            scores: {
                title: 0,
                meta_description: 0,
                content_length: 0,
                keyword_density: 0,
                headings: 0,
                readability: 0,
                images: 0,
                links: 0
            },
            recommendations: [],
            details: {}
        };

        // Analyze title
        analysis.scores.title = this.analyzeTitle(content);
        
        // Analyze meta description
        analysis.scores.meta_description = this.analyzeMetaDescription(content);
        
        // Analyze content length
        analysis.scores.content_length = this.analyzeContentLength(content);
        
        // Analyze keyword density
        analysis.scores.keyword_density = this.analyzeKeywordDensity(content);
        
        // Analyze headings structure
        analysis.scores.headings = this.analyzeHeadings(content);
        
        // Analyze readability
        analysis.scores.readability = this.analyzeReadability(content);
        
        // Analyze images
        analysis.scores.images = this.analyzeImages(content);
        
        // Analyze links
        analysis.scores.links = this.analyzeLinks(content);

        // Calculate overall score
        analysis.overall_score = this.calculateOverallScore(analysis.scores);

        // Generate recommendations
        analysis.recommendations = this.generateRecommendations(analysis);

        return analysis;
    },

    // Analyze title optimization
    analyzeTitle(content) {
        const title = content.title || '';
        const metaTitle = content.meta_title || title;
        const focusKeyword = content.focus_keyword || '';

        let score = 0;
        const details = {};

        // Title length check
        if (metaTitle.length >= 30 && metaTitle.length <= 60) {
            score += 30;
            details.length = 'optimal';
        } else if (metaTitle.length > 0) {
            score += 15;
            details.length = metaTitle.length < 30 ? 'too_short' : 'too_long';
        }

        // Focus keyword in title
        if (focusKeyword && metaTitle.toLowerCase().includes(focusKeyword.toLowerCase())) {
            score += 40;
            details.keyword_included = true;
        }

        // Title uniqueness and appeal
        if (metaTitle.length > 0) {
            score += 30;
            details.has_title = true;
        }

        return Math.min(score, 100);
    },

    // Analyze meta description
    analyzeMetaDescription(content) {
        const metaDescription = content.meta_description || '';
        const focusKeyword = content.focus_keyword || '';

        let score = 0;

        // Length check
        if (metaDescription.length >= 120 && metaDescription.length <= 160) {
            score += 40;
        } else if (metaDescription.length > 0) {
            score += 20;
        }

        // Focus keyword in description
        if (focusKeyword && metaDescription.toLowerCase().includes(focusKeyword.toLowerCase())) {
            score += 30;
        }

        // Call-to-action or compelling language
        const ctaWords = ['learn', 'discover', 'find out', 'get', 'download', 'read more'];
        if (ctaWords.some(word => metaDescription.toLowerCase().includes(word))) {
            score += 30;
        }

        return Math.min(score, 100);
    },

    // Analyze content length
    analyzeContentLength(content) {
        const text = this.extractTextContent(content.content || '');
        const wordCount = this.countWords(text);

        let score = 0;

        if (wordCount >= 300 && wordCount <= 2500) {
            score = 100;
        } else if (wordCount >= 200) {
            score = 70;
        } else if (wordCount >= 100) {
            score = 40;
        } else if (wordCount > 0) {
            score = 20;
        }

        return score;
    },

    // Analyze keyword density
    analyzeKeywordDensity(content) {
        const focusKeyword = content.focus_keyword || '';
        if (!focusKeyword) return 0;

        const text = this.extractTextContent(content.content || '').toLowerCase();
        const totalWords = this.countWords(text);
        const keywordOccurrences = (text.match(new RegExp(focusKeyword.toLowerCase(), 'g')) || []).length;
        
        if (totalWords === 0) return 0;

        const density = (keywordOccurrences / totalWords) * 100;

        let score = 0;
        if (density >= 0.5 && density <= 2.5) {
            score = 100;
        } else if (density >= 0.1 && density <= 4) {
            score = 70;
        } else if (density > 0) {
            score = 40;
        }

        return score;
    },

    // Analyze headings structure
    analyzeHeadings(content) {
        const text = content.content || '';
        const h1Count = (text.match(/<h1[^>]*>/gi) || []).length;
        const h2Count = (text.match(/<h2[^>]*>/gi) || []).length;
        const h3Count = (text.match(/<h3[^>]*>/gi) || []).length;

        let score = 0;

        // Should have proper heading structure
        if (h2Count > 0) {
            score += 50;
        }

        // Should not have multiple H1s
        if (h1Count <= 1) {
            score += 25;
        }

        // Good heading hierarchy
        if (h2Count > 0 && h3Count >= 0) {
            score += 25;
        }

        return score;
    },

    // Analyze readability
    analyzeReadability(content) {
        const text = this.extractTextContent(content.content || '');
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const words = this.countWords(text);

        if (sentences.length === 0 || words === 0) return 0;

        const avgWordsPerSentence = words / sentences.length;
        
        let score = 0;
        if (avgWordsPerSentence >= 10 && avgWordsPerSentence <= 20) {
            score = 100;
        } else if (avgWordsPerSentence >= 8 && avgWordsPerSentence <= 25) {
            score = 80;
        } else if (avgWordsPerSentence > 0) {
            score = 60;
        }

        return score;
    },

    // Analyze images
    analyzeImages(content) {
        const text = content.content || '';
        const images = (text.match(/<img[^>]*>/gi) || []).length;
        const wordCount = this.countWords(this.extractTextContent(text));

        let score = 0;

        // Should have images for longer content
        if (wordCount > 500 && images > 0) {
            score += 50;
        } else if (wordCount > 200 && images > 0) {
            score += 30;
        }

        // Alt text check (simplified)
        const imagesWithAlt = (text.match(/<img[^>]*alt=[^>]*>/gi) || []).length;
        if (images > 0 && imagesWithAlt === images) {
            score += 50;
        } else if (imagesWithAlt > 0) {
            score += 25;
        }

        return Math.min(score, 100);
    },

    // Analyze links
    analyzeLinks(content) {
        const text = content.content || '';
        const internalLinks = (text.match(/<a[^>]*href=[^>]*>/gi) || []).length;
        const wordCount = this.countWords(this.extractTextContent(text));

        let score = 0;

        // Should have internal links for longer content
        if (wordCount > 500 && internalLinks > 0) {
            score += 60;
        } else if (wordCount > 200 && internalLinks > 0) {
            score += 40;
        }

        // Good link ratio
        const linkRatio = internalLinks / (wordCount / 100);
        if (linkRatio >= 1 && linkRatio <= 5) {
            score += 40;
        } else if (linkRatio > 0) {
            score += 20;
        }

        return Math.min(score, 100);
    },

    // Calculate overall SEO score
    calculateOverallScore(scores) {
        const weights = {
            title: 0.2,
            meta_description: 0.15,
            content_length: 0.15,
            keyword_density: 0.15,
            headings: 0.1,
            readability: 0.1,
            images: 0.075,
            links: 0.075
        };

        let totalScore = 0;
        for (const [metric, score] of Object.entries(scores)) {
            totalScore += score * (weights[metric] || 0);
        }

        return Math.round(totalScore);
    },

    // Generate SEO recommendations
    generateRecommendations(analysis) {
        const recommendations = [];

        // Title recommendations
        if (analysis.scores.title < 70) {
            recommendations.push('Optimize your title length (30-60 characters) and include your focus keyword');
        }

        // Meta description recommendations
        if (analysis.scores.meta_description < 70) {
            recommendations.push('Write a compelling meta description (120-160 characters) with your focus keyword');
        }

        // Content length recommendations
        if (analysis.scores.content_length < 70) {
            recommendations.push('Increase your content length to at least 300 words for better SEO');
        }

        // Keyword density recommendations
        if (analysis.scores.keyword_density < 70) {
            recommendations.push('Include your focus keyword naturally throughout the content (0.5-2.5% density)');
        }

        // Headings recommendations
        if (analysis.scores.headings < 70) {
            recommendations.push('Use proper heading structure (H2, H3) to organize your content');
        }

        // Readability recommendations
        if (analysis.scores.readability < 70) {
            recommendations.push('Improve readability by using shorter sentences (10-20 words per sentence)');
        }

        // Images recommendations
        if (analysis.scores.images < 70) {
            recommendations.push('Add relevant images with descriptive alt text to enhance your content');
        }

        // Links recommendations
        if (analysis.scores.links < 70) {
            recommendations.push('Include internal links to related content on your website');
        }

        return recommendations.slice(0, 5); // Limit to top 5 recommendations
    },

    // Extract text content from HTML
    extractTextContent(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    },

    // Count words in text
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    },

    // Get SEO score color class
    getScoreColorClass(score) {
        if (score >= 80) return 'text-success';
        if (score >= 60) return 'text-info';
        if (score >= 40) return 'text-warning';
        return 'text-danger';
    },

    // Get SEO score description
    getScoreDescription(score) {
        if (score >= 90) return 'Excellent SEO optimization';
        if (score >= 80) return 'Good SEO optimization';
        if (score >= 60) return 'Needs improvement';
        if (score >= 40) return 'Poor SEO optimization';
        return 'Very poor SEO optimization';
    },

    // Get current analysis
    getCurrentAnalysis() {
        return this.data.currentAnalysis;
    },

    // Clear analysis data
    clearAnalysis() {
        this.data.currentAnalysis = null;
    }
};
