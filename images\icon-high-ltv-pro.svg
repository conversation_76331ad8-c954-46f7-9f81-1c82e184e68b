<?xml version="1.0" encoding="UTF-8"?>
<svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>High-LTV Customer Identification</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#f45888" offset="0%"></stop>
            <stop stop-color="#ff8cc6" offset="100%"></stop>
        </linearGradient>
        <filter x="-25%" y="-25%" width="150%" height="150%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Subtle Glow -->
        <circle cx="18" cy="18" r="12" fill="url(#linearGradient-1)" opacity="0.1" filter="url(#filter-2)"></circle>
        
        <!-- Main Icon -->
        <g transform="translate(4.000000, 4.000000)" stroke="url(#linearGradient-1)" stroke-width="1.5">
            <!-- Person Circle -->
            <circle cx="14" cy="9" r="6.5"></circle>
            
            <!-- Person Body -->
            <path d="M14,17 C8.5,17 4,21.5 4,27" stroke-linecap="round"></path>
            <path d="M14,17 C19.5,17 24,21.5 24,27" stroke-linecap="round"></path>
            
            <!-- Star/Badge -->
            <circle cx="22" cy="6" r="3" stroke-width="1.5"></circle>
            <path d="M22,4.5 L22,7.5" stroke-linecap="round"></path>
            <path d="M20.5,6 L23.5,6" stroke-linecap="round"></path>
            
            <!-- Graph Line -->
            <path d="M3,22 L7,18 L11,20 L15,16 L19,18 L23,14" stroke-linecap="round" stroke-linejoin="round"></path>
        </g>
    </g>
</svg>
