<?php
    // WordPress-Inspired Centralized Frontend Cache Integration
    require_once __DIR__ . '/adzeta-admin/src/Cache/FrontendCacheManager.php';

    // Initialize cache manager
    $cacheManager = new \AdZetaAdmin\Cache\FrontendCacheManager();

    // Get pagination and filter parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $category = $_GET['category'] ?? null;
    $tag = $_GET['tag'] ?? null;

    // Try to serve cached content first (BLAZING FAST!)
    $cachedContent = $cacheManager->getBlogListCache($page, $category, $tag);
    if ($cachedContent !== false) {
        // Content served with optimized headers and gzip compression
        echo $cachedContent;
        exit;
    }

    // Cache miss - generate content dynamically
    header('X-Cache: MISS');
    header('X-Cache-Reason: Generating fresh content');

    // Start output buffering to capture generated content for caching
    ob_start();

    // Initialize the application
    require_once 'bootstrap.php';
    require_once 'includes/BlogDatabase.php';

    // Get pagination parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $category = $_GET['category'] ?? null;
    $search = $_GET['search'] ?? null;
    $postsPerPage = 12;

    // Get blog posts from database
    $blogPosts = getBlogPosts([
        'page' => $page,
        'limit' => $postsPerPage,
        'category' => $category,
        'search' => $search
    ]);

    // Get total count for pagination
    $totalPosts = getBlogDatabase()->getBlogPostsCount([
        'category' => $category,
        'search' => $search
    ]);

    $totalPages = ceil($totalPosts / $postsPerPage);

    // Get categories for filter
    $categories = getBlogCategories();

    // Set page-specific SEO data
    $pageTitle = 'Performance Marketing Blog - AdZeta';
    $pageDescription = 'Latest insights on e-commerce growth, AI marketing, value-based bidding, and performance optimization strategies.';

    if ($category) {
        $categoryName = '';
        foreach ($categories as $cat) {
            if ($cat['slug'] === $category) {
                $categoryName = $cat['name'];
                break;
            }
        }
        if ($categoryName) {
            $pageTitle = $categoryName . ' - AdZeta Blog';
            $pageDescription = "Latest {$categoryName} insights and strategies for e-commerce growth.";
        }
    }

    if ($search) {
        $pageTitle = "Search Results for '{$search}' - AdZeta Blog";
        $pageDescription = "Search results for '{$search}' in our performance marketing blog.";
    }

    $pageData = [
        'title' => $pageTitle,
        'description' => $pageDescription,
        'keywords' => 'performance marketing blog, e-commerce growth, AI marketing, value-based bidding, customer lifetime value, marketing attribution',
        'canonical' => BASE_URL . 'blog/' . ($category ? "category/{$category}/" : '') . ($page > 1 ? "?page={$page}" : ''),
        'og_image' => BASE_URL . 'images/blog-og-image.jpg'
    ];

    include 'header.php';
?>

<!-- start page title -->
<section class="page-title-big-typography ipad-top-space-margin" data-parallax-background-ratio="0.5" aria-label="Blog header section">
    <div class="professional-gradient-container">
        <div class="corner-gradient top-left"></div>
        <div class="corner-gradient top-right"></div>
        <div class="corner-gradient bottom-left"></div>
        <div class="corner-gradient bottom-right"></div>
        <div class="diagonal-gradient"></div>
        <div class="vignette-overlay"></div>
    </div>
    <div class="container">
        <div class="row align-items-center justify-content-center small-screen">
            <div class="col-xl-6 col-lg-8 col-md-10 position-relative text-center">
                <h1 class="mb-20px text-white ls-minus-2px fw-600 text-shadow-double-large">
                    <?php if ($search): ?>
                        Search Results
                    <?php elseif ($category): ?>
                        <?= htmlspecialchars($categoryName ?? 'Category') ?>
                    <?php else: ?>
                        Latest Articles
                    <?php endif; ?>
                </h1>
                <p class="mb-0 alt-font text-white opacity-8 fw-400 ls-1px fs-20 mb-15">
                    <?php if ($search): ?>
                        Results for "<?= htmlspecialchars($search) ?>" (<?= $totalPosts ?> found)
                    <?php elseif ($category): ?>
                        Latest <?= htmlspecialchars($categoryName ?? 'category') ?> insights and strategies
                    <?php else: ?>
                        Discover our latest insights on e-commerce growth, AI marketing, and value-based bidding strategies
                    <?php endif; ?>
                </p>
                <div class="mt-auto justify-content-center breadcrumb breadcrumb-style-01 fs-15 text-white">
                    <ul>
                        <li><a href="<?= BASE_URL ?>" class="text-white">Home</a></li>
                        <li><a href="<?= BASE_URL ?>blog/" class="text-white">Blog</a></li>
                        <?php if ($category): ?>
                        <li><span class="opacity-7"><?= htmlspecialchars($categoryName ?? 'Category') ?></span></li>
                        <?php elseif ($search): ?>
                        <li><span class="opacity-7">Search</span></li>
                        <?php else: ?>
                        <li><span class="opacity-7">All Posts</span></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end page title -->

<!-- start blog filters -->
<section class="pt-4 pb-2">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center flex-wrap mb-4">
                    <!-- Category Filter -->
                    <div class="blog-filters">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <?= $category ? htmlspecialchars($categoryName ?? 'Category') : 'All Categories' ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= BASE_URL ?>blog/">All Categories</a></li>
                                <?php foreach ($categories as $cat): ?>
                                <li>
                                    <a class="dropdown-item" href="<?= BASE_URL ?>blog/category/<?= $cat['slug'] ?>/">
                                        <?= htmlspecialchars($cat['name']) ?> (<?= $cat['post_count'] ?>)
                                    </a>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>

                    <!-- Search Form -->
                    <div class="blog-search">
                        <form method="GET" action="<?= BASE_URL ?>blog/" class="d-flex">
                            <?php if ($category): ?>
                            <input type="hidden" name="category" value="<?= htmlspecialchars($category) ?>">
                            <?php endif; ?>
                            <input type="search" name="search" class="form-control me-2"
                                   placeholder="Search articles..."
                                   value="<?= htmlspecialchars($search ?? '') ?>">
                            <button type="submit" class="btn btn-primary">Search</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end blog filters -->

<!-- start blog section -->
<section class="pt-0 ps-10 pe-10 xl-ps-2 xl-pe-2 sm-mx-0">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <?php if (empty($blogPosts)): ?>
                <!-- No posts found -->
                <div class="text-center py-5">
                    <h3>No articles found</h3>
                    <p class="text-muted">
                        <?php if ($search): ?>
                            No articles match your search for "<?= htmlspecialchars($search) ?>".
                        <?php elseif ($category): ?>
                            No articles found in this category.
                        <?php else: ?>
                            No articles have been published yet.
                        <?php endif; ?>
                    </p>
                    <a href="<?= BASE_URL ?>blog/" class="btn btn-primary">View All Articles</a>
                </div>
                <?php else: ?>
                <!-- Blog posts grid -->
                <ul class="blog-masonry blog-wrapper grid-loading grid grid-4col xl-grid-3col lg-grid-3col md-grid-2col sm-grid-2col xs-grid-1col gutter-extra-large">
                    <li class="grid-sizer"></li>

                    <?php foreach ($blogPosts as $post): ?>
                    <!-- start blog item -->
                    <li class="grid-item">
                        <div class="card border-0 border-radius-4px overflow-hidden box-shadow-quadruple-large box-shadow-quadruple-large-hover">
                            <div class="card-top d-flex align-items-center">
                                <div class="author-avatar">
                                    <img src="<?= BASE_URL ?>images/case-studies/Natalie-Brooks-adzeta.jpg"
                                         class="avtar rounded-circle"
                                         alt="<?= htmlspecialchars($post['first_name'] . ' ' . $post['last_name']) ?>"
                                         width="40" height="40">
                                </div>
                                <span class="fs-15">
                                    By <span class="text-dark-gray fw-600"><?= htmlspecialchars($post['first_name'] . ' ' . $post['last_name']) ?></span>
                                    <span class="text-medium-gray fs-13">Growth Marketing Lead</span>
                                </span>
                                <div class="like-count ms-auto fs-13">
                                    <a href="<?= BASE_URL ?>blog/<?= $post['slug'] ?>">
                                        <img src="<?= BASE_URL ?>images/custom-arrow.svg" alt="Arrow"
                                             style="width: 11px; height: 11px; margin-left: 6px; transition: transform 0.2s ease;"
                                             data-no-retina="">
                                    </a>
                                </div>
                            </div>

                            <?php if ($post['featured_image']): ?>
                            <div class="blog-image position-relative overflow-hidden">
                                <a href="<?= BASE_URL ?>blog/<?= $post['slug'] ?>">
                                    <img src="<?= BASE_URL . ltrim($post['featured_image'], '/') ?>"
                                         alt="<?= htmlspecialchars($post['title']) ?>" />
                                </a>
                                <?php if ($post['category_name']): ?>
                                <div class="blog-categories">
                                    <a href="<?= BASE_URL ?>blog/category/<?= $post['category_slug'] ?>/"
                                       class="categories-btn bg-white text-dark-gray text-uppercase alt-font fw-600">
                                        <?= htmlspecialchars($post['category_name']) ?>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>

                            <div class="card-body p-0">
                                <div class="post-content ps-13 pe-13 pt-11 pb-11">
                                    <h3 class="mb-10px fw-500 fs-17 lh-26 fw-600 text-dark-gray d-inline-block">
                                        <a href="<?= BASE_URL ?>blog/<?= $post['slug'] ?>" class="text-dark-gray">
                                            <?= htmlspecialchars($post['title']) ?>
                                        </a>
                                    </h3>
                                    <p class="mb-10px">
                                        <?= htmlspecialchars(truncate($post['excerpt'], 150)) ?>
                                    </p>
                                    <a href="<?= BASE_URL ?>blog/<?= $post['slug'] ?>"
                                       class="blog-date fs-14 text-medium-gray text-dark-gray">
                                        <i class="feather icon-feather-calendar text-dark-gray fs-15"></i>
                                        <?= formatDate($post['published_at'], 'j F Y') ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </li>
                    <!-- end blog item -->
                    <?php endforeach; ?>
                </ul>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="col-12 mt-4 d-flex justify-content-center">
                <ul class="pagination pagination-style-01 fs-13 fw-500 mb-0">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?= BASE_URL ?>blog/<?= $category ? "category/{$category}/" : '' ?>?page=<?= $page - 1 ?><?= $search ? "&search=" . urlencode($search) : '' ?>">
                            <i class="feather icon-feather-arrow-left fs-18 d-xs-none"></i>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="<?= BASE_URL ?>blog/<?= $category ? "category/{$category}/" : '' ?>?page=<?= $i ?><?= $search ? "&search=" . urlencode($search) : '' ?>">
                            <?= str_pad($i, 2, '0', STR_PAD_LEFT) ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?= BASE_URL ?>blog/<?= $category ? "category/{$category}/" : '' ?>?page=<?= $page + 1 ?><?= $search ? "&search=" . urlencode($search) : '' ?>">
                            <i class="feather icon-feather-arrow-right fs-18 d-xs-none"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<!-- end blog section -->

<?php include 'footer.php'; ?>

<?php
    // Cache the generated content for blazing fast future requests
    $generatedContent = ob_get_contents();
    if ($generatedContent && $cacheManager->isEnabled()) {
        $cacheManager->cacheBlogList($generatedContent, $page, $category, $tag);
    }

    // End output buffering and send content to browser
    ob_end_flush();
?>
