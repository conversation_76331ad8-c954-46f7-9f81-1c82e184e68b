  // Synchronized counter and graph animations
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up animation...');
            
            // Set up intersection observer for scroll-triggered animation
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        console.log('Metrics section is visible, starting animations...');
                        // Start both counter and graph animations together
                        animateCounters();
                        animateGraphs();
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.3,
                rootMargin: '0px'
            });

            const metricsSection = document.querySelector('.metrics-section');
            if (metricsSection) {
                observer.observe(metricsSection);
            }

            function animateCounters() {
                console.log('Starting counter animation...');
                const counters = document.querySelectorAll('.counter-number');
                
                counters.forEach((counter, index) => {
                    const target = parseInt(counter.getAttribute('data-target'));
                    const duration = 2000;
                    const delay = index * 200;
                    
                    setTimeout(() => {
                        let startValue = 0;
                        const increment = target / (duration / 16);
                        
                        function animate() {
                            startValue += increment;
                            
                            if (startValue >= target) {
                                counter.textContent = target;
                            } else {
                                counter.textContent = Math.floor(startValue);
                                requestAnimationFrame(animate);
                            }
                        }
                        
                        animate();
                    }, delay);
                });
            }

            function animateGraphs() {
                console.log('Starting graph animations...');
                const lines = document.querySelectorAll('.metric-graph .line');
                const areas = document.querySelectorAll('.metric-graph .area');
                
                lines.forEach((line, index) => {
                    const delay = index * 200; // Same stagger as counters
                    
                    setTimeout(() => {
                        // Animate line drawing
                        line.style.transition = 'stroke-dashoffset 1.5s ease-out';
                        line.style.strokeDashoffset = '0';
                        
                        // Fade in area after line animation
                        setTimeout(() => {
                            if (areas[index]) {
                                areas[index].style.opacity = '0.8';
                            }
                        }, 800);
                    }, delay + 300); // Start graphs slightly after counters
                });
            }
            
            // Manual trigger for testing
            window.testAnimations = function() {
                animateCounters();
                animateGraphs();
            };
        });