# AdZeta AI Integration - Google Gemini API

## Overview

This document describes the comprehensive Google Gemini AI integration for the AdZeta admin panel. The system provides AI-powered content generation, SEO optimization, and writing assistance throughout the admin interface.

## Features

### 🤖 Core AI Capabilities
- **Title Generation**: AI-powered blog post title suggestions
- **Meta Description Generation**: SEO-optimized meta descriptions
- **Tag Suggestions**: Relevant content tags for better discoverability
- **SEO Analysis**: Comprehensive content analysis and recommendations
- **Content Writing Assistance**: AI-powered content improvement suggestions

### 🔧 Technical Features
- **Multiple API Key Management**: Automatic failover between API keys
- **JWT Authentication Integration**: Secure API access with existing auth system
- **Modular Architecture**: Easy integration into any admin panel component
- **Production-Ready**: Robust error handling and rate limiting
- **Responsive UI**: Clean, modern interface matching existing design

## Installation

### 1. Run Setup Script
```bash
php adzeta-admin/setup-ai-integration.php
```

This script will:
- Create necessary database tables
- Insert default AI settings
- Set up usage logging and caching tables

### 2. Configure API Keys
1. Go to **Admin Panel > AI Assistant**
2. Click **Add API Key**
3. Enter your Google Gemini API key(s)
4. Test the connection

### 3. Get Google Gemini API Keys
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Copy the key to the AI settings

## File Structure

```
adzeta-admin/
├── src/
│   ├── API/
│   │   └── AIController.php          # AI API endpoints
│   └── Services/
│       └── GeminiAIService.php      # Core Gemini API service
├── assets/
│   ├── js/modules/
│   │   ├── ai-assistant.js          # Global AI assistant
│   │   └── ai-settings.js           # AI settings management
│   └── css/
│       └── admin.css                # AI-specific styles
├── setup-ai-integration.php         # Database setup script
└── AI_INTEGRATION_README.md         # This file
```

## API Endpoints

### Authentication Required
All AI endpoints require valid JWT authentication.

### Available Endpoints

#### `POST /ai/test-connection`
Test API connection and key validity.

#### `POST /ai/generate-titles`
Generate blog post title suggestions.
```json
{
  "topic": "Digital Marketing Trends",
  "keywords": ["SEO", "content marketing"],
  "count": 5
}
```

#### `POST /ai/generate-meta-description`
Generate SEO-optimized meta descriptions.
```json
{
  "title": "Blog Post Title",
  "content": "Blog post content...",
  "keywords": ["keyword1", "keyword2"]
}
```

#### `POST /ai/generate-tags`
Generate relevant content tags.
```json
{
  "title": "Blog Post Title",
  "content": "Blog post content...",
  "maxTags": 10
}
```

#### `POST /ai/analyze-seo`
Analyze content for SEO optimization.
```json
{
  "title": "Blog Post Title",
  "content": "Blog post content...",
  "metaDescription": "Meta description...",
  "focusKeyword": "main keyword"
}
```

#### `GET /ai/settings`
Get current AI configuration.

#### `POST /ai/settings`
Update AI settings and API keys.

## Usage

### In Blog Post Editor

The AI assistant is automatically integrated into the blog post editor:

1. **Title Generation**: Click the robot icon next to the title field
2. **Meta Description**: Click the robot icon next to the meta description field
3. **AI Assistant Panel**: Use the dedicated AI panel in the sidebar
4. **Keyboard Shortcut**: Press `Ctrl+Shift+A` to open AI assistant

### Global AI Assistant

Access AI features from anywhere in the admin panel:
- Use `[data-ai-action]` attributes on buttons
- Call `window.AdZetaAI.showAIAssistant()` programmatically

### Example Integration

```html
<!-- Add AI button to any form field -->
<div class="input-group">
    <input type="text" class="form-control" id="title">
    <button class="btn btn-outline-primary" 
            data-ai-action="generate-title" 
            data-ai-target="my-form">
        <i class="fas fa-robot"></i>
    </button>
</div>
```

## Configuration

### AI Settings

Access via **Admin Panel > AI Assistant**:

- **API Keys**: Add multiple keys for automatic failover
- **Temperature**: Control creativity (0-1, higher = more creative)
- **Max Tokens**: Maximum response length
- **Auto-Suggestions**: Enable/disable automatic suggestions
- **SEO Analysis**: Enable/disable SEO recommendations

### Database Settings

All settings are stored in the `settings` table with keys prefixed by `ai_` or `gemini_`.

## Security

### API Key Protection
- API keys are encrypted in the database
- Only last 4 characters shown in UI
- Secure transmission via HTTPS
- JWT-based authentication for all requests

### Rate Limiting
- Automatic failover between API keys
- Built-in retry logic with exponential backoff
- Usage logging for monitoring

## Troubleshooting

### Common Issues

#### "No API keys configured"
- Add at least one active API key in AI settings
- Ensure the key is valid and has quota remaining

#### "Connection failed"
- Check internet connectivity
- Verify API key is correct
- Ensure Gemini API is enabled in Google Cloud Console

#### "Quota exceeded"
- Add additional API keys for automatic failover
- Check usage limits in Google AI Studio
- Consider upgrading your Gemini API plan

### Debug Mode

Enable debug mode by setting `ADZETA_DEBUG = true` in your environment configuration.

## Performance

### Caching
- AI responses are cached to reduce API calls
- Cache duration configurable in settings
- Automatic cache invalidation

### Optimization
- Lazy loading of AI modules
- Debounced API requests
- Efficient token usage

## Extending the System

### Adding New AI Features

1. **Backend**: Add new methods to `GeminiAIService.php`
2. **API**: Add new endpoints to `AIController.php`
3. **Frontend**: Add new actions to `ai-assistant.js`
4. **UI**: Add buttons with `data-ai-action` attributes

### Custom Prompts

Modify prompts in `GeminiAIService.php` methods:
- `generateTitleSuggestions()`
- `generateMetaDescription()`
- `generateTags()`
- `analyzeSEO()`

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the browser console for JavaScript errors
3. Check the server logs for PHP errors
4. Ensure all dependencies are properly installed

## License

This AI integration is part of the AdZeta admin panel system and follows the same licensing terms.
