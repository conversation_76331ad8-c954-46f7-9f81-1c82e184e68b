/* Professional Comparison Section - Enterprise-Grade Design */

.professional-comparison {
    padding: 120px 0;
    background-color: #f8f9fc;
    position: relative;
    overflow: hidden;
}

/* Container */
.pro-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    position: relative;
    z-index: 1;
}

/* Section header with professional styling */
.pro-header {
    margin-bottom: 80px;
    max-width: 800px;
}

.pro-tag {
    display: inline-block;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    color: #e958a1;
    margin-bottom: 20px;
    position: relative;
    padding-left: 30px;
}

.pro-tag::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 20px;
    height: 1px;
    background: #e958a1;
    transform: translateY(-50%);
}

.pro-title {
    font-size: 42px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 25px;
    color: #1a1a2e;
    letter-spacing: -0.5px;
}

.pro-title span {
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.pro-subtitle {
    font-size: 18px;
    line-height: 1.6;
    color: #4a4a68;
    max-width: 600px;
}

/* Comparison container */
.comparison-container {
    display: flex;
    justify-content: center;
    gap: 0;
    position: relative;
    margin-bottom: 80px;
}

/* VS divider with professional design */
.vs-divider {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 2px;
    background: linear-gradient(to bottom, 
        rgba(233, 88, 161, 0.1),
        rgba(233, 88, 161, 0.5),
        rgba(233, 88, 161, 0.1));
    z-index: 1;
}

.vs-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    z-index: 2;
    font-weight: 700;
    font-size: 16px;
    color: #1a1a2e;
    border: 1px solid rgba(233, 88, 161, 0.2);
}

/* Comparison cards with professional styling */
.pro-card {
    flex: 1;
    max-width: 500px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    padding: 0;
}

.pro-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

/* Card header */
.pro-card-header {
    padding: 30px;
    border-bottom: 1px solid #f0f0f7;
    position: relative;
}

/* Card badge */
.pro-card-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 15px;
    border-radius: 30px;
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 15px;
}

.pro-card.traditional .pro-card-badge {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.pro-card.adzeta .pro-card-badge {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.pro-card-badge i {
    margin-right: 8px;
    font-size: 12px;
}

/* Recommended tag */
.recommended-tag {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 5px 12px;
    border-radius: 4px;
    letter-spacing: 0.5px;
}

.pro-card-title {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a2e;
    margin-bottom: 10px;
}

.pro-card-description {
    font-size: 15px;
    color: #4a4a68;
    line-height: 1.5;
    margin: 0;
}

/* Feature list with professional styling */
.pro-feature-list {
    padding: 30px;
}

.pro-feature-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pro-feature-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    font-size: 15px;
    color: #4a4a68;
    line-height: 1.5;
}

.pro-feature-list li:last-child {
    margin-bottom: 0;
}

.pro-feature-icon {
    margin-right: 15px;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    margin-top: 2px;
}

.pro-feature-icon.negative {
    background: rgba(244, 67, 54, 0.1);
    color: #F44336;
}

.pro-feature-icon.positive {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

/* Card footer */
.pro-card-footer {
    padding: 20px 30px 30px;
    text-align: center;
    border-top: 1px solid #f0f0f7;
}

.pro-btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 15px;
    text-decoration: none;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
}

.pro-card.traditional .pro-btn {
    background: #f8f9fc;
    color: #4a4a68;
    border: 1px solid #e0e0e7;
}

.pro-card.traditional .pro-btn:hover {
    background: #e9ecef;
}

.pro-card.adzeta .pro-btn {
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.2);
}

.pro-card.adzeta .pro-btn:hover {
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    transform: translateY(-2px);
}

/* Results section with professional card design */
.pro-results {
    background: white;
    border-radius: 8px;
    padding: 50px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    text-align: center;
    position: relative;
    overflow: hidden;
    max-width: 900px;
    margin: 0 auto;
}

.pro-results-title {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a2e;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

.pro-results-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
}

/* Stats with professional design */
.pro-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.pro-stat {
    position: relative;
}

.pro-stat-value {
    font-size: 48px;
    font-weight: 700;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 10px;
    position: relative;
}

.pro-stat-label {
    font-size: 15px;
    color: #4a4a68;
    font-weight: 500;
}

/* CTA button with professional design */
.pro-cta {
    display: inline-flex;
    align-items: center;
    padding: 15px 35px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-weight: 600;
    font-size: 16px;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.2);
}

.pro-cta i {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.pro-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    color: white;
}

.pro-cta:hover i {
    transform: translateX(5px);
}

/* Animation classes */
.fade-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-up.active {
    opacity: 1;
    transform: translateY(0);
}

.fade-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-left.active {
    opacity: 1;
    transform: translateX(0);
}

.fade-right {
    opacity: 0;
    transform: translateX(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-right.active {
    opacity: 1;
    transform: translateX(0);
}

.scale-up {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.scale-up.active {
    opacity: 1;
    transform: scale(1);
}

/* Staggered animation delays */
.delay-1 {
    transition-delay: 0.1s;
}

.delay-2 {
    transition-delay: 0.2s;
}

.delay-3 {
    transition-delay: 0.3s;
}

.delay-4 {
    transition-delay: 0.4s;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .professional-comparison {
        padding: 80px 0;
    }
    
    .pro-title {
        font-size: 36px;
    }
    
    .comparison-container {
        flex-direction: column;
        align-items: center;
    }
    
    .pro-card {
        width: 100%;
        max-width: 500px;
        margin-bottom: 40px;
    }
    
    .vs-divider {
        display: none;
    }
    
    .vs-badge {
        position: relative;
        margin: 20px auto;
        transform: none;
        left: auto;
        top: auto;
    }
    
    .pro-results {
        padding: 40px 30px;
    }
}

@media (max-width: 767px) {
    .professional-comparison {
        padding: 60px 0;
    }
    
    .pro-header {
        margin-bottom: 50px;
    }
    
    .pro-title {
        font-size: 30px;
    }
    
    .pro-subtitle {
        font-size: 16px;
    }
    
    .pro-card-header,
    .pro-feature-list,
    .pro-card-footer {
        padding: 20px;
    }
    
    .pro-card-title {
        font-size: 20px;
    }
    
    .pro-feature-list li {
        font-size: 14px;
    }
    
    .pro-results {
        padding: 30px 20px;
    }
    
    .pro-results-title {
        font-size: 24px;
    }
    
    .pro-stat-value {
        font-size: 36px;
    }
    
    .pro-stats {
        gap: 30px;
    }
    
    .pro-cta {
        padding: 12px 25px;
        font-size: 15px;
    }
}
