<?php

namespace AdZetaAdmin\API;

/**
 * Dashboard API Controller
 */
class DashboardController extends BaseController
{
    /**
     * Get dashboard statistics
     */
    public function getStats()
    {
        $this->requireAuth();
        
        try {
            // Get blog post statistics
            $totalPosts = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_posts");
            $publishedPosts = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_posts WHERE status = 'published'");
            $draftPosts = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_posts WHERE status = 'draft'");
            $archivedPosts = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_posts WHERE status = 'archived'");

            // Get category and tag counts
            $totalCategories = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_categories");
            $totalTags = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_tags");

            // Get media statistics
            $totalMedia = $this->db->fetchColumn("SELECT COUNT(*) FROM media_library");
            $mediaSize = $this->db->fetchColumn("SELECT SUM(file_size) FROM media_library") ?: 0;

            // Get user statistics
            $totalUsers = $this->db->fetchColumn("SELECT COUNT(*) FROM users");
            $activeUsers = $this->db->fetchColumn("SELECT COUNT(*) FROM users WHERE status = 'active'");
            
            // Get recent activity
            $recentPosts = $this->db->fetchAll(
                "SELECT id, title, status, created_at FROM blog_posts 
                 ORDER BY created_at DESC LIMIT 5"
            );
            
            return $this->success([
                'stats' => [
                    'posts' => [
                        'total' => (int)$totalPosts,
                        'published' => (int)$publishedPosts,
                        'draft' => (int)$draftPosts,
                        'archived' => (int)$archivedPosts
                    ],
                    'categories' => (int)$totalCategories,
                    'tags' => (int)$totalTags,
                    'users' => [
                        'total' => (int)$totalUsers,
                        'active' => (int)$activeUsers
                    ],
                    'media' => [
                        'count' => (int)$totalMedia,
                        'size' => (int)$mediaSize,
                        'size_formatted' => $this->formatBytes($mediaSize)
                    ]
                ],
                'recent_posts' => $recentPosts
            ]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to load dashboard stats: ' . $e->getMessage());
        }
    }
    
    /**
     * Get recent activity
     */
    public function getRecentActivity()
    {
        $this->requireAuth();
        
        try {
            $activities = [];
            
            // Recent posts
            $recentPosts = $this->db->fetchAll(
                "SELECT 'post' as type, id, title, status, created_at, updated_at
                 FROM blog_posts
                 ORDER BY updated_at DESC LIMIT 15"
            );

            foreach ($recentPosts as $post) {
                $activities[] = [
                    'type' => 'post',
                    'action' => $post['status'] === 'published' ? 'published' : 'updated',
                    'title' => $post['title'],
                    'id' => $post['id'],
                    'timestamp' => $post['updated_at']
                ];
            }

            // Recent categories
            $recentCategories = $this->db->fetchAll(
                "SELECT 'category' as type, id, name as title, 'active' as status, created_at, updated_at
                 FROM blog_categories
                 ORDER BY updated_at DESC LIMIT 5"
            );

            foreach ($recentCategories as $category) {
                $activities[] = [
                    'type' => 'category',
                    'action' => 'created',
                    'title' => $category['title'],
                    'id' => $category['id'],
                    'timestamp' => $category['updated_at']
                ];
            }
            
            // Sort by timestamp
            usort($activities, function($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });
            
            return $this->success([
                'activities' => array_slice($activities, 0, 15)
            ]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to load recent activity: ' . $e->getMessage());
        }
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
