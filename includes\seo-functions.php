<?php
/**
 * SEO Management Functions
 * Comprehensive SEO data management for static pages and blog posts
 */

/**
 * Get database connection for SEO functions
 */
function getSEODatabase() {
    static $pdo = null;

    if ($pdo === null) {
        try {
            $pdo = new PDO(
                'mysql:host=localhost;dbname=adzetadb;charset=utf8mb4',
                'adzetauser',
                'Crazy1395#',
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
        } catch (PDOException $e) {
            error_log("SEO Database connection failed: " . $e->getMessage());
            return null;
        }
    }

    return $pdo;
}

/**
 * Get SEO data for the current page
 * Automatically detects if it's a blog post or static page
 */
function getCurrentPageSEO() {
    // First check if there's an override set by the page (for case studies, blog posts, etc.)
    if (isset($GLOBALS['override_seo_data'])) {
        return $GLOBALS['override_seo_data'];
    }

    $currentUrl = $_SERVER['REQUEST_URI'];
    $currentUrl = strtok($currentUrl, '?'); // Remove query parameters

    // Check if this is a case study URL
    if (preg_match('/\/case-studies\/([^\/]+)/', $currentUrl, $matches)) {
        $slug = $matches[1];
        return getCaseStudySEO($slug);
    }

    // Check if this is the case studies list page
    if (strpos($currentUrl, 'case-studies') !== false || $currentUrl === '/case-studies/' || $currentUrl === '/case-studies') {
        // This should be handled by the override_seo_data, but fallback just in case
        return getDefaultSEO();
    }

    // Check if this is a blog post URL
    if (preg_match('/\/blog\/([^\/]+)/', $currentUrl, $matches)) {
        $slug = $matches[1];
        return getBlogPostSEO($slug);
    }

    // Check if this is the blog list page
    if (strpos($currentUrl, 'blog-list') !== false || $currentUrl === '/blog/' || $currentUrl === '/blog') {
        $currentUrl = '/blog-list-dynamic.php';
    }

    // For static pages, get SEO data from page_seo table
    return getStaticPageSEO($currentUrl);
}

/**
 * Get SEO data for a blog post
 */
function getBlogPostSEO($slug) {
    $pdo = getSEODatabase();
    if (!$pdo) return getDefaultSEO();

    try {
        $stmt = $pdo->prepare("
            SELECT title, excerpt, content, featured_image, slug, created_at, updated_at,
                   meta_title, meta_description, meta_keywords, og_title, og_description, og_image
            FROM blog_posts
            WHERE slug = ? AND status = 'published'
        ");
        $stmt->execute([$slug]);
        $post = $stmt->fetch();

        if (!$post) {
            return getDefaultSEO();
        }

        // Generate SEO data from blog post
        $seoData = [
            'page_title' => (($post['meta_title'] ?? '') ?: $post['title']) . getDefaultSetting('default_title_suffix', ' - AdZeta'),
            'meta_description' => $post['meta_description'] ?: truncateText(strip_tags($post['excerpt'] ?: $post['content']), 160),
            'meta_keywords' => $post['meta_keywords'] ?: '',
            'og_title' => $post['og_title'] ?: ($post['meta_title'] ?: $post['title']),
            'og_description' => $post['og_description'] ?: truncateText(strip_tags($post['excerpt'] ?: $post['content']), 200),
            'og_image' => $post['og_image'] ?: $post['featured_image'] ?: getDefaultSetting('default_og_image'),
            'og_type' => 'article',
            'canonical_url' => getSEOBaseUrl() . 'blog/' . $post['slug'],
            'robots' => 'index,follow',
            'article_published_time' => $post['created_at'],
            'article_modified_time' => $post['updated_at'],
            'page_type' => 'blog_post',
            'schema_type' => 'BlogPosting'
        ];

        return $seoData;

    } catch (PDOException $e) {
        error_log("Error fetching blog post SEO: " . $e->getMessage());
        return getDefaultSEO();
    }
}

/**
 * Get SEO data for a case study
 */
function getCaseStudySEO($slug) {
    $pdo = getSEODatabase();
    if (!$pdo) return getDefaultSEO();

    try {
        $stmt = $pdo->prepare("
            SELECT title, hero_description as excerpt, slug, created_at, updated_at,
                   meta_title, meta_description, meta_keywords, og_title, og_description, og_image,
                   featured_image
            FROM case_studies
            WHERE slug = ? AND status = 'published'
        ");
        $stmt->execute([$slug]);
        $caseStudy = $stmt->fetch();

        if (!$caseStudy) {
            return getDefaultSEO();
        }

        $seoData = [
            'page_title' => ($caseStudy['meta_title'] ?: $caseStudy['title']) . ' - AdZeta',
            'meta_description' => $caseStudy['meta_description'] ?: $caseStudy['excerpt'],
            'meta_keywords' => $caseStudy['meta_keywords'] ?: 'case study, client success, ROI, growth',
            'canonical_url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/case-studies/' . $caseStudy['slug'],
            'og_title' => $caseStudy['og_title'] ?: ($caseStudy['meta_title'] ?: $caseStudy['title']),
            'og_description' => $caseStudy['og_description'] ?: $caseStudy['excerpt'],
            'og_image' => $caseStudy['featured_image'] ? 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/' . ltrim($caseStudy['featured_image'], '/') : 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/images/adzeta-og-default.jpg',
            'og_type' => 'article',
            'article_published_time' => $caseStudy['created_at'],
            'article_modified_time' => $caseStudy['updated_at'],
            'page_type' => 'case_study',
            'schema_type' => 'Article'
        ];

        return $seoData;

    } catch (PDOException $e) {
        error_log("Error fetching case study SEO: " . $e->getMessage());
        return getDefaultSEO();
    }
}

/**
 * Get SEO data for a static page
 */
function getStaticPageSEO($pageUrl) {
    $pdo = getSEODatabase();
    if (!$pdo) return getDefaultSEO();

    // Normalize the URL
    $pageUrl = normalizePageUrl($pageUrl);

    try {
        $stmt = $pdo->prepare("
            SELECT * FROM page_seo
            WHERE page_url = ? AND is_active = 1
        ");
        $stmt->execute([$pageUrl]);
        $seoData = $stmt->fetch();

        if (!$seoData) {
            // Try to find a partial match or create default
            return getDefaultSEOForPage($pageUrl);
        }

        // Process and return SEO data
        return [
            'page_title' => $seoData['page_title'],
            'meta_description' => $seoData['meta_description'],
            'meta_keywords' => $seoData['meta_keywords'],
            'og_title' => $seoData['og_title'] ?: $seoData['page_title'],
            'og_description' => $seoData['og_description'] ?: $seoData['meta_description'],
            'og_image' => $seoData['og_image'] ?: getDefaultSetting('default_og_image'),
            'og_type' => $seoData['og_type'] ?: 'website',
            'twitter_card' => $seoData['twitter_card'] ?: 'summary_large_image',
            'twitter_title' => $seoData['twitter_title'] ?: $seoData['og_title'],
            'twitter_description' => $seoData['twitter_description'] ?: $seoData['og_description'],
            'twitter_image' => $seoData['twitter_image'] ?: $seoData['og_image'],
            'canonical_url' => $seoData['canonical_url'] ?: getSEOBaseUrl() . ltrim($pageUrl, '/'),
            'robots' => $seoData['robots'] ?: 'index,follow',
            'schema_markup' => $seoData['schema_markup'] ? json_decode($seoData['schema_markup'], true) : null,
            'custom_head_tags' => $seoData['custom_head_tags'],
            'page_type' => 'static_page',
            'schema_type' => 'WebPage'
        ];

    } catch (PDOException $e) {
        error_log("Error fetching static page SEO: " . $e->getMessage());
        return getDefaultSEO();
    }
}

/**
 * Get default SEO settings
 */
function getDefaultSEO() {
    return [
        'page_title' => 'AdZeta - AI-Powered Performance Marketing',
        'meta_description' => getDefaultSetting('default_meta_description', 'AdZeta provides AI-powered performance marketing solutions for e-commerce businesses.'),
        'meta_keywords' => 'AI marketing, performance marketing, e-commerce advertising',
        'og_title' => 'AdZeta - AI-Powered Performance Marketing',
        'og_description' => getDefaultSetting('default_meta_description'),
        'og_image' => getDefaultSetting('default_og_image', '/images/adzeta-og-default.jpg'),
        'og_type' => 'website',
        'twitter_card' => 'summary_large_image',
        'canonical_url' => getCurrentUrl(),
        'robots' => getDefaultSetting('default_robots', 'index,follow'),
        'page_type' => 'default',
        'schema_type' => 'WebPage'
    ];
}

/**
 * Get default SEO for a specific page when no data exists
 */
function getDefaultSEOForPage($pageUrl) {
    $defaults = getDefaultSEO();

    // Customize based on page URL
    $pageName = basename($pageUrl, '.php');
    $pageName = str_replace(['-', '_'], ' ', $pageName);
    $pageName = ucwords($pageName);

    if ($pageName && $pageName !== 'Index') {
        $defaults['page_title'] = $pageName . getDefaultSetting('default_title_suffix', ' - AdZeta');
        $defaults['og_title'] = $pageName . ' - AdZeta';
    }

    return $defaults;
}

/**
 * Get a default setting value
 */
function getDefaultSetting($key, $fallback = '') {
    static $settings = null;

    if ($settings === null) {
        $pdo = getSEODatabase();
        if ($pdo) {
            try {
                $stmt = $pdo->query("SELECT setting_key, setting_value FROM seo_settings");
                $settings = [];
                while ($row = $stmt->fetch()) {
                    $settings[$row['setting_key']] = $row['setting_value'];
                }
            } catch (PDOException $e) {
                $settings = [];
            }
        } else {
            $settings = [];
        }
    }

    return $settings[$key] ?? $fallback;
}

/**
 * Normalize page URL for consistent matching
 */
function normalizePageUrl($url) {
    // Remove domain and protocol
    $url = parse_url($url, PHP_URL_PATH);

    // Handle root URL
    if ($url === '/' || $url === '') {
        return '/';
    }

    // Add leading slash if missing
    if (substr($url, 0, 1) !== '/') {
        $url = '/' . $url;
    }

    return $url;
}

/**
 * Get current full URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Get SEO base URL (with trailing slash)
 */
function getSEOBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . '/';
}

/**
 * Truncate text to specified length
 */
function truncateText($text, $length = 160) {
    $text = strip_tags($text);
    $text = preg_replace('/\s+/', ' ', trim($text));

    if (strlen($text) <= $length) {
        return $text;
    }

    return substr($text, 0, $length - 3) . '...';
}

/**
 * Generate JSON-LD schema markup
 */
function generateSchemaMarkup($seoData) {
    $schema = [
        '@context' => 'https://schema.org',
        '@type' => $seoData['schema_type'] ?? 'WebPage',
        'name' => $seoData['page_title'],
        'description' => $seoData['meta_description'],
        'url' => $seoData['canonical_url']
    ];

    // Add organization data
    $schema['publisher'] = [
        '@type' => 'Organization',
        'name' => getDefaultSetting('site_name', 'AdZeta'),
        'url' => getSEOBaseUrl(),
        'logo' => [
            '@type' => 'ImageObject',
            'url' => getSEOBaseUrl() . 'images/adzeta-logo-black.svg'
        ]
    ];

    // Add specific schema for blog posts and case studies
    $pageType = $seoData['page_type'] ?? '';
    if ($pageType === 'blog_post') {
        $schema['@type'] = 'BlogPosting';
        $schema['headline'] = $seoData['og_title'];
        $schema['datePublished'] = $seoData['article_published_time'] ?? null;
        $schema['dateModified'] = $seoData['article_modified_time'] ?? null;

        if ($seoData['og_image']) {
            $schema['image'] = [
                '@type' => 'ImageObject',
                'url' => $seoData['og_image']
            ];
        }
    }

    // Merge with custom schema if provided
    if (!empty($seoData['schema_markup'])) {
        $schema = array_merge($schema, $seoData['schema_markup']);
    }

    return json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
}

/**
 * Render SEO meta tags
 */
function renderSEOTags($seoData = null) {
    if ($seoData === null) {
        $seoData = getCurrentPageSEO();
    }

    $output = '';

    // Basic meta tags
    $output .= '<title>' . htmlspecialchars($seoData['page_title']) . '</title>' . "\n";
    $output .= '<meta name="description" content="' . htmlspecialchars($seoData['meta_description']) . '">' . "\n";

    if (!empty($seoData['meta_keywords'])) {
        $output .= '<meta name="keywords" content="' . htmlspecialchars($seoData['meta_keywords']) . '">' . "\n";
    }

    $output .= '<meta name="robots" content="' . htmlspecialchars($seoData['robots'] ?? 'index, follow') . '">' . "\n";
    $output .= '<link rel="canonical" href="' . htmlspecialchars($seoData['canonical_url'] ?? '') . '">' . "\n";

    // Open Graph tags
    $output .= '<meta property="og:title" content="' . htmlspecialchars($seoData['og_title'] ?? $seoData['page_title'] ?? '') . '">' . "\n";
    $output .= '<meta property="og:description" content="' . htmlspecialchars($seoData['og_description'] ?? $seoData['meta_description'] ?? '') . '">' . "\n";
    $output .= '<meta property="og:type" content="' . htmlspecialchars($seoData['og_type'] ?? 'website') . '">' . "\n";
    $output .= '<meta property="og:url" content="' . htmlspecialchars($seoData['canonical_url'] ?? '') . '">' . "\n";
    $output .= '<meta property="og:site_name" content="' . htmlspecialchars(getDefaultSetting('site_name', 'AdZeta')) . '">' . "\n";

    if (!empty($seoData['og_image'])) {
        $output .= '<meta property="og:image" content="' . htmlspecialchars($seoData['og_image']) . '">' . "\n";
    }

    // Twitter Card tags
    $output .= '<meta name="twitter:card" content="' . htmlspecialchars($seoData['twitter_card'] ?? 'summary_large_image') . '">' . "\n";
    $output .= '<meta name="twitter:title" content="' . htmlspecialchars($seoData['twitter_title'] ?? $seoData['og_title']) . '">' . "\n";
    $output .= '<meta name="twitter:description" content="' . htmlspecialchars($seoData['twitter_description'] ?? $seoData['og_description']) . '">' . "\n";

    if (!empty($seoData['twitter_image'])) {
        $output .= '<meta name="twitter:image" content="' . htmlspecialchars($seoData['twitter_image']) . '">' . "\n";
    }

    $twitterHandle = getDefaultSetting('default_twitter_handle');
    if ($twitterHandle) {
        $output .= '<meta name="twitter:site" content="' . htmlspecialchars($twitterHandle) . '">' . "\n";
    }

    // Article specific tags for blog posts and case studies
    $pageType = $seoData['page_type'] ?? '';
    if ($pageType === 'blog_post' || $pageType === 'case_study') {
        if (!empty($seoData['article_published_time'])) {
            $output .= '<meta property="article:published_time" content="' . htmlspecialchars($seoData['article_published_time']) . '">' . "\n";
        }
        if (!empty($seoData['article_modified_time'])) {
            $output .= '<meta property="article:modified_time" content="' . htmlspecialchars($seoData['article_modified_time']) . '">' . "\n";
        }
    }

    // JSON-LD Schema
    $output .= '<script type="application/ld+json">' . "\n";
    $output .= generateSchemaMarkup($seoData) . "\n";
    $output .= '</script>' . "\n";

    // Custom head tags
    if (!empty($seoData['custom_head_tags'])) {
        $output .= $seoData['custom_head_tags'] . "\n";
    }

    return $output;
}
?>
