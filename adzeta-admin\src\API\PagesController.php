<?php

namespace AdZetaAdmin\API;

/**
 * Pages API Controller
 */
class PagesController extends BaseController
{
    /**
     * Get all pages
     */
    public function index()
    {
        $this->requireAuth();
        
        try {
            $pages = $this->db->fetchAll(
                "SELECT id, title, slug, status, created_at, updated_at 
                 FROM pages 
                 ORDER BY created_at DESC"
            );
            
            return $this->success(['pages' => $pages]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to load pages: ' . $e->getMessage());
        }
    }
    
    /**
     * Get single page
     */
    public function show($id)
    {
        $this->requireAuth();
        
        try {
            $page = $this->db->fetch(
                "SELECT * FROM pages WHERE id = ?",
                [$id]
            );
            
            if (!$page) {
                return $this->error('Page not found', 404);
            }
            
            return $this->success(['page' => $page]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to load page: ' . $e->getMessage());
        }
    }
    
    /**
     * Create new page
     */
    public function store()
    {
        $this->requirePermission('manage_pages');
        $data = $this->getRequestData();
        
        $this->validateRequired($data, ['title', 'content']);
        
        try {
            $success = $this->db->insert('pages', [
                'title' => $data['title'],
                'slug' => $data['slug'] ?? $this->generateSlug($data['title']),
                'content' => $data['content'],
                'meta_title' => $data['meta_title'] ?? '',
                'meta_description' => $data['meta_description'] ?? '',
                'status' => $data['status'] ?? 'draft'
            ]);
            
            if ($success) {
                $pageId = $this->db->lastInsertId();
                $page = $this->db->fetch("SELECT * FROM pages WHERE id = ?", [$pageId]);
                
                return $this->success([
                    'message' => 'Page created successfully',
                    'page' => $page
                ], 201);
            } else {
                return $this->error('Failed to create page');
            }
            
        } catch (\Exception $e) {
            return $this->error('Failed to create page: ' . $e->getMessage());
        }
    }
    
    /**
     * Update page
     */
    public function update($id)
    {
        $this->requirePermission('manage_pages');
        $data = $this->getRequestData();
        
        try {
            $page = $this->db->fetch("SELECT * FROM pages WHERE id = ?", [$id]);
            
            if (!$page) {
                return $this->error('Page not found', 404);
            }
            
            $updateData = [];
            $allowedFields = ['title', 'slug', 'content', 'meta_title', 'meta_description', 'status'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            if (!empty($updateData)) {
                $success = $this->db->update('pages', $updateData, 'id = ?', [$id]);
                
                if ($success) {
                    $updatedPage = $this->db->fetch("SELECT * FROM pages WHERE id = ?", [$id]);
                    return $this->success([
                        'message' => 'Page updated successfully',
                        'page' => $updatedPage
                    ]);
                } else {
                    return $this->error('Failed to update page');
                }
            } else {
                return $this->error('No data to update');
            }
            
        } catch (\Exception $e) {
            return $this->error('Failed to update page: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete page
     */
    public function destroy($id)
    {
        $this->requirePermission('manage_pages');
        
        try {
            $page = $this->db->fetch("SELECT * FROM pages WHERE id = ?", [$id]);
            
            if (!$page) {
                return $this->error('Page not found', 404);
            }
            
            $success = $this->db->execute("DELETE FROM pages WHERE id = ?", [$id]);
            
            if ($success) {
                return $this->success(['message' => 'Page deleted successfully']);
            } else {
                return $this->error('Failed to delete page');
            }
            
        } catch (\Exception $e) {
            return $this->error('Failed to delete page: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate slug from title
     */
    private function generateSlug($title)
    {
        $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
        $slug = trim($slug, '-');
        
        // Check if slug exists and make it unique
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->db->fetchColumn("SELECT COUNT(*) FROM pages WHERE slug = ?", [$slug]) > 0) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
}
