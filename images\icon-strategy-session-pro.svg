<?xml version="1.0" encoding="UTF-8"?>
<svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Actionable Strategy Session</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#f45888" offset="0%"></stop>
            <stop stop-color="#ff8cc6" offset="100%"></stop>
        </linearGradient>
        <filter x="-25%" y="-25%" width="150%" height="150%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Subtle Glow -->
        <circle cx="18" cy="18" r="12" fill="url(#linearGradient-1)" opacity="0.1" filter="url(#filter-2)"></circle>
        
        <!-- Main Icon -->
        <g transform="translate(4.000000, 4.000000)" stroke="url(#linearGradient-1)" stroke-width="1.5">
            <!-- Strategy Board -->
            <rect x="1" y="1" width="26" height="20" rx="2"></rect>
            
            <!-- Strategy Elements -->
            <circle cx="7" cy="7" r="2.5"></circle>
            <circle cx="14" cy="14" r="2.5"></circle>
            <circle cx="21" cy="7" r="2.5"></circle>
            
            <!-- Connection Lines -->
            <path d="M9,7 L12,12" stroke-linecap="round"></path>
            <path d="M16,12 L19,7" stroke-linecap="round"></path>
            
            <!-- Checkmark -->
            <polyline points="6 14 8 16 10 12" stroke-linecap="round" stroke-linejoin="round"></polyline>
            <polyline points="18 14 20 16 22 12" stroke-linecap="round" stroke-linejoin="round"></polyline>
            
            <!-- Stand -->
            <path d="M10,21 L10,27" stroke-linecap="round"></path>
            <path d="M18,21 L18,27" stroke-linecap="round"></path>
            <path d="M6,27 L22,27" stroke-linecap="round"></path>
        </g>
    </g>
</svg>
