<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2B0B3A"/>
      <stop offset="100%" stop-color="#FF4081"/>
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#gradient)"/>
  
  <!-- Platform mockup -->
  <rect x="50" y="50" width="500" height="300" fill="#FFFFFF" fill-opacity="0.1" rx="10"/>
  
  <!-- Title -->
  <text x="300" y="100" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFFFFF" text-anchor="middle">AdZeta Platform</text>
  <text x="300" y="130" font-family="Arial, sans-serif" font-size="16" fill="#E6D8F2" text-anchor="middle">AI-Powered Performance Marketing</text>
  
  <!-- Dashboard elements -->
  <rect x="80" y="160" width="120" height="80" fill="#FFFFFF" fill-opacity="0.2" rx="5"/>
  <text x="140" y="185" font-family="Arial, sans-serif" font-size="12" fill="#FFFFFF" text-anchor="middle">ROAS</text>
  <text x="140" y="210" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#FF4081" text-anchor="middle">+247%</text>
  
  <rect x="220" y="160" width="120" height="80" fill="#FFFFFF" fill-opacity="0.2" rx="5"/>
  <text x="280" y="185" font-family="Arial, sans-serif" font-size="12" fill="#FFFFFF" text-anchor="middle">Cost Reduction</text>
  <text x="280" y="210" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#FF4081" text-anchor="middle">-68%</text>
  
  <rect x="360" y="160" width="120" height="80" fill="#FFFFFF" fill-opacity="0.2" rx="5"/>
  <text x="420" y="185" font-family="Arial, sans-serif" font-size="12" fill="#FFFFFF" text-anchor="middle">Revenue Growth</text>
  <text x="420" y="210" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#FF4081" text-anchor="middle">3.2x</text>
  
  <!-- Chart mockup -->
  <rect x="80" y="260" width="400" height="60" fill="#FFFFFF" fill-opacity="0.1" rx="5"/>
  <polyline points="100,300 150,280 200,260 250,270 300,250 350,240 400,230 450,220" 
            stroke="#FF4081" stroke-width="3" fill="none"/>
  <text x="280" y="315" font-family="Arial, sans-serif" font-size="10" fill="#E6D8F2" text-anchor="middle">Performance Trend</text>
</svg>
