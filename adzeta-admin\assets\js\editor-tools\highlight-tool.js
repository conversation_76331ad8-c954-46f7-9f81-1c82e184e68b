/**
 * Highlight Tool for Editor.js
 * Creates inline highlighted text with brand colors
 */

class HighlightTool {
    static get isInline() {
        return true;
    }

    static get sanitize() {
        return {
            mark: {
                style: true,
                class: true
            }
        };
    }

    static get title() {
        return 'Highlight';
    }

    constructor({ api, config }) {
        this.api = api;
        this.config = config || {};

        this.tag = 'MARK';
        this.class = 'adzeta-highlight';

        // Default colors from AdZeta brand palette
        this.colors = this.config.colors || [
            { name: 'Pink', background: '#FF4081', color: 'white' },
            { name: 'Lavender', background: '#E6D8F2', color: '#2B0B3A' },
            { name: 'Purple', background: '#2B0B3A', color: 'white' },
            { name: 'Light Grey', background: '#F5F5F5', color: '#2B0B3A' },
            { name: 'Yellow', background: '#FFF3CD', color: '#856404' }
        ];

        this.currentColorIndex = 0;
    }

    render() {
        this.button = document.createElement('button');
        this.button.type = 'button';
        this.button.innerHTML = '<svg width="20" height="18"><path d="M10.458 12.04l2.919 1.686-.781 1.417-.984-.30-.974 1.687H8.674l1.49-2.583-.508-.775.802-1.402zm.546-.952l3.624-6.327a1.685 1.685 0 0 1 2.312-.623 1.685 1.685 0 0 1 .623 2.312l-3.624 6.327-.623-2.312-2.312.623z" fill="#FF4081"/></svg>';
        this.button.classList.add(this.api.styles.inlineToolButton);

        return this.button;
    }

    surround(range) {
        if (!range) {
            return;
        }

        const termWrapper = this.api.selection.findParentTag(this.tag, this.class);

        if (termWrapper) {
            this.unwrap(termWrapper);
        } else {
            this.wrap(range);
        }
    }

    wrap(range) {
        const selectedText = range.extractContents();
        const mark = document.createElement(this.tag);

        // Apply current color
        const currentColor = this.colors[this.currentColorIndex];
        mark.style.cssText = `
            background: ${currentColor.background};
            color: ${currentColor.color};
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        `;
        mark.classList.add(this.class);
        mark.appendChild(selectedText);
        range.insertNode(mark);

        // Cycle to next color for next highlight
        this.currentColorIndex = (this.currentColorIndex + 1) % this.colors.length;

        this.api.selection.expandToTag(mark);
    }

    unwrap(termWrapper) {
        this.api.selection.expandToTag(termWrapper);

        const sel = window.getSelection();
        const range = sel.getRangeAt(0);

        const unwrappedContent = range.extractContents();
        termWrapper.parentNode.removeChild(termWrapper);
        range.insertNode(unwrappedContent);

        sel.removeAllRanges();
        sel.addRange(range);
    }

    checkState() {
        const termTag = this.api.selection.findParentTag(this.tag, this.class);

        // Only update button state if button exists
        if (this.button && this.api.styles.inlineToolButtonActive) {
            this.button.classList.toggle(this.api.styles.inlineToolButtonActive, !!termTag);
        }

        return !!termTag;
    }

    get shortcut() {
        return 'CMD+SHIFT+H';
    }

    // Paste config removed to avoid Editor.js conflicts
    // Custom paste handling is done through AI content processor

    // onPaste method removed to avoid conflicts
    // Paste handling is done through AI content processor
}

// Export for use in Editor.js
window.HighlightTool = HighlightTool;
