/**
 * AdZeta Admin Panel - Navigation Module
 * Handles view switching and navigation state
 */

window.AdZetaNavigation = {
    // Current active view
    currentView: 'dashboard',

    // Initialize navigation module
    init() {
        this.bindEvents();
        this.initSidebarToggle();
        // Don't set default title here - let URL handling determine initial state
        console.log('Navigation module initialized');
    },

    // Bind event listeners
    bindEvents() {
        // Sidebar navigation links
        const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', this.handleNavClick.bind(this));
        });

        // Mobile menu toggle (if needed)
        this.setupMobileMenu();

        // Browser back/forward buttons
        window.addEventListener('popstate', this.handlePopState.bind(this));

        // Escape key to close mobile menu
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && window.innerWidth <= 768) {
                this.closeMobileMenu();
            }
        });
    },

    // Handle navigation link clicks
    handleNavClick(event) {
        event.preventDefault();

        const link = event.currentTarget;
        const view = link.getAttribute('data-view');

        // Close mobile menu if open
        if (window.innerWidth <= 768) {
            this.closeMobileMenu();
        }

        // FIXED: Always allow navigation, don't check currentView for add-post
        if (view) {
            this.showView(view);
        }
    },

    // Show specific view
    showView(viewName) {
        console.log(`Switching to ${viewName} view`);

        // Special handling for add-post view - directly open editor
        if (viewName === 'add-post') {
            this.handleAddPostView();
            return;
        }

        // If we're currently in the same view but in editor mode, close the editor
        if (this.currentView === viewName && this.isInEditorMode()) {
            this.closeEditorMode();
            return;
        }

        // Hide all views
        this.hideAllViews();

        // Show target view
        const targetView = document.getElementById(`${viewName}View`);
        if (targetView) {
            targetView.style.display = 'block';

            // Update navigation state
            this.updateActiveNavLink(viewName);
            this.updatePageTitle(viewName);
            this.currentView = viewName;

            // Update URL without page reload (clear any action/id parameters)
            this.updateURL(viewName);

            // Load view-specific data AFTER showing the view
            setTimeout(() => {
                this.loadViewData(viewName);
            }, 100);

            console.log(`Successfully switched to ${viewName} view`);
        } else {
            console.error(`View not found: ${viewName}View`);
        }
    },

    // Hide all content views
    hideAllViews() {
        const views = document.querySelectorAll('.view-content');
        views.forEach(view => {
            view.style.display = 'none';
        });
    },

    // Update active navigation link
    updateActiveNavLink(viewName) {
        // Remove active class from all nav links
        const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to current nav link
        const activeLink = document.querySelector(`[data-view="${viewName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    },

    // Update page title and subtitle
    updatePageTitle(viewName) {
        const titleElement = document.getElementById('pageTitle');
        const subtitleElement = document.getElementById('pageSubtitle');
        const actionsElement = document.getElementById('pageActions');

        if (!titleElement) return;

        const viewConfig = this.getViewConfig(viewName);
        
        titleElement.innerHTML = `<i class="${viewConfig.icon} me-2"></i>${viewConfig.title}`;
        
        if (subtitleElement) {
            subtitleElement.textContent = viewConfig.subtitle;
        }

        if (actionsElement) {
            actionsElement.innerHTML = viewConfig.actions || '';
        }
    },

    // Get view configuration
    getViewConfig(viewName) {
        const configs = {
            dashboard: {
                title: 'Dashboard',
                subtitle: 'Welcome back! Here\'s what\'s happening with your blog.',
                icon: 'fas fa-home',
                actions: ''
            },
            'add-post': {
                title: 'Add New Post',
                subtitle: 'Create a new blog post with our powerful editor.',
                icon: 'fas fa-plus-circle',
                actions: ''
            },
            posts: {
                title: 'Blog Posts',
                subtitle: 'Manage your blog posts, create new content, and optimize for SEO.',
                icon: 'fas fa-edit',
                actions: '<button class="btn btn-primary" onclick="AdZetaPostEditor.createNew()"><i class="fas fa-plus me-2"></i>New Post</button>'
            },
            'case-studies': {
                title: 'Case Studies',
                subtitle: 'Showcase client success stories and results.',
                icon: 'fas fa-chart-line',
                actions: '<button class="btn btn-primary" onclick="AdZetaCaseStudies.createCaseStudy()"><i class="fas fa-plus me-2"></i>New Case Study</button>'
            },
            whitepapers: {
                title: 'Whitepapers',
                subtitle: 'Create and manage professional whitepapers and research documents.',
                icon: 'fas fa-file-pdf',
                actions: '<button class="btn btn-primary" onclick="AdZetaWhitepapers.createWhitepaper()"><i class="fas fa-plus me-2"></i>New Whitepaper</button>'
            },
            pages: {
                title: 'Pages',
                subtitle: 'Manage your static pages and content.',
                icon: 'fas fa-file-alt',
                actions: '<button class="btn btn-primary"><i class="fas fa-plus me-2"></i>New Page</button>'
            },
            media: {
                title: 'Media Library',
                subtitle: 'Upload and manage your images, videos, and documents.',
                icon: 'fas fa-images',
                actions: '<button class="btn btn-primary"><i class="fas fa-upload me-2"></i>Upload Media</button>'
            },
            seo: {
                title: 'SEO Management',
                subtitle: 'Manage SEO data for all pages and configure global SEO settings.',
                icon: 'fas fa-search',
                actions: '<button class="btn btn-primary" onclick="seoManager.showPageSEOModal()"><i class="fas fa-plus me-2"></i>Add Page SEO</button>'
            },
            'ai-settings': {
                title: 'AI Assistant',
                subtitle: 'Configure Google Gemini AI integration and manage API keys.',
                icon: 'fas fa-robot',
                actions: ''
            },
            'error-logs': {
                title: 'Error Logs',
                subtitle: 'Advanced error tracking and debugging for developers.',
                icon: 'fas fa-bug',
                actions: '<button class="btn btn-outline-primary" onclick="AdZetaErrorLogs.loadErrorLogs()"><i class="fas fa-sync-alt me-2"></i>Refresh</button>'
            },
            settings: {
                title: 'Settings',
                subtitle: 'Configure your blog settings and preferences.',
                icon: 'fas fa-cog',
                actions: ''
            }
        };

        return configs[viewName] || {
            title: 'Unknown',
            subtitle: '',
            icon: 'fas fa-question',
            actions: ''
        };
    },

    // Load view-specific data
    loadViewData(viewName) {
        switch (viewName) {
            case 'dashboard':
                if (window.AdZetaDashboard) {
                    window.AdZetaDashboard.load();
                }
                break;
            case 'add-post':
                // Directly open the post editor for new post
                if (window.AdZetaPostEditor) {
                    window.AdZetaPostEditor.createNew();
                }
                break;
            case 'posts':
                if (window.AdZetaPosts) {
                    window.AdZetaPosts.load();
                }
                break;
            case 'case-studies':
                if (window.AdZetaCaseStudies) {
                    window.AdZetaCaseStudies.init();
                }
                break;
            case 'whitepapers':
                if (window.AdZetaWhitepapers) {
                    window.AdZetaWhitepapers.init();
                }
                break;
            case 'pages':
                // Load pages data
                break;
            case 'media':
                if (window.AdZetaMediaManager) {
                    window.AdZetaMediaManager.load();
                }
                break;
            case 'users':
                if (window.AdZetaUsers) {
                    window.AdZetaUsers.load();
                }
                break;
            case 'seo':
                console.log('Loading SEO management view...');
                if (window.seoManager) {
                    console.log('SEO Manager found, initializing...');
                    window.seoManager.init();
                } else {
                    console.error('SEO Manager not found!');
                }
                break;
            case 'ai-settings':
                console.log('Loading AI settings view...');
                if (window.AdZetaAISettings) {
                    console.log('AdZetaAISettings found, initializing and rendering...');
                    window.AdZetaAISettings.init();
                    // Add a small delay to ensure initialization is complete
                    setTimeout(() => {
                        window.AdZetaAISettings.render();
                    }, 100);
                } else {
                    console.error('AdZetaAISettings not found!');
                }
                break;
            case 'error-logs':
                console.log('Loading error logs view...');
                if (window.AdZetaErrorLogs) {
                    console.log('AdZetaErrorLogs found, initializing and showing...');
                    window.AdZetaErrorLogs.init();
                    window.AdZetaErrorLogs.show();
                } else {
                    console.error('AdZetaErrorLogs not found!');
                }
                break;
            case 'settings':
                console.log('Loading settings view...');
                if (window.AdZetaSettings) {
                    console.log('AdZetaSettings found, initializing and rendering...');
                    window.AdZetaSettings.init();
                    // Add a small delay to ensure initialization is complete
                    setTimeout(() => {
                        window.AdZetaSettings.render();
                    }, 100);
                } else {
                    console.error('AdZetaSettings not found!');
                }
                break;
        }
    },

    // Update URL without page reload
    updateURL(viewName, action = null, id = null) {
        const url = new URL(window.location);
        url.searchParams.set('view', viewName);

        // Add action parameter if provided
        if (action) {
            url.searchParams.set('action', action);
        } else {
            url.searchParams.delete('action');
        }

        // Add id parameter if provided
        if (id) {
            url.searchParams.set('id', id);
        } else {
            url.searchParams.delete('id');
        }

        window.history.pushState({ view: viewName, action: action, id: id }, '', url);
    },

    // Handle browser back/forward buttons
    handlePopState(event) {
        const state = event.state;
        if (state && state.view) {
            this.showView(state.view);
        } else {
            // Default to dashboard if no state
            this.showView('dashboard');
        }
    },

    // Setup mobile menu functionality
    setupMobileMenu() {
        // Create mobile menu toggle button if needed
        if (window.innerWidth <= 768) {
            this.createMobileMenuToggle();
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                this.createMobileMenuToggle();
            } else {
                this.removeMobileMenuToggle();
            }
        });
    },

    // Create mobile menu toggle button
    createMobileMenuToggle() {
        const header = document.querySelector('.content-header');
        if (!header || header.querySelector('.mobile-menu-toggle')) return;

        const toggleButton = document.createElement('button');
        toggleButton.className = 'btn btn-outline-secondary mobile-menu-toggle d-md-none';
        toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
        toggleButton.addEventListener('click', this.toggleMobileMenu.bind(this));

        header.querySelector('.d-flex').insertBefore(toggleButton, header.querySelector('.d-flex').firstChild);
    },

    // Remove mobile menu toggle button
    removeMobileMenuToggle() {
        const toggleButton = document.querySelector('.mobile-menu-toggle');
        if (toggleButton) {
            toggleButton.remove();
        }
    },

    // Toggle mobile menu
    toggleMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            const isShowing = sidebar.classList.contains('show');
            if (isShowing) {
                this.closeMobileMenu();
            } else {
                this.openMobileMenu();
            }
        }
    },

    // Open mobile menu
    openMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.add('show');
            this.createBackdrop();
        }
    },

    // Close mobile menu
    closeMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.remove('show');
            this.removeBackdrop();
        }
    },

    // Create backdrop for mobile menu
    createBackdrop() {
        if (document.querySelector('.sidebar-backdrop')) return;

        const backdrop = document.createElement('div');
        backdrop.className = 'sidebar-backdrop';
        backdrop.addEventListener('click', this.closeMobileMenu.bind(this));
        document.body.appendChild(backdrop);

        // Trigger show after a small delay to ensure smooth animation
        setTimeout(() => {
            backdrop.classList.add('show');
        }, 10);
    },

    // Remove backdrop
    removeBackdrop() {
        const backdrop = document.querySelector('.sidebar-backdrop');
        if (backdrop) {
            backdrop.classList.remove('show');
            setTimeout(() => {
                backdrop.remove();
            }, 300); // Match CSS transition duration
        }
    },

    // Navigate to specific view programmatically
    navigateTo(viewName) {
        this.showView(viewName);
    },

    // Update URL with action (for sub-pages like new post, edit post)
    updateURLWithAction(viewName, action, id = null) {
        this.updateURL(viewName, action, id);
    },

    // Get current view
    getCurrentView() {
        return this.currentView;
    },

    // Check if view exists
    viewExists(viewName) {
        return document.getElementById(`${viewName}View`) !== null;
    },

    // Add breadcrumb navigation
    addBreadcrumb(items) {
        const header = document.querySelector('.content-header');
        if (!header) return;

        // Remove existing breadcrumb
        const existingBreadcrumb = header.querySelector('.breadcrumb-container');
        if (existingBreadcrumb) {
            existingBreadcrumb.remove();
        }

        // Create new breadcrumb
        const breadcrumbContainer = document.createElement('div');
        breadcrumbContainer.className = 'breadcrumb-container mt-2';

        const breadcrumb = document.createElement('nav');
        breadcrumb.setAttribute('aria-label', 'breadcrumb');

        const breadcrumbList = document.createElement('ol');
        breadcrumbList.className = 'breadcrumb mb-0';

        items.forEach((item, index) => {
            const listItem = document.createElement('li');
            listItem.className = 'breadcrumb-item';

            if (index === items.length - 1) {
                // Last item (current page)
                listItem.className += ' active';
                listItem.setAttribute('aria-current', 'page');
                listItem.textContent = item.text;
            } else {
                // Clickable items
                const link = document.createElement('a');
                link.href = '#';
                link.textContent = item.text;
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    if (item.view) {
                        this.navigateTo(item.view);
                    }
                });
                listItem.appendChild(link);
            }

            breadcrumbList.appendChild(listItem);
        });

        breadcrumb.appendChild(breadcrumbList);
        breadcrumbContainer.appendChild(breadcrumb);
        header.appendChild(breadcrumbContainer);
    },

    // Remove breadcrumb
    removeBreadcrumb() {
        const breadcrumb = document.querySelector('.breadcrumb-container');
        if (breadcrumb) {
            breadcrumb.remove();
        }
    },

    // Initialize sidebar toggle functionality
    initSidebarToggle() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        if (sidebarToggle && sidebar && mainContent) {
            // Load saved state
            const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
            if (isCollapsed) {
                this.toggleSidebar(true);
            }

            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }
    },

    // Toggle sidebar collapsed state
    toggleSidebar(forceState = null) {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const editorFooter = document.querySelector('.editor-footer');

        if (!sidebar || !mainContent) return;

        const isCurrentlyCollapsed = sidebar.classList.contains('collapsed');
        const shouldCollapse = forceState !== null ? forceState : !isCurrentlyCollapsed;

        if (shouldCollapse) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('sidebar-collapsed');
            if (editorFooter) editorFooter.classList.add('sidebar-collapsed');
        } else {
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('sidebar-collapsed');
            if (editorFooter) editorFooter.classList.remove('sidebar-collapsed');
        }

        // Save state
        localStorage.setItem('sidebar-collapsed', shouldCollapse.toString());

        // Trigger resize event for any components that need to adjust
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 300);
    },

    // Check if we're currently in editor mode
    isInEditorMode() {
        const url = new URL(window.location);
        const action = url.searchParams.get('action');
        return action === 'new' || action === 'edit';
    },

    // Close editor mode and return to base view
    closeEditorMode() {
        console.log('Closing editor mode...');

        // Check if post editor is available and close it
        if (window.AdZetaPostEditor && window.AdZetaPostEditor.state && window.AdZetaPostEditor.state.isEditing) {
            const closed = window.AdZetaPostEditor.hideEditor();
            if (!closed) {
                // User cancelled closing (unsaved changes)
                return false;
            }
        } else {
            // Manually reset URL and view if editor isn't available
            this.updateURL(this.currentView);
            this.removeBreadcrumb();
        }

        return true;
    },

    // Handle add-post view - directly open editor
    handleAddPostView() {
        console.log('Handling add-post view - opening editor directly');

        // FIXED: Properly update navigation state to prevent conflicts
        this.updateActiveNavLink('add-post');
        this.currentView = 'posts'; // Set to posts so editor knows where to return

        // Directly open the post editor
        if (window.AdZetaPostEditor) {
            window.AdZetaPostEditor.createNew();
        } else {
            console.error('AdZetaPostEditor not available');
            // Fallback to posts view
            this.showView('posts');
        }
    }
};
