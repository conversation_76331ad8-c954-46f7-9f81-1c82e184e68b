<?php

namespace AdZetaAdmin\Services;

/**
 * Sitemap Generator Service
 * Handles XML sitemap generation for SEO optimization
 */
class SitemapGenerator
{
    private $db;
    private string $baseUrl;
    private array $settings;

    public function __construct($db, string $baseUrl = '')
    {
        $this->db = $db;
        $this->baseUrl = $baseUrl ?: $this->getBaseUrl();
        $this->settings = [
            'include_blog_posts' => true,
            'include_case_studies' => true,
            'include_whitepapers' => true,
            'homepage_priority' => '1.0',
            'blog_priority' => '0.8',
            'case_study_priority' => '0.7',
            'whitepaper_priority' => '0.7'
        ];
    }

    /**
     * Generate XML sitemap with given settings
     */
    public function generate(array $settings = []): array
    {
        $this->settings = array_merge($this->settings, $settings);
        
        try {
            $urls = [];
            
            // Add homepage
            $urls[] = [
                'loc' => $this->baseUrl . '/',
                'lastmod' => date('c'),
                'changefreq' => 'daily',
                'priority' => $this->settings['homepage_priority']
            ];

            // Add blog posts
            if ($this->settings['include_blog_posts']) {
                $urls = array_merge($urls, $this->getBlogPostUrls());
            }

            // Add case studies
            if ($this->settings['include_case_studies']) {
                $urls = array_merge($urls, $this->getCaseStudyUrls());
            }

            // Add whitepapers
            if ($this->settings['include_whitepapers']) {
                $urls = array_merge($urls, $this->getWhitepaperUrls());
            }

            // Add static pages
            $urls = array_merge($urls, $this->getStaticPageUrls());

            // Generate XML
            $xml = $this->buildXML($urls);
            
            // Save to file
            $sitemapPath = $this->getSitemapPath();
            $success = file_put_contents($sitemapPath, $xml);
            
            if ($success === false) {
                throw new \Exception('Failed to write sitemap file');
            }

            return [
                'success' => true,
                'message' => 'Sitemap generated successfully',
                'url_count' => count($urls),
                'file_size' => filesize($sitemapPath),
                'sitemap_url' => $this->baseUrl . '/sitemap.xml'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate sitemap: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get sitemap status and information
     */
    public function getStatus(): array
    {
        $sitemapPath = $this->getSitemapPath();
        $exists = file_exists($sitemapPath);
        
        $data = [
            'exists' => $exists,
            'url_count' => 0,
            'last_modified' => null,
            'file_size' => 0,
            'url' => null
        ];

        if ($exists) {
            $data['last_modified'] = date('Y-m-d H:i:s', filemtime($sitemapPath));
            $data['file_size'] = filesize($sitemapPath);
            $data['url'] = $this->baseUrl . '/sitemap.xml';
            
            // Count URLs in sitemap
            $content = file_get_contents($sitemapPath);
            $data['url_count'] = substr_count($content, '<url>');
        }

        return $data;
    }

    /**
     * Get blog post URLs
     */
    private function getBlogPostUrls(): array
    {
        $urls = [];

        try {
            $posts = $this->db->fetchAll("
                SELECT slug, updated_at, created_at
                FROM blog_posts
                WHERE status = 'published'
                ORDER BY updated_at DESC
            ");

            foreach ($posts as $post) {
                $urls[] = [
                    'loc' => $this->baseUrl . '/blog/' . $post['slug'],
                    'lastmod' => date('c', strtotime($post['updated_at'] ?: $post['created_at'])),
                    'changefreq' => 'monthly',
                    'priority' => $this->settings['blog_priority']
                ];
            }
        } catch (\Exception $e) {
            error_log('Error fetching blog posts for sitemap: ' . $e->getMessage());
        }

        return $urls;
    }

    /**
     * Get case study URLs
     */
    private function getCaseStudyUrls(): array
    {
        $urls = [];

        try {
            $caseStudies = $this->db->fetchAll("
                SELECT slug, updated_at, created_at
                FROM case_studies
                WHERE status = 'published'
                ORDER BY updated_at DESC
            ");

            foreach ($caseStudies as $study) {
                $urls[] = [
                    'loc' => $this->baseUrl . '/case-studies/' . $study['slug'],
                    'lastmod' => date('c', strtotime($study['updated_at'] ?: $study['created_at'])),
                    'changefreq' => 'monthly',
                    'priority' => $this->settings['case_study_priority']
                ];
            }
        } catch (\Exception $e) {
            error_log('Error fetching case studies for sitemap: ' . $e->getMessage());
        }

        return $urls;
    }

    /**
     * Get whitepaper URLs
     */
    private function getWhitepaperUrls(): array
    {
        $urls = [];

        try {
            $whitepapers = $this->db->fetchAll("
                SELECT slug, updated_at, created_at
                FROM whitepapers
                WHERE status = 'published'
                ORDER BY updated_at DESC
            ");

            foreach ($whitepapers as $whitepaper) {
                $urls[] = [
                    'loc' => $this->baseUrl . '/whitepapers/' . $whitepaper['slug'],
                    'lastmod' => date('c', strtotime($whitepaper['updated_at'] ?: $whitepaper['created_at'])),
                    'changefreq' => 'monthly',
                    'priority' => $this->settings['whitepaper_priority']
                ];
            }
        } catch (\Exception $e) {
            error_log('Error fetching whitepapers for sitemap: ' . $e->getMessage());
        }

        return $urls;
    }

    /**
     * Get static page URLs
     */
    private function getStaticPageUrls(): array
    {
        return [
            [
                'loc' => $this->baseUrl . '/blog',
                'lastmod' => date('c'),
                'changefreq' => 'daily',
                'priority' => '0.9'
            ],
            [
                'loc' => $this->baseUrl . '/case-studies',
                'lastmod' => date('c'),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ],
            [
                'loc' => $this->baseUrl . '/whitepapers',
                'lastmod' => date('c'),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ]
        ];
    }

    /**
     * Build XML sitemap content
     */
    private function buildXML(array $urls): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>" . htmlspecialchars($url['loc']) . "</loc>\n";
            $xml .= "    <lastmod>" . $url['lastmod'] . "</lastmod>\n";
            $xml .= "    <changefreq>" . $url['changefreq'] . "</changefreq>\n";
            $xml .= "    <priority>" . $url['priority'] . "</priority>\n";
            $xml .= "  </url>\n";
        }

        $xml .= '</urlset>';
        
        return $xml;
    }

    /**
     * Get sitemap file path
     */
    private function getSitemapPath(): string
    {
        return $_SERVER['DOCUMENT_ROOT'] . '/sitemap.xml';
    }

    /**
     * Get base URL
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }
}
