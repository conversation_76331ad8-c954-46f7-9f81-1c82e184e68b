/**
 * SVG-based Graph Implementation
 * More efficient implementation using SVG, HTML and CSS with minimal JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the graph when the DOM is loaded
    initGraph();
});

function initGraph() {
    const graphContainer = document.querySelector('.animated-graph-container');
    if (!graphContainer) {
        console.error('Graph container not found');
        return;
    }

    // Clear any existing content
    graphContainer.innerHTML = '';

    // Create the SVG container with proper viewBox for scaling
    const svgContainer = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svgContainer.setAttribute('class', 'graph-svg');
    svgContainer.setAttribute('viewBox', '0 0 1000 500');
    svgContainer.setAttribute('preserveAspectRatio', 'xMidYMid meet');
    svgContainer.style.width = '100%';
    svgContainer.style.height = '100%';

    // Add gradient definitions
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

    // Adzeta area gradient
    const adzetaGradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
    adzetaGradient.setAttribute('id', 'adzetaGradient');
    adzetaGradient.setAttribute('x1', '0%');
    adzetaGradient.setAttribute('y1', '0%');
    adzetaGradient.setAttribute('x2', '0%');
    adzetaGradient.setAttribute('y2', '100%');

    const adzetaStop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    adzetaStop1.setAttribute('offset', '0%');
    adzetaStop1.setAttribute('stop-color', '#e958a1');
    adzetaStop1.setAttribute('stop-opacity', '0.25');

    const adzetaStop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    adzetaStop2.setAttribute('offset', '100%');
    adzetaStop2.setAttribute('stop-color', '#e958a1');
    adzetaStop2.setAttribute('stop-opacity', '0');

    adzetaGradient.appendChild(adzetaStop1);
    adzetaGradient.appendChild(adzetaStop2);
    defs.appendChild(adzetaGradient);

    // Traditional area gradient
    const traditionalGradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
    traditionalGradient.setAttribute('id', 'traditionalGradient');
    traditionalGradient.setAttribute('x1', '0%');
    traditionalGradient.setAttribute('y1', '0%');
    traditionalGradient.setAttribute('x2', '0%');
    traditionalGradient.setAttribute('y2', '100%');

    const traditionalStop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    traditionalStop1.setAttribute('offset', '0%');
    traditionalStop1.setAttribute('stop-color', '#4a9eff');
    traditionalStop1.setAttribute('stop-opacity', '0.15');

    const traditionalStop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    traditionalStop2.setAttribute('offset', '100%');
    traditionalStop2.setAttribute('stop-color', '#4a9eff');
    traditionalStop2.setAttribute('stop-opacity', '0');

    traditionalGradient.appendChild(traditionalStop1);
    traditionalGradient.appendChild(traditionalStop2);
    defs.appendChild(traditionalGradient);

    svgContainer.appendChild(defs);

    // Add the SVG container to the graph container
    graphContainer.appendChild(svgContainer);

    // Create the HTML overlay for data points and tooltips
    const htmlOverlay = document.createElement('div');
    htmlOverlay.className = 'graph-html-overlay';
    graphContainer.appendChild(htmlOverlay);

    // Generate the graph data
    const { adzetaData, traditionalData } = generateGraphData();

    // Set up the graph dimensions and padding
    const dimensions = {
        width: 1000,
        height: 500,
        padding: {
            top: 50,
            right: 50,
            bottom: 50,
            left: 50
        }
    };

    // Create the graph elements
    createGraphElements(svgContainer, htmlOverlay, adzetaData, traditionalData, dimensions);

    // Set up the intersection observer to trigger animations when the graph is visible
    setupScrollTrigger(graphContainer, svgContainer);
}

function generateGraphData() {
    // Generate data points for both lines
    const adzetaData = [];
    const traditionalData = [];

    // Generate 100 data points for smooth curves
    for (let i = 0; i < 100; i++) {
        const x = i / 99; // Normalize to 0-1 range

        // Adzeta: Steeper growth curve with slight variations
        // Start at 5% and end at 67% improvement (matching the original graph)
        const adzetaY = 0.25 + 0.42 * x + 0.25 * Math.pow(x, 1.5) + 0.03 * Math.sin(x * Math.PI * 2);

        // Traditional: Flatter, gentle curve
        const traditionalY = 0.25 + 0.2 * x + 0.05 * Math.pow(x, 1.2) + 0.02 * Math.sin(x * Math.PI);

        adzetaData.push({ x: x, y: adzetaY });
        traditionalData.push({ x: x, y: traditionalY });
    }

    // Calculate the percentage improvements at specific points for data labels
    const improvements = [];
    const dataPointPositions = [0.15, 0.3, 0.45, 0.6, 0.75, 0.9];

    dataPointPositions.forEach(position => {
        const index = Math.floor(position * 99);
        const improvement = Math.round((adzetaData[index].y - traditionalData[index].y) * 100);
        improvements.push(`+${improvement}%`);
    });

    console.log('Improvements at data points:', improvements);

    return { adzetaData, traditionalData };
}

function createGraphElements(svg, overlay, adzetaData, traditionalData, dimensions) {
    // Create the graph grid
    createGrid(svg, dimensions);

    // Create the graph axes and labels
    createAxes(svg, dimensions);

    // Create the traditional line and area
    createLine(svg, traditionalData, dimensions, 'traditional-line', 'traditional-area');

    // Create the Adzeta line and area
    createLine(svg, adzetaData, dimensions, 'adzeta-line', 'adzeta-area');

    // Create the profit difference area
    createProfitDifferenceArea(svg, adzetaData, traditionalData, dimensions);

    // Create the data points that will fall onto the Adzeta line
    createDataPoints(overlay, adzetaData, traditionalData, dimensions);

    // Create the legend
    createLegend(svg, dimensions);
}

function createGrid(svg, dimensions) {
    // Create a group for the grid
    const gridGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    gridGroup.setAttribute('class', 'grid');

    const { width, height, padding } = dimensions;
    const graphWidth = width - padding.left - padding.right;
    const graphHeight = height - padding.top - padding.bottom;

    // Create horizontal grid lines
    for (let i = 0; i <= 4; i++) {
        const y = padding.top + (graphHeight * i / 4);
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', padding.left);
        line.setAttribute('y1', y);
        line.setAttribute('x2', padding.left + graphWidth);
        line.setAttribute('y2', y);
        line.setAttribute('class', 'grid-line');
        gridGroup.appendChild(line);

        // Add percentage labels
        if (i > 0) {
            const percentage = 100 - (i * 25);
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', padding.left - 10);
            text.setAttribute('y', y);
            text.setAttribute('class', 'axis-label');
            text.setAttribute('text-anchor', 'end');
            text.setAttribute('dominant-baseline', 'middle');
            text.textContent = `${percentage}%`;
            gridGroup.appendChild(text);
        }
    }

    // Create vertical grid lines
    for (let i = 0; i <= 4; i++) {
        const x = padding.left + (graphWidth * i / 4);
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', x);
        line.setAttribute('y1', padding.top);
        line.setAttribute('x2', x);
        line.setAttribute('y2', padding.top + graphHeight);
        line.setAttribute('class', 'grid-line');
        gridGroup.appendChild(line);

        // Add month labels
        const months = i * 3;
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', x);
        text.setAttribute('y', padding.top + graphHeight + 20);
        text.setAttribute('class', 'axis-label');
        text.setAttribute('text-anchor', 'middle');
        text.textContent = `${months}mo`;
        gridGroup.appendChild(text);
    }

    svg.appendChild(gridGroup);
}

function createAxes(svg, dimensions) {
    const { width, height, padding } = dimensions;
    const graphWidth = width - padding.left - padding.right;
    const graphHeight = height - padding.top - padding.bottom;

    // Create X-axis label
    const xLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    xLabel.setAttribute('x', padding.left + graphWidth / 2);
    xLabel.setAttribute('y', height - 10);
    xLabel.setAttribute('class', 'axis-title');
    xLabel.setAttribute('text-anchor', 'middle');
    xLabel.textContent = 'Time';
    svg.appendChild(xLabel);

    // Create Y-axis label
    const yLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    yLabel.setAttribute('transform', `translate(15, ${padding.top + graphHeight / 2}) rotate(-90)`);
    yLabel.setAttribute('class', 'axis-title');
    yLabel.setAttribute('text-anchor', 'middle');
    yLabel.textContent = 'Profit Growth';
    svg.appendChild(yLabel);
}

function createLine(svg, data, dimensions, lineClass, areaClass) {
    const { width, height, padding } = dimensions;
    const graphWidth = width - padding.left - padding.right;
    const graphHeight = height - padding.top - padding.bottom;

    // Create the line path
    let linePath = `M`;

    // Create the area path (for filling under the line)
    let areaPath = `M${padding.left},${padding.top + graphHeight} `;

    // Generate the paths
    data.forEach((point, index) => {
        const x = padding.left + point.x * graphWidth;
        const y = padding.top + graphHeight - (point.y * graphHeight);

        if (index === 0) {
            linePath += `${x},${y}`;
            areaPath += `L${x},${y}`;
        } else {
            linePath += ` L${x},${y}`;
            areaPath += ` L${x},${y}`;
        }
    });

    // Complete the area path
    areaPath += ` L${padding.left + graphWidth},${padding.top + graphHeight} L${padding.left},${padding.top + graphHeight} Z`;

    // Create the line element
    const line = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    line.setAttribute('d', linePath);
    line.setAttribute('class', `graph-line ${lineClass}`);
    line.setAttribute('fill', 'none');

    // Create the area element
    const area = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    area.setAttribute('d', areaPath);
    area.setAttribute('class', `graph-area ${areaClass}`);

    // Add them to the SVG
    svg.appendChild(area);
    svg.appendChild(line);

    // Return the elements for animation
    return { line, area };
}

function createProfitDifferenceArea(svg, adzetaData, traditionalData, dimensions) {
    const { width, height, padding } = dimensions;
    const graphWidth = width - padding.left - padding.right;
    const graphHeight = height - padding.top - padding.bottom;

    // Create the difference area path
    let diffPath = '';

    // Add points from the Adzeta line
    adzetaData.forEach((point, index) => {
        const x = padding.left + point.x * graphWidth;
        const y = padding.top + graphHeight - (point.y * graphHeight);

        if (index === 0) {
            diffPath = `M${x},${y}`;
        } else {
            diffPath += ` L${x},${y}`;
        }
    });

    // Add points from the Traditional line in reverse
    for (let i = traditionalData.length - 1; i >= 0; i--) {
        const point = traditionalData[i];
        const x = padding.left + point.x * graphWidth;
        const y = padding.top + graphHeight - (point.y * graphHeight);

        diffPath += ` L${x},${y}`;
    }

    // Close the path
    diffPath += ' Z';

    // Create the difference area element
    const diffArea = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    diffArea.setAttribute('d', diffPath);
    diffArea.setAttribute('class', 'profit-difference-area');

    // Add it to the SVG
    svg.appendChild(diffArea);

    return diffArea;
}

function createDataPoints(overlay, adzetaData, traditionalData, dimensions) {
    const { width, height, padding } = dimensions;
    const graphWidth = width - padding.left - padding.right;
    const graphHeight = height - padding.top - padding.bottom;

    // Define the positions for data points (percentage along the line)
    const dataPointPositions = [0.15, 0.3, 0.45, 0.6, 0.75, 0.9];

    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = 'graph-tooltip';
    overlay.appendChild(tooltip);

    // Create data points
    dataPointPositions.forEach((position, index) => {
        // Get the corresponding data points
        const pointIndex = Math.floor(position * adzetaData.length);
        const adzetaPoint = adzetaData[pointIndex];
        const traditionalPoint = traditionalData[pointIndex];

        // Calculate the improvement percentage
        const improvement = Math.round((adzetaPoint.y - traditionalPoint.y) * 100);

        // Calculate the position
        const x = padding.left + adzetaPoint.x * graphWidth;
        const y = padding.top + graphHeight - (adzetaPoint.y * graphHeight);

        // Create the data point container
        const pointContainer = document.createElement('div');
        pointContainer.className = 'data-point-container';
        pointContainer.style.left = `${x / width * 100}%`;
        pointContainer.style.top = `${y / height * 100}%`;

        // Create the data point
        const dataPoint = document.createElement('div');
        dataPoint.className = 'data-point';
        dataPoint.setAttribute('data-delay', index * 0.2);

        // Create the label
        const label = document.createElement('div');
        label.className = 'data-point-label';
        label.textContent = `+${improvement}%`;

        // Add them to the container
        pointContainer.appendChild(dataPoint);
        pointContainer.appendChild(label);

        // Add tooltip functionality
        pointContainer.addEventListener('mouseenter', () => {
            // Calculate month based on position
            const month = Math.round(position * 12);

            // Format values for display
            const adzetaValue = Math.round(adzetaPoint.y * 100);
            const traditionalValue = Math.round(traditionalPoint.y * 100);

            // Update tooltip content
            tooltip.innerHTML = `
                <div class="tooltip-title">Month ${month}</div>
                <div class="tooltip-value">
                    <span class="tooltip-label">Adzeta AI:</span>
                    <span class="tooltip-adzeta">${adzetaValue}%</span>
                </div>
                <div class="tooltip-value">
                    <span class="tooltip-label">Traditional:</span>
                    <span class="tooltip-traditional">${traditionalValue}%</span>
                </div>
                <div class="tooltip-value">
                    <span class="tooltip-label">Improvement:</span>
                    <span class="tooltip-adzeta">+${improvement}%</span>
                </div>
            `;

            // Position the tooltip
            const tooltipRect = tooltip.getBoundingClientRect();
            const containerRect = overlay.getBoundingClientRect();

            let tooltipX = x - tooltipRect.width / 2;
            let tooltipY = y - tooltipRect.height - 15;

            // Ensure tooltip stays within bounds
            if (tooltipX < padding.left) tooltipX = padding.left;
            if (tooltipX + tooltipRect.width > containerRect.width - padding.right) {
                tooltipX = containerRect.width - padding.right - tooltipRect.width;
            }

            if (tooltipY < padding.top) tooltipY = y + 15;

            tooltip.style.left = `${tooltipX}px`;
            tooltip.style.top = `${tooltipY}px`;
            tooltip.classList.add('visible');
        });

        pointContainer.addEventListener('mouseleave', () => {
            tooltip.classList.remove('visible');
        });

        // Add the container to the overlay
        overlay.appendChild(pointContainer);
    });

    // Add hover detection for the entire graph area
    const hoverArea = document.createElement('div');
    hoverArea.style.position = 'absolute';
    hoverArea.style.left = `${padding.left}px`;
    hoverArea.style.top = `${padding.top}px`;
    hoverArea.style.width = `${graphWidth}px`;
    hoverArea.style.height = `${graphHeight}px`;
    hoverArea.style.cursor = 'crosshair';

    hoverArea.addEventListener('mousemove', (e) => {
        // Only show tooltip if not already showing for a data point
        if (tooltip.classList.contains('visible')) return;

        // Calculate position in graph coordinates
        const rect = hoverArea.getBoundingClientRect();
        const x = (e.clientX - rect.left) / graphWidth;

        if (x < 0 || x > 1) return;

        // Find closest data point
        const index = Math.floor(x * adzetaData.length);
        if (index >= adzetaData.length) return;

        const adzetaPoint = adzetaData[index];
        const traditionalPoint = traditionalData[index];

        // Calculate values
        const month = Math.round(x * 12);
        const improvement = Math.round((adzetaPoint.y - traditionalPoint.y) * 100);
        const adzetaValue = Math.round(adzetaPoint.y * 100);
        const traditionalValue = Math.round(traditionalPoint.y * 100);

        // Update tooltip content
        tooltip.innerHTML = `
            <div class="tooltip-title">Month ${month}</div>
            <div class="tooltip-value">
                <span class="tooltip-label">Adzeta AI:</span>
                <span class="tooltip-adzeta">${adzetaValue}%</span>
            </div>
            <div class="tooltip-value">
                <span class="tooltip-label">Traditional:</span>
                <span class="tooltip-traditional">${traditionalValue}%</span>
            </div>
            <div class="tooltip-value">
                <span class="tooltip-label">Improvement:</span>
                <span class="tooltip-adzeta">+${improvement}%</span>
            </div>
        `;

        // Position the tooltip
        const tooltipRect = tooltip.getBoundingClientRect();
        const graphX = padding.left + x * graphWidth;
        const graphY = padding.top + graphHeight - (adzetaPoint.y * graphHeight);

        let tooltipX = graphX - tooltipRect.width / 2;
        let tooltipY = graphY - tooltipRect.height - 15;

        // Ensure tooltip stays within bounds
        if (tooltipX < padding.left) tooltipX = padding.left;
        if (tooltipX + tooltipRect.width > rect.width + padding.left) {
            tooltipX = rect.width + padding.left - tooltipRect.width;
        }

        if (tooltipY < padding.top) tooltipY = graphY + 15;

        tooltip.style.left = `${tooltipX}px`;
        tooltip.style.top = `${tooltipY}px`;
        tooltip.classList.add('visible');
    });

    hoverArea.addEventListener('mouseleave', () => {
        tooltip.classList.remove('visible');
    });

    overlay.appendChild(hoverArea);
}

function createLegend(svg, dimensions) {
    const { padding } = dimensions;

    // Create a group for the legend
    const legendGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    legendGroup.setAttribute('class', 'legend');
    legendGroup.setAttribute('transform', `translate(${padding.left + 10}, ${padding.top + 20})`);

    // Create Adzeta legend item
    const adzetaLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    adzetaLine.setAttribute('x1', 0);
    adzetaLine.setAttribute('y1', 0);
    adzetaLine.setAttribute('x2', 20);
    adzetaLine.setAttribute('y2', 0);
    adzetaLine.setAttribute('class', 'adzeta-line');
    legendGroup.appendChild(adzetaLine);

    const adzetaText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    adzetaText.setAttribute('x', 25);
    adzetaText.setAttribute('y', 0);
    adzetaText.setAttribute('dominant-baseline', 'middle');
    adzetaText.setAttribute('class', 'legend-text');
    adzetaText.textContent = 'Adzeta AI';
    legendGroup.appendChild(adzetaText);

    // Create Traditional legend item
    const traditionalLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    traditionalLine.setAttribute('x1', 100);
    traditionalLine.setAttribute('y1', 0);
    traditionalLine.setAttribute('x2', 120);
    traditionalLine.setAttribute('y2', 0);
    traditionalLine.setAttribute('class', 'traditional-line');
    legendGroup.appendChild(traditionalLine);

    const traditionalText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    traditionalText.setAttribute('x', 125);
    traditionalText.setAttribute('y', 0);
    traditionalText.setAttribute('dominant-baseline', 'middle');
    traditionalText.setAttribute('class', 'legend-text');
    traditionalText.textContent = 'Traditional';
    legendGroup.appendChild(traditionalText);

    svg.appendChild(legendGroup);
}

function setupScrollTrigger(container, svg) {
    // Check if we're on mobile
    const isMobile = window.innerWidth < 768;

    // Create an intersection observer to trigger animations when the graph is visible
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Start the animations
                startAnimations(svg);
                // Unobserve after triggering
                observer.unobserve(container);
            }
        });
    }, {
        // Use a higher threshold on mobile to ensure the graph is more visible before animating
        threshold: isMobile ? 0.5 : 0.3,
        // Add root margin on mobile to delay animation until more of the graph is visible
        rootMargin: isMobile ? '-10% 0px' : '0px'
    });

    // Observe the container
    observer.observe(container);

    // Handle resize events
    window.addEventListener('resize', () => {
        // Recalculate positions for responsive layout
        const dataPoints = document.querySelectorAll('.data-point-container');
        if (dataPoints.length > 0) {
            // If data points exist, the graph has already been initialized
            // We could recalculate positions here if needed
        }
    });
}

function startAnimations(svg) {
    // Add the 'animate' class to start CSS animations
    svg.classList.add('animate');

    // Get all data points and animate them with staggered delays
    const dataPoints = document.querySelectorAll('.data-point-container');
    dataPoints.forEach(point => {
        setTimeout(() => {
            point.classList.add('animate');
        }, 1000 + parseFloat(point.querySelector('.data-point').getAttribute('data-delay')) * 1000);
    });

    // Log animation start for debugging
    console.log('Graph animation started');
}
