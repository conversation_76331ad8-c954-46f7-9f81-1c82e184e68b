/**
 * Bold Inline Tool for Editor.js
 * Based on official Editor.js documentation
 */

class BoldTool {
    static get isInline() {
        return true;
    }

    static get sanitize() {
        const sanitizeConfig = {
            b: {} // Allow <b> tags, clean all attributes
        };
        console.log('🔍 DEBUG: BoldTool sanitize config:', sanitizeConfig);
        return sanitizeConfig;
    }

    get state() {
        return this._state;
    }

    set state(state) {
        this._state = state;
        this.button.classList.toggle(this.api.styles.inlineToolButtonActive, state);
    }

    constructor({ api }) {
        this.api = api;
        this.button = null;
        this._state = false;
        this.tag = 'B';
        this.class = null;
    }

    /**
     * Create button for inline toolbar
     */
    render() {
        this.button = document.createElement('button');
        this.button.type = 'button';
        this.button.innerHTML = '<b>B</b>';
        this.button.classList.add(this.api.styles.inlineToolButton);

        return this.button;
    }

    /**
     * Wrap/unwrap selected text with <b> tags
     */
    surround(range) {
        if (this.state) {
            this.unwrap(range);
            return;
        }
        this.wrap(range);
    }

    /**
     * Wrap selection with <b> tag
     */
    wrap(range) {
        const selectedText = range.extractContents();
        const boldElement = document.createElement(this.tag);

        boldElement.appendChild(selectedText);
        range.insertNode(boldElement);

        this.api.selection.expandToTag(boldElement);
    }

    /**
     * Unwrap <b> tag
     */
    unwrap(range) {
        const boldElement = this.api.selection.findParentTag(this.tag, this.class);
        const text = range.extractContents();

        boldElement.remove();
        range.insertNode(text);
    }

    /**
     * Check if text is already bold
     * @param {Selection} selection - current Selection
     */
    checkState(selection) {
        const boldElement = this.api.selection.findParentTag(this.tag);
        this.state = !!boldElement;
    }

    /**
     * Keyboard shortcut
     */
    static get shortcut() {
        return 'CMD+B';
    }
}

// Make available globally
window.BoldTool = BoldTool;
