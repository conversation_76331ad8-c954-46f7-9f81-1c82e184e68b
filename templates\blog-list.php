<?php
/**
 * Blog List Template
 * Variables available: $posts, $pagination, $categories, $filters, $performance_info
 */
?>

<div class="row">
    <div class="col-12">
        <!-- Performance indicator -->
        <?php if (isset($show_debug) && $show_debug): ?>
            <div class="alert alert-info">
                <small>
                    <strong>Template Performance:</strong> 
                    <?= e($performance_info ?? 'Template rendered') ?> | 
                    Posts: <?= count($posts ?? []) ?> | 
                    Template: blog-list.php
                </small>
            </div>
        <?php endif; ?>
        
        <!-- Search Results Info -->
        <?php if (isset($filters['search']) && !empty($filters['search'])): ?>
            <div class="alert alert-info">
                <i class="fas fa-search me-2"></i>
                Search results for: <strong><?= e($filters['search']) ?></strong>
                (<?= e($pagination['total_posts'] ?? 0) ?> posts found)
                <a href="blog-templated.php" class="btn btn-sm btn-outline-primary ms-3">Clear Search</a>
            </div>
        <?php endif; ?>
        
        <?php if (empty($posts)): ?>
            <!-- No posts found -->
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-4x text-muted mb-4"></i>
                <h3 class="text-muted">No posts found</h3>
                <p class="text-muted">
                    <?php if (isset($filters['search']) && !empty($filters['search'])): ?>
                        Try adjusting your search terms or <a href="blog-templated.php">browse all posts</a>.
                    <?php else: ?>
                        Check back soon for new content!
                    <?php endif; ?>
                </p>
            </div>
        <?php else: ?>
            <!-- Blog posts grid -->
            <div class="row">
                <?php foreach ($posts as $post): ?>
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <article class="post-card h-100">
                            <!-- Featured Image -->
                            <?php if (!empty($post['featured_image'])): ?>
                                <div class="position-relative overflow-hidden" style="height: 200px;">
                                    <a href="post-templated.php?slug=<?= urlencode($post['slug']) ?>">
                                        <img src="<?= e($post['featured_image']) ?>" 
                                             alt="<?= e($post['title']) ?>" 
                                             class="w-100 h-100" style="object-fit: cover;">
                                    </a>
                                    
                                    <!-- Category Badge -->
                                    <?php if (!empty($post['category_name']) && $post['category_name'] !== 'Uncategorized'): ?>
                                        <div class="position-absolute top-0 start-0 m-3">
                                            <span class="badge bg-primary">
                                                <?= e($post['category_name']) ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Post Content -->
                            <div class="p-4 d-flex flex-column h-100">
                                <!-- Post Meta -->
                                <div class="text-muted small mb-3">
                                    <i class="fas fa-user me-1"></i>
                                    <?= e(trim($post['author_name']) ?: 'Unknown Author') ?>
                                    
                                    <span class="mx-2">•</span>
                                    
                                    <i class="fas fa-calendar me-1"></i>
                                    <?= $template->formatDate($post['published_at']) ?>
                                    
                                    <?php if (!empty($post['reading_time']) && $post['reading_time'] > 0): ?>
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-clock me-1"></i>
                                        <?= e($post['reading_time']) ?> min read
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Post Title -->
                                <h3 class="h5 mb-3">
                                    <a href="post-templated.php?slug=<?= urlencode($post['slug']) ?>" 
                                       class="text-decoration-none text-dark">
                                        <?= e($post['title'] ?: 'Untitled Post') ?>
                                    </a>
                                </h3>
                                
                                <!-- Post Excerpt -->
                                <p class="text-muted mb-3 flex-grow-1">
                                    <?= e($template->truncate($post['excerpt'] ?: strip_tags($post['content'] ?? ''), 120)) ?>
                                </p>
                                
                                <!-- Read More Link -->
                                <div class="mt-auto">
                                    <a href="post-templated.php?slug=<?= urlencode($post['slug']) ?>" 
                                       class="btn btn-outline-primary btn-sm">
                                        Read More <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                </div>
                            </div>
                        </article>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Pagination -->
    <?php if (isset($pagination) && is_array($pagination) && isset($pagination['total_pages']) && $pagination['total_pages'] > 1): ?>
        <div class="col-12 mt-4">
            <nav aria-label="Blog pagination">
                <ul class="pagination justify-content-center">
                    <!-- Previous Page -->
                    <?php if (isset($pagination['current_page']) && $pagination['current_page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $pagination['current_page'] - 1 ?><?= isset($filters['search']) ? '&search=' . urlencode($filters['search']) : '' ?><?= isset($filters['category']) ? '&category=' . $filters['category'] : '' ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <!-- Page Numbers -->
                    <?php if (isset($pagination['current_page']) && isset($pagination['total_pages'])): ?>
                        <?php
                        $start = max(1, $pagination['current_page'] - 2);
                        $end = min($pagination['total_pages'], $pagination['current_page'] + 2);
                        ?>

                        <?php for ($i = $start; $i <= $end; $i++): ?>
                            <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?><?= isset($filters['search']) ? '&search=' . urlencode($filters['search']) : '' ?><?= isset($filters['category']) ? '&category=' . $filters['category'] : '' ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    <?php endif; ?>
                    
                    <!-- Next Page -->
                    <?php if (isset($pagination['current_page']) && isset($pagination['total_pages']) && $pagination['current_page'] < $pagination['total_pages']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $pagination['current_page'] + 1 ?><?= isset($filters['search']) ? '&search=' . urlencode($filters['search']) : '' ?><?= isset($filters['category']) ? '&category=' . $filters['category'] : '' ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            
            <!-- Pagination Info -->
            <div class="text-center text-muted small mt-3">
                <?php if (isset($pagination['current_page']) && isset($pagination['posts_per_page']) && isset($pagination['total_posts'])): ?>
                    Showing <?= ($pagination['current_page'] - 1) * $pagination['posts_per_page'] + 1 ?>
                    to <?= min($pagination['current_page'] * $pagination['posts_per_page'], $pagination['total_posts']) ?>
                    of <?= $pagination['total_posts'] ?> posts
                <?php else: ?>
                    Showing all posts
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Sidebar (if categories available) -->
<?php if (!empty($categories)): ?>
    <div class="row mt-5">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-folder me-2"></i>Categories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-auto">
                            <a href="blog-templated.php"
                               class="btn btn-sm <?= !isset($filters['category']) ? 'btn-primary' : 'btn-outline-primary' ?> mb-2">
                                All Posts (<?= isset($pagination['total_posts']) ? $pagination['total_posts'] : 0 ?>)
                            </a>
                        </div>
                        <?php foreach ($categories as $category): ?>
                            <div class="col-auto">
                                <a href="blog-templated.php?category=<?= e($category['id'] ?? '') ?>"
                                   class="btn btn-sm <?= isset($filters['category']) && $filters['category'] == ($category['id'] ?? '') ? 'btn-primary' : 'btn-outline-primary' ?> mb-2">
                                    <?= e($category['name'] ?? 'Unknown') ?> (<?= e($category['post_count'] ?? 0) ?>)
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Search Form -->
<div class="row mt-4">
    <div class="col-lg-6 mx-auto">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="d-flex">
                    <input type="text" 
                           class="form-control me-2" 
                           name="search" 
                           placeholder="Search posts..." 
                           value="<?= e($filters['search'] ?? '') ?>">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
