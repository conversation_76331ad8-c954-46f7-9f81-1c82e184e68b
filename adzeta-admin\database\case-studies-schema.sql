-- Case Studies Database Schema
-- This creates the case_studies table for the AdZeta admin panel
-- Based on the luminous-skin-clinic template structure

CREATE TABLE IF NOT EXISTS case_studies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    client_name VARCHAR(255) NOT NULL,
    industry VARCHAR(100),
    
    -- Hero Section (based on luminous-skin-clinic template)
    hero_title LONGTEXT,
    hero_subtitle TEXT,
    hero_description LONGTEXT,
    hero_image VARCHAR(500),
    hero_badge_text VARCHAR(255),
    
    -- Project Highlights (6 sections from the template)
    highlight_1_title VARCHAR(255),
    highlight_1_content LONGTEXT,
    highlight_1_icon VARCHAR(100) DEFAULT 'bi-heart-pulse',
    
    highlight_2_title VARCHAR(255),
    highlight_2_content LONGTEXT,
    highlight_2_icon VARCHAR(100) DEFAULT 'bi-question-circle',
    
    highlight_3_title VARCHAR(255),
    highlight_3_content LONGTEXT,
    highlight_3_icon VARCHAR(100) DEFAULT 'bi-compass',
    
    highlight_4_title VARCHAR(255),
    highlight_4_content LONGTEXT,
    highlight_4_icon VARCHAR(100) DEFAULT 'bi-cpu',
    
    highlight_5_title VARCHAR(255),
    highlight_5_content LONGTEXT,
    highlight_5_icon VARCHAR(100) DEFAULT 'bi-gear-wide-connected',
    
    highlight_6_title VARCHAR(255),
    highlight_6_content LONGTEXT,
    highlight_6_icon VARCHAR(100) DEFAULT 'bi-trophy',
    
    -- Challenge Section
    challenge_title VARCHAR(255),
    challenge_subtitle VARCHAR(255),
    challenge_description LONGTEXT,
    
    -- Solution Section
    solution_title VARCHAR(255),
    solution_subtitle VARCHAR(255),
    solution_description LONGTEXT,

    -- Solution Points (4 main solution points)
    solution_point_1_title VARCHAR(255),
    solution_point_1_description LONGTEXT,
    solution_point_2_title VARCHAR(255),
    solution_point_2_description LONGTEXT,
    solution_point_3_title VARCHAR(255),
    solution_point_3_description LONGTEXT,
    solution_point_4_title VARCHAR(255),
    solution_point_4_description LONGTEXT,

    -- Results/Metrics (stored as JSON for flexibility)
    results_data JSON, -- Example: {"roi_increase": "300%", "cpa_reduction": "45%", "conversion_rate": "12.5%"}

    -- Results Section
    results_title VARCHAR(255),
    results_subtitle VARCHAR(255),
    results_description LONGTEXT,

    -- Testimonial Section
    testimonial_quote LONGTEXT,
    testimonial_author_name VARCHAR(255),
    testimonial_author_title VARCHAR(255),
    testimonial_author_company VARCHAR(255),
    testimonial_author_image VARCHAR(500),

    -- Call to Action Section
    cta_title VARCHAR(255),
    cta_description LONGTEXT,
    cta_button_text VARCHAR(100),
    cta_button_url VARCHAR(500),
    
    -- SEO and Meta fields
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    focus_keyword VARCHAR(100),
    canonical_url VARCHAR(500),
    og_title VARCHAR(255),
    og_description TEXT,
    og_image VARCHAR(500),
    twitter_card_type ENUM('summary', 'summary_large_image', 'app', 'player') DEFAULT 'summary_large_image',
    
    -- Standard content fields
    excerpt TEXT,
    featured_image VARCHAR(500),
    author_id INT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    visibility ENUM('public', 'private', 'password') DEFAULT 'public',
    password VARCHAR(255),
    published_at TIMESTAMP NULL,
    
    -- Template and customization
    template VARCHAR(100) DEFAULT 'luminous-skin-clinic',
    custom_css LONGTEXT,
    
    -- Analytics and engagement
    view_count INT DEFAULT 0,
    reading_time INT DEFAULT 0,
    word_count INT DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_industry (industry),
    INDEX idx_client (client_name),
    INDEX idx_published (published_at),
    INDEX idx_author (author_id),
    INDEX idx_template (template),
    INDEX idx_visibility (visibility)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample case study based on the luminous-skin-clinic template
INSERT INTO case_studies (
    title,
    slug,
    client_name,
    industry,
    hero_title,
    hero_subtitle,
    hero_description,
    hero_image,
    hero_badge_text,
    highlight_1_title,
    highlight_1_content,
    highlight_2_title,
    highlight_2_content,
    highlight_3_title,
    highlight_3_content,
    highlight_4_title,
    highlight_4_content,
    highlight_5_title,
    highlight_5_content,
    highlight_6_title,
    highlight_6_content,
    challenge_title,
    challenge_subtitle,
    challenge_description,
    results_data,
    meta_title,
    meta_description,
    status,
    author_id,
    template
) VALUES (
    'Luminous Skin Clinic Multiplies High-Value Clientele 3.2X Through Predictive LTV Targeting',
    'luminous-skin-clinic-adzeta-predictive-ltv-targeting',
    'Luminous Skin Clinic',
    'Healthcare',
    'Luminous Skin Clinic Multiplies High-Value Clientele 3.2X Through Predictive LTV Targeting',
    'AI-DRIVEN CLIENT ACQUISITION',
    'Luminous Skin Clinic, a premier esthetician service provider, revolutionized its client acquisition strategy by partnering with AdZeta. Leveraging predictive Lifetime Value (LTV) modeling, Luminous significantly reduced customer acquisition costs (CAC) while attracting a higher caliber of clients demonstrating increased treatment frequency and enhanced loyalty, thereby boosting overall profitability.',
    'images/case-studies/luminous-skin.png',
    'AI-DRIVEN CLIENT ACQUISITION',
    'Client Profile: Luminous Skin Clinic',
    'Luminous Skin Clinic is a premium provider of esthetician services, specializing in advanced facial aesthetics, microneedling, chemical peels, and customized skincare regimens. Their business model is built on delivering exceptional, personalized treatment plans and fostering long-term client relationships.',
    'The Core Challenge: Identifying Future High-Value Clients',
    'With a diverse service range, from individual treatments to comprehensive skincare packages, Luminous Skin Clinic found that standard advertising metrics, such as Cost Per Acquisition (CPA) for an initial appointment, failed to distinguish new clients with high long-term value potential from one-time visitors. This ambiguity hindered sustainable and profitable growth.',
    'Previous Approach: Inefficient Client Acquisition',
    'The clinic''s prior advertising strategy relied on optimizing Google and Meta campaigns for initial consultations or website inquiries. This approach led to unpredictable client retention rates and difficulties in scaling profitable customer acquisition, particularly for their premium service offerings.',
    'The AdZeta Intervention: Predictive LTV Implementation',
    'AdZeta introduced its advanced Predictive AI platform to forecast client LTV from the earliest interaction points. This enabled a shift to Value-Based Bidding strategies, specifically targeting the acquisition of high-potential clients likely to engage in more frequent treatments and upgrade to higher-value packages.',
    'Methodology: Data-Driven LTV Modeling & Campaign Integration',
    'AdZeta leveraged Luminous Skin Clinic''s first-party data to train bespoke LTV machine learning models. These predictive signals were then seamlessly integrated with Google Ads (Target ROAS) and Meta Ads (Value Optimization) campaigns. Rigorous A/B testing was conducted against the previous strategy, with a specific focus on high-value demographic segments.',
    'Key Outcomes: Significant Growth & Cost Reduction',
    'The strategic implementation of AdZeta''s predictive LTV insights resulted in a 3.2x increase in high-value client acquisition. Concurrently, Luminous Skin Clinic achieved a 41% reduction in customer acquisition costs for its premium service segments, demonstrating a marked improvement in marketing efficiency and ROI.',
    'The Challenge: Inefficient Ad Spend & Overlooked LTV Opportunities',
    'Understanding the Problem',
    'Traditional advertising models in the esthetic services sector often fail to accurately identify and efficiently target clients who will deliver the highest lifetime value, leading to suboptimal budget allocation and missed revenue potential.',
    '{"roi_increase": "320%", "cpa_reduction": "41%", "conversion_rate": "12.5%", "ltv_improvement": "3.2x", "revenue_growth": "$1.2M"}',
    'Luminous Skin Clinic Case Study - 320% ROI Increase | AdZeta',
    'See how Luminous Skin Clinic achieved a 320% ROI increase and 41% CAC reduction through AdZeta''s predictive LTV targeting technology.',
    'published',
    1,
    'luminous-skin-clinic'
);
