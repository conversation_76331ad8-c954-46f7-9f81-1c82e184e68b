<?php
/**
 * Case Studies Database Helper Functions
 * Similar to BlogDatabase.php but for case studies
 */

// Prevent direct access
if (!defined('ADZETA_INIT')) {
    die('Direct access not permitted');
}

class CaseStudiesDatabase {
    private static $instance = null;
    private $pdo;

    private function __construct() {
        try {
            // Use the same database configuration as admin panel
            $dsn = "mysql:host=localhost;dbname=adzetadb;charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, 'adzetauser', 'Crazy1395#', $options);

            // Test connection
            $this->pdo->query("SELECT 1");

        } catch (PDOException $e) {
            error_log("Case Studies database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->pdo;
    }

    /**
     * Get published case studies with pagination
     */
    public function getCaseStudies($options = []) {
        $limit = $options['limit'] ?? 12;
        $page = $options['page'] ?? 1;
        $industry = $options['industry'] ?? null;
        $search = $options['search'] ?? null;
        $offset = ($page - 1) * $limit;

        $sql = "SELECT * FROM case_studies WHERE status = 'published'";
        $params = [];

        // Add industry filter
        if ($industry) {
            $sql .= " AND industry = :industry";
            $params['industry'] = $industry;
        }

        // Add search filter
        if ($search) {
            $sql .= " AND (title LIKE :search OR client_name LIKE :search OR hero_description LIKE :search)";
            $params['search'] = '%' . $search . '%';
        }

        $sql .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->pdo->prepare($sql);
        
        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        
        $stmt->execute();
        $caseStudies = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process results_data JSON for each case study
        foreach ($caseStudies as &$study) {
            if (!empty($study['results_data'])) {
                $study['results_data'] = json_decode($study['results_data'], true);
            }
        }

        return $caseStudies;
    }

    /**
     * Get total count of published case studies
     */
    public function getCaseStudiesCount($options = []) {
        $industry = $options['industry'] ?? null;
        $search = $options['search'] ?? null;

        $sql = "SELECT COUNT(*) FROM case_studies WHERE status = 'published'";
        $params = [];

        // Add industry filter
        if ($industry) {
            $sql .= " AND industry = :industry";
            $params['industry'] = $industry;
        }

        // Add search filter
        if ($search) {
            $sql .= " AND (title LIKE :search OR client_name LIKE :search OR hero_description LIKE :search)";
            $params['search'] = '%' . $search . '%';
        }

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn();
    }

    /**
     * Get a single case study by slug
     */
    public function getCaseStudyBySlug($slug) {
        $sql = "SELECT * FROM case_studies WHERE slug = :slug AND status = 'published'";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['slug' => $slug]);

        $caseStudy = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($caseStudy) {
            // Decode JSON fields
            if ($caseStudy['results_data']) {
                $caseStudy['results_data'] = json_decode($caseStudy['results_data'], true);
            }

            // Increment view count
            $this->incrementViewCount($caseStudy['id']);
        }

        return $caseStudy;
    }

    /**
     * Get case studies by industry
     */
    public function getCaseStudiesByIndustry($industry, $limit = 10) {
        $sql = "SELECT * FROM case_studies 
                WHERE status = 'published' AND industry = :industry 
                ORDER BY created_at DESC 
                LIMIT :limit";

        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue(':industry', $industry);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        $caseStudies = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process results_data JSON for each case study
        foreach ($caseStudies as &$study) {
            if (!empty($study['results_data'])) {
                $study['results_data'] = json_decode($study['results_data'], true);
            }
        }

        return $caseStudies;
    }

    /**
     * Get related case studies (same industry, excluding current)
     */
    public function getRelatedCaseStudies($currentId, $industry, $limit = 3) {
        $sql = "SELECT * FROM case_studies 
                WHERE status = 'published' 
                AND industry = :industry 
                AND id != :current_id 
                ORDER BY created_at DESC 
                LIMIT :limit";

        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue(':industry', $industry);
        $stmt->bindValue(':current_id', $currentId, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        $caseStudies = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process results_data JSON for each case study
        foreach ($caseStudies as &$study) {
            if (!empty($study['results_data'])) {
                $study['results_data'] = json_decode($study['results_data'], true);
            }
        }

        return $caseStudies;
    }

    /**
     * Get all industries with case study counts
     */
    public function getIndustries() {
        $sql = "SELECT industry, COUNT(*) as count 
                FROM case_studies 
                WHERE status = 'published' 
                GROUP BY industry 
                ORDER BY count DESC, industry ASC";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Search case studies
     */
    public function searchCaseStudies($query, $limit = 10) {
        $sql = "SELECT * FROM case_studies 
                WHERE status = 'published'
                AND (title LIKE :query OR client_name LIKE :query OR hero_description LIKE :query)
                ORDER BY created_at DESC
                LIMIT :limit";

        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue(':query', '%' . $query . '%');
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        $caseStudies = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Process results_data JSON for each case study
        foreach ($caseStudies as &$study) {
            if (!empty($study['results_data'])) {
                $study['results_data'] = json_decode($study['results_data'], true);
            }
        }

        return $caseStudies;
    }

    /**
     * Increment view count for a case study
     */
    private function incrementViewCount($id) {
        $sql = "UPDATE case_studies SET view_count = view_count + 1 WHERE id = :id";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['id' => $id]);
    }
}

// Helper functions for easy access
function getCaseStudiesDatabase() {
    return CaseStudiesDatabase::getInstance();
}

function getCaseStudies($options = []) {
    return getCaseStudiesDatabase()->getCaseStudies($options);
}

function getCaseStudyBySlug($slug) {
    return getCaseStudiesDatabase()->getCaseStudyBySlug($slug);
}

function getCaseStudiesByIndustry($industry, $limit = 10) {
    return getCaseStudiesDatabase()->getCaseStudiesByIndustry($industry, $limit);
}

function getRelatedCaseStudies($currentId, $industry, $limit = 3) {
    return getCaseStudiesDatabase()->getRelatedCaseStudies($currentId, $industry, $limit);
}

function searchCaseStudies($query, $limit = 10) {
    return getCaseStudiesDatabase()->searchCaseStudies($query, $limit);
}
?>
