<?php

namespace AdZetaAdmin\Cache;

/**
 * OPcache Manager
 * Optimizes PHP bytecode caching for maximum performance
 */
class OPcacheManager {
    private $settings;

    public function __construct() {
        $this->loadSettings();
    }

    /**
     * Load OPcache settings from database
     */
    private function loadSettings() {
        try {
            // Load database connection
            require_once __DIR__ . '/../../bootstrap.php';
            global $admin_db;

            if ($admin_db) {
                // Get OPcache settings from database
                $opcacheSettings = $admin_db->fetchAll(
                    "SELECT setting_key, setting_value, setting_type
                     FROM settings
                     WHERE setting_key LIKE 'opcache_%'
                     ORDER BY setting_key"
                );

                $this->settings = [];
                foreach ($opcacheSettings as $setting) {
                    $this->settings[$setting['setting_key']] = $this->castSettingValue(
                        $setting['setting_value'],
                        $setting['setting_type']
                    );
                }

                // Use defaults if no settings found
                if (empty($this->settings)) {
                    $this->settings = $this->getDefaultSettings();
                }
            } else {
                $this->settings = $this->getDefaultSettings();
            }
        } catch (Exception $e) {
            error_log('OPcacheManager: Error loading settings: ' . $e->getMessage());
            $this->settings = $this->getDefaultSettings();
        }
    }

    /**
     * Get default OPcache settings
     */
    private function getDefaultSettings() {
        return [
            'opcache_enabled' => false,              // Default to disabled
            'opcache_preload' => false,              // Default to disabled
            'opcache_validate_timestamps' => true    // Default to enabled for development
        ];
    }

    /**
     * Cast setting value to appropriate type
     */
    private function castSettingValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'json':
                return json_decode($value, true) ?: [];
            case 'text':
            case 'string':
            default:
                return (string)$value;
        }
    }

    /**
     * Check if OPcache is available and enabled
     */
    public function isAvailable() {
        return function_exists('opcache_get_status');
    }

    /**
     * Check if OPcache is enabled
     */
    public function isEnabled() {
        if (!$this->isAvailable()) {
            return false;
        }

        $status = opcache_get_status();
        return $status !== false && $status['opcache_enabled'];
    }

    /**
     * Get OPcache status and statistics
     */
    public function getStatus() {
        if (!$this->isAvailable()) {
            return [
                'available' => false,
                'enabled' => false,
                'status' => 'OPcache extension not installed',
                'recommendation' => 'Install PHP OPcache extension for better performance'
            ];
        }

        $status = opcache_get_status();

        if (!$status) {
            return [
                'available' => true,
                'enabled' => false,
                'status' => 'OPcache is disabled',
                'recommendation' => 'Enable OPcache in php.ini: opcache.enable=1'
            ];
        }

        $config = opcache_get_configuration();

        return [
            'available' => true,
            'enabled' => true,
            'status' => 'OPcache is active and working',
            'memory_usage' => $status['memory_usage'],
            'opcache_statistics' => $status['opcache_statistics'],
            'configuration' => $config['directives'],
            'cached_scripts' => $status['opcache_statistics']['num_cached_scripts'] ?? 0,
            'hit_rate' => round($status['opcache_statistics']['opcache_hit_rate'] ?? 0, 2),
            'memory_consumption' => $config['directives']['opcache.memory_consumption'] ?? 0,
            'max_accelerated_files' => $config['directives']['opcache.max_accelerated_files'] ?? 0,
            'recommendations' => $this->getRecommendations($config['directives'])
        ];
    }

    /**
     * Get OPcache configuration recommendations
     */
    private function getRecommendations($directives) {
        $recommendations = [];

        // Check memory consumption
        $memoryConsumption = $directives['opcache.memory_consumption'] ?? 128;
        if ($memoryConsumption < 256) {
            $recommendations[] = [
                'setting' => 'opcache.memory_consumption',
                'current' => $memoryConsumption . 'MB',
                'recommended' => '256MB',
                'reason' => 'Increase memory for better caching capacity'
            ];
        }

        // Check max accelerated files
        $maxFiles = $directives['opcache.max_accelerated_files'] ?? 2000;
        if ($maxFiles < 10000) {
            $recommendations[] = [
                'setting' => 'opcache.max_accelerated_files',
                'current' => $maxFiles,
                'recommended' => '10000',
                'reason' => 'Increase for larger applications'
            ];
        }

        // Check interned strings buffer
        $internedStrings = $directives['opcache.interned_strings_buffer'] ?? 4;
        if ($internedStrings < 16) {
            $recommendations[] = [
                'setting' => 'opcache.interned_strings_buffer',
                'current' => $internedStrings . 'MB',
                'recommended' => '16MB',
                'reason' => 'Improve string interning performance'
            ];
        }

        // Check validate timestamps (production setting)
        $validateTimestamps = $directives['opcache.validate_timestamps'] ?? 1;
        if ($validateTimestamps && defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
            $recommendations[] = [
                'setting' => 'opcache.validate_timestamps',
                'current' => 'Enabled',
                'recommended' => 'Disabled (0)',
                'reason' => 'Disable in production for maximum performance'
            ];
        }

        return $recommendations;
    }

    /**
     * Generate optimal OPcache configuration
     */
    public function generateOptimalConfig() {
        $config = [
            'production' => [
                'opcache.enable' => 1,
                'opcache.memory_consumption' => 256,
                'opcache.interned_strings_buffer' => 16,
                'opcache.max_accelerated_files' => 10000,
                'opcache.validate_timestamps' => 0,
                'opcache.save_comments' => 0,
                'opcache.fast_shutdown' => 1,
                'opcache.enable_file_override' => 1,
                'opcache.optimization_level' => '0x7FFFBFFF',
                'opcache.revalidate_freq' => 0,
                'opcache.max_file_size' => 0,
                'opcache.consistency_checks' => 0,
                'opcache.force_restart_timeout' => 180,
                'opcache.error_log' => '',
                'opcache.log_verbosity_level' => 1,
                'opcache.preferred_memory_model' => '',
                'opcache.blacklist_filename' => '',
                'opcache.max_wasted_percentage' => 5,
                'opcache.use_cwd' => 1,
                'opcache.validate_permission' => 0,
                'opcache.validate_root' => 0,
                'opcache.file_update_protection' => 2,
                'opcache.huge_code_pages' => 1
            ],
            'development' => [
                'opcache.enable' => 1,
                'opcache.memory_consumption' => 128,
                'opcache.interned_strings_buffer' => 8,
                'opcache.max_accelerated_files' => 4000,
                'opcache.validate_timestamps' => 1,
                'opcache.revalidate_freq' => 2,
                'opcache.save_comments' => 1,
                'opcache.fast_shutdown' => 1,
                'opcache.enable_file_override' => 0,
                'opcache.optimization_level' => '0x7FFFBFFF',
                'opcache.max_file_size' => 0,
                'opcache.consistency_checks' => 0,
                'opcache.force_restart_timeout' => 180,
                'opcache.error_log' => '',
                'opcache.log_verbosity_level' => 1,
                'opcache.preferred_memory_model' => '',
                'opcache.blacklist_filename' => '',
                'opcache.max_wasted_percentage' => 5,
                'opcache.use_cwd' => 1,
                'opcache.validate_permission' => 0,
                'opcache.validate_root' => 0,
                'opcache.file_update_protection' => 2,
                'opcache.huge_code_pages' => 0
            ]
        ];

        return $config;
    }

    /**
     * Preload critical PHP files
     */
    public function preloadCriticalFiles() {
        if (!$this->isEnabled()) {
            return false;
        }

        $criticalFiles = [
            __DIR__ . '/../API/BaseController.php',
            __DIR__ . '/../API/CacheController.php',
            __DIR__ . '/../API/SettingsController.php',
            __DIR__ . '/../../includes/BlogManager.php',
            __DIR__ . '/../../includes/TemplateEngine.php',
            __DIR__ . '/CacheManager.php',
            __DIR__ . '/FrontendCacheManager.php'
        ];

        $preloaded = 0;
        $errors = [];

        foreach ($criticalFiles as $file) {
            if (file_exists($file)) {
                try {
                    if (function_exists('opcache_compile_file')) {
                        opcache_compile_file($file);
                        $preloaded++;
                    }
                } catch (\Exception $e) {
                    $errors[] = "Failed to preload {$file}: " . $e->getMessage();
                }
            }
        }

        return [
            'success' => true,
            'preloaded' => $preloaded,
            'total_files' => count($criticalFiles),
            'errors' => $errors
        ];
    }

    /**
     * Clear OPcache
     */
    public function clearCache() {
        if (!$this->isEnabled()) {
            return [
                'success' => false,
                'message' => 'OPcache is not enabled'
            ];
        }

        if (function_exists('opcache_reset')) {
            $result = opcache_reset();
            return [
                'success' => $result,
                'message' => $result ? 'OPcache cleared successfully' : 'Failed to clear OPcache'
            ];
        }

        return [
            'success' => false,
            'message' => 'opcache_reset function not available'
        ];
    }

    /**
     * Invalidate specific file in OPcache
     */
    public function invalidateFile($filePath) {
        if (!$this->isEnabled()) {
            return false;
        }

        if (function_exists('opcache_invalidate')) {
            return opcache_invalidate($filePath, true);
        }

        return false;
    }

    /**
     * Get cached scripts list
     */
    public function getCachedScripts() {
        if (!$this->isEnabled()) {
            return [];
        }

        $status = opcache_get_status(true);

        if (!isset($status['scripts'])) {
            return [];
        }

        $scripts = [];
        foreach ($status['scripts'] as $script => $info) {
            $scripts[] = [
                'file' => $script,
                'hits' => $info['hits'],
                'memory_consumption' => $info['memory_consumption'],
                'last_used_timestamp' => $info['last_used_timestamp'],
                'timestamp' => $info['timestamp']
            ];
        }

        // Sort by hits (most used first)
        usort($scripts, function($a, $b) {
            return $b['hits'] - $a['hits'];
        });

        return $scripts;
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics() {
        if (!$this->isEnabled()) {
            return null;
        }

        $status = opcache_get_status();
        $stats = $status['opcache_statistics'];

        return [
            'hit_rate' => round($stats['opcache_hit_rate'], 2),
            'miss_rate' => round(100 - $stats['opcache_hit_rate'], 2),
            'hits' => $stats['hits'],
            'misses' => $stats['misses'],
            'blacklist_misses' => $stats['blacklist_misses'] ?? 0,
            'cached_scripts' => $stats['num_cached_scripts'],
            'cached_keys' => $stats['num_cached_keys'],
            'max_cached_keys' => $stats['max_cached_keys'],
            'memory_usage' => [
                'used_memory' => $status['memory_usage']['used_memory'],
                'free_memory' => $status['memory_usage']['free_memory'],
                'wasted_memory' => $status['memory_usage']['wasted_memory'],
                'current_wasted_percentage' => round($status['memory_usage']['current_wasted_percentage'], 2)
            ]
        ];
    }

    /**
     * Generate OPcache preload script
     */
    public function generatePreloadScript() {
        $criticalFiles = [
            __DIR__ . '/../API/BaseController.php',
            __DIR__ . '/../API/CacheController.php',
            __DIR__ . '/../API/SettingsController.php',
            __DIR__ . '/../../includes/BlogManager.php',
            __DIR__ . '/../../includes/TemplateEngine.php',
            __DIR__ . '/CacheManager.php',
            __DIR__ . '/FrontendCacheManager.php'
        ];

        $script = "<?php\n";
        $script .= "/**\n";
        $script .= " * OPcache Preload Script\n";
        $script .= " * Generated by AdZeta OPcache Manager\n";
        $script .= " * Add this to php.ini: opcache.preload=" . __DIR__ . "/../../cache/opcache-preload.php\n";
        $script .= " */\n\n";

        foreach ($criticalFiles as $file) {
            if (file_exists($file)) {
                $script .= "if (file_exists('{$file}')) {\n";
                $script .= "    require_once '{$file}';\n";
                $script .= "}\n\n";
            }
        }

        return $script;
    }

    /**
     * Save preload script to file
     */
    public function savePreloadScript() {
        $script = $this->generatePreloadScript();
        $preloadFile = __DIR__ . '/../../cache/opcache-preload.php';

        $result = file_put_contents($preloadFile, $script);

        return [
            'success' => $result !== false,
            'file' => $preloadFile,
            'size' => $result,
            'instruction' => "Add this to php.ini: opcache.preload={$preloadFile}"
        ];
    }
}
