<?php
/**
 * Environment Configuration
 * Simple environment configuration for AdZeta Admin
 */

// Environment detection
function isDevelopment() {
    return $_SERVER['HTTP_HOST'] === 'localhost' || 
           strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false ||
           strpos($_SERVER['HTTP_HOST'], '.local') !== false;
}

// Base URL configuration
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    return $protocol . '://' . $host;
}

// Database configuration
function getDatabaseConfig() {
    return [
        'host' => 'localhost',
        'database' => 'adzetadb',
        'username' => 'adzetauser',
        'password' => 'Crazy1395#',
        'charset' => 'utf8mb4'
    ];
}

// Debug configuration
define('ADZETA_DEBUG', isDevelopment());

// Error reporting
if (isDevelopment()) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?>
