<?php

namespace AdZetaAdmin\AI;

/**
 * AI Blog Post Generator
 * Automatically generates blog posts with template-specific structure
 * Integrates with existing AI module and template system
 */
class BlogPostGenerator {
    private $geminiService;
    private $templateEngine;

    public function __construct($db = null) {
        // Initialize your existing Gemini AI service
        if (!$db) {
            require_once __DIR__ . '/../../includes/Database.php';
            $db = getDatabase();
        }

        require_once __DIR__ . '/../Services/GeminiAIService.php';
        $this->geminiService = new \AdZetaAdmin\Services\GeminiAIService($db);

        // Initialize template engine
        require_once __DIR__ . '/../Frontend/TemplateEngine.php';
        $this->templateEngine = new \AdZetaAdmin\Frontend\TemplateEngine();
    }

    /**
     * Generate a complete blog post with template-specific structure
     */
    public function generateBlogPost($topic, $template = 'professional-article', $options = []) {
        try {
            // Get template-specific prompt
            $prompt = $this->getTemplatePrompt($template, $topic, $options);

            // Generate content using your existing Gemini AI service
            $response = $this->geminiService->generateContent($prompt, [
                'temperature' => $options['temperature'] ?? 0.7,
                'maxOutputTokens' => $options['maxOutputTokens'] ?? 2048
            ]);

            if (!$response || !$response['success']) {
                throw new \Exception('Failed to generate content: ' . ($response['error'] ?? 'Unknown error'));
            }

            // Parse and structure the generated content
            $structuredContent = $this->parseGeneratedContent($response['content'], $template);

            // Add template-specific modules
            $finalContent = $this->addTemplateModules($structuredContent, $template, $topic);

            return [
                'success' => true,
                'data' => $finalContent
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get template-specific prompt for AI generation
     */
    private function getTemplatePrompt($template, $topic, $options) {
        $basePrompt = "Write a comprehensive, SEO-optimized blog post about: {$topic}\n\n";

        // Add template-specific instructions with proper HTML classes
        switch ($template) {
            case 'professional-enhanced':
                $prompt = $basePrompt . $this->getProfessionalEnhancedPrompt($options);
                break;

            case 'professional-article':
                $prompt = $basePrompt . $this->getProfessionalPrompt($options);
                break;

            case 'modern-magazine':
                $prompt = $basePrompt . $this->getMagazinePrompt($options);
                break;

            case 'minimal-clean':
                $prompt = $basePrompt . $this->getMinimalPrompt($options);
                break;

            case 'case-study':
                $prompt = $basePrompt . $this->getCaseStudyPrompt($options);
                break;

            default:
                $prompt = $basePrompt . $this->getProfessionalEnhancedPrompt($options);
        }

        // Add AdZeta-specific context
        $prompt .= $this->getAdZetaContext();

        return $prompt;
    }

    /**
     * Get Professional Enhanced template prompt with proper HTML structure
     */
    private function getProfessionalEnhancedPrompt($options) {
        $wordCount = $options['word_count'] ?? 1500;
        $tone = $options['tone'] ?? 'professional';

        return "Create content using this EXACT HTML structure for maximum readability:

**FORMATTING REQUIREMENTS:**
- Use <h2 class=\"section-heading\">Section Title</h2> for main sections
- Use <h3 class=\"subsection-heading\">Subsection Title</h3> for subsections
- Use <p class=\"content-paragraph\">Paragraph text</p> for all paragraphs
- Use <blockquote class=\"professional-quote\">Quote text</blockquote> for quotes
- Use <ul class=\"styled-list\"><li class=\"list-item\">Item</li></ul> for lists
- Use <strong>text</strong> for emphasis and <em>text</em> for italics

**CONTENT STRUCTURE:**
1. Opening paragraph (hook + overview)
2. 4-6 main sections with descriptive headings
3. Each section: 2-3 paragraphs with actionable insights
4. Include 1-2 relevant quotes or statistics
5. Conclusion with key takeaways

**STYLE REQUIREMENTS:**
- {$tone} tone throughout
- Approximately {$wordCount} words
- Include specific examples and actionable advice
- Use transition sentences between sections
- Write for business professionals and marketers

**EXAMPLE FORMAT:**
<p class=\"content-paragraph\">Opening hook paragraph...</p>

<h2 class=\"section-heading\">Understanding the Fundamentals</h2>
<p class=\"content-paragraph\">Section content...</p>

<blockquote class=\"professional-quote\">\"Relevant industry quote or statistic\"</blockquote>

Generate the complete article following this exact structure.";
    }

    /**
     * Professional article prompt
     */
    private function getProfessionalPrompt($options) {
        return "
TEMPLATE: Professional Article
STYLE: Clean, business-focused, authoritative
TARGET AUDIENCE: Business professionals, marketers, decision-makers

FORMAT REQUIREMENTS:
- Generate content as properly formatted HTML (NO markdown stars/asterisks)
- Use semantic HTML tags with AdZeta brand styling
- Include special markers for Editor.js custom blocks
- Use AdZeta brand colors in inline styles
- NO <html>, <head>, or <body> tags - content only

COLOR PALETTE TO USE:
- Primary Purple: #2B0B3A (headers, important text)
- Accent Pink: #FF4081 (CTAs, highlights, links)
- Secondary Lavender: #E6D8F2 (backgrounds, subtle highlights)
- Deep Charcoal: #1A1A1A (body text)
- Light Grey: #F5F5F5 (borders, subtle backgrounds)

STRUCTURE REQUIREMENTS:
1. <h2> headline with primary purple color
2. Executive summary in a styled card (use [STYLED-CARD] marker)
3. Introduction paragraph with engaging hook
4. 3-5 main sections with <h3> subheadings
5. Data-driven content with statistics (use [STATISTIC] marker)
6. Professional tone throughout
7. CTA section (use [CTA-BLOCK] marker)

SPECIAL MARKERS FOR CUSTOM BLOCKS:
- [STYLED-CARD:background=#E6D8F2;color=#2B0B3A;border=#FF4081]Content here[/STYLED-CARD]
- [STATISTIC:value=247;unit=%;label=Improvement;color=#FF4081][/STATISTIC]
- [CTA-BLOCK:title=Ready to Get Started?;description=Transform your marketing today;button=Start Free Trial;url=/demo][/CTA-BLOCK]
- [HIGHLIGHT:color=#FF4081]Important text[/HIGHLIGHT]

HTML FORMATTING EXAMPLES:
- Headers: <h2 style='color: #2B0B3A; font-weight: 600;'>Title</h2>
- Regular highlights: <span style='color: #FF4081; font-weight: 500;'>Important text</span>
- Blockquotes: <blockquote style='border-left: 4px solid #FF4081; padding-left: 20px; font-style: italic;'>Quote content</blockquote>

CONTENT GUIDELINES:
- Use industry terminology appropriately
- Include 2-3 statistics using [STATISTIC] markers
- Provide actionable advice in formatted lists
- Include 1 styled card for key takeaways using [STYLED-CARD]
- Maintain professional, authoritative tone
- Focus on business value and ROI
- Include real-world examples in blockquotes
- End with compelling CTA using [CTA-BLOCK]
- Use [HIGHLIGHT] for 3-5 key terms or phrases
- Optimize for search engines

LENGTH: 1500-2500 words
READING LEVEL: Professional/Advanced
";
    }

    /**
     * Magazine style prompt
     */
    private function getMagazinePrompt($options) {
        return "
TEMPLATE: Modern Magazine
STYLE: Editorial, engaging, storytelling-focused
TARGET AUDIENCE: Industry enthusiasts, thought leaders

FORMAT REQUIREMENTS:
- Generate content as properly formatted HTML (NO markdown)
- Use Bootstrap 5 classes and semantic HTML
- Include magazine-style elements (pull quotes, sidebars)
- Use AdZeta brand colors for visual hierarchy
- NO <html>, <head>, or <body> tags - content only

COLOR PALETTE:
- Primary Purple: #2B0B3A (headlines, important quotes)
- Accent Pink: #FF4081 (pull quotes, highlights)
- Secondary Lavender: #E6D8F2 (sidebar backgrounds)
- Deep Charcoal: #1A1A1A (body text)
- Light Grey: #F5F5F5 (quote borders)

STRUCTURE REQUIREMENTS:
1. <h2> captivating headline with emotional appeal
2. Compelling subtitle in styled paragraph
3. Strong opening paragraph with larger font
4. Story-driven narrative structure
5. Pull quotes in styled blockquotes
6. Rich, descriptive content with proper formatting
7. Expert interviews in quote boxes
8. Engaging conclusion with CTA

HTML FORMATTING EXAMPLES:
- Headlines: <h2 style='color: #2B0B3A; font-size: 2.5rem; font-weight: 700;'>Title</h2>
- Subtitles: <p class='lead text-center' style='color: #FF4081; font-size: 1.3rem;'>Subtitle</p>
- Pull quotes: <blockquote class='blockquote text-center p-4 my-4' style='background: #E6D8F2; border-left: 4px solid #FF4081;'><p style='color: #2B0B3A; font-style: italic; font-size: 1.2rem;'>Quote text</p></blockquote>
- Expert quotes: <div class='card border-0 mb-4' style='background: #F5F5F5;'><div class='card-body'><p style='font-style: italic;'>Expert opinion</p><footer class='blockquote-footer' style='color: #FF4081;'>Expert Name</footer></div></div>
- Highlights: <mark style='background: #E6D8F2; color: #2B0B3A; padding: 2px 6px;'>highlighted text</mark>

CONTENT GUIDELINES:
- Use storytelling techniques with proper HTML structure
- Include compelling anecdotes in formatted sections
- Rich, descriptive language with visual elements
- Expert perspectives in styled quote boxes
- Industry insights in highlighted cards
- Engaging, conversational tone
- Visual content descriptions
- Thought-provoking questions in callout boxes

LENGTH: 2000-3000 words
READING LEVEL: Accessible but sophisticated
";
    }

    /**
     * Minimal clean prompt
     */
    private function getMinimalPrompt($options) {
        return "
TEMPLATE: Minimal Clean
STYLE: Clear, concise, distraction-free
TARGET AUDIENCE: Focused readers seeking specific information

FORMAT REQUIREMENTS:
- Generate content as clean, minimal HTML (NO markdown)
- Use minimal Bootstrap classes for structure
- Focus on typography and whitespace
- Use AdZeta brand colors sparingly for maximum impact
- NO <html>, <head>, or <body> tags - content only

COLOR PALETTE (MINIMAL USE):
- Primary Purple: #2B0B3A (main headlines only)
- Accent Pink: #FF4081 (key highlights, CTAs)
- Deep Charcoal: #1A1A1A (body text)
- Light Grey: #F5F5F5 (subtle dividers)

STRUCTURE REQUIREMENTS:
1. Clean <h2> headline with minimal styling
2. Brief, informative introduction paragraph
3. Well-organized main content with proper spacing
4. Short, scannable paragraphs
5. Clean bullet points and numbered lists
6. Minimal but effective <h3> subheadings
7. Concise conclusion with subtle CTA

HTML FORMATTING EXAMPLES:
- Headlines: <h2 style='color: #2B0B3A; font-weight: 600; margin-bottom: 1.5rem;'>Title</h2>
- Subheadings: <h3 style='color: #1A1A1A; font-weight: 500; margin: 2rem 0 1rem;'>Subtitle</h3>
- Lists: <ul class='list-unstyled'><li style='margin-bottom: 0.5rem;'>• Item</li></ul>
- Highlights: <strong style='color: #FF4081;'>important text</strong>
- Dividers: <hr style='border: none; border-top: 1px solid #F5F5F5; margin: 2rem 0;'>
- CTAs: <p class='text-center mt-4'><a href='#' style='color: #FF4081; text-decoration: none; font-weight: 500;'>Learn More →</a></p>

CONTENT GUIDELINES:
- Clear, direct language with proper HTML structure
- Short sentences and paragraphs with adequate spacing
- Focus on essential information in clean formatting
- Eliminate unnecessary words and styling
- Use active voice throughout
- Scannable format with proper HTML semantics
- High information density
- Practical, actionable content in organized lists

LENGTH: 800-1500 words
READING LEVEL: Clear and accessible
";
    }

    /**
     * Case study prompt
     */
    private function getCaseStudyPrompt($options) {
        return "
TEMPLATE: Case Study
STYLE: Data-driven, analytical, results-focused
TARGET AUDIENCE: Decision-makers, analysts, potential clients

FORMAT REQUIREMENTS:
- Generate content as structured HTML (NO markdown)
- Use Bootstrap 5 classes for data presentation
- Include charts, metrics, and data visualizations
- Use AdZeta brand colors for professional presentation
- NO <html>, <head>, or <body> tags - content only

COLOR PALETTE:
- Primary Purple: #2B0B3A (section headers, key metrics)
- Accent Pink: #FF4081 (positive results, CTAs)
- Secondary Lavender: #E6D8F2 (metric backgrounds)
- Deep Charcoal: #1A1A1A (body text)
- Light Grey: #F5F5F5 (data table backgrounds)

STRUCTURE REQUIREMENTS:
1. Executive Summary with key metrics in styled cards
2. Challenge/Problem Statement with visual elements
3. Solution Overview with process diagrams
4. Implementation Process with timeline
5. Results and Metrics in data tables/charts
6. Key Learnings in highlighted sections
7. Conclusion and Next Steps with CTA

HTML FORMATTING EXAMPLES:
- Section Headers: <h2 style='color: #2B0B3A; font-weight: 600; border-bottom: 2px solid #FF4081; padding-bottom: 0.5rem;'>Section Title</h2>
- Metric Cards: <div class='row'><div class='col-md-4'><div class='card text-center border-0' style='background: #E6D8F2;'><div class='card-body'><h3 style='color: #2B0B3A; font-size: 2.5rem; font-weight: 700;'>247%</h3><p style='color: #1A1A1A; margin: 0;'>ROAS Improvement</p></div></div></div></div>
- Data Tables: <div class='table-responsive'><table class='table table-borderless'><thead style='background: #F5F5F5;'><tr><th style='color: #2B0B3A;'>Metric</th><th style='color: #2B0B3A;'>Before</th><th style='color: #2B0B3A;'>After</th></tr></thead><tbody><tr><td>Cost per Acquisition</td><td>$45</td><td style='color: #FF4081; font-weight: 600;'>$18</td></tr></tbody></table></div>
- Timeline: <div class='d-flex align-items-center mb-3'><div class='bg-primary rounded-circle' style='width: 12px; height: 12px; background: #FF4081 !important;'></div><div class='ms-3'><strong>Week 1-2:</strong> Initial setup and configuration</div></div>
- Callouts: <div class='alert border-0' style='background: #E6D8F2; border-left: 4px solid #FF4081 !important;'><strong style='color: #2B0B3A;'>Key Insight:</strong> Implementation insight here</div>

CONTENT GUIDELINES:
- Lead with quantifiable results in styled metric cards
- Include specific metrics and KPIs in data tables
- Detailed problem analysis with visual elements
- Step-by-step solution process with timelines
- Before/after comparisons in formatted tables
- Timeline and milestones with visual indicators
- Lessons learned in highlighted callout boxes
- Actionable insights in organized sections

REQUIRED ELEMENTS:
- Specific percentage improvements in metric cards
- Dollar amounts or cost savings in data tables
- Timeline information with visual elements
- Implementation challenges in callout boxes
- Success metrics in charts/tables
- Client testimonials in styled quote boxes

LENGTH: 2000-3500 words
READING LEVEL: Professional/Technical
";
    }

    /**
     * AdZeta-specific context
     */
    private function getAdZetaContext() {
        return "
COMPANY CONTEXT:
AdZeta is a leading performance marketing platform specializing in:
- AI-powered advertising optimization
- Value-based bidding strategies
- Customer lifetime value (CLV) modeling
- Marketing attribution and analytics
- E-commerce growth solutions

BRAND VOICE:
- Expert and authoritative
- Data-driven and analytical
- Innovation-focused
- Results-oriented
- Professional yet approachable

KEY TOPICS TO REFERENCE:
- Performance marketing
- AI and machine learning in advertising
- Value-based bidding
- Customer acquisition cost (CAC)
- Return on ad spend (ROAS)
- Marketing attribution
- E-commerce optimization
- Digital marketing automation

CALL-TO-ACTION OPTIONS:
- Book a free consultation
- Start a free trial
- Download a whitepaper
- Request a demo
- Get a performance audit

COLOR PALETTE CONTEXT:
Use language that evokes:
- Primary Purple (#2B0B3A): Authority, expertise, premium
- Accent Pink (#FF4081): Innovation, energy, results
- Professional and sophisticated tone
";
    }

    /**
     * Parse generated content into structured format
     */
    private function parseGeneratedContent($content, $template) {
        // Extract title, excerpt, and main content
        $lines = explode("\n", $content);
        $title = '';
        $excerpt = '';
        $mainContent = '';

        $inContent = false;
        $excerptFound = false;

        foreach ($lines as $line) {
            $line = trim($line);

            if (empty($line)) continue;

            // Extract title (first non-empty line or line starting with #)
            if (empty($title) && !empty($line)) {
                $title = preg_replace('/^#+\s*/', '', $line);
                // Clean HTML tags and markdown from title
                $title = strip_tags($title);
                $title = preg_replace('/```\w*/', '', $title); // Remove code block markers
                $title = preg_replace('/\*\*([^*]+)\*\*/', '$1', $title); // Remove **bold**
                $title = preg_replace('/\*([^*]+)\*/', '$1', $title); // Remove *italic*
                $title = trim($title);
                continue;
            }

            // Extract excerpt (first paragraph after title)
            if (!$excerptFound && !empty($line) && !preg_match('/^#+/', $line)) {
                // Clean excerpt: remove HTML tags and markdown formatting
                $cleanExcerpt = strip_tags($line);
                $cleanExcerpt = preg_replace('/\*\*([^*]+)\*\*/', '$1', $cleanExcerpt); // Remove **bold**
                $cleanExcerpt = preg_replace('/\*([^*]+)\*/', '$1', $cleanExcerpt); // Remove *italic*
                $cleanExcerpt = preg_replace('/_{2}([^_]+)_{2}/', '$1', $cleanExcerpt); // Remove __bold__
                $cleanExcerpt = preg_replace('/_([^_]+)_/', '$1', $cleanExcerpt); // Remove _italic_
                $cleanExcerpt = trim($cleanExcerpt);

                $excerpt = $cleanExcerpt;
                $excerptFound = true;
                continue;
            }

            // Main content
            if ($excerptFound) {
                $mainContent .= $line . "\n";
            }
        }

        // Generate SEO metadata
        $seoData = $this->generateSEOMetadata($title, $excerpt, $mainContent);

        return [
            'title' => $title,
            'excerpt' => $excerpt,
            'content' => $mainContent,
            'template' => $template,
            'seo' => $seoData,
            'reading_time' => $this->calculateReadingTime($mainContent),
            'word_count' => str_word_count(strip_tags($mainContent))
        ];
    }

    /**
     * Add template-specific modules to content
     */
    private function addTemplateModules($content, $template, $topic) {
        switch ($template) {
            case 'professional-article':
                $content['content'] = $this->addProfessionalModules($content['content'], $topic);
                break;

            case 'modern-magazine':
                $content['content'] = $this->addMagazineModules($content['content'], $topic);
                break;

            case 'case-study':
                $content['content'] = $this->addCaseStudyModules($content['content'], $topic);
                break;
        }

        return $content;
    }

    /**
     * Add professional article modules
     */
    private function addProfessionalModules($content, $topic) {
        // Add key takeaways box after first paragraph
        $paragraphs = explode("\n\n", $content);
        if (count($paragraphs) > 1) {
            $takeaways = "
<div class=\"takeaways-module\">
    <h3>Key Takeaways</h3>
    <ul>
        <li>Comprehensive understanding of {$topic}</li>
        <li>Actionable strategies for immediate implementation</li>
        <li>Data-driven insights for better decision making</li>
        <li>Best practices from industry experts</li>
    </ul>
</div>
";
            array_splice($paragraphs, 1, 0, $takeaways);
        }

        // Add spotlight module in the middle
        $midPoint = floor(count($paragraphs) / 2);
        $spotlight = "
<div class=\"spotlight-module\">
    <img src=\"/images/adzeta-platform-preview.svg\" alt=\"AdZeta Platform\" class=\"spotlight-image\">
    <div class=\"spotlight-content\">
        <h3>Ready to Optimize Your Performance Marketing?</h3>
        <p>AdZeta's AI-powered platform helps you implement the strategies discussed in this article with advanced automation and optimization.</p>
        <a href=\"/demo\" class=\"spotlight-cta\">Get Free Demo</a>
    </div>
</div>
";
        array_splice($paragraphs, $midPoint, 0, $spotlight);

        return implode("\n\n", $paragraphs);
    }

    /**
     * Add magazine modules
     */
    private function addMagazineModules($content, $topic) {
        // Add pull quotes
        $content = preg_replace(
            '/([.!?])\s+([A-Z][^.!?]*[.!?])\s+/',
            '$1 <div class="magazine-pullquote">$2</div> ',
            $content,
            1
        );

        return $content;
    }

    /**
     * Add case study modules
     */
    private function addCaseStudyModules($content, $topic) {
        // Add stats and metrics sections
        $statsSection = "
<div class=\"stats-grid\">
    <div class=\"stat-card\">
        <div class=\"stat-icon\"><i class=\"fas fa-chart-line\"></i></div>
        <div class=\"stat-value\">+247%</div>
        <div class=\"stat-label\">Performance Improvement</div>
    </div>
    <div class=\"stat-card\">
        <div class=\"stat-icon\"><i class=\"fas fa-dollar-sign\"></i></div>
        <div class=\"stat-value\">$1.2M</div>
        <div class=\"stat-label\">Additional Revenue</div>
    </div>
    <div class=\"stat-card\">
        <div class=\"stat-icon\"><i class=\"fas fa-percentage\"></i></div>
        <div class=\"stat-value\">-68%</div>
        <div class=\"stat-label\">Cost Reduction</div>
    </div>
</div>
";

        // Insert after first section
        $content = preg_replace('/(<h2.*?<\/h2>.*?<\/p>)/s', '$1' . $statsSection, $content, 1);

        return $content;
    }

    /**
     * Generate SEO metadata
     */
    private function generateSEOMetadata($title, $excerpt, $content) {
        // Extract keywords from content
        $keywords = $this->extractKeywords($content);

        return [
            'meta_title' => $title . ' - AdZeta Blog',
            'meta_description' => substr($excerpt, 0, 155),
            'meta_keywords' => implode(', ', array_slice($keywords, 0, 10)),
            'focus_keyword' => $keywords[0] ?? '',
            'og_title' => $title,
            'og_description' => substr($excerpt, 0, 200),
            'twitter_card_type' => 'summary_large_image'
        ];
    }

    /**
     * Extract keywords from content
     */
    private function extractKeywords($content) {
        // Simple keyword extraction
        $text = strip_tags($content);
        $words = str_word_count($text, 1);

        // Filter common words
        $commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'];

        $keywords = array_filter($words, function($word) use ($commonWords) {
            return strlen($word) > 3 && !in_array(strtolower($word), $commonWords);
        });

        // Count frequency and return top keywords
        $keywordCounts = array_count_values(array_map('strtolower', $keywords));
        arsort($keywordCounts);

        return array_keys($keywordCounts);
    }

    /**
     * Calculate reading time
     */
    private function calculateReadingTime($content) {
        $wordCount = str_word_count(strip_tags($content));
        return max(1, ceil($wordCount / 200)); // 200 words per minute
    }

    /**
     * Generate blog post suggestions based on trending topics
     */
    public function generateTopicSuggestions($category = null, $count = 5) {
        $topics = [
            'performance-marketing' => [
                'AI-Powered Performance Marketing Strategies for 2025',
                'Value-Based Bidding: Complete Implementation Guide',
                'Customer Lifetime Value Optimization Techniques',
                'Advanced Attribution Modeling for E-commerce',
                'Automated Campaign Optimization Best Practices'
            ],
            'ai-technology' => [
                'Machine Learning in Digital Advertising',
                'AI-Driven Customer Segmentation Strategies',
                'Predictive Analytics for Marketing ROI',
                'Automated Creative Optimization Techniques',
                'AI-Powered Audience Targeting Methods'
            ],
            'e-commerce' => [
                'E-commerce Growth Hacking Strategies',
                'Conversion Rate Optimization for Online Stores',
                'Mobile Commerce Optimization Guide',
                'Cross-Selling and Upselling Automation',
                'E-commerce Analytics and KPI Tracking'
            ]
        ];

        if ($category && isset($topics[$category])) {
            return array_slice($topics[$category], 0, $count);
        }

        // Return mixed topics
        $allTopics = array_merge(...array_values($topics));
        shuffle($allTopics);
        return array_slice($allTopics, 0, $count);
    }
}
