<?php
// =================================================================
// 1. DATA RETRIEVAL & SETUP (Unchanged)
// =================================================================
if (isset($currentPost)) { $post = $currentPost; }
elseif (isset($GLOBALS['currentPost'])) { $post = $GLOBALS['currentPost']; }
else {
    $slug = $_GET['slug'] ?? '';
    if (!empty($slug)) {
        require_once __DIR__ . '/../../../includes/BlogDatabase.php';
        $post = getBlogPostBySlug($slug);
    }
}

if (empty($post)) {
    header('HTTP/1.0 404 Not Found');
    include __DIR__ . '/../../../404.php';
    exit;
}



// Set global SEO data for header.php to use
$GLOBALS['override_seo_data'] = [
    'page_title' => ($post['meta_title'] ?: $post['title']) . ' - AdZeta',
    'meta_description' => $post['meta_description'] ?: $post['excerpt'],
    'meta_keywords' => $post['meta_keywords'] ?? '',
    'canonical_url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/blog/' . $post['slug'],
    'og_title' => $post['og_title'] ?: ($post['meta_title'] ?: $post['title']),
    'og_description' => $post['og_description'] ?: $post['excerpt'],
    'og_image' => $post['featured_image'] ? 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/' . ltrim($post['featured_image'], '/') : 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/images/adzeta-og-default.jpg',
    'og_type' => 'article'
];

$pageData = $GLOBALS['override_seo_data'];

$authorName = 'AdZeta Team';
if (!empty($post['author_name'])) { $authorName = $post['author_name']; }
elseif (!empty($post['first_name']) || !empty($post['last_name'])) { $authorName = trim($post['first_name'] . ' ' . $post['last_name']); }
$authorTitle = 'Growth Marketing Team';
if (!empty($post['author_bio'])) { $authorTitle = strtok($post['author_bio'], "\n"); }
$authorAvatar = !empty($post['author_avatar']) ? $post['author_avatar'] : 'images/case-studies/Natalie-Brooks-adzeta.jpg';
$authorLink = !empty($post['author_id']) ? '/blog/author/' . $post['author_id'] : '#';

include $_SERVER['DOCUMENT_ROOT'] . '/header.php'; ?>

<section class="ipad-top-space-margin bg-dark-gray cover-background one-third-screen d-flex align-items-center" style="background-image: url('<?php echo !empty($post['featured_image']) ? $post['featured_image'] : 'images/post-1-bg.webp'; ?>')">
    <div class="background-position-center-top h-100 w-100 position-absolute left-0px top-0" style="background-image: url('images/vertical-line-bg-small.svg')"></div>
    <div id="particles-style-01" class="h-100 position-absolute left-0px top-0 w-100" data-particle="true" data-particle-options='{"particles": {"number": {"value": 35,"density": {"enable": true,"value_area": 1500}},"color": {"value": ["#ff5d74", "#e958a1", "#ff8cc6", "8f76f5", "#00d2ff", "#3a7bd5"]},"shape": {"type": "circle","stroke":{"width":0,"color":"#000000"}},"opacity": {"value": 0.9,"random": true,"anim": {"enable": true,"speed": 1,"opacity_min": 0.5,"sync": false}},"size": {"value": 8,"random": true,"anim": {"enable": true,"speed": 2,"size_min": 3,"sync": false}},"line_linked":{"enable":false,"distance":0,"color":"#ffffff","opacity":1,"width":1},"move": {"enable": true,"speed":1.2,"direction": "right","random": true,"straight": false,"out_mode": "out"}},"interactivity": {"detect_on": "canvas","events": {"onhover": {"enable": true,"mode": "bubble"},"onclick": {"enable": true,"mode": "push"},"resize": true},"modes": {"bubble": {"distance": 100,"size": 12,"duration": 1,"opacity": 1,"speed": 3},"push": {"particles_nb": 4}}},"retina_detect": false}'></div>
    <div class="opacity-medium bg-dark-gray"></div>
    <div class="container ">  
        <div class="row justify-content-center">
            <div class="col-lg-10 position-relative" data-anime='{ "el": "childs", "opacity": [0, 1], "translateX": [50, 0], "staggervalue": 100, "easing": "easeOutQuad" }'>
                <div class="d-inline-block mb-20px sm-mb-25px">
                    <span class="text-white fs-16 opacity-5">
                        <a href="/blog/archives/<?php echo date('Y/m', strtotime($post['published_at'])); ?>" class="text-white"><?php echo date('j F Y', strtotime($post['published_at'])); ?></a>
                        <span class="d-inline-block fs-24 align-top ms-10px me-10px">•</span>
                        <a href="/blog/category/<?php echo $post['category_slug'] ?? 'digital-marketing'; ?>" class="text-white"><?php echo strtoupper($post['category_name'] ?? 'DIGITAL MARKETING'); ?></a>
                    </span>
                </div>
                <h1 class="text-white w-60 lg-w-80 md-w-70 sm-w-100 fw-500 fs-40 ls-minus-2px alt-font mb-30px overflow-hidden mb-0"><?php echo htmlspecialchars($post['title']); ?></h1>
                <div class="text-white fs-16 mt-40px d-flex align-items-center">
                    <img class="w-80px h-80px rounded-circle me-20px sm-me-15px border border-1 border-color-white" src="<?php echo htmlspecialchars($authorAvatar); ?>" alt="<?php echo htmlspecialchars($authorName); ?>">
                    <div class="author-info flex-grow-1">
                        <div class="author-byline mb-5px d-flex flex-wrap align-items-baseline">
                            <span class="fs-15 opacity-7 fst-italic me-2">Written by</span>
                            <a href="<?php echo htmlspecialchars($authorLink); ?>" class="text-white text-decoration-line-bottom fw-600 text-nowrap"><?php echo htmlspecialchars($authorName); ?></a>
                        </div>
                        <div class="author-title fs-14 opacity-8 fst-italic"><?php echo htmlspecialchars($authorTitle); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
 /* --- 1. Root & Color Palette --- */
:root {
    /* User's Color Palette */
    --color-primary: #de347f;
    --color-accent: #ff5d74;
    --color-bg-soft: #f8f9ff;
    --color-bg-light: #fff5f8;
    --color-border-strong: #de347f;
    --color-border-light: #e9ecef;
    --color-white: #ffffff;

    /* Derived & System Colors */
    --color-text-main: #212529; /* Dark, for headings */
    --color-text-secondary: #495057; /* Softer, for body text */
    --color-text-subtle: #6c757d;
    --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-accent));

    /* Typography */
    --font-family-main: 'Proxima Nova', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --font-size-base: 1.05rem; /* ~17px, slightly larger base for mobile */
    --line-height-base: 1.7;

    /* Layout */
    --border-radius: 12px;
    --container-max-width: 85ch; /* Optimal for readability */
}

/* --- 2. General Body and Layout --- */
body {
    background-color: var(--color-bg-soft);
    color: var(--color-text-main);
    font-family: var(--font-family-main);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    margin: 0;
    padding: 0;
}

.article-section-container {
    background-color: var(--color-white);
    padding: 2.5rem 1rem; /* Adjusted padding for mobile */
}

.article-content {
    max-width: var(--container-max-width);
    margin: 0 auto;
}

/* --- 3. Typography Hierarchy --- */
.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5 {
    font-weight: 700;
    color: var(--color-text-main);
    line-height: 1.3;
    margin: 2.5rem 0 1.5rem 0;
}

.article-content h1 { font-size: 2.25rem; }
.article-content h2 { font-size: 1.875rem; border-bottom: 2px solid var(--color-border-light); padding-bottom: 0.5rem; }
.article-content h3 { font-size: 1.5rem; }
.article-content h4 { font-size: 1.25rem; }

.article-content p {
    font-size: 1.125rem;
    color: var(--color-text-secondary);
    margin-bottom: 1.5rem;
}

.article-content a {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.article-content a:hover {
    color: var(--color-accent);
    text-decoration: underline;
}

/* --- UPDATED: Cohesive styles and minimal list styling --- */
.article-content ul,
.article-content ol {
    font-size: 1.125rem;
    color: var(--color-text-secondary);
    margin-bottom: 1.5rem;
    padding-left: 1.75rem;
}

.article-content li {
    margin-bottom: 0.75rem; /* Adds space between list items */
}

/* --- NEW: Minimal list marker styling --- */
.article-content ul li::marker {
    color: var(--color-primary);
    font-size: 1.1em; /* Makes the bullet slightly larger */
}

.article-content ol li::marker {
    color: var(--color-primary);
    font-weight: 700; /* Makes the number bold */
}

/* --- 4. Enhanced Content Blocks --- */
/* ... (rest of the CSS remains the same) ... */
.key-takeaways, .article-content blockquote, .stats-grid, .final-cta {
    /* All previous styles for these sections are unchanged */
}
/* All other styles from the previous version remain the same */
/* Enhanced Content Cards */
			.content-card {
	background: #ffffff;
	border: 1px solid #e9ecef;
	border-radius: 16px;
	padding: 32px;
	margin-bottom: 24px;
	box-shadow: 0 2px 12px rgba(0,0,0,0.04);
	transition: all 0.3s ease;
}

.content-card:hover {
	box-shadow: 0 4px 20px rgba(0,0,0,0.08);
	transform: translateY(-1px);
}

.highlight-card {
	background: linear-gradient(135deg, #f8f9ff, #fff5f8);
	border: 1px solid #e3e8ff;
}

.key-takeaways {
    background-color: var(--color-bg-light);
    border: 1px solid var(--color-border-light);
    border-left: 4px solid var(--color-primary);
    padding: 1.5rem;
    margin: 2rem 0 3rem 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.key-takeaways h4 {
    margin-top: 0;
    color: var(--color-primary);
    font-size: 1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.key-takeaways ul {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
}

.key-takeaways li {
    position: relative;
    padding-left: 1.75rem;
    margin-bottom: 0.5rem;
}

.key-takeaways li::before {
    content: '✓';
    position: absolute;
    left: 0;
    top: 1px;
    color: var(--color-primary);
    font-weight: 800;
}

.article-content blockquote {
    margin: 2.5rem 0;
    padding-left: 1.5rem;
    border-left: 3px solid var(--color-primary);
    font-size: 1.25rem;
    font-weight: 600;
    font-style: italic;
    color: var(--color-text-main);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    margin: 3rem 0;
    text-align: center;
}

.stat-card {
    background-color: var(--color-white);
    padding: 2rem 1rem;
    border: 1px solid var(--color-border-light);
    border-radius: var(--border-radius);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.07);
}

.stat-number {
    display: block;
    font-size: 2.75rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    line-height: 1;
}

.stat-label {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--color-text-subtle);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.final-cta {
    margin-top: 4rem;
    padding: 2.5rem 1.5rem;
    text-align: center;
    background: var(--gradient-primary);
    color: var(--color-white);
    border-radius: var(--border-radius);
}

.final-cta h3 {
    color: var(--color-white);
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.75rem;
    font-weight: 800;
}

.final-cta p {
    max-width: 550px;
    margin: 0 auto 2rem;
    opacity: 0.9;
    font-size: 1.1rem;
    color: var(--color-white);
}

.cta-button {
    display: inline-block;
    background: var(--color-white);
    color: var(--color-primary);
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.cta-button:hover {
    color: var(--color-primary);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* --- 7. Responsive Design (Desktop) --- */
@media (min-width: 768px) {
    .article-section-container {
        padding: 5rem;
    }

    .article-content h1 { font-size: 3.5rem; }
    .article-content h2 { font-size: 2.5rem; }
    .article-content h3 { font-size: 2rem; }
    .article-content h4 { font-size: 1.5rem; }

    .key-takeaways {
        padding: 2rem 2.5rem;
    }

    .final-cta {
        padding: 3.5rem;
    }

    .final-cta h3 {
        font-size: 2rem;
    }

    .cta-button {
        padding: 1rem 2.5rem;
        font-size: 1.1rem;
    }
}
</style>

<section class="article-section-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-12"> 

                <div class="article-content">
                    <?php
                    // Process content through EditorJSParser if content_blocks are available
                    if (!empty($post['content_blocks']) && is_array($post['content_blocks'])) {
                        // Use EditorJSParser to render custom blocks
                        require_once __DIR__ . '/../src/Services/EditorJSParser.php';
                        $parser = new \AdZetaAdmin\Services\EditorJSParser();

                        // Extract blocks from Editor.js format
                        $blocks = $post['content_blocks']['blocks'] ?? $post['content_blocks'];

                        if (is_array($blocks) && !empty($blocks)) {
                            echo $parser->parseToHTML($blocks);
                        } else {
                            // Fallback to regular content
                            echo $post['content'];
                        }
                    } else {
                        // Fallback to regular content
                        echo $post['content'];
                    }
                    ?>
                </div>

               

            </div>
        </div>
    </div>
</section>

<?php include $_SERVER['DOCUMENT_ROOT'] . '/footer.php'; ?>