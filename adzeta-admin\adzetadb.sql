-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 28, 2025 at 03:50 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `adzetadb`
--

-- --------------------------------------------------------

--
-- Table structure for table `ai_content_cache`
--

CREATE TABLE `ai_content_cache` (
  `id` int(11) NOT NULL,
  `cache_key` varchar(255) NOT NULL,
  `prompt_hash` varchar(64) NOT NULL,
  `response_content` longtext NOT NULL,
  `model_used` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ai_usage_logs`
--

CREATE TABLE `ai_usage_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action_type` varchar(50) NOT NULL,
  `prompt_tokens` int(11) DEFAULT 0,
  `completion_tokens` int(11) DEFAULT 0,
  `total_tokens` int(11) DEFAULT 0,
  `model_used` varchar(100) DEFAULT NULL,
  `success` tinyint(1) DEFAULT 1,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `api_error_logs`
--

CREATE TABLE `api_error_logs` (
  `id` int(11) NOT NULL,
  `request_id` varchar(100) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `user_id` int(11) DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `method` varchar(10) NOT NULL,
  `endpoint` varchar(500) NOT NULL,
  `full_url` varchar(1000) NOT NULL,
  `request_data` longtext DEFAULT NULL,
  `request_headers` longtext DEFAULT NULL,
  `status_code` int(11) DEFAULT NULL,
  `response_data` longtext DEFAULT NULL,
  `response_time` int(11) DEFAULT NULL COMMENT 'Response time in milliseconds',
  `error_type` varchar(100) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `error_stack` longtext DEFAULT NULL,
  `error_context` longtext DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `page_url` varchar(1000) DEFAULT NULL,
  `referrer` varchar(1000) DEFAULT NULL,
  `severity` enum('low','medium','high','critical') DEFAULT 'medium',
  `category` varchar(50) DEFAULT 'api_error',
  `is_resolved` tinyint(1) DEFAULT 0,
  `resolved_by` int(11) DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `resolution_notes` text DEFAULT NULL,
  `browser_info` longtext DEFAULT NULL,
  `device_info` varchar(200) DEFAULT NULL,
  `admin_version` varchar(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `api_error_logs`
--

INSERT INTO `api_error_logs` (`id`, `request_id`, `timestamp`, `user_id`, `session_id`, `method`, `endpoint`, `full_url`, `request_data`, `request_headers`, `status_code`, `response_data`, `response_time`, `error_type`, `error_message`, `error_stack`, `error_context`, `user_agent`, `ip_address`, `page_url`, `referrer`, `severity`, `category`, `is_resolved`, `resolved_by`, `resolved_at`, `resolution_notes`, `browser_info`, `device_info`, `admin_version`) VALUES
(1, 'req_1750428281_sample1', '2025-06-20 14:04:41', NULL, NULL, 'PUT', '/posts/20', 'http://localhost/adzeta-admin/api/posts/20', NULL, NULL, 400, NULL, NULL, 'ValidationError', 'Invalid content_blocks JSON format', NULL, NULL, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, '/adzeta-admin/?view=posts&action=edit&id=20', NULL, 'medium', 'validation', 0, NULL, NULL, NULL, NULL, NULL, NULL),
(2, 'req_1750428282_sample2', '2025-06-20 14:04:41', NULL, NULL, 'POST', '/media/upload', 'http://localhost/adzeta-admin/api/media/upload', NULL, NULL, 500, NULL, NULL, 'ServerError', 'Failed to create upload directory', NULL, NULL, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NULL, '/adzeta-admin/?view=media', NULL, 'high', 'file_system', 0, NULL, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `blog_categories`
--

CREATE TABLE `blog_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `blog_categories`
--

INSERT INTO `blog_categories` (`id`, `name`, `slug`, `description`, `parent_id`, `meta_title`, `meta_description`, `meta_keywords`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Technology', 'technology', 'Latest technology trends and insights', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(2, 'Marketing', 'marketing', 'Digital marketing strategies and tips', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(3, 'Design', 'design', 'UI/UX design and creative inspiration', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(4, 'Business', 'business', 'Business strategies and entrepreneurship', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(5, 'AI & Machine Learning', 'ai-machine-learning', 'Artificial intelligence and ML developments', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(6, 'Web Development', 'web-development', 'Web development tutorials and best practices', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(7, 'Case Studies', 'case-studies', 'Client success stories and implementation case studies', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(8, 'Client Success Stories', 'client-success-stories', 'Detailed client success stories with results and testimonials', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(9, 'Implementation Studies', 'implementation-studies', 'Technical implementation case studies and methodologies', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(10, 'ROI Analysis', 'roi-analysis', 'Return on investment analysis and financial impact studies', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(11, 'Industry Case Studies', 'industry-case-studies', 'Industry-specific case studies and sector analysis', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(12, 'Product Case Studies', 'product-case-studies', 'Product implementation and usage case studies', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(13, 'Whitepapers', 'whitepapers', 'Research whitepapers and industry analysis', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(14, 'Research Papers', 'research-papers', 'In-depth research papers and academic studies', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(15, 'Industry Reports', 'industry-reports', 'Comprehensive industry analysis and market reports', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(16, 'Best Practices', 'best-practices', 'Best practice guides and methodology papers', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(17, 'Technical Whitepapers', 'technical-whitepapers', 'Technical analysis and implementation guides', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(18, 'Market Analysis', 'market-analysis', 'Market research and competitive analysis papers', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(19, 'Research Reports', 'research-reports', 'Data-driven research reports and analysis', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(20, 'Market Research', 'market-research', 'Market size, trends, and competitive landscape research', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(21, 'Trend Reports', 'trend-reports', 'Industry trend analysis and future predictions', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(22, 'Competitive Analysis', 'competitive-analysis', 'Competitive landscape and positioning analysis', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(23, 'Consumer Research', 'consumer-research', 'Consumer behavior and preference studies', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(24, 'Technology Research', 'technology-research', 'Technology adoption and impact research', NULL, NULL, NULL, NULL, 0, 'active', '2025-06-17 08:30:46', '2025-06-17 08:30:46');

-- --------------------------------------------------------

--
-- Table structure for table `blog_posts`
--

CREATE TABLE `blog_posts` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` longtext DEFAULT NULL,
  `content_blocks` longtext DEFAULT NULL,
  `excerpt` text DEFAULT NULL,
  `featured_image` varchar(255) DEFAULT NULL,
  `author_id` int(11) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `status` enum('draft','published','archived','scheduled') NOT NULL DEFAULT 'draft',
  `visibility` enum('public','private','password') NOT NULL DEFAULT 'public',
  `password` varchar(255) DEFAULT NULL,
  `published_at` datetime DEFAULT NULL,
  `scheduled_at` datetime DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `focus_keyword` varchar(100) DEFAULT NULL,
  `canonical_url` varchar(255) DEFAULT NULL,
  `og_title` varchar(255) DEFAULT NULL,
  `og_description` text DEFAULT NULL,
  `og_image` varchar(255) DEFAULT NULL,
  `twitter_card_type` varchar(50) DEFAULT 'summary_large_image',
  `noindex` tinyint(1) DEFAULT 0,
  `nofollow` tinyint(1) DEFAULT 0,
  `allow_comments` tinyint(1) DEFAULT 1,
  `comment_count` int(11) DEFAULT 0,
  `view_count` int(11) DEFAULT 0,
  `like_count` int(11) DEFAULT 0,
  `share_count` int(11) DEFAULT 0,
  `reading_time` int(11) DEFAULT 0,
  `word_count` int(11) DEFAULT 0,
  `seo_score` decimal(5,2) DEFAULT 0.00,
  `template` varchar(50) DEFAULT 'basic-post',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `blog_posts`
--

INSERT INTO `blog_posts` (`id`, `title`, `slug`, `content`, `content_blocks`, `excerpt`, `featured_image`, `author_id`, `category_id`, `status`, `visibility`, `password`, `published_at`, `scheduled_at`, `meta_title`, `meta_description`, `meta_keywords`, `focus_keyword`, `canonical_url`, `og_title`, `og_description`, `og_image`, `twitter_card_type`, `noindex`, `nofollow`, `allow_comments`, `comment_count`, `view_count`, `like_count`, `share_count`, `reading_time`, `word_count`, `seo_score`, `template`, `created_at`, `updated_at`) VALUES
(52, 'Marketing Simplified: A Concise Guide to Success', 'marketing-simplified-a-concise-guide-to-success', '<p>Marketing Simplified: A Concise Guide to SuccessMarketing Simplified: A Concise Guide to Success\n\nIn today\'s fast-paced business environment, marketing can often feel overwhelming. This post aims to cut through the noise and provide a concise, actionable guide to marketing success. We\'ll cover the essential elements, from understanding your audience to measuring your results, all in a streamlined and easy-to-digest format.</p>\n<p>Understanding Your Target Audience\n\nThe foundation of any successful marketing strategy is a deep understanding of your target audience.  Who are they? <mark class=\"adzeta-highlight\" style=\"background: rgb(255, 64, 129); color: white; padding: 2px 6px; border-radius: 4px; font-weight: 500;\">What are their needs</mark>, desires, and pain points? What platforms do they frequent?  <b>Without this knowledge</b>, your marketing efforts will be scattered and ineffective.  According to a HubSpot study, companies that understand their customers are 60% more profitable.</p>\n<ol><li>Define Your Ideal Customer: Create detailed buyer personas that represent your target audience.Conduct Market Research: Use surveys, interviews, and social listening to gather insights.Analyze Customer Data:</li><li> Leverage your CRM and analytics tools to understand customer behavior.Crafting a Compelling Message\n\nOnce you understand your audience, you need to craft a message that resonates with them. Your message should be clear, concise, and compelling, highlighting the value you offer and addressing their specific needs.  Remember, people don\'t buy products; they buy solutions to their problems.</li></ol>\n<p>A study by Nielsen found that consumers are exposed to thousands of marketing messages every day, but only a small fraction of those messages actually break through the clutter. To stand out, your message needs to be authentic and relevant.</p>\n<p>Focus on Benefits, Not Features: Explain how your product or service will improve their lives.Use Clear and Concise Language: Avoid jargon and technical terms that your audience may not understand.Tell a Story:  Connect with your audience on an emotional level by sharing compelling stories.Choosing the Right Channels\n\nWith a clear understanding of your audience and a compelling message, you need to choose the right channels to reach them. There are countless marketing channels available, from social media to email marketing to search engine optimization (SEO). The key is to identify the channels where your target audience spends their time and focus your efforts there.</p>\n<p>According to Statista, digital advertising spending is projected to reach over $600 billion in 2023. This highlights the importance of digital channels in today\'s marketing landscape.</p>\n<p>Social Media Marketing: Engage with your audience on platforms like Facebook, Instagram, Twitter, and LinkedIn.Email Marketing:  Build relationships with your audience through personalized email campaigns.Search Engine Optimization (SEO):  Improve your website\'s ranking in search results to attract organic traffic.Content Marketing: Create valuable and informative content that attracts and engages your target audience.Measuring Your Results\n\nMarketing is not a one-time activity; it\'s an ongoing process of experimentation and optimization. You need to track your results closely to see what\'s working and what\'s not. This data will allow you to refine your strategy and improve your ROI.</p>\n<p>A study by McKinsey found that companies that use data-driven marketing are 6 times more likely to achieve their revenue goals.</p>\n<p>Track Key Metrics:  Monitor website traffic, lead generation, conversion rates, and customer acquisition cost.Use Analytics Tools:  Leverage tools like Google Analytics and HubSpot to track your marketing performance.A/B Testing: Experiment with different marketing messages and tactics to see what resonates best with your audience.Actionable TakeawaysPrioritize Your Audience:  Always start with a deep understanding of your target audience.Craft a Compelling Message:  Focus on the benefits of your product or service and tell a story that resonates.Choose the Right Channels:  Focus your efforts on the channels where your target audience spends their time.Measure Your Results:  Track your key metrics and use data to optimize your marketing strategy.\n\n\nBy following these concise guidelines, you can simplify your marketing efforts and achieve greater success. Remember, marketing is an ongoing process, so stay adaptable, keep learning, and never stop experimenting.</p>\n<p>Ready to take your marketing to the next level? Contact us today for a free consultation!</p>', '{\"time\":1750804097149,\"blocks\":[{\"id\":\"9aa0s6vlnf\",\"type\":\"paragraph\",\"data\":{\"text\":\"Marketing Simplified: A Concise Guide to SuccessMarketing Simplified: A Concise Guide to Success\\n\\nIn today\'s fast-paced business environment, marketing can often feel overwhelming. This post aims to cut through the noise and provide a concise, actionable guide to marketing success. We\'ll cover the essential elements, from understanding your audience to measuring your results, all in a streamlined and easy-to-digest format.\"}},{\"id\":\"2pa2o0snc8\",\"type\":\"paragraph\",\"data\":{\"text\":\"Understanding Your Target Audience\\n\\nThe foundation of any successful marketing strategy is a deep understanding of your target audience.  Who are they? <mark class=\\\"adzeta-highlight\\\" style=\\\"background: rgb(255, 64, 129); color: white; padding: 2px 6px; border-radius: 4px; font-weight: 500;\\\">What are their needs</mark>, desires, and pain points? What platforms do they frequent?  <b>Without this knowledge</b>, your marketing efforts will be scattered and ineffective.  According to a HubSpot study, companies that understand their customers are 60% more profitable.\"}},{\"id\":\"m_o-4B0c2y\",\"type\":\"list\",\"data\":{\"style\":\"ordered\",\"items\":[\"Define Your Ideal Customer: Create detailed buyer personas that represent your target audience.Conduct Market Research: Use surveys, interviews, and social listening to gather insights.Analyze Customer Data:\",\"&nbsp;Leverage your CRM and analytics tools to understand customer behavior.Crafting a Compelling Message\\n\\nOnce you understand your audience, you need to craft a message that resonates with them. Your message should be clear, concise, and compelling, highlighting the value you offer and addressing their specific needs.  Remember, people don\'t buy products; they buy solutions to their problems.\"]}},{\"id\":\"fr34xhawl6\",\"type\":\"paragraph\",\"data\":{\"text\":\"A study by Nielsen found that consumers are exposed to thousands of marketing messages every day, but only a small fraction of those messages actually break through the clutter. To stand out, your message needs to be authentic and relevant.\"}},{\"id\":\"nwa48q2nwu\",\"type\":\"paragraph\",\"data\":{\"text\":\"Focus on Benefits, Not Features: Explain how your product or service will improve their lives.Use Clear and Concise Language: Avoid jargon and technical terms that your audience may not understand.Tell a Story:  Connect with your audience on an emotional level by sharing compelling stories.Choosing the Right Channels\\n\\nWith a clear understanding of your audience and a compelling message, you need to choose the right channels to reach them. There are countless marketing channels available, from social media to email marketing to search engine optimization (SEO). The key is to identify the channels where your target audience spends their time and focus your efforts there.\"}},{\"id\":\"rvqeerpuny\",\"type\":\"paragraph\",\"data\":{\"text\":\"According to Statista, digital advertising spending is projected to reach over $600 billion in 2023. This highlights the importance of digital channels in today\'s marketing landscape.\"}},{\"id\":\"ge5lo2ci2d\",\"type\":\"paragraph\",\"data\":{\"text\":\"Social Media Marketing: Engage with your audience on platforms like Facebook, Instagram, Twitter, and LinkedIn.Email Marketing:  Build relationships with your audience through personalized email campaigns.Search Engine Optimization (SEO):  Improve your website\'s ranking in search results to attract organic traffic.Content Marketing: Create valuable and informative content that attracts and engages your target audience.Measuring Your Results\\n\\nMarketing is not a one-time activity; it\'s an ongoing process of experimentation and optimization. You need to track your results closely to see what\'s working and what\'s not. This data will allow you to refine your strategy and improve your ROI.\"}},{\"id\":\"jjbtpw0t90\",\"type\":\"paragraph\",\"data\":{\"text\":\"A study by McKinsey found that companies that use data-driven marketing are 6 times more likely to achieve their revenue goals.\"}},{\"id\":\"hvfxft8bqq\",\"type\":\"paragraph\",\"data\":{\"text\":\"Track Key Metrics:  Monitor website traffic, lead generation, conversion rates, and customer acquisition cost.Use Analytics Tools:  Leverage tools like Google Analytics and HubSpot to track your marketing performance.A/B Testing: Experiment with different marketing messages and tactics to see what resonates best with your audience.Actionable TakeawaysPrioritize Your Audience:  Always start with a deep understanding of your target audience.Craft a Compelling Message:  Focus on the benefits of your product or service and tell a story that resonates.Choose the Right Channels:  Focus your efforts on the channels where your target audience spends their time.Measure Your Results:  Track your key metrics and use data to optimize your marketing strategy.\\n\\n\\nBy following these concise guidelines, you can simplify your marketing efforts and achieve greater success. Remember, marketing is an ongoing process, so stay adaptable, keep learning, and never stop experimenting.\"}},{\"id\":\"wmpdx8oc2u\",\"type\":\"paragraph\",\"data\":{\"text\":\"Ready to take your marketing to the next level? Contact us today for a free consultation!\"}}],\"version\":\"2.31.0-rc.7\"}', 'Unlock marketing success with this concise guide. Learn essential strategies, from targeting to analytics, in a few minutes. Boost your ROI now!', '', 1, 1, 'published', 'public', NULL, '2025-06-25 00:27:23', NULL, 'Concise Marketing Guide: Strategies for Success', 'Unlock marketing success with this concise guide. Learn essential strategies, from targeting to analytics, in minutes. Boost your ROI now!', 'marketing strategy, digital marketing, target audience, marketing tips, marketing guide, SEO, content marketing', 'marketing strategy', '', 'Marketing Simplified: A Concise Guide to Success', 'Unlock marketing success with this concise guide. Learn essential strategies, from targeting to analytics, in a few minutes. Boost your ROI now!', '', 'summary_large_image', 0, 0, 1, 0, 11, 0, 0, 4, 653, 0.00, 'professional-enhanced', '2025-06-24 22:17:37', '2025-06-27 17:27:45'),
(53, 'Unlock Growth: Mastering Value-Based Bidding in Digital Advertising', 'unlock-growth-mastering-value-based-bidding-in-digital-advertising', '<p>Unlock Growth: Mastering Value-Based Bidding in Digital Advertising</p>\n<h2>Unlock Growth: Mastering Value-Based Bidding in Digital Advertising</h2>\n<p>In the ever-evolving landscape of digital advertising, simply<u> driving traffic</u> to your website isn\'t enough. You need to attract the <i>right</i> traffic – the kind that converts into paying customers and generates significant revenue. <b>That\'s where</b> value-based bidding comes in.<i> This sophisticated strategy </i>allows you to optimize your ad campaigns not just for clicks or<mark class=\"cdx-marker\"> impressions</mark>, but for the actual value each customer brings to your business.</p>\n<h3>What is Value-Based Bidding?</h3>\n<p>Value-based bidding is a smart bidding strategy that focuses on maximizing your return on ad spend (ROAS) by assigning different values to different conversions. Unlike traditional bidding methods that treat all conversions equally, value-based bidding recognizes that some customers are more valuable than others. For example, a customer who purchases a high-margin product is worth more than a customer who only signs up for a free trial.</p>\n<p>hat some customers are more valuable than others. For example, a customer who purchases a high-margin product is worth more than a customer who only signs up for a free trial.</p>\n<p>Instead of bidding based on cost-per-click (CPC) or cost-per-acquisition (CPA), you bid based on the predicted value of each potential customer. This allows you to prioritize high-value conversions and allocate your budget more effectively.</p>\n<h3>Why Choose Value-Based Bidding?</h3>\n<p>Here are several compelling reasons to adopt value-based bidding:</p>\n<ul><li><b>Improved ROI:</b> By focusing on high-value conversions, you can significantly increase your return on ad spend.</li><li><b>Enhanced Targeting:</b> Value-based bidding allows you to target specific customer segments based on their potential value.</li><li><b>Increased Efficiency:</b> Automate your bidding process and free up your time to focus on other strategic initiatives.</li><li><b>Better Budget Allocation:</b> Allocate your budget more effectively by prioritizing high-value conversions.</li><li><b>Data-Driven Decisions:</b> Make informed decisions based on real-time data and insights.</li></ul>\n<h3>How Value-Based Bidding Works</h3>\n<p>The process typically involves these steps:</p>\n<ol><li><b>Define Conversion Values:</b> Assign a monetary value to each type of conversion based on its estimated worth to your business. For example, a purchase might be worth $50, while a lead form submission might be worth $10.</li><li><b>Track Conversions Accurately:</b> Implement robust conversion tracking to accurately measure the value of each conversion. This may involve using tracking pixels, Google Analytics, or other analytics tools.</li><li><b>Feed Data to Your Advertising Platform:</b> Integrate your conversion tracking data with your advertising platform (e.g., Google Ads, Facebook Ads).</li><li><b>Set Your Target ROAS:</b> Define your desired return on ad spend. This is the ratio of revenue generated to ad spend.</li><li><b>Let the Algorithm Do Its Work:</b> The advertising platform\'s algorithm will automatically adjust your bids to maximize your ROAS based on the conversion values and target ROAS you have set.</li></ol>\n<h3>Implementing Value-Based Bidding: Best Practices</h3>\n<p>To successfully implement value-based bidding, consider these best practices:</p>\n<ul><li><b>Start with Accurate Data:</b> Garbage in, garbage out. Ensure your conversion tracking is accurate and reliable.</li><li><b>Segment Your Audience:</b> Identify and segment your audience based on their potential value.</li><li><b>Use Customer Lifetime Value (CLTV):</b> Incorporate CLTV into your conversion value calculations for a more accurate assessment of customer worth. According to a study by Bain & Company, increasing customer retention rates by 5% increases profits by 25% to 95%. CLTV helps account for this long-term value.</li><li><b>Monitor Performance Closely:</b> Regularly monitor your campaign performance and make adjustments as needed.</li><li><b>A/B Test Different Strategies:</b> Experiment with different bidding strategies and conversion values to find what works best for your business.</li><li><b>Consider Attribution Modeling:</b> Choose an attribution model that accurately reflects the customer journey and assigns value to each touchpoint.</li></ul>\n<h3>Common Challenges and How to Overcome Them</h3>\n<p>While value-based bidding offers significant advantages, it also presents some challenges:</p>\n<ul><li><b>Data Scarcity:</b> If you don\'t have enough conversion data, the algorithm may struggle to optimize your bids. <i>Solution:</i> Start with broad targeting and gradually refine your audience as you collect more data. Consider using value rules to adjust values based on location, device, or audience.</li><li><b>Inaccurate Conversion Values:</b> If your conversion values are inaccurate, the algorithm may make suboptimal bidding decisions. <i>Solution:</i> Regularly review and update your conversion values based on your business performance.</li><li><b>Algorithm Learning Curve:</b> It takes time for the algorithm to learn and optimize your bids. <i>Solution:</i> Be patient and allow the algorithm time to learn. Avoid making frequent changes to your campaigns.</li></ul>\n<h3>The Future of Value-Based Bidding</h3>\n<p>Value-based bidding is poised to become even more sophisticated in the future, with advancements in machine learning and artificial intelligence. Expect to see more personalized bidding strategies, improved predictive capabilities, and greater automation. As privacy regulations evolve, first-party data will become even more critical for accurate value assessment and effective bidding.</p>\n<h3>Take Action Today</h3>\n<p>Ready to unlock the power of value-based bidding? Start by defining your conversion values, implementing robust conversion tracking, and integrating your data with your advertising platform. With a data-driven approach and a commitment to continuous improvement, you can significantly improve your ROI and drive sustainable growth for your business.</p>\n<p><b>Want to learn more about optimizing your digital advertising strategy? Contact us today for a free consultation!</b></p>', '{\"time\":1751028904285,\"blocks\":[{\"id\":\"ssm5csu66s\",\"type\":\"paragraph\",\"data\":{\"text\":\"Unlock Growth: Mastering Value-Based Bidding in Digital Advertising\"}},{\"id\":\"evgsh6kyz8\",\"type\":\"header\",\"data\":{\"text\":\"Unlock Growth: Mastering Value-Based Bidding in Digital Advertising\",\"level\":2}},{\"id\":\"3s1xmg0ozn\",\"type\":\"paragraph\",\"data\":{\"text\":\"In the ever-evolving landscape of digital advertising, simply<u> driving traffic</u> to your website isn\'t enough. You need to attract the <i>right</i> traffic – the kind that converts into paying customers and generates significant revenue. <b>That\'s where</b> value-based bidding comes in.<i> This sophisticated strategy </i>allows you to optimize your ad campaigns not just for clicks or<mark class=\\\"cdx-marker\\\"> impressions</mark>, but for the actual value each customer brings to your business.\"}},{\"id\":\"myxa3nt04n\",\"type\":\"header\",\"data\":{\"text\":\"What is Value-Based Bidding?\",\"level\":3}},{\"id\":\"czoflk4evf\",\"type\":\"paragraph\",\"data\":{\"text\":\"Value-based bidding is a smart bidding strategy that focuses on maximizing your return on ad spend (ROAS) by assigning different values to different conversions. Unlike traditional bidding methods that treat all conversions equally, value-based bidding recognizes that some customers are more valuable than others. For example, a customer who purchases a high-margin product is worth more than a customer who only signs up for a free trial.\"}},{\"id\":\"2tt2ecgcfx\",\"type\":\"paragraph\",\"data\":{\"text\":\"hat some customers are more valuable than others. For example, a customer who purchases a high-margin product is worth more than a customer who only signs up for a free trial.\"}},{\"id\":\"jgojcvrbh3\",\"type\":\"paragraph\",\"data\":{\"text\":\"Instead of bidding based on cost-per-click (CPC) or cost-per-acquisition (CPA), you bid based on the predicted value of each potential customer. This allows you to prioritize high-value conversions and allocate your budget more effectively.\"}},{\"id\":\"optai6646t\",\"type\":\"header\",\"data\":{\"text\":\"Why Choose Value-Based Bidding?\",\"level\":3}},{\"id\":\"dur7atwvlq\",\"type\":\"paragraph\",\"data\":{\"text\":\"Here are several compelling reasons to adopt value-based bidding:\"}},{\"id\":\"gk41aqebc6\",\"type\":\"list\",\"data\":{\"style\":\"unordered\",\"items\":[\"<b>Improved ROI:</b> By focusing on high-value conversions, you can significantly increase your return on ad spend.\",\"<b>Enhanced Targeting:</b> Value-based bidding allows you to target specific customer segments based on their potential value.\",\"<b>Increased Efficiency:</b> Automate your bidding process and free up your time to focus on other strategic initiatives.\",\"<b>Better Budget Allocation:</b> Allocate your budget more effectively by prioritizing high-value conversions.\",\"<b>Data-Driven Decisions:</b> Make informed decisions based on real-time data and insights.\"]}},{\"id\":\"b207na5ot2\",\"type\":\"header\",\"data\":{\"text\":\"How Value-Based Bidding Works\",\"level\":3}},{\"id\":\"ipa41hov2k\",\"type\":\"paragraph\",\"data\":{\"text\":\"The process typically involves these steps:\"}},{\"id\":\"l85qxdvr7u\",\"type\":\"list\",\"data\":{\"style\":\"ordered\",\"items\":[\"<b>Define Conversion Values:</b> Assign a monetary value to each type of conversion based on its estimated worth to your business. For example, a purchase might be worth $50, while a lead form submission might be worth $10.\",\"<b>Track Conversions Accurately:</b> Implement robust conversion tracking to accurately measure the value of each conversion. This may involve using tracking pixels, Google Analytics, or other analytics tools.\",\"<b>Feed Data to Your Advertising Platform:</b> Integrate your conversion tracking data with your advertising platform (e.g., Google Ads, Facebook Ads).\",\"<b>Set Your Target ROAS:</b> Define your desired return on ad spend. This is the ratio of revenue generated to ad spend.\",\"<b>Let the Algorithm Do Its Work:</b> The advertising platform\'s algorithm will automatically adjust your bids to maximize your ROAS based on the conversion values and target ROAS you have set.\"]}},{\"id\":\"7r3u08z2k8\",\"type\":\"header\",\"data\":{\"text\":\"Implementing Value-Based Bidding: Best Practices\",\"level\":3}},{\"id\":\"93ifpjyoi9\",\"type\":\"paragraph\",\"data\":{\"text\":\"To successfully implement value-based bidding, consider these best practices:\"}},{\"id\":\"mk8ygeq19f\",\"type\":\"list\",\"data\":{\"style\":\"unordered\",\"items\":[\"<b>Start with Accurate Data:</b> Garbage in, garbage out. Ensure your conversion tracking is accurate and reliable.\",\"<b>Segment Your Audience:</b> Identify and segment your audience based on their potential value.\",\"<b>Use Customer Lifetime Value (CLTV):</b> Incorporate CLTV into your conversion value calculations for a more accurate assessment of customer worth. According to a study by Bain &amp; Company, increasing customer retention rates by 5% increases profits by 25% to 95%. CLTV helps account for this long-term value.\",\"<b>Monitor Performance Closely:</b> Regularly monitor your campaign performance and make adjustments as needed.\",\"<b>A/B Test Different Strategies:</b> Experiment with different bidding strategies and conversion values to find what works best for your business.\",\"<b>Consider Attribution Modeling:</b> Choose an attribution model that accurately reflects the customer journey and assigns value to each touchpoint.\"]}},{\"id\":\"su2wfe7yvp\",\"type\":\"header\",\"data\":{\"text\":\"Common Challenges and How to Overcome Them\",\"level\":3}},{\"id\":\"u96rfxbsj3\",\"type\":\"paragraph\",\"data\":{\"text\":\"While value-based bidding offers significant advantages, it also presents some challenges:\"}},{\"id\":\"wp73by30sf\",\"type\":\"list\",\"data\":{\"style\":\"unordered\",\"items\":[\"<b>Data Scarcity:</b> If you don\'t have enough conversion data, the algorithm may struggle to optimize your bids. <i>Solution:</i> Start with broad targeting and gradually refine your audience as you collect more data. Consider using value rules to adjust values based on location, device, or audience.\",\"<b>Inaccurate Conversion Values:</b> If your conversion values are inaccurate, the algorithm may make suboptimal bidding decisions. <i>Solution:</i> Regularly review and update your conversion values based on your business performance.\",\"<b>Algorithm Learning Curve:</b> It takes time for the algorithm to learn and optimize your bids. <i>Solution:</i> Be patient and allow the algorithm time to learn. Avoid making frequent changes to your campaigns.\"]}},{\"id\":\"t2667g29o7\",\"type\":\"header\",\"data\":{\"text\":\"The Future of Value-Based Bidding\",\"level\":3}},{\"id\":\"dj9vimze87\",\"type\":\"paragraph\",\"data\":{\"text\":\"Value-based bidding is poised to become even more sophisticated in the future, with advancements in machine learning and artificial intelligence. Expect to see more personalized bidding strategies, improved predictive capabilities, and greater automation. As privacy regulations evolve, first-party data will become even more critical for accurate value assessment and effective bidding.\"}},{\"id\":\"k2pufxedrs\",\"type\":\"header\",\"data\":{\"text\":\"Take Action Today\",\"level\":3}},{\"id\":\"abyku43cyi\",\"type\":\"paragraph\",\"data\":{\"text\":\"Ready to unlock the power of value-based bidding? Start by defining your conversion values, implementing robust conversion tracking, and integrating your data with your advertising platform. With a data-driven approach and a commitment to continuous improvement, you can significantly improve your ROI and drive sustainable growth for your business.\"}},{\"id\":\"setywwjicx\",\"type\":\"paragraph\",\"data\":{\"text\":\"<b>Want to learn more about optimizing your digital advertising strategy? Contact us today for a free consultation!</b>\"}}],\"version\":\"2.31.0-rc.7\"}', 'Stop wasting ad spend! Learn value-based bidding strategies to target high-value customers and maximize your ROI. Drive conversions and profits now!', '', 1, 1, 'published', 'public', NULL, '2025-06-27 14:55:04', NULL, 'Value Based Bidding: Unlock Ad Growth Now!', 'Master value-based bidding for higher ROI. Target high-value customers, optimize ad spend, and drive conversions with this smart advertising strategy.', 'value based bidding, ROAS, digital advertising, smart bidding, conversion optimization, online marketing, Google Ads', 'value based bidding', '', 'Master Value-Based Bidding: Supercharge Your Ad Campaigns', 'Learn how value-based bidding can revolutionize your digital advertising, boost ROI, and attract high-value customers. Get the guide now!', '', 'summary_large_image', 0, 0, 1, 0, 64, 0, 0, 5, 835, 0.00, 'professional-enhanced', '2025-06-24 22:29:40', '2025-06-28 11:32:51'),
(54, 'Trading for Beginners: A Comprehensive Guide to Getting Started', 'Trading-for-Beginners', '<h2>Trading for Beginners: A Comprehensive Guide to Getting Started</h2>\n<p>The world of trading can seem daunting,<u> filled with complex charts</u>, jargon, and the potential for both great rewards and significant risks. But don\'t let that scare you! This guide is designed to break down the basics of <mark class=\"cdx-marker\">trading</mark> for beginners, providing a solid foundation for your journey into the financial markets.</p>\n<h3>What is Tradings?</h3>\n<p>At its core, trading involves buying and selling financial instruments with the goal of profiting from price fluctuations. These instruments can include:</p>\n<ul><li>Stocks: Representing ownership in a company.</li><li>Bonds: Debt securities issued by governments or corporations.</li><li>Forex (Foreign Exchange): Trading currencies.</li><li>Commodities: Raw materials like gold, oil, and agricultural products.</li><li>Cryptocurrencies: Digital or virtual currencies using cryptography for security.</li><li>Derivatives: Contracts whose value is derived from an underlying asset (e.g., options, futures).</li></ul>\n<p>Traders analyze market trends, economic data, and company performance to make informed decisions about when to buy and sell. The goal is to buy low and sell high (or sell high and buy low in the case of short selling).</p>\n<h3>Understanding Different Trading Styles</h3>\n<p>There are various trading styles, each with its own time horizon and risk tolerance:</p>\n<ul><li><b>Day Trading:</b> Holding positions for a very short time, often just minutes or hours, and closing them out before the end of the trading day. This requires intense focus and quick decision-making.</li><li><b>Swing Trading:</b> Holding positions for several days or weeks, aiming to profit from short-term price swings.</li><li><b>Position Trading:</b> Holding positions for months or even years, focusing on long-term trends.</li><li><b>Scalping:</b> Making very small profits from tiny price changes, often holding positions for only seconds.</li></ul>\n<h3>Essential Steps to Start Trading</h3>\n<ol><li><b>Educate Yourself:</b> Knowledge is power. Read books, take online courses, and follow reputable financial news sources. Understand the markets you\'re interested in trading.</li><li><b>Choose a Broker:</b> A broker acts as an intermediary between you and the market. Research different brokers and compare their fees, trading platforms, and available instruments. Look for a regulated broker to ensure the safety of your funds. Examples include Fidelity, Charles Schwab, and Interactive Brokers.</li><li><b>Open an Account:</b> Once you\'ve chosen a broker, open a trading account. You\'ll need to provide personal information and potentially undergo identity verification.</li><li><b>Fund Your Account:</b> Deposit funds into your trading account. Start with an amount you\'re comfortable losing, as trading involves risk.</li><li><b>Develop a Trading Strategy:</b> A well-defined trading strategy is crucial. It should outline your entry and exit points, risk management rules, and the instruments you\'ll trade. Consider using technical analysis, fundamental analysis, or a combination of both.</li><li><b>Practice with a Demo Account:</b> Most brokers offer demo accounts that allow you to trade with virtual money. This is a great way to practice your strategy and get familiar with the trading platform without risking real capital.</li><li><b>Start Small:</b> When you\'re ready to trade with real money, start with small positions. This will help you manage risk and gain experience.</li><li><b>Manage Your Risk:</b> Implement risk management techniques such as setting stop-loss orders to limit potential losses. Never risk more than you can afford to lose.</li><li><b>Track Your Performance:</b> Keep a record of your trades and analyze your performance. Identify your strengths and weaknesses and adjust your strategy accordingly.</li></ol>\n<h3>Key Trading Concepts</h3>\n<p>Understanding these concepts is crucial for successful trading:</p>\n<ul><li><b>Technical Analysis:</b> Analyzing price charts and using indicators to identify potential trading opportunities.</li><li><b>Fundamental Analysis:</b> Evaluating the underlying value of an asset based on economic data, company financials, and industry trends.</li><li><b>Risk Management:</b> Techniques to limit potential losses, such as setting stop-loss orders and diversifying your portfolio.</li><li><b>Trading Psychology:</b> Understanding and managing your emotions while trading, avoiding impulsive decisions driven by fear or greed.</li></ul>\n<h3>Common Trading Mistakes to Avoid</h3>\n<p>Beginner traders often make these mistakes:</p>\n<ul><li><b>Trading without a plan:</b> Lack of a defined strategy leads to impulsive decisions.</li><li><b>Overtrading:</b> Trading too frequently, resulting in higher transaction costs and increased risk.</li><li><b>Chasing losses:</b> Trying to recoup losses by taking on more risk.</li><li><b>Ignoring risk management:</b> Failing to set stop-loss orders or manage position sizes.</li><li><b>Letting emotions dictate decisions:</b> Allowing fear or greed to influence trading choices.</li></ul>\n<h3>Resources for Learning More</h3>\n<p>There are countless resources available to help you learn more about trading:</p>\n<ul><li><b>Online Courses:</b> Platforms like Coursera, Udemy, and Skillshare offer courses on trading and investing.</li><li><b>Books:</b> Read classic trading books like \"Trading in the Zone\" by Mark Douglas and \"Technical Analysis of the Financial Markets\" by John Murphy.</li><li><b>Financial News Websites:</b> Stay informed about market trends and economic news through reputable sources like Bloomberg, Reuters, and the Wall Street Journal.</li><li><b>Trading Communities:</b> Join online forums and communities to connect with other traders and learn from their experiences.</li></ul>\n<h3>Conclusion</h3>\n<blockquote><p>Trading can be a rewarding but challenging endeavor. By educating yourself, developing a solid trading strategy, and managing your risk effectively, you can increase your chances of success. Remember to start small, practice with a demo account, and continuously learn and adapt to the ever-changing market conditions.</p><cite></cite></blockquote>\n<p><b>Ready to take the next step?</b> Research reputable brokers and open a demo account to start practicing your trading skills today!</p>\n\n<p></p>', '{\"time\":*************,\"blocks\":[{\"id\":\"kc1m1dkp0y\",\"type\":\"header\",\"data\":{\"text\":\"Trading for Beginners: A Comprehensive Guide to Getting Started\",\"level\":2}},{\"id\":\"l554yt7ivi\",\"type\":\"paragraph\",\"data\":{\"text\":\"The world of trading can seem daunting,<u> filled with complex charts</u>, jargon, and the potential for both great rewards and significant risks. But don\'t let that scare you! This guide is designed to break down the basics of <mark class=\\\"cdx-marker\\\">trading</mark> for beginners, providing a solid foundation for your journey into the financial markets.\"}},{\"id\":\"gjpu9n2dm5\",\"type\":\"header\",\"data\":{\"text\":\"What is Tradings?\",\"level\":3}},{\"id\":\"yk0vfy5aef\",\"type\":\"paragraph\",\"data\":{\"text\":\"At its core, trading involves buying and selling financial instruments with the goal of profiting from price fluctuations. These instruments can include:\"}},{\"id\":\"y46b5c0k3i\",\"type\":\"list\",\"data\":{\"style\":\"unordered\",\"items\":[\"Stocks: Representing ownership in a company.\",\"Bonds: Debt securities issued by governments or corporations.\",\"Forex (Foreign Exchange): Trading currencies.\",\"Commodities: Raw materials like gold, oil, and agricultural products.\",\"Cryptocurrencies: Digital or virtual currencies using cryptography for security.\",\"Derivatives: Contracts whose value is derived from an underlying asset (e.g., options, futures).\"]}},{\"id\":\"l8l2xg3oi0\",\"type\":\"paragraph\",\"data\":{\"text\":\"Traders analyze market trends, economic data, and company performance to make informed decisions about when to buy and sell. The goal is to buy low and sell high (or sell high and buy low in the case of short selling).\"}},{\"id\":\"slgjbr5qng\",\"type\":\"header\",\"data\":{\"text\":\"Understanding Different Trading Styles\",\"level\":3}},{\"id\":\"x8hmvpgjm7\",\"type\":\"paragraph\",\"data\":{\"text\":\"There are various trading styles, each with its own time horizon and risk tolerance:\"}},{\"id\":\"vce5edn3m3\",\"type\":\"list\",\"data\":{\"style\":\"unordered\",\"items\":[\"<b>Day Trading:</b> Holding positions for a very short time, often just minutes or hours, and closing them out before the end of the trading day. This requires intense focus and quick decision-making.\",\"<b>Swing Trading:</b> Holding positions for several days or weeks, aiming to profit from short-term price swings.\",\"<b>Position Trading:</b> Holding positions for months or even years, focusing on long-term trends.\",\"<b>Scalping:</b> Making very small profits from tiny price changes, often holding positions for only seconds.\"]}},{\"id\":\"qkayyvunsc\",\"type\":\"header\",\"data\":{\"text\":\"Essential Steps to Start Trading\",\"level\":3}},{\"id\":\"tpqesqx8id\",\"type\":\"list\",\"data\":{\"style\":\"ordered\",\"items\":[\"<b>Educate Yourself:</b> Knowledge is power. Read books, take online courses, and follow reputable financial news sources. Understand the markets you\'re interested in trading.\",\"<b>Choose a Broker:</b> A broker acts as an intermediary between you and the market. Research different brokers and compare their fees, trading platforms, and available instruments. Look for a regulated broker to ensure the safety of your funds. Examples include Fidelity, Charles Schwab, and Interactive Brokers.\",\"<b>Open an Account:</b> Once you\'ve chosen a broker, open a trading account. You\'ll need to provide personal information and potentially undergo identity verification.\",\"<b>Fund Your Account:</b> Deposit funds into your trading account. Start with an amount you\'re comfortable losing, as trading involves risk.\",\"<b>Develop a Trading Strategy:</b> A well-defined trading strategy is crucial. It should outline your entry and exit points, risk management rules, and the instruments you\'ll trade. Consider using technical analysis, fundamental analysis, or a combination of both.\",\"<b>Practice with a Demo Account:</b> Most brokers offer demo accounts that allow you to trade with virtual money. This is a great way to practice your strategy and get familiar with the trading platform without risking real capital.\",\"<b>Start Small:</b> When you\'re ready to trade with real money, start with small positions. This will help you manage risk and gain experience.\",\"<b>Manage Your Risk:</b> Implement risk management techniques such as setting stop-loss orders to limit potential losses. Never risk more than you can afford to lose.\",\"<b>Track Your Performance:</b> Keep a record of your trades and analyze your performance. Identify your strengths and weaknesses and adjust your strategy accordingly.\"]}},{\"id\":\"omx86vii0a\",\"type\":\"header\",\"data\":{\"text\":\"Key Trading Concepts\",\"level\":3}},{\"id\":\"xwj36znzy7\",\"type\":\"paragraph\",\"data\":{\"text\":\"Understanding these concepts is crucial for successful trading:\"}},{\"id\":\"m5hvh52ceg\",\"type\":\"list\",\"data\":{\"style\":\"unordered\",\"items\":[\"<b>Technical Analysis:</b> Analyzing price charts and using indicators to identify potential trading opportunities.\",\"<b>Fundamental Analysis:</b> Evaluating the underlying value of an asset based on economic data, company financials, and industry trends.\",\"<b>Risk Management:</b> Techniques to limit potential losses, such as setting stop-loss orders and diversifying your portfolio.\",\"<b>Trading Psychology:</b> Understanding and managing your emotions while trading, avoiding impulsive decisions driven by fear or greed.\"]}},{\"id\":\"8q3fl4adoe\",\"type\":\"header\",\"data\":{\"text\":\"Common Trading Mistakes to Avoid\",\"level\":3}},{\"id\":\"uyuhwiyssv\",\"type\":\"paragraph\",\"data\":{\"text\":\"Beginner traders often make these mistakes:\"}},{\"id\":\"qt1c3ehylp\",\"type\":\"list\",\"data\":{\"style\":\"unordered\",\"items\":[\"<b>Trading without a plan:</b> Lack of a defined strategy leads to impulsive decisions.\",\"<b>Overtrading:</b> Trading too frequently, resulting in higher transaction costs and increased risk.\",\"<b>Chasing losses:</b> Trying to recoup losses by taking on more risk.\",\"<b>Ignoring risk management:</b> Failing to set stop-loss orders or manage position sizes.\",\"<b>Letting emotions dictate decisions:</b> Allowing fear or greed to influence trading choices.\"]}},{\"id\":\"r2w7k37sja\",\"type\":\"header\",\"data\":{\"text\":\"Resources for Learning More\",\"level\":3}},{\"id\":\"omo3mstfzv\",\"type\":\"paragraph\",\"data\":{\"text\":\"There are countless resources available to help you learn more about trading:\"}},{\"id\":\"m96v41e8ro\",\"type\":\"list\",\"data\":{\"style\":\"unordered\",\"items\":[\"<b>Online Courses:</b> Platforms like Coursera, Udemy, and Skillshare offer courses on trading and investing.\",\"<b>Books:</b> Read classic trading books like \\\"Trading in the Zone\\\" by Mark Douglas and \\\"Technical Analysis of the Financial Markets\\\" by John Murphy.\",\"<b>Financial News Websites:</b> Stay informed about market trends and economic news through reputable sources like Bloomberg, Reuters, and the Wall Street Journal.\",\"<b>Trading Communities:</b> Join online forums and communities to connect with other traders and learn from their experiences.\"]}},{\"id\":\"c9ggkjdudx\",\"type\":\"header\",\"data\":{\"text\":\"Conclusion\",\"level\":3}},{\"id\":\"iUvKEiDUvD\",\"type\":\"quote\",\"data\":{\"text\":\"Trading can be a rewarding but challenging endeavor. By educating yourself, developing a solid trading strategy, and managing your risk effectively, you can increase your chances of success. Remember to start small, practice with a demo account, and continuously learn and adapt to the ever-changing market conditions.\",\"caption\":\"\",\"alignment\":\"left\"}},{\"id\":\"khnb89vl7k\",\"type\":\"paragraph\",\"data\":{\"text\":\"<b>Ready to take the next step?</b> Research reputable brokers and open a demo account to start practicing your trading skills today!\"}},{\"id\":\"s8JiMENvD_\",\"type\":\"ctaTool\",\"data\":{\"title\":\"Ready to Get Started?\",\"description\":\"Join thousands of businesses optimizing their performance with AdZeta.\",\"buttonText\":\"Start Free Trial\",\"buttonUrl\":\"#\",\"style\":\"gradient\",\"alignment\":\"left\"}},{\"id\":\"HzzHiJzlB7\",\"type\":\"paragraph\",\"data\":{\"text\":\"\"}}],\"version\":\"2.31.0-rc.7\"}', 'New to trading? This comprehensive guide breaks down the basics, covering everything from understanding markets to developing a trading strategy. Start your journey now!', '', 1, 1, 'draft', 'public', NULL, '2025-06-28 08:35:31', NULL, 'Trading for Beginners: Your Complete Guide to the Markets', 'Learn the basics of trading, from understanding markets to developing a winning strategy. This comprehensive guide is perfect for beginners. Start trading today!', 'trading, investing, stock market, forex, day trading, swing trading, financial markets', 'trading', '', 'Trading for Beginners: A Comprehensive Guide to Getting Started', 'New to trading? This comprehensive guide breaks down the basics, covering everything from understanding markets to developing a trading strategy. Start your journey now!', '', 'summary_large_image', 0, 0, 1, 0, 5, 0, 0, 5, 841, 0.00, 'professional-enhanced', '2025-06-27 17:11:12', '2025-06-28 07:43:11');

-- --------------------------------------------------------

--
-- Table structure for table `blog_tags`
--

CREATE TABLE `blog_tags` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `color` varchar(7) DEFAULT '#007bff',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `blog_tags`
--

INSERT INTO `blog_tags` (`id`, `name`, `slug`, `description`, `color`, `created_at`, `updated_at`) VALUES
(1, 'JavaScript', 'javascript', 'JavaScript programming language', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(2, 'PHP', 'php', 'PHP server-side scripting', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(3, 'React', 'react', 'React JavaScript library', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(4, 'Vue.js', 'vuejs', 'Vue.js progressive framework', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(5, 'SEO', 'seo', 'Search Engine Optimization', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(6, 'Content Marketing', 'content-marketing', 'Content marketing strategies', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(7, 'UI Design', 'ui-design', 'User Interface Design', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(8, 'UX Design', 'ux-design', 'User Experience Design', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(9, 'Startup', 'startup', 'Startup and entrepreneurship', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(10, 'AI', 'ai', 'Artificial Intelligence', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(11, 'Machine Learning', 'machine-learning', 'Machine Learning and Data Science', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(12, 'Tutorial', 'tutorial', 'Step-by-step tutorials', '#007bff', '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(13, 'Case Study', 'case-study', 'Case study content tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(14, 'Client Success', 'client-success', 'Client success story tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(15, 'ROI', 'roi', 'Return on investment tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(16, 'Implementation', 'implementation', 'Implementation methodology tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(17, 'Whitepaper', 'whitepaper', 'Whitepaper content tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(18, 'Research', 'research', 'Research and analysis tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(19, 'Market Analysis', 'market-analysis-tag', 'Market analysis tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(20, 'Industry Report', 'industry-report', 'Industry report tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(21, 'Best Practices', 'best-practices-tag', 'Best practices tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(22, 'Data Analysis', 'data-analysis', 'Data analysis and insights tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(23, 'Competitive Intelligence', 'competitive-intelligence', 'Competitive analysis tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(24, 'Methodology', 'methodology', 'Research methodology tag', '#007bff', '2025-06-17 08:30:46', '2025-06-17 08:30:46'),
(37, 'test-tag', 'test-tag', NULL, '#007bff', '2025-06-20 11:59:37', '2025-06-20 11:59:37'),
(38, 'debugging', 'debugging', NULL, '#007bff', '2025-06-20 11:59:37', '2025-06-20 11:59:37'),
(39, 'post-save', 'post-save', NULL, '#007bff', '2025-06-20 11:59:37', '2025-06-20 11:59:37'),
(40, 'yellow', 'yellow', NULL, '#007bff', '2025-06-20 12:15:47', '2025-06-20 12:15:47'),
(41, 'green', 'green', NULL, '#007bff', '2025-06-20 12:57:49', '2025-06-20 12:57:49'),
(42, 'good', 'good', NULL, '#007bff', '2025-06-20 13:01:41', '2025-06-20 13:01:41'),
(43, 'test-tag-1750424782', 'test-tag-1750424782', NULL, '#007bff', '2025-06-20 13:06:22', '2025-06-20 13:06:22'),
(44, 'test-tag-1750424861', 'test-tag-1750424861', NULL, '#007bff', '2025-06-20 13:07:41', '2025-06-20 13:07:41'),
(45, 'red', 'red', NULL, '#007bff', '2025-06-20 13:10:16', '2025-06-20 13:10:16'),
(46, 'gree', 'gree', NULL, '#007bff', '2025-06-20 13:10:16', '2025-06-20 13:10:16'),
(47, 'yellow, red, green', 'yellow-red-green', NULL, '#007bff', '2025-06-20 13:10:28', '2025-06-20 13:10:28'),
(48, 'simple', 'simple', NULL, '#007bff', '2025-06-20 13:17:34', '2025-06-20 13:17:34'),
(49, 'test', 'test', NULL, '#007bff', '2025-06-20 13:17:34', '2025-06-20 13:17:34'),
(50, 'green, red, yellow', 'green-red-yellow', NULL, '#007bff', '2025-06-20 13:28:11', '2025-06-20 13:28:11'),
(51, 'fudu', 'fudu', NULL, '#007bff', '2025-06-20 13:30:39', '2025-06-20 13:30:39'),
(52, 'post18', 'post18', NULL, '#007bff', '2025-06-20 13:31:47', '2025-06-20 13:31:47'),
(53, 'post18tag', 'post18tag', NULL, '#007bff', '2025-06-20 13:31:47', '2025-06-20 13:31:47'),
(54, 'bad', 'bad', NULL, '#007bff', '2025-06-20 13:46:10', '2025-06-20 13:46:10'),
(55, 'kadu', 'kadu', NULL, '#007bff', '2025-06-20 13:48:00', '2025-06-20 13:48:00'),
(56, 'ladu', 'ladu', NULL, '#007bff', '2025-06-20 13:49:47', '2025-06-20 13:49:47'),
(57, 'lan', 'lan', NULL, '#007bff', '2025-06-20 13:51:25', '2025-06-20 13:51:25'),
(58, 'lodu', 'lodu', NULL, '#007bff', '2025-06-20 13:51:55', '2025-06-20 13:51:55'),
(59, 'bb', 'bb', NULL, '#007bff', '2025-06-20 13:51:55', '2025-06-20 13:51:55'),
(60, 'cc', 'cc', NULL, '#007bff', '2025-06-20 13:51:55', '2025-06-20 13:51:55'),
(61, 'sad', 'sad', NULL, '#007bff', '2025-06-20 13:52:55', '2025-06-20 13:52:55'),
(62, 'rat', 'rat', NULL, '#007bff', '2025-06-20 13:58:50', '2025-06-20 13:58:50'),
(63, 'cat', 'cat', NULL, '#007bff', '2025-06-20 13:58:50', '2025-06-20 13:58:50'),
(64, 'VBB', 'vbb', NULL, '#007bff', '2025-06-21 13:30:36', '2025-06-21 13:30:36'),
(65, 'ROI optimization', 'roi-optimization', NULL, '#007bff', '2025-06-21 13:30:36', '2025-06-21 13:30:36'),
(66, 'AI Marketing', 'ai-marketing', NULL, '#007bff', '2025-06-21 17:00:09', '2025-06-21 17:00:09'),
(67, 'Digital Marketing', 'digital-marketing', NULL, '#007bff', '2025-06-21 17:00:09', '2025-06-21 17:00:09'),
(68, 'Campaign Optimization', 'campaign-optimization', NULL, '#007bff', '2025-06-21 17:00:09', '2025-06-21 17:00:09'),
(69, 'Google Ads', 'google-ads', NULL, '#007bff', '2025-06-21 17:00:09', '2025-06-21 17:00:09'),
(70, 'ROAS', 'roas', NULL, '#007bff', '2025-06-22 13:18:23', '2025-06-22 13:18:23'),
(71, 'marketing strategy', 'marketing-strategy', NULL, '#007bff', '2025-06-22 13:18:23', '2025-06-22 13:18:23'),
(72, 'AdZeta', 'adzeta', NULL, '#007bff', '2025-06-22 13:18:23', '2025-06-22 13:18:23'),
(73, 'growth marketing', 'growth-marketing', NULL, '#007bff', '2025-06-22 13:18:53', '2025-06-22 13:18:53'),
(74, 'marketing technology', 'marketing-technology', NULL, '#007bff', '2025-06-22 13:18:53', '2025-06-22 13:18:53'),
(75, 'Automation', 'automation', NULL, '#007bff', '2025-06-22 19:15:41', '2025-06-22 19:15:41'),
(76, 'Artificial Intelligence', 'artificial-intelligence', NULL, '#007bff', '2025-06-22 19:15:41', '2025-06-22 19:15:41'),
(77, 'Business Automation', 'business-automation', NULL, '#007bff', '2025-06-22 19:15:41', '2025-06-22 19:15:41'),
(78, 'Productivity', 'productivity', NULL, '#007bff', '2025-06-22 19:15:41', '2025-06-22 19:15:41'),
(79, 'Efficiency', 'efficiency', NULL, '#007bff', '2025-06-22 19:15:41', '2025-06-22 19:15:41'),
(80, 'Workflow Automation', 'workflow-automation', NULL, '#007bff', '2025-06-22 19:15:41', '2025-06-22 19:15:41'),
(81, 'Business', 'business', NULL, '#007bff', '2025-06-22 19:23:43', '2025-06-22 19:23:43'),
(82, 'Technology', 'technology', NULL, '#007bff', '2025-06-22 19:23:43', '2025-06-22 19:23:43'),
(83, 'Innovation', 'innovation', NULL, '#007bff', '2025-06-22 19:23:43', '2025-06-22 19:23:43'),
(84, 'Facebook Ads', 'facebook-ads', NULL, '#007bff', '2025-06-22 19:44:52', '2025-06-22 19:44:52'),
(85, 'Advertising', 'advertising', NULL, '#007bff', '2025-06-22 19:44:52', '2025-06-22 19:44:52'),
(86, 'Smart Bidding', 'smart-bidding', NULL, '#007bff', '2025-06-22 19:44:52', '2025-06-22 19:44:52'),
(87, 'Marketing', 'marketing', NULL, '#007bff', '2025-06-22 19:57:57', '2025-06-22 19:57:57'),
(88, 'Bidding', 'bidding', NULL, '#007bff', '2025-06-22 19:57:57', '2025-06-22 19:57:57'),
(89, 'Predictive Analytics', 'predictive-analytics', NULL, '#007bff', '2025-06-22 19:57:57', '2025-06-22 19:57:57'),
(90, 'Value-Based Bidding', 'value-based-bidding', NULL, '#007bff', '2025-06-22 19:57:57', '2025-06-22 19:57:57'),
(91, 'better life', 'better-life', NULL, '#007bff', '2025-06-22 20:11:28', '2025-06-22 20:11:28'),
(92, 'personal development', 'personal-development', NULL, '#007bff', '2025-06-22 20:11:28', '2025-06-22 20:11:28'),
(93, 'self-care', 'self-care', NULL, '#007bff', '2025-06-22 20:11:28', '2025-06-22 20:11:28'),
(94, 'happiness', 'happiness', NULL, '#007bff', '2025-06-22 20:11:28', '2025-06-22 20:11:28'),
(95, 'mental health', 'mental-health', NULL, '#007bff', '2025-06-22 20:11:28', '2025-06-22 20:11:28'),
(96, 'success', 'success', NULL, '#007bff', '2025-06-22 20:11:28', '2025-06-22 20:11:28'),
(97, 'well-being', 'well-being', NULL, '#007bff', '2025-06-22 20:11:28', '2025-06-22 20:11:28'),
(98, 'relationships', 'relationships', NULL, '#007bff', '2025-06-22 20:11:28', '2025-06-22 20:11:28'),
(99, 'Health', 'health', NULL, '#007bff', '2025-06-22 20:25:54', '2025-06-22 20:25:54'),
(100, 'Personal Growth', 'personal-growth', NULL, '#007bff', '2025-06-22 20:25:54', '2025-06-22 20:25:54'),
(101, 'Self-Improvement', 'self-improvement', NULL, '#007bff', '2025-06-22 20:25:54', '2025-06-22 20:25:54'),
(102, 'Mindfulness', 'mindfulness', NULL, '#007bff', '2025-06-22 20:25:54', '2025-06-22 20:25:54'),
(103, 'Habits', 'habits', NULL, '#007bff', '2025-06-22 20:47:32', '2025-06-22 20:47:32'),
(104, 'Routine', 'routine', NULL, '#007bff', '2025-06-22 20:47:32', '2025-06-22 20:47:32'),
(105, 'Motivation', 'motivation', NULL, '#007bff', '2025-06-22 20:47:32', '2025-06-22 20:47:32'),
(106, 'Lifestyle', 'lifestyle', NULL, '#007bff', '2025-06-22 20:47:32', '2025-06-22 20:47:32'),
(107, 'Achieve Goals', 'achieve-goals', NULL, '#007bff', '2025-06-23 20:47:40', '2025-06-23 20:47:40'),
(108, 'Habit Loop', 'habit-loop', NULL, '#007bff', '2025-06-23 20:48:40', '2025-06-23 20:48:40'),
(159, 'Habit Formation', 'habit-formation', NULL, '#007bff', '2025-06-24 10:12:18', '2025-06-24 10:12:18'),
(160, 'Goal Setting', 'goal-setting', NULL, '#007bff', '2025-06-24 10:12:18', '2025-06-24 10:12:18'),
(161, 'Time Management', 'time-management', NULL, '#007bff', '2025-06-24 10:12:18', '2025-06-24 10:12:18'),
(162, 'Value-Based Marketing', 'value-based-marketing', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(163, 'Customer Loyalty', 'customer-loyalty', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(164, 'Brand Values', 'brand-values', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(165, 'Ethical Business', 'ethical-business', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(166, 'Sustainable Business', 'sustainable-business', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(167, 'Social Responsibility', 'social-responsibility', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(168, 'Purpose-Driven Business', 'purpose-driven-business', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(169, 'Business Strategy', 'business-strategy', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(170, 'Consumer Behavior', 'consumer-behavior', NULL, '#007bff', '2025-06-24 10:27:56', '2025-06-24 10:27:56'),
(171, 'Value', 'value', NULL, '#007bff', '2025-06-24 14:07:14', '2025-06-24 14:07:14'),
(172, 'Strategy', 'strategy', NULL, '#007bff', '2025-06-24 14:07:14', '2025-06-24 14:07:14'),
(173, 'Growth', 'growth', NULL, '#007bff', '2025-06-24 14:07:14', '2025-06-24 14:07:14'),
(174, 'Customer Experience', 'customer-experience', NULL, '#007bff', '2025-06-24 14:07:14', '2025-06-24 14:07:14'),
(175, 'Values', 'values', NULL, '#007bff', '2025-06-24 16:30:51', '2025-06-24 16:30:51'),
(176, 'Ethics', 'ethics', NULL, '#007bff', '2025-06-24 16:30:51', '2025-06-24 16:30:51'),
(177, 'Sustainability', 'sustainability', NULL, '#007bff', '2025-06-24 16:30:51', '2025-06-24 16:30:51'),
(178, 'Employee Engagement', 'employee-engagement', NULL, '#007bff', '2025-06-24 16:30:51', '2025-06-24 16:30:51'),
(179, 'Corporate Social Responsibility', 'corporate-social-responsibility', NULL, '#007bff', '2025-06-24 16:30:51', '2025-06-24 16:30:51'),
(180, 'Growth Hacking', 'growth-hacking', NULL, '#007bff', '2025-06-24 18:41:45', '2025-06-24 18:41:45'),
(181, 'Customer Value', 'customer-value', NULL, '#007bff', '2025-06-24 18:41:45', '2025-06-24 18:41:45'),
(182, 'Value-Based Strategy', 'value-based-strategy', NULL, '#007bff', '2025-06-24 21:11:15', '2025-06-24 21:11:15'),
(183, 'Business Growth', 'business-growth', NULL, '#007bff', '2025-06-24 21:11:15', '2025-06-24 21:11:15'),
(184, 'Competitive Advantage', 'competitive-advantage', NULL, '#007bff', '2025-06-24 21:11:15', '2025-06-24 21:11:15'),
(185, 'Value Proposition', 'value-proposition', NULL, '#007bff', '2025-06-24 21:11:15', '2025-06-24 21:11:15'),
(186, 'Profitability', 'profitability', NULL, '#007bff', '2025-06-24 21:11:15', '2025-06-24 21:11:15'),
(187, 'fitness', 'fitness', NULL, '#007bff', '2025-06-24 21:31:58', '2025-06-24 21:31:58'),
(188, 'exercise', 'exercise', NULL, '#007bff', '2025-06-24 21:31:58', '2025-06-24 21:31:58'),
(189, 'workout', 'workout', NULL, '#007bff', '2025-06-24 21:31:58', '2025-06-24 21:31:58'),
(190, 'cardio', 'cardio', NULL, '#007bff', '2025-06-24 21:31:58', '2025-06-24 21:31:58'),
(191, 'strength training', 'strength-training', NULL, '#007bff', '2025-06-24 21:31:58', '2025-06-24 21:31:58'),
(192, 'flexibility', 'flexibility', NULL, '#007bff', '2025-06-24 21:31:58', '2025-06-24 21:31:58'),
(193, 'wellness', 'wellness', NULL, '#007bff', '2025-06-24 21:31:58', '2025-06-24 21:31:58'),
(194, 'guide', 'guide', NULL, '#007bff', '2025-06-24 22:18:07', '2025-06-24 22:18:07'),
(195, 'tips', 'tips', NULL, '#007bff', '2025-06-24 22:18:07', '2025-06-24 22:18:07'),
(196, 'Digital Advertising', 'digital-advertising', NULL, '#007bff', '2025-06-24 22:30:11', '2025-06-24 22:30:11'),
(197, 'Conversion Optimization', 'conversion-optimization', NULL, '#007bff', '2025-06-24 22:30:11', '2025-06-24 22:30:11'),
(198, 'Paid Advertising', 'paid-advertising', NULL, '#007bff', '2025-06-24 22:30:11', '2025-06-24 22:30:11'),
(199, 'Trading', 'trading', NULL, '#007bff', '2025-06-27 17:11:12', '2025-06-27 17:11:12'),
(200, 'Investing', 'investing', NULL, '#007bff', '2025-06-27 17:11:12', '2025-06-27 17:11:12'),
(201, 'Stock Market', 'stock-market', NULL, '#007bff', '2025-06-27 17:11:12', '2025-06-27 17:11:12'),
(202, 'Forex', 'forex', NULL, '#007bff', '2025-06-27 17:11:12', '2025-06-27 17:11:12'),
(203, 'Beginners', 'beginners', NULL, '#007bff', '2025-06-27 17:11:12', '2025-06-27 17:11:12'),
(204, 'Finance', 'finance', NULL, '#007bff', '2025-06-27 17:11:12', '2025-06-27 17:11:12'),
(205, 'Markets', 'markets', NULL, '#007bff', '2025-06-27 17:11:12', '2025-06-27 17:11:12');

-- --------------------------------------------------------

--
-- Table structure for table `media`
--

CREATE TABLE `media` (
  `id` int(11) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_url` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `width` int(11) DEFAULT 0,
  `height` int(11) DEFAULT 0,
  `alt_text` text DEFAULT NULL,
  `caption` text DEFAULT NULL,
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `media`
--

INSERT INTO `media` (`id`, `original_filename`, `filename`, `file_path`, `file_url`, `file_size`, `mime_type`, `width`, `height`, `alt_text`, `caption`, `uploaded_at`) VALUES
(1, 'testx.png', 'testx_1750177900_6851986c47a59.png', '../../uploads/media/testx_1750177900_6851986c47a59.png', '/adzeta-admin/uploads/media/testx_1750177900_6851986c47a59.png', 2610829, 'image/png', 4944, 3376, NULL, NULL, '2025-06-17 16:31:40'),
(2, 'testx.png', 'testx_1750177911_6851987718d1e.png', '../../uploads/media/testx_1750177911_6851987718d1e.png', '/adzeta-admin/uploads/media/testx_1750177911_6851987718d1e.png', 2610829, 'image/png', 4944, 3376, NULL, NULL, '2025-06-17 16:31:51'),
(3, 'testx.png', 'testx_1750178306_68519a0245cda.png', '../../uploads/media/testx_1750178306_68519a0245cda.png', '/adzeta-admin/uploads/media/testx_1750178306_68519a0245cda.png', 2610829, 'image/png', 4944, 3376, NULL, NULL, '2025-06-17 16:38:26'),
(4, 'testx.png', 'testx_1750178338_68519a22e9efa.png', '../../uploads/media/testx_1750178338_68519a22e9efa.png', '/adzeta-admin/uploads/media/testx_1750178338_68519a22e9efa.png', 2610829, 'image/png', 4944, 3376, NULL, NULL, '2025-06-17 16:38:58'),
(5, 'Untitled-4.png', 'Untitled-4_1750273815_68530f17735ca.png', '../../uploads/media/Untitled-4_1750273815_68530f17735ca.png', '/adzeta-admin/uploads/media/Untitled-4_1750273815_68530f17735ca.png', 13605, 'image/png', 387, 387, NULL, NULL, '2025-06-18 19:10:15'),
(6, 'AdZeta-Logo.png', 'AdZeta-Logo_1750273841_68530f31c5178.png', '../../uploads/media/AdZeta-Logo_1750273841_68530f31c5178.png', '/adzeta-admin/uploads/media/AdZeta-Logo_1750273841_68530f31c5178.png', 88531, 'image/png', 869, 869, NULL, NULL, '2025-06-18 19:10:41'),
(7, 'image (38)-topaz-low resolution v2-2x.png', 'image (38)-topaz-low resolution v2-2x_1750305805_68538c0d87754.png', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/image (38)-topaz-low resolution v2-2x_1750305805_68538c0d87754.png', '/adzeta-admin/uploads/media/image (38)-topaz-low resolution v2-2x_1750305805_68538c0d87754.png', 21709867, 'image/png', 4944, 3376, NULL, NULL, '2025-06-19 04:03:25'),
(8, 'favicon.png', 'favicon_1750305991_68538cc7d94c0.png', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/favicon_1750305991_68538cc7d94c0.png', '/adzeta-admin/uploads/media/favicon_1750305991_68538cc7d94c0.png', 2241, 'image/png', 32, 32, NULL, NULL, '2025-06-19 04:06:31'),
(9, 'AdZeta-Logo.png', 'AdZeta-Logo_1750306694_68538f86b3782.png', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/AdZeta-Logo_1750306694_68538f86b3782.png', '/adzeta-admin/uploads/media/AdZeta-Logo_1750306694_68538f86b3782.png', 88531, 'image/png', 869, 869, NULL, NULL, '2025-06-19 04:18:14'),
(10, 'testx.png', 'testx_1750306710_68538f96670c2.png', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/testx_1750306710_68538f96670c2.png', '/adzeta-admin/uploads/media/testx_1750306710_68538f96670c2.png', 2610829, 'image/png', 4944, 3376, NULL, NULL, '2025-06-19 04:18:30'),
(11, 'testx.png', 'testx_1750306739_68538fb30ca7c.png', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/testx_1750306739_68538fb30ca7c.png', '/adzeta-admin/uploads/media/testx_1750306739_68538fb30ca7c.png', 2610829, 'image/png', 4944, 3376, NULL, NULL, '2025-06-19 04:18:59'),
(12, '_DSC0881.jpg', '_DSC0881_1750306772_68538fd4ba805.jpg', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/_DSC0881_1750306772_68538fd4ba805.jpg', '/adzeta-admin/uploads/media/_DSC0881_1750306772_68538fd4ba805.jpg', 8368489, 'image/jpeg', 4000, 6000, NULL, NULL, '2025-06-19 04:19:32'),
(13, '_RAZ6537.jpg', '_RAZ6537_1750306987_685390abcb120.jpg', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/_RAZ6537_1750306987_685390abcb120.jpg', '/adzeta-admin/uploads/media/_RAZ6537_1750306987_685390abcb120.jpg', 8757160, 'image/jpeg', 6000, 4000, NULL, NULL, '2025-06-19 04:23:07'),
(14, 'GUND0289.JPG', 'GUND0289_1750307078_6853910642a42.JPG', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/GUND0289_1750307078_6853910642a42.JPG', '/adzeta-admin/uploads/media/GUND0289_1750307078_6853910642a42.JPG', 8038839, 'image/jpeg', 6720, 4480, NULL, NULL, '2025-06-19 04:24:38'),
(15, '_RAZ6536.jpg', '_RAZ6536_1750331039_6853ee9f8b166.jpg', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/_RAZ6536_1750331039_6853ee9f8b166.jpg', '/adzeta-admin/uploads/media/_RAZ6536_1750331039_6853ee9f8b166.jpg', 9352981, 'image/jpeg', 6000, 4000, NULL, NULL, '2025-06-19 11:03:59'),
(16, 'ai-predicts-ltv-visualization.jpg', 'ai-predicts-ltv-visualization_1750620256_68585860f31b6.jpg', 'C:\\xampp\\htdocs\\adzeta-admin\\src\\API/../../uploads/media/ai-predicts-ltv-visualization_1750620256_68585860f31b6.jpg', '/adzeta-admin/uploads/media/ai-predicts-ltv-visualization_1750620256_68585860f31b6.jpg', 141657, 'image/jpeg', 1280, 800, NULL, NULL, '2025-06-22 19:24:17');

-- --------------------------------------------------------

--
-- Table structure for table `page_seo`
--

CREATE TABLE `page_seo` (
  `id` int(11) NOT NULL,
  `page_url` varchar(255) NOT NULL,
  `page_title` varchar(255) NOT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `og_title` varchar(255) DEFAULT NULL,
  `og_description` text DEFAULT NULL,
  `og_image` varchar(500) DEFAULT NULL,
  `og_type` varchar(50) DEFAULT 'website',
  `twitter_card` varchar(50) DEFAULT 'summary_large_image',
  `twitter_title` varchar(255) DEFAULT NULL,
  `twitter_description` text DEFAULT NULL,
  `twitter_image` varchar(500) DEFAULT NULL,
  `canonical_url` varchar(500) DEFAULT NULL,
  `robots` varchar(100) DEFAULT 'index,follow',
  `schema_markup` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`schema_markup`)),
  `custom_head_tags` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `page_seo`
--

INSERT INTO `page_seo` (`id`, `page_url`, `page_title`, `meta_description`, `meta_keywords`, `og_title`, `og_description`, `og_image`, `og_type`, `twitter_card`, `twitter_title`, `twitter_description`, `twitter_image`, `canonical_url`, `robots`, `schema_markup`, `custom_head_tags`, `is_active`, `created_at`, `updated_at`) VALUES
(1, '/platform.php', 'AdZeta Platform - AI-Powered Performance Marketing Technology', 'Discover AdZeta\'s cutting-edge AI platform for performance marketing. Advanced algorithms, real-time optimization, and data-driven insights for maximum ROI.', 'AI marketing platform, performance marketing technology, AdZeta platform, marketing automation', 'AdZeta Platform - AI-Powered Marketing Technology', 'Advanced AI platform for performance marketing with real-time optimization and data-driven insights.', NULL, 'website', 'summary_large_image', NULL, NULL, NULL, NULL, 'index,follow', NULL, NULL, 1, '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(2, '/integrations.php', 'Third-Party Integrations - AdZeta Marketing Platform', 'Seamlessly integrate AdZeta with your existing marketing stack. Connect with Google Ads, Facebook, Shopify, and 100+ marketing tools and platforms.', 'marketing integrations, API connections, Google Ads integration, Facebook integration, Shopify', 'AdZeta Integrations - Connect Your Marketing Stack', 'Seamlessly integrate with Google Ads, Facebook, Shopify, and 100+ marketing tools and platforms.', NULL, 'website', 'summary_large_image', NULL, NULL, NULL, NULL, 'index,follow', NULL, NULL, 1, '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(3, '/data-trust.php', 'fudu Data Security & Trust - AdZeta Privacy & Compliance', 'fudu Learn about AdZeta\'s commitment to data security, privacy compliance, and trust. GDPR compliant, SOC 2 certified, and enterprise-grade security.', 'fudu,data security, privacy compliance, GDPR, SOC 2, marketing data protection', 'fuduj,Data Security & Trust - AdZeta Privacy Compliance', 'fudu,Enterprise-grade security, GDPR compliance, and SOC 2 certification for your marketing data.', '', 'website', 'summary_large_image', '', '', '', '', 'index,follow', NULL, '', 1, '2025-06-19 11:45:29', '2025-06-19 15:16:51'),
(4, '/blog-list-dynamic.php', 'Performance Marketing Blog - AdZeta Insights & Strategies', 'Stay ahead with the latest performance marketing insights, strategies, and industry trends. Expert tips for e-commerce growth and marketing optimization.', 'performance marketing blog, e-commerce marketing, digital marketing insights, marketing strategies', 'Performance Marketing Blog - Expert Insights & Strategies', 'Latest performance marketing insights, strategies, and industry trends for e-commerce growth.', NULL, 'website', 'summary_large_image', NULL, NULL, NULL, NULL, 'index,follow', NULL, NULL, 1, '2025-06-19 11:45:29', '2025-06-19 12:01:28'),
(5, '/', 'AdZeta - AI-Powered Performance Marketing for E-commerce', 'Boost your e-commerce performance with AdZeta\'s AI-powered marketing platform. Advanced algorithms, real-time optimization, and measurable results.', 'AI marketing, performance marketing, e-commerce advertising, marketing automation, AdZeta', 'AdZeta - AI-Powered Performance Marketing Platform', 'Boost e-commerce performance with AI-powered marketing, advanced algorithms, and real-time optimization.', '', 'website', 'summary_large_image', '', '', '', '', 'index,follow', NULL, '', 1, '2025-06-19 11:45:29', '2025-06-19 15:12:20');

-- --------------------------------------------------------

--
-- Table structure for table `posts`
--

CREATE TABLE `posts` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text DEFAULT NULL,
  `author_id` int(11) DEFAULT NULL,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `post_tags`
--

CREATE TABLE `post_tags` (
  `post_id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `post_tags`
--

INSERT INTO `post_tags` (`post_id`, `tag_id`, `created_at`) VALUES
(52, 5, '2025-06-24 22:28:17'),
(52, 6, '2025-06-24 22:28:17'),
(52, 67, '2025-06-24 22:28:17'),
(52, 81, '2025-06-24 22:28:17'),
(52, 87, '2025-06-24 22:28:17'),
(52, 172, '2025-06-24 22:28:17'),
(52, 194, '2025-06-24 22:28:17'),
(52, 195, '2025-06-24 22:28:17'),
(53, 69, '2025-06-27 12:55:04'),
(53, 70, '2025-06-27 12:55:04'),
(53, 71, '2025-06-27 12:55:04'),
(53, 86, '2025-06-27 12:55:04'),
(53, 90, '2025-06-27 12:55:04'),
(53, 196, '2025-06-27 12:55:04'),
(53, 197, '2025-06-27 12:55:04'),
(53, 198, '2025-06-27 12:55:04'),
(54, 172, '2025-06-28 07:43:11'),
(54, 201, '2025-06-28 07:43:11'),
(54, 205, '2025-06-28 07:43:11');

-- --------------------------------------------------------

--
-- Table structure for table `seo_settings`
--

CREATE TABLE `seo_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `seo_settings`
--

INSERT INTO `seo_settings` (`id`, `setting_key`, `setting_value`, `setting_description`, `created_at`, `updated_at`) VALUES
(1, 'default_title_suffix', ' - AdZeta', 'Default suffix added to page titles', '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(2, 'default_meta_description', 'AdZeta provides AI-powered performance marketing solutions for e-commerce businesses. Boost ROI with advanced algorithms and real-time optimization.', 'Default meta description when none is specified', '2025-06-19 11:45:29', '2025-06-19 12:01:28'),
(3, 'default_og_image', '/images/adzeta-og-gunu.jpg', 'Default Open Graph image', '2025-06-19 11:45:29', '2025-06-19 15:20:35'),
(4, 'default_twitter_handle', '@AdZetaAI', 'Default Twitter handle for Twitter cards', '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(5, 'google_analytics_id', 'GTM-KSC58XGP', 'Google Analytics/Tag Manager ID', '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(6, 'google_site_verification', '', 'Google Search Console verification code', '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(7, 'facebook_app_id', '', 'Facebook App ID for Open Graph', '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(8, 'default_robots', 'index,follow', 'Default robots meta tag', '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(9, 'site_name', 'AdZeta', 'Site name for Open Graph', '2025-06-19 11:45:29', '2025-06-19 11:45:29'),
(10, 'default_locale', 'en_US', 'Default locale for Open Graph', '2025-06-19 11:45:29', '2025-06-19 11:45:29');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` longtext DEFAULT NULL,
  `setting_type` enum('string','number','boolean','json','text') NOT NULL DEFAULT 'string',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'AdZeta Blog', 'string', 'general', 'Website name', 0, '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(2, 'site_description', 'Professional Blog Management System', 'string', 'general', 'Website description', 0, '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(3, 'site_url', 'http://localhost/adzeta-admin', 'string', 'general', 'Website URL', 0, '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(4, 'posts_per_page', '10', 'number', 'general', 'Number of posts per page', 0, '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(5, 'allow_comments', 'true', 'boolean', 'general', 'Allow comments on posts', 0, '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(6, 'timezone', 'UTC', 'string', 'general', 'Website timezone', 0, '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(7, 'date_format', 'Y-m-d', 'string', 'general', 'Date format', 0, '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(8, 'time_format', 'H:i:s', 'string', 'general', 'Time format', 0, '2025-06-17 08:09:47', '2025-06-17 08:09:47'),
(9, 'gemini_api_keys', '[{\"id\":\"test_key_1\",\"name\":\"Working API Key\",\"api_key\":\"AIzaSyB0Te6GCLgqME_ogMD3flo7VIZZAptRLnY\",\"is_active\":true}]', 'json', 'general', 'Google Gemini API keys configuration with automatic failover', 0, '2025-06-20 07:24:31', '2025-06-20 11:06:45'),
(10, 'ai_default_temperature', '0.7', 'number', 'general', 'Default temperature for AI content generation (0-1, higher = more creative)', 0, '2025-06-20 07:24:31', '2025-06-20 07:44:05'),
(11, 'ai_max_output_tokens', '2048', 'number', 'general', 'Maximum number of tokens for AI responses', 0, '2025-06-20 07:24:31', '2025-06-20 07:44:06'),
(12, 'ai_auto_suggestions_enabled', '0', 'boolean', 'general', 'Enable automatic AI suggestions while typing', 0, '2025-06-20 07:24:31', '2025-06-20 07:53:40'),
(13, 'ai_seo_analysis_enabled', '0', 'boolean', 'general', 'Enable automatic SEO analysis and recommendations', 0, '2025-06-20 07:24:31', '2025-06-20 07:53:40'),
(14, 'ai_content_assistance_enabled', '1', 'boolean', 'general', 'Enable AI content writing assistance', 0, '2025-06-20 07:24:31', '2025-06-20 07:24:31'),
(15, 'ai_title_generation_enabled', '1', 'boolean', 'general', 'Enable AI title generation suggestions', 0, '2025-06-20 07:24:31', '2025-06-20 07:24:31'),
(16, 'ai_meta_generation_enabled', '1', 'boolean', 'general', 'Enable AI meta description generation', 0, '2025-06-20 07:24:31', '2025-06-20 07:24:31'),
(17, 'ai_tag_generation_enabled', '1', 'boolean', 'general', 'Enable AI tag suggestions', 0, '2025-06-20 07:24:31', '2025-06-20 07:24:31'),
(18, 'ai_gemini_model', 'gemini-2.0-flash', 'string', 'general', 'Selected Gemini model for AI content generation', 0, '2025-06-21 15:25:04', '2025-06-21 15:43:49');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `role` enum('admin','editor','author') NOT NULL DEFAULT 'author',
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `name` varchar(255) NOT NULL DEFAULT '',
  `password` varchar(255) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `password_hash`, `role`, `first_name`, `last_name`, `avatar`, `bio`, `status`, `last_login`, `created_at`, `updated_at`, `name`, `password`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$JFi5lyYsdK1uLGbbFzcveuL8IsNZUyzYICRauk2AomhGNT2BBIYli', 'admin', 'Admin', 'User', '/adzeta-admin/uploads/media/testx_1750177911_6851987718d1e.png', 'This will be displayed on author pages and postsThis will be displayed on author pages and postsThis will be displayed on author pages and posts', 'active', '2025-06-28 19:08:11', '2025-06-17 08:09:47', '2025-06-28 13:38:11', 'talwinder', '');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `ai_content_cache`
--
ALTER TABLE `ai_content_cache`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cache_key` (`cache_key`),
  ADD KEY `idx_cache_key` (`cache_key`),
  ADD KEY `idx_prompt_hash` (`prompt_hash`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `ai_usage_logs`
--
ALTER TABLE `ai_usage_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action_type` (`action_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `api_error_logs`
--
ALTER TABLE `api_error_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_request_id` (`request_id`),
  ADD KEY `idx_timestamp` (`timestamp`),
  ADD KEY `idx_endpoint` (`endpoint`),
  ADD KEY `idx_status_code` (`status_code`),
  ADD KEY `idx_severity` (`severity`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_is_resolved` (`is_resolved`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_error_type` (`error_type`),
  ADD KEY `idx_error_logs_composite` (`timestamp`,`severity`,`is_resolved`),
  ADD KEY `idx_error_logs_endpoint_status` (`endpoint`,`status_code`),
  ADD KEY `idx_error_logs_user_session` (`user_id`,`session_id`);

--
-- Indexes for table `blog_categories`
--
ALTER TABLE `blog_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD UNIQUE KEY `unique_slug` (`slug`),
  ADD KEY `idx_parent` (`parent_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `blog_posts`
--
ALTER TABLE `blog_posts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD UNIQUE KEY `unique_post_slug` (`slug`),
  ADD KEY `idx_author` (`author_id`),
  ADD KEY `idx_category` (`category_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_published` (`published_at`),
  ADD KEY `idx_seo_score` (`seo_score`);

--
-- Indexes for table `blog_tags`
--
ALTER TABLE `blog_tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD UNIQUE KEY `unique_tag_slug` (`slug`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `media`
--
ALTER TABLE `media`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_filename` (`filename`),
  ADD KEY `idx_uploaded_at` (`uploaded_at`);

--
-- Indexes for table `page_seo`
--
ALTER TABLE `page_seo`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `page_url` (`page_url`),
  ADD KEY `idx_page_url` (`page_url`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `posts`
--
ALTER TABLE `posts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_author` (`author_id`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `post_tags`
--
ALTER TABLE `post_tags`
  ADD PRIMARY KEY (`post_id`,`tag_id`),
  ADD KEY `idx_post` (`post_id`),
  ADD KEY `idx_tag` (`tag_id`);

--
-- Indexes for table `seo_settings`
--
ALTER TABLE `seo_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD UNIQUE KEY `unique_setting_key` (`setting_key`),
  ADD KEY `idx_category` (`category`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `ai_content_cache`
--
ALTER TABLE `ai_content_cache`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ai_usage_logs`
--
ALTER TABLE `ai_usage_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `api_error_logs`
--
ALTER TABLE `api_error_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `blog_categories`
--
ALTER TABLE `blog_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `blog_posts`
--
ALTER TABLE `blog_posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55;

--
-- AUTO_INCREMENT for table `blog_tags`
--
ALTER TABLE `blog_tags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=206;

--
-- AUTO_INCREMENT for table `media`
--
ALTER TABLE `media`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `page_seo`
--
ALTER TABLE `page_seo`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `posts`
--
ALTER TABLE `posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `seo_settings`
--
ALTER TABLE `seo_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `posts`
--
ALTER TABLE `posts`
  ADD CONSTRAINT `posts_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
