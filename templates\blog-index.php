<?php
/**
 * Blog Index Template
 * Displays list of blog posts
 */

// Include header
include __DIR__ . '/../header.php';
?>

<main class="main-content">
    <!-- Page Title Section -->
    <section class="page-title-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title">Blog</h1>
                    <p class="page-subtitle">Latest insights on AI-powered advertising and e-commerce growth</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Posts Section -->
    <section class="blog-posts-section py-5">
        <div class="container">
            <div class="row">
                <?php if (!empty($posts)): ?>
                    <?php foreach ($posts as $post): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <article class="blog-card">
                                <?php if ($post['featured_image']): ?>
                                    <div class="blog-card-image">
                                        <img src="<?php echo htmlspecialchars($post['featured_image']); ?>" 
                                             alt="<?php echo htmlspecialchars($post['title']); ?>" 
                                             class="img-fluid">
                                    </div>
                                <?php endif; ?>
                                
                                <div class="blog-card-content">
                                    <h3 class="blog-card-title">
                                        <a href="/blog/<?php echo htmlspecialchars($post['slug']); ?>">
                                            <?php echo htmlspecialchars($post['title']); ?>
                                        </a>
                                    </h3>
                                    
                                    <?php if ($post['excerpt']): ?>
                                        <p class="blog-card-excerpt">
                                            <?php echo htmlspecialchars($post['excerpt']); ?>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <div class="blog-card-meta">
                                        <span class="blog-date">
                                            <?php echo formatDate($post['published_at']); ?>
                                        </span>
                                        <?php if ($post['reading_time']): ?>
                                            <span class="blog-reading-time">
                                                <?php echo $post['reading_time']; ?> min read
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <a href="/blog/<?php echo htmlspecialchars($post['slug']); ?>" 
                                       class="btn btn-primary btn-sm">
                                        Read More
                                    </a>
                                </div>
                            </article>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12 text-center">
                        <p>No blog posts found.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
</main>

<?php
// Include footer
include __DIR__ . '/../footer.php';
?>
