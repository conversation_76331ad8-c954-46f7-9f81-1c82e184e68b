<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" viewBox="0 0 1200 900" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- Definitions for gradients, filters, and animations -->
    <defs>
        <!-- Apple-inspired color palette -->
        <linearGradient id="clientGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#FF6A8E" />
            <stop offset="100%" stop-color="#FF4775" />
        </linearGradient>

        <linearGradient id="adzetaGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#7D7AFF" />
            <stop offset="100%" stop-color="#5E5CE6" />
        </linearGradient>

        <linearGradient id="googleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#4285F4" />
            <stop offset="100%" stop-color="#3372DB" />
        </linearGradient>

        <linearGradient id="resultsGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#30D158" />
            <stop offset="100%" stop-color="#27AE60" />
        </linearGradient>

        <!-- Glassmorphism gradients -->
        <linearGradient id="glassGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="white" stop-opacity="0.9" />
            <stop offset="100%" stop-color="white" stop-opacity="0.7" />
        </linearGradient>

        <linearGradient id="feedbackGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#FF6A8E" />
            <stop offset="50%" stop-color="#7D7AFF" />
            <stop offset="100%" stop-color="#30D158" />
        </linearGradient>

        <!-- Neumorphism filters -->
        <filter id="neumorphic-shadow" x="-5%" y="-5%" width="110%" height="110%">
            <feDropShadow dx="-2" dy="-2" stdDeviation="3" flood-color="white" flood-opacity="0.5" />
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#0000001A" flood-opacity="0.5" />
        </filter>

        <filter id="glass-blur" x="-10%" y="-10%" width="120%" height="120%">
            <feGaussianBlur stdDeviation="5" result="blur" />
            <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>

        <filter id="subtle-shadow" x="-10%" y="-10%" width="120%" height="120%">
            <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#0000001A" flood-opacity="0.2" />
        </filter>

        <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur stdDeviation="2" result="blur" />
            <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>

        <!-- Connection paths with precise curves -->
        <path id="path-1" d="M340 110 C360 110 380 110 400 110 C420 110 420 130 420 150 C420 170 420 190 440 190 C460 190 480 190 500 190" />
        <path id="path-2" d="M620 270 C620 290 620 310 620 330" />
        <path id="path-3" d="M620 500 C620 520 620 540 620 560" />
        <path id="path-4" d="M740 610 C760 610 780 610 800 610 C820 610 820 590 820 570 C820 550 820 530 820 510 C820 490 840 490 860 490 C880 490 900 490 900 490" />
        <path id="path-5" d="M1020 550 C1020 570 1020 590 1020 610" />
        <path id="path-6" d="M1020 720 C1020 740 1020 760 1020 780" />
        <path id="path-7" d="M900 800 C800 800 700 800 600 800 C500 800 400 800 400 800 C380 800 380 780 380 760 C380 740 380 720 380 700 C380 600 380 500 380 400 C380 300 380 200 380 130 C380 110 400 110 420 110" />

        <!-- Data particle -->
        <circle id="dataParticle" r="2" fill="white" filter="url(#glow)" />
        
        <!-- Checkmark icon -->
        <g id="checkmark">
            <circle r="6" fill-opacity="0.15" />
            <path d="M-3,0 L-1,2 L3,-2" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round" />
        </g>
    </defs>

    <!-- Background with subtle gradient and grid -->
    <rect width="1200" height="900" fill="#F9F9F9" />
    <rect width="1200" height="900" fill="url(#glassGradient)" opacity="0.3" />
    
    <!-- Subtle grid lines -->
    <g opacity="0.03">
        <path d="M0 150 H1200" stroke="#7D7AFF" stroke-width="1" />
        <path d="M0 350 H1200" stroke="#7D7AFF" stroke-width="1" />
        <path d="M0 550 H1200" stroke="#7D7AFF" stroke-width="1" />
        <path d="M0 750 H1200" stroke="#7D7AFF" stroke-width="1" />
        
        <path d="M300 0 V900" stroke="#7D7AFF" stroke-width="1" />
        <path d="M700 0 V900" stroke="#7D7AFF" stroke-width="1" />
        <path d="M1100 0 V900" stroke="#7D7AFF" stroke-width="1" />
    </g>

    <!-- Connection lines with improved styling -->
    <path d="M340 110 C360 110 380 110 400 110 C420 110 420 130 420 150 C420 170 420 190 440 190 C460 190 480 190 500 190" stroke="url(#clientGradient)" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-opacity="0.7" />
    <path d="M620 270 C620 290 620 310 620 330" stroke="url(#adzetaGradient)" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-opacity="0.7" />
    <path d="M620 500 C620 520 620 540 620 560" stroke="url(#adzetaGradient)" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-opacity="0.7" />
    <path d="M740 610 C760 610 780 610 800 610 C820 610 820 590 820 570 C820 550 820 530 820 510 C820 490 840 490 860 490 C880 490 900 490 900 490" stroke="url(#adzetaGradient)" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-opacity="0.7" />
    <path d="M1020 550 C1020 570 1020 590 1020 610" stroke="url(#googleGradient)" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-opacity="0.7" />
    <path d="M1020 720 C1020 740 1020 760 1020 780" stroke="url(#resultsGradient)" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-opacity="0.7" />
    <path d="M900 800 C800 800 700 800 600 800 C500 800 400 800 400 800 C380 800 380 780 380 760 C380 740 380 720 380 700 C380 600 380 500 380 400 C380 300 380 200 380 130 C380 110 400 110 420 110" stroke="url(#feedbackGradient)" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-opacity="0.7" stroke-dasharray="4,4" />

    <!-- Animated data particles -->
    <use href="#dataParticle" fill="#FF6A8E">
        <animateMotion dur="1.2s" repeatCount="indefinite" begin="0s">
            <mpath href="#path-1" />
        </animateMotion>
    </use>
    <use href="#dataParticle" fill="#FF6A8E">
        <animateMotion dur="1.2s" repeatCount="indefinite" begin="0.6s">
            <mpath href="#path-1" />
        </animateMotion>
    </use>
    
    <use href="#dataParticle" fill="#7D7AFF">
        <animateMotion dur="0.8s" repeatCount="indefinite" begin="0.2s">
            <mpath href="#path-2" />
        </animateMotion>
    </use>
    
    <use href="#dataParticle" fill="#7D7AFF">
        <animateMotion dur="0.8s" repeatCount="indefinite" begin="0.4s">
            <mpath href="#path-3" />
        </animateMotion>
    </use>
    
    <use href="#dataParticle" fill="#7D7AFF">
        <animateMotion dur="1.5s" repeatCount="indefinite" begin="0.6s">
            <mpath href="#path-4" />
        </animateMotion>
    </use>
    <use href="#dataParticle" fill="#7D7AFF">
        <animateMotion dur="1.5s" repeatCount="indefinite" begin="1.3s">
            <mpath href="#path-4" />
        </animateMotion>
    </use>
    
    <use href="#dataParticle" fill="#4285F4">
        <animateMotion dur="0.7s" repeatCount="indefinite" begin="0.8s">
            <mpath href="#path-5" />
        </animateMotion>
    </use>
    
    <use href="#dataParticle" fill="#30D158">
        <animateMotion dur="0.7s" repeatCount="indefinite" begin="1s">
            <mpath href="#path-6" />
        </animateMotion>
    </use>
    
    <use href="#dataParticle" fill="#FF6A8E">
        <animateMotion dur="3s" repeatCount="indefinite" begin="1.2s">
            <mpath href="#path-7" />
        </animateMotion>
    </use>
    <use href="#dataParticle" fill="#7D7AFF">
        <animateMotion dur="3s" repeatCount="indefinite" begin="2.2s">
            <mpath href="#path-7" />
        </animateMotion>
    </use>
    <use href="#dataParticle" fill="#30D158">
        <animateMotion dur="3s" repeatCount="indefinite" begin="3.2s">
            <mpath href="#path-7" />
        </animateMotion>
    </use>

    <!-- SECTION 1: Client Data Sources - Glassmorphism Card -->
    <g filter="url(#neumorphic-shadow)">
        <!-- Card with glassmorphism effect -->
        <rect x="100" y="50" width="240" height="120" rx="16" fill="url(#glassGradient)" stroke="#FFFFFF" stroke-width="1" />
        <rect x="100" y="50" width="240" height="6" rx="3" fill="url(#clientGradient)" />
        
        <!-- Icon with gradient background -->
        <circle cx="130" cy="90" r="20" fill="#FF6A8E" fill-opacity="0.1" />
        <g transform="translate(130,90) scale(0.4)">
            <path d="M-15,-10 C-15,-16 15,-16 15,-10 L15,10 C15,16 -15,16 -15,10 Z" stroke="#FF6A8E" stroke-width="2.5" fill="none" />
            <path d="M-15,0 C-15,-6 15,-6 15,0" stroke="#FF6A8E" stroke-width="2.5" fill="none" />
            <ellipse cx="0" cy="-10" rx="15" ry="6" stroke="#FF6A8E" stroke-width="2.5" fill="none" />
        </g>
        
        <!-- Text with modern typography -->
        <text x="160" y="85" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="16" font-weight="600" letter-spacing="-0.5" fill="#333">Client Data Sources</text>
        <text x="160" y="110" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">First-Party Data Integration</text>
    </g>

    <!-- SECTION 2: Secure Data Connection - Glassmorphism Card -->
    <g filter="url(#neumorphic-shadow)">
        <!-- Card with glassmorphism effect -->
        <rect x="500" y="150" width="240" height="120" rx="16" fill="url(#glassGradient)" stroke="#FFFFFF" stroke-width="1" />
        <rect x="500" y="150" width="240" height="6" rx="3" fill="url(#adzetaGradient)" />
        
        <!-- Icon with gradient background -->
        <circle cx="530" cy="190" r="20" fill="#7D7AFF" fill-opacity="0.1" />
        <g transform="translate(530,190) scale(0.4)">
            <path d="M0,-25 L25,0 V20 C25,30 0,35 0,35 C0,35 -25,30 -25,20 V0 Z" stroke="#7D7AFF" stroke-width="3" fill="none" />
            <circle cx="0" cy="5" r="8" stroke="#7D7AFF" stroke-width="3" fill="none" />
            <path d="M0,-3 V10" stroke="#7D7AFF" stroke-width="3" />
        </g>
        
        <!-- Text with modern typography -->
        <text x="560" y="185" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="16" font-weight="600" letter-spacing="-0.5" fill="#333">Secure Data Connection</text>
        <text x="560" y="210" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">Data Ingestion &amp; Processing</text>
    </g>

    <!-- SECTION 3: Adzeta AI Platform - Glassmorphism Card -->
    <g filter="url(#neumorphic-shadow)">
        <!-- Card with glassmorphism effect -->
        <rect x="500" y="330" width="240" height="170" rx="16" fill="url(#glassGradient)" stroke="#FFFFFF" stroke-width="1" />
        <rect x="500" y="330" width="240" height="6" rx="3" fill="url(#adzetaGradient)" />
        
        <!-- Icon with gradient background -->
        <circle cx="530" cy="360" r="20" fill="#7D7AFF" fill-opacity="0.1" />
        <g transform="translate(530,360) scale(0.35)">
            <circle cx="-20" cy="-20" r="6" stroke="#7D7AFF" stroke-width="3" fill="none" />
            <circle cx="20" cy="-20" r="6" stroke="#7D7AFF" stroke-width="3" fill="none" />
            <circle cx="-20" cy="0" r="6" stroke="#7D7AFF" stroke-width="3" fill="none" />
            <circle cx="20" cy="0" r="6" stroke="#7D7AFF" stroke-width="3" fill="none" />
            <circle cx="0" cy="20" r="6" stroke="#7D7AFF" stroke-width="3" fill="none" />
            <path d="M-20,-14 L-20,-6 M-20,-14 L20,-14 M20,-14 L20,-6 M-20,6 L-20,14 M20,6 L20,14 M-20,6 L0,14 M20,6 L0,14" 
                  stroke="#7D7AFF" stroke-width="2" />
        </g>
        
        <!-- Text with modern typography -->
        <text x="560" y="360" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="16" font-weight="600" letter-spacing="-0.5" fill="#333">Adzeta AI Platform</text>
        <text x="560" y="385" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">Analysis &amp; Prediction</text>
        
        <!-- Process steps with animated checkmarks -->
        <g transform="translate(560, 410)">
            <use href="#checkmark" fill="#7D7AFF" stroke="#7D7AFF">
                <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite" begin="0s" />
            </use>
            <text x="15" y="4" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">Data Preparation</text>
        </g>
        
        <g transform="translate(560, 440)">
            <use href="#checkmark" fill="#7D7AFF" stroke="#7D7AFF">
                <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite" begin="1s" />
            </use>
            <text x="15" y="4" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">Model Training</text>
        </g>
        
        <g transform="translate(560, 470)">
            <use href="#checkmark" fill="#7D7AFF" stroke="#7D7AFF">
                <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" repeatCount="indefinite" begin="2s" />
            </use>
            <text x="15" y="4" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">pLTV Generation</text>
        </g>
    </g>

    <!-- SECTION 4: Signal Transmission - Glassmorphism Card -->
    <g filter="url(#neumorphic-shadow)">
        <!-- Card with glassmorphism effect -->
        <rect x="500" y="560" width="240" height="100" rx="16" fill="url(#glassGradient)" stroke="#FFFFFF" stroke-width="1" />
        <rect x="500" y="560" width="240" height="6" rx="3" fill="url(#adzetaGradient)" />
        
        <!-- Icon with gradient background -->
        <circle cx="530" cy="600" r="20" fill="#7D7AFF" fill-opacity="0.1" />
        <g transform="translate(530,600) scale(0.35)">
            <path d="M-20,0 H-10 M10,0 H20" stroke="#7D7AFF" stroke-width="3" stroke-linecap="round" />
            <path d="M-10,-15 L-10,15 M10,-15 L10,15" stroke="#7D7AFF" stroke-width="3" stroke-linecap="round" />
            <path d="M-10,-10 H10 M-10,0 H10 M-10,10 H10" stroke="#7D7AFF" stroke-width="3" stroke-linecap="round" />
            <path d="M25,-10 Q30,0 25,10" stroke="#7D7AFF" stroke-width="2" fill="none" stroke-linecap="round" />
            <path d="M30,-15 Q37,0 30,15" stroke="#7D7AFF" stroke-width="2" fill="none" stroke-linecap="round" />
        </g>
        
        <!-- Text with modern typography -->
        <text x="560" y="590" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="16" font-weight="600" letter-spacing="-0.5" fill="#333">Signal Transmission</text>
        <text x="560" y="615" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">Google Ads API Integration</text>
    </g>

    <!-- SECTION 5: Google Ads Platform - Glassmorphism Card -->
    <g filter="url(#neumorphic-shadow)">
        <!-- Card with glassmorphism effect -->
        <rect x="900" y="430" width="240" height="120" rx="16" fill="url(#glassGradient)" stroke="#FFFFFF" stroke-width="1" />
        <rect x="900" y="430" width="240" height="6" rx="3" fill="url(#googleGradient)" />
        
        <!-- Icon with gradient background -->
        <circle cx="930" cy="470" r="20" fill="#4285F4" fill-opacity="0.1" />
        <g transform="translate(930,470) scale(0.35)">
            <path d="M-5,0 A20,20 0 1,1 -5,0.1 M-5,0 H15" stroke="#4285F4" stroke-width="4" fill="none" stroke-linecap="round" />
            <g transform="translate(0, 25)">
                <path d="M-15,-5 L0,-20 L15,-5 L0,10 Z" stroke="#4285F4" stroke-width="2.5" fill="none" />
                <path d="M-7.5,-5 H7.5" stroke="#4285F4" stroke-width="2.5" />
            </g>
        </g>
        
        <!-- Text with modern typography -->
        <text x="960" y="465" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="16" font-weight="600" letter-spacing="-0.5" fill="#333">Google Ads Platform</text>
        <text x="960" y="490" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">AI Bidding Optimization</text>
    </g>

    <!-- SECTION 6: Optimized Campaign Delivery - Glassmorphism Card -->
    <g filter="url(#neumorphic-shadow)">
        <!-- Card with glassmorphism effect -->
        <rect x="900" y="610" width="240" height="110" rx="16" fill="url(#glassGradient)" stroke="#FFFFFF" stroke-width="1" />
        <rect x="900" y="610" width="240" height="6" rx="3" fill="url(#resultsGradient)" />
        
        <!-- Icon with gradient background -->
        <circle cx="930" cy="650" r="20" fill="#30D158" fill-opacity="0.1" />
        <g transform="translate(930,650) scale(0.35)">
            <circle cx="0" cy="0" r="25" stroke="#30D158" stroke-width="3" fill="none" />
            <circle cx="0" cy="0" r="15" stroke="#30D158" stroke-width="3" fill="none" />
            <circle cx="0" cy="0" r="5" fill="#30D158" />
            <path d="M-35,-35 L-15,-15 M35,-35 L15,-15 M-35,35 L-15,15 M35,35 L15,15" 
                  stroke="#30D158" stroke-width="3" stroke-linecap="round" />
            <path d="M-35,-35 L-30,-25 M-35,-35 L-25,-30 M35,-35 L30,-25 M35,-35 L25,-30" 
                  stroke="#30D158" stroke-width="2" stroke-linecap="round" />
        </g>
        
        <!-- Text with modern typography -->
        <text x="960" y="645" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="16" font-weight="600" letter-spacing="-0.5" fill="#333">Optimized Campaigns</text>
        <text x="960" y="670" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="12" fill="#666">High-Value Customer Acquisition</text>
    </g>

    <!-- SECTION 7: Business Results - Glassmorphism Card -->
    <g filter="url(#neumorphic-shadow)">
        <!-- Card with glassmorphism effect -->
        <rect x="900" y="780" width="240" height="80" rx="16" fill="url(#glassGradient)" stroke="#FFFFFF" stroke-width="1" />
        <rect x="900" y="780" width="240" height="6" rx="3" fill="url(#resultsGradient)" />
        
        <!-- Icon with gradient background -->
        <circle cx="930" cy="810" r="20" fill="#30D158" fill-opacity="0.1" />
        <g transform="translate(930,810) scale(0.35)">
            <rect x="-25" y="-25" width="50" height="50" rx="5" stroke="#30D158" stroke-width="3" fill="none" />
            <path d="M-25,25 L-15,10 L0,15 L15,-5 L25,-20" stroke="#30D158" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round" />
            <circle cx="-15" cy="10" r="3" fill="#30D158" />
            <circle cx="0" cy="15" r="3" fill="#30D158" />
            <circle cx="15" cy="-5" r="3" fill="#30D158" />
            <circle cx="25" cy="-20" r="3" fill="#30D158" />
        </g>
        
        <!-- Text with modern typography -->
        <text x="960" y="810" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="16" font-weight="600" letter-spacing="-0.5" fill="#333">Improved Business Results</text>
    </g>

    <!-- Cutting-edge Feedback Loop Card with Glassmorphism and animation -->
    <g filter="url(#glass-blur)" transform="translate(600, 850)">
        <!-- Glassmorphism pill shape with gradient border -->
        <rect x="-180" y="-20" width="360" height="40" rx="20" fill="url(#glassGradient)" stroke="url(#feedbackGradient)" stroke-width="1.5" />
        
        <!-- Animated circular refresh icon -->
        <g transform="translate(-150, 0)">
            <circle cx="0" cy="0" r="15" fill="url(#feedbackGradient)" fill-opacity="0.1" />
            <path d="M0,-8 A8,8 0 1,1 -8,0 M-8,0 H0" stroke="url(#feedbackGradient)" stroke-width="2" fill="none" stroke-linecap="round" transform="rotate(30)">
                <animateTransform 
                    attributeName="transform"
                    attributeType="XML"
                    type="rotate"
                    from="30 0 0"
                    to="390 0 0"
                    dur="8s"
                    repeatCount="indefinite" />
            </path>
        </g>
        
        <!-- Text with gradient effect and subtle animation -->
        <text x="0" y="5" text-anchor="middle" font-family="Inter, -apple-system, BlinkMacSystemFont, sans-serif" font-size="14" font-weight="500" fill="url(#feedbackGradient)">
            Continuous Feedback Loop &amp; Model Refinement
            <animate attributeName="opacity" values="0.8;1;0.8" dur="4s" repeatCount="indefinite" />
        </text>
    </g>
</svg>
