/* Hero Animation Styles for Platform Page */

/* Main container for the hero animation */
.ecom-ppc .hero-animation-container {
    position: relative;
    width: 100%;
    height: 550px;
    margin: 0px auto 50px;
    padding-bottom: 30px;
}

/* Base styles for all animation elements */
.ecom-ppc .animation-element {
    position: absolute;
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: visible;
}

/* Center element - Adzeta AI Core */
.ecom-ppc .adzeta-ai-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(0deg);
    width: 180px;
    height: 180px;
    z-index: 10;
    animation: professional-glow 4s infinite ease-in-out;
    filter: drop-shadow(0 0 20px rgba(233, 88, 161, 0.5));
}

/* Left side elements - Data Input */
.ecom-ppc .adzeta-ai-left-1 {
    top: 20%;
    left: 15%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    z-index: 5;
    animation: float-left-1 8s infinite ease-in-out;
}

.ecom-ppc .adzeta-ai-left-2 {
    top: 50%;
    left: 8%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    z-index: 5;
    animation: float-left-2 8s infinite ease-in-out;
}

.ecom-ppc .adzeta-ai-left-3 {
    top: 80%;
    left: 15%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    z-index: 5;
    animation: float-left-3 8s infinite ease-in-out;
}

/* Right side elements - Ad Platforms */
.ecom-ppc .adzeta-ai-right-1 {
    top: 20%;
    right: 15%;
    transform: translate(50%, -50%);
    width: 120px;
    height: 120px;
    z-index: 5;
    animation: float-right-1 8s infinite ease-in-out;
}

.ecom-ppc .adzeta-ai-right-2 {
    top: 50%;
    right: 8%;
    transform: translate(50%, -50%);
    width: 120px;
    height: 120px;
    z-index: 5;
    animation: float-right-2 8s infinite ease-in-out;
}

.ecom-ppc .adzeta-ai-right-3 {
    top: 80%;
    right: 15%;
    transform: translate(50%, -50%);
    width: 120px;
    height: 120px;
    z-index: 5;
    animation: float-right-3 8s infinite ease-in-out;
}



/* Connection lines - Apple-inspired design */
.ecom-ppc .connection-line {
    position: absolute;
    z-index: 1;
    overflow: visible;
}

/* Connection lines base styles */
.ecom-ppc .connection-left-1, .ecom-ppc .connection-left-2, .ecom-ppc .connection-left-3,
.ecom-ppc .connection-right-1, .ecom-ppc .connection-right-2, .ecom-ppc .connection-right-3 {
    position: absolute;
}

/* Apple-inspired circuit path design */
.ecom-ppc .connection-left-1::before, .ecom-ppc .connection-left-2::before, .ecom-ppc .connection-left-3::before,
.ecom-ppc .connection-right-1::before, .ecom-ppc .connection-right-2::before, .ecom-ppc .connection-right-3::before {
    content: '';
    position: absolute;
    background: rgba(255, 255, 255, 0.15);
    height: 1px;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

.ecom-ppc .connection-left-1::after, .ecom-ppc .connection-left-2::after, .ecom-ppc .connection-left-3::after,
.ecom-ppc .connection-right-1::after, .ecom-ppc .connection-right-2::after, .ecom-ppc .connection-right-3::after {
    content: '';
    position: absolute;
    background: linear-gradient(90deg, rgba(233, 88, 161, 0.8), rgba(143, 118, 245, 0.8));
    height: 2px;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 2;
    transform-origin: left center;
    box-shadow: 0 0 8px rgba(233, 88, 161, 0.6);
    border-radius: 4px;
}

/* Left side connections - Converging to center */
.ecom-ppc .connection-left-1 {
    top: 25%;
    left: 15%;
    width: 35%;
    height: 2px;
    transform: rotate(22deg);
    transform-origin: left center;
}

.ecom-ppc .connection-left-1::after {
    animation: apple-circuit-flow 3s infinite cubic-bezier(0.42, 0, 0.58, 1);
}

.ecom-ppc .connection-left-2 {
    top: 50%;
    left: 8%;
    width: 42%;
    height: 2px;
    transform: rotate(0deg);
    transform-origin: left center;
}

.ecom-ppc .connection-left-2::after {
    animation: apple-circuit-flow 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 0.5s;
}

.ecom-ppc .connection-left-3 {
    top: 75%;
    left: 15%;
    width: 35%;
    height: 2px;
    transform: rotate(331deg);
    transform-origin: left center;
}

.ecom-ppc .connection-left-3::after {
    animation: apple-circuit-flow 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 1s;
}

/* Right side connections - Diverging from center */
.ecom-ppc .connection-right-1 {
    top: 38%;
    right: 15%;
    width: 35%;
    height: 2px;
    transform: rotate(-25deg);
    transform-origin: left center;
}

.ecom-ppc .connection-right-1::after {
    animation: apple-circuit-flow 3s infinite cubic-bezier(0.42, 0, 0.58, 1);
    transform-origin: left center;
}

.ecom-ppc .connection-right-2 {
    top: 50%;
    right: 8%;
    width: 42%;
    height: 2px;
    transform: rotate(0deg);
    transform-origin: left center;
}

.ecom-ppc .connection-right-2::after {
    animation: apple-circuit-flow 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 0.5s;
    transform-origin: left center;
}

.ecom-ppc .connection-right-3 {
    top: 56%;
    right: 15%;
    width: 35%;
    height: 2px;
    transform: rotate(34deg);
    transform-origin: left center;
}

.ecom-ppc .connection-right-3::after {
    animation: apple-circuit-flow 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 1s;
    transform-origin: left center;
}



/* Data flow particles - Apple-inspired */
.ecom-ppc .data-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0);
    z-index: 3;
    border-radius: 50%;
    opacity: 0;
    mix-blend-mode: screen;
    box-shadow:
        0 0 0 1px rgba(255, 255, 255, 0.95),
        0 0 8px 2px rgba(233, 88, 161, 0.9),
        0 0 16px 4px rgba(143, 118, 245, 0.6);
    backdrop-filter: blur(1px);
    -webkit-backdrop-filter: blur(1px);
}

.ecom-ppc .data-particle::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.7;
}

/* Left side particles */
.ecom-ppc .particle-left-1 {
    top: 25%;
    left: 15%;
    animation: apple-particle-left-1 3s infinite cubic-bezier(0.42, 0, 0.58, 1);
}

.ecom-ppc .particle-left-1-delay {
    top: 25%;
    left: 15%;
    animation: apple-particle-left-1 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 1.5s;
}

.ecom-ppc .particle-left-2 {
    top: 50%;
    left: 8%;
    animation: apple-particle-left-2 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 0.5s;
}

.ecom-ppc .particle-left-2-delay {
    top: 50%;
    left: 8%;
    animation: apple-particle-left-2 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 2s;
}

.ecom-ppc .particle-left-3 {
    top: 75%;
    left: 15%;
    animation: apple-particle-left-3 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 1s;
}

.ecom-ppc .particle-left-3-delay {
    top: 75%;
    left: 15%;
    animation: apple-particle-left-3 3s infinite cubic-bezier(0.42, 0, 0.58, 1) 2.5s;
}

/* Right side particles */
.ecom-ppc .particle-right-1 {
    top: 25%;
    right: 15%;
    transform-origin: left center;
    animation: apple-particle-right-1 4s infinite cubic-bezier(0.42, 0, 0.58, 1);
}

.ecom-ppc .particle-right-1-delay {
    top: 25%;
    right: 15%;
    transform-origin: left center;
    animation: apple-particle-right-1 4s infinite cubic-bezier(0.42, 0, 0.58, 1) 2s;
}

.ecom-ppc .particle-right-2 {
    top: 50%;
    right: 8%;
    transform-origin: left center;
    animation: apple-particle-right-2 4s infinite cubic-bezier(0.42, 0, 0.58, 1) 0.67s;
}

.ecom-ppc .particle-right-2-delay {
    top: 50%;
    right: 8%;
    transform-origin: left center;
    animation: apple-particle-right-2 4s infinite cubic-bezier(0.42, 0, 0.58, 1) 2.67s;
}

.ecom-ppc .particle-right-3 {
    top: 75%;
    right: 15%;
    transform-origin: left center;
    animation: apple-particle-right-3 4s infinite cubic-bezier(0.42, 0, 0.58, 1) 1.33s;
}

.ecom-ppc .particle-right-3-delay {
    top: 75%;
    right: 15%;
    transform-origin: left center;
    animation: apple-particle-right-3 4s infinite cubic-bezier(0.42, 0, 0.58, 1) 3.33s;
}



/* Element labels */
.ecom-ppc .element-label {
    position: absolute;
    font-size: 11px;
    font-weight: 500;
    color: rgba(233, 88, 161, 0.85);
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    width: 100%;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    z-index: 6;
    opacity: 0.9;
}

/* Left side labels */
.ecom-ppc .label-left-1 {
   bottom: -10px;
    left: -30px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(30deg);
}

.ecom-ppc .label-left-2 {
   bottom: -10px;
    left: -30px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(30deg);
}

.ecom-ppc .label-left-3 {
   bottom: -10px;
    left: -30px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(30deg);
}

/* Right side labels */
.ecom-ppc .label-right-1 {
     bottom: -5px;
    left: -25px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(30deg);
}

.ecom-ppc .label-right-2 {
     bottom: -5px;
    left: -25px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(30deg);
}

.ecom-ppc .label-right-3 {
    bottom: -5px;
    left: -25px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(30deg);


}

/* Special styling for center label to appear inscribed on the chip */
.ecom-ppc .label-center {
    top: 30%;
    left: 0;
    width: 100%;
    margin: 0;
    transform: rotate(0deg);
    color: rgba(255, 255, 255, 0.9);
    font-size: 10px;
    font-weight: 700;
    letter-spacing: 2px;
    text-shadow: 0 0 8px rgba(233, 88, 161, 0.9);
    mix-blend-mode: overlay;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(233, 88, 161, 0.7));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    animation: glow 2s infinite ease-in-out;
}

@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 8px rgba(233, 88, 161, 0.7);
    }
    50% {
        text-shadow: 0 0 15px rgba(233, 88, 161, 1);
    }
}



/* Animation keyframes */
@keyframes pulsate {
    0% {
        transform: translate(-50%, -50%) scale(1);
        filter: brightness(1) drop-shadow(0 0 10px rgba(233, 88, 161, 0.3));
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        filter: brightness(1.2) drop-shadow(0 0 20px rgba(233, 88, 161, 0.6));
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        filter: brightness(1) drop-shadow(0 0 10px rgba(233, 88, 161, 0.3));
    }
}

@keyframes professional-glow {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
        filter: brightness(1) drop-shadow(0 0 20px rgba(233, 88, 161, 0.4));
    }
    50% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1.04) translateY(-5px);
        filter: brightness(1.15) drop-shadow(0 0 30px rgba(233, 88, 161, 0.7));
    }
    100% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
        filter: brightness(1) drop-shadow(0 0 20px rgba(233, 88, 161, 0.4));
    }
}

/* Mobile version with reduced movement */
@media (max-width: 767px) {
    @keyframes professional-glow {
        0% {
            transform: translate(-50%, -50%) rotate(0deg) scale(1);
            filter: brightness(1) drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));
        }
        50% {
            transform: translate(-50%, -50%) rotate(0deg) scale(1.03) translateY(-3px);
            filter: brightness(1.12) drop-shadow(0 0 25px rgba(233, 88, 161, 0.7));
        }
        100% {
            transform: translate(-50%, -50%) rotate(0deg) scale(1);
            filter: brightness(1) drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));
        }
    }
}

/* Left side animations */
@keyframes float-left-1 {
    0% {
        transform: translate(-50%, -50%);
        filter: brightness(1);
    }
    25% {
        transform: translate(-50%, -55%) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(-45%, -50%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(-50%, -45%) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(-50%, -50%);
        filter: brightness(1);
    }
}

@keyframes float-left-2 {
    0% {
        transform: translate(-50%, -50%);
        filter: brightness(1);
    }
    25% {
        transform: translate(-55%, -50%) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(-50%, -45%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(-45%, -50%) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(-50%, -50%);
        filter: brightness(1);
    }
}

@keyframes float-left-3 {
    0% {
        transform: translate(-50%, -50%);
        filter: brightness(1);
    }
    25% {
        transform: translate(-50%, -45%) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(-45%, -50%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(-50%, -55%) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(-50%, -50%);
        filter: brightness(1);
    }
}

/* Right side animations */
@keyframes float-right-1 {
    0% {
        transform: translate(50%, -50%);
        filter: brightness(1);
    }
    25% {
        transform: translate(50%, -45%) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(45%, -50%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(50%, -55%) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(50%, -50%);
        filter: brightness(1);
    }
}

@keyframes float-right-2 {
    0% {
        transform: translate(50%, -50%);
        filter: brightness(1);
    }
    25% {
        transform: translate(55%, -50%) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(50%, -45%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(45%, -50%) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(50%, -50%);
        filter: brightness(1);
    }
}

@keyframes float-right-3 {
    0% {
        transform: translate(50%, -50%);
        filter: brightness(1);
    }
    25% {
        transform: translate(50%, -45%) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(55%, -50%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(50%, -55%) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(50%, -50%);
        filter: brightness(1);
    }
}



@keyframes apple-circuit-flow {
    0% {
        transform: scaleX(0);
        opacity: 0;
    }
    5% {
        opacity: 1;
    }
    45% {
        transform: scaleX(1);
        opacity: 1;
    }
    50% {
        transform: scaleX(1);
        transform-origin: right center;
        opacity: 1;
    }
    95% {
        opacity: 1;
    }
    100% {
        transform: scaleX(0);
        transform-origin: right center;
        opacity: 0;
    }
}

@keyframes apple-circuit-flow-reverse {
    0% {
        transform: scaleX(0);
        opacity: 0;
        transform-origin: left center;
    }
    5% {
        opacity: 1;
    }
    45% {
        transform: scaleX(1);
        opacity: 1;
    }
    50% {
        transform: scaleX(1);
        transform-origin: left center;
        opacity: 1;
    }
    95% {
        opacity: 1;
    }
    100% {
        transform: scaleX(0);
        transform-origin: left center;
        opacity: 0;
    }
}



/* Left side particle animations - Converging to center */
@keyframes apple-particle-left-1 {
    0%, 15% {
        opacity: 0;
        transform: translate(0, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    20% {
        opacity: 1;
        transform: translate(30px, 12px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    30% {
        opacity: 1;
        transform: translate(60px, 24px) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    40% {
        opacity: 1;
        transform: translate(90px, 36px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    45%, 100% {
        opacity: 0;
        transform: translate(120px, 48px) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}

/* Right side particle animations - Diverging from center */
@keyframes apple-particle-right-1 {
    0%, 10% {
        opacity: 0;
        transform: translate(0, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    15% {
        opacity: 0.7;
        transform: translate(15px, -7px) scale(0.6);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.8),
            0 0 5px 1px rgba(233, 88, 161, 0.7),
            0 0 10px 2px rgba(143, 118, 245, 0.4);
    }
    25% {
        opacity: 1;
        transform: translate(30px, -14px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    35% {
        opacity: 1;
        transform: translate(60px, -28px) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    45% {
        opacity: 1;
        transform: translate(90px, -42px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    55%, 100% {
        opacity: 0;
        transform: translate(120px, -56px) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}



/* Additional particle animations for left side - Converging to center */
@keyframes apple-particle-left-2 {
    0%, 15% {
        opacity: 0;
        transform: translate(0, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    20% {
        opacity: 1;
        transform: translate(30px, 0) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    30% {
        opacity: 1;
        transform: translate(60px, 0) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    40% {
        opacity: 1;
        transform: translate(90px, 0) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    45%, 100% {
        opacity: 0;
        transform: translate(120px, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}

@keyframes apple-particle-left-3 {
    0%, 15% {
        opacity: 0;
        transform: translate(0, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    20% {
        opacity: 1;
        transform: translate(30px, -10px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    30% {
        opacity: 1;
        transform: translate(60px, -20px) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    40% {
        opacity: 1;
        transform: translate(90px, -30px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    45%, 100% {
        opacity: 0;
        transform: translate(120px, -40px) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}

/* Additional particle animations for right side - Diverging from center */
@keyframes apple-particle-right-2 {
    0%, 10% {
        opacity: 0;
        transform: translate(0, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    15% {
        opacity: 0.7;
        transform: translate(15px, 0) scale(0.6);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.8),
            0 0 5px 1px rgba(233, 88, 161, 0.7),
            0 0 10px 2px rgba(143, 118, 245, 0.4);
    }
    25% {
        opacity: 1;
        transform: translate(30px, 0) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    35% {
        opacity: 1;
        transform: translate(60px, 0) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    45% {
        opacity: 1;
        transform: translate(90px, 0) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    55%, 100% {
        opacity: 0;
        transform: translate(120px, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}

@keyframes apple-particle-right-3 {
    0%, 10% {
        opacity: 0;
        transform: translate(0, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    15% {
        opacity: 0.7;
        transform: translate(15px, 10px) scale(0.6);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.8),
            0 0 5px 1px rgba(233, 88, 161, 0.7),
            0 0 10px 2px rgba(143, 118, 245, 0.4);
    }
    25% {
        opacity: 1;
        transform: translate(30px, 20px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    35% {
        opacity: 1;
        transform: translate(60px, 40px) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    45% {
        opacity: 1;
        transform: translate(90px, 60px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    55%, 100% {
        opacity: 0;
        transform: translate(120px, 80px) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}

/* Responsive adjustments for adzeta-ai elements */
@media (max-width: 991px) {
    /* Left side elements - Data Input */
    .ecom-ppc .adzeta-ai-left-1 {
        top: 25%;
        left: 18%;
        width: 100px;
        height: 100px;
    }

    .ecom-ppc .adzeta-ai-left-2 {
        top: 50%;
        left: 10%;
        width: 100px;
        height: 100px;
    }

    .ecom-ppc .adzeta-ai-left-3 {
        top: 75%;
        left: 18%;
        width: 100px;
        height: 100px;
    }

    /* Right side elements - Ad Platforms */
    .ecom-ppc .adzeta-ai-right-1 {
        top: 25%;
        right: 18%;
        width: 100px;
        height: 100px;
    }

    .ecom-ppc .adzeta-ai-right-2 {
        top: 50%;
        right: 10%;
        width: 100px;
        height: 100px;
    }

    .ecom-ppc .adzeta-ai-right-3 {
        top: 75%;
        right: 18%;
        width: 100px;
        height: 100px;
    }
}

/* Original responsive adjustments */
@media (max-width: 991px) {
    .ecom-ppc .hero-animation-container {
        height: 450px;
    }

    .ecom-ppc .adzeta-ai-center {
        width: 180px;
        height: 180px;
        top: 50%;
        transform: translate(-50%, -50%) rotate(0deg);
        filter: drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));
        animation: professional-glow 4s infinite ease-in-out;
    }

    .ecom-ppc .adzeta-ai-left,
    .ecom-ppc .adzeta-ai-right{
        width: 225px;
        height: 225px;
    }





    .ecom-ppc .adzeta-ai-left {
        left: 14%;
		top: 55%;
    }

    .ecom-ppc .adzeta-ai-right {
        right: 14%;
		top:60%;
    }

    .ecom-ppc .connection-left-1 {
        top: 25%;
        left: 15%;
        width: 35%;
        transform: rotate(12deg);
    }

    .ecom-ppc .connection-left-2 {
        top: 50%;
        left: 8%;
        width: 42%;
        transform: rotate(0deg);
    }

    .ecom-ppc .connection-left-3 {
        top: 70%;
        left: 15%;
        width: 35%;
        transform: rotate(-12deg);
    }

    .ecom-ppc .connection-right-1 {
        top: 38%;
        right: 15%;
        width: 35%;
        transform: rotate(-16deg);
        transform-origin: left center;
    }

    .ecom-ppc .connection-right-2 {
        top: 50%;
        right: 8%;
        width: 42%;
        transform: rotate(0deg);
        transform-origin: left center;
    }

    .ecom-ppc .connection-right-3 {
        top: 60%;
        right: 15%;
        width: 35%;
        transform: rotate(16deg);
        transform-origin: left center;
    }

    .ecom-ppc .label-center {
        font-size: 10px;
        top: 30%;
        transform: rotate(0deg);
    }

    .ecom-ppc .label-left {
        top: 150px;
        left: 22px;
        font-size: 9px;
    }

    .ecom-ppc .label-right {
        top: 125px;
        right: 40px;
        font-size: 9px;
    }

    .ecom-ppc .label-bottom {
        bottom: -30px;
        left: 35px;
        font-size: 9px;
    }
}

@media (max-width: 767px) {
    .ecom-ppc .hero-animation-container {
        height: 380px;
        padding-bottom: 40px;
    }

    .ecom-ppc .adzeta-ai-bottom {
        bottom: 25%;
    }

    .ecom-ppc .adzeta-ai-center {
        width: 120px;
        height: 120px;
        top: 50%;
        transform: translate(-50%, -50%) rotate(0deg);
        filter: drop-shadow(0 0 12px rgba(233, 88, 161, 0.4));
        animation: professional-glow 4s infinite ease-in-out;
    }

    /* Left side elements - Data Input */
    .ecom-ppc .adzeta-ai-left-1 {
        top: 25%;
        left: 20%;
        width: 80px;
        height: 80px;
    }

    .ecom-ppc .adzeta-ai-left-2 {
        top: 50%;
        left: 12%;
        width: 80px;
        height: 80px;
    }

    .ecom-ppc .adzeta-ai-left-3 {
        top: 75%;
        left: 20%;
        width: 80px;
        height: 80px;
    }

    /* Right side elements - Ad Platforms */
    .ecom-ppc .adzeta-ai-right-1 {
        top: 25%;
        right: 20%;
        width: 80px;
        height: 80px;
    }

    .ecom-ppc .adzeta-ai-right-2 {
        top: 50%;
        right: 12%;
        width: 80px;
        height: 80px;
    }

    .ecom-ppc .adzeta-ai-right-3 {
        top: 75%;
        right: 20%;
        width: 80px;
        height: 80px;
    }

    /* Legacy styles for backward compatibility */
    .ecom-ppc .adzeta-ai-left,
    .ecom-ppc .adzeta-ai-right {
        width: 140px;
        height: 140px;
    }

    .ecom-ppc .adzeta-ai-bottom {
        width: 110px;
        height: 110px;
    }

    .ecom-ppc .adzeta-ai-left {
        left: 15%;
        top: 40%;
    }

    .ecom-ppc .adzeta-ai-right {
        right: 15%;
        top: 40%;
    }

    .ecom-ppc .connection-left-1 {
        top: 25%;
        left: 15%;
        width: 35%;
        transform: rotate(18deg);
    }

    .ecom-ppc .connection-left-2 {
        top: 50%;
        left: 8%;
        width: 42%;
        transform: rotate(0deg);
    }

    .ecom-ppc .connection-left-3 {
        top: 75%;
        left: 15%;
        width: 35%;
        transform: rotate(-20deg);
    }

    .ecom-ppc .connection-right-1 {
        top: 38%;
        right: 18%;
        width: 28%;
        transform: rotate(-30deg);
        transform-origin: left center;
    }

    .ecom-ppc .connection-right-2 {
        top: 50%;
        right: 8%;
        width: 42%;
        transform: rotate(0deg);
        transform-origin: left center;
    }

    .ecom-ppc .connection-right-3 {
        top: 60%;
        right: 15%;
        width: 35%;
        transform: rotate(25deg);
        transform-origin: left center;
    }

    .ecom-ppc .element-label {
        font-size: 8px;
        letter-spacing: 1px;
    }

    .ecom-ppc .label-center {
        font-size: 9px;
        letter-spacing: 1.5px;
        top: 30%;
        transform: rotate(0deg);
    }

    .ecom-ppc .label-left {
        top: 90px;
        left: 20px;
        font-size: 8px;
    }

    .ecom-ppc .label-right {
        top: 85px;
        right: 20px;
        font-size: 8px;
    }

    .ecom-ppc .label-bottom {
        bottom: -20px;
        left: 30px;
        font-size: 8px;
    }

    .ecom-ppc .data-particle {
        width: 8px;
        height: 8px;
    }

    .ecom-ppc .particle-left-1 {
        top: 48%;
        left: 20%;
    }

    .ecom-ppc .particle-left-2 {
        top: 40%;
        left: 25%;
    }

    .ecom-ppc .particle-right-1 {
        top: 33%;
        right: 20%;
    }

    .ecom-ppc .particle-right-2 {
        top: 30%;
        right: 25%;
    }
}

/* Extra small devices (phones, less than 576px) */
@media (max-width: 575px) {
    .ecom-ppc .hero-animation-container {
        height: 320px;
        padding-bottom: 30px;
    }

    .ecom-ppc .adzeta-ai-center {
        width: 90px;
        height: 90px;
    }

    /* Left side elements - Data Input */
    .ecom-ppc .adzeta-ai-left-1 {
        top: 25%;
        left: 22%;
        width: 60px;
        height: 60px;
    }

    .ecom-ppc .adzeta-ai-left-2 {
        top: 50%;
        left: 15%;
        width: 60px;
        height: 60px;
    }

    .ecom-ppc .adzeta-ai-left-3 {
        top: 75%;
        left: 22%;
        width: 60px;
        height: 60px;
    }

    /* Right side elements - Ad Platforms */
    .ecom-ppc .adzeta-ai-right-1 {
        top: 25%;
        right: 22%;
        width: 60px;
        height: 60px;
    }

    .ecom-ppc .adzeta-ai-right-2 {
        top: 50%;
        right: 15%;
        width: 60px;
        height: 60px;
    }

    .ecom-ppc .adzeta-ai-right-3 {
        top: 75%;
        right: 22%;
        width: 60px;
        height: 60px;
    }

    /* Connection lines adjustments */
    .ecom-ppc .connection-left-1,
    .ecom-ppc .connection-left-2,
    .ecom-ppc .connection-left-3,
    .ecom-ppc .connection-right-1,
    .ecom-ppc .connection-right-2,
    .ecom-ppc .connection-right-3 {
        height: 1px;
    }

    .ecom-ppc .connection-left-1::after,
    .ecom-ppc .connection-left-2::after,
    .ecom-ppc .connection-left-3::after,
    .ecom-ppc .connection-right-1::after,
    .ecom-ppc .connection-right-2::after,
    .ecom-ppc .connection-right-3::after {
        height: 1px;
        box-shadow: 0 0 5px rgba(233, 88, 161, 0.5);
    }

    /* Particle adjustments */
    .ecom-ppc .data-particle {
        width: 5px;
        height: 5px;
    }

    /* Label adjustments */
    .ecom-ppc .element-label {
        font-size: 7px;
        letter-spacing: 0.5px;
    }

    .ecom-ppc .label-center {
        font-size: 8px;
        letter-spacing: 1px;
    }
}
