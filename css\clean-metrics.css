/* Clean Metrics Section Styles */
.metrics-section {
    padding: 80px 0;
    background-color: #fafafa;
}

.metrics-heading {
    margin-bottom: 50px;
}

.metrics-heading h2 {
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 32px;
    line-height: 1.3;
}

.metrics-heading p {
    font-size: 18px;
    line-height: 1.6;
    color: #555;
    max-width: 700px;
    margin: 0 auto;
}

/* Clean metric card styles */
.metric-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
    padding: 30px;
    height: 100%;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

/* Card header */
.metric-title {
    font-size: 14px;
    font-weight: 600;
    color: #555;
    margin: 0 0 20px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-badge {
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    float: right;
}

.metric-badge i {
    margin-right: 4px;
    font-size: 10px;
}

/* Metric value styling */
.metric-value {
    font-size: 48px;
    line-height: 1;
    font-weight: 700;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.trend {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.trend i {
    margin-right: 3px;
}

.metric-description {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 25px;
}

/* Mini chart container */
.mini-chart {
    height: 60px;
    position: relative;
    margin-top: auto;
}

/* Color variations */
.metric-card.roas {
    border-left-color: #8f76f5;
}
.metric-card.roas .metric-value {
    color: #8f76f5;
}
.metric-card.roas .metric-badge {
    background-color: rgba(143, 118, 245, 0.1);
    color: #8f76f5;
}

.metric-card.cac {
    border-left-color: #e958a1;
}
.metric-card.cac .metric-value {
    color: #e958a1;
}
.metric-card.cac .metric-badge {
    background-color: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.metric-card.revenue {
    border-left-color: #8f76f5;
}
.metric-card.revenue .metric-value {
    color: #8f76f5;
}
.metric-card.revenue .metric-badge {
    background-color: rgba(143, 118, 245, 0.1);
    color: #8f76f5;
}

.metric-card.waste {
    border-left-color: #e958a1;
}
.metric-card.waste .metric-value {
    color: #e958a1;
}
.metric-card.waste .metric-badge {
    background-color: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.trend-up {
    color: #2ecc71;
}

.trend-down {
    color: #3498db;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .metrics-section {
        padding: 60px 0;
    }
    
    .metric-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 767px) {
    .metrics-section {
        padding: 40px 0;
    }
    
    .metric-value {
        font-size: 42px;
    }
}
