<?php

namespace AdZetaAdmin\Services;

/**
 * Google Gemini AI Service
 * Handles all interactions with Google Gemini API
 */
class GeminiAIService
{
    private $db;
    private $apiKeys = [];
    private $currentKeyIndex = 0;
    private $baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/';
    private $model = 'gemini-2.5-flash'; // Default model (latest)
    private $maxRetries = 3;
    private $rateLimitDelay = 1; // seconds

    public function __construct($db)
    {
        $this->db = $db;
        $this->loadApiKeys();
        $this->loadModelSettings();
    }

    /**
     * Load API keys from database settings
     */
    private function loadApiKeys()
    {
        try {
            $setting = $this->db->fetch(
                "SELECT setting_value FROM settings WHERE setting_key = ?",
                ['gemini_api_keys']
            );

            if ($setting && !empty($setting['setting_value'])) {
                $keys = json_decode($setting['setting_value'], true);
                if (is_array($keys)) {
                    $this->apiKeys = array_filter($keys, function($key) {
                        return !empty($key['api_key']) && $key['is_active'];
                    });
                }
            }
        } catch (\Exception $e) {
            error_log('Failed to load Gemini API keys: ' . $e->getMessage());
        }
    }

    /**
     * Load model settings from database
     */
    private function loadModelSettings()
    {
        try {
            $setting = $this->db->fetch(
                "SELECT setting_value FROM settings WHERE setting_key = ?",
                ['ai_gemini_model']
            );

            if ($setting && !empty($setting['setting_value'])) {
                // Validate model selection
                $validModels = [
                    'gemini-2.5-flash',
                    'gemini-2.5-pro',
                    'gemini-2.0-flash',
                    'gemini-1.5-flash',
                    'gemini-1.5-pro',
                    'gemini-1.0-pro'
                ];

                if (in_array($setting['setting_value'], $validModels)) {
                    $this->model = $setting['setting_value'];
                } else {
                    error_log('Invalid Gemini model selected: ' . $setting['setting_value'] . '. Using default.');
                }
            }
        } catch (\Exception $e) {
            error_log('Failed to load Gemini model setting: ' . $e->getMessage());
        }
    }

    /**
     * Get current API key with automatic failover
     */
    private function getCurrentApiKey()
    {
        if (empty($this->apiKeys)) {
            throw new \Exception('No active Gemini API keys configured');
        }

        if ($this->currentKeyIndex >= count($this->apiKeys)) {
            $this->currentKeyIndex = 0;
        }

        return $this->apiKeys[$this->currentKeyIndex]['api_key'];
    }

    /**
     * Switch to next API key on failure
     */
    private function switchToNextKey()
    {
        $this->currentKeyIndex++;
        if ($this->currentKeyIndex >= count($this->apiKeys)) {
            $this->currentKeyIndex = 0;
            return false; // All keys exhausted
        }
        return true;
    }

    /**
     * Make API request to Gemini
     */
    private function makeRequest($endpoint, $data, $retryCount = 0)
    {
        if ($retryCount >= $this->maxRetries) {
            throw new \Exception('Maximum retry attempts reached');
        }

        $apiKey = $this->getCurrentApiKey();
        $url = $this->baseUrl . $this->model . ':' . $endpoint . '?key=' . $apiKey;

        $headers = [
            'Content-Type: application/json',
            'User-Agent: AdZeta-Admin/1.0'
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \Exception('cURL error: ' . $error);
        }

        $decodedResponse = json_decode($response, true);

        // Handle rate limiting and quota errors
        if ($httpCode === 429 || ($httpCode === 403 && isset($decodedResponse['error']['code']) && $decodedResponse['error']['code'] === 'QUOTA_EXCEEDED')) {
            if ($this->switchToNextKey()) {
                sleep($this->rateLimitDelay);
                return $this->makeRequest($endpoint, $data, $retryCount + 1);
            } else {
                throw new \Exception('All API keys have exceeded their quota');
            }
        }

        if ($httpCode !== 200) {
            $errorMessage = isset($decodedResponse['error']['message'])
                ? $decodedResponse['error']['message']
                : 'HTTP ' . $httpCode . ' error';
            throw new \Exception('API request failed: ' . $errorMessage);
        }

        return $decodedResponse;
    }

    /**
     * Generate content using Gemini
     */
    public function generateContent($prompt, $options = [])
    {
        $defaultOptions = [
            'temperature' => 0.7,
            'maxOutputTokens' => 2048,
            'topP' => 0.8,
            'topK' => 40
        ];

        $options = array_merge($defaultOptions, $options);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => $options['temperature'],
                'maxOutputTokens' => $options['maxOutputTokens'],
                'topP' => $options['topP'],
                'topK' => $options['topK']
            ],
            'safetySettings' => [
                [
                    'category' => 'HARM_CATEGORY_HARASSMENT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_HATE_SPEECH',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ],
                [
                    'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ]
            ]
        ];

        try {
            $response = $this->makeRequest('generateContent', $data);

            if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                return [
                    'success' => true,
                    'content' => $response['candidates'][0]['content']['parts'][0]['text'],
                    'usage' => $response['usageMetadata'] ?? null
                ];
            } else {
                throw new \Exception('Invalid response format from Gemini API');
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate blog post title suggestions
     */
    public function generateTitleSuggestions($topic, $keywords = [], $count = 5)
    {
        $keywordText = !empty($keywords) ? ' Keywords to include: ' . implode(', ', $keywords) : '';

        $prompt = "Generate {$count} compelling, SEO-friendly blog post titles about: {$topic}.{$keywordText}

Requirements:
- Titles should be 50-60 characters long
- Include power words and emotional triggers
- Make them click-worthy but not clickbait
- Ensure they're search engine optimized
- Format as a numbered list

Example format:
1. [Title here]
2. [Title here]";

        return $this->generateContent($prompt, ['temperature' => 0.8]);
    }

    /**
     * Generate SEO meta description
     */
    public function generateMetaDescription($title, $content, $keywords = [])
    {
        $keywordText = !empty($keywords) ? ' Focus keywords: ' . implode(', ', $keywords) : '';

        $prompt = "Create an SEO-optimized meta description for this blog post:

Title: {$title}
Content preview: " . substr(strip_tags($content), 0, 500) . "...{$keywordText}

Requirements:
- 150-160 characters maximum
- Include primary keyword naturally
- Compelling and click-worthy
- Accurately describe the content
- Include a call-to-action if appropriate

Return only the meta description text, no additional formatting.";

        return $this->generateContent($prompt, ['temperature' => 0.6, 'maxOutputTokens' => 200]);
    }

    /**
     * Generate content tags
     */
    public function generateTags($title, $content, $maxTags = 10)
    {
        $prompt = "Analyze this blog post and suggest relevant tags:

Title: {$title}
Content: " . substr(strip_tags($content), 0, 1000) . "...

Generate up to {$maxTags} relevant tags that:
- Are commonly searched keywords
- Relate to the content topic
- Help with content discovery
- Are 1-3 words each
- Mix broad and specific terms

Return as a comma-separated list only, no additional text.";

        return $this->generateContent($prompt, ['temperature' => 0.5, 'maxOutputTokens' => 150]);
    }

    /**
     * Analyze content for SEO improvements
     */
    public function analyzeSEO($title, $content, $metaDescription = '', $focusKeyword = '')
    {
        $prompt = "Analyze this content for SEO optimization:

Title: {$title}
Meta Description: {$metaDescription}
Focus Keyword: {$focusKeyword}
Content: " . substr(strip_tags($content), 0, 2000) . "...

Provide analysis and recommendations for:
1. Keyword density and placement
2. Title optimization
3. Meta description effectiveness
4. Content structure and readability
5. Missing SEO elements
6. Overall SEO score (1-100)

Format as JSON with sections: score, title_analysis, meta_analysis, keyword_analysis, content_analysis, recommendations.";

        return $this->generateContent($prompt, ['temperature' => 0.3, 'maxOutputTokens' => 1000]);
    }

    /**
     * Test API connection
     */
    public function testConnection()
    {
        try {
            $result = $this->generateContent('Test connection. Respond with "Connection successful"', [
                'maxOutputTokens' => 50,
                'temperature' => 0.1
            ]);

            return [
                'success' => $result['success'],
                'message' => $result['success'] ? 'API connection successful' : $result['error'],
                'api_keys_count' => count($this->apiKeys)
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage(),
                'api_keys_count' => count($this->apiKeys)
            ];
        }
    }

    /**
     * Generate case study content based on client information
     */
    public function generateCaseStudyContent($clientName, $industry, $challenge, $solution, $results = [])
    {
        $resultsText = !empty($results) ? ' Results achieved: ' . json_encode($results) : '';

        $prompt = "Generate comprehensive case study content for:

Client: {$clientName}
Industry: {$industry}
Challenge: {$challenge}
Solution: {$solution}{$resultsText}

Generate content for these specific sections in JSON format:

{
  \"hero_title\": \"Compelling main title (60-80 characters)\",
  \"hero_subtitle\": \"Brief industry/category badge text (20-30 characters)\",
  \"hero_description\": \"2-3 sentence overview paragraph\",
  \"highlights\": [
    {
      \"title\": \"Client Profile section title\",
      \"content\": \"Detailed description of the client and their business\"
    },
    {
      \"title\": \"The Core Challenge section title\",
      \"content\": \"Detailed explanation of the main challenge\"
    },
    {
      \"title\": \"Previous Approach section title\",
      \"content\": \"What they tried before that didn't work\"
    },
    {
      \"title\": \"Our Solution section title\",
      \"content\": \"How we solved their problem\"
    },
    {
      \"title\": \"Methodology section title\",
      \"content\": \"Technical approach and implementation\"
    },
    {
      \"title\": \"Key Outcomes section title\",
      \"content\": \"Results and improvements achieved\"
    }
  ],
  \"challenge_title\": \"Main challenge section heading\",
  \"challenge_description\": \"Detailed challenge explanation (2-3 paragraphs)\",
  \"results_metrics\": {
    \"primary_metric\": \"300%\",
    \"secondary_metric\": \"45%\",
    \"tertiary_metric\": \"12.5%\"
  }
}

Make the content professional, specific, and results-focused. Use industry-appropriate terminology.";

        return $this->generateContent($prompt, ['temperature' => 0.7, 'maxOutputTokens' => 3000]);
    }

    /**
     * Generate specific case study section content
     */
    public function generateCaseStudySection($sectionType, $context)
    {
        $prompts = [
            'hero' => "Create a compelling hero section for this case study:
Context: {$context}

Generate:
- Hero title (60-80 characters, compelling and specific)
- Hero subtitle/badge (20-30 characters)
- Hero description (2-3 sentences, overview of success)

Format as JSON: {\"title\": \"\", \"subtitle\": \"\", \"description\": \"\"}",

            'highlight' => "Create a project highlight section:
Context: {$context}

Generate:
- Section title (concise, descriptive)
- Content (2-3 sentences, specific details)

Format as JSON: {\"title\": \"\", \"content\": \"\"}",

            'challenge' => "Create a challenge section:
Context: {$context}

Generate:
- Challenge title (compelling heading)
- Challenge description (2-3 paragraphs explaining the problem)

Format as JSON: {\"title\": \"\", \"description\": \"\"}",

            'solution' => "Create a solution section:
Context: {$context}

Generate:
- Solution title (e.g., 'ADZETA'S SOLUTION')
- Solution subtitle (compelling approach description)
- Solution description (overview paragraph)
- 4 solution points with titles and descriptions

Format as JSON: {\"title\": \"\", \"subtitle\": \"\", \"description\": \"\", \"points\": [{\"title\": \"\", \"description\": \"\"}, ...]}",

            'results' => "Generate results metrics:
Context: {$context}

Create 4 specific metrics matching the template structure:
1. Ad spend increase metric
2. Client retention metric
3. LTV increase metric
4. ROI metric

Format as JSON: {\"metric_1\": {\"value\": \"64%\", \"title\": \"Increase in Ad Spend\", \"description\": \"While maintaining profitability\"}, \"metric_2\": {\"value\": \"56%\", \"title\": \"Boost in Client Retention\", \"description\": \"Across Premium Packages\"}, \"metric_3\": {\"value\": \"47%\", \"title\": \"Increase in LTV\", \"description\": \"Per Acquired Client\"}, \"metric_4\": {\"value\": \"3.2X\", \"title\": \"Return on Investment\", \"description\": \"ROI on Ad Spend\"}, \"section\": {\"title\": \"MEASURABLE IMPACT\", \"subtitle\": \"Superior Quality & Maximized ROI\", \"description\": \"Impact description...\"}}",

            'testimonial' => "Create a client testimonial:
Context: {$context}

Generate:
- Compelling testimonial quote (2-3 sentences from client perspective)
- Author name (realistic professional name)
- Author title (appropriate for industry)
- Company name (use context client name)

Format as JSON: {\"quote\": \"\", \"author_name\": \"\", \"author_title\": \"\", \"company\": \"\"}",

            'cta' => "Create a call-to-action section:
Context: {$context}

Generate:
- CTA title (compelling question or statement)
- CTA description (persuasive paragraph encouraging action)
- Button text (action-oriented, 2-3 words)
- Button URL (appropriate landing page)

Format as JSON: {\"title\": \"\", \"description\": \"\", \"button_text\": \"\", \"button_url\": \"\"}"
        ];

        $prompt = $prompts[$sectionType] ?? $prompts['highlight'];

        return $this->generateContent($prompt, ['temperature' => 0.6, 'maxOutputTokens' => 500]);
    }

    /**
     * Generate case study title suggestions
     */
    public function generateCaseStudyTitles($clientName, $industry, $results, $count = 5)
    {
        $prompt = "Generate {$count} compelling case study titles for:

Client: {$clientName}
Industry: {$industry}
Key Results: {$results}

Requirements:
- Include client name
- Highlight main achievement/result
- 60-80 characters long
- Professional and specific
- Results-focused

Format as numbered list:
1. [Title here]
2. [Title here]";

        return $this->generateContent($prompt, ['temperature' => 0.8, 'maxOutputTokens' => 300]);
    }

    /**
     * Generate case study meta description
     */
    public function generateCaseStudyMetaDescription($title, $clientName, $results)
    {
        $prompt = "Create an SEO-optimized meta description for this case study:

Title: {$title}
Client: {$clientName}
Results: {$results}

Requirements:
- 150-160 characters maximum
- Include client name and key results
- Compelling and click-worthy
- Include relevant keywords
- Professional tone

Return only the meta description text, no additional formatting.";

        return $this->generateContent($prompt, ['temperature' => 0.6, 'maxOutputTokens' => 200]);
    }

    /**
     * Get API usage statistics
     */
    public function getUsageStats()
    {
        // This would typically require additional API calls to get usage data
        // For now, return basic info
        return [
            'active_keys' => count($this->apiKeys),
            'current_key_index' => $this->currentKeyIndex,
            'model' => $this->model
        ];
    }
}
