<?php
/**
 * Localhost Development Configuration
 * Quick setup for Windows localhost development
 */

if (!defined('ADZETA_INIT')) {
    die('Direct access not permitted');
}

// =============================================================================
// LOCALHOST CONFIGURATION OPTIONS
// =============================================================================

// Option 1: Standard XAMPP/WAMP setup (most common)
// Uncomment this if your project is in: C:\xampp\htdocs\adzeta\
define('LOCALHOST_BASE_URL', 'http://localhost/adzeta/');

// Option 2: Custom port (if using different port)
// define('LOCALHOST_BASE_URL', 'http://localhost:8080/adzeta/');

// Option 3: IP address instead of localhost
// define('LOCALHOST_BASE_URL', 'http://127.0.0.1/adzeta/');

// Option 4: Different folder name
// define('LOCALHOST_BASE_URL', 'http://localhost/your-folder-name/');

// =============================================================================
// DEVELOPMENT SETTINGS
// =============================================================================

// Force localhost settings
if (!defined('BASE_URL')) {
    define('BASE_URL', LOCALHOST_BASE_URL);
}

// Override site URL for localhost
if (defined('SITE_URL')) {
    // Remove the constant and redefine it
    $siteUrl = str_replace('https://designgear.in/adzeta', 'http://localhost/adzeta', SITE_URL);
} else {
    $siteUrl = 'http://localhost/adzeta';
}

// Development-specific settings
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Disable caching for development
if (defined('CACHE_ENABLED')) {
    define('DEV_CACHE_ENABLED', false);
} else {
    define('CACHE_ENABLED', false);
}

// =============================================================================
// HELPER FUNCTIONS FOR LOCALHOST
// =============================================================================

/**
 * Get localhost-friendly asset URL
 */
function localhost_asset($path) {
    return LOCALHOST_BASE_URL . ltrim($path, '/');
}

/**
 * Get localhost-friendly page URL
 */
function localhost_url($path = '') {
    return LOCALHOST_BASE_URL . ltrim($path, '/');
}

/**
 * Check if running on localhost
 */
function is_localhost() {
    $host = $_SERVER['HTTP_HOST'] ?? '';
    return in_array($host, ['localhost', '127.0.0.1', '::1']) || 
           strpos($host, 'localhost:') === 0 || 
           strpos($host, '127.0.0.1:') === 0;
}

// =============================================================================
// AUTO-DETECTION FALLBACK
// =============================================================================

// If BASE_URL is still not defined, try auto-detection
if (!defined('BASE_URL')) {
    $protocol = 'http://'; // Force HTTP for localhost
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
    
    // Extract directory from script name
    $directory = dirname($scriptName);
    if ($directory === '/' || $directory === '\\') {
        $directory = '';
    }
    
    define('BASE_URL', $protocol . $host . $directory . '/');
}

// =============================================================================
// DEBUGGING INFORMATION
// =============================================================================

// Show configuration info in development mode
if (ENVIRONMENT === 'development' && isset($_GET['debug_config'])) {
    echo "<div style='background: #f0f0f0; padding: 20px; margin: 20px; border: 1px solid #ccc;'>";
    echo "<h3>Localhost Configuration Debug</h3>";
    echo "<p><strong>BASE_URL:</strong> " . (defined('BASE_URL') ? BASE_URL : 'Not defined') . "</p>";
    echo "<p><strong>SITE_URL:</strong> " . (defined('SITE_URL') ? SITE_URL : 'Not defined') . "</p>";
    echo "<p><strong>HTTP_HOST:</strong> " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "</p>";
    echo "<p><strong>SCRIPT_NAME:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";
    echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
    echo "<p><strong>DOCUMENT_ROOT:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</p>";
    echo "<p><strong>Is Localhost:</strong> " . (is_localhost() ? 'Yes' : 'No') . "</p>";
    echo "<p><em>Add ?debug_config=1 to any URL to see this info</em></p>";
    echo "</div>";
}
