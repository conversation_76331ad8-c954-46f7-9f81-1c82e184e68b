<?php
/**
 * AdZeta Website Bootstrap File
 * Initializes the application and handles routing
 */

// Define initialization constant
define('ADZETA_INIT', true);

// Start session
session_start();

// Load configuration
require_once __DIR__ . '/config/config.php';

// Load localhost configuration if running on localhost
if (isset($_SERVER['HTTP_HOST']) &&
    (in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', '::1']) ||
     strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0 ||
     strpos($_SERVER['HTTP_HOST'], '127.0.0.1:') === 0)) {
    require_once __DIR__ . '/config/localhost.php';
}

// Load static page configurations
require_once __DIR__ . '/config/pages.php';

// Autoloader for classes
spl_autoload_register(function ($class) {
    $file = INCLUDES_PATH . '/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// Error handler
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }

    $errorMsg = "Error: $message in $file on line $line";

    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>$errorMsg</div>";
    } else {
        error_log($errorMsg);
    }

    return true;
});

// Exception handler
set_exception_handler(function($exception) {
    $errorMsg = "Uncaught exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine();

    if (ENVIRONMENT === 'development') {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
        echo "<h3>Uncaught Exception</h3>";
        echo "<p><strong>Message:</strong> " . $exception->getMessage() . "</p>";
        echo "<p><strong>File:</strong> " . $exception->getFile() . "</p>";
        echo "<p><strong>Line:</strong> " . $exception->getLine() . "</p>";
        echo "<pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        error_log($errorMsg);
        echo "An error occurred. Please try again later.";
    }
});

// Helper functions
function asset($path) {
    return BASE_URL . ltrim($path, '/');
}

function url($path = '') {
    return BASE_URL . ltrim($path, '/');
}

function redirect($url, $code = 302) {
    header("Location: $url", true, $code);
    exit;
}

function old($key, $default = '') {
    return $_SESSION['old_input'][$key] ?? $default;
}

function csrf_token() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function csrf_field() {
    return '<input type="hidden" name="csrf_token" value="' . csrf_token() . '">';
}

function verify_csrf($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function sanitize($input) {
    if (is_array($input)) {
        return array_map('sanitize', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function formatDate($date, $format = 'M j, Y') {
    return date($format, strtotime($date));
}

function truncate($text, $length = 150, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

// Router disabled for now - using existing PHP files
// Initialize router if this is a web request
// if (php_sapi_name() !== 'cli') {
//     try {
//         $router = new Router();
//         $router->dispatch();
//     } catch (Exception $e) {
//         if (ENVIRONMENT === 'development') {
//             echo "<div style='background: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
//             echo "<h2>Application Error</h2>";
//             echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
//             echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
//             echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
//             echo "</div>";
//         } else {
//             error_log("Application error: " . $e->getMessage());
//             echo "An error occurred. Please try again later.";
//         }
//     }
// }
