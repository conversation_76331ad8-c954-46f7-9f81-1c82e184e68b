/**
 * Professional Enterprise Metrics - Consolidated CSS
 * Combines styles from clean-card-metrics.css and integrated-metrics.css
 */

/* Metrics section container */
.metrics-section {
    padding: 80px 0;
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
}

/* Clean metric card styles */
.metric-card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    padding: 30px;
    height: 100%;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    opacity: 0;
    transform: translateY(20px);
}

.metric-card.animate-in {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.metric-card:hover, .metric-card.hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
}

/* Card header */
.metric-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.metric-title {
    font-size: 14px;
    font-weight: 600;
    color: #555;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-badge {
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
}

.metric-badge i {
    margin-right: 4px;
    font-size: 10px;
}

/* Metric value styling */
.metric-value-container {
    margin-bottom: 20px;
}

.metric-value {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.counter-number {
    font-size: 48px;
    line-height: 1;
    font-weight: 700;
    background: linear-gradient(135deg, #8f76f5, #e958a1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 1;
}

.trend {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.trend i {
    margin-right: 3px;
}

.trend-up {
    color: #2ecc71;
}

.trend-down {
    color: #3498db;
}

.metric-description {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 0;
}

/* Mini chart styling */
.mini-chart {
    height: 60px;
    position: relative;
    margin-top: 20px;
    border-top: 1px solid rgba(0,0,0,0.05);
    padding-top: 15px;
}

/* SVG-based charts */
.metric-graph {
    position: relative;
    height: 60px;
    margin-top: auto;
    border-top: 1px solid rgba(0,0,0,0.05);
    padding-top: 15px;
    opacity: 0;
    transform: scaleX(0.9);
    transform-origin: left;
}

.metric-graph.animate-in {
    opacity: 1;
    transform: scaleX(1);
    transition: opacity 0.8s ease, transform 0.8s ease;
    transition-delay: 0.3s;
}

.metric-graph svg {
    width: 100%;
    height: 100%;
}

.metric-graph .area {
    fill: rgba(143, 118, 245, 0.1);
}

.metric-graph .line {
    stroke: #8f76f5;
    stroke-width: 2;
    fill: none;
}

/* Color variations for each card */
.row > div:nth-child(1) .metric-card {
    border-left-color: #8f76f5;
}

.row > div:nth-child(2) .metric-card {
    border-left-color: #e958a1;
}

.row > div:nth-child(3) .metric-card {
    border-left-color: #8f76f5;
}

.row > div:nth-child(4) .metric-card {
    border-left-color: #e958a1;
}

.row > div:nth-child(1) .metric-graph .line {
    stroke: #8f76f5;
}

.row > div:nth-child(2) .metric-graph .line {
    stroke: #e958a1;
}

.row > div:nth-child(3) .metric-graph .line {
    stroke: #8f76f5;
}

.row > div:nth-child(4) .metric-graph .line {
    stroke: #e958a1;
}

.row > div:nth-child(1) .metric-graph .area {
    fill: rgba(143, 118, 245, 0.1);
}

.row > div:nth-child(2) .metric-graph .area {
    fill: rgba(233, 88, 161, 0.1);
}

.row > div:nth-child(3) .metric-graph .area {
    fill: rgba(143, 118, 245, 0.1);
}

.row > div:nth-child(4) .metric-graph .area {
    fill: rgba(233, 88, 161, 0.1);
}

.row > div:nth-child(1) .metric-badge {
    background-color: rgba(143, 118, 245, 0.1);
    color: #8f76f5;
}

.row > div:nth-child(2) .metric-badge {
    background-color: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.row > div:nth-child(3) .metric-badge {
    background-color: rgba(143, 118, 245, 0.1);
    color: #8f76f5;
}

.row > div:nth-child(4) .metric-badge {
    background-color: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

/* Metrics footer */
.metrics-footer {
    text-align: center;
    color: #666;
    font-size: 13px;
    margin-top: 40px;
    padding-top: 15px;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.metrics-footer a {
    color: #8f76f5;
    text-decoration: none;
    font-weight: 500;
}

.metrics-footer a:hover {
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .metrics-section {
        padding: 60px 0;
    }
    
    .counter-number {
        font-size: 42px;
    }
    
    .metric-card {
        margin-bottom: 25px;
    }
}

@media (max-width: 767px) {
    .metrics-section {
        padding: 40px 0;
    }
    
    .counter-number {
        font-size: 36px;
    }
    
    .metric-graph, .mini-chart {
        height: 50px;
    }
    
    .metric-card {
        padding: 25px;
    }
}
