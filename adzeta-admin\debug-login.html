<!DOCTYPE html>
<html>
<head>
    <title>Debug Login Issue</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        button { padding: 12px 24px; margin: 8px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔍 Debug Login Issue</h1>
    
    <div class="info section">
        <h3>Current Error</h3>
        <p><strong>Error:</strong> <code>JSON.parse: unexpected character at line 1 column 1 of the JSON data</code></p>
        <p><strong>Location:</strong> auth.js:70:21</p>
        <p><strong>Cause:</strong> Authentication API returning HTML/error instead of JSON</p>
    </div>

    <div id="api-test" class="section warning">
        <h3>Test Authentication API</h3>
        <div>
            <input type="text" id="username" placeholder="Username" value="admin">
            <input type="password" id="password" placeholder="Password" value="admin123">
            <button onclick="testLogin()">Test Login API</button>
        </div>
        <div id="login-result">Click button to test login API...</div>
    </div>

    <div id="api-check" class="section warning">
        <h3>Check API Endpoints</h3>
        <button onclick="checkEndpoints()">Check API Endpoints</button>
        <div id="endpoint-result">Click button to check API endpoints...</div>
    </div>

    <div id="bootstrap-check" class="section warning">
        <h3>Check Bootstrap File</h3>
        <button onclick="checkBootstrap()">Check Bootstrap</button>
        <div id="bootstrap-result">Click button to check bootstrap file...</div>
    </div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            const sectionDiv = document.getElementById('api-test');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            resultDiv.innerHTML = '<p>Testing login API...</p>';
            
            try {
                const response = await fetch('/adzeta-admin/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                console.log('Response Status:', response.status);
                console.log('Response Headers:', response.headers);
                
                const responseText = await response.text();
                console.log('Raw Response:', responseText);
                
                try {
                    const data = JSON.parse(responseText);
                    sectionDiv.className = 'section success';
                    resultDiv.innerHTML = `
                        <h4>✅ Login API Working</h4>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Response Type:</strong> Valid JSON</p>
                        <h5>Response Data:</h5>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } catch (jsonError) {
                    sectionDiv.className = 'section error';
                    resultDiv.innerHTML = `
                        <h4>❌ Login API JSON Parse Error</h4>
                        <p><strong>HTTP Status:</strong> ${response.status}</p>
                        <p><strong>JSON Error:</strong> ${jsonError.message}</p>
                        <p><strong>Raw Response (first 1000 chars):</strong></p>
                        <pre>${responseText.substring(0, 1000)}</pre>
                    `;
                }
                
            } catch (error) {
                sectionDiv.className = 'section error';
                resultDiv.innerHTML = `
                    <h4>❌ Login API Request Failed</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        async function checkEndpoints() {
            const resultDiv = document.getElementById('endpoint-result');
            const sectionDiv = document.getElementById('api-check');
            
            resultDiv.innerHTML = '<p>Checking API endpoints...</p>';
            
            const endpoints = [
                '/adzeta-admin/api/',
                '/adzeta-admin/api/auth/login',
                '/adzeta-admin/bootstrap.php',
                '/adzeta-admin/config/environment.php'
            ];
            
            let html = '<h4>📋 API Endpoint Check Results</h4>';
            html += '<table style="width: 100%; border-collapse: collapse;">';
            html += '<tr style="background: #f8f9fa;"><th style="padding: 8px; border: 1px solid #ddd;">Endpoint</th><th style="padding: 8px; border: 1px solid #ddd;">Status</th><th style="padding: 8px; border: 1px solid #ddd;">Response</th></tr>';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    const responseText = await response.text();
                    const status = response.ok ? '✅ OK' : `❌ ${response.status}`;
                    const preview = responseText.substring(0, 100) + (responseText.length > 100 ? '...' : '');
                    
                    html += `<tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">${endpoint}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">${status}</td>
                        <td style="padding: 8px; border: 1px solid #ddd; font-size: 11px;">${preview}</td>
                    </tr>`;
                } catch (error) {
                    html += `<tr>
                        <td style="padding: 8px; border: 1px solid #ddd;">${endpoint}</td>
                        <td style="padding: 8px; border: 1px solid #ddd;">❌ Error</td>
                        <td style="padding: 8px; border: 1px solid #ddd; font-size: 11px;">${error.message}</td>
                    </tr>`;
                }
            }
            
            html += '</table>';
            
            sectionDiv.className = 'section info';
            resultDiv.innerHTML = html;
        }

        async function checkBootstrap() {
            const resultDiv = document.getElementById('bootstrap-result');
            const sectionDiv = document.getElementById('bootstrap-check');
            
            resultDiv.innerHTML = '<p>Checking bootstrap file...</p>';
            
            try {
                const response = await fetch('/adzeta-admin/bootstrap.php');
                const responseText = await response.text();
                
                if (response.ok) {
                    sectionDiv.className = 'section success';
                    resultDiv.innerHTML = `
                        <h4>✅ Bootstrap File Accessible</h4>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Response Preview:</strong></p>
                        <pre>${responseText.substring(0, 500)}${responseText.length > 500 ? '...' : ''}</pre>
                    `;
                } else {
                    sectionDiv.className = 'section error';
                    resultDiv.innerHTML = `
                        <h4>❌ Bootstrap File Error</h4>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Response:</strong></p>
                        <pre>${responseText}</pre>
                    `;
                }
            } catch (error) {
                sectionDiv.className = 'section error';
                resultDiv.innerHTML = `
                    <h4>❌ Bootstrap Check Failed</h4>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }

        // Auto-run endpoint check on page load
        window.addEventListener('load', function() {
            checkEndpoints();
        });
    </script>
</body>
</html>
