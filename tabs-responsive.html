<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Tabs to Accordion</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        :root {
            --base-color: #2946f3;
            --solitude-blue: #f8f9ff;
            --extra-medium-gray: #e5e5e5;
            --dark-gray: #232323;
            --very-light-gray: #f7f7f7;
            --transparent-dark-very-light: rgba(35, 35, 35, 0.1);
        }

        .bg-solitude-blue { background-color: var(--solitude-blue); }
        .bg-base-color { background-color: var(--base-color); }
        .border-color-extra-medium-gray { border-color: var(--extra-medium-gray); }
        .text-dark-gray { color: var(--dark-gray); }
        .bg-very-light-gray { background-color: var(--very-light-gray); }
        .border-color-transparent-dark-very-light { border-color: var(--transparent-dark-very-light); }

        .box-shadow-quadruple-large {
            box-shadow: 0 5px 30px rgba(0,0,0,0.1);
        }

        .box-shadow-large {
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        .fw-500 { font-weight: 500; }
        .fw-600 { font-weight: 600; }
        .fw-700 { font-weight: 700; }
        .fs-19 { font-size: 19px; }
        .fs-20 { font-size: 20px; }
        .ls-minus-05px { letter-spacing: -0.5px; }
        .ls-minus-1px { letter-spacing: -1px; }
        .ls-minus-2px { letter-spacing: -2px; }
        .lh-22 { line-height: 22px; }
        .border-radius-4px { border-radius: 4px; }
        .border-radius-6px { border-radius: 6px; }
        .w-250px { width: 250px; }
        .w-50px { width: 50px; }

        /* Tab styles for desktop */
        .tab-style-08 .nav-tabs {
            border: none;
        }

        .tab-style-08 .nav-link {
            position: relative;
            border: none;
            background: none;
            color: var(--dark-gray);
            padding: 20px 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .tab-style-08 .nav-link:hover,
        .tab-style-08 .nav-link.active {
            color: var(--base-color);
            background: none;
            border: none;
        }

        .tab-border {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 3px;
            transition: width 0.3s ease;
        }

        .tab-style-08 .nav-link.active .tab-border {
            width: 100%;
        }

        .separator-line-1px {
            height: 1px;
        }

        /* Accordion styles for mobile */
        .accordion-style .accordion-item {
            border: 1px solid var(--extra-medium-gray);
            margin-bottom: 10px;
            border-radius: 8px;
            overflow: hidden;
        }

        .accordion-style .accordion-header button {
            background: white;
            border: none;
            color: var(--dark-gray);
            font-weight: 500;
            font-size: 18px;
            padding: 20px;
            position: relative;
        }

        .accordion-style .accordion-header button:not(.collapsed) {
            color: var(--base-color);
            background: white;
            box-shadow: none;
        }

        .accordion-style .accordion-body {
            padding: 0;
            border-top: 1px solid var(--extra-medium-gray);
        }

        .vertical-counter {
            font-size: 48px;
        }

        /* Button styles */
        .btn-rounded {
            border-radius: 50px;
        }

        .with-rounded span {
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
        }

        .btn-white {
            background: white;
            color: var(--dark-gray);
            border: 1px solid var(--extra-medium-gray);
        }

        .btn-box-shadow {
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }

        /* Content item styling */
        .content-item {
            padding: 30px 0;
        }

        /* Responsive behavior */
        @media (min-width: 992px) {
            /* Desktop: Show tabs, hide accordion headers */
            .tab-navigation {
                display: block;
            }
            
            .accordion-style .accordion-header {
                display: none;
            }
            
            .accordion-style .accordion-collapse {
                display: block !important;
            }
            
            .content-item:not(.active) {
                display: none;
            }
            
            .content-item.active {
                display: block;
            }
        }

        @media (max-width: 991.98px) {
            /* Mobile: Hide tabs, show accordion */
            .tab-navigation {
                display: none;
            }
            
            .accordion-style .accordion-header {
                display: block;
            }
            
            .content-item {
                display: block;
            }

            .vertical-counter {
                font-size: 36px;
            }

            .w-250px {
                width: 200px;
            }

            /* Stack content vertically on mobile */
            .content-item .row.g-lg-0 > div {
                margin-bottom: 20px;
            }
            
            .content-item .row.g-lg-0 > div:last-child {
                margin-bottom: 0;
            }
        }

        @media (max-width: 575.98px) {
            .tab-style-08 .nav-link {
                padding: 15px 20px;
                font-size: 16px;
            }

            .vertical-counter {
                font-size: 28px;
            }

            .w-250px {
                width: 180px;
            }

            .fs-20 {
                font-size: 18px;
            }
        }

        /* Demo images placeholder */
        .demo-img {
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <section class="bg-solitude-blue pt-0">
        <!-- Tab Navigation (Desktop only) -->
        <div class="tab-navigation">
            <div class="tab-style-08 border-bottom border-color-extra-medium-gray bg-white box-shadow-quadruple-large">
                <div class="container">
                    <ul class="nav nav-tabs border-0 fw-500 fs-19 text-center">
                        <li class="nav-item">
                            <button class="nav-link active" data-target="strategic-planning">
                                Strategic planning
                                <span class="tab-border bg-base-color"></span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" data-target="audit-assurance">
                                Audit assurance
                                <span class="tab-border bg-base-color"></span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" data-target="financial-projections">
                                Financial projections
                                <span class="tab-border bg-base-color"></span>
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" data-target="business-planning">
                                Business planning
                                <span class="tab-border bg-base-color"></span>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content Container -->
        <div class="container">
            <div class="accordion accordion-style mt-4" id="servicesAccordion">
                
                <!-- Strategic Planning -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-strategic-planning" aria-expanded="true" aria-controls="collapse-strategic-planning">
                            Strategic planning
                        </button>
                    </h2>
                    <div id="collapse-strategic-planning" class="accordion-collapse collapse show" data-bs-parent="#servicesAccordion">
                        <div class="accordion-body">
                            <div class="content-item active" id="strategic-planning">
                                <div class="row align-items-center justify-content-center g-lg-0">
                                    <div class="col-md-6 mb-4 mb-md-0 position-relative overflow-hidden">
                                        <div class="demo-img border-radius-6px">Strategic Planning Image</div>
                                        <div class="bg-very-light-gray w-250px position-absolute pt-3 pb-3 ps-4 pe-4 border-radius-4px box-shadow-large d-flex align-items-center" style="bottom: 30px; left: 30px;">
                                            <h2 class="vertical-counter d-inline-flex text-dark-gray fw-700 ls-minus-2px mb-0 text-nowrap border-end border-1 border-color-transparent-dark-very-light pe-3 me-3">85</h2>
                                            <span class="text-dark-gray ls-minus-05px d-inline-block lh-22">Project completed</span>
                                        </div>
                                    </div>
                                    <div class="col-xl-4 col-lg-5 offset-lg-1 col-md-6 text-center text-md-start">
                                        <div class="mb-3">
                                            <div class="separator-line-1px w-50px bg-base-color d-inline-block align-middle me-2"></div>
                                            <span class="d-inline-block text-dark-gray align-middle fw-500 fs-20 ls-minus-05px">Strategic planning</span>
                                        </div>
                                        <h4 class="fw-600 text-dark-gray ls-minus-1px mb-3">Organization process of defining strategy.</h4>
                                        <p class="mb-4">We provide simplified accounting solutions and qualitative business process services to the customers which helps streamline your business and give your company a competitive.</p> 
                                        <a href="#" class="btn btn-large btn-rounded with-rounded btn-white btn-box-shadow fw-600">Learn more<span class="bg-base-color text-white"><i class="bi bi-arrow-right-short"></i></span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audit Assurance -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-audit-assurance" aria-expanded="false" aria-controls="collapse-audit-assurance">
                            Audit assurance
                        </button>
                    </h2>
                    <div id="collapse-audit-assurance" class="accordion-collapse collapse" data-bs-parent="#servicesAccordion">
                        <div class="accordion-body">
                            <div class="content-item" id="audit-assurance">
                                <div class="row align-items-center justify-content-center g-lg-0">
                                    <div class="col-md-6 mb-4 mb-md-0 position-relative overflow-hidden">
                                        <div class="demo-img border-radius-4px">Audit Assurance Image</div>
                                        <div class="bg-very-light-gray w-250px position-absolute pt-3 pb-3 ps-4 pe-4 border-radius-4px box-shadow-large d-flex align-items-center" style="bottom: 30px; left: 30px;">
                                            <h2 class="vertical-counter d-inline-flex text-dark-gray fw-700 ls-minus-2px mb-0 text-nowrap border-end border-1 border-color-transparent-dark-very-light pe-3 me-3">80</h2>
                                            <span class="text-dark-gray ls-minus-05px d-inline-block lh-22">Project completed</span>
                                        </div>
                                    </div>
                                    <div class="col-xl-4 col-lg-5 offset-lg-1 col-md-6 text-center text-md-start">
                                        <div class="mb-3">
                                            <div class="separator-line-1px w-50px bg-base-color d-inline-block align-middle me-2"></div>
                                            <span class="d-inline-block text-dark-gray align-middle fw-500 fs-20 ls-minus-05px">Audit assurance</span>
                                        </div>
                                        <h4 class="fw-600 text-dark-gray ls-minus-1px mb-3">An excellent audit service for company.</h4>
                                        <p class="mb-4">We provide simplified accounting solutions and qualitative business process services to the customers which helps streamline your business and give your company a competitive.</p> 
                                        <a href="#" class="btn btn-large btn-rounded with-rounded btn-white btn-box-shadow fw-600">Learn more<span class="bg-base-color text-white"><i class="bi bi-arrow-right-short"></i></span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Financial Projections -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-financial-projections" aria-expanded="false" aria-controls="collapse-financial-projections">
                            Financial projections
                        </button>
                    </h2>
                    <div id="collapse-financial-projections" class="accordion-collapse collapse" data-bs-parent="#servicesAccordion">
                        <div class="accordion-body">
                            <div class="content-item" id="financial-projections">
                                <div class="row align-items-center justify-content-center g-lg-0">
                                    <div class="col-md-6 mb-4 mb-md-0 position-relative overflow-hidden">
                                        <div class="demo-img border-radius-4px">Financial Projections Image</div>
                                        <div class="bg-very-light-gray w-250px position-absolute pt-3 pb-3 ps-4 pe-4 border-radius-4px box-shadow-large d-flex align-items-center" style="bottom: 30px; left: 30px;">
                                            <h2 class="vertical-counter d-inline-flex text-dark-gray fw-700 ls-minus-2px mb-0 text-nowrap border-end border-1 border-color-transparent-dark-very-light pe-3 me-3">85</h2>
                                            <span class="text-dark-gray ls-minus-05px d-inline-block lh-22">Project completed</span>
                                        </div>
                                    </div>
                                    <div class="col-xl-4 col-lg-5 offset-lg-1 col-md-6 text-center text-md-start">
                                        <div class="mb-3">
                                            <div class="separator-line-1px w-50px bg-base-color d-inline-block align-middle me-2"></div>
                                            <span class="d-inline-block text-dark-gray align-middle fw-500 fs-20 ls-minus-05px">Financial projections</span>
                                        </div>
                                        <h4 class="fw-600 text-dark-gray ls-minus-1px mb-3">We are leader in tax advisor and financial.</h4>
                                        <p class="mb-4">We provide simplified accounting solutions and qualitative business process services to the customers which helps streamline your business and give your company a competitive.</p> 
                                        <a href="#" class="btn btn-large btn-rounded with-rounded btn-white btn-box-shadow fw-600">Learn more<span class="bg-base-color text-white"><i class="bi bi-arrow-right-short"></i></span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Business Planning -->
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-business-planning" aria-expanded="false" aria-controls="collapse-business-planning">
                            Business planning
                        </button>
                    </h2>
                    <div id="collapse-business-planning" class="accordion-collapse collapse" data-bs-parent="#servicesAccordion">
                        <div class="accordion-body">
                            <div class="content-item" id="business-planning">
                                <div class="row align-items-center justify-content-center g-lg-0">
                                    <div class="col-md-6 mb-4 mb-md-0 position-relative overflow-hidden">
                                        <div class="demo-img border-radius-4px">Business Planning Image</div>
                                        <div class="bg-very-light-gray w-250px position-absolute pt-3 pb-3 ps-4 pe-4 border-radius-4px box-shadow-large d-flex align-items-center" style="bottom: 30px; left: 30px;">
                                            <h2 class="vertical-counter d-inline-flex text-dark-gray fw-700 ls-minus-2px mb-0 text-nowrap border-end border-1 border-color-transparent-dark-very-light pe-3 me-3">80</h2>
                                            <span class="text-dark-gray ls-minus-05px d-inline-block lh-22">Project completed</span>
                                        </div>
                                    </div>
                                    <div class="col-xl-4 col-lg-5 offset-lg-1 col-md-6 text-center text-md-start">
                                        <div class="mb-3">
                                            <div class="separator-line-1px w-50px bg-base-color d-inline-block align-middle me-2"></div>
                                            <span class="d-inline-block text-dark-gray align-middle fw-500 fs-20 ls-minus-05px">Business planning</span>
                                        </div>
                                        <h4 class="fw-600 text-dark-gray ls-minus-1px mb-3">We creating specific business strategies.</h4>
                                        <p class="mb-4">We provide simplified accounting solutions and qualitative business process services to the customers which helps streamline your business and give your company a competitive.</p> 
                                        <a href="#" class="btn btn-large btn-rounded with-rounded btn-white btn-box-shadow fw-600">Learn more<span class="bg-base-color text-white"><i class="bi bi-arrow-right-short"></i></span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Tab functionality for desktop
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-navigation .nav-link');
            const contentItems = document.querySelectorAll('.content-item');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const target = this.getAttribute('data-target');
                    
                    // Remove active class from all tabs and content
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    contentItems.forEach(item => item.classList.remove('active'));
                    
                    // Add active class to clicked tab and corresponding content
                    this.classList.add('active');
                    document.getElementById(target).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>