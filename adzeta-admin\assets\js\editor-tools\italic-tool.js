/**
 * Italic Inline Tool for Editor.js
 * Based on official Editor.js documentation
 */

class ItalicTool {
    static get isInline() {
        return true;
    }

    static get sanitize() {
        return {
            i: {} // Allow <i> tags, clean all attributes
        };
    }

    get state() {
        return this._state;
    }

    set state(state) {
        this._state = state;
        this.button.classList.toggle(this.api.styles.inlineToolButtonActive, state);
    }

    constructor({ api }) {
        this.api = api;
        this.button = null;
        this._state = false;
        this.tag = 'I';
        this.class = null;
    }

    /**
     * Create button for inline toolbar
     */
    render() {
        this.button = document.createElement('button');
        this.button.type = 'button';
        this.button.innerHTML = '<i>I</i>';
        this.button.classList.add(this.api.styles.inlineToolButton);

        return this.button;
    }

    /**
     * Wrap/unwrap selected text with <i> tags
     */
    surround(range) {
        if (this.state) {
            this.unwrap(range);
            return;
        }
        this.wrap(range);
    }

    /**
     * Wrap selection with <i> tag
     */
    wrap(range) {
        const selectedText = range.extractContents();
        const italicElement = document.createElement(this.tag);

        italicElement.appendChild(selectedText);
        range.insertNode(italicElement);

        this.api.selection.expandToTag(italicElement);
    }

    /**
     * Unwrap <i> tag
     */
    unwrap(range) {
        const italicElement = this.api.selection.findParentTag(this.tag, this.class);
        const text = range.extractContents();

        italicElement.remove();
        range.insertNode(text);
    }

    /**
     * Check if text is already italic
     * @param {Selection} selection - current Selection
     */
    checkState(selection) {
        const italicElement = this.api.selection.findParentTag(this.tag);
        this.state = !!italicElement;
    }

    /**
     * Keyboard shortcut
     */
    static get shortcut() {
        return 'CMD+I';
    }
}

// Make available globally
window.ItalicTool = ItalicTool;
