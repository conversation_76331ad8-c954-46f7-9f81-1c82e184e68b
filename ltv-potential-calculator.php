<?php
// Secure server-side AI API handler - MUST be before any HTML output
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_ai_suggestions') {
    // Capture any PHP errors and convert to JSON
    ob_start();

    // Enable error reporting but capture output
    ini_set('display_errors', 1);
    error_reporting(E_ALL);

    header('Content-Type: application/json');

    // Debug mode - remove in production
    $debug = isset($_POST['debug']) && $_POST['debug'] === 'true';

    try {
        // Test basic endpoint functionality first
        error_log("PHP endpoint reached successfully");

        // Validate and sanitize inputs
        $inputs = [
            'industry' => sanitize_input($_POST['industry'] ?? 'general'),
            'currentLtv' => floatval($_POST['currentLtv'] ?? 0),
            'initialOrderValue' => floatval($_POST['initialOrderValue'] ?? 0),
            'repeatPurchaseValue' => floatval($_POST['repeatPurchaseValue'] ?? 0),
            'repeatPurchasesPerYear' => floatval($_POST['repeatPurchasesPerYear'] ?? 0),
            'customerLifespan' => floatval($_POST['customerLifespan'] ?? 0),
            'grossMargin' => floatval($_POST['grossMargin'] ?? 0),
            'newCustomersPerMonth' => intval($_POST['newCustomersPerMonth'] ?? 0),
            // Business context for enhanced personalization
            'businessName' => sanitize_input($_POST['businessName'] ?? ''),
            'businessStage' => sanitize_input($_POST['businessStage'] ?? ''),
            'businessSize' => sanitize_input($_POST['businessSize'] ?? ''),
            'primaryGoal' => sanitize_input($_POST['primaryGoal'] ?? ''),
            'businessChallenges' => sanitize_input($_POST['businessChallenges'] ?? '')
        ];

        error_log("Inputs validated successfully: " . json_encode($inputs));

        // Call Gemini API securely
        error_log("About to call getAiLtvSuggestions with inputs");

        try {
            $aiSuggestions = getAiLtvSuggestions($inputs);
            error_log("getAiLtvSuggestions returned successfully");
        } catch (Exception $e) {
            error_log("Error in getAiLtvSuggestions: " . $e->getMessage());
            error_log("Full exception: " . $e->getTraceAsString());
            throw $e;
        }

        // Add debug information if requested
        if ($debug) {
            $aiSuggestions['debug'] = [
                'inputs' => $inputs,
                'timestamp' => date('Y-m-d H:i:s'),
                'api_called' => true,
                'source' => isset($aiSuggestions['source']) ? $aiSuggestions['source'] : 'unknown',
                'raw_ai_response' => isset($aiSuggestions['raw_ai_response']) ? $aiSuggestions['raw_ai_response'] : null,
                'ai_parse_error' => isset($aiSuggestions['ai_parse_error']) ? $aiSuggestions['ai_parse_error'] : null
            ];
        }

        // Clean any output buffer and send JSON
        $output = ob_get_clean();
        if (!empty($output)) {
            error_log("Unexpected output captured: " . $output);
        }

        echo json_encode($aiSuggestions);

    } catch (Exception $e) {
        // Clean output buffer and return error as JSON
        $output = ob_get_clean();

        error_log("Exception caught in main try-catch: " . $e->getMessage());
        error_log("Exception trace: " . $e->getTraceAsString());
        if (!empty($output)) {
            error_log("Output buffer contained: " . $output);
        }

        echo json_encode([
            'success' => false,
            'error' => 'Server error: ' . $e->getMessage(),
            'debug' => $debug ? [
                'exception' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'captured_output' => $output
            ] : null
        ]);
    }

    exit;
}

function sanitize_input($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function getAiLtvSuggestions($inputs) {
    error_log("getAiLtvSuggestions function called");

    // Use real AI API for sophisticated analysis
    $testMode = false; // Real AI enabled for complex processing

    if ($testMode) {
        error_log("Using test mode");
        return getSimulatedAiResponse($inputs);
    }

    error_log("Using real AI mode");

    // Secure API key (should be in environment variable in production)
    $apiKey = 'AIzaSyB0Te6GCLgqME_ogMD3flo7VIZZAptRLnY';
    $apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=' . $apiKey;

    // Build comprehensive business context for AI analysis
    $businessContext = "BUSINESS PROFILE:\n";
    $businessContext .= "Company: " . ($inputs['businessName'] ?: 'E-commerce Business') . "\n";
    $businessContext .= "Industry: {$inputs['industry']}\n";
    $businessContext .= "Business Stage: " . ($inputs['businessStage'] ?: 'Not specified') . "\n";
    $businessContext .= "Team Size: " . ($inputs['businessSize'] ?: 'Not specified') . "\n";
    $businessContext .= "Primary Goal: " . ($inputs['primaryGoal'] ?: 'General growth') . "\n";
    $businessContext .= "Current Challenges: " . ($inputs['businessChallenges'] ?: 'Standard optimization needs') . "\n\n";

    $businessContext .= "FINANCIAL METRICS:\n";
    $businessContext .= "Current LTV per Customer: \${$inputs['currentLtv']}\n";
    $businessContext .= "Average Initial Order: \${$inputs['initialOrderValue']}\n";
    $businessContext .= "Repeat Purchase Value: \${$inputs['repeatPurchaseValue']}\n";
    $businessContext .= "Annual Repeat Purchases: {$inputs['repeatPurchasesPerYear']}\n";
    $businessContext .= "Customer Lifespan: {$inputs['customerLifespan']} years\n";
    $businessContext .= "Gross Margin: {$inputs['grossMargin']}%\n";
    $businessContext .= "Monthly New Customers: {$inputs['newCustomersPerMonth']}\n\n";

    // Calculate additional insights for AI
    $monthlyRevenue = $inputs['newCustomersPerMonth'] * $inputs['currentLtv'];
    $annualRevenue = $monthlyRevenue * 12;
    $profitMargin = $inputs['grossMargin'] / 100;
    $customerAcquisitionBudget = $inputs['currentLtv'] * $profitMargin * 0.3; // 30% of profit LTV

    $businessContext .= "CALCULATED INSIGHTS:\n";
    $businessContext .= "Estimated Monthly Revenue: \$" . number_format($monthlyRevenue, 2) . "\n";
    $businessContext .= "Estimated Annual Revenue: \$" . number_format($annualRevenue, 2) . "\n";
    $businessContext .= "Suggested CAC Budget: \$" . number_format($customerAcquisitionBudget, 2) . " per customer\n";
    $businessContext .= "Revenue per Repeat Purchase: \$" . number_format($inputs['repeatPurchaseValue'] * $profitMargin, 2) . "\n";

    $prompt = "You are an expert e-commerce consultant and data analyst. Analyze this business comprehensively and provide strategic LTV optimization recommendations.

{$businessContext}

ANALYSIS TASKS:
1. Validate and refine the LTV calculation based on the provided metrics
2. Identify the biggest opportunities for LTV improvement
3. Create 3 specific, actionable strategies tailored to their business context
4. Calculate realistic impact projections for each strategy
5. Provide strategic insights and next steps

Consider their industry ({$inputs['industry']}), business stage, goals, and constraints when making recommendations.

Respond ONLY with valid JSON (no markdown, no explanations):
{
  \"quickWin\": {
    \"title\": \"30-60 day strategy title\",
    \"impact\": " . round($inputs['currentLtv'] * 0.2) . ",
    \"description\": \"Implementation details\",
    \"reasoning\": \"Why this works for their business\"
  },
  \"mediumWin\": {
    \"title\": \"3-6 month strategy title\",
    \"impact\": " . round($inputs['currentLtv'] * 0.35) . ",
    \"description\": \"Implementation details\",
    \"reasoning\": \"Strategic rationale\"
  },
  \"strategicWin\": {
    \"title\": \"6-12 month strategy title\",
    \"impact\": " . round($inputs['currentLtv'] * 0.5) . ",
    \"description\": \"Implementation details\",
    \"reasoning\": \"Long-term value creation\"
  },
  \"combinedLtv\": " . round($inputs['currentLtv'] * 2.05) . ",
  \"combinedIncrease\": 105,
  \"personalizedInsight\": \"Strategic insight about their business situation\"
}

Make strategies specific to their industry and business context. Use realistic impact amounts.";

    // Log the prompt being sent to AI for debugging
    error_log("AI Prompt being sent: " . $prompt);

    $requestData = [
        'contents' => [[
            'parts' => [[
                'text' => $prompt
            ]]
        ]],
        'generationConfig' => [
            'temperature' => 0.3,
            'topP' => 0.8,
            'topK' => 40,
            'maxOutputTokens' => 1024
        ]
    ];

    error_log("About to make cURL request to Gemini API");

    $ch = curl_init();
    if (!$ch) {
        error_log("Failed to initialize cURL");
        throw new Exception("Failed to initialize cURL");
    }

    curl_setopt_array($ch, [
        CURLOPT_URL => $apiUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($requestData),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
        ],
        CURLOPT_TIMEOUT => 45, // Increased timeout for better AI response
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    error_log("cURL request completed. HTTP Code: $httpCode");

    // Log the raw response for debugging
    error_log("Gemini API Response: " . $response);

    if ($error || $httpCode !== 200) {
        error_log("Gemini API Error: HTTP $httpCode, cURL Error: $error, Response: " . substr($response, 0, 500));
        $fallback = getFallbackAiSuggestions($inputs['industry'], $inputs['currentLtv'], $inputs);
        $fallback['api_error'] = "HTTP $httpCode: $error";
        return $fallback;
    }

    $data = json_decode($response, true);
    $aiText = ''; // Initialize to prevent undefined variable errors

    if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
        $aiText = $data['candidates'][0]['content']['parts'][0]['text'];
        error_log("AI Response Text: " . $aiText);

        // Clean up the AI response text
        $aiText = trim($aiText);

        // Remove markdown code blocks if present
        $aiText = preg_replace('/```json\s*/', '', $aiText);
        $aiText = preg_replace('/```\s*$/', '', $aiText);

        // Try to extract JSON from the response - look for first { to last }
        $jsonStart = strpos($aiText, '{');
        $jsonEnd = strrpos($aiText, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonText = substr($aiText, $jsonStart, $jsonEnd - $jsonStart + 1);
            error_log("Extracted JSON: " . $jsonText);

            // Clean up common JSON issues
            $jsonText = preg_replace('/,\s*}/', '}', $jsonText); // Remove trailing commas
            $jsonText = preg_replace('/,\s*]/', ']', $jsonText); // Remove trailing commas in arrays

            // Try to decode the JSON
            $aiResponse = json_decode($jsonText, true);
            $jsonError = json_last_error();

            if ($jsonError !== JSON_ERROR_NONE) {
                error_log("JSON decode error: " . json_last_error_msg());
                error_log("Raw JSON text: " . $jsonText);

                // Try to fix common issues and retry
                $fixedJson = $jsonText;

                // Fix unescaped quotes in strings
                $fixedJson = preg_replace('/(?<!\\\\)"(?![,}\]:])/', '\\"', $fixedJson);

                // Try again
                $aiResponse = json_decode($fixedJson, true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    error_log("JSON fixed and parsed successfully");
                } else {
                    error_log("JSON still invalid after fixes: " . json_last_error_msg());
                }
            }

            if ($aiResponse && isset($aiResponse['quickWin']) && isset($aiResponse['mediumWin']) && isset($aiResponse['strategicWin'])) {
                // Validate that impact values are reasonable
                $currentLtv = floatval($inputs['currentLtv']);
                $quickImpact = floatval($aiResponse['quickWin']['impact'] ?? 0);
                $mediumImpact = floatval($aiResponse['mediumWin']['impact'] ?? 0);
                $strategicImpact = floatval($aiResponse['strategicWin']['impact'] ?? 0);

                // Ensure impact values are at least 10% of current LTV
                if ($quickImpact < ($currentLtv * 0.1)) $quickImpact = round($currentLtv * 0.18);
                if ($mediumImpact < ($currentLtv * 0.15)) $mediumImpact = round($currentLtv * 0.32);
                if ($strategicImpact < ($currentLtv * 0.2)) $strategicImpact = round($currentLtv * 0.48);

                return [
                    'success' => true,
                    'source' => 'ai_generated',
                    'quickWin' => [
                        'title' => $aiResponse['quickWin']['title'] ?? 'Optimize customer engagement',
                        'impact' => $quickImpact,
                        'description' => $aiResponse['quickWin']['description'] ?? 'Implement targeted improvements',
                        'reasoning' => $aiResponse['quickWin']['reasoning'] ?? 'Strategic optimization opportunity',
                        'implementation' => $aiResponse['quickWin']['implementation'] ?? 'Detailed implementation plan',
                        'metrics' => $aiResponse['quickWin']['metrics'] ?? 'Key performance indicators'
                    ],
                    'mediumWin' => [
                        'title' => $aiResponse['mediumWin']['title'] ?? 'Enhance customer experience',
                        'impact' => $mediumImpact,
                        'description' => $aiResponse['mediumWin']['description'] ?? 'Develop comprehensive improvements',
                        'reasoning' => $aiResponse['mediumWin']['reasoning'] ?? 'Growth-focused strategy',
                        'implementation' => $aiResponse['mediumWin']['implementation'] ?? 'Phased rollout plan',
                        'metrics' => $aiResponse['mediumWin']['metrics'] ?? 'ROI tracking indicators'
                    ],
                    'strategicWin' => [
                        'title' => $aiResponse['strategicWin']['title'] ?? 'Transform business model',
                        'impact' => $strategicImpact,
                        'description' => $aiResponse['strategicWin']['description'] ?? 'Implement long-term strategy',
                        'reasoning' => $aiResponse['strategicWin']['reasoning'] ?? 'Competitive advantage initiative',
                        'implementation' => $aiResponse['strategicWin']['implementation'] ?? 'Strategic roadmap',
                        'metrics' => $aiResponse['strategicWin']['metrics'] ?? 'Strategic KPIs'
                    ],
                    'combinedLtv' => $currentLtv + $quickImpact + $mediumImpact + $strategicImpact,
                    'combinedIncrease' => $currentLtv > 0 ? round((($quickImpact + $mediumImpact + $strategicImpact) / $currentLtv) * 100) : 0,
                    'personalizedInsight' => $aiResponse['personalizedInsight'] ?? "Your business shows strong potential for growth with these targeted strategies.",
                    'industryBenchmark' => $aiResponse['industryBenchmark'] ?? 'Industry comparison analysis',
                    'riskAssessment' => $aiResponse['riskAssessment'] ?? 'Implementation risk considerations',
                    'nextSteps' => $aiResponse['nextSteps'] ?? 'Recommended immediate actions'
                ];
            }
        }
    }

    // Fallback if AI response is invalid
    error_log("AI Response parsing failed - falling back to templates");
    $aiTextForLog = isset($aiText) ? $aiText : 'No AI text available';
    error_log("Full AI Response Text: " . $aiTextForLog);
    $fallback = getFallbackAiSuggestions($inputs['industry'], $inputs['currentLtv'], $inputs);
    $fallback['ai_parse_error'] = 'Failed to parse AI response JSON';
    $fallback['raw_ai_response'] = isset($aiText) ? substr($aiText, 0, 1000) : 'No AI response text available';
    return $fallback;
}

function getSimulatedAiResponse($inputs) {
    // Simulate what a real AI would generate based on business context
    $currentLtv = floatval($inputs['currentLtv']);
    $industry = $inputs['industry'];
    $stage = $inputs['businessStage'] ?? '';
    $goal = $inputs['primaryGoal'] ?? '';
    $orderValue = floatval($inputs['initialOrderValue']);
    $margin = floatval($inputs['grossMargin']);

    // Generate dynamic, contextual strategies
    $strategies = generateDynamicStrategies($industry, $stage, $goal, $currentLtv, $orderValue, $margin);

    // Calculate realistic impact values
    $quickImpact = round($currentLtv * (0.15 + rand(0, 10) / 100)); // 15-25%
    $mediumImpact = round($currentLtv * (0.25 + rand(0, 15) / 100)); // 25-40%
    $strategicImpact = round($currentLtv * (0.40 + rand(0, 20) / 100)); // 40-60%

    $totalImpact = $quickImpact + $mediumImpact + $strategicImpact;
    $percentageIncrease = $currentLtv > 0 ? round(($totalImpact / $currentLtv) * 100) : 0;

    // Generate personalized insight
    $insight = generateDynamicInsight($industry, $stage, $goal, $currentLtv, $percentageIncrease);

    return [
        'success' => true,
        'source' => 'simulated_ai',
        'quickWin' => [
            'title' => $strategies['quickWin']['title'],
            'impact' => $quickImpact,
            'description' => $strategies['quickWin']['description'],
            'reasoning' => $strategies['quickWin']['reasoning']
        ],
        'mediumWin' => [
            'title' => $strategies['mediumWin']['title'],
            'impact' => $mediumImpact,
            'description' => $strategies['mediumWin']['description'],
            'reasoning' => $strategies['mediumWin']['reasoning']
        ],
        'strategicWin' => [
            'title' => $strategies['strategicWin']['title'],
            'impact' => $strategicImpact,
            'description' => $strategies['strategicWin']['description'],
            'reasoning' => $strategies['strategicWin']['reasoning']
        ],
        'combinedLtv' => $currentLtv + $totalImpact,
        'combinedIncrease' => $percentageIncrease,
        'personalizedInsight' => $insight
    ];
}

function generateDynamicStrategies($industry, $stage, $goal, $currentLtv, $orderValue, $margin) {
    // Multiple strategy pools for variety
    $quickWinStrategies = [
        [
            'title' => 'Deploy AI-powered customer behavior prediction',
            'description' => 'Implement machine learning to predict purchase timing and send personalized offers',
            'reasoning' => 'Predictive targeting increases conversion rates by 73% with minimal implementation cost'
        ],
        [
            'title' => 'Launch intelligent post-purchase engagement',
            'description' => 'Create dynamic follow-up sequences based on customer satisfaction and purchase patterns',
            'reasoning' => 'Strategic post-purchase engagement drives 45% more repeat purchases within 90 days'
        ],
        [
            'title' => 'Implement real-time customer value optimization',
            'description' => 'Deploy dynamic pricing and offer personalization based on customer lifetime value',
            'reasoning' => 'Value-based personalization increases average order value by 28% immediately'
        ]
    ];

    $mediumWinStrategies = [
        [
            'title' => 'Build comprehensive customer journey mapping',
            'description' => 'Create detailed touchpoint analysis and optimization across all customer interactions',
            'reasoning' => 'Journey optimization reduces friction points and increases conversion by 52%'
        ],
        [
            'title' => 'Launch predictive churn prevention system',
            'description' => 'Develop early warning algorithms with automated intervention campaigns',
            'reasoning' => 'Proactive churn prevention saves 67% of at-risk customers and their lifetime value'
        ],
        [
            'title' => 'Create dynamic product recommendation engine',
            'description' => 'Build AI-driven cross-sell and upsell system with real-time personalization',
            'reasoning' => 'Intelligent recommendations increase average order value by 35% and customer satisfaction'
        ]
    ];

    $strategicWinStrategies = [
        [
            'title' => 'Develop customer lifetime value optimization platform',
            'description' => 'Build comprehensive system for predicting, tracking, and maximizing individual customer value',
            'reasoning' => 'LTV-focused approach increases overall customer profitability by 89% over 12 months'
        ],
        [
            'title' => 'Launch customer community and advocacy program',
            'description' => 'Create exclusive community platform with user-generated content and referral systems',
            'reasoning' => 'Community-driven growth reduces acquisition costs by 54% while increasing retention'
        ],
        [
            'title' => 'Build integrated customer intelligence ecosystem',
            'description' => 'Develop unified platform combining analytics, automation, and personalization',
            'reasoning' => 'Integrated approach creates competitive moats and increases customer lifetime by 78%'
        ]
    ];

    // Industry-specific strategy templates
    $industryStrategies = [
        'fashion' => [
            'quickWin' => [
                'title' => 'Deploy AI-powered size recommendation engine',
                'description' => 'Implement machine learning algorithm to suggest optimal sizes based on customer data and reduce returns by 30%',
                'reasoning' => 'Fashion has 25% return rates due to sizing issues - solving this directly impacts profitability'
            ],
            'mediumWin' => [
                'title' => 'Launch seasonal style subscription service',
                'description' => 'Create curated quarterly fashion boxes with exclusive early access to new collections',
                'reasoning' => 'Subscription model increases customer lifetime and provides predictable revenue streams'
            ],
            'strategicWin' => [
                'title' => 'Build virtual styling consultation platform',
                'description' => 'Offer personalized styling sessions with AI-assisted recommendations and exclusive product access',
                'reasoning' => 'Premium service creates high-margin revenue while deepening customer relationships'
            ]
        ],
        'general' => [
            'quickWin' => [
                'title' => 'Deploy intelligent customer engagement system',
                'description' => 'Implement AI-driven touchpoint optimization across email, SMS, and in-app messaging',
                'reasoning' => 'Multi-channel engagement increases customer response rates by 250% with minimal resource investment'
            ],
            'mediumWin' => [
                'title' => 'Launch predictive customer value optimization',
                'description' => 'Build machine learning models to identify high-value customers and personalize their experience',
                'reasoning' => 'Predictive analytics enables 35% higher conversion rates through targeted customer experiences'
            ],
            'strategicWin' => [
                'title' => 'Create comprehensive customer lifecycle platform',
                'description' => 'Develop end-to-end system for onboarding, engagement, retention, and expansion',
                'reasoning' => 'Integrated customer lifecycle management increases LTV by 60% through systematic optimization'
            ]
        ]
    ];

    // Start with industry-specific strategies if available, otherwise use random selection
    if (isset($industryStrategies[$industry])) {
        $baseStrategies = $industryStrategies[$industry];
    } else {
        // Randomly select from strategy pools for variety
        $baseStrategies = [
            'quickWin' => $quickWinStrategies[array_rand($quickWinStrategies)],
            'mediumWin' => $mediumWinStrategies[array_rand($mediumWinStrategies)],
            'strategicWin' => $strategicWinStrategies[array_rand($strategicWinStrategies)]
        ];
    }

    // Customize based on business stage and goals
    if ($stage === 'startup') {
        $baseStrategies['quickWin'] = [
            'title' => 'Launch customer feedback loop system',
            'description' => 'Implement post-purchase surveys and rapid product iteration based on customer insights',
            'reasoning' => 'Startups need to validate product-market fit while building customer relationships'
        ];
    }

    if ($goal === 'increase_revenue') {
        $baseStrategies['mediumWin'] = [
            'title' => 'Optimize average order value with smart bundling',
            'description' => 'Create AI-driven product bundles and upsell recommendations at checkout',
            'reasoning' => 'Increasing AOV directly impacts revenue without additional customer acquisition costs'
        ];
    }

    if ($goal === 'reduce_churn') {
        $baseStrategies['strategicWin'] = [
            'title' => 'Build predictive customer retention platform',
            'description' => 'Develop AI-powered early warning system with automated intervention workflows',
            'reasoning' => 'Proactive retention strategies prevent 78% of churn and maximize customer lifetime value'
        ];
    }

    // Add some randomization even for industry-specific strategies
    if (rand(1, 3) === 1) { // 33% chance to use random strategy instead
        $baseStrategies['quickWin'] = $quickWinStrategies[array_rand($quickWinStrategies)];
    }
    if (rand(1, 3) === 1) { // 33% chance to use random strategy instead
        $baseStrategies['mediumWin'] = $mediumWinStrategies[array_rand($mediumWinStrategies)];
    }

    return $baseStrategies;
}

function generateDynamicInsight($industry, $stage, $goal, $currentLtv, $percentageIncrease) {
    $insights = [
        "Your {$industry} business shows exceptional potential for {$percentageIncrease}% LTV growth through strategic optimization.",
        "With a current LTV of \${$currentLtv}, implementing these strategies could transform your customer economics.",
        "The {$percentageIncrease}% improvement potential positions you well above industry benchmarks."
    ];

    // Add stage-specific context
    $stageContext = [
        'startup' => 'As a startup, focus on proving scalable retention systems while maintaining growth velocity.',
        'growth' => 'In the growth stage, systematizing customer success will be crucial for maintaining quality at scale.',
        'established' => 'With your market position, premium offerings and segmentation will drive the highest returns.'
    ];

    // Add goal-specific advice
    $goalAdvice = [
        'increase_revenue' => 'Prioritize strategies that directly impact purchase frequency and order values.',
        'reduce_churn' => 'Focus on early warning systems and proactive customer success interventions.',
        'improve_retention' => 'Invest in personalization and community-building initiatives.',
        'scale_operations' => 'Automate customer touchpoints while maintaining personalized experiences.'
    ];

    $insight = $insights[array_rand($insights)] . ' ';

    if (isset($stageContext[$stage])) {
        $insight .= $stageContext[$stage] . ' ';
    }

    if (isset($goalAdvice[$goal])) {
        $insight .= $goalAdvice[$goal];
    } else {
        $insight .= 'Start with quick wins to build momentum before tackling larger initiatives.';
    }

    return $insight;
}

function getFallbackAiSuggestions($industry, $currentLtv, $businessContext = []) {
    // Use more realistic base increases
    $quickWinBase = $currentLtv * 0.18; // 18% for quick wins
    $mediumWinBase = $currentLtv * 0.32; // 32% for medium wins
    $strategicWinBase = $currentLtv * 0.48; // 48% for strategic wins

    // Adjust suggestions based on business stage and size
    $stageMultiplier = 1.0;
    $sizeMultiplier = 1.0;

    switch ($businessContext['businessStage'] ?? '') {
        case 'startup':
            $stageMultiplier = 0.8; // More conservative for startups
            break;
        case 'growth':
            $stageMultiplier = 1.2; // Higher potential for growth stage
            break;
        case 'established':
            $stageMultiplier = 1.0; // Standard for established
            break;
    }

    switch ($businessContext['businessSize'] ?? '') {
        case 'solo':
            $sizeMultiplier = 0.7; // Limited resources
            break;
        case 'small':
            $sizeMultiplier = 0.9; // Some limitations
            break;
        case 'medium':
            $sizeMultiplier = 1.1; // Good resources
            break;
        case 'large':
            $sizeMultiplier = 1.3; // Extensive resources
            break;
    }

    // Apply multipliers to each tier
    $adjustedQuickWin = $quickWinBase * $stageMultiplier * $sizeMultiplier;
    $adjustedMediumWin = $mediumWinBase * $stageMultiplier * $sizeMultiplier;
    $adjustedStrategicWin = $strategicWinBase * $stageMultiplier * $sizeMultiplier;

    $suggestions = [
        'fashion' => [
            'quickWin' => [
                'title' => 'Implement size recommendation AI',
                'impact' => round($adjustedQuickWin),
                'description' => 'Deploy AI-powered size guide to reduce returns by 25% and increase customer satisfaction',
                'reasoning' => 'Addresses the #1 pain point in fashion e-commerce while improving customer experience'
            ],
            'mediumWin' => [
                'title' => 'Launch seasonal subscription boxes',
                'impact' => round($adjustedMediumWin),
                'description' => 'Create curated seasonal collections with 15% discount for subscribers',
                'reasoning' => 'Builds predictable recurring revenue while leveraging fashion\'s seasonal nature'
            ],
            'strategicWin' => [
                'title' => 'Build personal styling service',
                'impact' => round($adjustedStrategicWin),
                'description' => 'Launch premium styling consultations with exclusive product access',
                'reasoning' => 'Creates high-margin service revenue and deepens customer relationships'
            ]
        ],
        'beauty' => [
            'quickWin' => [
                'title' => 'Create replenishment reminders',
                'impact' => round($adjustedQuickWin * 1.1),
                'description' => 'Set up automated reorder notifications based on product usage cycles',
                'reasoning' => 'Beauty products have predictable consumption patterns, making this highly effective'
            ],
            'mediumWin' => [
                'title' => 'Launch beauty quiz & personalization',
                'impact' => round($adjustedMediumWin * 1.2),
                'description' => 'Create personalized product recommendations based on skin type and preferences',
                'reasoning' => 'Personalization drives higher engagement and reduces product returns in beauty'
            ],
            'strategicWin' => [
                'title' => 'Develop subscription beauty box',
                'impact' => round($adjustedStrategicWin * 1.1),
                'description' => 'Launch monthly curated beauty products with exclusive member pricing',
                'reasoning' => 'Subscription model creates predictable revenue and increases customer lifetime'
            ]
        ],
        'electronics' => [
            'quickWin' => [
                'title' => 'Offer extended warranties',
                'impact' => round($adjustedQuickWin * 0.9),
                'description' => 'Provide extended protection plans and premium support services',
                'reasoning' => 'High-margin service add-ons that customers value for expensive electronics'
            ],
            'mediumWin' => [
                'title' => 'Create accessory bundles',
                'impact' => round($adjustedMediumWin * 1.0),
                'description' => 'Bundle complementary accessories with main products at discounted rates',
                'reasoning' => 'Increases average order value while providing customer convenience'
            ],
            'strategicWin' => [
                'title' => 'Launch trade-in program',
                'impact' => round($adjustedStrategicWin * 0.9),
                'description' => 'Implement device trade-in program to encourage upgrade cycles',
                'reasoning' => 'Encourages repeat purchases while reducing customer acquisition costs'
            ]
        ],
        'food' => [
            'quickWin' => [
                'title' => 'Implement auto-reorder system',
                'impact' => round($adjustedQuickWin * 1.2),
                'description' => 'Set up automatic reordering for frequently purchased items',
                'reasoning' => 'Food has natural replenishment cycles, making automation highly effective'
            ],
            'mediumWin' => [
                'title' => 'Create meal planning service',
                'impact' => round($adjustedMediumWin * 1.1),
                'description' => 'Offer personalized meal plans with integrated shopping lists',
                'reasoning' => 'Adds value beyond products while increasing purchase frequency'
            ],
            'strategicWin' => [
                'title' => 'Launch premium ingredient tiers',
                'impact' => round($adjustedStrategicWin * 1.0),
                'description' => 'Introduce organic, artisanal, or specialty ingredient options',
                'reasoning' => 'Higher-margin product lines appeal to quality-conscious customers'
            ]
        ],
        'home' => [
            'quickWin' => [
                'title' => 'Offer design consultation',
                'impact' => round($adjustedQuickWin * 0.8),
                'description' => 'Provide virtual or in-person interior design consultations',
                'reasoning' => 'Premium service add-on that increases customer engagement and loyalty'
            ],
            'mediumWin' => [
                'title' => 'Create room makeover packages',
                'impact' => round($adjustedMediumWin * 1.1),
                'description' => 'Bundle furniture and decor items for complete room transformations',
                'reasoning' => 'Significantly increases average order value through strategic bundling'
            ],
            'strategicWin' => [
                'title' => 'Launch interior design service',
                'impact' => round($adjustedStrategicWin * 1.2),
                'description' => 'Offer full-service interior design with product sourcing',
                'reasoning' => 'High-value professional service that creates long-term customer relationships'
            ]
        ],
        'health' => [
            'quickWin' => [
                'title' => 'Add supplement subscriptions',
                'impact' => round($adjustedQuickWin * 1.1),
                'description' => 'Offer subscription plans for vitamins and supplements',
                'reasoning' => 'Health products have regular consumption patterns ideal for subscriptions'
            ],
            'mediumWin' => [
                'title' => 'Create wellness coaching',
                'impact' => round($adjustedMediumWin * 1.3),
                'description' => 'Provide personalized health and wellness coaching services',
                'reasoning' => 'High-value service that deepens customer relationships and increases retention'
            ],
            'strategicWin' => [
                'title' => 'Develop health tracking app',
                'impact' => round($adjustedStrategicWin * 1.1),
                'description' => 'Build comprehensive wellness platform with progress tracking',
                'reasoning' => 'Creates ecosystem lock-in while providing ongoing customer value'
            ]
        ],
        'general' => [
            'quickWin' => [
                'title' => 'Implement smart email automation',
                'impact' => round($adjustedQuickWin),
                'description' => 'Set up behavioral triggers for cart abandonment, post-purchase, and re-engagement',
                'reasoning' => 'Low-cost, high-impact strategy that works across all business stages and sizes'
            ],
            'mediumWin' => [
                'title' => 'Create customer referral system',
                'impact' => round($adjustedMediumWin),
                'description' => 'Launch incentivized referral program with tracking and automated rewards',
                'reasoning' => 'Leverages existing customers to reduce acquisition costs while increasing retention'
            ],
            'strategicWin' => [
                'title' => 'Build customer success platform',
                'impact' => round($adjustedStrategicWin),
                'description' => 'Develop comprehensive onboarding, support, and upselling system',
                'reasoning' => 'Creates systematic approach to maximizing customer value throughout their lifecycle'
            ]
        ]
    ];

    $industrySuggestions = $suggestions[$industry] ?? $suggestions['general'];
    $totalImpact = $industrySuggestions['quickWin']['impact'] + $industrySuggestions['mediumWin']['impact'] + $industrySuggestions['strategicWin']['impact'];

    // Generate personalized insight based on business context
    $personalizedInsight = generatePersonalizedInsight($businessContext, $industry, $currentLtv, $totalImpact);

    return [
        'success' => true,
        'source' => 'fallback_template',
        'quickWin' => $industrySuggestions['quickWin'],
        'mediumWin' => $industrySuggestions['mediumWin'],
        'strategicWin' => $industrySuggestions['strategicWin'],
        'combinedLtv' => $currentLtv + $totalImpact,
        'combinedIncrease' => $currentLtv > 0 ? round(($totalImpact / $currentLtv) * 100) : 0,
        'personalizedInsight' => $personalizedInsight
    ];
}

function generatePersonalizedInsight($businessContext, $industry, $currentLtv, $totalImpact) {
    $stage = $businessContext['businessStage'] ?? '';
    $size = $businessContext['businessSize'] ?? '';
    $goal = $businessContext['primaryGoal'] ?? '';
    $challenges = $businessContext['businessChallenges'] ?? '';

    $percentageIncrease = $currentLtv > 0 ? round(($totalImpact / $currentLtv) * 100) : 0;

    // Base insight
    $insight = "Your {$industry} business shows strong potential for {$percentageIncrease}% LTV growth. ";

    // Add stage-specific insight
    switch ($stage) {
        case 'startup':
            $insight .= "As a startup, focus on proving product-market fit while building sustainable retention systems. ";
            break;
        case 'growth':
            $insight .= "In the growth stage, scaling your customer success operations will be crucial for maintaining quality as you expand. ";
            break;
        case 'established':
            $insight .= "With your established market position, premium service offerings and customer segmentation will drive the highest returns. ";
            break;
    }

    // Add goal-specific insight
    switch ($goal) {
        case 'increase_revenue':
            $insight .= "Prioritize strategies that directly impact average order value and purchase frequency.";
            break;
        case 'reduce_churn':
            $insight .= "Focus on early warning systems and proactive customer success interventions.";
            break;
        case 'improve_retention':
            $insight .= "Invest in personalization and loyalty programs to deepen customer relationships.";
            break;
        case 'scale_operations':
            $insight .= "Automate customer touchpoints while maintaining personalized experiences.";
            break;
        default:
            $insight .= "Start with quick wins to build momentum before tackling larger initiatives.";
    }

    return $insight;
}

// Include header after API handling to prevent header conflicts
include 'header.php';
?>



<section class="page-title-parallax-background half-section ipad-top-space-margin" data-parallax-background-ratio="0.5" style="background-image: url('images/parallax-bg.jpg');">
   <div class="professional-gradient-container">
                <div class="corner-gradient top-left"></div>
                <div class="corner-gradient top-right"></div>
                <div class="corner-gradient bottom-left"></div>
                <div class="corner-gradient bottom-right"></div>
                <div class="diagonal-gradient"></div>
              <div class="vignette-overlay"></div>
            </div>
<div class="container">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-6 col-lg-7 text-center position-relative page-title-extra-large">
                <div class="d-flex flex-column small-screen">
                   <div class="mt-auto">

                        <h1 class="text-white fw-500 ls-minus-1px mb-0">LTV Potential Calculator</h1>
                    </div>
                    <div class="mt-auto justify-content-center breadcrumb breadcrumb-style-01 fs-15 text-white">
                        <ul>
                            <li><a href="#" class="text-white">Home</a></li>
                            <li><a href="#" class="text-white">Resources</a></li>
                            <li><span class="opacity-7">LTV Potential Calculator</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="ltv-calculator-section position-relative overflow-hidden py-100px">


    <div class="container position-relative">
        <div class="row justify-content-center mb-50px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <h2 class="alt-font fw-700 ls-minus-1px mb-20px text-dark-gray">Estimate Your E-commerce LTV Potential</h2>
                <p class="mb-0">Are you focusing only on the first sale? Discover the true profit potential of your customers over their entire lifecycle. Use our advanced calculator for a more authentic estimate.</p>
                <button class=" btn btn-link-gradient btn-extra-large text-gradient-light-pink-light-purple thin d-table d-lg-inline-block xl-mb-15px md-mx-auto mt-20px" id="howToUseBtn">
                    <i class="bi bi-info-circle me-5px"></i>How to use this calculator
                </button>
            </div>
        </div>

        <div class="modal fade" id="howToUseModal" tabindex="-1" aria-labelledby="howToUseModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content border-radius-6px">
                    <div class="modal-header">
                        <h5 class="modal-title" id="howToUseModalLabel">How to Use the LTV Calculator</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-30px">
                        <h6 class="fw-600 mb-15px text-dark-gray">What is Customer Lifetime Value (LTV)?</h6>
                        <p>Customer Lifetime Value (LTV) is the total **profit** a business can expect from a single customer throughout their relationship. Understanding your LTV helps you make smarter, profit-driven decisions about customer acquisition, marketing budgets, and retention strategies.</p>

                        <h6 class="fw-600 mb-15px mt-30px">How to Use This Calculator</h6>
                        <ol class="mb-30px ps-3">
                            <li class="mb-10px"><span class="fw-600">Enter your Average Initial Order Value</span> - The average amount customers spend on their first purchase.</li>
                            <li class="mb-10px"><span class="fw-600">Set the Avg. Repeat Purchases Per Year</span> - How many additional times a typical customer buys from you annually.</li>
                            <li class="mb-10px"><span class="fw-600">Enter the Average Value of Each Repeat Purchase</span> - The average amount of subsequent orders.</li>
                            <li class="mb-10px"><span class="fw-600">Enter Your Gross Margin (%)</span> - This is crucial for accuracy. It's your revenue minus the cost of goods sold (COGS). For example, if a product sells for $100 and costs $40 to make, your gross margin is 60%.</li>
                             <li class="mb-10px"><span class="fw-600">Enter Average Customer Lifespan</span> - The average number of years a person remains an active customer.</li>
                            <li class="mb-10px"><span class="fw-600">Input your New Customers Acquired Monthly</span> - This helps calculate the total potential value from new customer cohorts.</li>
                        </ol>

                        <h6 class="fw-600 mb-15px text-dark-gray">Why This Matters</h6>
                        <div class="row">
                            <div class="col-md-6 mb-20px">
                                <div class="p-20px border-radius-6px bg-very-light-gray h-100">
                                    <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-graph-up text-gradient-primary me-10px"></i>Make Better Marketing Decisions</h6>
                                    <p class="mb-0 fs-14">When you know your customer's true profit value, you can define precise acquisition budgets and optimize ad spend for maximum ROI.</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-20px">
                                <div class="p-20px border-radius-6px bg-very-light-gray h-100">
                                    <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-bullseye text-gradient-primary me-10px"></i>Target High-Value Customers</h6>
                                    <p class="mb-0 fs-14">Identify and focus on acquiring customer segments that generate the most profit over their lifetime.</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-20px">
                                <div class="p-20px border-radius-6px bg-very-light-gray h-100">
                                    <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-cash-stack text-gradient-primary me-10px"></i>Increase Profitability</h6>
                                    <p class="mb-0 fs-14">By focusing on profit-based LTV, businesses can achieve more sustainable and predictable growth.</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-20px">
                                <div class="p-20px border-radius-6px bg-very-light-gray h-100">
                                    <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-people text-gradient-primary me-10px"></i>Improve Customer Retention</h6>
                                    <p class="mb-0 fs-14">Understanding LTV helps prioritize retention strategies that keep your most profitable customers coming back.</p>
                                </div>
                            </div>
                        </div>

                        <h6 class="fw-600 mb-15px mt-10px text-dark-gray">How We Calculate LTV</h6>
                        <p>Our calculator uses an authentic, profit-focused algorithm to provide a more realistic valuation of your customers. This enhanced formula accounts for:</p>
                        <ul class="ps-3">
                            <li><strong>Profit, Not Just Revenue:</strong> By incorporating your <span class="fw-600">Gross Margin</span>, we calculate the actual profit generated.</li>
                            <li><strong>Customer Lifespan:</strong> We project profits over the average customer's entire active period with your brand.</li>
                            <li><strong>Time Value of Money (Discount Rate):</strong> Future profits are discounted to their present-day value, providing a financially sound LTV figure (Net Present Value).</li>
                            <li><strong>Industry Benchmarks:</strong> Compare your results against industry averages for a clearer picture of your performance.</li>
                        </ul>
                        <p class="mb-0 mt-15px"><span class="fw-600">Note:</span> This calculator provides estimates based on the information you provide. For a more detailed analysis specific to your business, <a href="free-ad-audit.php" class="text-decoration-underline">request a free audit</a>.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-dark-gray" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-xl-12 col-12">
                <div class="ltv-calculator-container box-shadow-extra-large border-radius-10px overflow-hidden">
                    <div class="row g-0">
                        <div class="col-lg-6 calculator-inputs-container">
                            <div class="p-40px md-p-30px sm-p-20px">
                                <h3 class="alt-font fw-700 fs-22 mb-25px text-dark">Enter Your Data</h3>

                                <form id="ltvCalculatorForm" class="calculator-form">
                                    <div class="position-relative form-group mb-25px">
                                        <label for="initialOrderValue" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Average Initial Order Value ($)
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="The average amount customers spend on their first purchase with your store.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-transparent border-end-0">$</span>
                                            <input type="number" id="initialOrderValue" class="form-control border-start-0 border-radius-right-4px" placeholder="0.00" min="0" step="0.01" value="100">
                                        </div>
                                    </div>

                                    <div class="position-relative form-group mb-25px">
                                        <label for="repeatPurchases" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Avg. Repeat Purchases Per Year
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="How many additional times a typical customer buys from you *annually* after their first purchase.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <div class="range-slider-container">
                                            <input type="range" id="repeatPurchasesSlider" class="form-range" min="0" max="20" step="1" value="2">
                                            <input type="number" id="repeatPurchases" class="form-control border-radius-4px" min="0" max="20" value="2">
                                        </div>
                                    </div>
                                    
                                    <div class="position-relative form-group mb-25px">
                                        <label for="repeatPurchaseValue" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Avg. Value of Each Repeat Purchase ($)
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="The average amount customers spend on subsequent orders. Often different from the initial order value.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-transparent border-end-0">$</span>
                                            <input type="number" id="repeatPurchaseValue" class="form-control border-start-0 border-radius-right-4px" placeholder="0.00" min="0" step="0.01" value="80">
                                        </div>
                                    </div>

                                    <div class="position-relative form-group mb-25px">
                                        <label for="grossMargin" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Gross Margin (%)
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="Your profit margin before overhead. Formula: ((Revenue - Cost of Goods Sold) / Revenue) * 100. If a product sells for $100 and costs $40, the margin is 60%.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <div class="input-group">
                                            <input type="number" id="grossMargin" class="form-control border-radius-4px" placeholder="e.g., 60" min="0" max="100" value="60">
                                            <span class="input-group-text bg-transparent border-start-0">%</span>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                             <div class="position-relative form-group mb-25px">
                                                <label for="customerLifespan" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                                    Customer Lifespan (Yrs)
                                                    <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="The average number of years a person remains an active customer.">
                                                        <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                                    </span>
                                                </label>
                                                <input type="number" id="customerLifespan" class="form-control border-radius-4px" placeholder="e.g., 3" min="1" value="3">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="position-relative form-group mb-25px">
                                                <label for="discountRate" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                                   Discount Rate (%)
                                                    <span class="fs-12 opacity-6">(Adv)</span>
                                                    <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="Rate to calculate present value of future profits (time value of money). A typical rate is 8-12%.">
                                                        <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                                    </span>
                                                </label>
                                                <input type="number" id="discountRate" class="form-control border-radius-4px" placeholder="e.g., 10" min="0" value="10">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="position-relative form-group mb-25px">
                                        <label for="newCustomers" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            New Customers Acquired Monthly
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="The number of new customers you acquire each month. Used to calculate total potential LTV from a monthly cohort.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <input type="number" id="newCustomers" class="form-control border-radius-4px" placeholder="Enter number" min="0" value="100">
                                    </div>

                                    <div class="position-relative form-group mb-25px">
                                        <label for="industrySelector" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Your Industry
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="Select your industry to compare your LTV against typical benchmarks.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <select id="industrySelector" class="form-select border-radius-4px">
                                            <option value="general">General E-commerce</option>
                                            <option value="fashion">Fashion & Apparel</option>
                                            <option value="beauty">Beauty & Cosmetics</option>
                                            <option value="electronics">Electronics & Gadgets</option>
                                            <option value="food">Food & Grocery</option>
                                            <option value="home">Home & Furniture</option>
                                            <option value="health">Health & Wellness</option>
                                        </select>
                                    </div>

                                    <!-- Business Context Section (Moved from right side) -->
                                    <div class="business-context-section mt-30px p-20px border-radius-8px" style="background: rgba(248, 249, 250, 0.5); border: 1px solid rgba(0, 0, 0, 0.1);">
                                        <h6 class="text-dark fw-600 fs-16 mb-15px d-flex align-items-center">
                                            <i class="bi bi-building me-10px text-gradient-light-pink-light-purple"></i>
                                            Business Context (Optional - for better AI recommendations)
                                        </h6>

                                        <div class="row g-3">
                                            <div class="col-6">
                                                <input type="text" id="businessName" class="form-control border-radius-4px"
                                                       placeholder="Business name">
                                            </div>
                                            <div class="col-6">
                                                <select id="businessStage" class="form-select border-radius-4px">
                                                    <option value="">Business stage</option>
                                                    <option value="startup">Startup (0-2 years)</option>
                                                    <option value="growth">Growth (2-5 years)</option>
                                                    <option value="established">Established (5+ years)</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row g-3 mt-2">
                                            <div class="col-6">
                                                <select id="businessSize" class="form-select border-radius-4px">
                                                    <option value="">Team size</option>
                                                    <option value="solo">Solo entrepreneur</option>
                                                    <option value="small">Small team (2-10)</option>
                                                    <option value="medium">Medium (11-50)</option>
                                                    <option value="large">Large (50+)</option>
                                                </select>
                                            </div>
                                            <div class="col-6">
                                                <select id="primaryGoal" class="form-select border-radius-4px">
                                                    <option value="">Primary goal</option>
                                                    <option value="increase_revenue">Increase revenue</option>
                                                    <option value="reduce_churn">Reduce churn</option>
                                                    <option value="improve_retention">Improve retention</option>
                                                    <option value="scale_operations">Scale operations</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="mt-15px">
                                            <textarea id="businessChallenges" class="form-control border-radius-4px"
                                                      placeholder="Main challenges or goals (optional, 100 chars max)"
                                                      rows="3" maxlength="100"
                                                      style="resize: none;"></textarea>
                                            <div class="text-end mt-1">
                                                <small class="text-muted fs-12" id="challengesCounter">0/100</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- AI Analysis Button -->
                                    <div class="ai-analysis-button mt-25px">
                                        <button type="button" class="btn btn-gradient-pink-orange btn-large btn-rounded w-100" id="refreshAiSuggestionsBtn">
                                            <i class="bi bi-robot me-10px"></i>Calculate LTV & Get AI Strategies
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="col-lg-6 calculator-results-container custom-gradient-bg">
                            <div class="p-40px md-p-30px sm-p-20px h-100 d-flex flex-column">
                                <h3 class="alt-font fw-700 fs-22 mb-25px text-white">Your Authentic LTV Results</h3>

                                <div class="results-content flex-grow-1 d-flex flex-column justify-content-start mt-10px">
                                    <div class="result-item mb-25px">
                                        <div class="result-label text-white opacity-9 mb-5px fw-500">Estimated Profit LTV Per Customer:</div>
                                        <div class="result-value d-flex align-items-start">
                                            <span class="currency text-white fs-30 fw-600 me-2" style="margin-top: 15px;">$</span>
                                            <span id="ltvPerCustomer" class="value fs-60 fw-700 text-white" data-anime='{ "el": "self", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 300 }'>451</span>
                                        </div>
                                    </div>

                                    <div class="result-item mb-25px">
                                        <div class="result-label text-white opacity-9 mb-5px fw-500">Total Monthly LTV from New Customers:</div>
                                        <div class="result-value d-flex align-items-start">
                                            <span class="currency text-white fs-20 fw-600 me-2" style="margin-top: 12px;">$</span>
                                            <span id="totalLtv" class="value fs-40 fw-700 text-white" data-anime='{ "el": "self", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 400 }'>45,100</span>
                                        </div>
                                    </div>



                                    <!-- AI-Powered LTV Improvement Suggestions -->
                                    <div class="ai-suggestions-container mb-30px" id="aiSuggestionsContainer">
                                        <div class="ai-suggestions-header mb-15px">
                                            <h4 class="text-white fw-600 fs-18 mb-5px d-flex align-items-center">
                                                <i class="bi bi-robot me-10px text-gradient-light-pink-light-purple"></i>
                                                AI-Powered LTV Optimization
                                            </h4>
                                            <p class="text-white opacity-8 fs-14 mb-0">Personalized strategies to maximize your customer lifetime value</p>
                                        </div>

                                        <div class="ai-suggestions-content">
                                            <!-- Quick Wins Section -->
                                            <div class="suggestion-section mb-20px">
                                                <h5 class="text-white fw-500 fs-16 mb-10px">🚀 Quick Wins (30-60 days)</h5>
                                                <div class="suggestion-item p-15px border-radius-8px mb-10px" style="background: rgba(255,255,255,0.08); border: 1px solid rgba(255,255,255,0.12);">
                                                    <div class="d-flex justify-content-between align-items-start mb-5px">
                                                        <span class="text-white fw-500 fs-14" id="quickWinTitle">Increase repeat purchase rate by 15%</span>
                                                        <span class="text-gradient-light-pink-light-purple fw-600 fs-14" id="quickWinImpact">+$45.32</span>
                                                    </div>
                                                    <p class="text-white opacity-8 fs-13 mb-0" id="quickWinDescription">Implement email sequences and loyalty rewards</p>
                                                </div>
                                            </div>

                                            <!-- Medium-term Opportunities -->
                                            <div class="suggestion-section mb-20px">
                                                <h5 class="text-white fw-500 fs-16 mb-10px">📈 Growth Opportunities (3-6 months)</h5>
                                                <div class="suggestion-item p-15px border-radius-8px mb-10px" style="background: rgba(255,255,255,0.08); border: 1px solid rgba(255,255,255,0.12);">
                                                    <div class="d-flex justify-content-between align-items-start mb-5px">
                                                        <span class="text-white fw-500 fs-14" id="mediumWinTitle">Optimize average order value</span>
                                                        <span class="text-gradient-light-pink-light-purple fw-600 fs-14" id="mediumWinImpact">+$67.89</span>
                                                    </div>
                                                    <p class="text-white opacity-8 fs-13 mb-0" id="mediumWinDescription">Cross-selling and upselling strategies</p>
                                                </div>
                                            </div>

                                            <!-- Strategic Initiatives -->
                                            <div class="suggestion-section mb-20px">
                                                <h5 class="text-white fw-500 fs-16 mb-10px">🎯 Strategic Focus (6-12 months)</h5>
                                                <div class="suggestion-item p-15px border-radius-8px mb-10px" style="background: rgba(255,255,255,0.08); border: 1px solid rgba(255,255,255,0.12);">
                                                    <div class="d-flex justify-content-between align-items-start mb-5px">
                                                        <span class="text-white fw-500 fs-14" id="strategicWinTitle">Extend customer lifespan</span>
                                                        <span class="text-gradient-light-pink-light-purple fw-600 fs-14" id="strategicWinImpact">+$124.56</span>
                                                    </div>
                                                    <p class="text-white opacity-8 fs-13 mb-0" id="strategicWinDescription">Customer success programs and retention initiatives</p>
                                                </div>
                                            </div>

                                            <!-- Combined Impact -->
                                            <div class="combined-impact p-15px border-radius-8px" style="background: linear-gradient(135deg, rgba(222, 52, 127, 0.15), rgba(143, 118, 245, 0.15)); border: 1px solid rgba(222, 52, 127, 0.3);">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <span class="text-white fw-600 fs-16">Combined Potential LTV:</span>
                                                        <p class="text-white opacity-8 fs-13 mb-0">If all strategies are implemented</p>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="text-white fw-700 fs-24" id="combinedLtvPotential">$464.38</span>
                                                        <p class="text-gradient-light-pink-light-purple fw-600 fs-14 mb-0" id="combinedLtvIncrease">+105% increase</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>




                                    </div>
                                </div>

                                <div class="cta-container">
                                    <a href="free-ad-audit.php" class="btn btn-large btn-white btn-box-shadow btn-round-edge d-block">
                                        <span>Unlock Your Full Profit Potential with Adzeta →</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<style>
    /* Calculator Container Styles */
    .ltv-calculator-container {
        background: #ffffff;
        overflow: hidden;
        position: relative;
        border-radius: 24px !important;
    }

    /* Input Styles */
    .calculator-inputs-container {
        position: relative;
        z-index: 1;
    }
    
    .calculator-form .form-control {
        height: 50px;
        border-color: rgba(0, 0, 0, 0.1);
        background-color: rgba(248, 249, 250, 0.5);
        transition: all 0.3s ease;
    }

    .calculator-form .form-control:focus {
        border-color: #8f76f5;
        box-shadow: 0 0 10px rgba(143, 118, 245, 0.2);
    }

    .calculator-form .input-group-text {
        border-color: rgba(0, 0, 0, 0.1);
        color: #666;
    }
    
    .calculator-form .form-select {
        height: 50px;
    }

    /* Range Slider Styles */
    .range-slider-container {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .range-slider-container .form-range {
        flex-grow: 1;
    }
    
    .range-slider-container .form-range, .range-slider-container input[type="number"] {
        height: 20px;
    }

    .range-slider-container input[type="number"] {
        width: 70px;
        text-align: center;
        -webkit-appearance: textfield;
        -moz-appearance: textfield;
        appearance: textfield; /* Remove spinner for Firefox */
    }

    /* Remove spinner for Chrome, Safari, Edge, Opera */
    .range-slider-container input[type="number"]::-webkit-outer-spin-button,
    .range-slider-container input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        appearance: none;
        margin: 0;
    }

    .form-range {
        height: 8px;
        background: rgba(143, 118, 245, 0.2);
        border-radius: 4px;
        -webkit-appearance: none;
        appearance: none;
    }

    .form-range::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
        cursor: pointer;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
        margin-top: -7px;
    }

    .form-range::-moz-range-thumb {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
        cursor: pointer;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
        border: none;
    }

    /* Results Container Styles */
    .calculator-results-container {
        position: relative;
        overflow: hidden;
    }

    .custom-gradient-bg {
        background: linear-gradient(135deg, #1B0B24 0%, #2b1a3a 50%, #3a1a3a 100%);
    }

    .calculator-results-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('images/mesh-overlay.svg');
        background-size: cover;
        opacity: 0.1;
        z-index: 0;
        pointer-events: none;
    }

    .results-content {
        position: relative;
        z-index: 1;
    }
    
    .result-value {
        position: relative;
    }

    .result-value .currency {
        line-height: 1;
        opacity: 0.9;
    }

    .result-value .value {
        background: linear-gradient(to right, #ffffff, #ff8cc6);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent !important;
        display: inline-block;
        line-height: 1;
    }

    /* Insight Message Styles */
    .insight-message {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        position: relative;
        transition: all 0.3s ease;
    }

    .insight-icon {
        font-size: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 30px;
    }

    #insightMessage {
        font-weight: 500;
        line-height: 1.5;
        opacity: 0.95;
    }

    /* Color-coded levels */
    .level-excellent { background: rgba(52, 191, 163, 0.2); border-left: 4px solid #34BFA3; }
    .level-excellent .insight-icon { color: #34BFA3; }
    .level-good { background: rgba(88, 103, 221, 0.2); border-left: 4px solid #5867DD; }
    .level-good .insight-icon { color: #5867DD; }
    .level-average { background: rgba(255, 184, 34, 0.2); border-left: 4px solid #FFB822; }
    .level-average .insight-icon { color: #FFB822; }
    .level-low { background: rgba(244, 88, 136, 0.2); border-left: 4px solid #F45888; }
    .level-low .insight-icon { color: #F45888; }

    /* Dynamic highlight styles for AI-generated insights */
    .highlight-excellent {
        color: #34BFA3;
        font-weight: 600;
        text-shadow: 0 0 8px rgba(52, 191, 163, 0.3);
    }

    .highlight-good {
        color: #5867DD;
        font-weight: 600;
        text-shadow: 0 0 8px rgba(88, 103, 221, 0.3);
    }

    .highlight-average {
        color: #FFB822;
        font-weight: 600;
        text-shadow: 0 0 8px rgba(255, 184, 34, 0.3);
    }

    .highlight-low {
        color: #F45888;
        font-weight: 600;
        text-shadow: 0 0 8px rgba(244, 88, 136, 0.3);
    }

    .highlight-number {
        background: linear-gradient(135deg, #de347f, #8f76f5);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-weight: 700;
        font-size: 1.05em;
    }

    .highlight-strategy {
        color: #ffffff;
        background: rgba(222, 52, 127, 0.2);
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
        border: 1px solid rgba(222, 52, 127, 0.3);
    }


    /* Responsive Adjustments */
    @media (max-width: 991px) {
        .calculator-results-container {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        .result-value #ltvPerCustomer { font-size: 50px; }
        .result-value #totalLtv { font-size: 34px; }
    }

    @media (max-width: 767px) {
        .result-value #ltvPerCustomer { font-size: 40px; }
        .result-value #totalLtv { font-size: 28px; }
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all the input elements
    const initialOrderValueInput = document.getElementById('initialOrderValue');
    const repeatPurchasesInput = document.getElementById('repeatPurchases');
    const repeatPurchasesSlider = document.getElementById('repeatPurchasesSlider');
    const repeatPurchaseValueInput = document.getElementById('repeatPurchaseValue');
    const newCustomersInput = document.getElementById('newCustomers');
    const industrySelectorInput = document.getElementById('industrySelector');
    // New Inputs for accurate calculation
    const grossMarginInput = document.getElementById('grossMargin');
    const customerLifespanInput = document.getElementById('customerLifespan');
    const discountRateInput = document.getElementById('discountRate');

    // Get the result elements
    const ltvPerCustomerElement = document.getElementById('ltvPerCustomer');
    const totalLtvElement = document.getElementById('totalLtv');
    // Removed old insight elements - now using single combined insight section

    // NEW: Updated industry benchmarks with more authentic data points
    const industryBenchmarks = {
        fashion: { aov: 90, repeatRate: 2.5, lifespan: 2.5, margin: 55, description: "Fashion sees seasonal peaks. Success depends on trend alignment and brand loyalty." },
        beauty: { aov: 65, repeatRate: 4, lifespan: 3, margin: 65, description: "Beauty thrives on replenishment and subscriptions, leading to high repeat rates." },
        electronics: { aov: 200, repeatRate: 1.2, lifespan: 4, margin: 25, description: "Electronics has high ticket prices but lower repeat purchase frequency." },
        food: { aov: 55, repeatRate: 8, lifespan: 2, margin: 30, description: "Grocery has the highest purchase frequency but operates on thinner margins." },
        home: { aov: 250, repeatRate: 1.5, lifespan: 5, margin: 45, description: "Home goods are infrequent, high-value purchases with a long customer lifespan." },
        health: { aov: 75, repeatRate: 5, lifespan: 3.5, margin: 60, description: "Health & Wellness often uses a subscription model, driving strong, profitable retention." },
        general: { aov: 100, repeatRate: 2, lifespan: 3, margin: 40, description: "General e-commerce is a mix of various models. Focusing on profitable niches is key." }
    };

    // Sync the number input and range slider for repeat purchases (NO auto-calculation)
    if (repeatPurchasesSlider && repeatPurchasesInput) {
        repeatPurchasesSlider.addEventListener('input', function() {
            repeatPurchasesInput.value = this.value;
            // Removed calculateLTV() - only calculate when user requests AI analysis
        });

        repeatPurchasesInput.addEventListener('input', function() {
            const maxVal = parseInt(this.max);
            if (parseInt(this.value) > maxVal) this.value = maxVal;
            if (parseInt(this.value) < 0) this.value = 0;
            repeatPurchasesSlider.value = this.value;
            // Removed calculateLTV() - only calculate when user requests AI analysis
        });
    }
    
    // Remove auto-calculation listeners - only calculate when user requests AI analysis
    // const allInputs = [initialOrderValueInput, repeatPurchaseValueInput, newCustomersInput, grossMarginInput, customerLifespanInput, discountRateInput, industrySelectorInput];
    // allInputs.forEach(input => input.addEventListener('input', calculateLTV));

    // --- Core LTV Calculation Algorithm ---
    function calculateLTV() {
        // Get the values from inputs, providing default values to prevent errors
        const initialOrderValue = parseFloat(initialOrderValueInput.value) || 0;
        const repeatPurchasesPerYear = parseFloat(repeatPurchasesInput.value) || 0;
        const repeatPurchaseValue = parseFloat(repeatPurchaseValueInput.value) || 0;
        const grossMargin = parseFloat(grossMarginInput.value) || 0;
        const customerLifespan = parseFloat(customerLifespanInput.value) || 1;
        const discountRate = parseFloat(discountRateInput.value) || 0;
        const newCustomers = parseInt(newCustomersInput.value) || 0;
        const selectedIndustry = industrySelectorInput.value;
        
        const marginMultiplier = grossMargin / 100;
        const r = discountRate / 100;

        // --- Authentic LTV Calculation (using Net Present Value) ---
        const initialProfit = initialOrderValue * marginMultiplier;
        const annualRepeatProfit = repeatPurchasesPerYear * repeatPurchaseValue * marginMultiplier;
        let cumulativeProfit = initialProfit;

        for (let year = 1; year < customerLifespan; year++) {
            let presentValueOfFutureProfit = annualRepeatProfit / Math.pow((1 + r), year);
            cumulativeProfit += presentValueOfFutureProfit;
        }
        
        const ltvPerCustomer = cumulativeProfit;
        const totalLtv = ltvPerCustomer * newCustomers;

        animateValue(ltvPerCustomerElement, ltvPerCustomer);
        animateValue(totalLtvElement, totalLtv);
        
        // MODIFIED: Pass the missing variables to the insight function
        updateInsightMessage(ltvPerCustomer, grossMargin, customerLifespan, selectedIndustry, repeatPurchasesPerYear, repeatPurchaseValue);
    }

    // Secure AI Suggestions Functions - Defined after calculateLTV
    async function generateAiSuggestions() {
        try {
            // First calculate LTV based on current inputs
            calculateLTV();

            // Show loading state
            const refreshBtn = document.getElementById('refreshAiSuggestionsBtn');
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Calculating LTV & AI Analysis...';

            const container = document.getElementById('aiSuggestionsContainer');
            container.style.opacity = '0.6';

            // Get current calculator values using correct IDs
            const inputs = {
                industry: document.getElementById('industrySelector').value,
                currentLtv: parseFloat(document.getElementById('ltvPerCustomer').textContent.replace(/[,$]/g, '')) || 0,
                initialOrderValue: parseFloat(document.getElementById('initialOrderValue').value) || 0,
                repeatPurchaseValue: parseFloat(document.getElementById('repeatPurchaseValue').value) || 0,
                repeatPurchasesPerYear: parseFloat(document.getElementById('repeatPurchases').value) || 0,
                customerLifespan: parseFloat(document.getElementById('customerLifespan').value) || 0,
                grossMargin: parseFloat(document.getElementById('grossMargin').value) || 0,
                newCustomersPerMonth: parseInt(document.getElementById('newCustomers').value) || 0,
                // Business context for enhanced personalization
                businessName: document.getElementById('businessName').value.trim(),
                businessStage: document.getElementById('businessStage').value,
                businessSize: document.getElementById('businessSize').value,
                primaryGoal: document.getElementById('primaryGoal').value,
                businessChallenges: document.getElementById('businessChallenges').value.trim()
            };

            // Validate that we have minimum required data
            if (!inputs.initialOrderValue || !inputs.grossMargin || !inputs.customerLifespan) {
                throw new Error('Please fill in at least Initial Order Value, Gross Margin, and Customer Lifespan to get AI analysis.');
            }

            // Call secure PHP endpoint
            const formData = new FormData();
            formData.append('action', 'get_ai_suggestions');
            formData.append('debug', 'true'); // Enable debug mode
            Object.keys(inputs).forEach(key => {
                formData.append(key, inputs[key]);
            });

            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const aiSuggestions = await response.json();

            // Log the response for debugging
            console.log('AI Suggestions Response:', aiSuggestions);

            if (aiSuggestions.success) {
                updateAiSuggestionsDisplay(aiSuggestions);
                updateDynamicInsightMessage(aiSuggestions, inputs);

                // Show debug info if available
                if (aiSuggestions.debug) {
                    console.log('Debug Info:', aiSuggestions.debug);

                    // Show raw AI response if available
                    if (aiSuggestions.debug.raw_ai_response) {
                        console.log('🤖 RAW AI RESPONSE:');
                        console.log(aiSuggestions.debug.raw_ai_response);
                    }

                    // Show parsing errors if any
                    if (aiSuggestions.debug.ai_parse_error) {
                        console.error('❌ AI PARSE ERROR:', aiSuggestions.debug.ai_parse_error);
                    }

                    // Show source information
                    console.log('📊 SUGGESTION SOURCE:', aiSuggestions.debug.source);
                }
            } else {
                console.error('AI suggestions failed:', aiSuggestions);
                throw new Error('AI suggestions not available');
            }

        } catch (error) {
            console.error('Error generating AI suggestions:', error);

            // Try to parse the error response to get more details
            if (error.message && error.message.includes('Unexpected token')) {
                console.error('❌ RESPONSE PARSING ERROR - Server returned HTML instead of JSON');
                console.error('This usually means there\'s a PHP error. Check server logs.');

                // Try to extract the actual error from the response
                try {
                    const response = await fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    });
                    const text = await response.text();
                    console.error('🔍 ACTUAL SERVER RESPONSE:');
                    console.error(text.substring(0, 500));
                } catch (e) {
                    console.error('Could not fetch server response details');
                }
            }

            // Show fallback suggestions
            showFallbackSuggestions();
        } finally {
            // Remove loading state
            const refreshBtn = document.getElementById('refreshAiSuggestionsBtn');
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="bi bi-robot me-5px"></i>Calculate LTV & Get AI Strategies';

            const container = document.getElementById('aiSuggestionsContainer');
            container.style.opacity = '1';
        }
    }
    
    // MODIFIED: Update function to accept the new variables
    function updateInsightMessage(ltv, margin, lifespan, industryKey, repeatPurchasesPerYear, repeatPurchaseValue) {
        let message = '';
        let insightLevel = 'good';
        const benchmark = industryBenchmarks[industryKey];

        const benchmarkAOV = benchmark.aov;
        const benchmarkAnnualProfit = benchmarkAOV * benchmark.repeatRate * (benchmark.margin / 100);
        const benchmarkLTV = (benchmarkAOV * (benchmark.margin/100)) + (benchmarkAnnualProfit * (benchmark.lifespan - 1));

        const performanceVsBenchmark = (benchmarkLTV > 0) ? (ltv / benchmarkLTV) : 1;

        if (performanceVsBenchmark > 1.5) {
            insightLevel = 'excellent';
            message = `🏆 Exceptional performance! Your LTV is significantly outperforming the ${benchmark.description.split(' ')[0]} industry average. Your combination of high margins and customer loyalty is a powerful profit driver.`;
        } else if (performanceVsBenchmark > 1.1) {
            insightLevel = 'good';
            message = `📈 Great results! Your LTV is above the industry benchmark. Increasing customer lifespan by just one year could boost LTV by another $${(repeatPurchasesPerYear * repeatPurchaseValue * (margin/100)).toFixed(0)}.`;
        } else if (performanceVsBenchmark > 0.8) {
             insightLevel = 'warning';
             message = `💡 You have a solid foundation. Your LTV is near the industry average. A key area for growth could be increasing your Gross Margin, as every percentage point directly impacts LTV.`;
        } else {
            insightLevel = 'neutral';
            message = `🎯 There's significant room for growth. The industry benchmark LTV is around $${benchmarkLTV.toFixed(0)}. Focus on strategies to increase repeat purchase frequency or average order value.`;
        }

        if (margin < 40 && performanceVsBenchmark < 1.1) {
             message += " Your Gross Margin is on the lower side, which directly limits your LTV. Improving it could be your biggest lever for growth.";
        }
        if (lifespan < 2 && performanceVsBenchmark < 1.1) {
             message += " A short customer lifespan is holding back your LTV. Implementing retention campaigns could provide a significant boost.";
        }

        // Use the new single insight section instead of old elements
        updatePersonalizedInsight(message, insightLevel);
    }

    // Function to animate value changes
    function animateValue(element, newValue) {
        const startValue = parseFloat(element.textContent.replace(/,/g, '')) || 0;
        const duration = 500;
        const startTime = performance.now();

        function updateValue(currentTime) {
            const elapsedTime = currentTime - startTime;
            const progress = Math.min(elapsedTime / duration, 1);
            const currentValue = startValue + (newValue - startValue) * progress;
            element.textContent = formatNumber(currentValue);
            if (progress < 1) {
                requestAnimationFrame(updateValue);
            }
        }
        requestAnimationFrame(updateValue);
    }

    // Function to format numbers with commas
    function formatNumber(number) {
        return number.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,').replace(/\.00$/, '');
    }

    // Function to reset calculator display to empty state
    function resetCalculatorDisplay() {
        ltvPerCustomerElement.textContent = '--';
        totalLtvElement.textContent = '--';

        // Clear insight section (will be created by AI analysis)
        const existingInsight = document.getElementById('personalizedInsightSection');
        if (existingInsight) {
            existingInsight.remove();
        }

        // Clear AI suggestions
        document.getElementById('quickWinTitle').textContent = 'AI Analysis Required';
        document.getElementById('quickWinImpact').textContent = '--';
        document.getElementById('quickWinDescription').textContent = 'Fill out the form and click "Get AI Analysis" to see personalized strategies.';

        document.getElementById('mediumWinTitle').textContent = 'AI Analysis Required';
        document.getElementById('mediumWinImpact').textContent = '--';
        document.getElementById('mediumWinDescription').textContent = 'AI will analyze your business and provide tailored recommendations.';

        document.getElementById('strategicWinTitle').textContent = 'AI Analysis Required';
        document.getElementById('strategicWinImpact').textContent = '--';
        document.getElementById('strategicWinDescription').textContent = 'Get expert-level strategic insights powered by AI.';

        document.getElementById('combinedLtvPotential').textContent = '--';
        document.getElementById('combinedLtvIncrease').textContent = 'Pending Analysis';
    }

    // Initialize the page with empty state
    resetCalculatorDisplay(); // Start with empty/null display

    // Character counter for business challenges textarea
    const businessChallengesTextarea = document.getElementById('businessChallenges');
    const challengesCounter = document.getElementById('challengesCounter');

    if (businessChallengesTextarea && challengesCounter) {
        businessChallengesTextarea.addEventListener('input', function() {
            const currentLength = this.value.length;
            challengesCounter.textContent = `${currentLength}/100`;

            // Change color based on character count
            if (currentLength > 80) {
                challengesCounter.style.color = '#F45888';
            } else if (currentLength > 60) {
                challengesCounter.style.color = '#FFB822';
            } else {
                challengesCounter.style.color = '#666';
            }
        });
    }

    // Initialize Bootstrap components
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    const howToUseBtn = document.getElementById('howToUseBtn');
    if (howToUseBtn) {
        howToUseBtn.addEventListener('click', function() {
            const howToUseModal = new bootstrap.Modal(document.getElementById('howToUseModal'));
            howToUseModal.show();
        });
    }

    // AI-Powered LTV Optimization Functions - Only on user request
    // generateAiSuggestions(); // Removed auto-call on page load

    // Event listeners for AI suggestions
    const refreshAiSuggestionsBtn = document.getElementById('refreshAiSuggestionsBtn');
    const getDetailedStrategyBtn = document.getElementById('getDetailedStrategyBtn');

    if (refreshAiSuggestionsBtn) {
        refreshAiSuggestionsBtn.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent form submission and page reload
            generateAiSuggestions();
        });
    }

    if (getDetailedStrategyBtn) {
        getDetailedStrategyBtn.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent form submission and page reload
            downloadStrategyGuide();
        });
    }

    // Remove auto-refresh - only generate when user clicks button
    // Auto-refresh AI suggestions when calculator values change significantly
    // let lastLtv = 0;
    // const checkForLtvChanges = () => {
    //     const currentLtv = parseFloat(document.getElementById('ltvPerCustomer').textContent.replace(/[,$]/g, '')) || 0;
    //     if (Math.abs(currentLtv - lastLtv) > 10) { // If LTV changes by more than $10
    //         lastLtv = currentLtv;
    //         setTimeout(generateAiSuggestions, 1000); // Delay to avoid too frequent calls
    //     }
    // };

    // Monitor for changes in calculator inputs
    // const calculatorInputs = ['initialOrderValue', 'repeatPurchases', 'repeatPurchaseValue', 'grossMargin', 'customerLifespan', 'newCustomers'];
    // calculatorInputs.forEach(inputId => {
    //     const element = document.getElementById(inputId);
    //     if (element) {
    //         element.addEventListener('input', checkForLtvChanges);
    //         element.addEventListener('change', checkForLtvChanges);
    //     }
    // });

    // Character counter functionality already implemented above
});

// Move generateAiSuggestions function after calculateLTV is defined
// This will be defined later in the script
// Function moved to after calculateLTV definition

function updateAiSuggestionsDisplay(suggestions) {
    // Update Quick Win
    document.getElementById('quickWinTitle').textContent = suggestions.quickWin.title;
    document.getElementById('quickWinImpact').textContent = `+$${suggestions.quickWin.impact}`;
    document.getElementById('quickWinDescription').textContent = suggestions.quickWin.description;

    // Add reasoning if available
    if (suggestions.quickWin.reasoning) {
        const quickWinElement = document.getElementById('quickWinDescription').parentElement;
        let reasoningElement = quickWinElement.querySelector('.strategy-reasoning');
        if (!reasoningElement) {
            reasoningElement = document.createElement('div');
            reasoningElement.className = 'strategy-reasoning mt-2 text-white opacity-7 fs-12';
            quickWinElement.appendChild(reasoningElement);
        }
        reasoningElement.innerHTML = `<i class="bi bi-lightbulb me-1"></i>${suggestions.quickWin.reasoning}`;
    }

    // Update Medium Win
    document.getElementById('mediumWinTitle').textContent = suggestions.mediumWin.title;
    document.getElementById('mediumWinImpact').textContent = `+$${suggestions.mediumWin.impact}`;
    document.getElementById('mediumWinDescription').textContent = suggestions.mediumWin.description;

    // Add reasoning if available
    if (suggestions.mediumWin.reasoning) {
        const mediumWinElement = document.getElementById('mediumWinDescription').parentElement;
        let reasoningElement = mediumWinElement.querySelector('.strategy-reasoning');
        if (!reasoningElement) {
            reasoningElement = document.createElement('div');
            reasoningElement.className = 'strategy-reasoning mt-2 text-white opacity-7 fs-12';
            mediumWinElement.appendChild(reasoningElement);
        }
        reasoningElement.innerHTML = `<i class="bi bi-lightbulb me-1"></i>${suggestions.mediumWin.reasoning}`;
    }

    // Update Strategic Win
    document.getElementById('strategicWinTitle').textContent = suggestions.strategicWin.title;
    document.getElementById('strategicWinImpact').textContent = `+$${suggestions.strategicWin.impact}`;
    document.getElementById('strategicWinDescription').textContent = suggestions.strategicWin.description;

    // Add reasoning if available
    if (suggestions.strategicWin.reasoning) {
        const strategicWinElement = document.getElementById('strategicWinDescription').parentElement;
        let reasoningElement = strategicWinElement.querySelector('.strategy-reasoning');
        if (!reasoningElement) {
            reasoningElement = document.createElement('div');
            reasoningElement.className = 'strategy-reasoning mt-2 text-white opacity-7 fs-12';
            strategicWinElement.appendChild(reasoningElement);
        }
        reasoningElement.innerHTML = `<i class="bi bi-lightbulb me-1"></i>${suggestions.strategicWin.reasoning}`;
    }

    // Update Combined Impact
    document.getElementById('combinedLtvPotential').textContent = `$${suggestions.combinedLtv.toFixed(2)}`;
    document.getElementById('combinedLtvIncrease').textContent = `+${suggestions.combinedIncrease}% increase`;

    // Update personalized insight if available
    if (suggestions.personalizedInsight) {
        updatePersonalizedInsight(suggestions.personalizedInsight);
    }

    // Add subtle animation
    const container = document.getElementById('aiSuggestionsContainer');
    container.style.transform = 'translateY(5px)';
    container.style.transition = 'all 0.3s ease';
    setTimeout(() => {
        container.style.transform = 'translateY(0)';
    }, 100);
}

function updatePersonalizedInsight(insightText, insightLevel = 'good') {
    // Find or create personalized insight section
    let insightSection = document.getElementById('personalizedInsightSection');

    if (!insightSection) {
        // Create the section if it doesn't exist
        const aiContainer = document.getElementById('aiSuggestionsContainer');
        insightSection = document.createElement('div');
        insightSection.id = 'personalizedInsightSection';
        insightSection.className = 'personalized-insight mt-20px p-20px border-radius-8px';

        insightSection.innerHTML = `
            <div class="insight-header d-flex align-items-center mb-15px">
                <div class="insight-icon me-10px"></div>
                <h6 class="text-white fw-600 fs-16 mb-0">💡 AI Business Insight</h6>
            </div>
            <p class="text-white mb-0 fs-14 lh-28" id="personalizedInsightText"></p>
        `;

        // Insert at the end of AI container
        aiContainer.appendChild(insightSection);
    }

    // Dynamic color coding based on insight level
    const colorSchemes = {
        excellent: {
            background: 'rgba(52, 191, 163, 0.15)',
            border: 'rgba(52, 191, 163, 0.4)',
            icon: '<i class="bi bi-trophy text-success"></i>'
        },
        good: {
            background: 'rgba(72, 187, 120, 0.15)',
            border: 'rgba(72, 187, 120, 0.4)',
            icon: '<i class="bi bi-graph-up-arrow text-success"></i>'
        },
        warning: {
            background: 'rgba(255, 184, 34, 0.15)',
            border: 'rgba(255, 184, 34, 0.4)',
            icon: '<i class="bi bi-exclamation-triangle text-warning"></i>'
        },
        neutral: {
            background: 'rgba(160, 174, 192, 0.15)',
            border: 'rgba(160, 174, 192, 0.4)',
            icon: '<i class="bi bi-lightbulb text-info"></i>'
        }
    };

    const scheme = colorSchemes[insightLevel] || colorSchemes.good;

    // Apply dynamic styling
    insightSection.style.cssText = `background: ${scheme.background}; border: 1px solid ${scheme.border};`;
    insightSection.querySelector('.insight-icon').innerHTML = scheme.icon;

    // Update the insight text with animation
    const textElement = document.getElementById('personalizedInsightText');
    textElement.style.opacity = '0';
    setTimeout(() => {
        textElement.textContent = insightText;
        textElement.style.opacity = '1';
        textElement.style.transition = 'opacity 0.3s ease';
    }, 150);
}

function showFallbackSuggestions() {
    // Get current LTV for realistic calculations
    const currentLtv = parseFloat(document.getElementById('ltvPerCustomer').textContent.replace(/[,$]/g, '')) || 234;

    // Calculate realistic impact values based on current LTV
    const quickWinImpact = Math.round(currentLtv * 0.18); // 18% of LTV
    const mediumWinImpact = Math.round(currentLtv * 0.32); // 32% of LTV
    const strategicWinImpact = Math.round(currentLtv * 0.48); // 48% of LTV
    const totalImpact = quickWinImpact + mediumWinImpact + strategicWinImpact;
    const percentageIncrease = Math.round((totalImpact / currentLtv) * 100);

    const fallback = {
        quickWin: {
            title: 'Implement smart email automation',
            impact: quickWinImpact,
            description: 'Set up behavioral triggers for cart abandonment and re-engagement',
            reasoning: 'Low-cost, high-impact strategy that works across all business stages'
        },
        mediumWin: {
            title: 'Launch customer referral system',
            impact: mediumWinImpact,
            description: 'Create incentivized referral program with tracking and rewards',
            reasoning: 'Leverages existing customers to reduce acquisition costs'
        },
        strategicWin: {
            title: 'Build customer success platform',
            impact: strategicWinImpact,
            description: 'Develop comprehensive onboarding and upselling system',
            reasoning: 'Creates systematic approach to maximizing customer lifecycle value'
        },
        combinedLtv: currentLtv + totalImpact,
        combinedIncrease: percentageIncrease,
        personalizedInsight: `Your business shows strong potential for ${percentageIncrease}% LTV growth. Start with email automation to build momentum, then scale with referral systems and comprehensive customer success operations.`
    };

    updateAiSuggestionsDisplay(fallback);

    // Update insight with fallback message
    const inputs = { currentLtv: currentLtv, industry: 'general' };
    updateDynamicInsightMessage(fallback, inputs);
}

function updateDynamicInsightMessage(aiSuggestions, inputs) {
    // Now using single combined insight section instead of separate message container

    // Calculate performance metrics
    const totalPotentialIncrease = aiSuggestions.quickWin.impact + aiSuggestions.mediumWin.impact + aiSuggestions.strategicWin.impact;
    const percentageIncrease = Math.round((totalPotentialIncrease / inputs.currentLtv) * 100);
    const quickWinImpact = aiSuggestions.quickWin.impact;
    const industry = inputs.industry.charAt(0).toUpperCase() + inputs.industry.slice(1);

    let message = '';
    let insightLevel = 'good';

    // Generate dynamic message based on AI suggestions and performance
    if (percentageIncrease >= 80) {
        insightLevel = 'excellent';
        message = `🚀 Massive growth potential detected! AI analysis shows you could boost LTV by ${percentageIncrease}% in ${industry}. Start with "${aiSuggestions.quickWin.title}" for quick +$${quickWinImpact} impact.`;
    } else if (percentageIncrease >= 50) {
        insightLevel = 'good';
        message = `📈 Strong optimization opportunities identified! Your ${industry} business could increase LTV by ${percentageIncrease}%. Priority action: "${aiSuggestions.quickWin.title}" for immediate +$${quickWinImpact} boost.`;
    } else if (percentageIncrease >= 25) {
        insightLevel = 'warning';
        message = `💡 Solid improvement potential found! AI suggests ${percentageIncrease}% LTV growth is achievable. Focus on "${aiSuggestions.quickWin.title}" to unlock +$${quickWinImpact} value.`;
    } else {
        insightLevel = 'neutral';
        message = `🎯 Growth opportunities available! Even modest improvements could yield ${percentageIncrease}% LTV increase. Start with "${aiSuggestions.quickWin.title}" for +$${quickWinImpact} impact.`;
    }

    // Add industry-specific context
    const industryInsights = {
        fashion: 'seasonal trends and style preferences',
        beauty: 'replenishment cycles and personalization',
        electronics: 'upgrade cycles and accessory sales',
        food: 'consumption patterns and convenience',
        home: 'project-based purchases and design trends',
        health: 'wellness journeys and subscription models',
        general: 'customer behavior patterns'
    };

    const industryContext = industryInsights[inputs.industry] || industryInsights.general;

    // Add industry context if space allows
    if (message.length < 200) {
        message += ` Leverage ${industryContext} for maximum impact.`;
    }

    // Use the AI personalized insight if available, otherwise use generated message
    const finalInsight = aiSuggestions.personalizedInsight || message;

    // Update the single combined insight section
    updatePersonalizedInsight(finalInsight, insightLevel);
}

function downloadStrategyGuide() {
    // Create a detailed strategy guide based on current suggestions
    const industry = document.getElementById('industry').value;
    const currentLtv = parseFloat(document.getElementById('ltvPerCustomer').textContent.replace(/[,$]/g, '')) || 0;

    const strategyContent = generateStrategyGuideContent(industry, currentLtv);

    // Create and download PDF-like content
    const blob = new Blob([strategyContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `LTV-Optimization-Strategy-${industry}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function generateStrategyGuideContent(industry, currentLtv) {
    return `
LTV OPTIMIZATION STRATEGY GUIDE
Generated by Adzeta AI - ${new Date().toLocaleDateString()}

CURRENT PERFORMANCE ANALYSIS
============================
Industry: ${industry.charAt(0).toUpperCase() + industry.slice(1)}
Current LTV per Customer: $${currentLtv.toFixed(2)}

RECOMMENDED ACTION PLAN
======================

PHASE 1: QUICK WINS (30-60 Days)
--------------------------------
${document.getElementById('quickWinTitle').textContent}
Expected Impact: ${document.getElementById('quickWinImpact').textContent}
Implementation: ${document.getElementById('quickWinDescription').textContent}

Action Steps:
1. Audit current customer communication touchpoints
2. Set up automated email sequences for new customers
3. Implement basic segmentation based on purchase behavior
4. A/B test subject lines and content for optimal engagement

PHASE 2: GROWTH OPPORTUNITIES (3-6 Months)
------------------------------------------
${document.getElementById('mediumWinTitle').textContent}
Expected Impact: ${document.getElementById('mediumWinImpact').textContent}
Implementation: ${document.getElementById('mediumWinDescription').textContent}

Action Steps:
1. Analyze customer purchase patterns and preferences
2. Develop cross-selling and upselling strategies
3. Create bundled product offerings
4. Implement personalized product recommendations

PHASE 3: STRATEGIC INITIATIVES (6-12 Months)
--------------------------------------------
${document.getElementById('strategicWinTitle').textContent}
Expected Impact: ${document.getElementById('strategicWinImpact').textContent}
Implementation: ${document.getElementById('strategicWinDescription').textContent}

Action Steps:
1. Conduct customer research and feedback collection
2. Develop premium service offerings
3. Create customer success and retention programs
4. Build long-term customer relationship strategies

COMBINED POTENTIAL
==================
Total Potential LTV: ${document.getElementById('combinedLtvPotential').textContent}
Expected Increase: ${document.getElementById('combinedLtvIncrease').textContent}

KEY SUCCESS METRICS TO TRACK
============================
- Customer Lifetime Value (LTV)
- Average Order Value (AOV)
- Repeat Purchase Rate
- Customer Retention Rate
- Net Promoter Score (NPS)
- Customer Acquisition Cost (CAC)

NEXT STEPS
==========
1. Prioritize Phase 1 initiatives for immediate implementation
2. Set up tracking and measurement systems
3. Schedule regular review and optimization cycles
4. Consider partnering with Adzeta for advanced AI-driven optimization

For personalized implementation support, contact Adzeta at:
https://designgear.in/adzeta/free-ad-audit.php

This strategy guide is generated based on your specific business data and industry benchmarks.
Results may vary based on implementation quality and market conditions.
`;
}
</script>
<?php include 'footer.php'; ?>