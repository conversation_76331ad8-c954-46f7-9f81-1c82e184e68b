/**
 * AI Content Processor for Editor.js
 * Converts AI-generated content with special markers into Editor.js blocks
 */

window.AIContentProcessor = {
    
    /**
     * Process AI-generated content and convert to Editor.js blocks
     */
    processAIContent(htmlContent) {
        console.log('🔄 Processing AI content with custom blocks...');
        
        const blocks = [];
        
        // Split content by custom block markers while preserving them
        const parts = this.splitContentByMarkers(htmlContent);
        
        parts.forEach(part => {
            if (part.trim() === '') return;
            
            // Check if this part is a custom block marker
            if (this.isCustomBlockMarker(part)) {
                const customBlock = this.parseCustomBlock(part);
                if (customBlock) {
                    blocks.push(customBlock);
                }
            } else {
                // Process regular HTML content
                const regularBlocks = this.processRegularHTML(part);
                blocks.push(...regularBlocks);
            }
        });
        
        console.log('✅ Generated', blocks.length, 'blocks from AI content');
        return blocks;
    },
    
    /**
     * Split content by custom block markers
     */
    splitContentByMarkers(content) {
        // Regex to match all custom block markers
        const markerRegex = /(\[(?:STYLED-CARD|STATISTIC|CTA-BLOCK|HIGHLIGHT):[^\]]*\].*?\[\/(?:STYLED-CARD|STATISTIC|CTA-BLOCK|HIGHLIGHT)\])/gs;
        
        const parts = [];
        let lastIndex = 0;
        let match;
        
        while ((match = markerRegex.exec(content)) !== null) {
            // Add content before the marker
            if (match.index > lastIndex) {
                parts.push(content.substring(lastIndex, match.index));
            }
            
            // Add the marker itself
            parts.push(match[1]);
            lastIndex = match.index + match[1].length;
        }
        
        // Add remaining content
        if (lastIndex < content.length) {
            parts.push(content.substring(lastIndex));
        }
        
        return parts;
    },
    
    /**
     * Check if a part is a custom block marker
     */
    isCustomBlockMarker(part) {
        return /^\[(?:STYLED-CARD|STATISTIC|CTA-BLOCK|HIGHLIGHT):/.test(part.trim());
    },
    
    /**
     * Parse custom block markers into Editor.js blocks
     */
    parseCustomBlock(markerContent) {
        const trimmed = markerContent.trim();
        
        // Parse STYLED-CARD
        if (trimmed.startsWith('[STYLED-CARD:')) {
            return this.parseStyledCard(trimmed);
        }
        
        // Parse STATISTIC
        if (trimmed.startsWith('[STATISTIC:')) {
            return this.parseStatistic(trimmed);
        }
        
        // Parse CTA-BLOCK
        if (trimmed.startsWith('[CTA-BLOCK:')) {
            return this.parseCTABlock(trimmed);
        }
        
        // Parse HIGHLIGHT (inline - convert to paragraph with highlight)
        if (trimmed.startsWith('[HIGHLIGHT:')) {
            return this.parseHighlight(trimmed);
        }
        
        return null;
    },
    
    /**
     * Parse styled card marker
     */
    parseStyledCard(content) {
        const match = content.match(/\[STYLED-CARD:([^\]]*)\](.*?)\[\/STYLED-CARD\]/s);
        if (!match) return null;
        
        const params = this.parseParameters(match[1]);
        const cardContent = match[2].trim();
        
        return {
            type: 'styledCard',
            data: {
                content: cardContent,
                backgroundColor: params.background || '#E6D8F2',
                textColor: params.color || '#2B0B3A',
                borderColor: params.border || '#FF4081'
            }
        };
    },
    
    /**
     * Parse statistic marker\n     */\n    parseStatistic(content) {\n        const match = content.match(/\\[STATISTIC:([^\\]]*)\\]\\[?\\/STATISTIC\\]/s);\n        if (!match) return null;\n        \n        const params = this.parseParameters(match[1]);\n        \n        return {\n            type: 'statisticsTool',\n            data: {\n                value: params.value || '100',\n                unit: params.unit || '%',\n                label: params.label || 'Statistic',\n                color: params.color || '#FF4081'\n            }\n        };\n    },\n    \n    /**\n     * Parse CTA block marker\n     */\n    parseCTABlock(content) {\n        const match = content.match(/\\[CTA-BLOCK:([^\\]]*)\\]\\[?\\/CTA-BLOCK\\]/s);\n        if (!match) return null;\n        \n        const params = this.parseParameters(match[1]);\n        \n        return {\n            type: 'ctaTool',\n            data: {\n                title: params.title || 'Ready to Get Started?',\n                description: params.description || 'Transform your marketing today',\n                buttonText: params.button || 'Start Free Trial',\n                buttonUrl: params.url || '/demo',\n                style: params.style || 'gradient',\n                alignment: params.alignment || 'center'\n            }\n        };\n    },\n    \n    /**\n     * Parse highlight marker (convert to paragraph with highlighted text)\n     */\n    parseHighlight(content) {\n        const match = content.match(/\\[HIGHLIGHT:([^\\]]*)\\](.*?)\\[\\/HIGHLIGHT\\]/s);\n        if (!match) return null;\n        \n        const params = this.parseParameters(match[1]);\n        const highlightText = match[2].trim();\n        const color = params.color || '#FF4081';\n        \n        // Create paragraph with highlighted span\n        const highlightedHTML = `<mark style=\"background: ${color}; color: white; padding: 2px 6px; border-radius: 4px; font-weight: 500;\">${highlightText}</mark>`;\n        \n        return {\n            type: 'paragraph',\n            data: {\n                text: highlightedHTML\n            }\n        };\n    },\n    \n    /**\n     * Parse parameters from marker attributes\n     */\n    parseParameters(paramString) {\n        const params = {};\n        \n        // Split by semicolon and parse key=value pairs\n        paramString.split(';').forEach(pair => {\n            const [key, value] = pair.split('=').map(s => s.trim());\n            if (key && value) {\n                params[key] = value;\n            }\n        });\n        \n        return params;\n    },\n    \n    /**\n     * Process regular HTML content into standard Editor.js blocks\n     */\n    processRegularHTML(htmlContent) {\n        const blocks = [];\n        \n        // Create temporary DOM element to parse HTML\n        const tempDiv = document.createElement('div');\n        tempDiv.innerHTML = htmlContent;\n        \n        // Process each child element\n        Array.from(tempDiv.children).forEach(element => {\n            const block = this.convertElementToBlock(element);\n            if (block) {\n                blocks.push(block);\n            }\n        });\n        \n        // If no child elements, treat as text content\n        if (blocks.length === 0 && htmlContent.trim()) {\n            const textContent = tempDiv.textContent.trim();\n            if (textContent) {\n                blocks.push({\n                    type: 'paragraph',\n                    data: {\n                        text: htmlContent.trim()\n                    }\n                });\n            }\n        }\n        \n        return blocks;\n    },\n    \n    /**\n     * Convert HTML element to Editor.js block\n     */\n    convertElementToBlock(element) {\n        const tagName = element.tagName.toLowerCase();\n        \n        switch (tagName) {\n            case 'h1':\n            case 'h2':\n            case 'h3':\n            case 'h4':\n            case 'h5':\n            case 'h6':\n                return {\n                    type: 'header',\n                    data: {\n                        text: this.preserveInlineHTML(element.innerHTML),\n                        level: parseInt(tagName.charAt(1))\n                    }\n                };\n                \n            case 'p':\n                const textContent = element.textContent.trim();\n                if (textContent) {\n                    return {\n                        type: 'paragraph',\n                        data: {\n                            text: this.preserveInlineHTML(element.innerHTML)\n                        }\n                    };\n                }\n                break;\n                \n            case 'ul':\n            case 'ol':\n                const items = Array.from(element.querySelectorAll('li')).map(li => \n                    this.preserveInlineHTML(li.innerHTML)\n                ).filter(item => item.trim());\n                \n                if (items.length > 0) {\n                    return {\n                        type: 'list',\n                        data: {\n                            style: tagName === 'ol' ? 'ordered' : 'unordered',\n                            items: items\n                        }\n                    };\n                }\n                break;\n                \n            case 'blockquote':\n                return {\n                    type: 'quote',\n                    data: {\n                        text: this.preserveInlineHTML(element.innerHTML),\n                        caption: ''\n                    }\n                };\n                \n            case 'div':\n                // Handle div with special styling\n                if (element.style.background || element.style.backgroundColor) {\n                    return {\n                        type: 'styledCard',\n                        data: {\n                            content: this.preserveInlineHTML(element.innerHTML),\n                            backgroundColor: element.style.backgroundColor || element.style.background || '#E6D8F2',\n                            textColor: element.style.color || '#2B0B3A',\n                            borderColor: this.extractBorderColor(element.style.border || element.style.borderLeft) || '#FF4081'\n                        }\n                    };\n                } else {\n                    // Convert div content to paragraph\n                    const textContent = element.textContent.trim();\n                    if (textContent) {\n                        return {\n                            type: 'paragraph',\n                            data: {\n                                text: this.preserveInlineHTML(element.innerHTML)\n                            }\n                        };\n                    }\n                }\n                break;\n        }\n        \n        return null;\n    },\n    \n    /**\n     * Preserve important inline HTML while cleaning dangerous tags\n     */\n    preserveInlineHTML(html) {\n        // Allow more tags for styled content\n        const allowedTags = ['strong', 'b', 'em', 'i', 'u', 'a', 'code', 'span', 'mark'];\n        const allowedAttributes = ['href', 'class', 'style', 'target', 'rel'];\n        \n        // Simple HTML preservation - keep formatting and styling\n        let cleaned = html;\n        \n        // Remove dangerous tags but keep content\n        cleaned = cleaned.replace(/<script[^>]*>[\\s\\S]*?<\\/script>/gi, '');\n        cleaned = cleaned.replace(/<style[^>]*>[\\s\\S]*?<\\/style>/gi, '');\n        \n        // Clean up extra whitespace\n        cleaned = cleaned.replace(/\\s+/g, ' ').trim();\n        \n        return cleaned;\n    },\n    \n    /**\n     * Extract border color from CSS border property\n     */\n    extractBorderColor(borderStyle) {\n        if (!borderStyle) return null;\n        \n        // Extract color from border style (e.g., \"4px solid #FF4081\")\n        const colorMatch = borderStyle.match(/#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgb\\([^)]+\\)|rgba\\([^)]+\\)/);\n        return colorMatch ? colorMatch[0] : null;\n    }\n};
