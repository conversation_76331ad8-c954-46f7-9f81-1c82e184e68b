/* Tighter line spacing for heading */

.heading-gradient br {

	display: block;

	content: "";

	margin-top: -8px;

}


/* Custom heading gradient for platform page */

.hero-section .heading-gradient {

	background-image: linear-gradient(166deg, #ffffff 0%, #f5f5f5 40%, #ff8cc6 60%, #df367d 75%, #ee5c46 100%);

	-webkit-background-clip: text;

	background-clip: text;

	-webkit-text-fill-color: transparent;

	color: transparent;

	text-shadow: 0 0 1px rgba(255, 255, 255, 0.05);


	letter-spacing: -1.5px;

	line-height: 1.25;

}

.modern-heading-gradient {

	-webkit-text-fill-color: transparent;

	background-image: linear-gradient(166deg, #2b2d3a 0%, #393b48 40%, #4b4d5b 60%, #f04b64 80%, #f35a77 100%);

	-webkit-background-clip: text;

	background-clip: text;

	color: transparent;

	/* letter-spacing: -0.0625rem; */

	font-weight: 700;

	line-height: 1.2;

	position: relative;

	display: inline-block;

}


@media (max-width: 767px) {


	.modern-heading-gradient {


		background-image: linear-gradient(132deg, #2b2d3a 0%, #393b48 40%, #4b4d5b 60%, #f04b64 80%, #f35a77 100%);

	}


}


.light-gradient-container,

.light-accent-gradient {

	position: absolute;

	inset: 0;
	/* shorthand for top: 0; right: 0; bottom: 0; left: 0; */

	z-index: 0;

	pointer-events: none;
	/* optional: allows clicks to pass through */

}


.light-gradient-container {

	background: linear-gradient(to bottom, #f2f0ee 0%, #f5f3f1 50%, #ffffff 100%);

}


.light-accent-gradient {

	background: radial-gradient(circle at center, rgba(233, 88, 161, 0.08) 0%, rgba(255, 255, 255, 0) 70%),

		radial-gradient(circle at top right, rgba(143, 118, 245, 0.05) 0%, rgba(255, 255, 255, 0) 70%);

	background-repeat: no-repeat;

	background-size: cover;

}


/* Compact Scale Tag */

.scale-tag-container {

	display: flex;

	justify-content: center;

	margin-bottom: 5px;

}


.scale-tag {

	display: inline-block;

	font-size: 12px;

	font-weight: 600;

	letter-spacing: 1px;

	text-transform: uppercase;

	color: #2e303d;

	/*  background: linear-gradient(to right, #e958a1, #ff5d74); */

	/*   padding: 3px 20px; */

	border-radius: 100px;

	/*     box-shadow: 0 2px 8px rgba(233, 88, 161, 0.2); */

	transition: all 0.3s ease;

	position: relative;

	overflow: hidden;

	letter-spacing: 2px;

}


.scale-tag:before {

	content: "";

	position: absolute;

	top: 0;

	left: -100%;

	width: 100%;

	height: 100%;

	background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);

	transition: all 0.6s ease;

}


.scale-tag:hover:before {

	left: 100%;

}


.cta-final::after {

	content: "";

	background-image: url('../images/free-ltv-bg.png');

	background-repeat: no-repeat;

	background-position: right center;

	background-size: 450px auto;

	position: absolute;

	top: 0;

	left: 0;

	right: 0;

	bottom: 0;

	z-index: 5;

	pointer-events: none;

}


/* AI Icon Animation */

.ai-icon {

	display: inline-flex;

	margin-right: 6px;

	vertical-align: middle;

	position: relative;

	top: -1px;

}


.ai-icon svg {

	width: 16px;

	height: 16px;

	filter: drop-shadow(0 0 3px rgba(233, 88, 161, 0.3));

}


.ai-icon .pulse {

	animation: ai-pulse 3s infinite ease-in-out;

}


.ai-icon .sparkle {

	animation: ai-sparkle 3s infinite ease-in-out;

	transform-origin: center;

}


/* Hero image pulsating animation */

.platform-animation-container img.apple-inspired-animation {

	animation: hero-image-pulse 3s infinite ease-in-out;

	transform-origin: center center;

}


@keyframes hero-image-pulse {

	0% {

		transform: scale(1);

		filter: brightness(1);

	}

	50% {

		transform: scale(1.02);

		filter: brightness(1.05);

	}

	100% {

		transform: scale(1);

		filter: brightness(1);

	}

}


/* Circuit animations with different timings for a more tech feel */

.ai-icon [class^="circuit"] {

	animation: ai-circuit-pulse 2s infinite ease-in-out;

	stroke-dasharray: 0;

	stroke-dashoffset: 0;

}


.ai-icon .circuit1 {
	animation-delay: 0.0s;
}

.ai-icon .circuit2 {
	animation-delay: 0.2s;
}

.ai-icon .circuit3 {
	animation-delay: 0.4s;
}

.ai-icon .circuit4 {
	animation-delay: 0.6s;
}

.ai-icon .circuit5 {
	animation-delay: 0.8s;
}

.ai-icon .circuit6 {
	animation-delay: 1.0s;
}

.ai-icon .circuit7 {
	animation-delay: 1.2s;
}

.ai-icon .circuit8 {
	animation-delay: 1.4s;
}

.ai-icon .circuit9 {
	animation-delay: 1.6s;
}

.ai-icon .circuit10 {
	animation-delay: 1.8s;
}

.ai-icon .circuit11 {
	animation-delay: 2.0s;
}

.ai-icon .circuit12 {
	animation-delay: 2.2s;
}


.ai-icon .grid {

	animation: ai-grid-pulse 3s infinite ease-in-out;

}


.ai-icon .core {

	animation: ai-core-pulse 2s infinite ease-in-out;

}


@keyframes ai-pulse {

	0% {

		opacity: 0.9;

		transform: scale(1);

		filter: brightness(1);

	}

	50% {

		opacity: 1;

		transform: scale(1.05);

		filter: brightness(1.2);

	}

	100% {

		opacity: 0.9;

		transform: scale(1);

		filter: brightness(1);

	}

}


@keyframes ai-sparkle {

	0% {

		opacity: 0.3;

		stroke-dasharray: 1 3;

	}

	50% {

		opacity: 0.7;

		stroke-dasharray: 2 2;

	}

	100% {

		opacity: 0.3;

		stroke-dasharray: 1 3;

	}

}


@keyframes ai-circuit-pulse {

	0% {

		opacity: 0.3;

		stroke-width: 1;

	}

	50% {

		opacity: 1;

		stroke-width: 2;

	}

	100% {

		opacity: 0.3;

		stroke-width: 1;

	}

}


@keyframes ai-grid-pulse {

	0% {

		opacity: 0.3;

		stroke-dasharray: 0.5 2.5;

	}

	50% {

		opacity: 0.7;

		stroke-dasharray: 1 2;

	}

	100% {

		opacity: 0.3;

		stroke-dasharray: 0.5 2.5;

	}

}


@keyframes ai-core-pulse {

	0% {

		opacity: 0.7;

		filter: brightness(0.9);

	}

	50% {

		opacity: 1;

		filter: brightness(1.3);

	}

	100% {

		opacity: 0.7;

		filter: brightness(0.9);

	}

}

/**

 * Enhanced Particles Effect CSS

 * Provides styling for the AI-themed particle animations

 */


/* Data flow lines */

.data-flow-container {

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	pointer-events: none;

	z-index: 2;

	overflow: hidden;

}


/* Subtle glow effects for particles container */

.ai-particles-glow {

	animation: subtleGlow 12s infinite alternate;

}


@keyframes subtleGlow {

	0% {

		filter: brightness(1);

	}

	50% {

		filter: brightness(1.1);

	}

	100% {

		filter: brightness(1);

	}

}


@keyframes subtlePulse {

	0% {

		opacity: 0.3;

		transform: scale(1);

	}

	100% {

		opacity: 0.4;

		transform: scale(1.05);

	}

}


/* Particle burst effect */

.particle-burst {

	position: absolute;

	width: 15px;

	height: 15px;

	border-radius: 50%;

	background: #e958a1;

	transform: scale(0);

	opacity: 0;

	pointer-events: none;

	z-index: 3;

	animation: particle-burst 1.5s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;

	box-shadow: 0 0 20px rgba(233, 88, 161, 0.8);

}


@keyframes particle-burst {

	0% {

		transform: scale(0);

		opacity: 0;

	}

	20% {

		transform: scale(1);

		opacity: 0.8;

	}

	80% {

		transform: scale(1.2);

		opacity: 0.4;

	}

	100% {

		transform: scale(2);

		opacity: 0;

	}

}


/* Glow effect for particles */

.ai-particles-glow canvas {

	filter: drop-shadow(0 0 5px rgba(233, 88, 161, 0.3));

}


.particles-glow-overlay {

	pointer-events: none;

	mix-blend-mode: screen;

}


/* Enhanced particle canvas styling */

#particles-style-03 {

	overflow: hidden;

}


#particles-style-03 canvas {

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

}


/* Homepage Chip Particles and Animation CSS */


.chip-image-container {

	position: relative;

	width: 100%;

	height: 100%;

	overflow: visible;

}


.chip-image-container img.absolute-middle-center {

	animation: pulse-rotate 15s infinite ease-in-out;

	transform-origin: center center;

}


@keyframes pulse-rotate {

	0% {

		transform: translate(-50%, -50%) scale(1) rotate(0deg);

		opacity: 0.8;

	}

	50% {

		transform: translate(-50%, -50%) scale(1.05) rotate(5deg);

		opacity: 1;

	}

	100% {

		transform: translate(-50%, -50%) scale(1) rotate(0deg);

		opacity: 0.8;

	}

}


.chip-image-container img.position-relative {

	animation: float-effect 6s infinite ease-in-out;

}


@keyframes float-effect {

	0% {

		transform: translateY(0);

	}

	50% {

		transform: translateY(-10px);

	}

	100% {

		transform: translateY(0);

	}

}


.particles-container {

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	pointer-events: none;

	overflow: hidden;

}


.particle {

	position: absolute;

	width: 6px;

	height: 6px;

	border-radius: 50%;

	background: #ff6a8d;

	opacity: 0;

	transform: scale(0);

	animation: moveParticle linear forwards;

	box-shadow: 0 0 8px rgba(255, 106, 141, 0.6);

}


@keyframes moveParticle {

	0% {

		opacity: 0;

		transform: scale(0);

	}

	10% {

		opacity: 0.8;

		transform: scale(1);

	}

	80% {

		opacity: 0.6;

		transform: scale(1);

	}

	100% {

		opacity: 0;

		transform: scale(0.5);

	}

}


@media (max-width: 991px) {

	.particle {

		width: 4px;

		height: 4px;

	}

}


/* Homepage Chip Particles End */


/* Homepage Standard vs Value-Based Bidding Starts */

.comparison-section-v2 {

	background: linear-gradient(to bottom, #fff 0, #f2f0ee 100%);

	overflow-x: hidden;

	position: relative;

	border-bottom: none;

	width: 100%
}

.comparison-section-v2 .container {

	position: relative;

	z-index: 2;

	padding-top: 30px;

	padding-bottom: 50px
}

.comparison-header {

	text-align: center;

	margin-bottom: 40px
}

.sub-tag {

	display: inline-block;

	padding: 5px 5px;

	background: linear-gradient(90deg, rgba(158, 114, 186, .08) 0, rgba(74, 158, 255, .08) 100%);

	color: #8f76f5;

	font-size: 12px;

	text-align: center;

	position: relative;

	display: block;

	font-weight: 500;

	letter-spacing: 1.2px;

	text-transform: uppercase;

	border-radius: 30px;

	margin-bottom: 20px
}

.comparison-toggle-mobile {

	display: flex;

	justify-content: center;

	margin-bottom: 30px
}

.modern-toggle-slider {

	position: relative;

	display: flex;

	width: 85%;

	max-width: 280px;

	height: 46px;

	background-color: #f8f9fa;

	border-radius: 23px;

	box-shadow: 0 2px 10px rgba(0, 0, 0, .05);

	margin: 0 auto;

	overflow: hidden;

	border: 1px solid rgba(240, 240, 245, .5);

	padding: 3px;

	z-index: 1
}

.toggle-option {

	flex: 1;

	display: flex;

	flex-direction: row;

	align-items: center;

	justify-content: center;

	padding: 0 15px;

	cursor: pointer;

	position: relative;

	z-index: 2;

	transition: color .3s ease;

	color: #777;

	font-size: 14px;

	font-weight: 500;

	border-radius: 20px;

	user-select: none
}

.toggle-option i {

	font-size: 14px;

	margin-right: 6px
}

.toggle-option.active {

	color: #fff;

	font-weight: 600
}

.slider-indicator {

	position: absolute;

	top: 3px;

	left: 3px;

	width: calc(50% - 3px);

	height: calc(100% - 6px);

	background: linear-gradient(135deg, #e958a1, #8f76f5);

	border-radius: 20px;

	transition: all .4s cubic-bezier(.25, 1, .5, 1);

	z-index: 1;

	box-shadow: 0 2px 8px rgba(143, 118, 245, .25);

	will-change: left, right;

	pointer-events: none
}

.toggle-option:not(.active):hover {

	color: #444
}

@keyframes indicatorPulse {

	0% {

		box-shadow: 0 2px 8px rgba(143, 118, 245, .25)
	}

	50% {

		box-shadow: 0 2px 12px rgba(143, 118, 245, .35)
	}

	100% {

		box-shadow: 0 2px 8px rgba(143, 118, 245, .25)
	}

}

.slider-indicator {

	animation: indicatorPulse 2s infinite
}

@media (max-width:360px) {

	.modern-toggle-slider {

		width: 95%;

		height: 42px
	}

	.toggle-option {

		font-size: 13px;

		padding: 0 10px
	}

	.toggle-option i {

		font-size: 12px;

		margin-right: 4px
	}

}

.comparison-content-area {

	margin-top: 30px
}

.mobile-comparison-content {

	margin-bottom: 30px;

	padding: 0 15px;

	max-width: 100%;

	overflow-x: hidden
}

.tab-pane {

	opacity: 0;

	transition: opacity .4s ease, transform .4s ease;

	display: none;

	transform: translateY(15px);

	pointer-events: none
}

.tab-pane.show {

	opacity: 1;

	display: block;

	transform: translateY(0);

	pointer-events: auto
}

.tab-pane .comparison-card {

	transition: all .5s ease
}

.mobile-comparison-content .comparison-card {

	margin-top: 15px;

	padding: 30px 25px;

	border-radius: 24px;

	border: none;

	background-color: #fff;

	position: relative;

	overflow: hidden
}

.mobile-comparison-content .comparison-card::before {

	content: '';

	position: absolute;

	top: 0;

	right: 0;

	width: 100%;

	height: 100%;

	background-image: radial-gradient(circle at top right, rgba(233, 88, 161, .03) 0, rgba(143, 118, 245, .02) 50%, transparent 70%);

	z-index: 0
}

.mobile-comparison-content .comparison-card h3 {

	font-size: 24px;

	font-weight: 700;

	margin-bottom: 5px;

	text-align: center;

	position: relative;

	z-index: 1
}

.mobile-comparison-content .comparison-card ul {

	position: relative;

	z-index: 1
}

.mobile-comparison-content .comparison-card li {

	padding: 12px 0;

	font-size: 15px;

	display: flex;

	align-items: flex-start;

	border-bottom: 1px solid rgba(240, 240, 245, .6)
}

.mobile-comparison-content .comparison-card li:last-child {

	border-bottom: none
}

.mobile-comparison-content .comparison-card .icon-wrapper {

	width: 24px;

	height: 24px;

	border-radius: 50%;

	display: flex;

	align-items: center;

	justify-content: center;

	margin-right: 12px;

	flex-shrink: 0
}

.mobile-comparison-content .comparison-card.traditional .icon-wrapper {

	background-color: #ff7878
}

.mobile-comparison-content .comparison-card.AdZeta .icon-wrapper {

	background: linear-gradient(135deg, #e958a1, #8f76f5)
}

.mobile-comparison-content .comparison-card.recommended {

	position: relative
}

.desktop-comparison-content.row {

	margin-left: -15px;

	margin-right: -15px
}

.comparison-card {

	background-color: #fff;

	border-radius: 32px;

	padding: 40px 30px;

	transition: all .4s ease;

	height: 100%;

	position: relative;

	overflow: hidden;

	margin-bottom: 20px
}

.comparison-card.AdZeta {

	background-color: #fff;

	position: relative;

	z-index: 1
}

.comparison-card h3,
.comparison-card li,
.comparison-card ul {

	position: relative;

	z-index: 2
}

.comparison-card:hover {

	transform: translateY(-6px);

	border-color: rgba(240, 240, 245, .9)
}

.comparison-card.AdZeta:hover {

	border-color: rgba(143, 118, 245, .25)
}

.desktop-comparison-content .comparison-card.recommended {

	position: relative
}

.comparison-card h3 {

	font-size: 24px;

	font-weight: 600;

	color: #1a1a1a;

	margin-bottom: 8px;

	text-align: center;

	letter-spacing: -.3px;

	line-height: 1.3
}

.comparison-card .sub-tag {

	display: inline-block;

	text-align: center;

	font-size: 10px;

	color: #fff;

	margin: 0 auto 35px;

	text-transform: uppercase;

	letter-spacing: .5px;

	font-weight: 500;

	background: linear-gradient(135deg, #ff6a8d, #8f76f5);

	padding: 3px 10px;

	border-radius: 12px;

	position: relative;

	left: 50%;

	transform: translateX(-50%);

	line-height: 1.4
}

.comparison-card .sub-tag-outline {

	display: inline-block;

	text-align: center;

	font-size: 10px;

	color: #777;

	margin: 0 auto 35px;

	text-transform: uppercase;

	letter-spacing: .5px;

	font-weight: 500;

	background: 0 0;

	padding: 2px 10px;

	border-radius: 12px;

	border: 1px solid rgba(120, 120, 120, .2);

	position: relative;

	left: 50%;

	transform: translateX(-50%);

	line-height: 1.4
}

.comparison-card ul {

	list-style: none;

	padding: 0;

	margin: 0
}

.comparison-card li {

	display: flex;

	align-items: flex-start;

	font-size: 15px;

	color: #333;

	line-height: 1.7;

	padding: 15px 0;

	border-bottom: 1px solid #f0f0f5;

	letter-spacing: .2px;

	font-weight: 400
}

.comparison-card li:last-child {

	border-bottom: none;

	padding-bottom: 0
}

.comparison-card li span.icon-wrapper {

	display: flex;

	align-items: center;

	justify-content: center;

	height: 22px;

	width: 22px;

	border-radius: 50%;

	margin-right: 14px;

	flex-shrink: 0;

	margin-top: 4px
}

.comparison-card li span.icon-wrapper i {

	font-size: .85rem;

	color: #fff;

	line-height: 1
}

.comparison-card.traditional li span.icon-wrapper {

	background-color: #ff7878;

	box-shadow: 0 2px 5px rgba(255, 107, 107, .3)
}

.comparison-card.AdZeta li span.icon-wrapper {

	background: linear-gradient(135deg, #e958a1, #8f76f5);

	box-shadow: 0 2px 5px rgba(143, 118, 245, .3)
}

@keyframes fadeInItem {

	from {

		opacity: 0;

		transform: translateY(8px)
	}

	to {

		opacity: 1;

		transform: translateY(0)
	}

}

.mobile-comparison-content .tab-pane.active .comparison-card li {

	animation: fadeInItem .35s ease forwards;

	opacity: 0
}

.mobile-comparison-content .tab-pane.active .comparison-card li:nth-child(1) {

	animation-delay: 50ms
}

.mobile-comparison-content .tab-pane.active .comparison-card li:nth-child(2) {

	animation-delay: .1s
}

.mobile-comparison-content .tab-pane.active .comparison-card li:nth-child(3) {

	animation-delay: .15s
}

.mobile-comparison-content .tab-pane.active .comparison-card li:nth-child(4) {

	animation-delay: .2s
}

@media (min-width:768px) {

	.desktop-comparison-content.row {

		gap: 0
	}

	.desktop-comparison-content>.col-md-6 {

		padding-left: 15px;

		padding-right: 15px
	}

	.desktop-comparison-content .comparison-card {

		padding: 45px;

		opacity: 1;

		transform: translateY(0) scale(1);

		background-color: #fff
	}

	.desktop-comparison-content .comparison-card:hover {

		transform: translateY(-6px)
	}

	.desktop-comparison-content .comparison-card.AdZeta.recommended {

		border-color: rgba(143, 118, 245, .2)
	}

	.desktop-comparison-content .comparison-card.AdZeta.recommended:hover {

		border-color: rgba(143, 118, 245, .5)
	}

	.desktop-comparison-content .comparison-card li {

		opacity: 1
	}

}

@media (min-width:992px) {

	.comparison-section-v2 {

		padding: 20px 0
	}

	.comparison-header {

		margin-bottom: 70px
	}

	.desktop-comparison-content>.col-md-6 {

		padding-left: 20px;

		padding-right: 20px
	}

	.desktop-comparison-content .comparison-card {

		padding: 50px
	}

}

@media (min-width:1200px) {

	.desktop-comparison-content>.col-md-6 {

		padding-left: 25px;

		padding-right: 25px
	}

	.desktop-comparison-content .comparison-card {

		padding: 55px
	}

}

/* Homepage Standard vs Value-Based Bidding Ends */


/* Homepage steps start */

/* Apple-inspired cutting-edge step number styling */

.homepage .process-step-icon {

	background: linear-gradient(135deg, #f8f8f8, #ffffff) !important;

	box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.03), inset 0 0 0 1px rgba(255, 255, 255, 0.9) !important;

	border: none !important;

	transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);

	position: relative;

	overflow: visible;

}


.homepage .process-step-icon::before {

	content: '';

	position: absolute;

	top: -1px;

	left: -1px;

	right: -1px;

	bottom: -1px;

	border-radius: 50%;

	border: 1px solid rgba(0, 0, 0, 0.08);

	z-index: 0;

}


.homepage .process-step-icon .number {

	background: linear-gradient(135deg, #ff6a8d, #f45888);

	-webkit-background-clip: text;

	-webkit-text-fill-color: transparent;

	background-clip: text;

	color: transparent;

	font-weight: 600 !important;

	font-size: 16px !important;

	position: relative;

	z-index: 2;

	transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);

}


.hover-box:hover .process-step-icon {

	transform: translateY(-3px) scale(1.02);

	box-shadow: 0 14px 28px rgba(0, 0, 0, 0.08), 0 4px 10px rgba(0, 0, 0, 0.04), inset 0 0 0 1px rgba(255, 255, 255, 0.9) !important;

}


.hover-box:hover .process-step-icon .number {

	background: linear-gradient(135deg, #ff7c9b, #ff5c82);

	-webkit-background-clip: text;

	background-clip: text;

}


.homepage .progress-step-separator {

	background: linear-gradient(to bottom, #f45888 0%, rgba(244, 88, 136, 0.3) 100%) !important;

	width: 2px !important;

	opacity: 0.8 !important;

	height: calc(100% - 70px) !important;

	top: 70px !important;

	border-radius: 2px;

}


.homepage .box-overlay {

	display: none;

}


/* Process step content refinements */

.process-content {

	padding-left: 35px !important;

}


@media (max-width: 767px) {

	.homepage .process-step-icon {

		height: 50px !important;

		width: 50px !important;

	}


	.homepage .progress-step-separator {

		height: calc(100% - 60px) !important;

		top: 60px !important;

	}

}


/* Homepage steps ends */

/* Homepage FAQ styling start*/

.faq-section {


	padding: 80px 0;

	position: relative;

	overflow: hidden;

}


.faq-section .text-gradient-pink-blue {

	background: linear-gradient(to right, #f45888, #4a7ab5);

	-webkit-background-clip: text;

	-webkit-text-fill-color: transparent;

	background-clip: text;

}


.apple-style-faq {

	margin-top: 30px;

}


.faq-item {

	background-color: #ffffff;

	border-radius: 12px;

	margin-bottom: 16px;

	overflow: hidden;

	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

	transition: all 0.3s ease;

	display: flex;

	flex-direction: column-reverse;

}


.faq-item:hover {

	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

	transform: translateY(-2px);

}


.faq-header {

	padding: 0;

	border: none;

}


.faq-toggle {

	display: block;

	padding: 0;

	text-decoration: none;

	width: 100%;

}


.faq-title {

	display: flex;

	align-items: center;

	padding: 24px 30px;

	position: relative;

}


.faq-number {

	font-size: 14px;

	font-weight: 600;

	color: #f45888;

	margin-right: 20px;

	opacity: 0.8;

	min-width: 28px;

}


.faq-title h3 {

	font-size: 18px;

	font-weight: 500;

	color: #333;

	margin: 0;

	flex-grow: 1;

}


.faq-icon {

	position: relative;

	width: 20px;

	height: 20px;

}


.faq-icon span {

	position: absolute;

	background-color: #333;

	transition: all 0.3s ease;

}


.faq-icon span:first-child {

	top: 9px;

	left: 0;

	width: 100%;

	height: 2px;

}


.faq-icon span:last-child {

	top: 0;

	left: 9px;

	width: 2px;

	height: 100%;

}


.faq-toggle[aria-expanded="true"] .faq-icon span:last-child {

	transform: rotate(90deg);

	opacity: 0;

}


.faq-body {

	padding: 0 30px 24px 78px;

	order: 2;

}


.faq-header {

	order: 1;

}


.faq-body p {

	margin: 0;

	color: #666;

	line-height: 1.6;

}


.faq-body p strong {

	color: #333;

	font-weight: 500;

}


.faq-item {

	transition: all 0.3s ease;

}


.faq-item.active {

	background-color: #ffffff;

}


@media (max-width: 767px) {

	.faq-title {

		padding: 20px;

	}


	.faq-body {

		padding: 0 20px 20px 60px;

	}


	.faq-number {

		margin-right: 12px;

		min-width: 24px;

	}

}

/* Homepage FAQ styling ends*/

/* Homepage Features Section Start*/

.high-tech-features-section {

	background: linear-gradient(180deg, #1B0B24 0%, #1F1133 100%);

	position: relative;

	overflow: hidden;

}


.tech-bg-grid {

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	background-image:

		radial-gradient(circle at 20% 30%, rgba(244, 88, 136, 0.08) 0%, rgba(31, 33, 42, 0) 70%),

		radial-gradient(circle at 80% 80%, rgba(143, 118, 245, 0.05) 0%, rgba(0, 0, 0, 0) 70%);

	opacity: 0.8;

	z-index: 1;

}


.tech-grid-lines {

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	background-size: 30px 30px;

	background-image:

		linear-gradient(to right, rgba(255, 255, 255, 0.03) 1px, transparent 1px),

		linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);

	opacity: 0.4;

	z-index: 1;

}


/* Add subtle animated particles */

.high-tech-features-section:before {

	content: '';

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	background-image:

		radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),

		radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.02) 1px, transparent 1px),

		radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),

		radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.02) 1px, transparent 1px),

		radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.03) 1px, transparent 1px);

	background-size:

		100px 100px,

		150px 150px,

		200px 200px,

		250px 250px,

		300px 300px;

	animation: particleMovement 60s linear infinite;

	opacity: 0.5;

	z-index: 1;

}


@keyframes particleMovement {

	0% {

		background-position:

			0 0,

			0 0,

			0 0,

			0 0,

			0 0;

	}

	100% {

		background-position:

			100px 100px,

			150px 150px,

			200px 200px,

			250px 250px,

			300px 300px;

	}

}


.high-tech-features-content {

	position: relative;

	z-index: 2;

}


.high-tech-features-section .bg-dark-slate-blue {

	background-color: rgba(27, 11, 36, 0.7) !important;

	backdrop-filter: blur(15px);

	-webkit-backdrop-filter: blur(15px);

	border: 1px solid rgba(255, 255, 255, 0.07);

	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);

}


.high-tech-features-section .feature-box {

	transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);

	position: relative;

	z-index: 1;

	overflow: hidden;

}


.high-tech-features-section .feature-box:before {

	content: '';

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	background: linear-gradient(135deg, rgba(244, 88, 136, 0.03) 0%, rgba(143, 118, 245, 0.03) 100%);

	z-index: -1;

	opacity: 0;

	transition: opacity 0.3s ease;

}


.high-tech-features-section .feature-box:hover {

	transform: translateY(-5px);

	box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25),

		0 0 0 1px rgba(255, 255, 255, 0.1);

	border-color: rgba(255, 255, 255, 0.1);

}


.high-tech-features-section .feature-box:hover:before {

	opacity: 1;

}


.high-tech-features-section .feature-box-overlay.bg-cornflower-blue {

	background: linear-gradient(135deg, rgba(244, 88, 136, 0.15) 0%, rgba(143, 118, 245, 0.15) 100%) !important;

	opacity: 0;

	transition: opacity 0.3s ease;

}


.high-tech-features-section .feature-box:hover .feature-box-overlay {

	opacity: 1;

}


.high-tech-features-section .text-light-opacity {

	color: rgba(255, 255, 255, 0.7);

	font-size: 14px;

	line-height: 1.6;

	letter-spacing: 0.2px;

	margin-bottom: 0;

}


.high-tech-features-section .feature-box-content span {

	position: relative;

	display: inline-block;

	margin-bottom: 10px !important;

	font-weight: 600 !important;

}


.high-tech-features-section .border-radius-100px.bg-dark-slate-blue {

	background: linear-gradient(90deg, rgba(244, 88, 136, 0.2) 0%, rgba(143, 118, 245, 0.2) 100%) !important;

	border: none;

}


.high-tech-features-section .feature-box-icon {

	position: relative;

	margin-right: 20px;

}


.high-tech-features-section .line-icon-Target:before,

.high-tech-features-section .line-icon-Money-2:before,

.high-tech-features-section .line-icon-Shop-4:before,

.high-tech-features-section .line-icon-Security-Block:before {

	background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);

	-webkit-background-clip: text;

	-webkit-text-fill-color: transparent;

	background-clip: text;

	opacity: 1;

	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));

}


.high-tech-features-section .bi-chat-text:before {

	background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);

	-webkit-background-clip: text;

	-webkit-text-fill-color: transparent;

	background-clip: text;

	opacity: 1;

	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));

}


.high-tech-features-section .icon-double-large {

	font-size: 38px;

	position: relative;

	z-index: 2;

}


.high-tech-features-section a {

	transition: all 0.3s ease;

}


.high-tech-features-section a:hover {

	opacity: 0.8;

}


.high-tech-features-section .feature-box:hover {

	box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2),

		0 0 20px rgba(244, 88, 136, 0.1),

		0 0 30px rgba(143, 118, 245, 0.05);

}


/* Responsive adjustments */

@media (max-width: 991px) {

	.high-tech-features-section .icon-double-large {

		font-size: 32px;

	}

}


@media (max-width: 767px) {

	.high-tech-features-section .icon-double-large {

		font-size: 28px;

	}

}

/* Homepage Features Section Ends*/

/* Homepage Case Study Cards starts */

.case-study-card {

	transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);

	background: linear-gradient(145deg, #ffffff 0%, #f8faff 85%, #f0f5ff 100%);

	border-radius: 16px;

	overflow: hidden;

	border: none;

	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02), 0 6px 20px rgba(0, 0, 0, 0.03);

	height: 100%;

	display: grid;

	grid-template-rows: auto 1fr auto;

	position: relative;

}


.case-study-card:before {

	content: '';

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(240, 245, 255, 0.5) 100%);

	opacity: 0;

	transition: opacity 0.4s ease;

	pointer-events: none;

	z-index: 1;

}


.case-study-card:hover {

	transform: translateY(-2px);

	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);

	background: linear-gradient(145deg, #ffffff 0%, #f5f8ff 85%, #eef3ff 100%);

}


.case-study-card:hover:before {

	opacity: 1;

}


.case-study-card .logo-link {

	display: flex;

	align-items: center;

	justify-content: center;

	padding: 24px 30px;

	background: transparent;

	text-decoration: none;

	position: relative;

	z-index: 2;

}


.case-study-card .logo-link:after {

	content: '';

	position: absolute;

	bottom: 0;

	left: 24px;

	right: 24px;

	height: 1px;

	background: linear-gradient(90deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.08) 50%, rgba(0, 0, 0, 0.04) 100%);

}


.case-study-card .logo-link img {

	height: 55px;

	width: 100%;

	object-fit: contain;

	max-width: 100%;

}


.case-study-card .logo-link .arrow-icon {

	position: absolute;

	top: 20px;

	right: 20px;

	color: rgba(0, 0, 0, 0.15);

	font-size: 20px;

	transition: all 0.3s ease;

}


.case-study-card .logo-link:hover .arrow-icon {

	color: rgba(0, 0, 0, 0.4);

	transform: translateX(2px);

}


.case-study-card .bg-white {

	background: transparent !important;

	padding: 0;

	display: flex;

	flex-direction: column;

	position: relative;

	z-index: 2;

}


.case-study-card .ps-30px,

.case-study-card .pe-30px {

	padding-left: 24px !important;

	padding-right: 24px !important;

}


.case-study-card .pt-30px {

	padding-top: 20px !important;

}


.case-study-card .pb-20px {

	padding-bottom: 20px !important;

}


/* Homepage Case Study Cards ends */


/* Homepage Metrics Start */


.key-metric {

	margin: 0 0 20px;

	position: relative;

	display: grid;

	grid-template-columns: auto 1fr;

	align-items: center;

	gap: 12px;

}


.key-metric-badge {

	display: inline-flex;

	align-items: center;

	justify-content: center;

	background-color: rgba(244, 88, 136, 0.08);

	border-radius: 4px;

	padding: 4px 8px;

	font-size: 11px;

	font-weight: 600;

	color: #f45888;

	text-transform: uppercase;

	letter-spacing: 0.5px;

	position: relative;

}


.key-metric-badge:before {

	content: '';

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);

	border-radius: 4px;

}


.key-metric-badge.reduction {

	background-color: rgba(74, 122, 181, 0.08);

	color: #4a7ab5;

}


.key-metric-value {

	font-size: 28px;

	font-weight: 700;

	line-height: 1;

	letter-spacing: -0.5px;

	color: #f45888;

	display: inline-flex;

	align-items: center;

	position: relative;

}


.key-metric-value.reduction {

	color: #4a7ab5;

}


.case-study-card p {

	color: #444;

	font-size: 14px !important;

	line-height: 1.6 !important;

	margin-bottom: 0;

	position: relative;

	padding: 0;

	font-weight: 400;

	font-style: italic;

}


.client-info-container {

	display: flex;

	align-items: center;

	margin-top: 20px;

	padding: 16px 24px;

	background: linear-gradient(to right, rgba(240, 245, 255, 0.7) 0%, rgba(248, 250, 255, 0.7) 50%, rgba(240, 245, 255, 0.7) 100%);

	border-top: 1px solid rgba(0, 0, 0, 0.04);

	position: relative;

	z-index: 2;

}


.client-info-container:before {

	content: '';

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 1px;

	background: linear-gradient(90deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.06) 50%, rgba(0, 0, 0, 0.02) 100%);

}


.client-photo {

	width: 36px;

	height: 36px;

	border-radius: 50%;

	object-fit: cover;

	margin-right: 12px;

	border: 2px solid #fff;

	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

}


.client-info {

	display: flex;

	flex-direction: column;

}


.client-name {

	font-size: 14px;

	font-weight: 600;

	color: #333;

	line-height: 1.3;

}


.client-title {

	font-size: 12px;

	color: #666;

	line-height: 1.3;

	margin-top: 2px;

}


.swiper-slide {

	height: auto;

	padding: 4px;

}


.swiper-wrapper {

	align-items: stretch;

}


.services-box-style-03 {

	height: 100%;

	display: flex;

	flex-direction: column;

}


.services-box-style-03 .bg-white {

	flex: 1;

	display: flex;

	flex-direction: column;

}


.services-box-style-03 .bg-white>div:first-child {

	flex: 1;

	display: flex;

	flex-direction: column;

}


.data-viz-element {

	position: absolute;

	background: linear-gradient(135deg, rgba(244, 88, 136, 0.03) 0%, rgba(244, 88, 136, 0) 70%);

	border-radius: 50%;

	z-index: 0;

	opacity: 0.5;

}


.data-viz-element.circle-1 {

	width: 120px;

	height: 120px;

	top: -60px;

	right: -60px;

}


.data-viz-element.circle-2 {

	width: 80px;

	height: 80px;

	bottom: 40px;

	left: -40px;

	background: linear-gradient(135deg, rgba(74, 122, 181, 0.03) 0%, rgba(74, 122, 181, 0) 70%);

}


.data-point {

	position: absolute;

	width: 4px;

	height: 4px;

	border-radius: 50%;

	background-color: rgba(244, 88, 136, 0.2);

	z-index: 0;

}


.data-point.point-1 {

	top: 30px;

	right: 40px;

}


.data-point.point-2 {

	top: 50px;

	right: 60px;

}


.data-point.point-3 {

	top: 40px;

	right: 80px;

}


.data-point.point-4 {

	bottom: 60px;

	left: 30px;

	background-color: rgba(74, 122, 181, 0.2);

}


.data-point.point-5 {

	bottom: 40px;

	left: 50px;

	background-color: rgba(74, 122, 181, 0.2);

}


.slider-navigation-style-04 {

	width: 48px !important;

	height: 48px !important;

	border-radius: 50% !important;

	background-color: transparent !important;

	border: 1px solid rgba(0, 0, 0, 0.1) !important;

	display: flex !important;

	align-items: center !important;

	justify-content: center !important;

	font-size: 14px !important;

	color: #333 !important;

	transition: all 0.2s ease !important;

	box-shadow: none !important;

}


.slider-navigation-style-04:hover {

	border-color: rgba(0, 0, 0, 0.2) !important;

	transform: scale(1.05) !important;

}


/* .btn-link.btn-hover-animation-switch {

    padding: 0 !important;

    background: none !important;

    border: none !important;

    box-shadow: none !important;

    font-size: 15px !important;

    font-weight: 500 !important;

    color: #333 !important;

    transition: all 0.2s ease !important;

} */


.btn-link.btn-hover-animation-switch:hover {

	color: #f45888 !important;

}


.btn-link.btn-hover-animation-switch .btn-icon {

	font-size: 15px !important;

	margin-left: 5px !important;

}


.btn-link.btn-hover-animation-switch:hover .btn-icon {

	transform: translateX(3px) !important;

	transition: transform 0.2s ease !important;

}


.metrics-section {

	background: linear-gradient(to bottom, #f2f0ee 0%, #f6f4f9 100%);

	position: relative;

	border-top: none;
	overflow: hidden;
}


.metric-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24px;
	padding: 28px;
	height: 100%;
	position: relative;
	border: 1px solid transparent;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	/* GPU acceleration */
	will-change: transform;
	transform: translateZ(0);
	/* Single transition for better performance */
	transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	opacity: 0;
	animation: fadeInUp 0.6s ease-out forwards;
}


/* Staggered animation delays */
.metric-card:nth-child(1) {
	animation-delay: 0.1s;
}

.metric-card:nth-child(2) {
	animation-delay: 0.2s;
}

.metric-card:nth-child(3) {
	animation-delay: 0.3s;
}

.metric-card:nth-child(4) {
	animation-delay: 0.4s;
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px) translateZ(0);
	}

	to {
		opacity: 1;
		transform: translateY(0) translateZ(0);
	}
}

.metric-card:hover {
	transform: translateY(-8px) translateZ(0);
}


/* Simplified gradient border effect */
.metric-card::before {
	content: '';
	position: absolute;
	inset: 0;
	border-radius: 24px;
	padding: 1px;
	background: linear-gradient(135deg,
			rgba(244, 88, 136, 0) 0%,
			rgba(244, 88, 136, 0.3) 25%,
			rgba(106, 140, 175, 0.3) 75%,
			rgba(106, 140, 175, 0) 100%);
	mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
	mask-composite: xor;
	-webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
	-webkit-mask-composite: xor;
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}

.metric-card:hover::before {
	opacity: 1;
}


.metric-title {

	font-size: 16px;

	font-weight: 600;

	color: #666;

	margin: 0 0 24px 0;

	letter-spacing: 0.3px;

	display: block;

	transition: color 0.3s ease;

}


.metric-card:hover .metric-title {

	color: #444;

}


.metric-badge {

	font-size: 11px;

	font-weight: 500;

	padding: 3px 8px;

	border-radius: 12px;

	background-color: #f5f5f7;

	color: #999;

	margin-left: 8px;

	display: inline-block;

	vertical-align: middle;

}


.counter-wrapper {

	display: flex;

	align-items: baseline;

	margin-bottom: 8px;

}


.counter-number {

	font-size: 56px;

	line-height: 1;

	font-weight: 700;

	color: #5a5a5a;

	transition: color 0.3s ease;

}


.unit {

	font-size: 32px;

	font-weight: 600;

	margin-left: 2px;

	color: #5a5a5a;

	transition: color 0.3s ease;

}


.metric-card:hover .counter-number,

.metric-card:hover .unit {

	color: #333;

}


.trend {

	font-size: 13px;

	font-weight: 500;

	display: inline-flex;

	align-items: center;

	margin-bottom: 16px;

	transition: all 0.3s ease;

}


.trend i {

	margin-right: 4px;

	font-size: 12px;

	transition: color 0.3s ease, transform 0.2s ease;

}


.trend-up {

	color: #f45888;

}


.trend-down {

	color: #6a8caf;

}


.text-pink {

	color: #f45888 !important;

	transition: color 0.3s ease;

}


.text-blue {

	color: #6a8caf !important;

	transition: color 0.3s ease;

}


.metric-card:hover .trend-up i,

.metric-card:hover .text-pink {

	color: #ff3a7a !important;

}


.metric-card:hover .trend-down i,

.metric-card:hover .text-blue {

	color: #4a7ab5 !important;

}


.metric-card:hover .trend i {

	transform: scale(1.1);

}


.feather {

	stroke: currentColor;

	stroke-width: 2;

	stroke-linecap: round;

	stroke-linejoin: round;

	fill: none;

}


.metric-description {

	font-size: 14px;

	line-height: 1.5;

	color: #777;

	margin-bottom: 20px;

	transition: color 0.3s ease;

}


.metric-card:hover .metric-description {

	color: #555;

}


.metric-graph {

	position: relative;

	height: 40px;

	margin-top: auto;


	overflow: hidden;

}


.metric-graph.animate-in {

	opacity: 1;

	transition: opacity 0.6s ease;

	transition-delay: 0.2s;

}


.metric-graph svg {

	width: 100%;

	height: 100%;

}


.metric-graph .line {

	stroke-width: 2.5;
	fill: none;
	stroke-linecap: round;
	stroke-linejoin: round;
	/* Remove CSS animations - will be controlled by JS */
	stroke-dasharray: 300;
	stroke-dashoffset: 300;
	transition: stroke-width 0.3s ease;

}


.metric-card:hover .metric-graph .line {
	stroke-width: 3;
}


.positive-line {
	stroke: rgba(244, 88, 136, 0.8);
}

.reduction-line {
	stroke: rgba(106, 140, 175, 0.8);
}

.metric-graph .area {
	opacity: 0;
	transition: opacity 0.8s ease;
}


.metrics-footer {

	text-align: center;

	color: #8e8e93;

	font-size: 12px;

	margin-top: 15px;

	padding: 0;

	background-color: transparent;

}


.metrics-footer a {

	color: #6a8caf;

	text-decoration: none;

	font-weight: 500;

	position: relative;

	padding-bottom: 1px;

}


.metrics-footer a:hover {

	color: #4a7ab5;

	text-decoration: none;

}


.metrics-footer a::after {

	content: '';

	position: absolute;

	bottom: 0;

	left: 0;

	width: 0;

	height: 1px;

	background-color: #4a7ab5;

	transition: width 0.2s ease;

}


.metrics-footer a:hover::after {

	width: 100%;

}


.feature-box.hover-box.dark-hover:hover h2[style*="background"] {

	background: none !important;

	-webkit-background-clip: initial !important;

	background-clip: initial !important;

	-webkit-text-fill-color: #ffffff !important;

	color: #ffffff !important;

}


.feature-box.hover-box.dark-hover:hover .text-dark-gray {

	color: #ffffff !important;

}


.feature-box.hover-box.dark-hover .content-slide-up {

	padding: 40px !important;
	width: 100%;
}


/* Responsive adjustments */

@media (max-width: 991px) {

	.metrics-section {

		padding: 50px 0;

	}


	.counter-number {

		font-size: 48px;

	}


	.unit {

		font-size: 28px;

	}


	.metric-card {

		margin-bottom: 20px;

	}

}


@media (max-width: 767px) {

	.metrics-section {

		padding: 40px 0;

	}


	.counter-number {

		font-size: 42px;

	}


	.unit {

		font-size: 24px;

	}


	.metric-graph {

		height: 30px;

	}


	.metric-card {

		padding: 24px;

	}


	.metrics-footer {

		margin-top: 5px;

		font-size: 11px;

	}


	.metrics-footer {

		flex-direction: column;

		align-items: center;

		gap: 5px;

	}

}

/* Homepage Metrics Ends */


/* platform page testimonial start */


.testimonial-section {

	margin: 40px 0;

	position: relative;

}


.simple-testimonial {

	width: 100%;

	margin: 0 auto;

	background-color: #ffffff;

	border-radius: 16px;

	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);

	padding: 40px;

	position: relative;

	overflow: hidden;

}


.testimonial-content {

	display: grid;

	grid-template-columns: 280px 1fr;

	gap: 50px;

}


.testimonial-left {

	display: grid;

	grid-template-columns: auto 1fr;

	grid-template-areas:

		"pic info"

		"link link";

	column-gap: 20px;

	align-items: center;

}


.profile-pic {

	width: 90px;

	height: 90px;

	border-radius: 50%;

	overflow: hidden;

	background-color: #f8f8f8;

	border: 3px solid #fff;

	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);

	grid-area: pic;

}


.profile-pic img {

	width: 100%;

	height: 100%;

	object-fit: cover;

}


.profile-info {

	grid-area: info;

	display: flex;

	flex-direction: column;

}


.testimonial-author {

	font-size: 18px;

	font-weight: 600;

	color: #333;

	margin-bottom: 6px;

}


.testimonial-position {

	font-size: 14px;

	color: #666;

	margin-bottom: 6px;

}


.testimonial-company {

	display: flex;

	align-items: center;

	font-size: 14px;

	color: #666;

	margin-bottom: 0;

	padding: 6px 12px;

	background-color: #f9f9f9;

	border-radius: 20px;

	display: inline-flex;

	width: fit-content;

}


.testimonial-company img {

	height: 16px;

	margin-right: 8px;

	opacity: 0.9;

}


.case-study-link {

	display: inline-flex;

	align-items: center;

	font-size: 14px;

	font-weight: 500;

	color: #f45888;

	text-decoration: none;

	transition: all 0.2s ease;

	grid-area: link;

	margin-top: 25px;

	width: fit-content;

	position: relative;

}


.case-study-link:hover {

	color: #e03a6e;

}


.case-study-link img {

	transition: transform 0.2s ease;

}


.case-study-link:hover img {

	transform: translate(2px, -2px);

}


.testimonial-right {

	display: flex;

	flex-direction: column;

}


.testimonial-quote {

	font-size: 16px;

	line-height: 1.6;

	color: #333;

	margin-bottom: 30px;

	font-weight: 400;

	font-style: italic;

	position: relative;

}


.testimonial-metrics {

	display: grid;

	grid-template-columns: repeat(3, 1fr);

	gap: 30px;

	margin-top: 30px;

	position: relative;

}


.testimonial-metrics:before {

	content: "";

	position: absolute;

	top: -15px;

	left: 0;

	right: 0;

	height: 1px;

	background-color: rgba(0, 0, 0, 0.06);

}


.metric-item {

	display: flex;

	flex-direction: column;

	align-items: flex-start;

}


.metric-label {

	font-size: 13px;

	color: #666;

	margin-bottom: 6px;

	font-weight: 400;

	letter-spacing: 0.2px;

}


.testimonial-right .metric-value {

	font-size: 28px;

	font-weight: 600;

	color: #f45888;

	line-height: 1;

}


@media (max-width: 991px) {

	.testimonial-metrics {

		grid-template-columns: repeat(3, 1fr);

		gap: 20px;

	}


	.metric-value {

		font-size: 24px;

	}

}


@media (max-width: 991px) {

	.testimonial-content {

		grid-template-columns: 240px 1fr;

		gap: 30px;

	}

}


@media (max-width: 767px) {

	.simple-testimonial {

		padding: 30px;

	}


	.testimonial-content {

		grid-template-columns: 1fr;

		gap: 30px;

	}


	.testimonial-left {

		grid-template-columns: auto 1fr;

		grid-template-areas:

			"pic info"

			"link link";

		column-gap: 15px;

	}


	.profile-pic {

		width: 70px;

		height: 70px;

	}


	.case-study-link {

		margin-top: 20px;

		width: auto;

		justify-content: center;

	}


	.testimonial-quote {

		font-size: 15px;

		margin-bottom: 25px;

	}


	.testimonial-metrics {

		display: flex;

		flex-direction: column;

		gap: 20px;

		margin-top: 25px;

	}


	.testimonial-metrics:before {

		top: -12px;

	}


	.metric-item {

		flex-direction: row;

		justify-content: space-between;

		align-items: center;

		padding-bottom: 20px;

		border-bottom: 1px solid rgba(0, 0, 0, 0.06);

	}


	.metric-item:last-child {

		border-bottom: none;

		padding-bottom: 0;

	}


	.metric-label {

		margin-bottom: 0;

		font-size: 13px;

	}


	.metric-value {

		font-size: 22px;

	}

}


@media (max-width: 480px) {

	.testimonial-left {

		grid-template-columns: 1fr;

		grid-template-areas:

			"pic"

			"info"

			"link";

		justify-items: center;

		text-align: center;

		row-gap: 15px;

	}


	.profile-info {

		align-items: center;

	}


	.testimonial-company {

		justify-content: center;

	}

}


/* platform page testimonial ends */


/* platform page hero animation starts */


.platform-animation-container {

	position: relative;

	max-width: 800px;

	margin: 0 auto;

}


.hero-section .platform-animation-container {

	filter: drop-shadow(0 0 15px rgba(0, 0, 0, 0.2));

}


.hero-animation-container {

	position: relative;

	width: 100%;

	height: 500px;

	margin: 30px auto 50px;

	padding-bottom: 30px;

}


.animation-element {

	position: absolute;

	transition: all 0.3s ease;

	border-radius: 10px;

	overflow: visible;

}


.platform .adzeta-ai-center {

	top: 10%;

	left: 50%;

	transform: translate(-50%, -50%) rotate(0deg);

	width: 180px;

	height: 180px;

	z-index: 10;

	animation: professional-glow 4s infinite ease-in-out;

	filter: drop-shadow(0 0 20px rgba(233, 88, 161, 0.5));

}


.platform .adzeta-ai-left {

	top: 40%;

	left: 10%;

	transform: translate(-50%, -50%);

	width: 260px;

	height: 260px;

	z-index: 5;

}


.platform .adzeta-ai-right {

	top: 45%;

	right: 5%;

	transform: translate(50%, -50%);

	width: 270px;

	height: 270px;

	z-index: 5;

}


.platform .adzeta-ai-bottom {

	bottom: 22%;

	left: 50%;

	transform: translate(-50%, 0);

	width: 160px;

	height: 160px;

	z-index: 5;

}


.platform .connection-line {

	position: absolute;

	z-index: 1;

	overflow: visible;

}


.platform .connection-left,
.platform .connection-right,
.platform .connection-bottom {

	position: relative;

}


.platform .connection-left::before,
.platform .connection-right::before,
.platform .connection-bottom::before {

	content: '';

	position: absolute;

	background: rgba(255, 255, 255, 0.15);

	height: 1px;

	width: 100%;

	top: 0;

	left: 0;

	z-index: 1;

}


.platform .connection-left::after,
.platform .connection-right::after,
.platform .connection-bottom::after {

	content: '';

	position: absolute;

	background: linear-gradient(90deg, rgba(233, 88, 161, 0.8), rgba(143, 118, 245, 0.8));

	height: 2px;

	width: 100%;

	top: 0;

	left: 0;

	z-index: 2;

	transform-origin: left center;

	animation: apple-circuit-flow 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);

	box-shadow: 0 0 8px rgba(233, 88, 161, 0.6);

	border-radius: 4px;

}


.platform .connection-left {

	top: 10%;

	left: 25%;

	width: 30%;

	height: 2px;

	transform: rotate(-35deg);

	transform-origin: top right;

}


.platform .connection-right {

	top: 13%;

	left: 50%;

	width: 30%;

	height: 2px;

	transform: rotate(35deg);

	transform-origin: top left;

}


.platform .connection-bottom {

	top: 20%;

	left: 50%;

	height: 40%;

	width: 2px;

	transform: translateX(-50%);

}


.platform .connection-bottom::before {

	width: 1px;

	height: 100%;

}


.platform .connection-bottom::after {

	width: 2px;

	height: 100%;

	background: linear-gradient(to bottom, rgba(233, 88, 161, 0.8), rgba(143, 118, 245, 0.8));

	animation: apple-circuit-flow-vertical 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);

}


.data-particle {

	position: absolute;

	width: 6px;

	height: 6px;

	background: rgba(255, 255, 255, 0);

	z-index: 3;

	border-radius: 50%;

	opacity: 0;

	mix-blend-mode: screen;

	box-shadow:

		0 0 0 1px rgba(255, 255, 255, 0.95),

		0 0 8px 2px rgba(233, 88, 161, 0.9),

		0 0 16px 4px rgba(143, 118, 245, 0.6);

	backdrop-filter: blur(1px);

	-webkit-backdrop-filter: blur(1px);

}


.data-particle::after {

	content: '';

	position: absolute;

	top: -2px;

	left: -2px;

	right: -2px;

	bottom: -2px;

	background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0) 70%);

	border-radius: 50%;

	z-index: -1;

	opacity: 0.7;

}


.platform .particle-left-1 {

	top: 35%;

	left: 25%;

	animation: apple-particle-left 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);

}


.platform .particle-left-2 {

	top: 35%;

	left: 25%;

	animation: apple-particle-left 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1) 1s;

}


.platform .particle-right-1 {

	top: 35%;

	right: 15%;

	animation: apple-particle-right 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);

}


.platform .particle-right-2 {

	top: 35%;

	right: 15%;

	animation: apple-particle-right 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1) 1s;

}


.platform .particle-bottom-1 {

	top: 20%;

	left: 50%;

	animation: apple-particle-bottom 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);

}


.platform .particle-bottom-2 {

	top: 20%;

	left: 50%;

	animation: apple-particle-bottom 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1) 1s;

}


.platform .element-label {

	position: absolute;

	font-size: 11px;

	font-weight: 500;

	color: rgba(233, 88, 161, 0.85);

	text-align: center;

	text-transform: uppercase;

	letter-spacing: 1.5px;

	width: 100%;

	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

	z-index: 6;

	opacity: 0.9;

}


.platform .label-left {

	top: 180px;

	left: 25px;

	width: 100%;

	margin: 0;

	font-size: 10px;

	opacity: 0.8;

	transform: rotate(-30deg);

}


.platform .label-right {

	top: 160px;

	right: 55px;

	width: 100%;

	margin: 0;

	font-size: 10px;

	opacity: 0.8;

	transform: rotate(35deg);

}


.platform .label-center {

	top: 30%;

	left: 0;

	width: 100%;

	margin: 0;

	transform: rotate(0deg);

	color: rgba(255, 255, 255, 0.9);

	font-size: 10px;

	font-weight: 700;

	letter-spacing: 2px;

	text-shadow: 0 0 8px rgba(233, 88, 161, 0.9);

	mix-blend-mode: overlay;

	background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(233, 88, 161, 0.7));

	-webkit-background-clip: text;

	background-clip: text;

	-webkit-text-fill-color: transparent;

	color: transparent;

	animation: glow 2s infinite ease-in-out;

}


@keyframes glow {

	0%,
	100% {

		text-shadow: 0 0 8px rgba(233, 88, 161, 0.7);

	}

	50% {

		text-shadow: 0 0 15px rgba(233, 88, 161, 1);

	}

}


.platform .label-bottom {

	bottom: -40px;

	left: 45px;

	width: 100%;

	margin: 0;

	font-size: 10px;

	opacity: 0.8;

	transform: rotate(-30deg);

}


@keyframes pulsate {

	0% {

		transform: translate(-50%, -50%) scale(1);

		filter: brightness(1) drop-shadow(0 0 10px rgba(233, 88, 161, 0.3));

	}

	50% {

		transform: translate(-50%, -50%) scale(1.05);

		filter: brightness(1.2) drop-shadow(0 0 20px rgba(233, 88, 161, 0.6));

	}

	100% {

		transform: translate(-50%, -50%) scale(1);

		filter: brightness(1) drop-shadow(0 0 10px rgba(233, 88, 161, 0.3));

	}

}


@keyframes professional-glow {

	0% {

		transform: translate(-50%, -50%) rotate(0deg) scale(1);

		filter: brightness(1) drop-shadow(0 0 20px rgba(233, 88, 161, 0.4));

	}

	50% {

		transform: translate(-50%, -50%) rotate(0deg) scale(1.04) translateY(-5px);

		filter: brightness(1.15) drop-shadow(0 0 30px rgba(233, 88, 161, 0.7));

	}

	100% {

		transform: translate(-50%, -50%) rotate(0deg) scale(1);

		filter: brightness(1) drop-shadow(0 0 20px rgba(233, 88, 161, 0.4));

	}

}


@media (max-width: 767px) {

	@keyframes professional-glow {

		0% {

			transform: translate(-50%, -50%) rotate(0deg) scale(1);

			filter: brightness(1) drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));

		}

		50% {

			transform: translate(-50%, -50%) rotate(0deg) scale(1.03) translateY(-3px);

			filter: brightness(1.12) drop-shadow(0 0 25px rgba(233, 88, 161, 0.7));

		}

		100% {

			transform: translate(-50%, -50%) rotate(0deg) scale(1);

			filter: brightness(1) drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));

		}

	}

}


@keyframes float-left {

	0% {

		transform: translate(-50%, -50%);

		filter: brightness(1);

	}

	25% {

		transform: translate(-50%, -55%) scale(1.02);

		filter: brightness(1.1);

	}

	50% {

		transform: translate(-45%, -50%) scale(1.04);

		filter: brightness(1.15);

	}

	75% {

		transform: translate(-50%, -45%) scale(1.02);

		filter: brightness(1.1);

	}

	100% {

		transform: translate(-50%, -50%);

		filter: brightness(1);

	}

}


@keyframes float-right {

	0% {

		transform: translate(50%, -50%);

		filter: brightness(1);

	}

	25% {

		transform: translate(50%, -45%) scale(1.02);

		filter: brightness(1.1);

	}

	50% {

		transform: translate(45%, -50%) scale(1.04);

		filter: brightness(1.15);

	}

	75% {

		transform: translate(50%, -55%) scale(1.02);

		filter: brightness(1.1);

	}

	100% {

		transform: translate(50%, -50%);

		filter: brightness(1);

	}

}


@keyframes float-bottom {

	0% {

		transform: translate(-50%, 0);

		filter: brightness(1);

	}

	25% {

		transform: translate(-55%, 0) scale(1.02);

		filter: brightness(1.1);

	}

	50% {

		transform: translate(-50%, -5%) scale(1.04);

		filter: brightness(1.15);

	}

	75% {

		transform: translate(-45%, 0) scale(1.02);

		filter: brightness(1.1);

	}

	100% {

		transform: translate(-50%, 0);

		filter: brightness(1);

	}

}


@keyframes apple-circuit-flow {

	0% {

		transform: scaleX(0);

		opacity: 0;

	}

	5% {

		opacity: 1;

	}

	50% {

		transform: scaleX(1);

		opacity: 1;

	}

	50.1% {

		transform: scaleX(1);

		transform-origin: right center;

	}

	100% {

		transform: scaleX(0);

		transform-origin: right center;

		opacity: 0;

	}

}


@keyframes apple-circuit-flow-vertical {

	0% {

		transform: scaleY(0);

		opacity: 0;

	}

	5% {

		opacity: 1;

	}

	50% {

		transform: scaleY(1);

		opacity: 1;

	}

	50.1% {

		transform: scaleY(1);

		transform-origin: center bottom;

	}

	100% {

		transform: scaleY(0);

		transform-origin: center bottom;

		opacity: 0;

	}

}


@keyframes apple-particle-left {

	0%,
	15% {

		opacity: 0;

		transform: translate(0, 0) scale(0.2);

		box-shadow:

			0 0 0 0 rgba(255, 255, 255, 0),

			0 0 0 0 rgba(233, 88, 161, 0),

			0 0 0 0 rgba(143, 118, 245, 0);

	}

	20% {

		opacity: 1;

		transform: translate(30px, -30px) scale(0.8);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 0.95),

			0 0 8px 2px rgba(233, 88, 161, 0.9),

			0 0 16px 4px rgba(143, 118, 245, 0.6);

	}

	30% {

		opacity: 1;

		transform: translate(60px, -60px) scale(1.2);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 1),

			0 0 12px 3px rgba(233, 88, 161, 1),

			0 0 24px 6px rgba(143, 118, 245, 0.8);

	}

	40% {

		opacity: 1;

		transform: translate(90px, -90px) scale(0.8);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 0.95),

			0 0 8px 2px rgba(233, 88, 161, 0.9),

			0 0 16px 4px rgba(143, 118, 245, 0.6);

	}

	45%,
	100% {

		opacity: 0;

		transform: translate(120px, -120px) scale(0.2);

		box-shadow:

			0 0 0 0 rgba(255, 255, 255, 0),

			0 0 0 0 rgba(233, 88, 161, 0),

			0 0 0 0 rgba(143, 118, 245, 0);

	}

}


@keyframes apple-particle-right {

	0%,
	15% {

		opacity: 0;

		transform: translate(0, 0) scale(0.2);

		box-shadow:

			0 0 0 0 rgba(255, 255, 255, 0),

			0 0 0 0 rgba(233, 88, 161, 0),

			0 0 0 0 rgba(143, 118, 245, 0);

	}

	20% {

		opacity: 1;

		transform: translate(-30px, -30px) scale(0.8);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 0.95),

			0 0 8px 2px rgba(233, 88, 161, 0.9),

			0 0 16px 4px rgba(143, 118, 245, 0.6);

	}

	30% {

		opacity: 1;

		transform: translate(-60px, -60px) scale(1.2);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 1),

			0 0 12px 3px rgba(233, 88, 161, 1),

			0 0 24px 6px rgba(143, 118, 245, 0.8);

	}

	40% {

		opacity: 1;

		transform: translate(-90px, -90px) scale(0.8);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 0.95),

			0 0 8px 2px rgba(233, 88, 161, 0.9),

			0 0 16px 4px rgba(143, 118, 245, 0.6);

	}

	45%,
	100% {

		opacity: 0;

		transform: translate(-120px, -120px) scale(0.2);

		box-shadow:

			0 0 0 0 rgba(255, 255, 255, 0),

			0 0 0 0 rgba(233, 88, 161, 0),

			0 0 0 0 rgba(143, 118, 245, 0);

	}

}


@keyframes apple-particle-bottom {

	0%,
	15% {

		opacity: 0;

		transform: translateY(0) scale(0.2);

		box-shadow:

			0 0 0 0 rgba(255, 255, 255, 0),

			0 0 0 0 rgba(233, 88, 161, 0),

			0 0 0 0 rgba(143, 118, 245, 0);

	}

	20% {

		opacity: 1;

		transform: translateY(40px) scale(0.8);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 0.95),

			0 0 8px 2px rgba(233, 88, 161, 0.9),

			0 0 16px 4px rgba(143, 118, 245, 0.6);

	}

	30% {

		opacity: 1;

		transform: translateY(80px) scale(1.2);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 1),

			0 0 12px 3px rgba(233, 88, 161, 1),

			0 0 24px 6px rgba(143, 118, 245, 0.8);

	}

	40% {

		opacity: 1;

		transform: translateY(120px) scale(0.8);

		box-shadow:

			0 0 0 1px rgba(255, 255, 255, 0.95),

			0 0 8px 2px rgba(233, 88, 161, 0.9),

			0 0 16px 4px rgba(143, 118, 245, 0.6);

	}

	45%,
	100% {

		opacity: 0;

		transform: translateY(160px) scale(0.2);

		box-shadow:

			0 0 0 0 rgba(255, 255, 255, 0),

			0 0 0 0 rgba(233, 88, 161, 0),

			0 0 0 0 rgba(143, 118, 245, 0);

	}

}


@media (max-width: 991px) {

	.platform .hero-animation-container {

		height: 450px;

	}


	.platform .adzeta-ai-center {

		width: 180px;

		height: 180px;

		top: 20%;

		transform: translate(-50%, -50%) rotate(0deg);

		filter: drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));

		animation: professional-glow 4s infinite ease-in-out;

	}


	.platform .adzeta-ai-left,

	.platform .adzeta-ai-right {

		width: 225px;

		height: 225px;

	}


	.platform .adzeta-ai-bottom {

		width: 150px;

		height: 150px;

	}


	.platform .adzeta-ai-left {

		left: 14%;

		top: 55%;

	}


	.platform .adzeta-ai-right {

		right: 14%;

		top: 60%;

	}


	.platform .connection-left {

		top: 15%;

		left: 22%;

		width: 32%;

	}


	.platform .connection-right {

		top: 15%;

		right: 22%;

		width: 32%;

	}


	.platform .label-center {

		font-size: 10px;

		top: 30%;

		transform: rotate(0deg);

	}


	.platform .label-left {

		top: 150px;

		left: 22px;

		font-size: 9px;

	}


	.platform .label-right {

		top: 125px;

		right: 40px;

		font-size: 9px;

	}


	.platform .label-bottom {

		bottom: -30px;

		left: 35px;

		font-size: 9px;

	}

}


@media (max-width: 767px) {

	.platform .hero-animation-container {

		height: 380px;

		padding-bottom: 40px;

	}


	.platform .adzeta-ai-bottom {

		bottom: 25%;

	}


	.platform .adzeta-ai-center {

		width: 150px;

		height: 150px;

		top: 20%;

		transform: translate(-50%, -50%) rotate(0deg);

		filter: drop-shadow(0 0 12px rgba(233, 88, 161, 0.4));

		animation: professional-glow 4s infinite ease-in-out;

	}


	.platform .adzeta-ai-left,

	.platform .adzeta-ai-right {

		width: 140px;

		height: 140px;

	}


	.platform .adzeta-ai-bottom {

		width: 110px;

		height: 110px;

	}


	.platform .adzeta-ai-left {

		left: 15%;

		top: 40%;

	}


	.platform .adzeta-ai-right {

		right: 15%;

		top: 40%;

	}


	.platform .connection-left {

		top: 15%;

		left: 25%;

		width: 30%;

	}


	.platform .connection-right {

		top: 15%;

		right: 25%;

		width: 30%;

	}


	.platform .element-label {

		font-size: 8px;

		letter-spacing: 1px;

	}


	.platform .label-center {

		font-size: 9px;

		letter-spacing: 1.5px;

		top: 30%;

		transform: rotate(0deg);

	}


	.platform .label-left {

		top: 90px;

		left: 20px;

		font-size: 8px;

	}


	.platform .label-right {

		top: 85px;

		right: 20px;

		font-size: 8px;

	}


	.platform .label-bottom {

		bottom: -20px;

		left: 30px;

		font-size: 8px;

	}


	.data-particle {

		width: 8px;

		height: 8px;

	}


	.platform .particle-left-1 {

		top: 48%;

		left: 20%;

	}


	.platform .particle-left-2 {

		top: 40%;

		left: 25%;

	}


	.platform .particle-right-1 {

		top: 33%;

		right: 20%;

	}


	.platform .particle-right-2 {

		top: 30%;

		right: 25%;

	}

}


@media (max-width: 768px) {

	.platform-animation-container {

		max-width: 100%;

	}

}


/* platform page hero animation ends */


/* platform steps section */


.process-step-circle {

	width: 120px;

	height: 120px;

	border-radius: 50%;

	background: #ffffff;

	position: relative;

	display: inline-flex;

	align-items: center;

	justify-content: center;

	transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);

	/* iOS-inspired shadow - subtle layered shadows for depth */

	box-shadow:

		0 2px 5px rgba(0, 0, 0, 0.02),

		0 10px 30px rgba(0, 0, 0, 0.04),

		0 20px 40px rgba(0, 0, 0, 0.03);

	/* Subtle border for definition */

	border: 1px solid rgba(233, 88, 161, 0.05);

	overflow: hidden;

}


.process-step-circle i {

	font-size: 32px;

	position: absolute;

	top: 50%;

	left: 50%;

	transform: translate(-50%, -50%);

	transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);

}


.process-step-circle:hover {

	transform: translateY(-8px) scale(1.02);

	box-shadow:

		0 5px 15px rgba(0, 0, 0, 0.03),

		0 15px 35px rgba(0, 0, 0, 0.06),

		0 30px 60px rgba(0, 0, 0, 0.04);

}


.process-step-circle:hover::after {

	content: '';

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	border-radius: 50%;

	background: radial-gradient(circle at center, rgba(233, 88, 161, 0.15) 0%, rgba(143, 118, 245, 0) 70%);

	z-index: 1;

	opacity: 0;

	animation: subtle-glow 1.5s ease-in-out forwards;

}


.process-step-circle:hover i {

	transform: translate(-50%, -50%) scale(1.1);

	background: linear-gradient(135deg, #e958a1 0%, #df367d 50%, #ee5c46 100%);

	-webkit-background-clip: text;

	background-clip: text;

	-webkit-text-fill-color: transparent;

}


@keyframes subtle-glow {

	0% {

		opacity: 0;

	}

	50% {

		opacity: 1;

	}

	100% {

		opacity: 0.7;

	}

}


@media (max-width: 991px) {

	.process-step-circle {

		width: 100px;

		height: 100px;

	}


	.process-step-circle i {

		font-size: 28px;

	}

}


@media (max-width: 767px) {

	.process-step-circle {

		width: 90px;

		height: 90px;

	}


	.process-step-circle i {

		font-size: 24px;

	}

}


/* platform steps section ends*/


/* platform Our Predictive AI section start*/


.showcase-header {

	margin-bottom: 60px;

	text-align: center;

}


.showcase-title {

	margin-bottom: 20px;

	font-size: 38px;

	line-height: 1.2;

	font-weight: 600;

}


.showcase-description {

	font-size: 18px;

	line-height: 1.6;

	max-width: 800px;

	margin: 0 auto;

	color: rgba(44, 46, 60, 0.8);

}


.process-flow {

	position: relative;

	margin-bottom: 50px;

}


.process-nav {

	display: flex;

	justify-content: center;

	margin-bottom: 40px;

	position: relative;

	z-index: 2;

	background: white;

	padding: 0;

	border-bottom: 1px solid rgba(0, 0, 0, 0.1);

	box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);

	border-radius: 8px 8px 0 0;

	max-width: 1200px;

	margin-left: auto;

	margin-right: auto;

}


.process-step {

	position: relative;

	text-align: center;

	cursor: pointer;

	transition: all 0.3s ease;

	padding: 20px 30px;

	margin: 0;

	flex: 1;

	border-bottom: 3px solid transparent;

}


.step-label {

	font-size: 16px;

	font-weight: 500;

	color: #2c2e3c;

	transition: all 0.3s ease;

	position: relative;

	white-space: nowrap;

}


.step-label::after {

	content: none;

}


.process-step.active {

	border-bottom: 3px solid #e958a1;

	background-color: rgba(233, 88, 161, 0.05);

}


.process-step.active .step-label {

	color: #e958a1;

	font-weight: 600;

}


.process-step:hover:not(.active) {

	background-color: rgba(233, 88, 161, 0.02);

	border-bottom: 3px solid rgba(233, 88, 161, 0.3);

}


.process-step:hover:not(.active) .step-label {

	color: #e958a1;

}


.step-number {

	display: none;

}


.step-label-mobile {

	font-size: 15px;

	font-weight: 600;

	color: #2c2e3c;

}


.step-icon-mobile {

	font-size: 16px;

	transition: transform 0.3s ease;

}


.platform .process-content {

	position: relative;

	min-height: 550px;

}


.process-panel {

	display: none;

	opacity: 0;

	transition: all 0.5s ease;

	position: absolute;

	width: 100%;

	top: 0;

	left: 0;

	padding-bottom: 40px;

}


@media (min-width: 768px) {

	.process-panel.active {

		display: block !important;

		opacity: 1;

	}

}


.process-panel.active {

	display: block;

	opacity: 1;

	animation: fadeInUp 0.5s ease forwards;

}


@media (max-width: 767px) {}


@media (max-width: 767px) {

	.process-content>.process-panel {

		display: none !important;

	}

}


.panel-container {

	display: flex;

	align-items: stretch;

	background: white;

	border-radius: 16px;

	overflow: hidden;

	transition: all 0.5s ease;

	max-width: 1200px;

	margin: 0 auto;

	min-height: 500px;

}


.panel-visual {

	width: 45%;

	padding: 30px;

	/*     background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%); */

	display: flex;

	align-items: center;

	justify-content: center;

	position: relative;

	overflow: hidden;

}


.panel-visual::before {

	content: '';

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	background: linear-gradient(135deg, rgba(233, 88, 161, 0.01) 0%, rgba(143, 118, 245, 0.01) 100%);

	z-index: 1;

}


.panel-visual::after {

	content: '';

	position: absolute;

	top: 0;

	left: 0;

	width: 100%;

	height: 100%;

	/*     background-image: radial-gradient(rgba(233, 88, 161, 0.1) 2px, transparent 2px),

                      radial-gradient(rgba(143, 118, 245, 0.1) 2px, transparent 2px); */

	background-size: 30px 30px, 25px 25px;

	background-position: 0 0, 15px 15px;

	opacity: 0.3;

	z-index: 1;

}


.panel-visual .svg-container {

	position: relative;

	z-index: 2;

	max-width: 100%;

	height: auto;

}


.panel-content {

	width: 55%;

	padding: 40px 50px;

	position: relative;

	display: flex;

	flex-direction: column;

	justify-content: center;

}


.panel-title {

	font-size: 32px;

	margin-bottom: 15px;

	color: #2c2e3c;

	font-weight: 700;

	line-height: 1.3;

	position: relative;

	display: inline-block;

}


.panel-description {

	font-size: 16px;

	line-height: 1.7;

	color: rgba(44, 46, 60, 0.8);

	margin-bottom: 30px;

}


.panel-features {

	margin-top: 30px;

	display: flex;

	flex-direction: column;

	gap: 15px;

	margin-bottom: 20px;
	/* Ensure space at bottom */

}


.feature-item {

	display: flex;

	align-items: center;

	padding: 12px 18px;

	border-radius: 12px;

	background-color: rgba(248, 249, 250, 0.7);

	transition: all 0.3s ease;

	border-left: 3px solid transparent;

	margin-bottom: 0;
	/* Reset any default margins */

}


.feature-item:hover {

	transform: translateX(5px);

	background-color: rgba(248, 249, 250, 0.9);

	border-left: 3px solid #e958a1;

	box-shadow: 0 5px 15px rgba(233, 88, 161, 0.08);

}


.feature-icon {

	margin-right: 18px;

	color: #e958a1;

	font-size: 20px;

	flex-shrink: 0;

	width: 45px;

	height: 45px;

	background: rgba(233, 88, 161, 0.08);

	border-radius: 50%;

	display: flex;

	align-items: center;

	justify-content: center;

	transition: all 0.3s ease;

	box-shadow: 0 4px 10px rgba(233, 88, 161, 0.12);

	border: 1px solid rgba(233, 88, 161, 0.1);

}


.feature-item:hover .feature-icon {

	background: linear-gradient(to right, #e958a1, #ff5d74);

	color: white;

	transform: scale(1.1);

	box-shadow: 0 5px 15px rgba(233, 88, 161, 0.25);

	transition: all 0.3s ease;

}


.feature-text {

	font-size: 16px;

	line-height: 1.6;

	width: 100%;

	word-wrap: break-word;

}


.feature-text strong {

	display: block;

	margin-bottom: 3px;

	color: #2c2e3c;

	font-weight: 600;

}


.svg-container {

	width: 100%;

	height: 100%;

	min-height: 300px;

	display: flex;

	align-items: center;

	justify-content: center;

}


.svg-animation {

	width: 100%;

	height: auto;

	max-width: 450px;

}


@keyframes fadeInUp {

	from {

		opacity: 0;

		transform: translateY(20px);

	}

	to {

		opacity: 1;

		transform: translateY(0);

	}

}


@media (max-width: 1199px) {

	.panel-visual {

		width: 40%;

	}


	.panel-content {

		width: 60%;

	}

}


@media (max-width: 991px) {


	.showcase-title {

		font-size: 32px;

	}


	.panel-container {

		flex-direction: column;

	}


	.panel-visual,
	.panel-content {

		width: 100%;

	}


	.panel-visual {

		min-height: 300px;

		order: 1;

		padding: 30px;

	}


	.panel-content {

		order: 2;

		padding: 35px;

	}


	.panel-title {

		font-size: 24px;

	}


	.feature-item {

		padding: 12px 15px;

	}

}


@media (max-width: 767px) {


	.process-nav {

		display: none !important;

	}


	.process-content {

		position: static;

		min-height: auto;

		margin-top: 0;

		width: 100%;

	}


	.process-panel {

		position: static;

		opacity: 1;

		animation: none;

		padding-bottom: 20px;

	}


	.accordion-panel .process-panel {

		display: block !important;

	}


	/* Other mobile styles */

	.showcase-title {

		font-size: 24px;

		margin-bottom: 15px;

	}


	.showcase-description {

		font-size: 15px;

	}


	.showcase-header {

		margin-bottom: 30px;

	}


	.panel-title {

		font-size: 20px;

		margin-bottom: 15px;

	}


	.panel-description {

		font-size: 15px;

		margin-bottom: 20px;

	}


	.panel-content,
	.panel-visual {

		padding: 20px;

		width: 100%;

		box-sizing: border-box;

	}


	/* Ensure feature items don't overflow on mobile */

	.feature-item {

		width: 100%;

		box-sizing: border-box;

		flex-wrap: wrap;

	}


	.feature-text {

		font-size: 14px;

		width: calc(100% - 55px);
		/* Account for icon width */

	}


	.panel-visual {

		min-height: 220px;

	}


	.feature-item {

		padding: 10px 15px;

	}


	.feature-icon {

		width: 35px;

		height: 35px;

		font-size: 16px;

		margin-right: 12px;

	}


	.feature-text {

		font-size: 14px;

	}


	/* Adjust panel container for accordion */

	.panel-container {

		border-radius: 0;

		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);

		min-height: auto;

		overflow: hidden;

	}

}


/* SVG Animation Styles */

.svg-animation {

	width: 100%;

	height: 100%;

	min-height: 300px;

}


/* ValueBid Framework Section Styling - Matching Our Predictive AI mobile accordions */

.bg-very-light-gray .accordion-style-04 .accordion-item {

	margin-bottom: 10px;

	border-radius: 8px;

	overflow: hidden;

	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

}


.bg-very-light-gray .accordion-style-04 .accordion-header {

	background: white;

	padding: 15px 20px;

	border-radius: 8px;

	text-align: left;

}


.bg-very-light-gray .accordion-style-04 .accordion-title {

	position: relative;

	margin-bottom: 0;

	padding-right: 20px;

	color: #2c2e3c;

	font-weight: 600;

	font-size: 18px;

	text-align: left;

}


.bg-very-light-gray .accordion-style-04 .accordion-item.active-accordion .accordion-header {

	background: linear-gradient(to right, rgba(233, 88, 161, 0.1), rgba(255, 255, 255, 0.9));

	border-left: 3px solid #e958a1;

	border-radius: 8px 8px 0 0;

}


.bg-very-light-gray .accordion-style-04 .accordion-item.active-accordion .accordion-title {

	color: #e958a1 !important;

	font-weight: 700;

}


/* Match the arrow styling with Our Predictive AI mobile accordions */

.bg-very-light-gray .accordion-style-04 .accordion-title i.fa-angle-down,

.bg-very-light-gray .accordion-style-04 .accordion-title i.fa-angle-right {

	position: absolute;

	right: 0;

	top: 50%;

	transform: translateY(-50%);

	transition: transform 0.3s ease;

	color: #e958a1;

	font-size: 16px;

}


/* Rotate the down arrow when active */

.bg-very-light-gray .accordion-style-04 .accordion-item.active-accordion .accordion-title i.fa-angle-down {

	transform: translateY(-50%) rotate(180deg);

}


/* Hide the right-angle icon when active and show down-angle icon */

.bg-very-light-gray .accordion-style-04 .accordion-item.active-accordion .accordion-title i.fa-angle-right {

	display: none;

}


.valuebid-showcase .accordion-style-04 .accordion-item.active-accordion .accordion-title i.fa-angle-down {

	display: inline-block;

}


/* Hide the down-angle icon when not active and show right-angle icon */

.valuebid-showcase .accordion-style-04 .accordion-item:not(.active-accordion) .accordion-title i.fa-angle-down {

	display: none;

}


.valuebid-showcase .accordion-style-04 .accordion-item:not(.active-accordion) .accordion-title i.fa-angle-right {

	display: inline-block;

}


.valuebid-showcase .accordion-style-04 .accordion-body {

	background: white;

	border-radius: 0 0 8px 8px;

}


/* Match the SVG container styling for ValueBid section */

.valuebid-showcase .svg-container {

	width: 100%;

	height: 100%;

	min-height: 500px;
	/* Increased minimum height for the container */

	display: flex;

	align-items: center;

	justify-content: center;

	padding: 20px 0;
	/* Add some vertical padding */

}


.valuebid-showcase .svg-animation {

	width: 100%;

	max-width: 100%;
	/* Set to 100% to fill the container */

	height: auto;

}


/* Data Flow Animation */

.data-source {

	transition: all 0.5s ease;

}


.data-path {

	stroke-dasharray: 1000;

	stroke-dashoffset: 1000;

}


.data-particle {

	opacity: 0;

}


/* Neural Network Animation */

.network-node {

	transform-origin: center;

	transition: all 0.5s ease;

}


.network-connection {

	stroke-dasharray: 100;

	stroke-dashoffset: 100;

}


/* Prediction Graph Animation */

.prediction-line {

	stroke-dasharray: 1000;

	stroke-dashoffset: 1000;

}


.confidence-area {

	opacity: 0;

}


/* Learning Cycle Animation */

.cycle-path {

	stroke-dasharray: 1000;

	stroke-dashoffset: 1000;

}


.cycle-node {

	transform-origin: center;

	transition: all 0.5s ease;

}


/* ValueBid Showcase Section - Match padding with Predictive AI section */

.valuebid-showcase .container {

	padding-left: 15px;

	padding-right: 15px;

	width: 100%;

	max-width: 1140px;

	margin-left: auto;

	margin-right: auto;

}


.valuebid-showcase .row.align-items-center {

	padding-left: 0;

	padding-right: 0;

}


/* Match accordion styling with Predictive AI section */

.valuebid-showcase .accordion-style-01 {

	width: 100%;

	max-width: 100%;

	min-width: 320px;
	/* Ensure minimum width for accordion */

}


.valuebid-showcase .accordion-item {

	margin-bottom: 10px;

}


/* Match font sizes between both sections */

.valuebid-showcase .accordion-title {

	font-size: 16px;

	font-weight: 600;

}


/* Icon styling for ValueBid accordions */

.valuebid-showcase .accordion-title {

	display: flex;

	align-items: center;

}


/* Feature icon wrapper to override global styles */

.valuebid-showcase .feature-icon-wrapper {

	display: inline-flex;

	align-items: center;

	position: static;

	transform: none;

}


.valuebid-showcase .feature-icon-wrapper i {

	color: #232323;
	/* Same color as title text */

	position: static !important;

	transform: none !important;

	display: inline-flex;

	align-items: center;

	vertical-align: middle;

	margin-right: 12px;
	/* Add right margin after icons */

	font-size: 16px;
	/* Match the font size of the title */

}


/* Position the chevron icon on the right */

.valuebid-showcase .accordion-title i.icon-small {

	position: absolute;

	right: 0;

	top: 50%;

	transform: translateY(-50%);

	color: #e958a1;

}


/* Active accordion styling */

.valuebid-showcase .accordion-item.active-accordion .accordion-title .feature-icon-wrapper i {

	color: #e958a1;
	/* Pink color for active state */

	font-weight: bold;

}


/* Rotate chevron when accordion is active */

.valuebid-showcase .accordion-item.active-accordion .accordion-title i.bi-chevron-down.icon-small {

	transform: translateY(-50%) rotate(180deg);

}


/* Ensure text is properly positioned */

.valuebid-showcase .accordion-title span:not(.feature-icon-wrapper) {

	flex: 1;

}


@media (max-width: 1024px) {

	.valuebid-showcase .container {

		padding-left: 15px;

		padding-right: 15px;

	}


	.valuebid-showcase .col-xl-5,

	.valuebid-showcase .col-lg-6,

	.valuebid-showcase .col-xl-7,

	.valuebid-showcase .col-lg-6 {

		padding-left: 15px;

		padding-right: 15px;

		width: 100%;

	}


	/* Adjust SVG container on tablet */

	.valuebid-showcase .svg-container {

		min-height: 400px;

		margin-top: 30px;

	}


	.valuebid-showcase .svg-animation {

		max-width: 100%;

	}


	.valuebid-showcase .accordion-style-01 {

		width: 100%;

		max-width: 100%;

		min-width: 0;
		/* Reset min-width on tablet */

	}


	.valuebid-showcase .accordion-header {

		width: 100%;

		max-width: 100%;

		margin-left: auto;

		margin-right: auto;

		box-sizing: border-box;

	}


	/* Responsive font sizes for tablet */

	.valuebid-showcase .accordion-title {

		font-size: 15px;

	}


	/* Adjust icon size on tablet */

	.valuebid-showcase .accordion-title .feature-icon-wrapper i {

		font-size: 15px;

	}

}


/* Mobile specific styles */

@media (max-width: 767px) {

	/* Responsive font sizes for mobile */

	/* Adjust SVG container on mobile */

	.valuebid-showcase .svg-container {

		min-height: 350px;

	}


	.valuebid-showcase .svg-animation {

		max-width: 100%;

	}


	/* Adjust icon size on mobile */

	.valuebid-showcase .accordion-title .feature-icon-wrapper i {

		font-size: 14px;

	}


	/* Ensure proper spacing on mobile */

	.valuebid-showcase .accordion-title {

		padding-right: 25px;

	}


	/* Fix content overflow in ValueBid accordions */

	.valuebid-showcase .accordion-body {

		padding: 15px 20px;

		width: 100%;

		box-sizing: border-box;

	}


	.valuebid-showcase .accordion-body p {

		width: 100%;

		word-wrap: break-word;

		margin-bottom: 0;

	}

}


/* platform Our Predictive AI section ends*/


/* platform Scale Section starts*/


/* ========================================
   DUAL GROWTH COMPARISON SECTION - START
   Clean Apple-inspired design for side-by-side growth trajectory comparison
   ======================================== */

/* Main container for dual growth comparison */
.dual-growth-comparison {
	background: #ffffff;
	border-radius: 16px;
	padding: 40px;
	border: 1px solid #f0f0f0;
	position: relative;
}

/* Header section with title, subtitle and animate button */
.comparison-header {
	text-align: center;
	margin-bottom: 40px;
	padding-bottom: 20px;
	border-bottom: 1px solid #f5f5f7;
}

/* Main section title */
.comparison-title {
	font-size: 24px;
	font-weight: 600;
	color: #1d1d1f;
	margin-bottom: 8px;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	letter-spacing: -0.3px;
}

/* Descriptive subtitle */
.comparison-subtitle {
	font-size: 16px;
	color: #86868b;
	margin-bottom: 24px;
	line-height: 1.4;
}

/* Animate button with gradient background */
.animate-button {
	display: inline-flex;
	/* Flexbox fallbacks */
	display: -webkit-inline-flex;
	display: -moz-inline-flex;
	align-items: center;
	-webkit-align-items: center;
	gap: 8px;
	/* Cross-browser gradient support */
	background: linear-gradient(135deg, #F25EB0 0%, #F25781 100%);
	background: -webkit-linear-gradient(135deg, #F25EB0 0%, #F25781 100%);
	background: -moz-linear-gradient(135deg, #F25EB0 0%, #F25781 100%);
	background: -o-linear-gradient(135deg, #F25EB0 0%, #F25781 100%);
	color: white;
	border: none;
	padding: 12px 24px;
	border-radius: 12px;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	/* Cross-browser transition support */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	-webkit-transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	-moz-transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	-o-transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	box-shadow: 0 4px 16px rgba(242, 94, 176, 0.3);
	-webkit-box-shadow: 0 4px 16px rgba(242, 94, 176, 0.3);
}

.animate-button:hover {
	/* Cross-browser transform support */
	transform: translateY(-2px);
	-webkit-transform: translateY(-2px);
	-moz-transform: translateY(-2px);
	-ms-transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(242, 94, 176, 0.4);
	-webkit-box-shadow: 0 6px 20px rgba(242, 94, 176, 0.4);
}

/* Container for side-by-side chart comparison */
.dual-chart-container {
	display: grid;
	/* Fallback for browsers without grid support */
	display: -ms-grid;
	grid-template-columns: 1fr 1fr;
	-ms-grid-columns: 1fr 30px 1fr;
	gap: 30px;
	margin-bottom: 40px;
}

/* Grid fallback for older browsers */
@supports not (display: grid) {
	.dual-chart-container {
		display: flex;
		display: -webkit-flex;
		display: -moz-flex;
	}

	.dual-chart-container .chart-section {
		flex: 1;
		-webkit-flex: 1;
		margin-right: 30px;
	}

	.dual-chart-container .chart-section:last-child {
		margin-right: 0;
	}
}

/* Modern chart section - Apple/Samsung inspired */
.chart-section {
	background: rgba(255, 255, 255, 0.8);
	backdrop-filter: blur(20px);
	-webkit-backdrop-filter: blur(20px);
	border-radius: 24px;
	padding: 32px;
	position: relative;
	border: 1px solid rgba(255, 255, 255, 0.2);
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.04),
		0 1px 2px rgba(0, 0, 0, 0.02),
		inset 0 1px 0 rgba(255, 255, 255, 0.9);
	transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
	overflow: hidden;
}

/* Modern hover effect with depth */
.chart-section:hover {
	transform: translateY(-8px) scale(1.02);
	box-shadow:
		0 20px 60px rgba(0, 0, 0, 0.08),
		0 8px 24px rgba(0, 0, 0, 0.04),
		inset 0 1px 0 rgba(255, 255, 255, 1);
	border-color: rgba(255, 255, 255, 0.4);
}

/* Traditional section with subtle gradient accent */
.traditional-section {
	background: linear-gradient(135deg,
			rgba(255, 107, 107, 0.03) 0%,
			rgba(255, 255, 255, 0.8) 100%);
}

.traditional-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 4px;
	height: 100%;
	background: linear-gradient(180deg, #ff6b6b 0%, #ff8a80 100%);
	border-radius: 2px 0 0 2px;
}

/* Adzeta section with modern gradient */
.adzeta-section {
	background: linear-gradient(135deg,
			rgba(48, 209, 88, 0.03) 0%,
			rgba(255, 255, 255, 0.8) 100%);
}

.adzeta-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 4px;
	height: 100%;
	background: linear-gradient(180deg, #30d158 0%, #4caf50 100%);
	border-radius: 2px 0 0 2px;
}

/* Modern chart header - borderless design */
.chart-header {
	display: flex;
	align-items: center;
	gap: 16px;
	margin-bottom: 28px;
	padding: 0;
	background: transparent;
	border: none;
	transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Modern icon container - Apple style with perfect centering */
.chart-icon {
	width: 44px;
	height: 44px;
	border-radius: 12px;
	display: flex;
	/* Flexbox fallbacks for older browsers */
	display: -webkit-flex;
	display: -moz-flex;
	align-items: center;
	-webkit-align-items: center;
	justify-content: center;
	-webkit-justify-content: center;
	border: none;
	transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
	position: relative;
	overflow: hidden;
}

/* Center all icon types perfectly in chart headers */
.chart-icon svg,
.chart-icon i {
	display: block;
	margin: 0 auto;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	line-height: 1;
}

/* Traditional icon with modern glass effect */
.traditional-icon {
	background: rgba(255, 107, 107, 0.06);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}

/* Adzeta icon with modern glass effect */
.adzeta-icon {
	background: rgba(48, 209, 88, 0.06);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}

/* Chart title styling */
.chart-info h4 {
	font-size: 16px;
	font-weight: 600;
	color: #1d1d1f;
	margin-bottom: 2px;
	/* Cross-browser font stack with Safari optimization */
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	/* Safari font smoothing */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Chart description text */
.chart-info p {
	font-size: 13px;
	color: #86868b;
	margin: 0;
	/* Cross-browser font stack with Safari optimization */
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	/* Safari font smoothing */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Modern chart canvas - borderless design */
.chart-canvas {
	position: relative;
	background: transparent;
	border-radius: 16px;
	padding: 24px;
	border: none;
	overflow: hidden;
}

/* SVG chart element */
.growth-chart {
	width: 100%;
	height: 200px;
}

/* Modern floating overlay - Apple style */
.chart-overlay {
	position: absolute !important;
	top: 24px !important;
	right: 24px !important;
	background: rgba(255, 255, 255, 0.85) !important;
	backdrop-filter: blur(20px) !important;
	-webkit-backdrop-filter: blur(20px) !important;
	border-radius: 16px !important;
	padding: 8px 10px !important;
	border: 1px solid rgba(255, 255, 255, 0.3) !important;
	min-width: 130px !important;
	z-index: 10 !important;
	display: block !important;
	opacity: 1 !important;
	visibility: visible !important;
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.06),
		0 1px 2px rgba(0, 0, 0, 0.02),
		inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* Desktop-specific overlay positioning to avoid chart line interference */
@media (min-width: 1024px) {
	.traditional-section .chart-overlay {
		top: 2px !important;
		right: 20px !important;
	}

	.adzeta-section .chart-overlay {
		top: 1px !important;
		right: 20px !important;
	}
}

/* Container for growth rate and label */
.growth-indicator {
	text-align: center !important;
	display: flex !important;
	flex-direction: column !important;
	justify-content: center !important;
	align-items: center !important;
	width: 100% !important;
	height: 100% !important;
	opacity: 1 !important;
	visibility: visible !important;
	padding: 0 !important;
	margin: 0 !important;
}

/* Growth percentage display (e.g., +15%, +127%) */
.growth-rate {
	display: block !important;
	font-size: 20px !important;
	font-weight: 700 !important;
	margin: 0 !important;
	padding: 0 !important;
	/* Cross-browser font stack with Safari optimization */
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif !important;
	text-align: center !important;
	line-height: 1 !important;
	opacity: 1 !important;
	visibility: visible !important;
	letter-spacing: -0.3px !important;
	/* Safari font smoothing */
	-webkit-font-smoothing: antialiased !important;
	-moz-osx-font-smoothing: grayscale !important;
	/* Prevent text selection issues in Safari */
	-webkit-user-select: none !important;
	-moz-user-select: none !important;
	-ms-user-select: none !important;
	user-select: none !important;
}

/* Traditional chart growth rate color */
.traditional-overlay .growth-rate {
	color: #ff6b6b;
}

/* Adzeta AI chart growth rate color */
.adzeta-overlay .growth-rate {
	color: #30d158;
}

/* Growth description text (e.g., "Slowing Growth", "Accelerating Growth") */
.growth-label {
	display: block !important;
	font-size: 13px !important;
	color: #6d6d70 !important;
	font-weight: 500 !important;
	/* Cross-browser font stack with Safari optimization */
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif !important;
	white-space: nowrap !important;
	line-height: 1 !important;
	text-align: center !important;
	margin: 2px 0 0 0 !important;
	padding: 0 !important;
	opacity: 1 !important;
	visibility: visible !important;
	/* Safari font smoothing */
	-webkit-font-smoothing: antialiased !important;
	-moz-osx-font-smoothing: grayscale !important;
	/* Prevent text selection issues in Safari */
	-webkit-user-select: none !important;
	-moz-user-select: none !important;
	-ms-user-select: none !important;
	user-select: none !important;
}

/* Key insights section below the charts */
.growth-insights {
	display: flex;
	gap: 20px;
	margin-top: 24px;
}

/* Modern insight card - borderless */
.insight-item {
	flex: 1;
	display: flex;
	align-items: flex-start;
	gap: 16px;
	background: rgba(255, 255, 255, 0.6);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border-radius: 16px;
	padding: 24px;
	border: 1px solid rgba(255, 255, 255, 0.2);
	transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
	box-shadow:
		0 4px 16px rgba(0, 0, 0, 0.04),
		inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Enhanced hover effect for insights */
.insight-item:hover {
	transform: translateY(-4px);
	background: rgba(255, 255, 255, 0.8);
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.08),
		inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* Modern icon container for insights - properly centered */
.insight-icon {
	width: 40px;
	height: 40px;
	border-radius: 12px;
	display: flex;
	/* Flexbox fallbacks for older browsers */
	display: -webkit-flex;
	display: -moz-flex;
	align-items: center;
	-webkit-align-items: center;
	justify-content: center;
	-webkit-justify-content: center;
	flex-shrink: 0;
	-webkit-flex-shrink: 0;
	border: none;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	/* Ensure perfect centering */
	position: relative;
}

/* Center all icon types perfectly in insight icons */
.insight-icon svg,
.insight-icon i {
	display: block;
	margin: 0 auto;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	line-height: 1;
}

/* Traditional insight icon with glass effect */
.traditional-insight-icon {
	background: rgba(255, 107, 107, 0.08);
}

/* Adzeta AI insight icon with glass effect */
.adzeta-insight-icon {
	background: rgba(48, 209, 88, 0.08);
}

/* Insight text content */
.insight-text {
	font-size: 14px;
	line-height: 1.5;
	color: #1d1d1f;
	font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Highlighted text in insights */
.insight-text strong {
	color: #F25EB0;
	font-weight: 600;
}

/* Bottom metrics section showing proven results */
.metrics-results-section {

	border-radius: 16px;
	padding: 40px;
}

/* Metrics section header */
.metrics-header h3 {
	font-size: 24px;
	font-weight: 600;
	color: #1d1d1f;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	letter-spacing: -0.3px;
}

/* Metrics section description */
.metrics-header p {
	font-size: 16px;
	color: #86868b;
	margin: 0;
}

/* Data Sources & Credibility Section - Clean Design */
.data-credibility-section {
	background: white;
	border: 1px solid #e5e5e7;
	border-radius: 12px;
	padding: 32px;
	margin-top: 32px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.credibility-header {
	text-align: center;
	margin-bottom: 32px;
	padding-bottom: 20px;
	border-bottom: 1px solid #f0f0f0;
}

.credibility-header h4 {
	font-size: 20px;
	font-weight: 600;
	color: #1d1d1f;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	letter-spacing: -0.3px;
	margin-bottom: 8px;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.credibility-header p {
	font-size: 14px;
	color: #86868b;
	margin: 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* Clean credibility grid layout */
.credibility-grid {
	display: flex;
	gap: 24px;
	justify-content: center;
	margin-bottom: 32px;
}

/* Individual credibility item - minimal design */
.credibility-item {
	flex: 1;
	max-width: 280px;
	text-align: center;
	padding: 0;
	background: transparent;
	border: none;
}

/* Simple metric display */
.credibility-metric {
	display: inline-flex;
	align-items: center;
	gap: 8px;
	background: #f8f9fa;
	border: 1px solid #e5e5e7;
	border-radius: 8px;
	padding: 12px 16px;
	margin-bottom: 12px;
}

.credibility-metric .icon {
	width: 20px;
	height: 20px;
	color: #F25EB0;
}

.credibility-metric .value {
	font-size: 18px;
	font-weight: 700;
	color: #1d1d1f;
	margin-right: 4px;
}

.credibility-metric .label {
	font-size: 13px;
	color: #86868b;
	font-weight: 500;
}

/* Clean description text */
.credibility-description {
	font-size: 13px;
	color: #86868b;
	line-height: 1.4;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}


/* Simple case study references */
.case-study-references {
	text-align: center;
	padding-top: 24px;
	border-top: 1px solid #f0f0f0;
}

.case-study-references h5 {
	font-size: 16px;
	font-weight: 600;
	color: #1d1d1f;
	margin-bottom: 16px;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.reference-links {
	display: flex;
	gap: 16px;
	justify-content: center;
	flex-wrap: wrap;
}

/* Simple reference link styling */
.reference-link {
	display: inline-flex;
	align-items: center;
	gap: 6px;
	text-decoration: none;
	font-size: 13px;
	color: #F25EB0;
	font-weight: 500;
	padding: 6px 12px;
	border-radius: 6px;
	background: rgba(242, 94, 176, 0.08);
	border: 1px solid rgba(242, 94, 176, 0.2);
	transition: all 0.2s ease;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.reference-link:hover {
	background: rgba(242, 94, 176, 0.12);
	border-color: rgba(242, 94, 176, 0.3);
	text-decoration: none;
	color: #F25EB0;
}


/* Responsive design for credibility section */
@media (max-width: 768px) {
	.data-credibility-section {
		padding: 24px 20px;
		margin-top: 24px;
	}

	.credibility-header {
		margin-bottom: 24px;
		padding-bottom: 16px;
	}

	.credibility-header h4 {
		font-size: 18px;
	}

	.credibility-header p {
		font-size: 13px;
	}

	.credibility-grid {
		flex-direction: column;
		gap: 16px;
		margin-bottom: 24px;
	}

	.credibility-item {
		max-width: none;
	}

	.credibility-metric {
		justify-content: center;
		padding: 10px 14px;
	}

	.credibility-metric .value {
		font-size: 16px;
	}

	.credibility-metric .label {
		font-size: 12px;
	}

	.credibility-description {
		font-size: 12px;
	}

	.case-study-references {
		padding-top: 20px;
	}

	.case-study-references h5 {
		font-size: 15px;
		margin-bottom: 12px;
	}

	.reference-links {
		flex-direction: column;
		gap: 8px;
		align-items: center;
	}

	.reference-link {
		font-size: 12px;
		padding: 5px 10px;
	}
}

/* ========================================
   DUAL GROWTH COMPARISON SECTION - END
   ======================================== */

/* Metrics Overlay */
.metrics-overlay {
	position: absolute;
	top: 20px;
	right: 20px;
	display: flex;
	flex-direction: column;
	gap: 12px;
	z-index: 10;
}

.metric-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 12px;
	padding: 16px;
	min-width: 160px;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	opacity: 0.7;
	transform: scale(0.95);
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.metric-card.active {
	opacity: 1;
	transform: scale(1);
	box-shadow: 0 8px 24px rgba(242, 94, 176, 0.2);
	border-color: rgba(242, 94, 176, 0.3);
}

.metric-header {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 8px;
}

.metric-icon {
	color: #F25EB0;
	font-size: 16px;
}


.performance-dashboard .metric-icon {
	color: #ffffffbd;
}

.performance-dashboard .metric-title {
	font-size: 12px;
	font-weight: 600;
	color: #86868b;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.performance-dashboard .metric-value {
	font-size: 24px;
	font-weight: 700;
	color: #1d1d1f;
	line-height: 1;
	margin-bottom: 4px;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

.metric-change {
	font-size: 11px;
	color: #30d158;
	font-weight: 500;
}

/* Journey Insights */
.journey-insights {
	display: flex;
	gap: 20px;
	margin-top: 30px;
}

.insight-card {
	flex: 1;
	background: #f5f5f7;
	border-radius: 16px;
	padding: 24px;
	opacity: 0.6;
	transform: translateY(10px);
	transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.insight-card.active {
	opacity: 1;
	transform: translateY(0);
	background: white;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.insight-icon {
	font-size: 32px;
	margin-bottom: 16px;
	display: block;
}

.insight-content h4 {
	font-size: 16px;
	font-weight: 600;
	color: #1d1d1f;
	margin-bottom: 8px;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
}

.insight-content p {
	font-size: 14px;
	color: #86868b;
	line-height: 1.5;
	margin: 0;
}

/* Responsive Design for Dual Growth Comparison */
@media (max-width: 768px) {
	.dual-growth-comparison {
		padding: 24px;
		border-radius: 16px;
	}

	.comparison-title {
		font-size: 20px;
	}

	.comparison-subtitle {
		font-size: 14px;
	}

	.dual-chart-container {
		grid-template-columns: 1fr;
		-ms-grid-columns: 1fr;
		gap: 20px;
	}

	/* Tablet overlay positioning */
	.chart-overlay {
		top: 16px !important;
		right: 16px !important;
		padding: 8px 10px !important;
		min-width: 120px !important;
		min-height: auto !important;
		height: auto !important;
	}

	.traditional-section .chart-overlay {
		top: 16px !important;
	}

	.adzeta-section .chart-overlay {
		top: 40px !important;
	}

	/* Grid fallback for mobile */
	@supports not (display: grid) {
		.dual-chart-container {
			display: block;
		}

		.dual-chart-container .chart-section {
			margin-right: 0;
			margin-bottom: 20px;
		}

		.dual-chart-container .chart-section:last-child {
			margin-bottom: 0;
		}
	}

	.chart-section {
		padding: 20px;
	}

	.chart-icon {
		width: 36px;
		height: 36px;
		font-size: 16px;
	}

	.chart-info h4 {
		font-size: 15px;
	}

	.chart-info p {
		font-size: 11px;
	}

	.growth-chart {
		height: 160px;
	}

	.growth-rate {
		font-size: 16px !important;
	}

	.growth-insights {
		flex-direction: column;
		gap: 16px;
	}

	/* Flexbox fallback for mobile insights */
	@supports not (display: flex) {
		.growth-insights {
			display: block;
		}

		.insight-item {
			margin-bottom: 16px;
		}

		.insight-item:last-child {
			margin-bottom: 0;
		}
	}

	.insight-item {
		padding: 16px;
	}

	.insight-item .insight-icon {
		font-size: 20px;
	}

	.insight-text {
		font-size: 13px;
	}

	.metrics-results-section {
		padding: 30px 20px;
	}

	.metrics-header h3 {
		font-size: 20px;
	}

	.metrics-header p {
		font-size: 14px;
	}
}

@media (max-width: 480px) {
	.dual-growth-comparison {
		padding: 20px;
		margin: 0 -10px;
		border-radius: 12px;
	}

	.comparison-title {
		font-size: 18px;
	}

	.comparison-subtitle {
		font-size: 13px;
		margin-bottom: 20px;
	}

	.animate-button {
		padding: 10px 20px;
		font-size: 13px;
	}

	.dual-chart-container {
		gap: 16px;
	}

	.chart-section {
		padding: 16px;
	}

	.chart-header {
		gap: 10px;
		margin-bottom: 16px;
	}

	.chart-icon {
		width: 32px;
		height: 32px;
		font-size: 14px;
	}

	.chart-info h4 {
		font-size: 14px;
	}

	.chart-info p {
		font-size: 10px;
	}

	.chart-canvas {
		padding: 12px;
	}

	.growth-chart {
		height: 140px;
	}

	/* Enhanced overlay positioning for mobile devices */
	.chart-overlay {
		top: 8px !important;
		right: 8px !important;
		padding: 6px 8px !important;
		min-width: 100px !important;
		font-size: 11px !important;
		min-height: auto !important;
		height: auto !important;
	}

	/* Traditional section overlay - positioned to avoid chart line interference */
	.traditional-section .chart-overlay {
		top: 8px !important;
		right: 8px !important;
	}

	/* Adzeta section overlay - positioned higher to avoid steep growth curve */
	.adzeta-section .chart-overlay {
		top: 35px !important;
		right: 8px !important;
	}

	.growth-rate {
		font-size: 12px !important;
	}

	.growth-label {
		font-size: 10px !important;
	}

	.insight-item {
		padding: 14px;
		gap: 10px;
	}

	.insight-item .insight-icon {
		font-size: 18px;
	}

	.insight-text {
		font-size: 12px;
	}

	.metrics-results-section {
		padding: 24px 16px;
	}

	.metrics-header {
		margin-bottom: 30px;
	}

	.metrics-header h3 {
		font-size: 18px;
	}

	.metrics-header p {
		font-size: 13px;
	}
}

/* Responsive Design */
@media (max-width: 768px) {
	.ltv-comparison-container {
		flex-direction: column;
		gap: 30px;
		padding: 30px 15px;
	}

	.section-title {
		font-size: 18px;
	}

	.progress-number {
		font-size: 28px;
	}

	.ai-circle {
		width: 50px;
		height: 50px;
		font-size: 16px;
	}

	.ai-pulse {
		width: 50px;
		height: 50px;
	}

	.result-number {
		font-size: 32px;
	}

	.result-label {
		font-size: 16px;
	}
}

/* Medium mobile devices - Additional breakpoint for better overlay positioning */
@media (max-width: 640px) {
	.dual-growth-comparison {
		padding: 20px;
	}

	.chart-overlay {
		top: 10px !important;
		right: 10px !important;
		padding: 6px 8px !important;
		min-width: 105px !important;
		min-height: auto !important;
		height: auto !important;
	}

	.traditional-section .chart-overlay {
		top: 10px !important;
	}

	.adzeta-section .chart-overlay {
		top: 36px !important;
	}

	.growth-rate {
		font-size: 13px !important;
	}

	.growth-label {
		font-size: 10px !important;
	}
}

@media (max-width: 480px) {

	/* Dual Growth Comparison - Very small screens */
	.dual-growth-comparison {
		padding: 16px;
		margin: 0 -5px;
	}

	.chart-overlay {
		top: 6px !important;
		right: 6px !important;
		padding: 4px 6px !important;
		min-width: 95px !important;
		font-size: 10px !important;
		min-height: auto !important;
		height: auto !important;
	}

	.traditional-section .chart-overlay {
		top: 6px !important;
	}

	.adzeta-section .chart-overlay {
		top: 2px !important;
	}

	.growth-rate {
		font-size: 12px !important;
		margin: 0 !important;
		line-height: 1 !important;
	}

	.growth-label {
		font-size: 9px !important;
		margin: 1px 0 0 0 !important;
		line-height: 1 !important;
	}

	.ltv-comparison-container {
		padding: 20px 10px;
		gap: 25px;
	}

	.section-title {
		font-size: 16px;
	}

	.progress-number {
		font-size: 24px;
	}

	.customer-dots {
		max-width: 100px;
		gap: 4px;
	}

	.customer-dot {
		width: 6px;
		height: 6px;
	}

	.customer-dot.large {
		width: 10px;
		height: 10px;
	}

	.result-number {
		font-size: 28px;
	}

	.result-label {
		font-size: 14px;
	}
}


/* Scale Section Accordion - Matching ValueBid Section Style */

.accordion-style-01 {

	margin-bottom: 20px;

}


.accordion-style-01 .accordion-item {

	border: none;

	margin-bottom: 16px;

	border-radius: 16px !important;

	overflow: hidden;

	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

	background-color: #ffffff;

	transition: transform 0.2s ease, box-shadow 0.2s ease;
	padding: 0 !important;
}


.accordion-style-01 .accordion-item:hover {

	transform: translateY(-2px);

	box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);

}


.accordion-style-01 .accordion-header {

	padding: 0;

	background-color: #ffffff;

}


.accordion-style-01 .accordion-title {

	padding: 16px 20px;

	font-size: 16px;

	font-weight: 600;

	color: #333;

	display: flex;

	align-items: center;

	position: relative;

}


.accordion-style-01 .accordion-title .feature-icon-wrapper {

	margin-right: 12px;

	display: inline-flex;

}


.accordion-style-01 .accordion-title .feature-icon-wrapper i {

	font-size: 18px;

	background: linear-gradient(45deg, #f45888 0%, #ee5c46 100%);

	-webkit-background-clip: text;

	-webkit-text-fill-color: transparent;

	background-clip: text;

}


.accordion-style-01 .accordion-title i.icon-small {

	position: absolute;

	right: 20px;

	top: 50%;

	transform: translateY(-50%);

	font-size: 14px;

	color: #999;

	transition: all 0.3s ease;

	margin-left: auto;

}


.accordion-style-01 .accordion-body {

	padding: 0 20px 20px 20px !important;

	color: #666;

	font-size: 14px;

	line-height: 1.6;

}


.accordion-style-01 .active-accordion .accordion-title i.icon-small {

	transform: translateY(-50%) rotate(180deg);

}


/* Apple-style Metric Cards */

.apple-metric-card {

	padding: 28px;

	border-radius: 16px;

	background-color: #ffffff;

	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

	transition: all 0.2s ease;

	height: 100%;

	display: flex;

	flex-direction: column;

	position: relative;

	margin-bottom: 16px;

}


.apple-metric-card:hover {

	transform: translateY(-3px);

	box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);

}


.apple-metric-card .metric-icon {

	margin-bottom: 16px;

}


.apple-metric-card .metric-icon i {

	font-size: 22px;

	background: linear-gradient(45deg, #f45888 0%, #ee5c46 100%);

	-webkit-background-clip: text;

	-webkit-text-fill-color: transparent;

	background-clip: text;

}


.apple-metric-card.reduction .metric-icon i {

	background: linear-gradient(45deg, #6c7ae0 0%, #4e5bd0 100%);

	-webkit-background-clip: text;

	-webkit-text-fill-color: transparent;

	background-clip: text;

}


.apple-metric-card .metric-label {

	font-size: 16px;

	font-weight: 600;

	color: #333;

	margin-bottom: 8px;

	letter-spacing: -0.2px;

}


.apple-metric-card .metric-value {

	font-size: 32px;

	font-weight: 700;

	margin-bottom: 12px;

	letter-spacing: -0.5px;

}


.apple-metric-card.positive .metric-value {

	color: #f45888;

}


.apple-metric-card.reduction .metric-value {

	color: #6c7ae0;

}


.apple-metric-card .metric-description {

	font-size: 16px;

	line-height: 1.5;

	color: #666;

	flex-grow: 1;

}


.getting-started-section {

	background: linear-gradient(to bottom, #F8F7F6 0%, #ffffff 100%);

}


.getting-started-journey {

	display: flex;

	flex-direction: column;

	gap: 30px;

	max-width: 900px;

	margin: 0 auto;

}


.journey-step {

	display: flex;

	align-items: flex-start;

	background-color: #fff;

	border-radius: 16px;

	padding: 24px;

	box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);

	transition: all 0.3s ease;

	border-left: 3px solid transparent;

}


.journey-step:hover {

	transform: translateY(-5px);

	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);

	border-left: 3px solid #e958a1;

}


.step-icon-container {

	position: relative;

	width: 60px;

	height: 60px;

	border-radius: 50%;

	background: linear-gradient(135deg, rgba(233, 88, 161, 0.1), rgba(255, 93, 116, 0.05));

	display: flex;

	align-items: center;

	justify-content: center;

	margin-right: 20px;

	flex-shrink: 0;

}


.step-icon-container i {

	font-size: 24px;

	color: #e958a1;

}


.step-number {

	position: absolute;

	top: -5px;

	right: -5px;

	width: 24px;

	height: 24px;

	border-radius: 50%;

	background: linear-gradient(to right, #e958a1, #ff5d74);

	color: white;

	font-size: 12px;

	font-weight: 600;

	display: flex;

	align-items: center;

	justify-content: center;

	box-shadow: 0 2px 5px rgba(233, 88, 161, 0.3);

}


.step-content {

	flex: 1;

}


.step-content h4 {

	margin-top: 0;

	margin-bottom: 10px;

	font-size: 18px;

}


.step-content p {

	margin-bottom: 0;

	color: #555;

	font-size: 15px;

	line-height: 1.5;

}


@media (max-width: 767px) {

	.journey-step {

		flex-direction: column;

		align-items: center;

		text-align: center;

		padding: 20px 15px;

	}


	.step-icon-container {

		margin-right: 0;

		margin-bottom: 15px;

	}


	.step-content h4 {

		font-size: 16px;

	}


	.step-content p {

		font-size: 14px;

	}

}


/* .scale-section::before {

    content: "";

    position: absolute;

    top: 0;

    left: 0;

    right: 0;

    bottom: 0;

    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f0f0f0' fill-opacity='0.4' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/svg%3E");

    opacity: 0.5;

    z-index: 0;

    pointer-events: none;

} */


.scale-section .container {

	position: relative;

	z-index: 1;

}


.scale-section .row {

	display: flex;

	flex-wrap: wrap;

	margin-bottom: 30px;

}


.scale-section .row:last-child {

	margin-bottom: 0;

}


.scale-section .row>[class*='col-'] {

	display: flex;

	flex-direction: column;

}


#accordion-scale {

	height: 100%;

	display: flex;

	flex-direction: column;

}


.scale-metrics-container {

	height: 100%;

}


.scale-metrics-container .row {

	height: 100%;

	margin-bottom: 20px;

}


.scale-metrics-container .row:last-child {

	margin-bottom: 0;

}


/* HTML/CSS Growth Chart Visualization */
.growth-chart-container {
	width: 100%;
	height: 400px;
	background: #ffffff;
	border-radius: 16px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
	position: relative;
	overflow: hidden;
	margin: 20px 0;
}

.chart-area {
	position: absolute;
	top: 40px;
	left: 60px;
	right: 40px;
	bottom: 60px;
	border-left: 2px solid #e0e0e0;
	border-bottom: 2px solid #e0e0e0;
}

/* Grid Lines */
.grid-line {
	position: absolute;
	background: #f0f0f0;
}

.grid-line.horizontal {
	width: 100%;
	height: 1px;
}

.grid-line.vertical {
	width: 1px;
	height: 100%;
}

/* Y-axis labels */
.y-label {
	position: absolute;
	left: -50px;
	font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
	font-size: 16px;
	color: #999;
	text-align: right;
	width: 40px;
	transform: translateY(-50%);
}

/* X-axis labels */
.x-label {
	position: absolute;
	bottom: -40px;
	font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
	font-size: 16px;
	color: #999;
	text-align: center;
	transform: translateX(-50%);
}

/* Growth Lines SVG */
.growth-lines {
	position: absolute;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

.traditional-line {
	stroke-dasharray: 1000;
	stroke-dashoffset: 1000;
	animation: drawLine 2s ease-out forwards;
	animation-delay: 0.3s;
}

.ai-line {
	stroke-dasharray: 1000;
	stroke-dashoffset: 1000;
	animation: drawLine 2s ease-out forwards;
	animation-delay: 0.8s;
}

/* Data Points */
.data-point {
	position: absolute;
	width: 8px;
	height: 8px;
	border-radius: 50%;
	transform: translate(-50%, -50%);
	z-index: 10;
	opacity: 0;
	animation: fadeIn 0.3s ease-out forwards;
}

.traditional-point {
	background: #aaaaaa;
	width: 6px;
	height: 6px;
}

.ai-point {
	background: linear-gradient(45deg, #f45888 0%, #ee5c46 100%);
	width: 8px;
	height: 8px;
	box-shadow: 0 2px 8px rgba(244, 88, 136, 0.4);
}

.data-point:nth-child(1) {
	animation-delay: 1.8s;
}

.data-point:nth-child(2) {
	animation-delay: 2.0s;
}

.data-point:nth-child(3) {
	animation-delay: 2.2s;
}

.data-point:nth-child(4) {
	animation-delay: 2.4s;
}

.data-point:nth-child(5) {
	animation-delay: 2.6s;
}

.data-point:nth-child(6) {
	animation-delay: 2.8s;
}

/* Line Labels */
.line-label {
	position: absolute;
	font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
	font-size: 14px;
	white-space: nowrap;
	z-index: 15;
}

.traditional-label {
	color: #aaaaaa;
	right: -180px;
	top: 46%;
}

.ai-label {
	color: #f45888;
	font-weight: 600;
	right: -160px;
	top: 10%;
}

/* Annotations */
.annotation {
	position: absolute;
	z-index: 20;
}

.annotation-box {
	background: white;
	border: 1px solid #f0f0f0;
	border-radius: 15px;
	padding: 8px 16px;
	font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
	font-size: 12px;
	white-space: nowrap;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.annotation.diminishing {
	top: 40%;
	right: 20%;
}

.annotation.diminishing .annotation-box {
	color: #aaaaaa;
}

.annotation.ai-advantage {
	top: 20%;
	right: 15%;
}

.annotation.ai-advantage .annotation-box {
	color: #f45888;
}

/* Animations */
@keyframes drawLine {
	to {
		stroke-dashoffset: 0;
	}
}

@keyframes fadeIn {
	to {
		opacity: 1;
	}
}

/* Mobile responsive adjustments */
@media (max-width: 767px) {
	.growth-chart-container {
		height: 450px;
		margin: 30px 0;
		border-radius: 12px;
	}

	.y-label,
	.x-label {
		font-size: 18px;
	}

	.line-label {
		font-size: 16px;
	}

	.annotation-box {
		font-size: 14px;
		padding: 10px 18px;
	}

	.chart-area {
		left: 70px;
		bottom: 70px;
	}

	.y-label {
		left: -60px;
		width: 50px;
	}

	.x-label {
		bottom: -50px;
	}
}

@media (max-width: 480px) {
	.growth-chart-container {
		height: 400px;
	}

	.y-label,
	.x-label {
		font-size: 20px;
	}

	.line-label {
		font-size: 18px;
	}

	.annotation {
		display: none;
		/* Hide annotations on very small screens for clarity */
	}
}


@media (max-width: 991px) {

	.scale-section .row>[class*='col-'] {

		height: auto;

	}


	.scale-section {

		padding-bottom: 60px;

	}

}


@media (max-width: 767px) {

	.scale-section {

		padding-bottom: 40px;

	}

}


/* platform Scale Section ends*/


/* Responsive adjustments */

@media (max-width: 768px) {

	.data-flow-line {

		opacity: 0;

		display: none;

	}


	.particle-burst {

		width: 10px;

		height: 10px;

	}

}

/* AI Decision Section */
.ai-decision-section {
	padding: 100px 0;
	background: linear-gradient(135deg, #09050D 0%, #1C0E26 25%, #3F1D40 50%, #73305B 75%, #3F1D40 100%);
	position: relative;
	overflow: hidden;
}

/* Connection Bridge Elements */
.ai-simulator-container .connection-bridge {
	max-width: 600px;
	margin: 0 auto;
}

.ai-simulator-container .connection-summary {
	background: rgba(255, 255, 255, 0.03);
	border: 1px solid rgba(242, 94, 176, 0.1);
	border-radius: 16px;
	backdrop-filter: blur(10px);
	max-width: 700px;
	margin: 0 auto;
}

.ai-simulator-container .next-step-indicator {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 5px;
}

.ai-simulator-container .next-step-indicator i {
	animation: bounceArrow 2s ease-in-out infinite;
}

@keyframes bounceArrow {

	0%,
	100% {
		transform: translateY(0px);
		opacity: 0.7;
	}

	50% {
		transform: translateY(5px);
		opacity: 1;
	}
}

.ai-decision-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(circle at 85% 20%, rgba(242, 242, 242, 0.08) 0%, transparent 50%),
		radial-gradient(circle at 15% 85%, rgba(115, 48, 91, 0.15) 0%, transparent 50%),
		radial-gradient(circle at 0% 0%, rgba(9, 5, 13, 0.6) 0%, transparent 40%),
		radial-gradient(circle at 100% 0%, rgba(28, 14, 38, 0.4) 0%, transparent 35%),
		radial-gradient(circle at 0% 100%, rgba(63, 29, 64, 0.3) 0%, transparent 35%),
		radial-gradient(circle at 100% 100%, rgba(9, 5, 13, 0.5) 0%, transparent 40%);
	pointer-events: none;
}

/* AI Decision Engine Simulator */
.ai-simulator-container {
	background: transparent;
	border-radius: 0;
	padding: 0;
	box-shadow: none;
	border: none;
	position: relative;
	overflow: visible;
	color: white;
}

.ai-simulator-container::before {
	display: none;
}

.ai-simulator-container::after {
	display: none;
}

/* AI Section Gradient for Headings */
.ai-section-gradient {
	background-image: linear-gradient(132deg, #F2F2F2 0%, #A29CA6 20%, #F25EB0 40%, #F24987 70%, #7D6BF2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	color: transparent;
}

/* Status Indicator */
.ai-simulator-container .status-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10px;
	margin-top: 15px;
}

.ai-simulator-container .status-dot {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #00ff88;
	position: relative;
}

.ai-simulator-container .status-dot.pulsing {
	animation: statusPulse 2s infinite;
}

.ai-simulator-container .status-text {
	color: #00ff88;
	font-size: 14px;
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 1px;
}

/* AI Brain Container */
.ai-simulator-container .ai-brain-container {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 400px;
	margin: 40px 0;
}

.ai-simulator-container .brain-core {
	position: relative;
	width: 400px;
	height: 400px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: transparent;
	border: none;
}


/* Processing Center */
.ai-simulator-container .processing-center {
	background: transparent;
	border: none;
	border-radius: 0;
	width: 350px;
	height: 350px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 10;
	position: relative;
}

.ai-simulator-container .ai-processing-image {
	width: 350px;
	height: 350px;
	object-fit: contain;
	animation: processingFloat 6s ease-in-out infinite, processingGlow 4s infinite;
	filter: drop-shadow(0 0 40px rgba(242, 94, 176, 0.6)) drop-shadow(0 0 80px rgba(125, 107, 242, 0.3));
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	transition: all 0.3s ease;
}

.ai-simulator-container .processing-text {
	font-size: 12px;
	font-weight: 700;
	color: #F25EB0;
	text-transform: uppercase;
	letter-spacing: 1px;
	position: absolute;
	bottom: -40px;
	left: 50%;
	transform: translateX(-50%);
	white-space: nowrap;
	z-index: 11;
}

.ai-simulator-container .processing-speed {
	position: absolute;
	bottom: -50px;
	left: 50%;
	transform: translateX(-50%);
	white-space: nowrap;
	z-index: 11;
	display: flex;
	align-items: baseline;
	gap: 8px;
	font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.ai-simulator-container .processing-count {
	font-size: 18px;
	font-weight: 600;
	color: #F25EB0;
	background: linear-gradient(135deg, #F25EB0 0%, #7D6BF2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	letter-spacing: -0.5px;
	animation: countPulse 2s ease-in-out infinite;
	transition: transform 0.15s ease, filter 0.15s ease;
	position: relative;
}

.ai-simulator-container .processing-count::after {
	content: '';
	position: absolute;
	top: -2px;
	right: -8px;
	width: 6px;
	height: 6px;
	background: #F25EB0;
	border-radius: 50%;
	animation: livePulse 1.5s ease-in-out infinite;
	opacity: 0.8;
}

.ai-simulator-container .processing-unit {
	font-size: 11px;
	font-weight: 400;
	color: #A29CA6;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	opacity: 0.8;
	animation: unitFlicker 4s ease-in-out infinite;
}

/* Data Streams */
.ai-simulator-container .data-streams {
	position: absolute;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.ai-simulator-container .stream-container {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.ai-simulator-container .left-streams {
	align-items: flex-end;
	text-align: right;
}

.ai-simulator-container .right-streams {
	align-items: flex-start;
	text-align: left;
}

.ai-simulator-container .data-stream,
.ai-simulator-container .optimization-output {
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 12px;
	padding: 15px 20px;
	min-width: 200px;
	backdrop-filter: blur(10px);
	transition: all 0.3s ease;
}

.ai-simulator-container .data-stream:hover,
.ai-simulator-container .optimization-output:hover {
	background: rgba(255, 255, 255, 0.1);
	border-color: #F25EB0;
	box-shadow: 0 8px 32px rgba(242, 94, 176, 0.2);
}

.ai-simulator-container .stream-label,
.ai-simulator-container .output-label {
	font-size: 11px;
	color: #999;
	text-transform: uppercase;
	letter-spacing: 1px;
	margin-bottom: 8px;
}

.ai-simulator-container .stream-value,
.ai-simulator-container .output-value {
	font-size: 14px;
	font-weight: 600;
	color: white;
	margin-bottom: 5px;
}

.ai-simulator-container .changing-value {
	animation: valueFlicker 3s infinite;
}

.ai-simulator-container .stream-indicator {
	width: 8px;
	height: 8px;
	border-radius: 50%;
	background: #00ff88;
	margin-top: 5px;
	animation: indicatorPulse 2s infinite;
}

.ai-simulator-container .output-change {
	font-size: 12px;
	font-weight: 600;
}

.ai-simulator-container .output-change.positive {
	color: #00ff88;
}

.ai-simulator-container .output-change.negative {
	color: #ff4444;
}

/* Performance Dashboard */
.ai-simulator-container .performance-dashboard {
	background: rgba(255, 255, 255, 0.03);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 20px;
	padding: 30px;
	backdrop-filter: blur(10px);
}

.ai-simulator-container .dashboard-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25px;
}

.ai-simulator-container .refresh-indicator {
	display: flex;
	align-items: center;
	gap: 8px;
	color: #ddd3d9d1;
	font-size: 12px;
}

.ai-simulator-container .refresh-icon {
	font-size: 14px;
}

.ai-simulator-container .refresh-icon.rotating {
	animation: rotate 2s linear infinite;
}

.ai-simulator-container .metrics-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 20px;
}

.ai-simulator-container .metric-tile {
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 16px;
	padding: 25px;
	display: flex;
	align-items: center;
	gap: 20px;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
}

.ai-simulator-container .metric-tile:hover {
	background: rgba(255, 255, 255, 0.08);
	border-color: #F25EB0;
	transform: translateY(-2px);
	box-shadow: 0 8px 32px rgba(242, 94, 176, 0.2);
}

.ai-simulator-container .metric-icon {
	font-size: 24px;
	width: 50px;
	height: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #F24987 0%, #7D6BF2 100%);
	border-radius: 12px;
	box-shadow: 0 4px 16px rgba(242, 73, 135, 0.3);
}

.ai-simulator-container .metric-content {
	flex: 1;
}

.ai-simulator-container .metric-label {
	font-size: 12px;
	color: #999;
	text-transform: uppercase;
	letter-spacing: 1px;
	margin-bottom: 8px;
}

.ai-simulator-container .metric-value {
	font-size: 24px;
	font-weight: 700;
	color: white;
	margin-bottom: 5px;
}

.ai-simulator-container .live-counter {
	background: linear-gradient(135deg, #F25EB0 0%, #7D6BF2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.ai-simulator-container .metric-trend {
	font-size: 11px;
	color: #00ff88;
	font-weight: 500;
}

.ai-simulator-container .metric-trend.positive {
	color: #00ff88;
}

.ai-simulator-container .metric-trend.negative {
	color: #ff4444;
}

/* Decision Log */
.ai-simulator-container .decision-log {
	background: rgba(255, 255, 255, 0.03);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 16px;
	padding: 25px;
	backdrop-filter: blur(10px);
}

.ai-simulator-container .log-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
}

.ai-simulator-container .log-status {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 12px;
	color: #999;
}

.ai-simulator-container .status-dot.active {
	background: #00ff88;
	animation: statusPulse 2s infinite;
}

.ai-simulator-container .log-container {
	display: flex;
	flex-direction: column;
	gap: 12px;
	max-height: 200px;
	overflow-y: auto;
	padding-right: 8px;

	/* Webkit browsers (Chrome, Safari, Edge) */
	scrollbar-width: thin;
	scrollbar-color: rgba(242, 94, 176, 0.6) rgba(255, 255, 255, 0.1);
}

/* Webkit scrollbar styling */
.ai-simulator-container .log-container::-webkit-scrollbar {
	width: 6px;
}

.ai-simulator-container .log-container::-webkit-scrollbar-track {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 3px;
}

.ai-simulator-container .log-container::-webkit-scrollbar-thumb {
	background: linear-gradient(180deg, #F25EB0 0%, #7D6BF2 100%);
	border-radius: 3px;
	transition: background 0.3s ease;
}

.ai-simulator-container .log-container::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(180deg, #F24987 0%, #73305B 100%);
}

.ai-simulator-container .log-container::-webkit-scrollbar-thumb:active {
	background: linear-gradient(180deg, #F25781 0%, #3F1D40 100%);
}

.ai-simulator-container .log-entry {
	display: flex;
	align-items: center;
	gap: 15px;
	padding: 12px 15px;
	background: rgba(255, 255, 255, 0.02);
	border: 1px solid rgba(255, 255, 255, 0.05);
	border-radius: 8px;
	transition: all 0.3s ease;
	animation: logEntrySlide 0.5s ease-out;
}

.ai-simulator-container .log-entry:hover {
	background: rgba(255, 255, 255, 0.05);
	border-color: rgba(242, 94, 176, 0.3);
}

.ai-simulator-container .log-time {
	font-size: 11px;
	color: #999;
	font-family: 'Courier New', monospace;
	min-width: 60px;
}

.ai-simulator-container .log-action {
	flex: 1;
	font-size: 13px;
	color: white;
}

.ai-simulator-container .log-result {
	font-size: 12px;
	font-weight: 600;
	padding: 4px 8px;
	border-radius: 6px;
	background: rgba(0, 255, 136, 0.1);
	color: #00ff88;
	border: 1px solid rgba(0, 255, 136, 0.2);
}

/* AI Simulator Animations */
@keyframes statusPulse {

	0%,
	100% {
		opacity: 1;
		transform: scale(1);
	}

	50% {
		opacity: 0.6;
		transform: scale(1.2);
	}
}


@keyframes processingGlow {

	0%,
	100% {
		filter: drop-shadow(0 0 30px rgba(242, 94, 176, 0.6)) drop-shadow(0 0 60px rgba(125, 107, 242, 0.3));
	}

	50% {
		filter: drop-shadow(0 0 50px rgba(242, 94, 176, 0.9)) drop-shadow(0 0 80px rgba(125, 107, 242, 0.5));
	}
}

@keyframes processingFloat {

	0%,
	100% {
		transform: translate(-50%, -50%) translateY(0px) rotate(0deg);
	}

	33% {
		transform: translate(-50%, -50%) translateY(-8px) rotate(1deg);
	}

	66% {
		transform: translate(-50%, -50%) translateY(4px) rotate(-1deg);
	}
}

@keyframes brainPulse {

	0%,
	100% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.1);
	}
}

@keyframes valueFlicker {

	0%,
	90%,
	100% {
		opacity: 1;
	}

	95% {
		opacity: 0.7;
	}
}

@keyframes indicatorPulse {

	0%,
	100% {
		opacity: 1;
		transform: scale(1);
	}

	50% {
		opacity: 0.5;
		transform: scale(1.5);
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

@keyframes logEntrySlide {
	from {
		transform: translateX(-20px);
		opacity: 0;
	}

	to {
		transform: translateX(0);
		opacity: 1;
	}
}

@keyframes countUp {
	from {
		opacity: 0;
		transform: translateY(10px);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes drawPath {
	to {
		stroke-dashoffset: 0;
	}
}

@keyframes countPulse {

	0%,
	100% {
		opacity: 1;
		transform: scale(1);
	}

	50% {
		opacity: 0.9;
		transform: scale(1.02);
	}
}

@keyframes livePulse {

	0%,
	100% {
		opacity: 0.8;
		transform: scale(1);
		box-shadow: 0 0 0 0 rgba(242, 94, 176, 0.7);
	}

	50% {
		opacity: 1;
		transform: scale(1.2);
		box-shadow: 0 0 0 4px rgba(242, 94, 176, 0);
	}
}

@keyframes unitFlicker {

	0%,
	90%,
	100% {
		opacity: 0.8;
	}

	95% {
		opacity: 0.6;
	}
}

/* Mobile-First Responsive Design */
@media (max-width: 991px) {
	.ai-simulator-container {
		padding: 25px 15px;
		margin: 0 -15px;
		border-radius: 0;
	}

	.ai-simulator-container .simulator-header {
		margin-bottom: 30px;
	}

	.ai-simulator-container .simulator-header h3 {
		font-size: 24px;
		line-height: 1.3;
	}

	.ai-simulator-container .simulator-header p {
		font-size: 14px;
		margin-bottom: 15px;
	}

	/* Mobile Brain Layout */
	.ai-simulator-container .ai-brain-container {
		min-height: 280px;
		margin: 25px 0;
		flex-direction: column;
		gap: 25px;
	}

	.ai-simulator-container .brain-core {
		width: 280px;
		height: 280px;
		order: 2;
	}

	.ai-simulator-container .processing-center {
		width: 250px;
		height: 250px;
	}

	.ai-simulator-container .ai-processing-image {
		width: 250px;
		height: 250px;
	}

	.ai-simulator-container .processing-text {
		font-size: 10px;
		bottom: -30px;
	}

	.ai-simulator-container .processing-speed {
		bottom: -40px;
		gap: 6px;
	}

	.ai-simulator-container .processing-count {
		font-size: 16px;
	}

	.ai-simulator-container .processing-unit {
		font-size: 10px;
	}

	/* Mobile Data Streams */
	.ai-simulator-container .data-streams {
		position: static;
		order: 1;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 15px;
		width: 100%;
	}

	.ai-simulator-container .stream-container {
		gap: 12px;
	}

	.ai-simulator-container .left-streams {
		align-items: flex-start;
		text-align: left;
	}

	.ai-simulator-container .right-streams {
		align-items: flex-start;
		text-align: left;
	}

	.ai-simulator-container .data-stream,
	.ai-simulator-container .optimization-output {
		min-width: auto;
		width: 100%;
		padding: 12px 15px;
		font-size: 12px;
	}

	.ai-simulator-container .stream-label,
	.ai-simulator-container .output-label {
		font-size: 10px;
		margin-bottom: 6px;
	}

	.ai-simulator-container .stream-value,
	.ai-simulator-container .output-value {
		font-size: 13px;
		margin-bottom: 4px;
	}

	.ai-simulator-container .output-change {
		font-size: 11px;
	}

	/* Mobile Performance Dashboard */
	.ai-simulator-container .performance-dashboard {
		padding: 20px 15px;
		margin-top: 25px;
	}

	.ai-simulator-container .dashboard-header {
		flex-direction: column;
		align-items: flex-start;
		gap: 10px;
		margin-bottom: 20px;
	}

	.ai-simulator-container .dashboard-header h4 {
		font-size: 18px;
	}

	.ai-simulator-container .refresh-indicator {
		font-size: 11px;
	}

	.ai-simulator-container .metrics-grid {
		grid-template-columns: 1fr 1fr;
		gap: 12px;
	}

	.ai-simulator-container .metric-tile {
		padding: 15px 12px;
		flex-direction: column;
		text-align: center;
		gap: 10px;
	}

	.ai-simulator-container .metric-icon {
		width: 40px;
		height: 40px;
		font-size: 18px;
		margin: 0 auto;
	}

	.ai-simulator-container .metric-label {
		font-size: 10px;
		margin-bottom: 5px;
	}

	.ai-simulator-container .metric-value {
		font-size: 18px;
		margin-bottom: 3px;
	}

	.ai-simulator-container .metric-trend {
		font-size: 10px;
	}
}

/* Small Mobile Devices */
@media (max-width: 767px) {
	.ai-simulator-container {
		padding: 20px 10px;
		margin: 0 -10px;
	}

	.ai-simulator-container .simulator-header h3 {
		font-size: 20px;
		margin-bottom: 10px;
	}

	.ai-simulator-container .simulator-header p {
		font-size: 13px;
		margin-bottom: 12px;
	}

	.ai-simulator-container .status-indicator {
		margin-top: 10px;
	}

	.ai-simulator-container .status-text {
		font-size: 12px;
	}

	/* Compact Brain Layout */
	.ai-simulator-container .ai-brain-container {
		min-height: 220px;
		margin: 20px 0;
		gap: 20px;
	}

	.ai-simulator-container .brain-core {
		width: 220px;
		height: 220px;
	}

	.ai-simulator-container .processing-center {
		width: 200px;
		height: 200px;
	}

	.ai-simulator-container .ai-processing-image {
		width: 200px;
		height: 200px;
	}

	.ai-simulator-container .processing-text {
		font-size: 8px;
		bottom: -25px;
	}

	.ai-simulator-container .processing-speed {
		bottom: -35px;
		gap: 5px;
	}

	.ai-simulator-container .processing-count {
		font-size: 14px;
	}

	.ai-simulator-container .processing-unit {
		font-size: 9px;
	}

	/* Mobile scrollbar adjustments */
	.ai-simulator-container .log-container {
		padding-right: 4px;
		max-height: 180px;
	}

	.ai-simulator-container .log-container::-webkit-scrollbar {
		width: 4px;
	}

	.ai-simulator-container .log-container::-webkit-scrollbar-thumb {
		border-radius: 2px;
	}

	/* Single Column Data Streams */
	.ai-simulator-container .data-streams {
		grid-template-columns: 1fr;
		gap: 10px;
		order: 3;
		margin-top: 20px;
	}

	.ai-simulator-container .stream-container {
		gap: 8px;
	}

	.ai-simulator-container .data-stream,
	.ai-simulator-container .optimization-output {
		padding: 10px 12px;
		font-size: 11px;
	}

	.ai-simulator-container .stream-label,
	.ai-simulator-container .output-label {
		font-size: 9px;
		margin-bottom: 4px;
	}

	.ai-simulator-container .stream-value,
	.ai-simulator-container .output-value {
		font-size: 12px;
		margin-bottom: 3px;
	}

	.ai-simulator-container .output-change {
		font-size: 10px;
	}

	/* Compact Dashboard */
	.ai-simulator-container .performance-dashboard {
		padding: 15px 10px;
		margin-top: 20px;
	}

	.ai-simulator-container .dashboard-header h4 {
		font-size: 16px;
		margin-bottom: 15px;
	}

	.ai-simulator-container .refresh-indicator {
		font-size: 10px;
	}

	.ai-simulator-container .metrics-grid {
		grid-template-columns: 1fr;
		gap: 10px;
	}

	.ai-simulator-container .metric-tile {
		padding: 12px 10px;
		gap: 8px;
	}

	.ai-simulator-container .metric-icon {
		width: 35px;
		height: 35px;
		font-size: 16px;
	}

	.ai-simulator-container .metric-label {
		font-size: 9px;
		margin-bottom: 4px;
	}

	.ai-simulator-container .metric-value {
		font-size: 16px;
		margin-bottom: 2px;
	}

	.ai-simulator-container .metric-trend {
		font-size: 9px;
	}

	/* Compact Decision Log */
	.ai-simulator-container .decision-log {
		padding: 15px 10px;
		margin-top: 20px;
	}

	.ai-simulator-container .log-header h5 {
		font-size: 14px;
	}

	.ai-simulator-container .log-status {
		font-size: 10px;
	}

	.ai-simulator-container .log-container {
		max-height: 150px;
	}

	.ai-simulator-container .log-entry {
		flex-direction: column;
		align-items: flex-start;
		gap: 6px;
		padding: 10px 12px;
	}

	.ai-simulator-container .log-time {
		font-size: 10px;
		min-width: auto;
	}

	.ai-simulator-container .log-action {
		font-size: 11px;
		line-height: 1.3;
	}

	.ai-simulator-container .log-result {
		font-size: 10px;
		padding: 3px 6px;
		align-self: flex-end;
	}
}

/* Extra Small Devices */
@media (max-width: 480px) {
	.ai-simulator-container {
		padding: 15px 8px;
	}

	.ai-simulator-container .brain-core {
		width: 140px;
		height: 140px;
	}

	.ai-simulator-container .processing-center {
		width: 160px;
		height: 160px;
	}

	.ai-simulator-container .ai-processing-image {
		width: 160px;
		height: 160px;
	}

	.ai-simulator-container .neural-node {
		width: 16px;
		height: 16px;
	}

	.ai-simulator-container .data-stream,
	.ai-simulator-container .optimization-output {
		padding: 8px 10px;
	}

	.ai-simulator-container .metrics-grid {
		gap: 8px;
	}

	.ai-simulator-container .metric-tile {
		padding: 10px 8px;
	}
}

/* Interactive Insights */
.interactive-insights {
	background: #f8f9fa;
	border-radius: 16px;
	padding: 30px;
}

.insight-cards {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 25px;
}

.insight-card {
	display: flex;
	align-items: center;
	gap: 20px;
	background: white;
	padding: 25px;
	border-radius: 16px;
	box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
	border: 1px solid #e9ecef;
	transition: all 0.3s ease;
	cursor: pointer;
}

.insight-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* Progress Ring */
.progress-ring {
	position: relative;
	width: 60px;
	height: 60px;
}

.progress-svg {
	transform: rotate(-90deg);
}

.progress-circle {
	transition: stroke-dashoffset 2s ease-out;
	animation: progressRing 2s ease-out forwards;
	animation-delay: 2s;
}

.progress-text {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	font-size: 14px;
	font-weight: 700;
	color: #f45888;
}

/* Efficiency Bars */
.efficiency-bars {
	display: flex;
	align-items: flex-end;
	gap: 8px;
	height: 40px;
	width: 60px;
}

.bar {
	flex: 1;
	background: #dee2e6;
	border-radius: 4px 4px 2px 2px;
	transition: height 1.5s ease-out;
	animation: barGrow 1.5s ease-out forwards;
	animation-delay: 2.5s;
}

.ai-bar {
	background: linear-gradient(180deg, #f45888 0%, #ee5c46 100%);
}

/* Growth Indicator */
.growth-indicator {
	position: relative;
	width: 60px;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.growth-arrow {
	font-size: 24px;
	animation: bounce 2s infinite;
}

.growth-pulse {
	position: absolute;
	width: 100%;
	height: 100%;
	border: 2px solid #f45888;
	border-radius: 50%;
	animation: pulse 2s infinite;
}

/* Animations */
@keyframes towerGrow {
	from {
		height: 0%;
	}

	to {
		height: var(--tower-height, 50%);
	}
}

@keyframes drawPath {
	to {
		stroke-dashoffset: 0;
	}
}

@keyframes fadeInPoint {
	to {
		opacity: 1;
	}
}

@keyframes progressRing {
	from {
		stroke-dashoffset: 157;
	}

	to {
		stroke-dashoffset: 47;
	}
}

@keyframes barGrow {
	from {
		height: 0%;
	}
}

@keyframes bounce {

	0%,
	20%,
	50%,
	80%,
	100% {
		transform: translateY(0);
	}

	40% {
		transform: translateY(-10px);
	}

	60% {
		transform: translateY(-5px);
	}
}

@keyframes pulse {
	0% {
		transform: scale(0.8);
		opacity: 1;
	}

	100% {
		transform: scale(1.2);
		opacity: 0;
	}
}

/* Responsive Design */
@media (max-width: 991px) {
	.growth-visualization-container {
		padding: 30px 20px;
	}

	.control-buttons {
		flex-direction: column;
		gap: 10px;
	}

	.chart-btn {
		justify-content: center;
		padding: 15px 20px;
	}

	.revenue-towers {
		height: 150px;
		margin-bottom: 30px;
	}

	.revenue-tower {
		width: 60px;
		height: 120px;
	}

	.trajectory-svg {
		height: 250px;
	}

	.metrics-overlay {
		position: static;
		flex-direction: row;
		justify-content: center;
		margin-top: 20px;
	}

	.insight-cards {
		grid-template-columns: 1fr;
		gap: 20px;
	}
}

@media (max-width: 767px) {
	.chart-display {
		padding: 20px;
		min-height: 300px;
	}

	.revenue-towers {
		height: 120px;
		margin-bottom: 20px;
	}

	.revenue-tower {
		width: 50px;
		height: 100px;
	}

	.tower-value {
		font-size: 16px;
	}

	.tower-subtitle {
		font-size: 10px;
	}

	.trajectory-svg {
		height: 200px;
	}

	.metrics-overlay {
		flex-direction: column;
		align-items: center;
	}

	.metric-card {
		min-width: 140px;
		padding: 12px 16px;
	}

	.metric-number {
		font-size: 16px;
	}

	.insight-card {
		flex-direction: column;
		text-align: center;
		gap: 15px;
	}

	.interactive-insights {
		padding: 20px;
	}
}