<?php
/**
 * Case Studies Database Setup
 * Ensures the case_studies table exists and is properly configured
 */

require_once __DIR__ . '/bootstrap.php';

try {
    echo "Setting up Case Studies database...\n";
    
    // Read the schema file
    $schemaFile = __DIR__ . '/database/case-studies-schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }
    
    $schema = file_get_contents($schemaFile);
    if (!$schema) {
        throw new Exception("Could not read schema file");
    }
    
    // Execute the schema
    $admin_db->exec($schema);
    echo "✅ Case studies table created/updated successfully\n";
    
    // Check if table exists and has data
    $stmt = $admin_db->query("SELECT COUNT(*) as count FROM case_studies");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📊 Current case studies count: " . $result['count'] . "\n";
    
    // Test the API endpoint
    echo "\n🧪 Testing API endpoint...\n";
    
    // Create a simple test case study if none exist
    if ($result['count'] == 0) {
        echo "Creating test case study...\n";
        
        $testData = [
            'title' => 'Test Case Study',
            'slug' => 'test-case-study',
            'client_name' => 'Test Client',
            'industry' => 'Technology',
            'hero_title' => 'Test Hero Title',
            'hero_description' => 'This is a test case study for development purposes.',
            'status' => 'draft',
            'template' => 'luminous-skin-clinic',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $columns = implode(', ', array_keys($testData));
        $placeholders = ':' . implode(', :', array_keys($testData));
        
        $stmt = $admin_db->prepare("INSERT INTO case_studies ($columns) VALUES ($placeholders)");
        $stmt->execute($testData);
        
        echo "✅ Test case study created\n";
    }
    
    echo "\n🎉 Case Studies database setup completed successfully!\n";
    echo "\nYou can now:\n";
    echo "1. Visit http://localhost/adzeta-admin/?view=case-studies\n";
    echo "2. Visit http://localhost/adzeta-admin/?view=add-case-study\n";
    echo "3. Test the API at http://localhost/adzeta-admin/api/case-studies\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
