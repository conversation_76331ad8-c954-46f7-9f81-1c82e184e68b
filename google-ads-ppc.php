<?php include 'header.php'; ?>
	  <link rel="stylesheet" href="css/google-ads-ppc.css?v=1.0" />
<!-- end header -->
<!-- start hero section -->
<section class="cover-background top-space-padding pb-0 overflow-hidden position-relative lg-pb-30px hero-section ecom-ppc">
    <link rel="stylesheet" href="css/ppc-hero-animation.css?v=1.0" />
    <div class="professional-gradient-container">
        <div class="corner-gradient top-left"></div>
        <div class="corner-gradient top-right"></div>
        <div class="corner-gradient bottom-left"></div>
        <div class="corner-gradient bottom-right"></div>
        <div class="diagonal-gradient"></div>
        <div class="mesh-overlay"></div>
        <div class="vignette-overlay"></div>
    </div>
    <div class="container h-100">
        <!-- Removed distracting background elements for a more professional look -->
        <div class="row align-items-center h-100 md-mt-30px md-mb-10px pt-4">
            <div class="col-xl-6 col-lg-6 mb-9 position-relative z-index-1 ps-lg-5" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                <div class="d-flex align-items-center mb-15px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
                    <span class="fs-12 fw-light text-white opacity-90 primary-font ls-wide">
                        <span class="ai-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="pulse">
                                <!-- Modern AI chip/processor shape -->
                                <rect x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5"/>
                                <!-- Circuit lines -->
                                <path class="circuit1" d="M8 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit2" d="M12 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit3" d="M16 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit4" d="M8 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit5" d="M12 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit6" d="M16 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit7" d="M2 8H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit8" d="M2 12H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit9" d="M2 16H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit10" d="M20 8H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit11" d="M20 12H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit12" d="M20 16H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <!-- Inner processor grid -->
                                <path class="grid" d="M8 8H16M8 12H16M8 16H16M8 8V16M12 8V16M16 8V16" stroke="url(#paint0_linear_ai_icon)" stroke-width="0.75" stroke-linecap="round" stroke-dasharray="1 2"/>
                                <!-- Central core -->
                                <rect class="core" x="10" y="10" width="4" height="4" fill="url(#paint0_linear_ai_icon)"/>
                                <!-- Sparkle overlay -->
                                <rect class="sparkle" x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint1_linear_ai_icon)" stroke-width="1" stroke-opacity="0.7"/>
                                <defs>
                                    <linearGradient id="paint0_linear_ai_icon" x1="2" y1="2" x2="22" y2="22" gradientUnits="userSpaceOnUse">
                                        <stop offset="0" stop-color="#e958a1"/>
                                        <stop offset="0.5" stop-color="#8f76f5"/>
                                        <stop offset="1" stop-color="#4a9eff"/>
                                    </linearGradient>
                                    <linearGradient id="paint1_linear_ai_icon" x1="20" y1="4" x2="4" y2="20" gradientUnits="userSpaceOnUse">
                                        <stop offset="0" stop-color="#ffffff"/>
                                        <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </span>
                        <span class="text-gradient-purple-blue ls-3px">GOOGLE ADS x ADZETA AI</span>
                    </span>
                </div>
                <h1 class="alt-font mb-15px fs-50 md-fs-60 sm-fs-60 xs-fs-45 fw-600 lh-2-5 heading-gradient" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 1000, "delay": 300, "easing": "easeOutQuad" }'>
                    <span class="fw-300">Supercharge<br>Google VBB with<br></span>Predictive LTV.
                </h1>
                <div class="alt-font fw-400 fs-16 w-90 sm-w-100 mb-25px xs-mb-20px text-white opacity-75 lh-1-5" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 800, "easing": "easeOutQuint" }'>Go beyond standard tROAS limits. Adzeta's AI predicts future customer value, enabling Google Ads to optimize for true profit and sustainable scaling from day one.</div>
                <div class="d-flex flex-wrap" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 1000, "easing": "easeOutQuint" }'>
                    <a href="free-ad-audit.php" class="btn btn-large btn-hover-animation box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-gradient-pink-orange btn-rounded me-15px mt-15px fw-600 alt-font">
                    <span>
                    <span class="btn-text">Meet a VBB Consultant</span>
                    <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
                    </span>
                    </a>
                    <a href="#" onclick="alert('Download currently unavailable. Please try again later.'); return false;" 
                        class="btn btn-large box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-transparent-white-light btn-rounded border-1 mt-20px fw-600 alt-font">
                    <span>
                    <span class="btn-text">Free VBB Guide</span>
                    <span class="btn-icon"><i class="fa-solid fa-download"></i></span>
                    </span>
                    </a>
                </div>
            </div>
            <div class="col-xl-6 col-lg-6 align-self-center">
                <div class="platform-animation-container pt-0" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 500 }'>
                    <!-- New Hero Animation Structure with Semicircle Layout -->
                    <div class="hero-animation-container">
                        <!-- Center Element - Adzeta AI Core -->
                        <div class="animation-element adzeta-ai-center">
                            <div class="position-relative">
                                <img src="images/adzeta-ai-center.png" alt="Adzeta AI Core" class="w-100">
                                <div class="element-label label-center">ADZETA AI</div>
                            </div>
                        </div>
                        <!-- Left Side Elements - Data Input (Semicircle) -->
                        <div class="animation-element adzeta-ai-left-1">
                            <img src="images/data-source-1.png" alt="Customer Data" class="w-100">
                            <div class="element-label label-left-1">PRODUCT FEED</div>
                        </div>
                        <div class="animation-element adzeta-ai-left-2">
                            <img src="images/data-source-2.png" alt="Website Data" class="w-100">
                            <div class="element-label label-left-2">WEBSITE SIGNALS</div>
                        </div>
                        <div class="animation-element adzeta-ai-left-3">
                            <img src="images/data-source-3.png" alt="CRM Data" class="w-100">
                            <div class="element-label label-left-3">CONVERSION DATA</div>
                        </div>
                        <!-- Right Side Elements - Ad Platforms (Semicircle) -->
                        <div class="animation-element adzeta-ai-right-1">
                            <img src="images/platform-1.png" alt="Predictive LTV Generated" class="w-100">
                            <div class="element-label label-right-1">Predictive LTV</div>
                        </div>
                        <div class="animation-element adzeta-ai-right-2">
                            <img src="images/platform-2.png" alt="VBB Enhanced" class="w-100">
                            <div class="element-label label-right-2">VBB Enhanced</div>
                        </div>
                        <div class="animation-element adzeta-ai-right-3">
                            <img src="images/platform-3.png" alt="Optimized Profit / ROAS" class="w-100">
                            <div class="element-label label-right-3">Optimized ROAS</div>
                        </div>
                        <!-- Connection Lines - Left Side -->
                        <div class="connection-line connection-left-1"></div>
                        <div class="connection-line connection-left-2"></div>
                        <div class="connection-line connection-left-3"></div>
                        <!-- Connection Lines - Right Side -->
                        <div class="connection-line connection-right-1"></div>
                        <div class="connection-line connection-right-2"></div>
                        <div class="connection-line connection-right-3"></div>
                        <!-- Data Flow Particles - Left Side -->
                        <div class="data-particle particle-left-1"></div>
                        <div class="data-particle particle-left-1-delay"></div>
                        <div class="data-particle particle-left-2"></div>
                        <div class="data-particle particle-left-2-delay"></div>
                        <div class="data-particle particle-left-3"></div>
                        <div class="data-particle particle-left-3-delay"></div>
                        <!-- Data Flow Particles - Right Side -->
                        <div class="data-particle particle-right-1"></div>
                        <div class="data-particle particle-right-1-delay"></div>
                        <div class="data-particle particle-right-2"></div>
                        <div class="data-particle particle-right-2-delay"></div>
                        <div class="data-particle particle-right-3"></div>
                        <div class="data-particle particle-right-3-delay"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end hero section -->
<!-- start section -->
<!-- start section: Value-Based Bidding -->
<section class="vbb-section position-relative overflow-hidden">
    <!-- Light section background with subtle gradient that fades to white at bottom -->
    <div class="position-absolute" style="
        inset: 0;
        background: linear-gradient(to bottom,
        rgba(248, 249, 250, 1) 0%,
        rgba(242, 240, 238, 0.8) 40%,
        rgba(255, 255, 255, 1) 100%);
        z-index: 0;
        pointer-events: none;
        "></div>
    <!-- Subtle mesh texture -->
    <div class="position-absolute" style="
        inset: 0;
        background:
        linear-gradient(90deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px),
        linear-gradient(0deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px);
        background-size: 28px 28px;
        opacity: 0.5;
        z-index: 0;
        pointer-events: none;
        "></div>
    <div class="container position-relative" style="z-index: 1;">
        <div class="row justify-content-center mb-50px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="scale-tag-container">
                    <div class="scale-tag">THE FOUNDATION</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Why Value-Based-Bidding?</span></h3>
            </div>
        </div>
    </div>
    <div class="container position-relative" style="z-index: 1;">
        <div class="vbb-content-container box-shadow-extra-large box-shadow-extra-large-hover">
            <div class="row align-items-center">
                <!-- Left Column: Text Content -->
                <div class="col-lg-5 mb-5 mb-lg-0" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
                    <div class="vbb-content">
                        <p class="md-mt-30">Not all conversions hold the same long-term value. Optimizing Google Ads solely for initial CPA or ROAS often treats a $50 LTV customer the same as a $500 one. This common approach means wasted ad spend and limits your ability to scale e-commerce profit effectively.</p>
                        <p><span class="fw-600">Value-Based Bidding (VBB)</span> aims higher – maximizing total profit by bidding smarter based on customer worth. Adzeta's Predictive AI provides the crucial missing piece: accurately forecasting LTV from day one, so you can optimize bids intelligently and invest confidently in your most valuable future customers.</p>
                    </div>
                </div>
                <!-- Right Column: Visual Comparison (Apple-style) -->
                <div class="col-lg-7" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 500, "easing": "easeOutQuad" }'>
                    <div class="vbb-visual">
                        <!-- Google logo -->
                        <div class="bidding-strategies">
                            <!-- Customer profiles row -->
                            <div class="strategy-row">
                                <div class="strategy-label"></div>
                                <div class="customers-row">
                                    <div class="customer-column">
                                        <div class="customer-info">
                                            <div class="customer-photo">
                                                <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Customer Photo">
                                            </div>
                                            <div class="customer-title">Customer 1</div>
                                            <div class="customer-value">LTV $100</div>
                                        </div>
                                    </div>
                                    <div class="customer-column">
                                        <div class="customer-info">
                                            <div class="customer-photo">
                                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Customer Photo">
                                            </div>
                                            <div class="customer-title">Customer 2</div>
                                            <div class="customer-value">LTV $300</div>
                                        </div>
                                    </div>
                                    <div class="customer-column customer-column-highlight">
                                        <div class="customer-info highlight">
                                            <div class="customer-photo">
                                                <img src="https://randomuser.me/api/portraits/men/65.jpg" alt="Customer Photo">
                                            </div>
                                            <div class="customer-title">Customer 3</div>
                                            <div class="customer-value">LTV $500</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- tCPA bidding row -->
                            <div class="strategy-row">
                                <div class="strategy-label tcpa">tCPA</div>
                                <div class="bid-row">
                                    <div class="bid-box">
                                        <span class="bid-label">Bid:</span>
                                        <span class="bid-amount">$10</span>
                                    </div>
                                    <div class="bid-box">
                                        <span class="bid-label">Bid:</span>
                                        <span class="bid-amount">$10</span>
                                    </div>
                                    <div class="bid-box highlight">
                                        <span class="bid-label">Bid:</span>
                                        <span class="bid-amount">$10</span>
                                    </div>
                                </div>
                            </div>
                            <!-- Value-based bidding row -->
                            <div class="strategy-row">
                                <div class="strategy-label vbb">Value-based bidding or tROAS</div>
                                <div class="bid-row">
                                    <div class="bid-box lower">
                                        <span class="bid-label">Bid:</span>
                                        <span class="bid-amount">$5</span>
                                    </div>
                                    <div class="bid-box same">
                                        <span class="bid-label">Bid:</span>
                                        <span class="bid-amount">$10</span>
                                    </div>
                                    <div class="bid-box higher highlight">
                                        <span class="bid-label">Bid:</span>
                                        <span class="bid-amount">$15</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end section: Value-Based Bidding -->
<!-- start section: Overcoming Google's Value Signal Limitations -->
<section class="google-value-challenges overflow-hidden position-relative">
    <!-- Light section background with subtle gradient that matches THE FOUNDATION section -->
    <div class="position-absolute" style="
        inset: 0;
        background: linear-gradient(to bottom,
        rgba(255, 255, 255, 1) 0%,
        rgba(248, 249, 250, 0.8) 20%,
        rgba(242, 240, 238, 0.8) 50%,
        rgba(249, 249, 255, 0.8) 80%,
        rgba(255, 255, 255, 1) 100%);
        z-index: 0;
        pointer-events: none;
        "></div>
    <!-- Subtle mesh texture -->
    <div class="position-absolute" style="
        inset: 0;
        background:
        linear-gradient(90deg, rgba(143, 118, 245, 0.02) 1px, transparent 1px),
        linear-gradient(0deg, rgba(143, 118, 245, 0.02) 1px, transparent 1px);
        background-size: 28px 28px;
        opacity: 0.5;
        z-index: 0;
        pointer-events: none;
        "></div>
    <div class="container position-relative" style="z-index: 1;">
        <div class="row justify-content-center mb-50px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="scale-tag-container">
                    <div class="scale-tag">OVERCOMING LIMITATIONS</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Why you might be failing</span></h3>
                <p class="mb-0">Standard Google Value-Based Bidding faces key challenges that limit your growth potential. Here's how Adzeta's approach solves these fundamental issues.</p>
            </div>
        </div>
        <!-- Single unified card with tabs -->
        <div class="unified-value-card mb-50px box-shadow-extra-large">
            <!-- Tab content -->
            <!-- Tab 1: Google's Short Window -->
            <div class="row">
                <div class="col-12 mb-4">
                    <h4 class="alt-font fw-600 fs-20 mb-2">Google's Short Window Misses Late Value</h4>
                    <p class="text-gray mb-4">Google's AI primarily uses data from the first 7 days, missing the true long-term value that develops over months.</p>
                    <!-- Customer comparison table -->
                    <div class="customer-comparison mb-4">
                        <table class="w-100" style="background-color: #f9f9fb; border-collapse: separate; border-spacing: 0; border-radius: 8px; overflow: hidden;">
                            <tr>
                                <td style="width: 33%; padding: 12px; vertical-align: middle; text-align: center; border-right: 1px solid rgba(0,0,0,0.03);">
                                    <div style="margin-bottom: 8px;">
                                        <div style="width: 24px; height: 2px; border-top: 2px dashed rgba(0,0,0,0.3); display: inline-block; margin-right: 6px; vertical-align: middle;"></div>
                                        <span style="font-size: 11px; color: #555; vertical-align: middle;">7-day window</span>
                                    </div>
                                    <div>
                                        <div style="width: 24px; height: 2px; background-color: rgba(0,0,0,0.3); display: inline-block; margin-right: 6px; vertical-align: middle;"></div>
                                        <span style="font-size: 11px; color: #555; vertical-align: middle;">180+ day value</span>
                                    </div>
                                </td>
                                <td style="width: 33%; padding: 12px; text-align: center;">
                                    <div style="font-weight: 600; font-size: 12px; margin-bottom: 3px;">Customer A: High LTV, Slow Start</div>
                                    <div style="font-weight: 700; color: #ff8cc6; margin-bottom: 6px; font-size: 13px;">$200 LTV</div>
                                    <div style="margin-bottom: 4px;">
                                        <div style="width: 16px; height: 2px; border-top: 2px dashed #4285F4; display: inline-block; margin-right: 4px; vertical-align: middle;"></div>
                                        <span style="font-size: 11px; color: #777;">Google sees: $15</span>
                                    </div>
                                    <div>
                                        <div style="width: 16px; height: 2px; background-color: #ff8cc6; display: inline-block; margin-right: 4px; vertical-align: middle;"></div>
                                        <span style="font-size: 11px; color: #777;">True value: $200</span>
                                    </div>
                                </td>
                                <td style="width: 33%; padding: 12px; text-align: center;">
                                    <div style="font-weight: 600; font-size: 12px; margin-bottom: 3px;">Customer B: Low LTV, Fast Start</div>
                                    <div style="font-weight: 700; color: #ADD8E6; margin-bottom: 6px; font-size: 13px;">$35 LTV</div>
                                    <div style="margin-bottom: 4px;">
                                        <div style="width: 16px; height: 2px; border-top: 2px dashed #4285F4; display: inline-block; margin-right: 4px; vertical-align: middle;"></div>
                                        <span style="font-size: 11px; color: #777;">Google sees: $18</span>
                                    </div>
                                    <div>
                                        <div style="width: 16px; height: 2px; background-color: #ADD8E6; display: inline-block; margin-right: 4px; vertical-align: middle;"></div>
                                        <span style="font-size: 11px; color: #777;">True value: $35</span>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <!-- Timeline Graph -->
                    <div class="timeline-graph" style="height: 380px; background-color: white;" data-anime='{ "opacity": [0,1], "duration": 800, "easing": "easeOutQuad" }'>
                        <!-- Y-axis with clear label - Improved spacing to prevent cutting from left side -->
                        <div class="y-axis-timeline" style="position: absolute; left: 5px; top: 0; height: 100%; display: flex; flex-direction: column; justify-content: space-between; padding: 40px 0; z-index: 5;">
                            <div class="y-axis-label-timeline" style="color: #888; font-size: 12px;">$200</div>
                            <div class="y-axis-label-timeline" style="color: #888; font-size: 12px;">$150</div>
                            <div class="y-axis-label-timeline" style="color: #888; font-size: 12px;">$100</div>
                            <div class="y-axis-label-timeline" style="color: #888; font-size: 12px;">$50</div>
                            <div class="y-axis-label-timeline" style="color: #888; font-size: 12px;">$0</div>
                        </div>
                        <!-- Y-axis title - Positioned as in ASCII diagram -->
                        <div style="position: absolute; left: -20px; top: 50%; transform: rotate(-90deg) translateY(-50%); transform-origin: center; color: #666; font-size: 12px; font-weight: 500; white-space: nowrap; z-index: 5;">
                            Customer Lifetime Value ($)
                        </div>
                        <!-- Google's limited window with better styling - full height blue area -->
                        <div class="google-window" style="background-color: rgba(66, 133, 244, 0.05); height: 100%; width: 20%; left: 0; position: absolute; z-index: 1;">
                        </div>
                        <!-- Timeline axis markers - Styled exactly as in screenshot -->
                        <div class="timeline-axis" style="position: absolute; bottom: 10px; width: 100%; display: flex; justify-content: space-between; padding: 0 5%; z-index: 10;">
                            <div class="timeline-marker" style="position: relative; color: #666; font-size: 12px;">Day 1</div>
                            <div class="timeline-marker" style="position: relative; color: #666; font-size: 12px;">Day 7</div>
                            <div class="timeline-marker" style="position: relative; color: #666; font-size: 12px;">Day 30</div>
                            <div class="timeline-marker" style="position: relative; color: #666; font-size: 12px;">Day 90</div>
                            <div class="timeline-marker" style="position: relative; color: #666; font-size: 12px;">Day 180+</div>
                        </div>
                        <!-- Simplified SVG for just two contrasting customer types with improved curves -->
                        <svg class="value-curve" viewBox="0 0 1000 280" preserveAspectRatio="none">
                            <!-- Horizontal grid lines aligned with dollar values -->
                            <line x1="0" y1="40" x2="1000" y2="40" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- $200 -->
                            <line x1="0" y1="100" x2="1000" y2="100" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- $150 -->
                            <line x1="0" y1="160" x2="1000" y2="160" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- $100 -->
                            <line x1="0" y1="220" x2="1000" y2="220" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- $50 -->
                            <line x1="0" y1="280" x2="1000" y2="280" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- $0 -->
                            <!-- Day markers - single set of vertical lines -->
                            <line x1="0" y1="0" x2="0" y2="280" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- Day 1 -->
                            <line x1="200" y1="0" x2="200" y2="280" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- Day 7 -->
                            <line x1="400" y1="0" x2="400" y2="280" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- Day 30 -->
                            <line x1="650" y1="0" x2="650" y2="280" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- Day 90 -->
                            <line x1="900" y1="0" x2="900" y2="280" stroke="rgba(0,0,0,0.05)" stroke-width="1" />
                            <!-- Day 180+ -->
                            <!-- Customer A (High LTV, Slow Start) - Matching the screenshot exactly -->
                            <path d="M0,240 C50,230 100,220 200,200 C300,180 400,160 500,140 C600,120 700,80 800,40 C900,20 950,10 1000,5" stroke="#ff8cc6" stroke-width="2" fill="none" />
                            <!-- Customer B (Low LTV, Fast Start) - Matching the screenshot exactly -->
                            <path d="M0,240 C50,220 100,200 200,180 C300,170 400,160 500,150 C600,145 700,140 800,135 C900,130 950,125 1000,120" stroke="#ADD8E6" stroke-width="2" fill="none" />
                        </svg>
                        <!-- Customer A - High LTV, Slow Start - Styled exactly as in screenshot -->
                        <div class="customer-profile-a">
                            <!-- Star highlight -->
                            <div class="star-highlight" style="top: -6px; right: -6px; width: 18px; height: 18px; background-color: #ff8cc6; border-radius: 50%; display: flex; align-items: center; justify-content: center; position: absolute; z-index: 6;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="white" viewBox="0 0 16 16">
                                    <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                                </svg>
                            </div>
                            <div class="profile-image" style="width: 32px; height: 32px; border-radius: 50%; overflow: hidden; margin-right: 8px; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Customer A" style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                            <div class="profile-content" style="display: flex; flex-direction: column;">
                                <div class="profile-name" style="font-weight: 600; font-size: 12px; color: #333; line-height: 1.2;">Customer A</div>
                                <div class="profile-type" style="color: #ff8cc6; font-size: 10px; line-height: 1.2; margin-bottom: 2px;">High LTV, Slow Start</div>
                                <div class="dollar-indicator" style="color: #ff8cc6; font-size: 12px; font-weight: 600;">
                                    $200 LTV
                                </div>
                            </div>
                        </div>
                        <!-- Customer B - Low LTV, Fast Start - Styled exactly as in screenshot -->
                        <div class="customer-profile-b">
                            <div class="profile-image" style="width: 32px; height: 32px; border-radius: 50%; overflow: hidden; margin-right: 8px; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Customer B" style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                            <div class="profile-content" style="display: flex; flex-direction: column;">
                                <div class="profile-name" style="font-weight: 600; font-size: 12px; color: #333; line-height: 1.2;">Customer B</div>
                                <div class="profile-type" style="color: #ADD8E6; font-size: 10px; line-height: 1.2; margin-bottom: 2px;">Low LTV, Fast Start</div>
                                <div class="dollar-indicator" style="color: #ADD8E6; font-size: 12px; font-weight: 600;">
                                    $120 LTV
                                </div>
                            </div>
                        </div>
                        <!-- Removed dollar sign indicators to reduce clutter -->
                        <!-- Removed Adzeta AI icon to reduce clutter -->
                        <!-- Removed target icon and label as requested -->
                        <!-- Key annotations only - simplified and more meaningful -->
                        <!-- Google's limited window annotation - positioned as in ASCII diagram -->
                        <div class="annotation-window" >
                            <div style="background-color: rgba(66, 133, 244, 0.1); color: #4285F4; border: 1px solid rgba(66, 133, 244, 0.3); font-weight: 500; padding: 8px 12px; border-radius: 12px; font-size: 12px; white-space: nowrap;">
                                7-Day Window
                            </div>
                        </div>
                        <!-- Early Signals Mislead annotation - positioned as in ASCII diagram -->
                        <div class="annotation-blue" >
                            <div style="background-color: rgba(66, 133, 244, 0.1); color: #4285F4; border: 1px solid rgba(66, 133, 244, 0.3); font-weight: 500; padding: 8px 12px; border-radius: 12px; font-size: 12px; white-space: nowrap;">
                                Early Signals Mislead
                            </div>
                        </div>
                        <!-- Adzeta's prediction annotation - positioned as in ASCII diagram -->
                        <div class="annotation-pink">
                            <div style="background-color: rgba(255, 140, 198, 0.1); color: #ff8cc6; border: 1px solid rgba(255, 140, 198, 0.3); font-weight: 500; padding: 8px 12px; border-radius: 12px; font-size: 12px; white-space: nowrap; position: relative;">
                                Adzeta Predicts True LTV
                            </div>
                        </div>
                        <!-- Arrow element - positioned as in ASCII diagram -->
                        <div style="position: absolute; top: 25%; left: 40%; z-index: 15; width: 400px; height: 150px;">
                            <svg width="100%" height="100%" viewBox="0 0 400 150" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0,30 Q100,20 200,10 Q300,0 380,10" stroke="#ff8cc6" stroke-width="1.5" stroke-dasharray="3,3" fill="none"/>
                                <polygon points="380,10 370,15 375,5" fill="#ff8cc6"/>
                            </svg>
                        </div>
                        <!-- Removed "Early Signals Mislead" annotation to reduce clutter -->
                    </div>
                </div>
            </div>
        </div>
        <!-- Two content blocks explaining challenges with Standard Google Value-Based Bidding -->
        <div class="row justify-content-center mt-80px mb-50px">
            <div class="col-lg-6 col-md-6 mb-30px">
                <div class="challenge-card h-100" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 600, "delay": 200 }'>
                    <div class="challenge-icon">
                        <i class="line-icon-Magnifi-Glass2 icon-medium"></i>
                    </div>
                    <h4 class="challenge-title">The Visibility Gap</h4>
                    <p class="challenge-description">Google's Value-Based Bidding relies on limited data from the first 7 days after conversion, creating a critical visibility gap. This short window misses 60-85% of a customer's true lifetime value, which typically develops over months. Without accurate LTV prediction, your campaigns systematically undervalue high-potential customers and overspend on low-value ones.</p>
                    <div class="challenge-highlight">
                        <div class="highlight-icon">
                            <i class="feather icon-feather-alert-circle"></i>
                        </div>
                        <p>Google's algorithms can't see beyond their limited data window, causing them to misallocate your budget based on incomplete customer value information.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-6 mb-30px">
                <div class="challenge-card h-100" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 600, "delay": 300 }'>
                    <div class="challenge-icon">
                        <i class="line-icon-Gear-2 icon-medium"></i>
                    </div>
                    <h4 class="challenge-title">The Optimization Paradox</h4>
                    <p class="challenge-description">Standard Value-Based Bidding creates a fundamental paradox: it optimizes for early purchase behavior, which often inversely correlates with long-term value. Fast-converting customers frequently have lower lifetime value, while your most valuable customers often start with smaller initial purchases. This misalignment leads to systematically flawed bidding decisions.</p>
                    <div class="challenge-highlight">
                        <div class="highlight-icon">
                            <i class="feather icon-feather-alert-circle"></i>
                        </div>
                        <p>Without predictive LTV data, Google's algorithms optimize for the wrong signals, prioritizing quick conversions over true customer value and long-term profitability.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end section: Overcoming Google's Value Signal Limitations -->
<!-- start section: Data to Decisions -->
<section class="data-decisions-section position-relative overflow-hidden py-150px">
    <!-- Modern dark base gradient -->
    <div class="position-absolute" style="
        inset: 0;
        background: linear-gradient(135deg, #0A0913 0%, #1A142A 40%, #271E3D 75%, #341F53 100%);
        z-index: 0;
        pointer-events: none;
        "></div>
    <!-- Top-right soft purple glow -->
    <div class="position-absolute" style="
        inset: 0;
        background: radial-gradient(circle at 85% 20%, rgba(190, 130, 255, 0.18) 0%, transparent 60%);
        z-index: 1;
        pointer-events: none;
        "></div>
    <!-- Bottom-left pink accent glow -->
    <div class="position-absolute" style="
        inset: 0;
        background: radial-gradient(circle at 15% 85%, rgba(255, 105, 180, 0.15) 0%, transparent 60%);
        z-index: 1;
        pointer-events: none;
        "></div>
    <!-- Subtle mesh texture -->
    <div class="position-absolute" style="
        inset: 0;
        background:
        linear-gradient(90deg, rgba(255, 160, 255, 0.03) 1px, transparent 1px),
        linear-gradient(0deg, rgba(255, 160, 255, 0.03) 1px, transparent 1px);
        background-size: 28px 28px;
        opacity: 0.25;
        z-index: 1;
        pointer-events: none;
        "></div>
    <div class="container position-relative" style="z-index: 2;">
        <div class="row justify-content-center mb-80px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="mb-10px">
                    <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">ADZETA'S SOLUTION</span>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px text-white">Why you need Adzeta's Predictive LTV for Google Ads</h3>
                <p class="mb-10 text-white-transparent md-mb-30">Adzeta bridges the critical information gap in Google Ads. Our Predictive AI engine transforms your rich first-party e-commerce data into highly accurate LTV forecasts from a customer's very first interaction. We don't just predict value; we translate that foresight into the precise, actionable signals needed to make Google's Value-Based Bidding truly effective for your profit goals.</p>
            </div>
        </div>
        <div class="data-decisions-items-wrapper">
            <!-- Row 1: AI Predictions For Google -->
            <div class="row justify-content-start mb-4 md-mt-30">
                <div class="col-lg-7 col-md-9 col-sm-11">
                    <a href="#" class="data-decisions-item">
                        <div class="data-decisions-item-header">
                            <div class="data-decisions-item-icon">
                                <i class="line-icon-Brain icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                            </div>
                            <h3 class="data-decisions-item-h3">Day-One LTV Prediction</h3>
                        </div>
                        <p class="data-decisions-item-p">Our AI analyzes hundreds of signals (purchase history, site behavior, product affinity) to generate precise LTV scores before Google's typical learning phase, overcoming the "cold start" challenge. Adzeta's AI engine creates custom prediction models that understand the specific drivers of value in your business from day one.</p>
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                            <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                </div>
            </div>
            <!-- Row 2: Prescriptive AI Automation -->
            <div class="row justify-content-center mb-4">
                <div class="col-lg-7 col-md-9 col-sm-11">
                    <div class="animated-card">
                        <a href="#" class="data-decisions-item is-dark" style="background: linear-gradient(135deg, #1E1A33 0%, #2D1E4A 100%); border: none;">
                            <div class="data-decisions-item-header">
                                <div class="data-decisions-item-icon">
                                    <i class="line-icon-Data-Transfer icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                                </div>
                                <h3 class="data-decisions-item-h3 is-white">Smart Signal Delivery to Google</h3>
                            </div>
                            <p class="data-decisions-item-p is-white">We securely feed predictive LTV data into your Google Ads account via approved methods (like Offline Conversion Import with Value), directly enhancing tROAS and Maximize Conversion Value campaigns. Our AI intelligently balances the timing and value of signals to meet Google's training requirements, delivering early signals to jumpstart campaign optimization.</p>
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                                <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <!-- Row 3: 24-7 Always On Optimization -->
            <div class="row justify-content-end mb-4">
                <div class="col-lg-7 col-md-9 col-sm-11">
                    <a href="#" class="data-decisions-item">
                        <div class="data-decisions-item-header">
                            <div class="data-decisions-item-icon">
                                <i class="line-icon-Shopping-Cart icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                            </div>
                            <h3 class="data-decisions-item-h3">Profit-Driven PMax & Shopping</h3>
                        </div>
                        <p class="data-decisions-item-p">Apply LTV insights to effectively optimize Performance Max and Shopping campaigns, ensuring budget is allocated to acquire customers with the highest overall profit margin, not just revenue. Our models are continuously tested and verified by Adzeta's AI engine to maintain the highest prediction accuracy and adapt to algorithm changes.</p>
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                            <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Background elements -->
    <img src="images/ltv-signal.png" loading="lazy" width="300" alt="Google" class="data-decisions-img-bg is-google">
    <img src="images/adzeta-bg-circle.svg" loading="lazy" width="538" height="538" alt="AdZeta" class="data-decisions-img-bg is-adzeta">
    <!-- Small decorative dots -->
    <div class="data-decisions-dots"></div>
    <!-- Add CSS for this section -->
</section>
<!-- end section: Data to Decisions -->
<!-- SECTION : Proven Results on Google Ads - Start -->
<section class="proven-results-section overflow-hidden position-relative pb-0">
    <!-- Light section background with subtle gradient that matches previous sections -->
    <div class="position-absolute" style="
        inset: 0;
        background: linear-gradient(to bottom,
        rgba(255, 255, 255, 1) 0%,
        rgba(249, 249, 255, 0.8) 20%,
        rgba(248, 249, 250, 0.8) 50%,
        rgba(242, 240, 238, 0.8) 80%,
        rgba(255, 255, 255, 1) 100%);
        z-index: 0;
        pointer-events: none;
        "></div>
    <!-- Subtle mesh texture -->
    <div class="position-absolute" style="
        inset: 0;
        background:
        linear-gradient(90deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px),
        linear-gradient(0deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px);
        background-size: 28px 28px;
        opacity: 0.5;
        z-index: 0;
        pointer-events: none;
        "></div>
    <div class="container position-relative" style="z-index: 1;">
        <div class="row justify-content-center mb-60px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="scale-tag-container">
                    <div class="scale-tag">PROVEN RESULTS</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">The Adzeta Impact: Real Growth on Google Ads</span></h3>
                <p class="mb-0">See the measurable improvements e-commerce brands typically achieve when Adzeta's Predictive LTV supercharges their Google Ads Value-Based Bidding.</p>
            </div>
        </div>
        <!-- Key Google Ads Performance Metrics with Adzeta -->
        <div class="row justify-content-center mb-0" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay":0, "staggervalue": 300, "easing": "easeOutQuad" }'>
            <!-- Metric Card 1: ROAS Lift -->
            <div class="col-lg-3 col-md-6 col-sm-6 mb-30px">
                <div class="compact-metric-card h-100 text-center">
                    <div class="metric-icon">
                        <i class="line-icon-Arrow-Up icon-medium" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                    </div>
                    <h2 class="counter d-inline-flex fw-700 ls-minus-2px mb-10px" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" data-speed="2000" data-to="4">4</h2>
                    <span class="counter-suffix">x+</span>
                    <h4 class="metric-title">LTV-Focused ROAS Lift</h4>
                    <p class="metric-description">Average increase in Return On Ad Spend when optimizing for predicted lifetime value.</p>
                </div>
            </div>
            <!-- Metric Card 2: CAC Reduction -->
            <div class="col-lg-3 col-md-6 col-sm-6 mb-30px">
                <div class="compact-metric-card h-100 text-center">
                    <div class="metric-icon">
                        <i class="line-icon-Arrow-Down icon-medium" style="background: linear-gradient(135deg, #8f76f5 0%, #4a9eff 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                    </div>
                    <h2 class="counter d-inline-flex fw-700 ls-minus-2px mb-10px" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" data-speed="2000" data-to="36">36</h2>
                    <span class="counter-suffix">%</span>
                    <h4 class="metric-title">Average CAC Reduction</h4>
                    <p class="metric-description">Significant decrease in Customer Acquisition Cost by focusing bids on high-value customers.</p>
                </div>
            </div>
            <!-- Metric Card 3: PMax/Shopping Profitability -->
            <div class="col-lg-3 col-md-6 col-sm-6 mb-30px">
                <div class="compact-metric-card h-100 text-center">
                    <div class="metric-icon">
                        <i class="line-icon-Shopping-Cart icon-medium" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                    </div>
                    <h2 class="counter d-inline-flex fw-700 ls-minus-2px mb-10px" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" data-speed="2000" data-to="30">30</h2>
                    <span class="counter-suffix">%</span>
                    <h4 class="metric-title">PMax/Shopping Profit Boost</h4>
                    <p class="metric-description">Increased profit margins from Performance Max and Shopping campaigns with LTV insights.</p>
                </div>
            </div>
            <!-- Metric Card 4: Time to Value -->
            <div class="col-lg-3 col-md-6 col-sm-6 mb-30px">
                <div class="compact-metric-card h-100 text-center">
                    <div class="metric-icon">
                        <i class="line-icon-Clock icon-medium" style="background: linear-gradient(135deg, #8f76f5 0%, #4a9eff 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                    </div>
                    <h2 class="counter d-inline-flex fw-700 ls-minus-2px mb-10px" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" data-speed="2000" data-to="50">50</h2>
                    <span class="counter-suffix">%</span>
                    <h4 class="metric-title">VBB Ramp-Up to Target ROAS</h4>
                    <p class="metric-description">Reach your Google Ads Value-Based Bidding performance goals significantly quicker.</p>
                </div>
            </div>
        </div>
        <!-- Trust Indicators Section -->
        <!-- Trust Indicators Row -->
        <div class="row justify-content-center mb-5">
            <div class="col-12">
                <p class="results-disclaimer text-center mt-3">
                    Results based on average performance across Adzeta clients using Google Ads Value-Based Bidding with LTV predictions. Updated July 2023.
                </p>
            </div>
        </div>
    </div>
</section>
<section class="proven-results-section overflow-hidden position-relative py-100px">
    <div class="container">
        <div class="row justify-content-center mb-60px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="scale-tag-container">
                    <div class="scale-tag">CLIENT SUCCESS</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">How to make it work</span></h3>
                <p class="mb-0">See how leading brands are leveraging Adzeta's Predictive LTV to transform their Google Ads performance..</p>
            </div>
        </div>
        <!-- Client Success Snapshots -->
        <div class="row justify-content-center">
            <!-- Value-Based Bidding Guide Card -->
            <div class="col-lg-6 col-md-6 mb-30px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 600 }'>
                <div class="case-study-card h-100 box-shadow-medium-hover" style="background: linear-gradient(135deg, #f8f9ff, #fff5f8); border: 1px solid #e3e8ff;">
                    <div class="case-study-header">
                        <div class="industry-tag" style="background: linear-gradient(135deg, #de347f, #ff5d74); color: white;">Ultimate Guide • VBB Strategy</div>
                        <h4 class="case-study-title" style="text-align: left; margin-top: 25px; margin-bottom: 0; padding-top: 10px;">The Ultimate Guide to Value-Based Bidding: Maximize Your ROAS</h4>
                    </div>
                    <div class="case-study-content">
                        <div class="result-highlight">
                            <span class="highlight-number" style="background: linear-gradient(135deg, #de347f, #ff5d74); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">2025</span>
                            <span class="highlight-text">Complete Guide</span>
                        </div>
                        <p class="case-study-text">Master Google's VBB strategies with our comprehensive guide. Learn the 4-step implementation plan, advanced strategies, and how to shift from cost-focused to profit-focused advertising in 2025.</p>
                        <div class="case-study-attribution">
                            <div class="attribution-photo">
                                <img src="images/case-studies/Natalie-Brooks-adzeta.jpg" alt="Natalie Brooks">
                            </div>
                            <div class="attribution-info">
                                <span class="attribution-name">Natalie Brooks</span>
                                <span class="attribution-title">Growth Marketing Lead</span>
                            </div>
                        </div>
                    </div>
                    <a href="/value-based-bidding-ultimate-guide-2025.php" class="case-study-link">
                    <span>Read Complete Guide</span>
                    <span style="margin-left: 8px;">
                        <svg width="12" height="12" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.36406 10.3334L0.664062 9.63338L8.96406 1.33337H4.06406V0.333374H10.6641V6.93337H9.66406V2.03337L1.36406 10.3334Z" fill="currentColor"></path>
                        </svg>
                    </span>
                    </a>
                </div>
            </div>
            <!-- Coffee Brand Case Study Card -->
            <div class="col-lg-6 col-md-6 mb-30px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 700 }'>
                <div class="case-study-card h-100 box-shadow-medium-hover">
                    <div class="case-study-header">
                        <div class="industry-tag">E-commerce • Coffee</div>
                        <h4 class="case-study-title" style="text-align: left; margin-top: 25px; margin-bottom: 0; padding-top: 10px;">Coffee Brand Scales Google Ads 3.5x with Predictive Value Bidding</h4>
                    </div>
                    <div class="case-study-content">
                        <div class="result-highlight">
                            <span class="highlight-number">3.5x</span>
                            <span class="highlight-text">VIP Customer Growth</span>
                        </div>
                        <p class="case-study-text">"With AdZeta's AI platform, we've been able to scale our ad spend by 3.5x while significantly increasing the acquisition of our most valuable VIP subscribers. The predictive LTV insights gave us the confidence to expand into new channels we previously thought were too risky for our premium coffee brand."</p>
                        <div class="case-study-attribution">
                            <div class="attribution-photo">
                                <img src="images/case-studies/Sarah-Johnson.jpg" alt="Sarah Johnson">
                            </div>
                            <div class="attribution-info">
                                <span class="attribution-name">Sarah Johnson</span>
                                <span class="attribution-title">Co-Founder @ Oakbrew Coffee</span>
                            </div>
                        </div>
                    </div>
                    <a href="/oakbrew-coffee-gains-vip-customers-with-ltv-predictions.php" class="case-study-link">
                    <span>Read Full Case Study</span>
                    <span style="margin-left: 8px;">
                        <svg width="12" height="12" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.36406 10.3334L0.664062 9.63338L8.96406 1.33337H4.06406V0.333374H10.6641V6.93337H9.66406V2.03337L1.36406 10.3334Z" fill="currentColor"></path>
                        </svg>
                    </span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- SECTION : Proven Results on Google Ads - End -->
<section class="ad-networks-section  position-relative overflow-hidden  pb-0">
    <div class="light-gradient-container position-absolute w-100 h-100"
        style="background: linear-gradient(to bottom, #eae8e6 0%, #ffffff 100%); z-index: 0;"></div>
    <div class="light-accent-gradient position-absolute w-100 h-100"
        style="background:
        radial-gradient(circle at center, rgba(233,88,161,0.05) 0%, rgba(255,255,255,0) 70%),
        radial-gradient(circle at bottom right, rgba(143,118,245,0.03) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;"></div>
    <!--  <div id="particles-style-06" class="h-100 position-absolute left-0px top-0 w-100" style="z-index: 1;" data-particle="true" data-particle-options='{"particles": {"number": {"value": 10,"density": {"enable": true,"value_area": 800}},"color": {"value": ["#f7afbd", "#e958a1", "#c5d8f8", "#8f76f5"]},"shape": {"type": "triangle","stroke":{"width":0,"color":"#000000"}},"opacity": {"value": 0.3,"random": false,"anim": {"enable": false,"speed": 1,"sync": false}},"size": {"value": 20,"random": true,"anim": {"enable": false,"sync": true}},"line_linked":{"enable":false,"distance":0,"color":"#ffffff","opacity":0.4,"width":1},"move": {"enable": true,"speed":1,"direction": "top","random": false,"straight": false}},"interactivity": {"detect_on": "canvas","events": {"onhover": {"enable": false,"mode": "repulse"},"onclick": {"enable": false,"mode": "push"},"resize": true}},"retina_detect": false}'></div>-->
    <div class="container position-relative" style="z-index: 1;">
        <div class="row justify-content-center mb-0 mt-60px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="scale-tag-container">
                    <div class="scale-tag">YOUR STRATEGIC PARTNER</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Expertise Driving Your Google Ads Success</span></h3>
                <p>Achieving peak profitability on Google Ads requires more than just technology – it demands deep expertise and a strategic partnership. Adzeta brings you both, ensuring our Predictive AI translates into real-world success for your e-commerce campaigns.</p>
            </div>
        </div>
        <!-- Ad Platform Blocks with Central Image -->
        <div class="ad-networks-wrapper position-relative" style="height: 1000px;">
            <!-- Central Image -->
            <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: -1;">
                <img class="central-image" src="images/bg-ecom-platforms.png" alt="Ad Networks Integration" style="max-width: 550px; animation: fadeInAnimation 1.5s ease-out forwards, floatAnimation 6s ease-in-out infinite 1.5s;">
            </div>
            <!-- Block 1: Google Ads - Top Left -->
            <div class="ad-platform-card position-absolute" style="top: 100px; left: 20px;" data-anime='{ "translateY": [-30, 0], "opacity": [0,1], "duration": 800, "delay": 200 }'>
                <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                    <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                    <div class="d-flex align-items-center mb-20px position-relative">
                        <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                            <img src="images/google-ai.png" alt="Google Ads" height="40">
                        </div>
                        <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Google VBB Specialists</h3>
                    </div>
                    <p class="mb-20px position-relative fs-15 lh-24 text-left"> Deep knowledge of tROAS, Max Conversion Value, PMax & Shopping nuances for e-commerce profit.</p>
                    <!--  <div class="text-left position-relative">
                        <a href="/services/ecommerce-ppc" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Google Ads →</a>
                        </div> -->
                </div>
            </div>
            <!-- Block 2: Meta Ads - Top Right -->
            <div class="ad-platform-card position-absolute" style="top: 180px; right: 20px;" data-anime='{ "translateY": [-30, 0], "opacity": [0,1], "duration": 800, "delay": 300 }'>
                <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                    <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                    <div class="d-flex align-items-center mb-20px position-relative">
                        <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                            <img src="images/e-com-focused.png" alt="E-commerce-Focused" height="60" >
                        </div>
                        <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">E-commerce Focused</h3>
                    </div>
                    <p class="mb-20px position-relative fs-15 lh-24 text-left">Strategies and AI models specifically trained for D2C and online retail challenges on Google Ads.</p>
                    <!--   <div class="text-left position-relative">
                        <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Meta Ads →</a>
                        </div> -->
                </div>
            </div>
            <!-- Block 3: TikTok Ads - Bottom Left -->
            <div class="ad-platform-card position-absolute" style="bottom: 180px; left: 20px;" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 400 }'>
                <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                    <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                    <div class="d-flex align-items-center mb-20px position-relative">
                        <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                            <img src="images/precision-ai.png" alt="TikTok Ads" height="40" style="filter: drop-shadow(0px 2px 4px rgba(0,0,0,0.1));">
                        </div>
                        <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Precision AI Integration</h3>
                    </div>
                    <p class="mb-20px position-relative fs-15 lh-24 text-left"> Seamlessly feeding predictive LTV into Google's ecosystem for enhanced algorithm performance.</p>
                    <!--      <div class="text-left position-relative">
                        <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about TikTok Ads →</a>
                        </div> -->
                </div>
            </div>
            <!-- Block 4: Programmatic DSPs - Bottom Right -->
            <div class="ad-platform-card position-absolute" style="bottom: 100px; right: 10px;" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 500 }'>
                <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                    <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                    <div class="d-flex align-items-center mb-20px position-relative">
                        <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                            <img src="images/st-partner.png" alt="Strategic-Partner" height="40" >
                        </div>
                        <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Your Strategic Partner</h3>
                    </div>
                    <p class="mb-20px position-relative fs-15 lh-24 text-left">Ongoing analysis, transparent reporting, and collaborative support to ensure continuous Google Ads success.</p>
                    <!--  <div class="text-left position-relative">
                        <a href="/services/programmatic" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Programmatic →</a>
                        </div> -->
                </div>
            </div>
        </div>
        <!-- Mobile Version (will be shown only on small screens) -->
        <div class="d-block d-lg-none">
            <div class="text-center mb-30px">
                <img class="central-image" src="images/bg-ecom-platforms.png" alt="Ad Networks Integration" style="max-width: 320px;">
            </div>
            <div class="row">
                <div class="col-md-6 col-12 mb-30px">
                    <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                        <div class="d-flex align-items-center mb-20px">
                            <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                <img src="images/google-ai.png" alt="Google Ads" height="40">
                            </div>
                            <h3 class="alt-font fw-600 fs-20 mb-0">Google VBB Specialists</h3>
                        </div>
                        <p class="mb-20px text-left"> Deep knowledge of tROAS, Max Conversion Value, PMax & Shopping nuances for e-commerce profit.</p>
                        <!-- <div class="text-left">
                            <a href="/services/ecommerce-ppc" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Google Ads →</a>
                            </div> -->
                    </div>
                </div>
                <div class="col-md-6 col-12 mb-30px">
                    <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                        <div class="d-flex align-items-center mb-20px">
                            <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                <img src="images/e-com-focused.png" alt="Meta Ads" height="40">
                            </div>
                            <h3 class="alt-font fw-600 fs-20 mb-0">E-commerce Focused</h3>
                        </div>
                        <p class="mb-20px text-left"> Strategies and AI models specifically trained for D2C and online retail challenges on Google Ads.</p>
                        <!-- <div class="text-left">
                            <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Meta Ads →</a>
                            </div> -->
                    </div>
                </div>
                <div class="col-md-6 col-12 mb-30px">
                    <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                        <div class="d-flex align-items-center mb-20px">
                            <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                <img src="images/precision-ai.png" alt="TikTok Ads" height="40">
                            </div>
                            <h3 class="alt-font fw-600 fs-20 mb-0">Precision AI Integration</h3>
                        </div>
                        <p class="mb-20px text-left"> Seamlessly feeding predictive LTV into Google's ecosystem for enhanced algorithm performance.</p>
                        <!--      <div class="text-left">
                            <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about TikTok Ads →</a>
                            </div> -->
                    </div>
                </div>
                <div class="col-md-6 col-12 mb-30px">
                    <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                        <div class="d-flex align-items-center mb-20px">
                            <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                <img src="images/st-partner.png" alt="Programmatic Advertising" height="40">
                            </div>
                            <h3 class="alt-font fw-600 fs-20 mb-0">Your Strategic Partner</h3>
                        </div>
                        <p class="mb-20px text-left">Ongoing analysis, transparent reporting, and collaborative support to ensure continuous Google Ads success.</p>
                        <!--  <div class="text-left">
                            <a href="/services/programmatic" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Programmatic →</a>
                            </div> -->
                    </div>
                </div>
            </div>
        </div>
        <div class=" p-40px lg-p-30px md-p-25px border-radius-20px">
            <div class="row justify-content-center">
                <div class="col-12 col-xl-7 col-lg-8 col-md-8 text-center margin-5-rem-bottom md-margin-3-rem-bottom ">
                    <h2 class="alt-font fw-600 text-dark-gray margin-20px-bottom fs-40 lg-fs-32 md-fs-30 sm-fs-28 ls-minus-1px">Ready to Maximize Your Google Ads Profit Potential?</h2>
                    <p class="w-80 mx-auto md-w-100 md-fs-15 sm-fs-14">Stop leaving money on the table. Discover how much hidden LTV and untapped growth opportunities Adzeta's Predictive AI can unlock specifically within your Google Ads campaigns. Get your free, no-obligation analysis today.</p>
                </div>
            </div>
            <!-- CTA Buttons -->
            <div class="row justify-content-center mb-5">
                <div class="col-12 col-lg-10 text-center">
                    <a href="free-ad-audit.php" class="btn btn-large btn-gradient-pink-orange btn-round-edge margin-15px-right sm-margin-15px-bottom">
                        Request Your Free Google Ads Analysis
                        <span style="margin-left:10px;">
                            <svg width="12" height="12" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1.36406 10.3334L0.664062 9.63338L8.96406 1.33337H4.06406V0.333374H10.6641V6.93337H9.66406V2.03337L1.36406 10.3334Z" fill="#fff"></path>
                            </svg>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- start enhanced footer -->
<?php include 'footer.php'; ?>