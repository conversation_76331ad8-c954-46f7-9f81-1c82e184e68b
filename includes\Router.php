<?php
/**
 * Simple Router Class
 * Handles URL routing for clean URLs and dynamic pages
 */

class Router {
    private $routes = [];
    private $pageManager;
    
    public function __construct() {
        $this->pageManager = new PageManager();
        $this->setupDefaultRoutes();
    }
    
    private function setupDefaultRoutes() {
        // Default routes
        $this->addRoute('GET', '/', [$this, 'handleHomePage']);
        $this->addRoute('GET', '/blog', [$this, 'handleBlogIndex']);
        $this->addRoute('GET', '/blog/{slug}', [$this, 'handleBlogPost']);
        $this->addRoute('GET', '/contact', [$this, 'handleContactPage']);
        $this->addRoute('POST', '/contact', [$this, 'handleContactSubmission']);
        
        // Admin routes
        $this->addRoute('GET', '/admin', [$this, 'handleAdminDashboard']);
        $this->addRoute('GET', '/admin/pages', [$this, 'handleAdminPages']);
        $this->addRoute('GET', '/admin/blog', [$this, 'handleAdminBlog']);
        
        // Catch-all for dynamic pages
        $this->addRoute('GET', '/{slug}', [$this, 'handleDynamicPage']);
    }
    
    public function addRoute($method, $path, $handler) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler
        ];
    }
    
    public function dispatch() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Remove trailing slash except for root
        if ($path !== '/' && substr($path, -1) === '/') {
            $path = rtrim($path, '/');
        }
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $path)) {
                $params = $this->extractParams($route['path'], $path);
                return call_user_func_array($route['handler'], $params);
            }
        }
        
        // 404 Not Found
        $this->handle404();
    }
    
    private function matchPath($routePath, $requestPath) {
        // Convert route path to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $requestPath);
    }
    
    private function extractParams($routePath, $requestPath) {
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        preg_match($pattern, $requestPath, $matches);
        array_shift($matches); // Remove full match
        
        return $matches;
    }
    
    public function handleHomePage() {
        // Check if there's a custom home page in database
        $homePage = $this->pageManager->getPage('home');
        
        if ($homePage) {
            echo $this->pageManager->renderPage($homePage);
        } else {
            // Fallback to existing index.php content
            $this->includeTemplate('index');
        }
    }
    
    public function handleBlogIndex() {
        $posts = $this->pageManager->getBlogPosts(10);
        $pageData = [
            'title' => 'Blog',
            'description' => 'Latest insights on AI-powered advertising and e-commerce growth',
            'template' => 'blog-index'
        ];
        
        echo $this->pageManager->renderPage($pageData, ['posts' => $posts]);
    }
    
    public function handleBlogPost($slug) {
        $post = $this->pageManager->getBlogPost($slug);
        
        if (!$post) {
            $this->handle404();
            return;
        }
        
        $pageData = [
            'title' => $post['title'],
            'description' => $post['meta_description'] ?: $post['excerpt'],
            'keywords' => $post['meta_keywords'],
            'og_image' => $post['og_image'] ?: $post['featured_image'],
            'template' => 'blog-single'
        ];
        
        echo $this->pageManager->renderPage($pageData, ['post' => $post]);
    }
    
    public function handleContactPage() {
        $pageData = [
            'title' => 'Contact Us',
            'description' => 'Get in touch with AdZeta for AI-powered advertising solutions',
            'template' => 'contact'
        ];
        
        echo $this->pageManager->renderPage($pageData);
    }
    
    public function handleContactSubmission() {
        // Handle contact form submission
        if ($_POST) {
            $data = [
                'name' => $_POST['name'] ?? '',
                'email' => $_POST['email'] ?? '',
                'company' => $_POST['company'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'subject' => $_POST['subject'] ?? '',
                'message' => $_POST['message'] ?? '',
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            
            $db = Database::getInstance();
            $db->insert('contact_submissions', $data);
            
            // Redirect with success message
            header('Location: /contact?success=1');
            exit;
        }
        
        $this->handleContactPage();
    }
    
    public function handleDynamicPage($slug) {
        $page = $this->pageManager->getPage($slug);
        
        if (!$page) {
            $this->handle404();
            return;
        }
        
        echo $this->pageManager->renderPage($page);
    }
    
    public function handleAdminDashboard() {
        // Simple admin check (implement proper authentication)
        if (!$this->isAdmin()) {
            header('Location: /admin/login');
            exit;
        }
        
        $this->includeTemplate('admin/dashboard');
    }
    
    public function handleAdminPages() {
        if (!$this->isAdmin()) {
            header('Location: /admin/login');
            exit;
        }
        
        $pages = $this->pageManager->getAllPages();
        $this->includeTemplate('admin/pages', ['pages' => $pages]);
    }
    
    public function handleAdminBlog() {
        if (!$this->isAdmin()) {
            header('Location: /admin/login');
            exit;
        }
        
        $posts = $this->pageManager->getBlogPosts(50);
        $this->includeTemplate('admin/blog', ['posts' => $posts]);
    }
    
    private function handle404() {
        http_response_code(404);
        $pageData = [
            'title' => '404 - Page Not Found',
            'description' => 'The page you are looking for could not be found.',
            'template' => '404'
        ];
        
        echo $this->pageManager->renderPage($pageData);
    }
    
    private function includeTemplate($template, $data = []) {
        $templateFile = $template . '.php';
        
        // Check if it's an absolute path or relative
        if (strpos($template, '/') !== false) {
            $templateFile = TEMPLATES_PATH . '/' . $template . '.php';
        } else {
            $templateFile = $template . '.php';
        }
        
        if (file_exists($templateFile)) {
            extract($data);
            include $templateFile;
        } else {
            echo "Template not found: " . $templateFile;
        }
    }
    
    private function isAdmin() {
        // Simple admin check - implement proper session management
        return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
    }
}
