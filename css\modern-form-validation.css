/* Modern Form Validation Styles */

/* Form validation states */
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    animation: shake 0.5s ease-in-out;
}

/* Shake animation for invalid fields */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Form results styling */
.form-results {
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.form-results.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #c3e6cb;
    color: #155724;
}

.form-results.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Loading state for buttons */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.15em;
}

/* Enhanced form styling */
.homepage-cta-form .form-control:focus {
    border-color: #de347f;
    box-shadow: 0 0 0 0.2rem rgba(222, 52, 127, 0.25);
}

#free-audit-form .form-control:focus {
    border-color: #de347f;
    box-shadow: 0 0 0 0.2rem rgba(222, 52, 127, 0.25);
}

/* Smooth transitions */
.form-control {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Success checkmark animation */
.form-control.is-valid::after {
    content: '✓';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #28a745;
    font-weight: bold;
}

/* Error icon animation */
.form-control.is-invalid::after {
    content: '✗';
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #dc3545;
    font-weight: bold;
}

/* Form group positioning for icons */
.form-group {
    position: relative;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .form-results {
        font-size: 14px;
        padding: 12px;
    }
    
    .form-control.is-invalid,
    .form-control.is-valid {
        box-shadow: none;
        border-width: 2px;
    }
}

/* Professional loading overlay for forms */
.form-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.form-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
    z-index: 10;
    border-radius: 8px;
    backdrop-filter: blur(2px);
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid rgba(222, 52, 127, 0.2);
    border-top: 3px solid #de347f;
    border-radius: 50%;
    animation: professional-spin 1.2s linear infinite;
    z-index: 11;
    box-shadow: 0 4px 15px rgba(222, 52, 127, 0.1);
}

@keyframes professional-spin {
    0% {
        transform: rotate(0deg);
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        transform: rotate(360deg);
        opacity: 1;
    }
}

/* Professional button loading states */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: professional-spin 1s linear infinite;
}

/* Enhanced button states */
.btn-gradient-pink-orange:hover:not(:disabled):not(.loading) {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(222, 52, 127, 0.3);
    transition: all 0.3s ease;
}

.btn-gradient-pink-orange:active:not(:disabled):not(.loading) {
    transform: translateY(0);
}

/* Professional success state */
.btn.success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none;
    color: white;
}

.btn.success::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    font-weight: bold;
}

/* Focus states for accessibility */
.form-control:focus {
    outline: none;
}

.btn:focus {
    outline: 2px solid #de347f;
    outline-offset: 2px;
}

/* Custom select styling */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
}

select.form-control:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23de347f' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Beautiful Success Message Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Success Checkmark Animation */
.success-checkmark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    stroke-width: 3;
    stroke: #28a745;
    stroke-miterlimit: 10;
    margin: 0 auto;
    box-shadow: inset 0px 0px 0px #28a745;
    animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
    position: relative;
}

.success-checkmark.animate {
    animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
}

.success-checkmark .check-icon {
    width: 80px;
    height: 80px;
    position: relative;
    border-radius: 50%;
    box-sizing: border-box;
    border: 3px solid #28a745;
}

.success-checkmark .check-icon::before {
    top: 3px;
    left: -2px;
    width: 30px;
    transform-origin: 100% 50%;
    border-radius: 100px 0 0 100px;
}

.success-checkmark .check-icon::after {
    top: 0;
    left: 30px;
    width: 60px;
    transform-origin: 0 50%;
    border-radius: 0 100px 100px 0;
    animation: rotate-circle 4.25s ease-in;
}

.success-checkmark .check-icon::before,
.success-checkmark .check-icon::after {
    content: '';
    height: 100px;
    position: absolute;
    background: #fff;
    transform: rotate(-45deg);
}

.success-checkmark .icon-line {
    height: 3px;
    background-color: #28a745;
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 10;
}

.success-checkmark .icon-line.line-tip {
    top: 46px;
    left: 14px;
    width: 25px;
    transform: rotate(45deg);
    animation: icon-line-tip 0.75s;
}

.success-checkmark .icon-line.line-long {
    top: 38px;
    right: 8px;
    width: 47px;
    transform: rotate(-45deg);
    animation: icon-line-long 0.75s;
}

.success-checkmark .icon-circle {
    top: -3px;
    left: -3px;
    z-index: 10;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    position: absolute;
    box-sizing: content-box;
    border: 3px solid #28a745;
}

.success-checkmark .icon-fix {
    top: 8px;
    width: 5px;
    left: 26px;
    z-index: 1;
    height: 85px;
    position: absolute;
    transform: rotate(-45deg);
    background-color: #fff;
}

@keyframes rotate-circle {
    0% {
        transform: rotate(-45deg);
    }
    5% {
        transform: rotate(-45deg);
    }
    12% {
        transform: rotate(-405deg);
    }
    100% {
        transform: rotate(-405deg);
    }
}

@keyframes icon-line-tip {
    0% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    54% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    70% {
        width: 50px;
        left: -8px;
        top: 37px;
    }
    84% {
        width: 17px;
        left: 21px;
        top: 48px;
    }
    100% {
        width: 25px;
        left: 14px;
        top: 45px;
    }
}

@keyframes icon-line-long {
    0% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    65% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    84% {
        width: 55px;
        right: 0px;
        top: 35px;
    }
    100% {
        width: 47px;
        right: 8px;
        top: 38px;
    }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 60px #28a745;
    }
}

@keyframes scale {
    0%, 100% {
        transform: none;
    }
    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

/* Next steps styling */
.audit-success-message .next-steps {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid rgba(0,0,0,0.05);
}

.audit-success-message .step-item {
    text-align: center;
}

.audit-success-message .cta-buttons .btn {
    transition: all 0.3s ease;
}

.audit-success-message .cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}
