/* Circular Workflow Styles - Apple-inspired Design */

.workflow-section {
    padding: 80px 0;
    background: linear-gradient(to bottom, #F7F7F9 0%, #FFFFFF 100%);
    position: relative;
    overflow: hidden;
}

.workflow-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
}

.workflow-circular-container {
    position: relative;
    width: 100%;
    height: 500px;
    margin: 40px auto;
    overflow: visible;
}

/* Card styles with Apple-inspired design */
.workflow-card {
    position: absolute;
    width: 160px;
    height: 180px;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.05),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    padding: 15px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 1px solid rgba(255, 255, 255, 0.7);
}

.workflow-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 8px 24px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Card accent colors */
.workflow-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: 16px 16px 0 0;
}

.client-card::before {
    background: linear-gradient(90deg, #FF6A8E 0%, #FF4775 100%);
}

.adzeta-card::before {
    background: linear-gradient(90deg, #7D7AFF 0%, #5E5CE6 100%);
}

.google-card::before {
    background: linear-gradient(90deg, #4285F4 0%, #3372DB 100%);
}

.results-card::before {
    background: linear-gradient(90deg, #30D158 0%, #27AE60 100%);
}

/* Card content */
.card-icon {
    width: 80px;
    height: 80px;
    margin-bottom: 10px;
    object-fit: contain;
}

.card-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    letter-spacing: -0.5px;
}

.card-subtitle {
    font-size: 11px;
    color: #666;
    font-weight: 400;
}

/* Connection paths */
.connection-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
}

/* Data particles */
.data-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    filter: blur(1px);
    z-index: 6;
}

.client-particle {
    background: #FF6A8E;
    box-shadow: 0 0 6px rgba(255, 106, 142, 0.6);
}

.adzeta-particle {
    background: #7D7AFF;
    box-shadow: 0 0 6px rgba(125, 122, 255, 0.6);
}

.google-particle {
    background: #4285F4;
    box-shadow: 0 0 6px rgba(66, 133, 244, 0.6);
}

.results-particle {
    background: #30D158;
    box-shadow: 0 0 6px rgba(48, 209, 88, 0.6);
}

/* Feedback loop */
.feedback-loop {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 10px 30px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.7);
    z-index: 20;
}

.feedback-loop::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    border: 1.5px solid transparent;
    background: linear-gradient(90deg, #FF6A8E, #7D7AFF, #30D158) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
    pointer-events: none;
}

.feedback-icon {
    width: 24px;
    height: 24px;
    margin-right: 15px;
    animation: rotate 8s linear infinite;
}

.feedback-text {
    font-size: 14px;
    font-weight: 500;
    background: linear-gradient(90deg, #FF6A8E, #7D7AFF, #30D158);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Animations */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0% { opacity: 0.7; transform: scale(0.95); }
    50% { opacity: 1; transform: scale(1.05); }
    100% { opacity: 0.7; transform: scale(0.95); }
}

@keyframes moveParticle1 {
    0% { left: 160px; top: 250px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 320px; top: 150px; opacity: 0; }
}

@keyframes moveParticle2 {
    0% { left: 320px; top: 150px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 480px; top: 250px; opacity: 0; }
}

@keyframes moveParticle3 {
    0% { left: 480px; top: 250px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 640px; top: 150px; opacity: 0; }
}

@keyframes moveParticle4 {
    0% { left: 640px; top: 150px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 800px; top: 250px; opacity: 0; }
}

@keyframes moveParticle5 {
    0% { left: 800px; top: 250px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 960px; top: 150px; opacity: 0; }
}

@keyframes moveParticle6 {
    0% { left: 960px; top: 150px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 1120px; top: 250px; opacity: 0; }
}

@keyframes moveFeedback {
    0% { left: 1120px; top: 250px; opacity: 0; }
    5% { opacity: 1; }
    45% { left: 600px; top: 400px; opacity: 1; }
    50% { left: 600px; top: 400px; opacity: 1; }
    95% { opacity: 1; }
    100% { left: 80px; top: 250px; opacity: 0; }
}

/* Workflow legend */
.workflow-legend {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 15px;
    font-size: 13px;
    color: #555;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
    margin-right: 8px;
}

.legend-client {
    background: linear-gradient(90deg, #FF6A8E 0%, #FF4775 100%);
}

.legend-adzeta {
    background: linear-gradient(90deg, #7D7AFF 0%, #5E5CE6 100%);
}

.legend-google {
    background: linear-gradient(90deg, #4285F4 0%, #3372DB 100%);
}

.legend-results {
    background: linear-gradient(90deg, #30D158 0%, #27AE60 100%);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .workflow-circular-container {
        height: 700px;
        transform: scale(0.9);
        transform-origin: top center;
    }
}

@media (max-width: 992px) {
    .workflow-circular-container {
        height: 900px;
        transform: scale(0.8);
    }
}

@media (max-width: 768px) {
    .workflow-circular-container {
        height: 1200px;
        transform: scale(0.7);
    }
}

@media (max-width: 576px) {
    .workflow-circular-container {
        height: 1400px;
        transform: scale(0.6);
    }
}
