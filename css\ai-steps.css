/* AI Steps Section Styles */
.ai-steps-section {
    background-color: #f8f9fa;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.ai-steps-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255, 106, 141, 0.05) 0%, rgba(248, 249, 250, 0) 70%);
    pointer-events: none;
}

.ai-steps-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ff6a8d' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
    opacity: 0.5;
}

/* Steps Container */
.ai-steps-container {
    position: relative;
    margin-top: 40px;
}

/* Left Side Steps */
.ai-steps-left {
    position: relative;
}

.ai-step-item {
    display: flex;
    margin-bottom: 40px;
    position: relative;
    transition: all 0.3s ease;
    padding: 10px;
    border-radius: 8px;
}

.ai-step-item:hover, .ai-step-item.active {
    transform: translateX(5px);
    background-color: rgba(255, 106, 141, 0.05);
}

.ai-step-item.animate {
    animation: fade-slide-in 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes fade-slide-in {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-number {
    margin-right: 20px;
    position: relative;
}

.step-number::after {
    content: '';
    position: absolute;
    top: 50px;
    left: 25px;
    width: 2px;
    height: calc(100% + 20px);
    background: linear-gradient(to bottom, #ff6a8d, rgba(255, 106, 141, 0.1));
    z-index: 0;
    border-radius: 1px;
}

.ai-step-item:last-child .step-number::after {
    display: none;
}

.number-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid #ff6a8d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #ff6a8d;
    background-color: #ffffff;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 106, 141, 0.1);
}

.ai-step-item:hover .number-circle {
    background-color: #ff6a8d;
    color: #ffffff;
    box-shadow: 0 8px 20px rgba(255, 106, 141, 0.2);
    transform: scale(1.05);
}

.step-content {
    flex: 1;
}

.step-content h4 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2c2e3c;
    transition: color 0.3s ease;
}

.ai-step-item:hover .step-content h4 {
    color: #ff6a8d;
}

/* Right Side Unified Illustration */
.ai-steps-right {
    position: relative;
}

.unified-illustration-container {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Device Frame */
.device-frame {
    position: relative;
    width: 90%;
    max-width: 400px;
    height: 450px;
    margin: 0 auto;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(255, 106, 141, 0.1);
    overflow: hidden;
    padding: 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transform: perspective(1000px) rotateY(-5deg);
    transition: transform 0.5s ease;
}

.device-frame:hover {
    transform: perspective(1000px) rotateY(0deg);
}

.device-screen {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
}

/* Unified Illustration */
.unified-illustration {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Step Content */
.step-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 20px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
    pointer-events: none;
    display: flex;
    flex-direction: column;
}

.step-content.active {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* Step Header */
.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.step-icon {
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* AI Badge */
.ai-badge {
    display: flex;
    align-items: center;
    background: linear-gradient(90deg, #ff6a8d, #e958a1);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    margin-top: auto;
    align-self: center;
    box-shadow: 0 4px 10px rgba(255, 106, 141, 0.2);
}

.ai-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2a10 10 0 1 0 10 10H12V2z'/%3E%3Cpath d='M12 2a10 10 0 0 1 10 10h-10V2z'/%3E%3Cpath d='M12 12l-8 8'/%3E%3Cpath d='M12 12l8 8'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

/* Step 1: Customer Segments */
.customer-segments {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 10px;
}

.segment {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 10px;
    background: #f8f9fa;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.segment:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.segment.high-value {
    border-left: 4px solid #ff6a8d;
}

.segment.medium-value {
    border-left: 4px solid #ffb347;
}

.segment.low-value {
    border-left: 4px solid #aaaaaa;
}

.segment-icon {
    font-size: 24px;
    margin-right: 15px;
}

.segment-info {
    flex: 1;
}

.segment-info h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.segment-value {
    font-size: 18px;
    font-weight: 700;
    color: #ff6a8d;
}

/* Step 2: LTV Prediction */
.ltv-prediction {
    display: flex;
    justify-content: space-between;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.customer-profile {
    display: flex;
    align-items: center;
}

.profile-icon {
    font-size: 24px;
    margin-right: 10px;
}

.profile-details {
    font-size: 13px;
}

.profile-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.profile-traits {
    display: flex;
    flex-direction: column;
    font-size: 12px;
    color: #666;
}

.prediction-result {
    text-align: right;
}

.prediction-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.prediction-value {
    font-size: 24px;
    font-weight: 700;
    color: #ff6a8d;
}

.confidence {
    font-size: 12px;
    color: #666;
}

.prediction-factors {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.factor {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.factor-name {
    font-size: 13px;
    font-weight: 500;
}

.factor-bar {
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.factor-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6a8d, #e958a1);
    border-radius: 4px;
    width: 0;
    transition: width 1s ease;
}

/* Step 3: Bid Optimization */
.bid-optimization {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.platform-bids {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.platform {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 10px;
    background: #f8f9fa;
    transition: transform 0.3s ease;
}

.platform:hover {
    transform: translateX(5px);
}

.platform-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 12px;
    color: white;
}

.platform-icon.google {
    background: #4285F4;
}

.platform-icon.facebook {
    background: #1877F2;
}

.platform-icon.instagram {
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
}

.platform-details {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.platform-name {
    font-size: 14px;
    font-weight: 500;
}

.bid-adjustment {
    font-weight: 700;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
}

.bid-adjustment.increase {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.bid-adjustment.decrease {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.optimization-results {
    display: flex;
    justify-content: space-around;
    margin-top: 10px;
}

.result {
    text-align: center;
}

.result-value {
    font-size: 20px;
    font-weight: 700;
}

.result-value:first-child {
    color: #dc3545;
}

.result-value:last-child {
    color: #28a745;
}

.result-label {
    font-size: 12px;
    color: #666;
}

/* Animations for the Unified Illustration */

/* Step Content Animations */
.step-content.active .step-header {
    animation: fade-in 0.5s ease-out forwards;
}

.step-content.active .ai-badge {
    animation: slide-up 0.6s ease-out forwards 0.8s;
    opacity: 0;
    transform: translateY(10px);
}

@keyframes slide-up {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Step 1 Animations */
.step-content.active .segment {
    animation: slide-in 0.5s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}

.step-content.active .segment:nth-child(1) {
    animation-delay: 0.2s;
}

.step-content.active .segment:nth-child(2) {
    animation-delay: 0.4s;
}

.step-content.active .segment:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes slide-in {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.step-content.active .segment-icon {
    animation: pop 0.5s ease-out forwards;
    animation-delay: 0.3s;
    transform: scale(0);
}

@keyframes pop {
    0% {
        transform: scale(0);
    }
    70% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.step-content.active .segment-value {
    animation: fade-in 0.5s ease-out forwards;
    animation-delay: 0.5s;
    opacity: 0;
}

@keyframes fade-in {
    to {
        opacity: 1;
    }
}

/* Step 2 Animations */
.step-content.active .ltv-prediction {
    animation: fade-in 0.5s ease-out forwards;
    animation-delay: 0.2s;
    opacity: 0;
}

.step-content.active .customer-profile {
    animation: slide-in 0.5s ease-out forwards;
    animation-delay: 0.3s;
    opacity: 0;
    transform: translateX(-20px);
}

.step-content.active .prediction-result {
    animation: slide-in-right 0.5s ease-out forwards;
    animation-delay: 0.4s;
    opacity: 0;
    transform: translateX(20px);
}

@keyframes slide-in-right {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.step-content.active .prediction-value {
    animation: highlight 1s ease-out forwards;
    animation-delay: 0.6s;
}

@keyframes highlight {
    0% {
        color: #ff6a8d;
    }
    50% {
        color: #e958a1;
    }
    100% {
        color: #ff6a8d;
    }
}

.step-content.active .factor {
    animation: slide-up 0.5s ease-out forwards;
    opacity: 0;
    transform: translateY(10px);
}

.step-content.active .factor:nth-child(1) {
    animation-delay: 0.5s;
}

.step-content.active .factor:nth-child(2) {
    animation-delay: 0.6s;
}

.step-content.active .factor:nth-child(3) {
    animation-delay: 0.7s;
}

/* Step 3 Animations */
.step-content.active .platform {
    animation: slide-in 0.5s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}

.step-content.active .platform:nth-child(1) {
    animation-delay: 0.2s;
}

.step-content.active .platform:nth-child(2) {
    animation-delay: 0.4s;
}

.step-content.active .platform:nth-child(3) {
    animation-delay: 0.6s;
}

.step-content.active .platform-icon {
    animation: rotate-in 0.5s ease-out forwards;
    transform: rotate(-90deg) scale(0.5);
    opacity: 0;
}

@keyframes rotate-in {
    to {
        transform: rotate(0) scale(1);
        opacity: 1;
    }
}

.step-content.active .platform:nth-child(1) .platform-icon {
    animation-delay: 0.3s;
}

.step-content.active .platform:nth-child(2) .platform-icon {
    animation-delay: 0.5s;
}

.step-content.active .platform:nth-child(3) .platform-icon {
    animation-delay: 0.7s;
}

.step-content.active .bid-adjustment {
    animation: pulse-color 2s infinite;
}

@keyframes pulse-color {
    0% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.8;
    }
}

.step-content.active .optimization-results {
    animation: fade-in 0.5s ease-out forwards;
    animation-delay: 0.8s;
    opacity: 0;
}

.step-content.active .result-value {
    animation: count-up 1s ease-out forwards;
    animation-delay: 1s;
    counter-reset: count 0;
}

@keyframes count-up {
    from {
        opacity: 0.5;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}



/* Responsive Adjustments */
@media (max-width: 991px) {
    .ai-steps-section {
        padding: 60px 0;
    }

    .ai-illustrations-container {
        height: auto;
        margin-top: 40px;
    }

    .ai-illustration {
        position: relative;
        top: auto !important;
        left: auto !important;
        margin-bottom: 30px;
        display: none;
    }

    .ai-illustration.active {
        display: block;
    }
}
