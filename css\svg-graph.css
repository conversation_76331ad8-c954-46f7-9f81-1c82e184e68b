/* SVG Graph Styles - Enhanced to match canvas version */

.animated-graph-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 400px;
    overflow: hidden;
    background-color: transparent;
}

/* SVG Base Styles */
.graph-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.1));
}

/* Grid and Axes */
.grid-line {
    stroke: rgba(255, 255, 255, 0.05);
    stroke-width: 0.5;
    vector-effect: non-scaling-stroke;
}

.axis-label {
    fill: rgba(255, 255, 255, 0.5);
    font-size: 10px;
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    letter-spacing: 0.5px;
}

.axis-title {
    fill: rgba(255, 255, 255, 0.5);
    font-size: 11px;
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    letter-spacing: 0.5px;
}

/* Graph Lines */
.graph-line {
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
    stroke-dasharray: 2000;
    stroke-dashoffset: 2000;
    filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.2));
}

.graph-svg.animate .graph-line {
    animation: drawLine 2.5s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

.adzeta-line {
    stroke: #e958a1;
    stroke-width: 2.5;
    z-index: 10;
}

.traditional-line {
    stroke: #4a9eff;
    stroke-width: 2;
    z-index: 5;
}

/* Graph Areas */
.graph-area {
    opacity: 0;
    mix-blend-mode: screen;
}

.graph-svg.animate .adzeta-area {
    animation: fadeInArea 1.5s ease-in-out 1s forwards;
}

.graph-svg.animate .traditional-area {
    animation: fadeInArea 1.5s ease-in-out 0.5s forwards;
}

.adzeta-area {
    fill: url(#adzetaGradient);
    z-index: 9;
}

.traditional-area {
    fill: url(#traditionalGradient);
    z-index: 4;
}

.profit-difference-area {
    fill: rgba(233, 88, 161, 0.18);
    opacity: 0;
    z-index: 7;
}

.graph-svg.animate .profit-difference-area {
    animation: fadeInArea 1.5s ease-in-out 1.8s forwards;
}

/* Legend */
.legend {
    opacity: 0;
    z-index: 20;
}

.graph-svg.animate .legend {
    animation: fadeIn 0.5s ease-in-out 2.5s forwards;
}

.legend-text {
    fill: rgba(255, 255, 255, 0.8);
    font-size: 10px;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

/* HTML Overlay for Data Points */
.graph-html-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 30;
}

.data-point-container {
    position: absolute;
    transform: translate(-50%, -50%);
    opacity: 0;
    z-index: 15;
}

.data-point-container.animate {
    animation: fallIn 1s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.data-point {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #e958a1;
    border: 1.5px solid rgba(255, 255, 255, 0.8);
    position: relative;
    transform: scale(0);
    box-shadow: 0 0 5px rgba(233, 88, 161, 0.5);
}

.data-point-container.animate .data-point {
    animation: popIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.data-point-label {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%) scale(0);
    color: #e958a1;
    font-size: 10px;
    font-weight: 600;
    font-family: 'Inter', sans-serif;
    white-space: nowrap;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.data-point-container.animate .data-point-label {
    animation: popIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.3s forwards;
}

/* Tooltip */
.graph-tooltip {
    position: absolute;
    background-color: rgba(28, 28, 32, 0.95);
    border: 1px solid rgba(233, 88, 161, 0.3);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 12px;
    font-family: 'Inter', sans-serif;
    color: white;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 100;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    max-width: 200px;
}

.graph-tooltip.visible {
    opacity: 1;
}

.tooltip-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: rgba(255, 255, 255, 0.9);
}

.tooltip-value {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
}

.tooltip-label {
    color: rgba(255, 255, 255, 0.7);
}

.tooltip-adzeta {
    color: #e958a1;
    font-weight: 500;
}

.tooltip-traditional {
    color: #4a9eff;
    font-weight: 500;
}

/* Animations */
@keyframes drawLine {
    0% {
        stroke-dashoffset: 2000;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes fadeInArea {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 0.25;
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fallIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -200%);
    }
    70% {
        opacity: 1;
        transform: translate(-50%, -40%);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

@keyframes popIn {
    0% {
        transform: scale(0);
    }
    70% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .animated-graph-container {
        min-height: 350px;
    }

    .axis-label, .axis-title, .legend-text {
        font-size: 9px;
    }

    .data-point {
        width: 6px;
        height: 6px;
    }

    .data-point-label {
        font-size: 9px;
    }

    .graph-tooltip {
        font-size: 10px;
        padding: 6px 10px;
    }
}
