// Clean Card Metrics Charts and Animations
document.addEventListener('DOMContentLoaded', function() {
    // Wait a short time to ensure other scripts have loaded
    setTimeout(function() {
        // Initialize mini charts
        initCleanCardCharts();
    }, 500);
});

function initCleanCardCharts() {
    console.log('Initializing clean card charts');

    // ROAS Chart - Upward trend
    const roasCtx = document.getElementById('roas-chart');
    if (roasCtx) {
        console.log('Found ROAS chart canvas');
        // Destroy existing chart if it exists
        if (window.roasChart) {
            window.roasChart.destroy();
        }

        window.roasChart = new Chart(roasCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', ''],
                datasets: [{
                    data: [10, 15, 12, 18, 20, 25],
                    borderColor: '#8f76f5',
                    backgroundColor: 'rgba(143, 118, 245, 0.1)',
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } else {
        console.log('ROAS chart canvas not found');
    }

    // CAC Chart - Downward trend (good)
    const cacCtx = document.getElementById('cac-chart');
    if (cacCtx) {
        console.log('Found CAC chart canvas');
        // Destroy existing chart if it exists
        if (window.cacChart) {
            window.cacChart.destroy();
        }

        window.cacChart = new Chart(cacCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', ''],
                datasets: [{
                    data: [25, 22, 18, 15, 12, 10],
                    borderColor: '#e958a1',
                    backgroundColor: 'rgba(233, 88, 161, 0.1)',
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } else {
        console.log('CAC chart canvas not found');
    }

    // Revenue Chart - Upward trend
    const revenueCtx = document.getElementById('revenue-chart');
    if (revenueCtx) {
        console.log('Found Revenue chart canvas');
        // Destroy existing chart if it exists
        if (window.revenueChart) {
            window.revenueChart.destroy();
        }

        window.revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', ''],
                datasets: [{
                    data: [8, 12, 15, 18, 22, 28],
                    borderColor: '#8f76f5',
                    backgroundColor: 'rgba(143, 118, 245, 0.1)',
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } else {
        console.log('Revenue chart canvas not found');
    }

    // Waste Chart - Downward trend (good)
    const wasteCtx = document.getElementById('ad-waste-chart');
    if (wasteCtx) {
        console.log('Found Waste chart canvas');
        // Destroy existing chart if it exists
        if (window.wasteChart) {
            window.wasteChart.destroy();
        }

        window.wasteChart = new Chart(wasteCtx, {
            type: 'line',
            data: {
                labels: ['', '', '', '', '', ''],
                datasets: [{
                    data: [42, 38, 32, 28, 22, 18],
                    borderColor: '#e958a1',
                    backgroundColor: 'rgba(233, 88, 161, 0.1)',
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                },
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } else {
        console.log('Waste chart canvas not found');
    }
}
