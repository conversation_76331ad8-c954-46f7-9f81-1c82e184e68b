/**
 * AdZeta Admin Panel - Core Application
 * Main application controller and state management
 */

window.AdZetaApp = {
    // Application state
    state: {
        isAuthenticated: false,
        user: null,
        token: null,
        currentView: 'dashboard',
        loading: false
    },

    // Configuration
    config: window.AdZetaConfig || {},

    // API base URL
    get apiUrl() {
        return this.config.api_url || '/adzeta-admin/api';
    },

    // Initialize application
    init() {
        console.log('Initializing AdZeta Admin Panel...');
        
        // Hide loading screen
        this.hideLoading();
        
        // Check for existing authentication
        this.checkAuth();
        
        // Initialize modules
        this.initializeModules();
        
        console.log('AdZeta Admin Panel initialized successfully!');
    },

    // Hide loading screen
    hideLoading() {
        const loadingElement = document.getElementById('loading');
        if (loadingElement) {
            setTimeout(() => {
                loadingElement.style.display = 'none';
            }, 500);
        }
    },

    // Check for existing authentication
    checkAuth() {
        const token = localStorage.getItem('adzeta_token');
        const user = localStorage.getItem('adzeta_user');
        
        if (token && user) {
            try {
                this.state.token = token;
                this.state.user = JSON.parse(user);
                this.state.isAuthenticated = true;
                this.showDashboard();
            } catch (error) {
                console.error('Error parsing stored user data:', error);
                this.logout();
            }
        } else {
            this.showLogin();
        }
    },

    // Show login page
    showLogin() {
        document.getElementById('loginPage').style.display = 'flex';
        document.getElementById('adminDashboard').style.display = 'none';
        this.state.isAuthenticated = false;
    },

    // Show admin dashboard
    showDashboard() {
        document.getElementById('loginPage').style.display = 'none';
        document.getElementById('adminDashboard').style.display = 'flex';
        this.state.isAuthenticated = true;

        // Initialize AI Assistant after authentication (with small delay to ensure state is set)
        setTimeout(() => {
            if (window.AdZetaAI && window.AdZetaAI.initializeAfterAuth) {
                window.AdZetaAI.initializeAfterAuth();
            }
        }, 200);

        // Handle initial URL parameters after dashboard is shown
        setTimeout(() => {
            this.handleInitialURL();
        }, 100);
    },

    // Login user
    async login(credentials) {
        try {
            this.state.loading = true;
            this.updateLoginButton(true);

            const response = await fetch(`${this.apiUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: credentials.username,
                    password: credentials.password
                })
            });

            // Check if response is ok
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Get response text first to debug JSON issues
            const responseText = await response.text();

            // Try to parse JSON
            let result;
            try {
                result = JSON.parse(responseText);
            } catch (jsonError) {
                console.error('Login JSON Parse Error:', jsonError);
                console.error('Response Text:', responseText.substring(0, 500));
                throw new Error(`Authentication API returned invalid response. Please check if the API is working correctly.`);
            }

            if (result.success) {
                // Store authentication data
                localStorage.setItem('adzeta_token', result.token);
                localStorage.setItem('adzeta_user', JSON.stringify(result.user));

                if (credentials.remember) {
                    localStorage.setItem('adzeta_remember', 'true');
                }

                // Update state
                this.state.token = result.token;
                this.state.user = result.user;
                this.state.isAuthenticated = true;

                // Update UI
                document.getElementById('currentUser').textContent = result.user.username;

                // Show dashboard
                this.showDashboard();

                return { success: true };
            } else {
                throw new Error(result.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showLoginError(error.message);
            return { success: false, error: error.message };
        } finally {
            this.state.loading = false;
            this.updateLoginButton(false);
        }
    },

    // Logout user
    logout() {
        // Clear stored data
        localStorage.removeItem('adzeta_token');
        localStorage.removeItem('adzeta_user');
        localStorage.removeItem('adzeta_remember');
        
        // Reset state
        this.state.isAuthenticated = false;
        this.state.user = null;
        this.state.token = null;
        
        // Show login page
        this.showLogin();
        
        // Clear any error messages
        this.hideLoginError();
    },

    // Show login error
    showLoginError(message) {
        const errorElement = document.getElementById('loginError');
        const errorText = document.getElementById('loginErrorText');
        
        if (errorElement && errorText) {
            errorText.textContent = message;
            errorElement.style.display = 'block';
        }
    },

    // Hide login error
    hideLoginError() {
        const errorElement = document.getElementById('loginError');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    },

    // Update login button state
    updateLoginButton(loading) {
        const button = document.getElementById('loginBtn');
        const buttonText = document.getElementById('loginBtnText');
        const icon = button.querySelector('i');
        
        if (loading) {
            button.disabled = true;
            icon.className = 'spinner me-2';
            buttonText.textContent = 'Signing in...';
        } else {
            button.disabled = false;
            icon.className = 'fas fa-sign-in-alt me-2';
            buttonText.textContent = 'Sign In';
        }
    },

    // Initialize all modules
    initializeModules() {
        // Initialize authentication
        if (window.AdZetaAuth) {
            window.AdZetaAuth.init();
        }
        
        // Initialize navigation
        if (window.AdZetaNavigation) {
            window.AdZetaNavigation.init();
        }
        
        // Initialize dashboard
        if (window.AdZetaDashboard) {
            window.AdZetaDashboard.init();
        }
        
        // Initialize posts module
        if (window.AdZetaPosts) {
            window.AdZetaPosts.init();
        }

        // Initialize templates module
        if (window.AdZetaTemplates) {
            window.AdZetaTemplates.init();
        }

        // Initialize media library
        if (window.AdZetaMediaLibrary) {
            window.AdZetaMediaLibrary.init();
        }

        // Initialize media manager
        if (window.AdZetaMediaManager) {
            window.AdZetaMediaManager.init();
        }

        // Initialize users module
        if (window.AdZetaUsers) {
            window.AdZetaUsers.init();
        }

        // Initialize post editor
        if (window.AdZetaPostEditor) {
            window.AdZetaPostEditor.init();
        }

        // Initialize settings module
        if (window.AdZetaSettings) {
            window.AdZetaSettings.init();
        }

        // Initialize case studies module
        if (window.AdZetaCaseStudies) {
            window.AdZetaCaseStudies.init();
        }

        // Initialize whitepapers module
        if (window.AdZetaWhitepapers) {
            window.AdZetaWhitepapers.init();
        }

        // Initialize error logs module
        if (window.AdZetaErrorLogs) {
            window.AdZetaErrorLogs.init();
            console.log('AdZetaErrorLogs module initialized successfully');
        } else {
            console.warn('AdZetaErrorLogs module not found during initialization');
        }
    },

    // Utility function for delays
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    },

    // Make API request with centralized logging
    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiUrl}${endpoint}`;
        const startTime = Date.now();

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json'
            }
        };

        // Add authorization header if token exists
        if (this.state.token) {
            defaultOptions.headers['Authorization'] = `Bearer ${this.state.token}`;
        }

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        // Log the request
        const requestId = window.AdZetaAPILogger?.logRequest(
            finalOptions.method || 'GET',
            url,
            finalOptions.body ? JSON.parse(finalOptions.body) : null,
            finalOptions.headers
        );

        try {
            const response = await fetch(url, finalOptions);
            const responseTime = Date.now() - startTime;

            // Handle authentication errors
            if (response.status === 401) {
                const error = new Error('Authentication required');
                window.AdZetaAPILogger?.logError(requestId, error, {
                    action: 'redirecting to login',
                    endpoint: endpoint
                });
                console.error('Authentication failed - redirecting to login');
                this.logout();
                throw error;
            }

            if (!response.ok) {
                // Try to get the actual error message from the API
                let errorMessage = `HTTP error! status: ${response.status}`;
                let errorData = null;

                try {
                    errorData = await response.text();
                    console.error('API Error Response:', errorData);
                    if (errorData) {
                        errorMessage += ` - ${errorData}`;
                    }
                } catch (e) {
                    console.error('Could not parse error response');
                }

                // Log the error response
                window.AdZetaAPILogger?.logResponse(requestId, response.status, errorData, responseTime);

                const error = new Error(errorMessage);
                window.AdZetaAPILogger?.logError(requestId, error, {
                    endpoint: endpoint,
                    responseData: errorData,
                    responseTime: responseTime
                });

                throw error;
            }

            const data = await response.json();

            // Log successful response
            window.AdZetaAPILogger?.logResponse(requestId, response.status, data, responseTime);

            return data;
        } catch (error) {
            // Log any other errors
            if (!error.logged) {
                window.AdZetaAPILogger?.logError(requestId, error, {
                    endpoint: endpoint,
                    options: finalOptions
                });
            }

            console.error('API request failed:', error);
            throw error;
        }
    },

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification container if it doesn't exist
        let notificationContainer = document.getElementById('notificationContainer');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'notificationContainer';
            notificationContainer.className = 'notification-container';
            document.body.appendChild(notificationContainer);
        }

        // Create notification element with Apple-inspired design
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                </div>
                <div class="notification-message">${message}</div>
                <button type="button" class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add slide-in animation
        notification.style.transform = 'translateY(-100%)';
        notification.style.opacity = '0';

        // Add to container
        notificationContainer.appendChild(notification);

        // Trigger animation
        setTimeout(() => {
            notification.style.transform = 'translateY(0)';
            notification.style.opacity = '1';
        }, 10);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.transform = 'translateY(-100%)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    },

    // Get notification icon
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            warning: 'exclamation-triangle',
            danger: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    // Format date
    formatDate(dateString) {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Handle initial URL parameters on page load
    handleInitialURL() {
        const url = new URL(window.location);
        const view = url.searchParams.get('view');
        const action = url.searchParams.get('action');
        const id = url.searchParams.get('id');

        console.log('Handling initial URL:', { view, action, id });

        if (view && action) {
            // Handle specific actions (like edit post)
            if (view === 'posts' && action === 'edit' && id) {
                // Navigate to posts view first, then open editor
                if (window.AdZetaNavigation) {
                    window.AdZetaNavigation.showView('posts');
                    // Delay to ensure posts view is loaded
                    setTimeout(() => {
                        // Set "Add Post" as active for edit actions
                        window.AdZetaNavigation.updateActiveNavLink('add-post');
                        if (window.AdZetaPostEditor) {
                            window.AdZetaPostEditor.edit(id);
                        }
                    }, 200);
                }
            } else if (view === 'posts' && action === 'new') {
                // Handle new post creation
                if (window.AdZetaNavigation) {
                    window.AdZetaNavigation.handleAddPostView();
                }
            } else if (view === 'case-studies' && action === 'edit' && id) {
                // Handle case study editing
                if (window.AdZetaNavigation) {
                    window.AdZetaNavigation.showView('add-case-study');
                    setTimeout(() => {
                        if (window.AdZetaCaseStudyEditor) {
                            window.AdZetaCaseStudyEditor.edit(id);
                        }
                    }, 200);
                }
            } else if (view === 'case-studies' && action === 'new') {
                // Handle new case study creation
                if (window.AdZetaNavigation) {
                    window.AdZetaNavigation.showView('add-case-study');
                    setTimeout(() => {
                        if (window.AdZetaCaseStudyEditor) {
                            window.AdZetaCaseStudyEditor.createNew();
                        }
                    }, 200);
                }
            }
        } else if (view) {
            // Handle simple view navigation
            if (window.AdZetaNavigation) {
                window.AdZetaNavigation.showView(view);
            }
        } else {
            // Default to dashboard if no view specified
            if (window.AdZetaNavigation) {
                window.AdZetaNavigation.showView('dashboard');
            }
        }
    }
};
