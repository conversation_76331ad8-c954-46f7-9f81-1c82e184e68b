<?php include 'header.php'; ?>
        <!-- end header -->
<!-- start page title -->
<section class="page-title-parallax-background half-section ipad-top-space-margin" data-parallax-background-ratio="0.5" style="background-image: url('images/parallax-bg.jpg');">
        <script>
        // Gemini API configuration
        const GEMINI_API_KEY = 'AIzaSyB0Te6GCLgqME_ogMD3flo7VIZZAptRLnY'; // Using the provided API key
        const GEMINI_MODEL = 'gemini-1.5-flash';
        const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/' + GEMINI_MODEL + ':generateContent';

        const apiCallTracker = {
            lastCallTime: 0,
            minTimeBetweenCalls: 1000, // 1 second
            retryCount: 0,
            maxRetries: 3
        };

        async function callGeminiAPI(inputs) {
            const now = Date.now();
            const timeSinceLastCall = now - apiCallTracker.lastCallTime;
            if (timeSinceLastCall < apiCallTracker.minTimeBetweenCalls) {
                const waitTime = apiCallTracker.minTimeBetweenCalls - timeSinceLastCall;
                console.log(`Rate limiting: Waiting ${waitTime}ms`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }

            const requestData = {
                contents: [{
                    parts: [{
                        text: `Analyze this B2C marketing data for High-LTV customer acquisition optimization:
                               Industry: ${inputs.industry}
                               Current Monthly Ad Spend: $${inputs.adSpend}
                               Current CAC for High-LTV Segment: $${inputs.currentCacHighLtv}
                               Percentage of Spend on High-LTV Customers: ${inputs.highLtvSpendPercentage}%
                               Average LTV of a High-LTV Customer: $${inputs.avgLtvHighLtv}
                               Average Order Value (AOV): $${inputs.avgOrderValue}
                               Gross Margin: ${inputs.grossMargin}%

                               Please provide your response in the following JSON format ONLY, with no additional text or markdown:
                               {
                                 "potentialCacReductionHighLtv": "percentage as a number (e.g., 28 for 28%)",
                                 "potentialLtvIncreaseHighLtv": "percentage as a number (e.g., 15 for 15%)",
                                 "recommendedImplementationTimelineMonths": "number of months (e.g., 4)",
                                 "keyInsights": [
                                   "Insight 1 specific to optimizing High-LTV CAC in the given industry and context.",
                                   "Insight 2 related to LTV enhancement or customer retention for this profile.",
                                   "Insight 3 providing a strategic recommendation."
                                 ]
                               }
                               Ensure your response is valid JSON. Focus on realistic, actionable figures for a B2C company.`
                    }]
                }],
                generationConfig: {
                    temperature: 0.3, // Slightly more deterministic for financial advice
                    topP: 0.8,
                    topK: 40,
                    maxOutputTokens: 1024,
                    responseMimeType: "application/json", // Request JSON directly
                }
            };

            try {
                console.log("Calling Gemini API with inputs:", inputs);
                apiCallTracker.lastCallTime = Date.now();
                const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });

                if (response.status === 429) {
                    if (apiCallTracker.retryCount < apiCallTracker.maxRetries) {
                        apiCallTracker.retryCount++;
                        const backoffTime = Math.pow(2, apiCallTracker.retryCount) * 1000;
                        console.log(`Rate limited (429). Retrying in ${backoffTime}ms. Attempt ${apiCallTracker.retryCount}`);
                        await new Promise(resolve => setTimeout(resolve, backoffTime));
                        return callGeminiAPI(inputs);
                    } else {
                        console.error("Max retries for API call reached. Using fallback.");
                        apiCallTracker.retryCount = 0;
                        return getFallbackAiPredictions(inputs.industry);
                    }
                }
                apiCallTracker.retryCount = 0;
                if (!response.ok) {
                    const errorBody = await response.text();
                    console.error(`API request failed: ${response.status}`, errorBody);
                    throw new Error(`API request failed with status ${response.status}`);
                }
                const data = await response.json();
                console.log("Gemini API response:", data);
                return parseGeminiJsonResponse(data);
            } catch (error) {
                console.error('Error calling or parsing Gemini API:', error);
                return getFallbackAiPredictions(inputs.industry);
            }
        }

        function parseGeminiJsonResponse(data) {
             // Gemini now directly returns JSON when responseMimeType is set
            if (data && data.candidates && data.candidates[0] && data.candidates[0].content &&
                data.candidates[0].content.parts && data.candidates[0].content.parts[0] &&
                data.candidates[0].content.parts[0].text) {
                try {
                    const parsedData = JSON.parse(data.candidates[0].content.parts[0].text);
                    console.log("Successfully parsed JSON from Gemini:", parsedData);
                    return {
                        potentialCacReductionHighLtv: parseFloat(parsedData.potentialCacReductionHighLtv) || 25,
                        potentialLtvIncreaseHighLtv: parseFloat(parsedData.potentialLtvIncreaseHighLtv) || 10,
                        recommendedImplementationTimelineMonths: parseInt(parsedData.recommendedImplementationTimelineMonths) || 6,
                        keyInsights: parsedData.keyInsights || getFallbackAiPredictions("ecommerce").keyInsights
                    };
                } catch (jsonError) {
                    console.error("JSON parsing error from Gemini response:", jsonError, "Raw text:", data.candidates[0].content.parts[0].text);
                    throw new Error("Invalid JSON in Gemini response");
                }
            }
            throw new Error("Could not parse Gemini API response structure.");
        }

        function getFallbackAiPredictions(industry) {
            console.log("Using fallback AI predictions for industry:", industry);
            let predictions = {
                potentialCacReductionHighLtv: 25, // Default
                potentialLtvIncreaseHighLtv: 10,  // Default
                recommendedImplementationTimelineMonths: 6, // Default
                keyInsights: [
                    "Focus on granular audience segmentation within your High-LTV customer base to tailor messaging.",
                    "Implement A/B testing for ad creatives and landing pages specifically targeting your best customer profiles.",
                    "Explore loyalty programs or personalized offers to increase repeat purchases from High-LTV customers."
                ]
            };
            if (industry === 'saas') {
                predictions.potentialCacReductionHighLtv = 30;
                predictions.potentialLtvIncreaseHighLtv = 15;
                predictions.keyInsights = [
                    "For SaaS, analyze feature adoption and usage patterns to identify High-LTV indicators.",
                    "Consider tiered pricing or add-ons that appeal to your most valuable customer segments.",
                    "Invest in customer success initiatives to improve retention and expansion revenue from High-LTV clients."
                ];
            } else if (industry === 'finance') {
                predictions.potentialCacReductionHighLtv = 20;
                predictions.potentialLtvIncreaseHighLtv = 12;
                 predictions.keyInsights = [
                    "For Financial Services, leverage data to identify clients with potential for higher-value products.",
                    "Focus on building trust and providing personalized financial advice to High-LTV clients.",
                    "Streamline onboarding processes for premium services to attract and retain valuable customers."
                ];
            }
            return predictions;
        }
        </script>
        <style>
            body { font-family: 'Inter', sans-serif; background-color: #f8f9fa; color: #333; }
            .container-fluid { max-width: 1400px; }
            .calculator-section {  }
			
            /* Range Slider Styles */
            .range-slider-container {
               <!--  display: flex;
                align-items: center;
                gap: 15px; -->
            }

            .range-slider-container .form-range {
                flex-grow: 1;
            }

            .range-slider-container input[type="number"] {
                width: 60px;
                text-align: center;
                -webkit-appearance: textfield;
                -moz-appearance: textfield;
                appearance: textfield; /* Remove spinner for Firefox */
            }

            /* Remove spinner for Chrome, Safari, Edge, Opera */
            .range-slider-container input[type="number"]::-webkit-outer-spin-button,
            .range-slider-container input[type="number"]::-webkit-inner-spin-button {
                -webkit-appearance: none;
                appearance: none;
                margin: 0;
            }

            .form-range {
                height: 8px;
                background: rgba(143, 118, 245, 0.2);
                border-radius: 4px;
                -webkit-appearance: none;
                appearance: none;
            }

            .form-range::-webkit-slider-thumb {
                -webkit-appearance: none;
                width: 22px;
                height: 22px;
                border-radius: 50%;
                background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
                cursor: pointer;
                box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
                margin-top: -7px;
            }

            .form-range::-moz-range-thumb {
                width: 22px;
                height: 22px;
                border-radius: 50%;
                background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
                cursor: pointer;
                box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
                border: none;
            }
            .profitability-calculator-container { background: #ffffff; border-radius: 16px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); overflow: hidden; }
            .calculator-inputs-container, .calculator-results-container { padding: 30px; }
            .calculator-results-container { background: linear-gradient(135deg, #2b2d3a, #393b3b, #4b4d5b); color: white; }
            .form-label { font-weight: 600; margin-bottom: 8px; font-size: 0.9rem;}
            .form-control, .form-select { border-radius: 8px; border: 1px solid #ced4da; padding: 0.75rem 1rem; font-size: 0.95rem; transition: border-color 0.2s ease, box-shadow 0.2s ease;}
            .form-control:focus, .form-select:focus { border-color: #8f76f5; box-shadow: 0 0 0 0.2rem rgba(143, 118, 245, 0.25); }
            .input-group-text { background-color: #e9ecef;border-color: rgba(0, 0, 0, 0.1);}
            .form-range { accent-color: #de347f; }
            .tooltip-icon { cursor: help; color: #6c757d; }
            .result-card { background: rgba(255,255,255,0.08); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.12); border-radius: 20px; padding: 20px; margin-bottom: 20px; }
            .result-card h4 { font-size: 0.9375rem; font-weight: 400; }
            .result-card .value { font-size: 2rem; font-weight: 700; }
            .result-card .icon-circle { min-width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #de347f, #f04b64); display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 6px rgba(222, 52, 127, 0.3); }
            .btn-primary-custom { background: linear-gradient(135deg, #de347f, #8f76f5); border: none; color: white; padding: 10px 20px; border-radius: 8px; font-weight: 500; transition: all 0.3s ease;}
            .btn-primary-custom:hover { opacity: 0.9; box-shadow: 0 4px 12px rgba(143, 118, 245, 0.3); }
            .btn-secondary-custom { background-color: rgba(255,255,255,0.15); color: white; border: 1px solid rgba(255,255,255,0.25); padding: 10px 20px; border-radius: 8px; font-weight: 500; }
            .btn-secondary-custom:hover { background-color: rgba(255,255,255,0.25); }
            .calculation-details { background: rgba(255,255,255,0.05); border-radius: 8px; padding: 15px; margin-top: 20px; font-size: 0.85rem; }
            .calculation-details h6 { font-size: 0.9rem; margin-bottom: 10px; }
            .calculation-details ul { padding-left: 20px; }
            .calculation-details li { margin-bottom: 5px; }
            .modal-content { border-radius: 12px; }
            .modal-header { border-bottom: none; }
            .modal-footer { border-top: none; }
            .spinner-border { color: #8f76f5; }
            #resultsSection h3, #resultsSection .result-card h4, #resultsSection .result-card .value, #resultsSection .text-white { color: #fff !important; }
            .page-title { text-align: center; padding: 30px 0; background-color: #212529; color:white; margin-bottom: 30px;}
            .page-title h1 { font-size: 2.2rem; font-weight: 600;}
            .page-title p { font-size: 1.1rem; color: #adb5bd;}
            .form-group { margin-bottom: 1.75rem; }
            .industry-benchmark { background-color: rgba(222, 52, 127, 0.05); border: 1px solid rgba(222, 52, 127, 0.1); padding: 15px; border-radius: 10px; font-size: 0.85rem; margin-top: 10px;}
            .text-gradient-primary { background: linear-gradient(135deg, #de347f, #8f76f5); -webkit-background-clip: text; background-clip: text; color: transparent;}
            .alert-info-custom { background-color: rgba(13, 202, 240, 0.1); border: 1px solid rgba(13, 202, 240, 0.2); color: #0a58ca; border-radius: 8px; padding: 15px;}
            .alert-success-custom { background-color: rgba(25, 135, 84, 0.1); border: 1px solid rgba(25, 135, 84, 0.2); color: #0f5132; border-radius: 8px; padding: 10px; margin-top:15px;}
             /* Responsive adjustments */
            @media (max-width: 991px) {
                .calculator-inputs-container, .calculator-results-container { border-left: none !important; border-top: 1px solid rgba(0,0,0,0.1); }
                .profitability-calculator-container { margin: 0 15px; }
            }
        </style>
		<script>
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize Bootstrap tooltips
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                // Input Elements
                const adSpendInput = document.getElementById('currentMonthlyAdSpend');
                const currentCacHighLtvInput = document.getElementById('currentCacHighLtv');
                const highLtvSpendPercentageSlider = document.getElementById('percentageSpendOnHighLtvSlider');
                const highLtvSpendPercentageInput = document.getElementById('percentageSpendOnHighLtv');
                const avgLtvHighLtvInput = document.getElementById('avgLtvHighLtv');
                const avgOrderValueInput = document.getElementById('avgOrderValue');
                const grossMarginPercentageInput = document.getElementById('grossMarginPercentage');
                const industryTypeSelect = document.getElementById('industryType');
                const implementationTimeframeSelect = document.getElementById('implementationTimeframe');
                const expectedCacReductionGoalInput = document.getElementById('expectedCacReductionPercentageGoal');

                // Output Elements
                const optimizedCacHighLtvEl = document.getElementById('optimizedCacHighLtv');
                const currentCacHighLtvDisplayEl = document.getElementById('currentCacHighLtvDisplay');
                const additionalHighLtvCustomersEl = document.getElementById('additionalHighLtvCustomers');
                const potentialMonthlySavingsEl = document.getElementById('potentialMonthlySavings');
                const ltvToCacRatioEl = document.getElementById('ltvToCacRatio');
                const profitFromAdditionalCustomersEl = document.getElementById('profitFromAdditionalCustomers');
                const roiOnOptimizationEl = document.getElementById('roiOnOptimization');
                const industryBenchmarkTextEl = document.getElementById('industryBenchmarkText');
                const calculationDetailsEl = document.getElementById('calculationDetails');
                const resultsNoticeEl = document.getElementById('resultsNotice');
                const successMessageEl = document.getElementById('successMessage');

                // AI Modal Elements
                const aiEnhancementModal = new bootstrap.Modal(document.getElementById('aiEnhancementModal'));
                const aiLoadingIndicator = document.getElementById('aiLoadingIndicator');
                const aiErrorIndicator = document.getElementById('aiErrorIndicator');
                const aiEnhancementContent = document.getElementById('aiEnhancementContent');
                const aiCacReductionEl = document.getElementById('aiCacReduction');
                const aiLtvIncreaseEl = document.getElementById('aiLtvIncrease');
                const aiTimelineEl = document.getElementById('aiTimeline');
                const aiKeyInsightsListEl = document.getElementById('aiKeyInsightsList');
                const applyAIEnhancementsBtn = document.getElementById('applyAIEnhancementsBtn');
                const aiAppliedCacReductionEl = document.getElementById('aiAppliedCacReduction');
                const aiAppliedLtvIncreaseEl = document.getElementById('aiAppliedLtvIncrease');
                const aiEnhancedInsightSectionEl = document.getElementById('aiEnhancedInsightSection');
                const aiKeyInsightsEl = document.getElementById('aiKeyInsights');

                let lastAiSuggestions = null; // To store the latest suggestions from AI

                const industryData = {
                    ecommerce: { name: 'E-commerce', cacReductionPotential: {min: 20, max: 40}, timelineFactors: { 3: 0.5, 6: 0.8, 12: 1.0 }, typicalImplementationCostFactor: 0.15 }, // 15% of 6-month ad spend
                    saas: { name: 'SaaS / Subscription', cacReductionPotential: {min: 25, max: 45}, timelineFactors: { 3: 0.4, 6: 0.75, 12: 1.0 }, typicalImplementationCostFactor: 0.20 },
                    finance: { name: 'Financial Services', cacReductionPotential: {min: 15, max: 35}, timelineFactors: { 3: 0.45, 6: 0.7, 12: 1.0 }, typicalImplementationCostFactor: 0.25 },
                    travel: { name: 'Travel & Hospitality', cacReductionPotential: {min: 18, max: 38}, timelineFactors: { 3: 0.5, 6: 0.8, 12: 1.0 }, typicalImplementationCostFactor: 0.18 },
                    education: { name: 'Education', cacReductionPotential: {min: 15, max: 30}, timelineFactors: { 3: 0.4, 6: 0.7, 12: 1.0 }, typicalImplementationCostFactor: 0.15 },
                    healthcare: { name: 'Healthcare', cacReductionPotential: {min: 12, max: 28}, timelineFactors: { 3: 0.35, 6: 0.65, 12: 1.0 }, typicalImplementationCostFactor: 0.22 },
                    other: { name: 'Other', cacReductionPotential: {min: 10, max: 25}, timelineFactors: { 3: 0.4, 6: 0.7, 12: 1.0 }, typicalImplementationCostFactor: 0.15 }
                };

                function updateIndustryBenchmark() {
                    const selectedIndustry = industryTypeSelect.value;
                    const data = industryData[selectedIndustry];
                    industryBenchmarkTextEl.innerHTML = `<i class="bi bi-info-circle me-1"></i> ${data.name} industry: Typical High-LTV CAC reduction potential is <strong>${data.cacReductionPotential.min}% - ${data.cacReductionPotential.max}%</strong>.`;
                }

                highLtvSpendPercentageSlider.addEventListener('input', function() {
                    highLtvSpendPercentageInput.value = this.value;
                    calculateAndDisplayResults();
                });
                highLtvSpendPercentageInput.addEventListener('input', function() {
                    let val = parseInt(this.value);
                    if (val < 10) val = 10;
                    if (val > 90) val = 90;
                    this.value = val;
                    highLtvSpendPercentageSlider.value = val;
                    calculateAndDisplayResults();
                });

                const allInputs = [adSpendInput, currentCacHighLtvInput, highLtvSpendPercentageInput, avgLtvHighLtvInput, avgOrderValueInput, grossMarginPercentageInput, industryTypeSelect, implementationTimeframeSelect, expectedCacReductionGoalInput];
                allInputs.forEach(input => {
                    input.addEventListener('input', calculateAndDisplayResults);
                    input.addEventListener('change', calculateAndDisplayResults); // For select elements
                });

                function formatCurrency(value) {
                    return value.toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2, maximumFractionDigits: 2 });
                }
                function formatPercentage(value) {
                    return value.toFixed(1) + '%';
                }
                function formatNumber(value, decimals = 0) {
                    return value.toLocaleString('en-US', { minimumFractionDigits: decimals, maximumFractionDigits: decimals });
                }

                function calculateAndDisplayResults(appliedAiSuggestions = null) {
                    // --- 1. Gather Inputs ---
                    const adSpend = parseFloat(adSpendInput.value) || 0;
                    const currentCacHighLtv = parseFloat(currentCacHighLtvInput.value) || 0;
                    const highLtvSpendPerc = parseFloat(highLtvSpendPercentageInput.value) / 100 || 0;
                    let avgLtvHighLtv = parseFloat(avgLtvHighLtvInput.value) || 0; // Will be modified if AI LTV increase is applied
                    const avgOrderValue = parseFloat(avgOrderValueInput.value) || 0;
                    const grossMarginPerc = parseFloat(grossMarginPercentageInput.value) / 100 || 0;
                    const industry = industryTypeSelect.value;
                    const timeframe = parseInt(implementationTimeframeSelect.value);
                    let expectedCacReductionGoal = parseFloat(expectedCacReductionGoalInput.value) / 100 || 0;

                    resultsNoticeEl.style.display = 'none';
                    resultsNoticeEl.textContent = '';

                    if (adSpend <= 0 || currentCacHighLtv <= 0 || highLtvSpendPerc <= 0 || avgLtvHighLtv <=0 || avgOrderValue <=0 || grossMarginPerc <=0 || expectedCacReductionGoal <=0) {
                        resultsNoticeEl.textContent = 'Please fill in all input fields with positive values to see projections.';
                        resultsNoticeEl.style.display = 'block';
                        // Reset outputs to 0 or N/A if inputs are invalid
                        optimizedCacHighLtvEl.textContent = formatCurrency(0);
                        currentCacHighLtvDisplayEl.textContent = formatCurrency(currentCacHighLtv);
                        additionalHighLtvCustomersEl.textContent = '+0';
                        potentialMonthlySavingsEl.textContent = formatCurrency(0);
                        ltvToCacRatioEl.textContent = '0:1';
                        profitFromAdditionalCustomersEl.textContent = formatCurrency(0);
                        roiOnOptimizationEl.textContent = '0%';
                        calculationDetailsEl.innerHTML = '<p>Enter valid inputs to see calculation details.</p>';
                        return;
                    }

                    // Apply AI suggestions if provided
                    if (appliedAiSuggestions) {
                        if (appliedAiSuggestions.potentialCacReductionHighLtv) {
                            expectedCacReductionGoal = appliedAiSuggestions.potentialCacReductionHighLtv / 100;
                            expectedCacReductionGoalInput.value = appliedAiSuggestions.potentialCacReductionHighLtv; // Update input field
                            aiAppliedCacReductionEl.textContent = `${appliedAiSuggestions.potentialCacReductionHighLtv}%`;
                        }
                        if (appliedAiSuggestions.potentialLtvIncreaseHighLtv) {
                            const ltvIncreaseFactor = 1 + (appliedAiSuggestions.potentialLtvIncreaseHighLtv / 100);
                            avgLtvHighLtv = avgLtvHighLtv * ltvIncreaseFactor;
                            // avgLtvHighLtvInput.value = avgLtvHighLtv.toFixed(0); // Optionally update input field, or just show "enhanced LTV"
                            aiAppliedLtvIncreaseEl.textContent = `${appliedAiSuggestions.potentialLtvIncreaseHighLtv}% (New LTV: ${formatCurrency(avgLtvHighLtv)})`;
                        }
                         if (appliedAiSuggestions.recommendedImplementationTimelineMonths) {
                            implementationTimeframeSelect.value = appliedAiSuggestions.recommendedImplementationTimelineMonths;
                        }
                        aiEnhancedInsightSectionEl.style.display = 'block';

                        // Display AI insights in the results section
                        if (appliedAiSuggestions.keyInsights && appliedAiSuggestions.keyInsights.length > 0) {
                            aiKeyInsightsEl.innerHTML = appliedAiSuggestions.keyInsights.map(insight =>
                                `<div class="mb-2 text-white "><i class="bi bi-check-circle-fill text-success me-2 base-color-gradient"></i>${insight}</div>`
                            ).join('');
                        }
                    }

                    // --- 2. Core Calculations ---
                    const industryInfo = industryData[industry];
                    const timeframeAchievementFactor = industryInfo.timelineFactors[timeframe];
                    const achievedCacReductionPerc = expectedCacReductionGoal * timeframeAchievementFactor;
                    const optimizedCac = currentCacHighLtv * (1 - achievedCacReductionPerc);

                    const adSpendOnHighLtv = adSpend * highLtvSpendPerc;
                    const currentHighLtvCustomers = adSpendOnHighLtv / currentCacHighLtv;
                    const newHighLtvCustomers = adSpendOnHighLtv / optimizedCac;
                    const additionalCustomers = newHighLtvCustomers - currentHighLtvCustomers;

                    const savingsPerCustomer = currentCacHighLtv - optimizedCac;
                    const monthlySavings = currentHighLtvCustomers * savingsPerCustomer;

                    const ltvToCac = avgLtvHighLtv / optimizedCac;

                    const profitPerAdditionalCustomerAOV = avgOrderValue * grossMarginPerc;
                    const monthlyProfitFromAdditional = additionalCustomers * profitPerAdditionalCustomerAOV;

                    // ROI on Optimization Effort
                    // Estimate implementation cost: e.g., a factor of total ad spend over the implementation period, or a fixed range based on industry.
                    // Using a factor of the High-LTV ad spend for the timeframe as a proxy for implementation/agency/tool costs.
                    const estimatedImplementationCost = (adSpendOnHighLtv * industryInfo.typicalImplementationCostFactor) * (timeframe / 6) ; // Scale cost by timeframe relative to 6 months
                    const totalLtvFromAdditionalCust = additionalCustomers * avgLtvHighLtv * timeframe; // LTV gain over the timeframe
                    let roiOptimization = 0;
                    if (estimatedImplementationCost > 0) {
                        roiOptimization = ((totalLtvFromAdditionalCust - estimatedImplementationCost) / estimatedImplementationCost) * 100;
                    }

                    // --- 3. Display Results ---
                    optimizedCacHighLtvEl.textContent = formatCurrency(optimizedCac);
                    currentCacHighLtvDisplayEl.textContent = formatCurrency(currentCacHighLtv);
                    additionalHighLtvCustomersEl.textContent = (additionalCustomers > 0 ? '+' : '') + formatNumber(additionalCustomers, 1);
                    potentialMonthlySavingsEl.textContent = formatCurrency(monthlySavings);
                    ltvToCacRatioEl.textContent = formatNumber(ltvToCac, 1) + ':1';
                    profitFromAdditionalCustomersEl.textContent = formatCurrency(monthlyProfitFromAdditional);
                    roiOnOptimizationEl.textContent = formatPercentage(roiOptimization);

                    // --- 4. Update Calculation Details ---
                    updateCalculationDetailsHTML({
                        adSpend, currentCacHighLtv, highLtvSpendPerc, avgLtvHighLtv, avgOrderValue, grossMarginPerc, industry, timeframe, expectedCacReductionGoal,
                        timeframeAchievementFactor, achievedCacReductionPerc, optimizedCac, adSpendOnHighLtv, currentHighLtvCustomers, newHighLtvCustomers, additionalCustomers,
                        savingsPerCustomer, monthlySavings, ltvToCac, profitPerAdditionalCustomerAOV, monthlyProfitFromAdditional, estimatedImplementationCost, totalLtvFromAdditionalCust, roiOptimization
                    });
                }

                function updateCalculationDetailsHTML(data) {
                    calculationDetailsEl.innerHTML = `
                        <h6>Core Inputs & Assumptions:</h6>
                        <ul>
                            <li>Total Monthly Ad Spend: ${formatCurrency(data.adSpend)}</li>
                            <li>Current CAC (High-LTV Segment): ${formatCurrency(data.currentCacHighLtv)}</li>
                            <li>% Spend on High-LTV: ${formatPercentage(data.highLtvSpendPerc * 100)} (Amount: ${formatCurrency(data.adSpendOnHighLtv)})</li>
                            <li>Average LTV (High-LTV Customer): ${formatCurrency(data.avgLtvHighLtv)}</li>
                            <li>Average Order Value (AOV): ${formatCurrency(data.avgOrderValue)}</li>
                            <li>Gross Margin: ${formatPercentage(data.grossMarginPerc * 100)}</li>
                            <li>Target CAC Reduction Goal (High-LTV): ${formatPercentage(data.expectedCacReductionGoal * 100)}</li>
                            <li>Implementation Timeframe: ${data.timeframe} months (Achieving ${formatPercentage(data.timeframeAchievementFactor*100)} of goal)</li>
                        </ul>
                        <h6>Optimization Calculations:</h6>
                        <ul>
                            <li>Achieved CAC Reduction % for High-LTV: ${formatPercentage(data.achievedCacReductionPerc * 100)}</li>
                            <li><strong>Optimized CAC (High-LTV Segment):</strong> ${formatCurrency(data.optimizedCac)}</li>
                            <li>Current High-LTV Customers Acquired: ${formatNumber(data.currentHighLtvCustomers,1)}</li>
                            <li>New High-LTV Customers (same budget): ${formatNumber(data.newHighLtvCustomers,1)}</li>
                            <li><strong>Additional High-LTV Customers:</strong> ${formatNumber(data.additionalCustomers,1)}</li>
                            <li>Savings per High-LTV Customer: ${formatCurrency(data.savingsPerCustomer)}</li>
                            <li><strong>Potential Monthly Savings:</strong> ${formatCurrency(data.monthlySavings)} (if acquiring same # of customers)</li>
                            <li><strong>LTV:CAC Ratio (High-LTV):</strong> ${formatNumber(data.ltvToCac,1)}:1</li>
                        </ul>
                        <h6>Impact Metrics:</h6>
                        <ul>
                            <li>Profit from one AOV (Additional Customer): ${formatCurrency(data.profitPerAdditionalCustomerAOV)}</li>
                            <li><strong>Est. Monthly Profit from Additional Customers:</strong> ${formatCurrency(data.monthlyProfitFromAdditional)}</li>
                            <li>Estimated Optimization Implementation Cost (over ${data.timeframe} months): ${formatCurrency(data.estimatedImplementationCost)}</li>
                            <li>Total LTV from Additional Customers (over ${data.timeframe} months): ${formatCurrency(data.totalLtvFromAdditionalCust)}</li>
                            <li><strong>Long-Term ROI on Optimization Effort:</strong> ${formatPercentage(data.roiOptimization)}</li>
                        </ul>
                        <p class="mt-2"><small>Note: "ROI on Optimization Effort" considers the LTV gained from additional customers generated through optimization, relative to an estimated cost of implementing these optimization strategies. Implementation cost is a simplified estimate.</small></p>
                    `;
                }

                document.getElementById('showCalculationBtn').addEventListener('click', function() {
                    const btnText = document.getElementById('calculationBtnText');
                    if (calculationDetailsEl.style.display === 'none') {
                        calculationDetailsEl.style.display = 'block';
                        btnText.textContent = 'Hide How We Calculate This';
                    } else {
                        calculationDetailsEl.style.display = 'none';
                        btnText.textContent = 'Show How We Calculate This';
                    }
                });

                document.getElementById('resetBtn').addEventListener('click', function() {
                    document.getElementById('cacCalculatorForm').reset();
                    highLtvSpendPercentageSlider.value = 50; // Reset slider specifically
                    updateIndustryBenchmark();
                    calculateAndDisplayResults();
                    aiEnhancedInsightSectionEl.style.display = 'none'; // Hide AI section on reset
                    successMessageEl.style.display = 'none';
                });

                document.getElementById('enhanceWithAIBtn').addEventListener('click', async function() {
                    aiLoadingIndicator.style.display = 'block';
                    aiErrorIndicator.style.display = 'none';
                    aiEnhancementContent.style.display = 'none';
                    applyAIEnhancementsBtn.disabled = true;
                    aiEnhancementModal.show();

                    const inputsForAI = {
                        industry: industryTypeSelect.value,
                        adSpend: parseFloat(adSpendInput.value) || 0,
                        currentCacHighLtv: parseFloat(currentCacHighLtvInput.value) || 0,
                        highLtvSpendPercentage: parseFloat(highLtvSpendPercentageInput.value) || 0,
                        avgLtvHighLtv: parseFloat(avgLtvHighLtvInput.value) || 0,
                        avgOrderValue: parseFloat(avgOrderValueInput.value) || 0,
                        grossMargin: parseFloat(grossMarginPercentageInput.value) || 0
                    };

                    try {
                        lastAiSuggestions = await callGeminiAPI(inputsForAI);
                        if (lastAiSuggestions) {
                            aiCacReductionEl.textContent = lastAiSuggestions.potentialCacReductionHighLtv;
                            aiLtvIncreaseEl.textContent = lastAiSuggestions.potentialLtvIncreaseHighLtv;
                            aiTimelineEl.textContent = lastAiSuggestions.recommendedImplementationTimelineMonths;
                            aiKeyInsightsListEl.innerHTML = lastAiSuggestions.keyInsights.map(insight => `<li><i class="bi bi-check-circle-fill text-success me-2"></i>${insight}</li>`).join('');
                            aiEnhancementContent.style.display = 'block';
                            applyAIEnhancementsBtn.disabled = false;
                        } else {
                            throw new Error("AI did not return valid suggestions.");
                        }
                    } catch (error) {
                        console.error("Error during AI enhancement:", error);
                        aiErrorIndicator.textContent = `Error fetching AI suggestions: ${error.message}. Using fallback.`;
                        aiErrorIndicator.style.display = 'block';
                        // Populate with fallback if error
                        lastAiSuggestions = getFallbackAiPredictions(inputsForAI.industry);
                        aiCacReductionEl.textContent = lastAiSuggestions.potentialCacReductionHighLtv;
                        aiLtvIncreaseEl.textContent = lastAiSuggestions.potentialLtvIncreaseHighLtv;
                        aiTimelineEl.textContent = lastAiSuggestions.recommendedImplementationTimelineMonths;
                        aiKeyInsightsListEl.innerHTML = lastAiSuggestions.keyInsights.map(insight => `<li><i class="bi bi-check-circle-fill text-success me-2"></i>${insight}</li>`).join('');
                        aiEnhancementContent.style.display = 'block';
                        applyAIEnhancementsBtn.disabled = false; // Enable apply even for fallback
                    } finally {
                        aiLoadingIndicator.style.display = 'none';
                    }
                });

                applyAIEnhancementsBtn.addEventListener('click', function() {
                    if (lastAiSuggestions) {
                        calculateAndDisplayResults(lastAiSuggestions); // Pass suggestions to apply them
                        aiEnhancementModal.hide();
                        successMessageEl.textContent = 'AI suggestions have been applied to the calculator inputs!';
                        successMessageEl.style.display = 'block';
                        setTimeout(() => { successMessageEl.style.display = 'none'; }, 4000);
                    }
                });

                const howToUseModal = new bootstrap.Modal(document.getElementById('howToUseModal'));
                document.getElementById('howToUseBtn').addEventListener('click', () => howToUseModal.show());

                // Initial setup
                updateIndustryBenchmark();
                calculateAndDisplayResults(); // Initial calculation on load
            });
        </script>
   <div class="professional-gradient-container">
                <div class="corner-gradient top-left"></div>
                <div class="corner-gradient top-right"></div>
                <div class="corner-gradient bottom-left"></div>
                <div class="corner-gradient bottom-right"></div>
                <div class="diagonal-gradient"></div>
                <div class="vignette-overlay"></div>
            </div>
    <div class="container">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-6 col-lg-7 text-center position-relative page-title-extra-large">
                <div class="d-flex flex-column small-screen">
                   <div class="mt-auto">
                        <h1 class="text-white fw-500 ls-minus-1px mb-0">High-LTV CAC Optimization Calculator.</h1>
                    </div>
                    <div class="mt-auto justify-content-center breadcrumb breadcrumb-style-01 fs-15 text-white">
                        <ul>
                            <li><a href="#" class="text-white">Home</a></li>
                            <li><a href="#" class="text-white">Resources</a></li>
                            <li><span>High-LTV CAC Optimization Calculator</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end page title -->
        <section class="calculator-section overflow-hidden position-relative">
<!-- 		    <div class="position-absolute top-0 left-0 w-100 h-100" style="background: radial-gradient(circle at top right, rgba(222, 52, 127, 0.05) 0%, rgba(31, 33, 42, 0) 70%);"></div>
    <div class="position-absolute bottom-0 left-0 w-100 h-100" style="background: radial-gradient(ellipse at bottom left, rgba(143, 118, 245, 0.05) 0%, rgba(0, 0, 0, 0) 80%);"></div>
    <div class="position-absolute top-0 left-0 w-100 h-100" style="background: linear-gradient(180deg, rgba(240, 240, 247, 0.03) 0%, rgba(240, 240, 247, 0) 100%);"></div> -->
            <div class="container-fluid">
			
			  <div class="row justify-content-center mb-50px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <h2 class="alt-font fw-700 ls-minus-1px mb-20px text-dark-gray">Optimize Your High-LTV Customer Acquisition Cost</h2>
                <p class="mb-0">Paying the same to acquire every customer? See how AdZeta's AI helps you drastically reduce CAC for your most valuable segments and acquire more of them.</p>
                <button class="btn btn-link-gradient btn-extra-large text-gradient-light-pink-light-purple thin d-table d-lg-inline-block xl-mb-15px md-mx-auto mt-20px" id="howToUseBtn">
                    <i class="bi bi-info-circle me-5px"></i>How to use this calculator
                </button>
            </div>
        </div>
			
            
                <div class="profitability-calculator-container">
                    <div class="row g-0">
                        <div class="col-lg-6 calculator-inputs-container">
						<div class="p-35px md-p-25px sm-p-20px">
                            <h3 class="fw-600 fs-5 mb-4 text-dark-gray">Enter Your Business Data</h3>
                            <form id="cacCalculatorForm" class="calculator-form">
                                <div class="row">
                                    <div class="col-md-6 form-group">
                                        <label for="currentMonthlyAdSpend" class="form-label">Current Monthly Ad Spend <i class="bi bi-question-circle tooltip-icon" data-bs-toggle="tooltip" title="Total amount spent on advertising per month."></i></label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" id="currentMonthlyAdSpend" class="form-control" value="10000" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6 form-group">
                                        <label for="currentCacHighLtv" class="form-label">Current CAC (High-LTV Segment) <i class="bi bi-question-circle tooltip-icon" data-bs-toggle="tooltip" title="Your current average Cost to Acquire a Customer specifically within your High-LTV segment."></i></label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" id="currentCacHighLtv" class="form-control" value="50" min="1">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group ">
								<div class="range-slider-container">
                                    <label for="percentageSpendOnHighLtvSlider" class="form-label">% of Ad Spend on High-LTV Customers <i class="bi bi-question-circle tooltip-icon" data-bs-toggle="tooltip" title="Estimate the portion of your ad budget currently targeting your High-LTV customer segments."></i></label>
                                    <div class="d-flex align-items-center">
                                        <input type="range" id="percentageSpendOnHighLtvSlider" class="form-range me-3" min="10" max="90" step="5" value="50">
                                        <input type="number" id="percentageSpendOnHighLtv" class="form-control" style="width: 80px;" value="50" min="10" max="90">
                                        <span class="ms-2">%</span>
                                    </div>
                                </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 form-group">
                                        <label for="avgLtvHighLtv" class="form-label">Average LTV (High-LTV Customer) <i class="bi bi-question-circle tooltip-icon" data-bs-toggle="tooltip" title="The average total Lifetime Value you expect from a single customer in your High-LTV segment."></i></label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" id="avgLtvHighLtv" class="form-control" value="1500" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6 form-group">
                                        <label for="avgOrderValue" class="form-label">Average Order Value (AOV) <i class="bi bi-question-circle tooltip-icon" data-bs-toggle="tooltip" title="The average revenue generated per order/transaction."></i></label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" id="avgOrderValue" class="form-control" value="100" min="0">
                                        </div>
                                    </div>
                                </div>
								
								 <div class="row">
									     <div class="col-md-12 form-group position-relative">
                               
                                    <label for="grossMarginPercentage" class="form-label">Gross Margin % <i class="bi bi-question-circle tooltip-icon" data-bs-toggle="tooltip" title="Your gross profit margin ( (Revenue - COGS) / Revenue ) * 100."></i></label>
                                    <div class="input-group">
                                        <input type="number" id="grossMarginPercentage" class="form-control" value="60" min="0" max="100">
                                        <span class="input-group-text border-start-0">%</span>
                                    </div>
                              
                                </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 form-group">
                                        <label for="industryType" class="form-label">Your Industry</label>
                                        <select id="industryType" class="form-select">
                                            <option value="ecommerce" selected>E-commerce</option>
                                            <option value="saas">SaaS / Subscription</option>
                                            <option value="finance">Financial Services</option>
                                            <option value="travel">Travel & Hospitality</option>
                                            <option value="education">Education</option>
                                            <option value="healthcare">Healthcare</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 form-group">
                                        <label for="implementationTimeframe" class="form-label">Implementation Timeframe</label>
                                        <select id="implementationTimeframe" class="form-select">
                                            <option value="3">3 Months (Initial Impact)</option>
                                            <option value="6" selected>6 Months (Optimized)</option>
                                            <option value="12">12 Months (Full Maturity)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="expectedCacReductionPercentageGoal" class="form-label">Target CAC Reduction % (High-LTV) <i class="bi bi-question-circle tooltip-icon" data-bs-toggle="tooltip" title="Your desired percentage reduction in CAC for your High-LTV segment through optimization efforts."></i></label>
                                    <div class="input-group">
                                        <input type="number" id="expectedCacReductionPercentageGoal" class="form-control" value="30" min="0" max="80">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <div id="industryBenchmarkText" class="industry-benchmark">E-commerce industry benchmark: 20-40% typical CAC reduction potential for High-LTV segments.</div>
                                </div>
                                <div class="text-center mt-4">
                                    <p class="fs-6 text-muted"><i class="bi bi-arrow-repeat"></i> Results update automatically as you change values.</p>
                                </div>
                            </form>
                        </div>
                        </div>

                       <div class="col-xl-6 calculator-results-container" id="resultsSection" style="background: linear-gradient(135deg, rgb(43, 45, 58), rgb(57, 59, 59), rgb(75, 77, 91)); border-left: 1px solid rgba(255, 255, 255, 0.1); display: block;">
                            <div class="p-35px md-p-25px sm-p-20px">
							<h3 class="fw-600 fs-5 mb-4 text-white">Projected Optimization Results</h3>
                            <div id="resultsNotice" class="alert-info-custom mb-3" style="display:none;"></div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="result-card">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="icon-circle me-2"><i class="bi bi-bullseye fs-5 text-white"></i></div>
                                            <h4 class="text-white mb-0">Optimized High-LTV CAC</h4>
                                        </div>
                                        <span id="optimizedCacHighLtv" class="value text-white">$0.00</span>
                                        <p class="fs-14 text-white opacity-75 mt-1">vs. Current <span id="currentCacHighLtvDisplay">$0.00</span></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="result-card">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="icon-circle me-2"><i class="bi bi-people-fill fs-5 text-white"></i></div>
                                            <h4 class="text-white mb-0">Additional High-LTV Customers</h4>
                                        </div>
                                        <span id="additionalHighLtvCustomers" class="value text-white">+0</span>
                                        <p class="fs-14 text-white opacity-75 mt-1">per month (same High-LTV budget)</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="result-card">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="icon-circle me-2"><i class="bi bi-piggy-bank-fill fs-5 text-white"></i></div>
                                            <h4 class="text-white mb-0">Potential Monthly Savings</h4>
                                        </div>
                                        <span id="potentialMonthlySavings" class="value text-white">$0</span>
                                        <p class="fs-14 text-white opacity-75 mt-1">(acquiring same # of High-LTV customers)</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="result-card">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="icon-circle me-2"><i class="bi bi-graph-up-arrow fs-5 text-white"></i></div>
                                            <h4 class="text-white mb-0">LTV:CAC Ratio (High-LTV)</h4>
                                        </div>
                                        <span id="ltvToCacRatio" class="value text-white">0:1</span>
                                        <p class="fs-14 text-white opacity-75 mt-1">Ideal: 3:1 or higher</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="result-card">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="icon-circle me-2"><i class="bi bi-cash-coin fs-5 text-white"></i></div>
                                            <h4 class="text-white mb-0">Est. Monthly Profit (Add. Cust.)</h4>
                                        </div>
                                        <span id="profitFromAdditionalCustomers" class="value text-white">$0</span>
                                        <p class="fs-14 text-white opacity-75 mt-1">from additional High-LTV customers</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="result-card">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="icon-circle me-2"><i class="bi bi-arrow-repeat fs-5 text-white"></i></div>
                                            <h4 class="text-white mb-0">ROI on Optimization</h4>
                                        </div>
                                        <span id="roiOnOptimization" class="value text-white">0%</span>
                                        <p class="fs-14 text-white opacity-75 mt-1">Long-term, based on LTV of add. customers</p>
                                    </div>
                                </div>
                            </div>

                            <div id="aiEnhancedInsightSection" class="mt-3" style="display:none;">
                                <h5 class="text-white fw-500 fs-6"><i class="bi bi-robot me-2 text-gradient-primary"></i>AI-Powered Insights & Recommendations:</h5>
                                <div id="aiKeyInsights" class="p-3 border-radius-8px" style="background: rgba(222, 52, 127, 0.1); backdrop-filter: blur(20px); border: 1px solid rgba(222, 52, 127, 0.15); box-shadow: 0 5px 15px rgba(0,0,0,0.05);">
                                </div>
                                <p class="fs-14 text-white opacity-75 mt-2">AI suggestions applied: CAC Reduction Goal set to <span id="aiAppliedCacReduction" class="fw-bold">N/A</span>. LTV enhanced by <span id="aiAppliedLtvIncrease" class="fw-bold">N/A</span>.</p>
                            </div>
                            <div id="successMessage" class="alert-success-custom" style="display:none;"></div>

                            <div class="mt-4">
                                <button id="enhanceWithAIBtn" class="btn btn-large btn-gradient-pink-orange btn-rounded w-100 mb-2">
                                    <i class="bi bi-stars me-2"></i>Enhance with AI Predictions
                                </button>
                                <button id="showCalculationBtn" class="btn btn-secondary-custom w-100 mb-2 btn btn-large  btn-transparent-white-light btn-rounded border-1 alt-font">
                                    <i class="bi bi-calculator-fill me-2"></i><span id="calculationBtnText">Show How We Calculate This</span>
                                </button>
                                <button id="resetBtn" class="btn btn-secondary-custom w-100 mb-2 btn btn-large  btn-transparent-white-light btn-rounded border-1 alt-font" >
                                    <i class="bi bi-arrow-counterclockwise me-2"></i>Reset All Values
                                </button>
                            </div>

                            <div id="calculationDetails" class="calculation-details" style="display: none;">
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- How to Use Modal -->
        <div class="modal fade" id="howToUseModal" tabindex="-1" aria-labelledby="howToUseModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="howToUseModalLabel">How to Use This Advanced Calculator</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-4">
                        <p>This calculator helps B2C businesses model the potential impact of optimizing Customer Acquisition Cost (CAC) for their High Lifetime Value (LTV) customer segments.</p>
                        <h6>Input Fields Explained:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2"><strong>Current Monthly Ad Spend:</strong> Your total advertising budget per month.</li>
                            <li class="mb-2"><strong>Current CAC (High-LTV Segment):</strong> The average cost to acquire one customer specifically from your identified High-LTV segment. If unsure, you can start with your overall average CAC, but a segment-specific CAC will yield more accurate results.</li>
                            <li class="mb-2"><strong>% of Ad Spend on High-LTV Customers:</strong> The portion of your total ad spend currently dedicated to acquiring these High-LTV customers.</li>
                            <li class="mb-2"><strong>Average LTV (High-LTV Customer):</strong> The total net profit you expect from an average customer in your High-LTV segment over their entire relationship with your business.</li>
                            <li class="mb-2"><strong>Average Order Value (AOV):</strong> The average revenue generated per transaction.</li>
                            <li class="mb-2"><strong>Gross Margin %:</strong> Your profit margin before operating expenses, calculated as ((Revenue - Cost of Goods Sold) / Revenue) * 100.</li>
                            <li class="mb-2"><strong>Your Industry:</strong> Helps tailor benchmarks and AI insights.</li>
                            <li class="mb-2"><strong>Implementation Timeframe:</strong> The period over which you expect to implement and see results from optimization efforts. Longer timeframes generally allow for achieving a greater portion of the potential CAC reduction.</li>
                            <li class="mb-2"><strong>Target CAC Reduction % (High-LTV):</strong> Your goal for reducing CAC within your High-LTV segment. The calculator will show industry benchmarks to help guide this input.</li>
                        </ul>
                        <h6>Output Metrics:</h6>
                        <p>The calculator will project several key performance indicators (KPIs) based on your inputs, including Optimized High-LTV CAC, additional customers you could acquire, potential monthly savings, LTV:CAC ratio, and ROI on your optimization efforts.</p>
                        <h6>AI Enhancement:</h6>
                        <p>Click "Enhance with AI Predictions" to get suggestions from Gemini AI for CAC reduction, LTV increase, and implementation timeline, along with strategic insights. You can then choose to apply these AI-driven figures to the calculator.</p>
                        <p class="mt-3"><small><strong>Note:</strong> This tool provides estimates for planning and strategic discussion. Actual results will vary based on specific business conditions, market dynamics, and execution quality.</small></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Enhancement Modal -->
        <div class="modal fade" id="aiEnhancementModal" tabindex="-1" aria-labelledby="aiEnhancementModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="aiEnhancementModalLabel"><i class="bi bi-robot me-2 text-gradient-primary"></i>AI-Enhanced Predictions</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div id="aiLoadingIndicator" class="text-center py-5" style="display:none;">
                            <div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div>
                            <p class="mt-3">Analyzing your data with Gemini AI... This may take a moment.</p>
                        </div>
                        <div id="aiErrorIndicator" class="alert alert-danger" style="display:none;"></div>
                        <div id="aiEnhancementContent" style="display:none;">
                            <p>Gemini AI has analyzed your inputs and industry. Here are its suggestions:</p>
                            <div class="row mb-3">
                                <div class="col-md-6"><strong>Potential High-LTV CAC Reduction:</strong> <span id="aiCacReduction" class="fw-bold"></span>%</div>
                                <div class="col-md-6"><strong>Potential High-LTV LTV Increase:</strong> <span id="aiLtvIncrease" class="fw-bold"></span>%</div>
                            </div>
                            <div class="mb-3"><strong>Recommended Implementation Timeline:</strong> <span id="aiTimeline" class="fw-bold"></span> months</div>
                            <h6>Key Strategic Insights:</h6>
                            <ul id="aiKeyInsightsList" class="list-unstyled"></ul>
                            <div class="alert alert-info-custom mt-3">
                                <small><i class="bi bi-info-circle-fill me-2"></i>These AI predictions are based on patterns and benchmarks. For optimal results, combine these insights with your deep business knowledge.</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn  btn-small btn-transparent-light-gray btn-rounded" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn  btn-small btn-gradient-pink-orange btn-rounded " id="applyAIEnhancementsBtn" disabled>Apply AI Suggestions</button>
                    </div>
                </div>
            </div>
        </div>

 <?php include 'footer.php'; ?>   