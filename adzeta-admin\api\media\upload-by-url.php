<?php
/**
 * AdZeta Admin Panel - Media Upload by URL API
 * Handles image uploads from external URLs
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Database configuration
$host = 'localhost';
$dbname = 'adzetadb';
$username = 'adzetauser';
$password = 'Crazy1395#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$imageUrl = $input['url'] ?? '';

if (empty($imageUrl)) {
    echo json_encode(['success' => false, 'message' => 'Image URL is required']);
    exit;
}

// Validate URL
if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
    echo json_encode(['success' => false, 'message' => 'Invalid URL format']);
    exit;
}

// Configuration
$uploadDir = '../../uploads/media/';
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$maxFileSize = 10 * 1024 * 1024; // 10MB

// Create upload directory if it doesn't exist
if (!file_exists($uploadDir)) {
    if (!mkdir($uploadDir, 0755, true)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to create upload directory']);
        exit;
    }
}

try {
    // Download image from URL
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'AdZeta Media Library/1.0'
        ]
    ]);
    
    $imageData = file_get_contents($imageUrl, false, $context);
    
    if ($imageData === false) {
        echo json_encode(['success' => false, 'message' => 'Failed to download image from URL']);
        exit;
    }

    // Check file size
    if (strlen($imageData) > $maxFileSize) {
        echo json_encode(['success' => false, 'message' => 'Image too large. Maximum size is 10MB.']);
        exit;
    }

    // Create temporary file to check image type
    $tempFile = tempnam(sys_get_temp_dir(), 'adzeta_image_');
    file_put_contents($tempFile, $imageData);
    
    // Get image info
    $imageInfo = getimagesize($tempFile);
    if ($imageInfo === false) {
        unlink($tempFile);
        echo json_encode(['success' => false, 'message' => 'Invalid image file']);
        exit;
    }

    $mimeType = $imageInfo['mime'];
    $width = $imageInfo[0];
    $height = $imageInfo[1];

    // Validate MIME type
    if (!in_array($mimeType, $allowedTypes)) {
        unlink($tempFile);
        echo json_encode(['success' => false, 'message' => 'Invalid image type. Only JPG, PNG, GIF, and WebP are allowed.']);
        exit;
    }

    // Generate filename
    $urlPath = parse_url($imageUrl, PHP_URL_PATH);
    $originalName = pathinfo($urlPath, PATHINFO_FILENAME) ?: 'image';
    $extension = '';
    
    switch ($mimeType) {
        case 'image/jpeg':
            $extension = 'jpg';
            break;
        case 'image/png':
            $extension = 'png';
            break;
        case 'image/gif':
            $extension = 'gif';
            break;
        case 'image/webp':
            $extension = 'webp';
            break;
    }

    $filename = $originalName . '_' . time() . '_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;

    // Move temp file to final location
    if (!rename($tempFile, $filepath)) {
        unlink($tempFile);
        echo json_encode(['success' => false, 'message' => 'Failed to save image']);
        exit;
    }

    // Generate file URL
    $fileUrl = '/adzeta-admin/uploads/media/' . $filename;
    $fileSize = filesize($filepath);

    // Create media table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS media (
            id INT AUTO_INCREMENT PRIMARY KEY,
            original_filename VARCHAR(255) NOT NULL,
            filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_url VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            width INT DEFAULT 0,
            height INT DEFAULT 0,
            alt_text TEXT,
            caption TEXT,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_filename (filename),
            INDEX idx_uploaded_at (uploaded_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createTableSQL);

    // Insert media record
    $stmt = $pdo->prepare("
        INSERT INTO media (original_filename, filename, file_path, file_url, file_size, mime_type, width, height)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $originalName . '.' . $extension,
        $filename,
        $filepath,
        $fileUrl,
        $fileSize,
        $mimeType,
        $width,
        $height
    ]);

    $mediaId = $pdo->lastInsertId();

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Image uploaded successfully from URL',
        'media' => [
            'id' => $mediaId,
            'original_filename' => $originalName . '.' . $extension,
            'filename' => $filename,
            'file_url' => $fileUrl,
            'file_size' => $fileSize,
            'mime_type' => $mimeType,
            'width' => $width,
            'height' => $height,
            'uploaded_at' => date('Y-m-d H:i:s')
        ]
    ]);

} catch (Exception $e) {
    // Clean up any created files
    if (isset($tempFile) && file_exists($tempFile)) {
        unlink($tempFile);
    }
    if (isset($filepath) && file_exists($filepath)) {
        unlink($filepath);
    }
    
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
