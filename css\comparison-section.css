/* Comparison Section Styling */

.comparison-section {
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.comparison-section .header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.comparison-section .header h2 {
    font-family: var(--alt-font);
    font-weight: 700;
    font-size: 42px;
    line-height: 1.2;
    color: var(--dark-gray);
    margin-bottom: 20px;
    letter-spacing: -0.02em;
}

.comparison-section .header h2 span {
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.comparison-section .header p {
    font-family: var(--primary-font);
    font-size: 18px;
    line-height: 1.6;
    color: var(--medium-gray);
    max-width: 700px;
    margin: 0 auto;
}

/* Visual Comparison Container */
.visual-comparison {
    position: relative;
    margin: 40px 0 60px;
}

.comparison-content {
    display: flex;
    position: relative;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    border-radius: 20px;
    overflow: hidden;
}

/* Side Styling */
.traditional, .adzeta {
    flex: 1;
    padding: 40px 30px;
    position: relative;
    z-index: 2;
    transition: transform 0.4s ease, box-shadow 0.4s ease;
}

.traditional {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
}

.adzeta {
    background: linear-gradient(135deg, #f9f5ff, #fff5f9);
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
}

.traditional:hover, .adzeta:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 3;
}

/* Side Title */
.side-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.side-title i {
    font-size: 24px;
    margin-right: 15px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.traditional .side-title i {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.adzeta .side-title i {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.side-title h3 {
    font-family: var(--alt-font);
    font-weight: 700;
    font-size: 24px;
    color: var(--dark-gray);
    margin: 0;
}

/* Benefit Cards */
.benefit-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.benefit-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    opacity: 0.7;
}

.traditional .benefit-card::before {
    background: #ff5a5a;
}

.adzeta .benefit-card::before {
    background: linear-gradient(to bottom, #e958a1, #8f76f5);
}

.benefit-card:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.benefit-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    margin-right: 15px;
    flex-shrink: 0;
}

.traditional .benefit-icon {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.adzeta .benefit-icon {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.benefit-icon i {
    font-size: 20px;
}

.benefit-text h4 {
    font-family: var(--alt-font);
    font-weight: 600;
    font-size: 18px;
    color: var(--dark-gray);
    margin: 0 0 5px;
}

.benefit-text p {
    font-family: var(--primary-font);
    font-size: 14px;
    color: var(--medium-gray);
    margin: 0;
}

/* Divider and VS Badge */
.divider {
    position: absolute;
    top: 10%;
    bottom: 10%;
    left: 50%;
    width: 1px;
    background: linear-gradient(to bottom, transparent, rgba(233, 88, 161, 0.3), transparent);
    z-index: 1;
}

.vs-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-family: var(--alt-font);
    font-weight: 700;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
}

/* Testimonial Section */
.testimonial {
    margin: 60px 0;
    position: relative;
}

.testimonial-content {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f9f5ff, #fff5f9);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.testimonial-content::before {
    content: '"';
    position: absolute;
    top: 20px;
    left: 30px;
    font-size: 120px;
    font-family: Georgia, serif;
    color: rgba(233, 88, 161, 0.1);
    line-height: 1;
}

.testimonial-quote {
    flex: 2;
    padding-right: 40px;
}

.testimonial-quote p {
    font-family: var(--primary-font);
    font-size: 18px;
    line-height: 1.6;
    color: var(--dark-gray);
    font-style: italic;
    margin-bottom: 20px;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-image {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
}

.author-info h5 {
    font-family: var(--alt-font);
    font-weight: 600;
    font-size: 16px;
    color: var(--dark-gray);
    margin: 0 0 5px;
}

.author-info p {
    font-family: var(--primary-font);
    font-size: 14px;
    color: var(--medium-gray);
    margin: 0;
}

.testimonial-result {
    flex: 1;
    text-align: center;
    padding-left: 40px;
    border-left: 1px solid rgba(233, 88, 161, 0.2);
}

.result-value {
    font-family: var(--alt-font);
    font-weight: 700;
    font-size: 60px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    line-height: 1;
    margin-bottom: 10px;
}

.result-label {
    font-family: var(--primary-font);
    font-size: 16px;
    color: var(--medium-gray);
}

/* Results Section */
.results {
    text-align: center;
    padding: 40px 0;
    position: relative;
}

.results h3 {
    font-family: var(--alt-font);
    font-weight: 700;
    font-size: 32px;
    color: var(--dark-gray);
    margin-bottom: 40px;
}

.stats-container {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
}

.stat {
    flex: 1;
    max-width: 250px;
    padding: 30px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.stat:hover {
    transform: translateY(-10px);
}

.stat-value {
    font-family: var(--alt-font);
    font-weight: 700;
    font-size: 48px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    line-height: 1;
    margin-bottom: 10px;
}

.stat-label {
    font-family: var(--primary-font);
    font-size: 16px;
    color: var(--medium-gray);
}

.action {
    margin-top: 40px;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    padding: 15px 30px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-family: var(--alt-font);
    font-weight: 600;
    font-size: 16px;
    border-radius: 30px;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
}

.action-btn i {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.4);
    color: white;
}

.action-btn:hover i {
    transform: translateX(5px);
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.active {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-left.active {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-right.active {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scale-in.active {
    opacity: 1;
    transform: scale(1);
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .comparison-section .header h2 {
        font-size: 36px;
    }
    
    .comparison-content {
        flex-direction: column;
    }
    
    .traditional, .adzeta {
        border-radius: 20px;
        margin-bottom: 20px;
    }
    
    .divider {
        display: none;
    }
    
    .vs-badge {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.8);
    }
    
    .testimonial-content {
        flex-direction: column;
    }
    
    .testimonial-quote {
        padding-right: 0;
        margin-bottom: 30px;
    }
    
    .testimonial-result {
        padding-left: 0;
        border-left: none;
        border-top: 1px solid rgba(233, 88, 161, 0.2);
        padding-top: 30px;
    }
    
    .stats-container {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }
    
    .stat {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 767px) {
    .comparison-section .header h2 {
        font-size: 30px;
    }
    
    .comparison-section .header p {
        font-size: 16px;
    }
    
    .side-title h3 {
        font-size: 20px;
    }
    
    .benefit-card {
        padding: 15px;
    }
    
    .benefit-icon {
        width: 40px;
        height: 40px;
    }
    
    .benefit-text h4 {
        font-size: 16px;
    }
    
    .testimonial-content {
        padding: 30px 20px;
    }
    
    .testimonial-quote p {
        font-size: 16px;
    }
    
    .result-value {
        font-size: 48px;
    }
    
    .results h3 {
        font-size: 26px;
    }
    
    .stat-value {
        font-size: 36px;
    }
}

/* Particle Effects */
.particles-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.3;
}

.particle-1 {
    top: 10%;
    left: 5%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(233, 88, 161, 0.2) 0%, rgba(233, 88, 161, 0) 70%);
    animation: float 15s infinite ease-in-out;
}

.particle-2 {
    bottom: 20%;
    right: 10%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(143, 118, 245, 0.2) 0%, rgba(143, 118, 245, 0) 70%);
    animation: float 18s infinite ease-in-out reverse;
}

.particle-3 {
    top: 40%;
    right: 5%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 106, 141, 0.2) 0%, rgba(255, 106, 141, 0) 70%);
    animation: float 12s infinite ease-in-out;
}

@keyframes float {
    0% {
        transform: translate(0, 0);
    }
    50% {
        transform: translate(20px, 20px);
    }
    100% {
        transform: translate(0, 0);
    }
}
