/* Enhanced Case Studies Styling */
.case-study-item {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    height: 100%;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
}

.case-study-item:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

/* Make the entire case study item clickable */
.grid-item a.case-study-full-link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.case-study-image {
    position: relative;
    overflow: hidden;
    border-radius: 16px 16px 0 0;
}

.case-study-image img {
    width: 100%;
    height: 260px;
    object-fit: cover;
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: brightness(0.95);
}

.case-study-item:hover .case-study-image img {
    transform: scale(1.05);
    filter: brightness(1.05);
}

.case-study-category {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    color: white;
    padding: 8px 16px;
    border-radius: 30px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    z-index: 2;
    box-shadow: 0 4px 12px rgba(233, 88, 161, 0.3);
}

.case-study-content {
    padding: 30px;
    position: relative;
}

.case-study-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2b2d3a;
    line-height: 1.4;
    letter-spacing: -0.5px;
}

.case-study-description {
    font-size: 15px;
    line-height: 1.6;
    color: #6c6d80;
    margin-bottom: 24px;
}

.case-study-metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    padding-top: 20px;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.metric {
    text-align: center;
    transition: transform 0.3s ease;
}

.metric:hover {
    transform: translateY(-5px);
}

.metric-value {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.3s ease;
}

.metric-label {
    font-size: 12px;
    color: #6c6d80;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Make the entire case study item clickable */
.grid-item a.case-study-full-link {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

.case-study-logo {
    position: absolute;
    bottom: 30px;
    right: 30px;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.case-study-logo:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.case-study-logo img {
    width: 30px;
    height: auto;
}

/* Enhanced filter navigation */
.portfolio-filter.nav-tabs {
    border-bottom: none;
    margin-bottom: 40px;
    width: 100%;
    display: flex;
    justify-content: center;
}

.portfolio-filter li {
    padding: 0 15px; /* Override default padding */
}

.portfolio-filter.nav-tabs .nav a {
    position: relative;
    color: #6c6d80;
    font-size: 15px;
    font-weight: 500;
    padding: 10px 15px;
    transition: all 0.3s ease;
    background: transparent;
    border-bottom: none !important; /* Override default border */
    white-space: nowrap;
}

.portfolio-filter.nav-tabs .nav a i {
    margin-right: 10px; /* Increase space between icon and text */
}

.portfolio-filter.nav-tabs .nav a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    z-index: 1; /* Ensure our custom underline is on top */
}

.portfolio-filter.nav-tabs .nav a:hover {
    color: #e958a1;
    border-bottom: none !important; /* Override default border on hover */
}

.portfolio-filter.nav-tabs .nav a:hover::after {
    width: 40%;
}

.portfolio-filter.nav-tabs .nav.active a {
    color: #e958a1;
    border-bottom: none !important; /* Override default border on active state */
}

.portfolio-filter.nav-tabs .nav.active a::after {
    width: 80%;
}

/* Page title enhancements */
.case-studies-header {
    position: relative;
}

.case-studies-header h1 {
    font-weight: 700;
    letter-spacing: -2px;
}

.case-studies-header p {
    max-width: 700px;
    margin: 0 auto;
}

/* Animation for down arrow */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.animate-bounce {
    animation: bounce 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .case-study-image img {
        height: 220px;
    }

    .case-study-content {
        padding: 25px;
    }

    .case-study-title {
        font-size: 20px;
    }

    .metric-value {
        font-size: 24px;
    }

    .case-study-logo {
        width: 44px;
        height: 44px;
        bottom: 25px;
        right: 25px;
    }
}

@media (max-width: 991px) {
    .portfolio-filter.nav-tabs {
        width: 100%;
        justify-content: flex-start;
        overflow-x: auto;
        flex-wrap: nowrap;
        padding-bottom: 10px;
    }
}

@media (max-width: 767px) {
    .case-study-metrics {
        flex-wrap: wrap;
    }

    .metric {
        width: 50%;
        margin-bottom: 15px;
    }

    .portfolio-filter.nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 10px;
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        scrollbar-width: none; /* Hide scrollbar for Firefox */
    }

    .portfolio-filter.nav-tabs::-webkit-scrollbar {
        display: none; /* Hide scrollbar for Chrome/Safari */
    }

    .portfolio-filter.nav-tabs .nav a {
        white-space: nowrap;
        padding: 8px 16px;
        font-size: 14px;
    }
}
