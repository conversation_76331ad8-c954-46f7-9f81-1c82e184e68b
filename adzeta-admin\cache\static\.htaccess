# WordPress-Inspired Static Cache Serving Rules
# Serves cached HTML files directly for blazing fast performance

# Enable rewrite engine
RewriteEngine On

# Security: Prevent direct access to cache files from web
# Only allow internal serving
<Files "*.html">
    Order Allow,Deny
    Allow from all
</Files>

# Gzip compression for cached files
<IfModule mod_deflate.c>
    <FilesMatch "\.(html|htm)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>

# Cache headers for static HTML files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/html "access plus 4 hours"
</IfModule>

<IfModule mod_headers.c>
    # Add cache headers
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=14400"
        Header set X-Cache-Type "Static-HTML"
        Header set X-Served-By "AdZeta-Cache"
    </FilesMatch>
    
    # Add gzip headers for .gz files
    <FilesMatch "\.html\.gz$">
        Header set Content-Encoding "gzip"
        Header set Content-Type "text/html; charset=UTF-8"
        Header set X-Cache-Type "Static-HTML-Gzip"
    </FilesMatch>
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
