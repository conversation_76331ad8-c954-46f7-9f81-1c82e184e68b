# AdZeta Website - URL Rewriting and Security

# Enable URL Rewriting
RewriteEngine On

# WordPress-Inspired Frontend Router
# Clean, flexible approach without hard-coded cache paths

# Route all blog URLs through the frontend router
RewriteCond %{REQUEST_URI} ^/blog
RewriteRule ^blog(.*)$ frontend-router.php [QSA,L]

# Optional: Route other content types through router
# RewriteCond %{REQUEST_URI} ^/case-studies
# RewriteRule ^case-studies(.*)$ frontend-router.php [QSA,L]

# RewriteCond %{REQUEST_URI} ^/whitepapers
# RewriteRule ^whitepapers(.*)$ frontend-router.php [QSA,L]

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN

    # Prevent MIME type sniffing
    Header set X-Content-Type-Options nosniff

    # Enable XSS protection
    Header set X-XSS-Protection "1; mode=block"

    # Referrer Policy
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|sql|md|json|lock)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to config and includes directories
RedirectMatch 404 ^/(config|includes|database|templates)/.*$

# Prevent access to hidden files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# Blog URL Rewriting Rules
# Skip rewriting for existing files and directories
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Skip rewriting for admin panel and assets
RewriteCond %{REQUEST_URI} !^/adzeta-admin/
RewriteCond %{REQUEST_URI} !^/assets/
RewriteCond %{REQUEST_URI} !^/images/

# Blog URL Routes
RewriteRule ^blog/?$ blog-list-dynamic.php [L]
RewriteRule ^blog/([^/]+)/?$ blog-post.php?slug=$1 [L]
RewriteRule ^blog/category/([^/]+)/?$ blog-category.php?slug=$1 [L]
RewriteRule ^blog/tag/([^/]+)/?$ blog-tag.php?slug=$1 [L]

# General routing for other pages (if needed later)
# RewriteRule ^(.*)$ bootstrap.php [QSA,L]

# Force HTTPS (uncomment for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slashes (except for root)
RewriteCond %{REQUEST_URI} !^/$
RewriteRule ^(.*)/$ /$1 [R=301,L]
