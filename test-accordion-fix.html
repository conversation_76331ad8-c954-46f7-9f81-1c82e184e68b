<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accordion Safari Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .accordion-header {
            background: #007bff;
            color: white;
            padding: 15px;
            cursor: pointer;
            border-radius: 5px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .accordion-header.active {
            background: #0056b3;
        }
        .accordion-panel {
            display: none;
            background: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
        }
        .spacer {
            height: 200px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="debug-info" id="debugInfo">
        <div>Browser: <span id="browserInfo"></span></div>
        <div>Device: <span id="deviceInfo"></span></div>
        <div>Viewport: <span id="viewportInfo"></span></div>
        <div>Scroll Fix: <span id="scrollFixInfo"></span></div>
    </div>

    <div class="container">
        <h1>Accordion Safari Fix Test</h1>
        
        <div class="test-info">
            <h4>Test Instructions:</h4>
            <ul>
                <li><strong>Desktop:</strong> Click accordion headers - page should NOT scroll upward</li>
                <li><strong>Mobile Safari:</strong> Click accordion headers - page SHOULD scroll to show content</li>
                <li><strong>Other Mobile:</strong> Click accordion headers - page should NOT scroll upward</li>
            </ul>
            <p>Check the debug info in the top-right corner to see what browser/device is detected.</p>
        </div>

        <div class="spacer">Spacer Content - Scroll down to test accordions</div>

        <!-- Test Accordion 1 -->
        <div class="accordion-header" data-target="test-panel-1">
            <span>Test Accordion 1 - Data Processing</span>
            <span>▼</span>
        </div>
        <div class="accordion-panel" id="test-panel-1-accordion">
            <h5>Data Processing Panel</h5>
            <p>This is the content for the first test accordion. On mobile Safari, the page should scroll to show this content properly. On desktop browsers, there should be no automatic scrolling.</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </div>

        <!-- Test Accordion 2 -->
        <div class="accordion-header" data-target="test-panel-2">
            <span>Test Accordion 2 - AI Modeling</span>
            <span>▼</span>
        </div>
        <div class="accordion-panel" id="test-panel-2-accordion">
            <h5>AI Modeling Panel</h5>
            <p>This is the content for the second test accordion. The scroll behavior should be consistent with the first accordion.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        </div>

        <!-- Test Accordion 3 -->
        <div class="accordion-header" data-target="test-panel-3">
            <span>Test Accordion 3 - Predictions</span>
            <span>▼</span>
        </div>
        <div class="accordion-panel" id="test-panel-3-accordion">
            <h5>Predictions Panel</h5>
            <p>This is the content for the third test accordion. Test the scroll behavior here as well.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </div>

        <div class="spacer">More content below custom accordions</div>

        <!-- Bootstrap Accordions Test -->
        <h2>Bootstrap Accordions Test</h2>
        <div class="accordion" id="accordionExample">
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                        Bootstrap Accordion Item #1
                    </button>
                </h2>
                <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <strong>This is the first Bootstrap accordion item's body.</strong> This should NOT cause scrolling on desktop browsers.
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                        Bootstrap Accordion Item #2
                    </button>
                </h2>
                <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <strong>This is the second Bootstrap accordion item's body.</strong> This should also NOT cause scrolling on desktop browsers.
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                        Bootstrap Accordion Item #3
                    </button>
                </h2>
                <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                    <div class="accordion-body">
                        <strong>This is the third Bootstrap accordion item's body.</strong> Test scrolling behavior here too.
                    </div>
                </div>
            </div>
        </div>

        <div class="spacer">Even more content to test scrolling</div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script src="js/platform.js"></script>
    <script>
        // Update debug info
        function updateDebugInfo() {
            const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
            const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isMobileViewport = window.innerWidth <= 768;
            const isMobileSafari = isIOSSafari && isMobileViewport;

            document.getElementById('browserInfo').textContent = navigator.userAgent.includes('Safari') ? 'Safari' : 'Other';
            document.getElementById('deviceInfo').textContent = isMobileDevice ? 'Mobile' : 'Desktop';
            document.getElementById('viewportInfo').textContent = window.innerWidth + 'x' + window.innerHeight;
            document.getElementById('scrollFixInfo').textContent = isMobileSafari ? 'ENABLED' : 'DISABLED';
        }

        // Update debug info on load and resize
        updateDebugInfo();
        window.addEventListener('resize', updateDebugInfo);

        // Log scroll events for testing
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                console.log('Scroll position:', window.pageYOffset);
            }, 100);
        });
    </script>
</body>
</html>
