-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 30, 2025 at 02:13 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `adzetadb`
--

-- --------------------------------------------------------

--
-- Table structure for table `case_studies`
--

CREATE TABLE `case_studies` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `client_name` varchar(255) NOT NULL,
  `industry` varchar(100) DEFAULT NULL,
  `hero_title` longtext DEFAULT NULL,
  `hero_subtitle` text DEFAULT NULL,
  `hero_description` longtext DEFAULT NULL,
  `hero_image` varchar(500) DEFAULT NULL,
  `hero_badge_text` varchar(255) DEFAULT NULL,
  `highlight_1_title` varchar(255) DEFAULT NULL,
  `highlight_1_content` longtext DEFAULT NULL,
  `highlight_1_icon` varchar(100) DEFAULT 'bi-heart-pulse',
  `highlight_2_title` varchar(255) DEFAULT NULL,
  `highlight_2_content` longtext DEFAULT NULL,
  `highlight_2_icon` varchar(100) DEFAULT 'bi-question-circle',
  `highlight_3_title` varchar(255) DEFAULT NULL,
  `highlight_3_content` longtext DEFAULT NULL,
  `highlight_3_icon` varchar(100) DEFAULT 'bi-compass',
  `highlight_4_title` varchar(255) DEFAULT NULL,
  `highlight_4_content` longtext DEFAULT NULL,
  `highlight_4_icon` varchar(100) DEFAULT 'bi-cpu',
  `highlight_5_title` varchar(255) DEFAULT NULL,
  `highlight_5_content` longtext DEFAULT NULL,
  `highlight_5_icon` varchar(100) DEFAULT 'bi-gear-wide-connected',
  `highlight_6_title` varchar(255) DEFAULT NULL,
  `highlight_6_content` longtext DEFAULT NULL,
  `highlight_6_icon` varchar(100) DEFAULT 'bi-trophy',
  `challenge_title` varchar(255) DEFAULT NULL,
  `challenge_subtitle` varchar(255) DEFAULT NULL,
  `challenge_description` longtext DEFAULT NULL,
  `solution_title` varchar(255) DEFAULT NULL,
  `solution_subtitle` varchar(255) DEFAULT NULL,
  `solution_description` longtext DEFAULT NULL,
  `solution_point_1_title` varchar(255) DEFAULT NULL,
  `solution_point_1_description` longtext DEFAULT NULL,
  `solution_point_2_title` varchar(255) DEFAULT NULL,
  `solution_point_2_description` longtext DEFAULT NULL,
  `solution_point_3_title` varchar(255) DEFAULT NULL,
  `solution_point_3_description` longtext DEFAULT NULL,
  `solution_point_4_title` varchar(255) DEFAULT NULL,
  `solution_point_4_description` longtext DEFAULT NULL,
  `results_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`results_data`)),
  `results_title` varchar(255) DEFAULT NULL,
  `results_subtitle` varchar(255) DEFAULT NULL,
  `results_description` longtext DEFAULT NULL,
  `methodology_title` varchar(255) DEFAULT NULL,
  `methodology_description` longtext DEFAULT NULL,
  `outcomes_title` varchar(255) DEFAULT NULL,
  `outcomes_description` longtext DEFAULT NULL,
  `testimonial_quote` longtext DEFAULT NULL,
  `testimonial_author_name` varchar(255) DEFAULT NULL,
  `testimonial_author_title` varchar(255) DEFAULT NULL,
  `testimonial_author_company` varchar(255) DEFAULT NULL,
  `cta_title` varchar(255) DEFAULT NULL,
  `cta_description` longtext DEFAULT NULL,
  `cta_button_text` varchar(100) DEFAULT NULL,
  `cta_button_url` varchar(500) DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `focus_keyword` varchar(100) DEFAULT NULL,
  `canonical_url` varchar(500) DEFAULT NULL,
  `og_title` varchar(255) DEFAULT NULL,
  `og_description` text DEFAULT NULL,
  `og_image` varchar(500) DEFAULT NULL,
  `twitter_card_type` enum('summary','summary_large_image','app','player') DEFAULT 'summary_large_image',
  `excerpt` text DEFAULT NULL,
  `featured_image` varchar(500) DEFAULT NULL,
  `author_id` int(11) DEFAULT NULL,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `visibility` enum('public','private','password') DEFAULT 'public',
  `password` varchar(255) DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `template` varchar(100) DEFAULT 'luminous-skin-clinic',
  `custom_css` longtext DEFAULT NULL,
  `view_count` int(11) DEFAULT 0,
  `reading_time` int(11) DEFAULT 0,
  `word_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `case_studies`
--

INSERT INTO `case_studies` (`id`, `title`, `slug`, `client_name`, `industry`, `hero_title`, `hero_subtitle`, `hero_description`, `hero_image`, `hero_badge_text`, `highlight_1_title`, `highlight_1_content`, `highlight_1_icon`, `highlight_2_title`, `highlight_2_content`, `highlight_2_icon`, `highlight_3_title`, `highlight_3_content`, `highlight_3_icon`, `highlight_4_title`, `highlight_4_content`, `highlight_4_icon`, `highlight_5_title`, `highlight_5_content`, `highlight_5_icon`, `highlight_6_title`, `highlight_6_content`, `highlight_6_icon`, `challenge_title`, `challenge_subtitle`, `challenge_description`, `solution_title`, `solution_subtitle`, `solution_description`, `solution_point_1_title`, `solution_point_1_description`, `solution_point_2_title`, `solution_point_2_description`, `solution_point_3_title`, `solution_point_3_description`, `solution_point_4_title`, `solution_point_4_description`, `results_data`, `results_title`, `results_subtitle`, `results_description`, `methodology_title`, `methodology_description`, `outcomes_title`, `outcomes_description`, `testimonial_quote`, `testimonial_author_name`, `testimonial_author_title`, `testimonial_author_company`, `cta_title`, `cta_description`, `cta_button_text`, `cta_button_url`, `meta_title`, `meta_description`, `meta_keywords`, `focus_keyword`, `canonical_url`, `og_title`, `og_description`, `og_image`, `twitter_card_type`, `excerpt`, `featured_image`, `author_id`, `status`, `visibility`, `password`, `published_at`, `template`, `custom_css`, `view_count`, `reading_time`, `word_count`, `created_at`, `updated_at`) VALUES
(5, 'Lulla Skin Care', 'lulla-skin-care', 'Lulla Skin Care fudu', 'healthcare', 'Lulla Skin Care Achieves 300% ROI Increase with Precision Targeting', '', 'Lulla Skin Care, a leading provider of advanced skincare solutions, partnered with us to overcome inefficient ad spend. By implementing a precision LTV targeting strategy, we dramatically improved their ROI, reduced costs, and enhanced customer satisfaction.', '', 'AI-DRIVEN CLIENT ACQUISITION', 'Client Profile', 'Lulla Skin Care fudu is a prominent healthcare company specializing in advanced skincare solutions. They offer a range of products designed to address various skin concerns and are committed to providing high-quality, effective treatments to their customers. Their business model relies on both direct-to-consumer sales and partnerships with medical professionals.', 'bi-heart-pulse', 'The Core Challenge', 'Lulla Skin Care was facing significant challenges with their digital advertising campaigns. Their ad spend was inefficient, resulting in a low return on investment. They struggled to identify and target high-value customers effectively, leading to wasted resources and missed opportunities for growth.', 'bi-question-circle', 'Previous Approach', 'Previously, Lulla Skin Care relied on broad demographic targeting and generic messaging in their advertising campaigns. They attempted to optimize campaigns based on initial conversion rates, but lacked the data and tools to accurately predict customer lifetime value. This resulted in acquiring customers with low long-term value, negatively impacting overall profitability.', 'bi-compass', 'Our Solution', 'We implemented a precision LTV targeting strategy designed to identify and acquire high-value customers for Lulla Skin Care. This involved leveraging advanced data analytics, predictive modeling, and personalized messaging to optimize ad campaigns and maximize return on investment. Our solution focused on understanding the long-term value of each customer segment and tailoring acquisition efforts accordingly.', 'bi-cpu', 'Methodology', 'Our methodology involved several key steps: 1) Data Analysis: We analyzed Lulla Skin Care\'s existing customer data to identify key attributes and behaviors associated with high-value customers. 2) Predictive Modeling: We developed a predictive model to estimate customer lifetime value (LTV) based on various factors. 3) Audience Segmentation: We segmented the target audience based on LTV predictions. 4) Personalized Messaging: We created personalized ad copy and offers tailored to each segment. 5) A/B Testing: We conducted A/B tests to optimize ad performance and refine targeting strategies. 6) Continuous Monitoring: We continuously monitored campaign performance and made adjustments as needed to maximize ROI.', 'bi-gear-wide-connected', 'Key Outcomes', 'The implementation of our precision LTV targeting strategy yielded significant results for Lulla Skin Care. They experienced a 300% increase in ROI, a 45% reduction in customer acquisition costs, and a 12.5% improvement in conversion rates. Furthermore, customer satisfaction scores increased by 85%, demonstrating the effectiveness of our targeted messaging and offers.', 'bi-trophy', 'The Challenge: Inefficient Ad Spend and Low ROI', 'Understanding the Problem', 'Lulla Skin Care faced a common problem in the healthcare industry: inefficient ad spend. Their existing digital marketing campaigns were not delivering the desired return on investment. They were struggling to identify and target the right customers, leading to wasted resources and missed opportunities for growth.\n\nThe core issue was a lack of understanding of customer lifetime value (LTV). Lulla Skin Care was acquiring customers without knowing their long-term potential, resulting in a mix of high-value and low-value customers. This made it difficult to optimize ad campaigns and allocate resources effectively.\n\nFurthermore, their reliance on broad demographic targeting and generic messaging failed to resonate with specific customer segments. They needed a more targeted and personalized approach to attract and retain high-value customers.', 'Our Solution: Precision LTV Targeting', NULL, 'Our solution was to implement a precision LTV targeting strategy that focused on acquiring and retaining high-value customers for Lulla Skin Care. This involved leveraging advanced data analytics, predictive modeling, and personalized messaging to optimize ad campaigns and maximize return on investment.\n\nWe began by analyzing Lulla Skin Care\'s existing customer data to identify key attributes and behaviors associated with high-value customers. This data was then used to develop a predictive model that could estimate customer lifetime value (LTV) based on various factors. This allowed us to segment the target audience based on their predicted LTV and tailor our acquisition efforts accordingly.\n\nFinally, we created personalized ad copy and offers tailored to each segment, ensuring that our messaging resonated with their specific needs and interests. This resulted in a significant improvement in conversion rates and a dramatic increase in ROI.', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{\"metric_1_value\":\"300%\",\"metric_1_title\":\"ROI Increase\",\"metric_1_description\":\"Return on investment improvement\",\"metric_2_value\":\"45%\",\"metric_2_title\":\"Cost Reduction\",\"metric_2_description\":\"Operational cost savings in customer acquisition\",\"metric_3_value\":\"12.5%\",\"metric_3_title\":\"Conversion Rate\",\"metric_3_description\":\"Improved conversion performance on targeted ad campaigns\",\"metric_4_value\":\"85%\",\"metric_4_title\":\"Client Satisfaction\",\"metric_4_description\":\"Customer satisfaction score based on post-implementation survey\",\"title_metric\":\"Results & Impact\",\"subtitle_metric\":\"Measurable Outcomes\",\"description_metric\":\"The implementation of our precision LTV targeting strategy yielded significant and measurable results for Lulla Skin Care. By focusing on acquiring high-value customers, we were able to dramatically improve their return on investment and reduce their customer acquisition costs.\\n\\nThe increased conversion rates and higher customer satisfaction scores demonstrate the effectiveness of our targeted messaging and offers. These results highlight the power of data-driven marketing and the importance of focusing on customer lifetime value.\\n\\nUltimately, our partnership with Lulla Skin Care resulted in significant growth, improved profitability, and a stronger brand reputation.\",\"metric_1_value_metric\":\"300%\",\"metric_1_title_metric\":\"ROI Increase\",\"metric_1_description_metric\":\"Return on investment improvement\",\"metric_2_value_metric\":\"45%\",\"metric_2_title_metric\":\"Cost Reduction\",\"metric_2_description_metric\":\"Operational cost savings in customer acquisition\",\"metric_3_value_metric\":\"12.5%\",\"metric_3_title_metric\":\"Conversion Rate\",\"metric_3_description_metric\":\"Improved conversion performance on targeted ad campaigns\",\"metric_4_value_metric\":\"85%\",\"metric_4_title_metric\":\"Client Satisfaction\",\"metric_4_description_metric\":\"Customer satisfaction score based on post-implementation survey\"}', NULL, NULL, NULL, '', NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Lulla Skin Care: 300% ROI with Precision Targeting', 'Learn how Lulla Skin Care achieved a 300% ROI increase and 45% cost reduction using our precision LTV targeting strategy. Contact us today!', '', 'precision LTV targeting', '', '', '', NULL, 'summary_large_image', '', '', 1, 'published', 'public', NULL, '2025-06-30 08:40:31', 'luminous-skin-clinic', NULL, 5, 0, 0, '2025-06-30 11:39:08', '2025-06-30 12:10:43');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `case_studies`
--
ALTER TABLE `case_studies`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_slug` (`slug`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_industry` (`industry`),
  ADD KEY `idx_client` (`client_name`),
  ADD KEY `idx_published` (`published_at`),
  ADD KEY `idx_author` (`author_id`),
  ADD KEY `idx_template` (`template`),
  ADD KEY `idx_visibility` (`visibility`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `case_studies`
--
ALTER TABLE `case_studies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
