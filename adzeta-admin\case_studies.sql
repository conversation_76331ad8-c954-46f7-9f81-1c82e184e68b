-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 30, 2025 at 02:47 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `adzetadb`
--

-- --------------------------------------------------------

--
-- Table structure for table `case_studies`
--

CREATE TABLE `case_studies` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `client_name` varchar(255) NOT NULL,
  `industry` varchar(100) DEFAULT NULL,
  `hero_title` longtext DEFAULT NULL,
  `hero_subtitle` text DEFAULT NULL,
  `hero_description` longtext DEFAULT NULL,
  `hero_image` varchar(500) DEFAULT NULL,
  `hero_badge_text` varchar(255) DEFAULT NULL,
  `highlight_1_title` varchar(255) DEFAULT NULL,
  `highlight_1_content` longtext DEFAULT NULL,
  `highlight_1_icon` varchar(100) DEFAULT 'bi-heart-pulse',
  `highlight_2_title` varchar(255) DEFAULT NULL,
  `highlight_2_content` longtext DEFAULT NULL,
  `highlight_2_icon` varchar(100) DEFAULT 'bi-question-circle',
  `highlight_3_title` varchar(255) DEFAULT NULL,
  `highlight_3_content` longtext DEFAULT NULL,
  `highlight_3_icon` varchar(100) DEFAULT 'bi-compass',
  `highlight_4_title` varchar(255) DEFAULT NULL,
  `highlight_4_content` longtext DEFAULT NULL,
  `highlight_4_icon` varchar(100) DEFAULT 'bi-cpu',
  `highlight_5_title` varchar(255) DEFAULT NULL,
  `highlight_5_content` longtext DEFAULT NULL,
  `highlight_5_icon` varchar(100) DEFAULT 'bi-gear-wide-connected',
  `highlight_6_title` varchar(255) DEFAULT NULL,
  `highlight_6_content` longtext DEFAULT NULL,
  `highlight_6_icon` varchar(100) DEFAULT 'bi-trophy',
  `challenge_title` varchar(255) DEFAULT NULL,
  `challenge_subtitle` varchar(255) DEFAULT NULL,
  `challenge_description` longtext DEFAULT NULL,
  `challenge_funnel_without_title` varchar(255) DEFAULT NULL,
  `challenge_funnel_without_description` varchar(255) DEFAULT NULL,
  `challenge_funnel_without_result` varchar(255) DEFAULT NULL,
  `challenge_funnel_with_title` varchar(255) DEFAULT NULL,
  `challenge_funnel_with_description` varchar(255) DEFAULT NULL,
  `challenge_funnel_with_result` varchar(255) DEFAULT NULL,
  `challenge_point_1_title` varchar(255) DEFAULT NULL,
  `challenge_point_1_description` longtext DEFAULT NULL,
  `challenge_point_2_title` varchar(255) DEFAULT NULL,
  `challenge_point_2_description` longtext DEFAULT NULL,
  `challenge_point_3_title` varchar(255) DEFAULT NULL,
  `challenge_point_3_description` longtext DEFAULT NULL,
  `solution_title` varchar(255) DEFAULT NULL,
  `solution_subtitle` varchar(255) DEFAULT NULL,
  `solution_description` longtext DEFAULT NULL,
  `solution_point_1_title` varchar(255) DEFAULT NULL,
  `solution_point_1_description` longtext DEFAULT NULL,
  `solution_point_2_title` varchar(255) DEFAULT NULL,
  `solution_point_2_description` longtext DEFAULT NULL,
  `solution_point_3_title` varchar(255) DEFAULT NULL,
  `solution_point_3_description` longtext DEFAULT NULL,
  `solution_point_4_title` varchar(255) DEFAULT NULL,
  `solution_point_4_description` longtext DEFAULT NULL,
  `solution_ab_test_title` varchar(255) DEFAULT NULL,
  `solution_ab_test_control_title` varchar(255) DEFAULT NULL,
  `solution_ab_test_control_description` longtext DEFAULT NULL,
  `solution_ab_test_experiment_title` varchar(255) DEFAULT NULL,
  `solution_ab_test_experiment_description` longtext DEFAULT NULL,
  `results_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`results_data`)),
  `results_title` varchar(255) DEFAULT NULL,
  `results_subtitle` varchar(255) DEFAULT NULL,
  `results_description` longtext DEFAULT NULL,
  `methodology_title` varchar(255) DEFAULT NULL,
  `methodology_description` longtext DEFAULT NULL,
  `outcomes_title` varchar(255) DEFAULT NULL,
  `outcomes_description` longtext DEFAULT NULL,
  `testimonial_quote` longtext DEFAULT NULL,
  `testimonial_author_name` varchar(255) DEFAULT NULL,
  `testimonial_author_title` varchar(255) DEFAULT NULL,
  `testimonial_author_company` varchar(255) DEFAULT NULL,
  `cta_title` varchar(255) DEFAULT NULL,
  `cta_description` longtext DEFAULT NULL,
  `cta_button_text` varchar(100) DEFAULT NULL,
  `cta_button_url` varchar(500) DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `focus_keyword` varchar(100) DEFAULT NULL,
  `canonical_url` varchar(500) DEFAULT NULL,
  `og_title` varchar(255) DEFAULT NULL,
  `og_description` text DEFAULT NULL,
  `og_image` varchar(500) DEFAULT NULL,
  `twitter_card_type` enum('summary','summary_large_image','app','player') DEFAULT 'summary_large_image',
  `excerpt` text DEFAULT NULL,
  `featured_image` varchar(500) DEFAULT NULL,
  `author_id` int(11) DEFAULT NULL,
  `status` enum('draft','published','archived') DEFAULT 'draft',
  `visibility` enum('public','private','password') DEFAULT 'public',
  `password` varchar(255) DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `template` varchar(100) DEFAULT 'luminous-skin-clinic',
  `custom_css` longtext DEFAULT NULL,
  `view_count` int(11) DEFAULT 0,
  `reading_time` int(11) DEFAULT 0,
  `word_count` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `case_studies`
--

INSERT INTO `case_studies` (`id`, `title`, `slug`, `client_name`, `industry`, `hero_title`, `hero_subtitle`, `hero_description`, `hero_image`, `hero_badge_text`, `highlight_1_title`, `highlight_1_content`, `highlight_1_icon`, `highlight_2_title`, `highlight_2_content`, `highlight_2_icon`, `highlight_3_title`, `highlight_3_content`, `highlight_3_icon`, `highlight_4_title`, `highlight_4_content`, `highlight_4_icon`, `highlight_5_title`, `highlight_5_content`, `highlight_5_icon`, `highlight_6_title`, `highlight_6_content`, `highlight_6_icon`, `challenge_title`, `challenge_subtitle`, `challenge_description`, `solution_title`, `solution_subtitle`, `solution_description`, `solution_point_1_title`, `solution_point_1_description`, `solution_point_2_title`, `solution_point_2_description`, `solution_point_3_title`, `solution_point_3_description`, `solution_point_4_title`, `solution_point_4_description`, `results_data`, `results_title`, `results_subtitle`, `results_description`, `methodology_title`, `methodology_description`, `outcomes_title`, `outcomes_description`, `testimonial_quote`, `testimonial_author_name`, `testimonial_author_title`, `testimonial_author_company`, `cta_title`, `cta_description`, `cta_button_text`, `cta_button_url`, `meta_title`, `meta_description`, `meta_keywords`, `focus_keyword`, `canonical_url`, `og_title`, `og_description`, `og_image`, `twitter_card_type`, `excerpt`, `featured_image`, `author_id`, `status`, `visibility`, `password`, `published_at`, `template`, `custom_css`, `view_count`, `reading_time`, `word_count`, `created_at`, `updated_at`) VALUES
(5, 'Lulla Skin Care', 'lulla-skin-care', 'Lulla Skin Care fudu', 'healthcare', 'Lulla Skin Care Achieves 300% ROI Increase with Precision Targeting', '', 'Lulla Skin Care, a leading provider of innovative skincare solutions, partnered with us to overcome inefficient ad spend. Through precision LTV targeting, we achieved a remarkable 300% ROI increase and significantly reduced customer acquisition costs.', '', 'AI-DRIVEN CLIENT ACQUISITION', 'Client Profile', 'Lulla Skin Care fudu is a dynamic healthcare company specializing in advanced skincare products designed to address various dermatological needs. They are committed to providing high-quality, science-backed solutions to their customers and maintaining a strong brand reputation within the competitive skincare market. Their target audience spans diverse demographics seeking effective and reliable skincare solutions.', 'bi-heart-pulse', 'The Core Challenge', 'Lulla Skin Care faced the challenge of inefficient ad spend, resulting in a low return on investment and unsustainable customer acquisition costs. Their marketing campaigns were not effectively targeting high-value customers, leading to wasted resources and missed opportunities for growth. They needed a more precise and data-driven approach to optimize their ad campaigns and maximize their marketing ROI.', 'bi-question-circle', 'Previous Approach', 'Prior to partnering with us, Lulla Skin Care relied on broad-based marketing campaigns targeting a wide audience. This approach lacked the precision needed to identify and attract high-value customers, resulting in low conversion rates and a high cost per acquisition. They also experimented with various A/B testing strategies, but these efforts were not guided by a comprehensive understanding of customer lifetime value (LTV).', 'bi-compass', 'Our Solution', 'We implemented a precision LTV targeting strategy designed to identify and acquire high-value customers for Lulla Skin Care. This involved leveraging advanced data analytics and machine learning to predict customer lifetime value and tailor marketing campaigns accordingly. Our solution focused on optimizing ad spend, improving conversion rates, and increasing overall marketing ROI.', 'bi-cpu', 'Methodology', 'Our methodology involved several key steps: (1) Data Integration: We integrated Lulla Skin Care\'s customer data with external data sources to create a comprehensive customer profile. (2) LTV Modeling: We developed a predictive LTV model to identify high-value customers based on their purchase history, demographics, and online behavior. (3) Targeted Campaign Development: We created targeted ad campaigns tailored to the specific needs and preferences of high-value customers. (4) Continuous Optimization: We continuously monitored campaign performance and made data-driven adjustments to optimize ad spend and improve conversion rates.', 'bi-gear-wide-connected', 'Key Outcomes', 'The implementation of our precision LTV targeting strategy yielded significant results for Lulla Skin Care, including a 300% increase in ROI, a 45% reduction in customer acquisition costs, a 12.5% improvement in conversion rates, and an 85% customer satisfaction score. These results demonstrate the effectiveness of our data-driven approach and the importance of focusing on customer lifetime value.', 'bi-trophy', 'The Challenge: Inefficient Ad Spend and Low ROI', 'Understanding the Problem', 'Lulla Skin Care was struggling with inefficient ad spend, resulting in a low return on investment and unsustainable customer acquisition costs. Their marketing campaigns lacked the precision needed to target high-value customers, leading to wasted resources and missed opportunities for growth.\n\nThe company\'s previous marketing efforts were not effectively reaching the right audience, resulting in low conversion rates and a high cost per acquisition. They needed a more sophisticated approach to identify and attract customers with a higher lifetime value, ultimately improving their marketing ROI and driving sustainable growth.\n\nFurthermore, the inability to accurately predict customer lifetime value hindered their ability to make informed decisions about ad spend allocation and campaign optimization. This lack of data-driven insights resulted in a reactive marketing strategy that was not aligned with their business goals.', 'Our Solution', 'Strategic Approach', 'Our solution involved a comprehensive, data-driven approach to precision LTV targeting. We leveraged advanced analytics and machine learning to identify and acquire high-value customers for Lulla Skin Care, optimizing their ad spend and maximizing their marketing ROI.\n\nWe began by integrating Lulla Skin Care\'s customer data with external data sources to create a comprehensive customer profile. This allowed us to develop a predictive LTV model that accurately identified high-value customers based on their purchase history, demographics, and online behavior.\n\nBased on the LTV model, we created targeted ad campaigns tailored to the specific needs and preferences of high-value customers. We continuously monitored campaign performance and made data-driven adjustments to optimize ad spend and improve conversion rates, ensuring that Lulla Skin Care was reaching the right audience with the right message at the right time.', 'Strategy Development', 'We conducted a thorough analysis of Lulla Skin Care\'s existing customer data and marketing campaigns to identify areas for improvement. This involved developing a clear understanding of their target audience, their purchasing behavior, and their lifetime value.', 'Implementation Process', 'We implemented a step-by-step methodology that included data integration, LTV modeling, targeted campaign development, and continuous optimization. This ensured that the solution was implemented effectively and efficiently.', 'Technology Integration', 'We leveraged advanced data analytics and machine learning tools to build the predictive LTV model and optimize ad campaigns. This included integrating Lulla Skin Care\'s CRM data with external data sources and utilizing machine learning algorithms to identify high-value customers.', 'Optimization & Monitoring', 'We continuously monitored campaign performance and made data-driven adjustments to optimize ad spend and improve conversion rates. This involved tracking key metrics such as ROI, customer acquisition cost, and conversion rate, and making adjustments to the campaigns based on these metrics.', '{\"metric_1_value\":\"300%\",\"metric_1_title\":\"ROI Increase\",\"metric_1_description\":\"Return on investment improvement\",\"metric_2_value\":\"45%\",\"metric_2_title\":\"Cost Reduction\",\"metric_2_description\":\"Operational cost savings in customer acquisition\",\"metric_3_value\":\"12.5%\",\"metric_3_title\":\"Conversion Rate\",\"metric_3_description\":\"Improved conversion performance on targeted ad campaigns\",\"metric_4_value\":\"85%\",\"metric_4_title\":\"Client Satisfaction\",\"metric_4_description\":\"Customer satisfaction score based on post-implementation survey\",\"title_metric\":\"Results & Impact\",\"subtitle_metric\":\"Measurable Outcomes\",\"description_metric\":\"The implementation of our precision LTV targeting strategy yielded significant and measurable results for Lulla Skin Care. By focusing on acquiring high-value customers, we were able to dramatically improve their return on investment and reduce their customer acquisition costs.\\n\\nThe increased conversion rates and higher customer satisfaction scores demonstrate the effectiveness of our targeted messaging and offers. These results highlight the power of data-driven marketing and the importance of focusing on customer lifetime value.\\n\\nUltimately, our partnership with Lulla Skin Care resulted in significant growth, improved profitability, and a stronger brand reputation.\",\"metric_1_value_metric\":\"300%\",\"metric_1_title_metric\":\"ROI Increase\",\"metric_1_description_metric\":\"Return on investment improvement\",\"metric_2_value_metric\":\"45%\",\"metric_2_title_metric\":\"Cost Reduction\",\"metric_2_description_metric\":\"Operational cost savings in customer acquisition\",\"metric_3_value_metric\":\"12.5%\",\"metric_3_title_metric\":\"Conversion Rate\",\"metric_3_description_metric\":\"Improved conversion performance on targeted ad campaigns\",\"metric_4_value_metric\":\"85%\",\"metric_4_title_metric\":\"Client Satisfaction\",\"metric_4_description_metric\":\"Customer satisfaction score based on post-implementation survey\"}', '', '', NULL, '', NULL, '', NULL, 'Our partnership has been transformative. We\'ve seen a dramatic improvement in our ROI and a significant reduction in our customer acquisition costs. Their data-driven approach is exactly what we needed to reach our target audience effectively.', 'Jane Doe', 'CEO', 'Lulla Skin Care fudu', 'Ready to Transform Your Business?', 'Discover how our precision LTV targeting strategy can help you achieve significant ROI improvements and reduce your customer acquisition costs. Let\'s discuss your unique business needs and develop a tailored solution.', 'Get Started Today', '/contact', 'Lulla Skin Care: 300% ROI with Precision LTV Targeting', 'Learn how Lulla Skin Care achieved a 300% ROI increase and 45% cost reduction with our precision LTV targeting strategy. Contact us today!', '', 'precision LTV targeting', '', '', '', NULL, 'summary_large_image', '', '', 1, 'published', 'public', NULL, '2025-06-30 09:13:20', 'luminous-skin-clinic', NULL, 6, 0, 0, '2025-06-30 11:39:08', '2025-06-30 12:45:02');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `case_studies`
--
ALTER TABLE `case_studies`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_slug` (`slug`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_industry` (`industry`),
  ADD KEY `idx_client` (`client_name`),
  ADD KEY `idx_published` (`published_at`),
  ADD KEY `idx_author` (`author_id`),
  ADD KEY `idx_template` (`template`),
  ADD KEY `idx_visibility` (`visibility`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `case_studies`
--
ALTER TABLE `case_studies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
