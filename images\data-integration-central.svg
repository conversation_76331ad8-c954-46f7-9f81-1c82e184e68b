<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient background circle -->
  <circle cx="200" cy="200" r="200" fill="url(#paint0_radial)"/>
  
  <!-- Central icon -->
  <g transform="translate(100, 100) scale(2)">
    <path d="M50 20C33.4315 20 20 33.4315 20 50C20 66.5685 33.4315 80 50 80C66.5685 80 80 66.5685 80 50C80 33.4315 66.5685 20 50 20Z" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M50 35V50L60 60" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M50 20C58.2843 20 65 13.2843 65 5" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M80 50C80 41.7157 86.7157 35 95 35" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M50 80C41.7157 80 35 86.7157 35 95" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M20 50C20 58.2843 13.2843 65 5 65" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Connection lines -->
  <path d="M200 50L200 150" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="4 4"/>
  <path d="M200 250L200 350" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="4 4"/>
  <path d="M50 200L150 200" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="4 4"/>
  <path d="M250 200L350 200" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="4 4"/>
  
  <!-- Diagonal connection lines -->
  <path d="M80 80L150 150" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="4 4"/>
  <path d="M250 250L320 320" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="4 4"/>
  <path d="M80 320L150 250" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="4 4"/>
  <path d="M250 150L320 80" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="4 4"/>
  
  <!-- Small data nodes -->
  <circle cx="50" cy="200" r="10" fill="#FFFFFF" fill-opacity="0.2"/>
  <circle cx="350" cy="200" r="10" fill="#FFFFFF" fill-opacity="0.2"/>
  <circle cx="200" cy="50" r="10" fill="#FFFFFF" fill-opacity="0.2"/>
  <circle cx="200" cy="350" r="10" fill="#FFFFFF" fill-opacity="0.2"/>
  <circle cx="80" cy="80" r="8" fill="#FFFFFF" fill-opacity="0.2"/>
  <circle cx="320" cy="80" r="8" fill="#FFFFFF" fill-opacity="0.2"/>
  <circle cx="80" cy="320" r="8" fill="#FFFFFF" fill-opacity="0.2"/>
  <circle cx="320" cy="320" r="8" fill="#FFFFFF" fill-opacity="0.2"/>
  
  <!-- Animated pulse circles (for visual effect) -->
  <circle cx="200" cy="200" r="160" stroke="#FFFFFF" stroke-opacity="0.1" stroke-width="1">
    <animate attributeName="r" from="160" to="180" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="stroke-opacity" from="0.1" to="0" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="200" r="140" stroke="#FFFFFF" stroke-opacity="0.15" stroke-width="1">
    <animate attributeName="r" from="140" to="160" dur="3s" repeatCount="indefinite" begin="1s"/>
    <animate attributeName="stroke-opacity" from="0.15" to="0" dur="3s" repeatCount="indefinite" begin="1s"/>
  </circle>
  
  <!-- Gradient definitions -->
  <defs>
    <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(200 200) rotate(90) scale(200)">
      <stop offset="0" stop-color="#e958a1"/>
      <stop offset="0.5" stop-color="#ff5d74"/>
      <stop offset="1" stop-color="#8f76f5"/>
    </radialGradient>
  </defs>
</svg>
