/**
 * AdZeta Admin Panel - Error Logs Module
 * Advanced error tracking and debugging interface
 */

window.AdZetaErrorLogs = {
    // Module state
    state: {
        currentPage: 1,
        filters: {
            severity: '',
            category: '',
            status: '',
            search: '',
            dateFrom: '',
            dateTo: ''
        },
        selectedLog: null,
        autoRefresh: false,
        refreshInterval: null
    },

    // Initialize error logs module
    init() {
        console.log('Error Logs module initialized');
        this.bindEvents();
        return this; // Return this for chaining
    },

    // Bind event listeners
    bindEvents() {
        // Auto-refresh toggle
        document.addEventListener('change', (e) => {
            if (e.target.id === 'autoRefresh') {
                this.toggleAutoRefresh(e.target.checked);
            }
        });

        // Filter changes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('error-filter')) {
                this.updateFilters();
                this.loadErrorLogs();
            }
        });

        // Search input
        document.addEventListener('input', (e) => {
            if (e.target.id === 'errorSearch') {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.state.filters.search = e.target.value;
                    this.loadErrorLogs();
                }, 500);
            }
        });
    },

    // Show error logs view
    show() {
        this.renderErrorLogsView();
        this.loadErrorLogs();
        this.loadErrorStats();
    },

    // Render error logs interface
    renderErrorLogsView() {
        const content = document.getElementById('error-logsView');
        if (!content) {
            console.error('Error logs view container not found');
            return;
        }
        content.innerHTML = `
            <div class="error-logs-container">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2><i class="fas fa-bug text-danger me-2"></i>Error Logs</h2>
                        <p class="text-muted mb-0">Advanced error tracking and debugging</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-success" onclick="AdZetaErrorLogs.toggleRealTimeMonitoring()" id="realTimeToggle">
                            <i class="fas fa-eye me-2"></i>Real-Time Monitor
                        </button>
                        <button class="btn btn-outline-primary" onclick="AdZetaErrorLogs.exportLogs()">
                            <i class="fas fa-download me-2"></i>Export
                        </button>
                        <button class="btn btn-outline-warning" onclick="AdZetaErrorLogs.clearOldLogs()">
                            <i class="fas fa-trash me-2"></i>Clear Old
                        </button>
                        <button class="btn btn-primary" onclick="AdZetaErrorLogs.loadErrorLogs()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh
                        </button>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4" id="errorStatsCards">
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0" id="criticalCount">-</h4>
                                        <small>Critical Errors</small>
                                    </div>
                                    <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0" id="highCount">-</h4>
                                        <small>High Priority</small>
                                    </div>
                                    <i class="fas fa-exclamation-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0" id="resolvedCount">-</h4>
                                        <small>Resolved</small>
                                    </div>
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0" id="totalCount">-</h4>
                                        <small>Total Errors</small>
                                    </div>
                                    <i class="fas fa-list fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">Severity</label>
                                <select class="form-select error-filter" id="severityFilter">
                                    <option value="">All Severities</option>
                                    <option value="critical">Critical</option>
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Category</label>
                                <select class="form-select error-filter" id="categoryFilter">
                                    <option value="">All Categories</option>
                                    <option value="posts">Posts</option>
                                    <option value="media">Media</option>
                                    <option value="authentication">Auth</option>
                                    <option value="validation">Validation</option>
                                    <option value="database">Database</option>
                                    <option value="general">General</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status</label>
                                <select class="form-select error-filter" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="unresolved">Unresolved</option>
                                    <option value="resolved">Resolved</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <input type="text" class="form-control" id="errorSearch" 
                                       placeholder="Search errors, endpoints, messages...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Date From</label>
                                <input type="date" class="form-control error-filter" id="dateFromFilter">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">Auto Refresh</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoRefresh">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error Logs Table -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Error Logs</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Time</th>
                                        <th>Severity</th>
                                        <th>Method</th>
                                        <th>Endpoint</th>
                                        <th>Status</th>
                                        <th>Error</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="errorLogsTableBody">
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div id="errorLogsPagination"></div>
                    </div>
                </div>
            </div>

            <!-- Error Detail Modal -->
            <div class="modal fade" id="errorDetailModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Error Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="errorDetailContent">
                            <!-- Error details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" onclick="AdZetaErrorLogs.resolveError()">
                                <i class="fas fa-check me-2"></i>Mark as Resolved
                            </button>
                            <button type="button" class="btn btn-danger" onclick="AdZetaErrorLogs.deleteError()">
                                <i class="fas fa-trash me-2"></i>Delete
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // Load error logs
    async loadErrorLogs() {
        try {
            const params = new URLSearchParams({
                page: this.state.currentPage,
                limit: 50,
                ...this.state.filters
            });

            const response = await window.AdZetaApp.apiRequest(`/error-logs?${params}`);
            
            if (response.success) {
                this.renderErrorLogsTable(response.logs);
                this.renderPagination(response.pagination);
            }
        } catch (error) {
            console.error('Failed to load error logs:', error);
            window.AdZetaApp.showNotification('Failed to load error logs', 'danger');
        }
    },

    // Load error statistics
    async loadErrorStats() {
        try {
            const response = await window.AdZetaApp.apiRequest('/error-logs/stats');
            
            if (response.success) {
                this.renderErrorStats(response.stats);
            }
        } catch (error) {
            console.error('Failed to load error stats:', error);
        }
    },

    // Render error logs table
    renderErrorLogsTable(logs) {
        const tbody = document.getElementById('errorLogsTableBody');
        
        if (logs.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4 text-muted">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <br>No error logs found
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = logs.map(log => `
            <tr class="error-log-row ${log.is_resolved ? 'table-success' : ''}" 
                onclick="AdZetaErrorLogs.showErrorDetail(${log.id})">
                <td>
                    <small class="text-muted">${this.formatDate(log.timestamp)}</small>
                </td>
                <td>
                    <span class="badge bg-${this.getSeverityColor(log.severity)}">${log.severity}</span>
                </td>
                <td>
                    <span class="badge bg-secondary">${log.method}</span>
                </td>
                <td>
                    <code class="small">${this.truncateText(log.endpoint, 30)}</code>
                </td>
                <td>
                    <span class="badge bg-${log.status_code >= 500 ? 'danger' : 'warning'}">${log.status_code}</span>
                </td>
                <td>
                    <div class="small">
                        <strong>${log.error_type || 'Unknown'}</strong><br>
                        <span class="text-muted">${this.truncateText(log.error_message, 50)}</span>
                    </div>
                </td>
                <td>
                    <span class="badge bg-info">${log.category}</span>
                </td>
                <td>
                    ${log.is_resolved ? 
                        '<i class="fas fa-check-circle text-success" title="Resolved"></i>' : 
                        '<i class="fas fa-exclamation-circle text-warning" title="Unresolved"></i>'
                    }
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); AdZetaErrorLogs.showErrorDetail(${log.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    },

    // Show error detail modal
    async showErrorDetail(id) {
        try {
            const response = await window.AdZetaApp.apiRequest(`/error-logs/${id}`);
            
            if (response.success) {
                this.state.selectedLog = response.log;
                this.renderErrorDetail(response.log);
                
                const modal = new bootstrap.Modal(document.getElementById('errorDetailModal'));
                modal.show();
            }
        } catch (error) {
            console.error('Failed to load error detail:', error);
            window.AdZetaApp.showNotification('Failed to load error details', 'danger');
        }
    },

    // Render error detail modal content
    renderErrorDetail(log) {
        const content = document.getElementById('errorDetailContent');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Request Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Request ID:</strong></td><td><code>${log.request_id}</code></td></tr>
                        <tr><td><strong>Method:</strong></td><td><span class="badge bg-secondary">${log.method}</span></td></tr>
                        <tr><td><strong>Endpoint:</strong></td><td><code>${log.endpoint}</code></td></tr>
                        <tr><td><strong>Full URL:</strong></td><td><code class="small">${log.full_url}</code></td></tr>
                        <tr><td><strong>Status Code:</strong></td><td><span class="badge bg-${log.status_code >= 500 ? 'danger' : 'warning'}">${log.status_code}</span></td></tr>
                        <tr><td><strong>Response Time:</strong></td><td>${log.response_time}ms</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Error Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Error Type:</strong></td><td>${log.error_type || 'Unknown'}</td></tr>
                        <tr><td><strong>Severity:</strong></td><td><span class="badge bg-${this.getSeverityColor(log.severity)}">${log.severity}</span></td></tr>
                        <tr><td><strong>Category:</strong></td><td><span class="badge bg-info">${log.category}</span></td></tr>
                        <tr><td><strong>Timestamp:</strong></td><td>${this.formatDate(log.timestamp)}</td></tr>
                        <tr><td><strong>Page URL:</strong></td><td>${log.page_url ? `<a href="${log.page_url}" target="_blank">${log.page_url}</a>` : 'N/A'}</td></tr>
                    </table>
                </div>
            </div>

            <div class="mt-4">
                <h6>Error Message</h6>
                <div class="alert alert-danger">
                    <pre class="mb-0">${log.error_message || 'No error message available'}</pre>
                </div>
            </div>

            ${log.error_stack ? `
                <div class="mt-3">
                    <h6>Stack Trace</h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0 small">${log.error_stack}</pre>
                    </div>
                </div>
            ` : ''}

            ${log.request_data ? `
                <div class="mt-3">
                    <h6>Request Data</h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0 small">${JSON.stringify(log.request_data, null, 2)}</pre>
                    </div>
                </div>
            ` : ''}

            ${log.response_data ? `
                <div class="mt-3">
                    <h6>Response Data</h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0 small">${log.response_data}</pre>
                    </div>
                </div>
            ` : ''}

            <div class="mt-3">
                <h6>Environment</h6>
                <div class="row">
                    <div class="col-md-6">
                        <small><strong>User Agent:</strong><br>${log.user_agent || 'N/A'}</small>
                    </div>
                    <div class="col-md-6">
                        <small><strong>IP Address:</strong> ${log.ip_address || 'N/A'}</small><br>
                        <small><strong>Admin Version:</strong> ${log.admin_version || 'N/A'}</small>
                    </div>
                </div>
            </div>

            ${log.is_resolved ? `
                <div class="mt-3">
                    <div class="alert alert-success">
                        <h6>Resolution Information</h6>
                        <p><strong>Resolved At:</strong> ${this.formatDate(log.resolved_at)}</p>
                        <p><strong>Notes:</strong> ${log.resolution_notes || 'No notes provided'}</p>
                    </div>
                </div>
            ` : ''}
        `;
    },

    // Helper methods
    getSeverityColor(severity) {
        const colors = {
            critical: 'danger',
            high: 'warning',
            medium: 'info',
            low: 'secondary'
        };
        return colors[severity] || 'secondary';
    },

    formatDate(dateString) {
        return new Date(dateString).toLocaleString();
    },

    truncateText(text, length) {
        if (!text) return 'N/A';
        return text.length > length ? text.substring(0, length) + '...' : text;
    },

    // Update filters
    updateFilters() {
        this.state.filters = {
            severity: document.getElementById('severityFilter').value,
            category: document.getElementById('categoryFilter').value,
            status: document.getElementById('statusFilter').value,
            search: document.getElementById('errorSearch').value,
            dateFrom: document.getElementById('dateFromFilter').value,
            dateTo: ''
        };
        this.state.currentPage = 1;
    },

    // Toggle auto refresh
    toggleAutoRefresh(enabled) {
        this.state.autoRefresh = enabled;
        
        if (enabled) {
            this.state.refreshInterval = setInterval(() => {
                this.loadErrorLogs();
                this.loadErrorStats();
            }, 30000); // Refresh every 30 seconds
            window.AdZetaApp.showNotification('Auto-refresh enabled (30s)', 'info');
        } else {
            if (this.state.refreshInterval) {
                clearInterval(this.state.refreshInterval);
                this.state.refreshInterval = null;
            }
            window.AdZetaApp.showNotification('Auto-refresh disabled', 'info');
        }
    },

    // Render error statistics
    renderErrorStats(stats) {
        // Update stat cards
        document.getElementById('criticalCount').textContent = stats.by_severity.critical || 0;
        document.getElementById('highCount').textContent = stats.by_severity.high || 0;
        document.getElementById('resolvedCount').textContent = stats.resolution.resolved || 0;
        document.getElementById('totalCount').textContent = stats.resolution.total || 0;
    },

    // Render pagination
    renderPagination(pagination) {
        const container = document.getElementById('errorLogsPagination');
        
        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<nav><ul class="pagination pagination-sm mb-0">';
        
        // Previous button
        paginationHTML += `
            <li class="page-item ${pagination.page === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="AdZetaErrorLogs.goToPage(${pagination.page - 1})">Previous</a>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= Math.min(pagination.pages, 10); i++) {
            paginationHTML += `
                <li class="page-item ${i === pagination.page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="AdZetaErrorLogs.goToPage(${i})">${i}</a>
                </li>
            `;
        }
        
        // Next button
        paginationHTML += `
            <li class="page-item ${pagination.page === pagination.pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="AdZetaErrorLogs.goToPage(${pagination.page + 1})">Next</a>
            </li>
        `;
        
        paginationHTML += '</ul></nav>';
        
        container.innerHTML = paginationHTML;
    },

    // Go to specific page
    goToPage(page) {
        this.state.currentPage = page;
        this.loadErrorLogs();
    },

    // Resolve error
    async resolveError() {
        if (!this.state.selectedLog) return;

        const notes = prompt('Resolution notes (optional):');
        if (notes === null) return; // User cancelled

        try {
            const response = await window.AdZetaApp.apiRequest(`/error-logs/${this.state.selectedLog.id}/resolve`, {
                method: 'POST',
                body: JSON.stringify({
                    resolved_by: 1, // Current user ID
                    resolution_notes: notes
                })
            });

            if (response.success) {
                window.AdZetaApp.showNotification('Error marked as resolved', 'success');
                bootstrap.Modal.getInstance(document.getElementById('errorDetailModal')).hide();
                this.loadErrorLogs();
                this.loadErrorStats();
            }
        } catch (error) {
            console.error('Failed to resolve error:', error);
            window.AdZetaApp.showNotification('Failed to resolve error', 'danger');
        }
    },

    // Delete error
    async deleteError() {
        if (!this.state.selectedLog) return;

        if (!confirm('Are you sure you want to delete this error log?')) return;

        try {
            const response = await window.AdZetaApp.apiRequest(`/error-logs/${this.state.selectedLog.id}`, {
                method: 'DELETE'
            });

            if (response.success) {
                window.AdZetaApp.showNotification('Error log deleted', 'success');
                bootstrap.Modal.getInstance(document.getElementById('errorDetailModal')).hide();
                this.loadErrorLogs();
                this.loadErrorStats();
            }
        } catch (error) {
            console.error('Failed to delete error:', error);
            window.AdZetaApp.showNotification('Failed to delete error', 'danger');
        }
    },

    // Export logs
    exportLogs() {
        // Use the existing API logger export functionality
        if (window.AdZetaAPILogger) {
            window.AdZetaAPILogger.exportLogs();
        } else {
            window.AdZetaApp.showNotification('Export functionality not available', 'warning');
        }
    },

    // Clear old logs
    async clearOldLogs() {
        if (!confirm('This will delete all error logs older than 30 days. Continue?')) return;

        try {
            const response = await window.AdZetaApp.apiRequest('/error-logs/clear', {
                method: 'DELETE'
            });

            if (response.success) {
                window.AdZetaApp.showNotification(response.message, 'success');
                this.loadErrorLogs();
                this.loadErrorStats();
            }
        } catch (error) {
            console.error('Failed to clear old logs:', error);
            window.AdZetaApp.showNotification('Failed to clear old logs', 'danger');
        }
    },

    // Toggle real-time monitoring
    toggleRealTimeMonitoring() {
        if (window.AdZetaErrorMonitor) {
            const isEnabled = !window.AdZetaErrorMonitor.isEnabled;
            window.AdZetaErrorMonitor.toggle(isEnabled);

            const button = document.getElementById('realTimeToggle');
            if (button) {
                if (isEnabled) {
                    button.innerHTML = '<i class="fas fa-eye-slash me-2"></i>Stop Monitor';
                    button.className = 'btn btn-outline-danger';
                } else {
                    button.innerHTML = '<i class="fas fa-eye me-2"></i>Real-Time Monitor';
                    button.className = 'btn btn-outline-success';
                }
            }

            window.AdZetaApp.showNotification(
                isEnabled ? 'Real-time monitoring enabled' : 'Real-time monitoring disabled',
                isEnabled ? 'success' : 'info'
            );
        } else {
            window.AdZetaApp.showNotification('Error monitor not available', 'warning');
        }
    }
};

// Ensure the module is available globally
console.log('AdZetaErrorLogs module loaded:', typeof window.AdZetaErrorLogs);

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        if (window.AdZetaErrorLogs) {
            console.log('Auto-initializing AdZetaErrorLogs module');
        }
    });
} else {
    // DOM is already ready
    if (window.AdZetaErrorLogs) {
        console.log('AdZetaErrorLogs module ready');
    }
}
