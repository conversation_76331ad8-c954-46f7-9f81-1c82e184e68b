/**
 * Enhanced Step Process Animations and Interactions
 */
document.addEventListener('DOMContentLoaded', function() {
  // Initialize the enhanced step process
  initEnhancedSteps();
  
  // Set up scroll-based animations
  setupScrollAnimations();
});

function initEnhancedSteps() {
  // Get all step items
  const stepItems = document.querySelectorAll('.enhanced-step-item');
  if (!stepItems.length) return;
  
  // Add event listeners for hover effects
  stepItems.forEach((item, index) => {
    // Add mouse enter event
    item.addEventListener('mouseenter', function() {
      // Add active class to the step container
      const stepContainer = this.closest('.enhanced-step-process');
      if (stepContainer) {
        stepContainer.classList.add('step-process-active');
      }
      
      // Highlight the current step
      this.classList.add('step-active');
      
      // Add pulse animation to the current step
      const pulse = this.querySelector('.step-pulse');
      if (pulse) {
        pulse.style.animationPlayState = 'running';
      }
    });
    
    // Add mouse leave event
    item.addEventListener('mouseleave', function() {
      // Remove active class from the step container
      const stepContainer = this.closest('.enhanced-step-process');
      if (stepContainer) {
        stepContainer.classList.remove('step-process-active');
      }
      
      // Remove highlight from the current step
      this.classList.remove('step-active');
      
      // Pause pulse animation
      const pulse = this.querySelector('.step-pulse');
      if (pulse) {
        pulse.style.animationPlayState = 'paused';
      }
    });
  });
}

function setupScrollAnimations() {
  // Get all elements with fade-in animation
  const fadeElements = document.querySelectorAll('.step-fade-in');
  if (!fadeElements.length) return;
  
  // Create intersection observer
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // Add active class to trigger animation
        entry.target.classList.add('active');
        
        // Unobserve after animation is triggered
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.2, // Trigger when 20% of the element is visible
    rootMargin: '0px 0px -100px 0px' // Adjust based on when you want the animation to trigger
  });
  
  // Observe all fade elements
  fadeElements.forEach(element => {
    observer.observe(element);
  });
  
  // Activate the step process when it comes into view
  const stepProcess = document.querySelector('.enhanced-step-process');
  if (stepProcess) {
    const processObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Add active class to trigger progress animation
          setTimeout(() => {
            entry.target.classList.add('step-process-active');
          }, 800); // Delay to allow step items to fade in first
          
          // Unobserve after animation is triggered
          processObserver.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.3,
      rootMargin: '0px 0px -100px 0px'
    });
    
    processObserver.observe(stepProcess);
  }
}
