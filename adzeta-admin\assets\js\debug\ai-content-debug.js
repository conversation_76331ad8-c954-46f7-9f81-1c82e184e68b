/**
 * AI Content Debug Helper
 * Helps debug AI content processing issues
 */

window.AIContentDebugger = {

    /**
     * Test AI content processing with sample data
     */
    testAIContentProcessing() {
        console.log('🧪 Testing AI Content Processing...');

        // Sample AI-generated content with markers
        const sampleContent = `
        <h2>Maximizing ROI with Advanced Bidding Strategies</h2>

        <p>In today's competitive digital landscape, businesses need sophisticated approaches to maximize their return on investment.</p>

        [STYLED-CARD:background=#E6D8F2;color=#2B0B3A;border=#FF4081]
        Key Takeaway: Advanced bidding strategies can improve campaign performance by up to 40% when implemented correctly.
        [/STYLED-CARD]

        <p>Our analysis shows that companies implementing these strategies see significant improvements in their <span style="color: #FF4081; font-weight: 500;">conversion rates</span>.</p>

        [STATISTIC:value=247;unit=%;label=ROI Improvement;color=#FF4081][/STATISTIC]

        <p>The data clearly demonstrates the impact of [HIGHLIGHT:color=#FF4081]strategic bid optimization[/HIGHLIGHT] on overall campaign performance.</p>

        [CTA-BLOCK:title=Ready to Optimize Your Campaigns?;description=Let our experts help you implement advanced bidding strategies;button=Get Started Today;url=/contact][/CTA-BLOCK]
        `;

        console.log('📝 Sample content:', sampleContent);

        // Test marker detection
        const hasMarkers = /\[(?:STYLED-CARD|STATISTIC|CTA-BLOCK|HIGHLIGHT):/.test(sampleContent);
        console.log('🔍 Has custom markers:', hasMarkers);

        // Test AI Content Processor
        if (window.AIContentProcessor) {
            console.log('✅ AIContentProcessor available');

            try {
                const blocks = window.AIContentProcessor.processAIContent(sampleContent);
                console.log('🎯 Processed blocks:', blocks);
                console.log('📊 Block types:', blocks.map(b => b.type));

                // Test each block type
                blocks.forEach((block, index) => {
                    console.log(`Block ${index + 1}:`, {
                        type: block.type,
                        data: block.data
                    });
                });

                return blocks;
            } catch (error) {
                console.error('❌ Error processing AI content:', error);
            }
        } else {
            console.error('❌ AIContentProcessor not available');
        }
    },

    /**
     * Test custom Editor.js tools availability
     */
    testCustomTools() {
        console.log('🔧 Testing Custom Editor.js Tools...');

        const tools = [
            { name: 'StyledCard', class: window.StyledCard },
            { name: 'StatisticsTool', class: window.StatisticsTool },
            { name: 'CTATool', class: window.CTATool },
            { name: 'HighlightTool', class: window.HighlightTool }
        ];

        tools.forEach(tool => {
            if (tool.class) {
                console.log(`✅ ${tool.name} available`);

                // Test toolbox configuration
                if (tool.class.toolbox) {
                    console.log(`  📦 Toolbox:`, tool.class.toolbox);
                }
            } else {
                console.error(`❌ ${tool.name} not available`);
            }
        });
    },

    /**
     * Test Editor.js instance and configuration
     */
    testEditorInstance() {
        console.log('📝 Testing Editor.js Instance...');

        if (window.AdZetaPostEditor && window.AdZetaPostEditor.state.editor) {
            const editor = window.AdZetaPostEditor.state.editor;
            console.log('✅ Editor instance available:', editor);

            // Test editor configuration
            console.log('🔧 Editor configuration:', editor.configuration);

            // Test available tools
            if (editor.configuration.tools) {
                console.log('🛠️ Available tools:', Object.keys(editor.configuration.tools));
            }

            return editor;
        } else {
            console.error('❌ Editor instance not available');
            console.log('🔍 AdZetaPostEditor state:', window.AdZetaPostEditor?.state);
        }
    },

    /**
     * Simulate AI content insertion
     */
    async simulateAIContentInsertion() {
        console.log('🤖 Simulating AI Content Insertion...');

        const sampleAIResponse = {
            title: 'Advanced Bidding Strategies for Maximum ROI',
            excerpt: 'Learn how to optimize your campaigns with advanced bidding techniques that deliver measurable results.',
            content: `
            <h2>Maximizing ROI with Advanced Bidding Strategies</h2>

            <p>In today's competitive digital landscape, businesses need sophisticated approaches to maximize their return on investment.</p>

            [STYLED-CARD:background=#E6D8F2;color=#2B0B3A;border=#FF4081]
            Key Takeaway: Advanced bidding strategies can improve campaign performance by up to 40% when implemented correctly.
            [/STYLED-CARD]

            <p>Our analysis shows significant improvements in conversion rates.</p>

            [STATISTIC:value=247;unit=%;label=ROI Improvement;color=#FF4081][/STATISTIC]

            <p>The data demonstrates the impact of [HIGHLIGHT:color=#FF4081]strategic bid optimization[/HIGHLIGHT] on performance.</p>

            [CTA-BLOCK:title=Ready to Optimize?;description=Let our experts help you;button=Get Started;url=/contact][/CTA-BLOCK]
            `,
            seo: {
                meta_title: 'Advanced Bidding Strategies | ROI Optimization Guide',
                meta_description: 'Discover proven bidding strategies that increase ROI by 247%. Expert tips for campaign optimization.',
                focus_keyword: 'bidding strategies'
            }
        };

        console.log('📤 Sample AI response:', sampleAIResponse);

        // Test content processing
        if (window.AdZetaPostEditor) {
            try {
                console.log('🔄 Processing with populateFormWithAIContent...');
                window.AdZetaPostEditor.populateFormWithAIContent(sampleAIResponse);
                console.log('✅ Content insertion completed');
            } catch (error) {
                console.error('❌ Error during content insertion:', error);
            }
        } else {
            console.error('❌ AdZetaPostEditor not available');
        }
    },

    /**
     * Test plus button visibility
     */
    testPlusButtonVisibility() {
        console.log('➕ Testing Plus Button Visibility...');

        const toolbars = document.querySelectorAll('.ce-toolbar');
        const plusButtons = document.querySelectorAll('.ce-toolbar__plus');

        console.log('🔍 Found toolbars:', toolbars.length);
        console.log('🔍 Found plus buttons:', plusButtons.length);

        toolbars.forEach((toolbar, index) => {
            const styles = window.getComputedStyle(toolbar);
            console.log(`Toolbar ${index + 1}:`, {
                opacity: styles.opacity,
                visibility: styles.visibility,
                display: styles.display,
                position: styles.position
            });
        });

        plusButtons.forEach((button, index) => {
            const styles = window.getComputedStyle(button);
            console.log(`Plus Button ${index + 1}:`, {
                opacity: styles.opacity,
                visibility: styles.visibility,
                display: styles.display,
                width: styles.width,
                height: styles.height
            });
        });

        // Check for duplicates
        const blocks = document.querySelectorAll('.ce-block');
        let duplicateCount = 0;

        blocks.forEach((block, blockIndex) => {
            const blockToolbars = block.querySelectorAll('.ce-toolbar');
            const blockPlusButtons = block.querySelectorAll('.ce-toolbar__plus');

            if (blockToolbars.length > 1) {
                console.warn(`⚠️ Block ${blockIndex + 1} has ${blockToolbars.length} toolbars (should be 1)`);
                duplicateCount++;
            }

            if (blockPlusButtons.length > 1) {
                console.warn(`⚠️ Block ${blockIndex + 1} has ${blockPlusButtons.length} plus buttons (should be 1)`);
                duplicateCount++;
            }
        });

        if (duplicateCount > 0) {
            console.log('🔧 Removing duplicates...');
            if (window.AdZetaPostEditor && window.AdZetaPostEditor.removeDuplicateToolbars) {
                window.AdZetaPostEditor.removeDuplicateToolbars();
            }
        }

        // Force visibility and positioning
        console.log('🔧 Forcing plus button visibility and positioning...');
        toolbars.forEach(toolbar => {
            toolbar.style.opacity = '1';
            toolbar.style.visibility = 'visible';
            toolbar.style.display = 'flex';
            toolbar.style.position = 'absolute';
            toolbar.style.left = '-50px';
            toolbar.style.top = '0';
            toolbar.style.zIndex = '2';
        });

        plusButtons.forEach(button => {
            button.style.opacity = '1';
            button.style.visibility = 'visible';
            button.style.display = 'block';
            button.style.position = 'absolute';
            button.style.left = '-40px';
            button.style.top = '2px';
            button.style.width = '32px';
            button.style.height = '32px';
            button.style.zIndex = '3';
        });

        console.log('✅ Plus button visibility test completed');

        // Count visible plus buttons manually
        const allPlusButtons = document.querySelectorAll('.ce-toolbar__plus');
        let visibleCount = 0;
        allPlusButtons.forEach(button => {
            const styles = window.getComputedStyle(button);
            if (styles.display !== 'none' && styles.visibility !== 'hidden' && styles.opacity !== '0') {
                visibleCount++;
            }
        });

        console.log(`📊 Final count: ${visibleCount} visible plus buttons out of ${allPlusButtons.length} total`);
    },

    /**
     * Check Editor.js natural behavior
     */
    checkEditorJSBehavior() {
        console.log('🔍 Checking Editor.js Natural Behavior...');

        const blocks = document.querySelectorAll('.ce-block');
        console.log(`📊 Found ${blocks.length} blocks`);

        blocks.forEach((block, index) => {
            const toolbar = block.querySelector('.ce-toolbar');
            const plusButton = block.querySelector('.ce-toolbar__plus');

            console.log(`Block ${index + 1}:`);
            console.log('  Has toolbar:', !!toolbar);
            console.log('  Has plus button:', !!plusButton);

            if (toolbar) {
                const toolbarStyles = window.getComputedStyle(toolbar);
                const rect = toolbar.getBoundingClientRect();
                console.log('  Toolbar styles:', {
                    opacity: toolbarStyles.opacity,
                    visibility: toolbarStyles.visibility,
                    display: toolbarStyles.display,
                    position: toolbarStyles.position,
                    left: toolbarStyles.left,
                    top: toolbarStyles.top
                });
                console.log('  Toolbar position:', {
                    x: rect.x,
                    y: rect.y,
                    width: rect.width,
                    height: rect.height,
                    visible: rect.x >= 0 && rect.y >= 0
                });
            }

            if (plusButton) {
                const buttonStyles = window.getComputedStyle(plusButton);
                const rect = plusButton.getBoundingClientRect();
                console.log('  Plus button styles:', {
                    opacity: buttonStyles.opacity,
                    visibility: buttonStyles.visibility,
                    display: buttonStyles.display,
                    position: buttonStyles.position,
                    left: buttonStyles.left,
                    top: buttonStyles.top
                });
                console.log('  Plus button position:', {
                    x: rect.x,
                    y: rect.y,
                    width: rect.width,
                    height: rect.height,
                    visible: rect.x >= 0 && rect.y >= 0
                });
            }
        });

        // Check editor container positioning
        const editorContainer = document.getElementById('editorjs');
        if (editorContainer) {
            const containerStyles = window.getComputedStyle(editorContainer);
            const containerRect = editorContainer.getBoundingClientRect();
            console.log('Editor container:', {
                marginLeft: containerStyles.marginLeft,
                paddingLeft: containerStyles.paddingLeft,
                position: containerStyles.position,
                x: containerRect.x,
                width: containerRect.width
            });
        }

        console.log('📝 Instructions:');
        console.log('1. Hover over each block to see if toolbar appears');
        console.log('2. Click on a block to focus it and see toolbar');
        console.log('3. Check if toolbar x position is negative (cut off)');

        console.log('✅ Editor.js behavior check completed');
    },

    /**
     * Remove all extra spacing and let Editor.js work naturally
     */
    fixToolbarPositioning() {
        console.log('🔧 Removing all extra spacing and letting Editor.js work naturally...');

        // Remove all custom padding from editor container
        const editorContainer = document.getElementById('editorjs');
        if (editorContainer) {
            editorContainer.style.paddingLeft = '';
            editorContainer.style.marginLeft = '';
            console.log('✅ Removed all custom spacing from editor container');
        }

        // Remove all custom padding from card bodies
        const cardBodies = document.querySelectorAll('.card-body');
        cardBodies.forEach((cardBody, index) => {
            cardBody.style.paddingLeft = '';
            console.log(`✅ Reset card body ${index + 1} padding`);
        });

        // Reset codex editor styling
        const codexEditor = document.querySelector('.codex-editor');
        if (codexEditor) {
            codexEditor.style.paddingLeft = '';
            codexEditor.style.marginLeft = '';
            console.log('✅ Reset codex editor spacing');
        }

        // Reset redactor styling
        const redactor = document.querySelector('.codex-editor__redactor');
        if (redactor) {
            redactor.style.paddingLeft = '';
            redactor.style.marginLeft = '';
            console.log('✅ Reset redactor spacing');
        }

        // Let Editor.js handle toolbar positioning completely naturally
        const toolbars = document.querySelectorAll('.ce-toolbar');
        toolbars.forEach((toolbar, index) => {
            // Remove any custom positioning
            toolbar.style.position = '';
            toolbar.style.left = '';
            toolbar.style.top = '';
            toolbar.style.zIndex = '';
            console.log(`✅ Reset toolbar ${index + 1} to natural positioning`);
        });

        console.log('✅ All custom spacing removed - Editor.js now works naturally');
        console.log('📝 Note: Plus button should now appear in its natural Editor.js position');

        // Test the result
        setTimeout(() => {
            this.checkEditorJSBehavior();
        }, 500);
    },

    /**
     * Debug fullscreen functionality
     */
    debugFullscreen() {
        console.log('🔍 Debugging fullscreen functionality...');

        // Check if editor card exists
        const editorCard = document.querySelector('#editorjs')?.closest('.card');
        console.log('Editor card found:', !!editorCard);

        if (editorCard) {
            console.log('Editor card classes:', editorCard.className);
            console.log('Editor card position:', {
                position: getComputedStyle(editorCard).position,
                zIndex: getComputedStyle(editorCard).zIndex,
                display: getComputedStyle(editorCard).display
            });
        }

        // Check if fullscreen button exists
        const fullscreenBtn = document.querySelector('button[onclick="AdZetaPostEditor.toggleFullscreen()"]');
        console.log('Fullscreen button found:', !!fullscreenBtn);

        // Check if editor content exists
        const editorjs = document.getElementById('editorjs');
        console.log('EditorJS container found:', !!editorjs);

        if (editorjs) {
            console.log('EditorJS visibility:', {
                display: getComputedStyle(editorjs).display,
                visibility: getComputedStyle(editorjs).visibility,
                opacity: getComputedStyle(editorjs).opacity,
                height: getComputedStyle(editorjs).height
            });
        }

        // Check if codex editor exists
        const codexEditor = document.querySelector('.codex-editor');
        console.log('Codex editor found:', !!codexEditor);

        if (codexEditor) {
            console.log('Codex editor visibility:', {
                display: getComputedStyle(codexEditor).display,
                visibility: getComputedStyle(codexEditor).visibility,
                opacity: getComputedStyle(codexEditor).opacity,
                height: getComputedStyle(codexEditor).height
            });
        }

        // Test fullscreen toggle
        console.log('📝 Testing fullscreen toggle...');
        if (window.AdZetaPostEditor && typeof window.AdZetaPostEditor.toggleFullscreen === 'function') {
            console.log('✅ AdZetaPostEditor.toggleFullscreen() is available');
            console.log('📝 You can test it by running: AdZetaPostEditor.toggleFullscreen()');
        } else {
            console.log('❌ AdZetaPostEditor.toggleFullscreen() is not available');
        }

        // Check for content blocks
        const blocks = document.querySelectorAll('.ce-block');
        console.log('Content blocks found:', blocks.length);

        blocks.forEach((block, index) => {
            const blockStyles = getComputedStyle(block);
            console.log(`Block ${index + 1}:`, {
                display: blockStyles.display,
                visibility: blockStyles.visibility,
                opacity: blockStyles.opacity,
                height: blockStyles.height
            });
        });

        // Check redactor
        const redactor = document.querySelector('.codex-editor__redactor');
        if (redactor) {
            console.log('Redactor styles:', {
                display: getComputedStyle(redactor).display,
                height: getComputedStyle(redactor).height,
                minHeight: getComputedStyle(redactor).minHeight
            });
        }

        console.log('✅ Fullscreen debug completed');

        // Provide fix suggestions
        console.log('🔧 If content is still not visible, try:');
        console.log('  debugAI.forceFullscreenVisibility() - Force all elements visible');
    },

    /**
     * Force fullscreen visibility
     */
    forceFullscreenVisibility() {
        console.log('🔧 Forcing fullscreen visibility...');

        const editorCard = document.querySelector('.fullscreen-editor');
        if (!editorCard) {
            console.log('❌ No fullscreen editor found');
            return;
        }

        // Force editor visibility
        const editorjs = document.getElementById('editorjs');
        if (editorjs) {
            editorjs.style.display = 'block';
            editorjs.style.visibility = 'visible';
            editorjs.style.opacity = '1';
            editorjs.style.height = '100%';
            editorjs.style.minHeight = '500px';
            console.log('✅ Forced EditorJS visibility');
        }

        // Force codex editor visibility
        const codexEditor = document.querySelector('.fullscreen-editor .codex-editor');
        if (codexEditor) {
            codexEditor.style.display = 'block';
            codexEditor.style.visibility = 'visible';
            codexEditor.style.opacity = '1';
            codexEditor.style.height = '100%';
            codexEditor.style.minHeight = '500px';
            console.log('✅ Forced Codex Editor visibility');
        }

        // Force redactor visibility
        const redactor = document.querySelector('.fullscreen-editor .codex-editor__redactor');
        if (redactor) {
            redactor.style.display = 'block';
            redactor.style.visibility = 'visible';
            redactor.style.opacity = '1';
            redactor.style.minHeight = '400px';
            console.log('✅ Forced Redactor visibility');
        }

        // Force all blocks visibility
        const blocks = document.querySelectorAll('.fullscreen-editor .ce-block');
        blocks.forEach((block, index) => {
            block.style.display = 'block';
            block.style.visibility = 'visible';
            block.style.opacity = '1';
        });
        console.log(`✅ Forced ${blocks.length} blocks visibility`);

        // Force card body layout
        const cardBody = document.querySelector('.fullscreen-editor .card-body');
        if (cardBody) {
            cardBody.style.display = 'flex';
            cardBody.style.flexDirection = 'column';
            cardBody.style.height = '100%';
            console.log('✅ Forced card body layout');
        }

        console.log('✅ Fullscreen visibility forced - content should now be visible');
    },

    /**
     * Test HTML entity decoding fix
     */
    async testHTMLEntityDecoding() {
        console.log('🔧 TESTING HTML ENTITY DECODING FIX');
        console.log('=========================================');

        // Test the decoding with sample data from your database
        const testData = {
            paragraph: "&lt;strong&gt;Practice Active Listening:&lt;\/strong&gt; Truly listen to what others have to say.",
            listItem: "&lt;strong&gt;Gratitude Journal:&lt;\/strong&gt; Write down three things you're grateful for each day.",
            header: "Proven Strategies for a Better Life: Happiness, Health &amp;amp; Success"
        };

        console.log('📝 Original encoded data:');
        console.log('  Paragraph:', testData.paragraph);
        console.log('  List item:', testData.listItem);
        console.log('  Header:', testData.header);

        // Test HTML entity decoding (simulate what EditorJSParser does)
        console.log('\n🔄 Testing HTML entity decoding...');

        // Create a test function that mimics the PHP decoding
        function decodeHTMLEntities(text) {
            // Create a temporary element to decode HTML entities
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = text;
            let decoded = tempDiv.textContent || tempDiv.innerText || '';

            // Handle double-encoded entities
            decoded = decoded.replace(/&amp;lt;/g, '<');
            decoded = decoded.replace(/&amp;gt;/g, '>');
            decoded = decoded.replace(/&amp;amp;/g, '&');

            return decoded;
        }

        const decodedData = {
            paragraph: decodeHTMLEntities(testData.paragraph),
            listItem: decodeHTMLEntities(testData.listItem),
            header: decodeHTMLEntities(testData.header)
        };

        console.log('📊 Decoded results:');
        console.log('  Paragraph:', decodedData.paragraph);
        console.log('  List item:', decodedData.listItem);
        console.log('  Header:', decodedData.header);

        // Check if HTML tags are properly restored
        const hasStrongTags = decodedData.paragraph.includes('<strong>') && decodedData.listItem.includes('<strong>');
        const hasProperAmpersand = decodedData.header.includes('&') && !decodedData.header.includes('&amp;');

        console.log('\n✅ DECODING RESULTS:');
        console.log(`  <strong> tags restored: ${hasStrongTags ? '✅ YES' : '❌ NO'}`);
        console.log(`  Ampersands fixed: ${hasProperAmpersand ? '✅ YES' : '❌ NO'}`);

        if (hasStrongTags && hasProperAmpersand) {
            console.log('\n🎉 SUCCESS: HTML entity decoding is working!');
            console.log('Your frontend should now display proper formatting instead of HTML code.');
        } else {
            console.log('\n❌ ISSUE: HTML entity decoding needs adjustment.');
        }

        return {
            original: testData,
            decoded: decodedData,
            success: hasStrongTags && hasProperAmpersand
        };
    },

    /**
     * Test the official Editor.js HTML-preserving tools
     */
    async testOfficialHTMLTools() {
        console.log('🔧 TESTING OFFICIAL EDITOR.JS HTML-PRESERVING TOOLS');
        console.log('====================================================');

        if (!window.AdZetaPostEditor || !window.AdZetaPostEditor.state.editor) {
            console.log('❌ Editor not available. Go to post editor first.');
            return;
        }

        const editor = window.AdZetaPostEditor.state.editor;

        try {
            // Save current content
            const currentData = await editor.save();
            console.log('💾 Saved current content for restoration');

            // Test content with HTML formatting
            const testBlocks = {
                blocks: [
                    {
                        type: 'paragraph',
                        data: {
                            text: 'This paragraph has <strong>bold text</strong> and <em>italic text</em> and <span style="color: red;">colored text</span>.'
                        }
                    },
                    {
                        type: 'header',
                        data: {
                            text: 'This header has <strong>bold formatting</strong>',
                            level: 2
                        }
                    },
                    {
                        type: 'paragraph',
                        data: {
                            text: 'Another paragraph with <code>inline code</code> and <a href="#">a link</a>.'
                        }
                    }
                ]
            };

            console.log('📤 Inserting test content with HTML formatting...');
            await editor.render(testBlocks);

            // Wait for Editor.js to process
            setTimeout(async () => {
                try {
                    console.log('🔍 Testing HTML preservation after save...');
                    const savedData = await editor.save();

                    console.log('📊 RESULTS:');
                    savedData.blocks.forEach((block, index) => {
                        console.log(`\nBlock ${index + 1} (${block.type}):`);
                        console.log('  Text:', block.data.text);

                        if (block.data.text) {
                            const hasStrong = block.data.text.includes('<strong>');
                            const hasEm = block.data.text.includes('<em>');
                            const hasSpan = block.data.text.includes('<span');
                            const hasCode = block.data.text.includes('<code>');
                            const hasLink = block.data.text.includes('<a ');

                            console.log('  HTML Tags Preserved:');
                            console.log(`    - <strong>: ${hasStrong ? '✅' : '❌'}`);
                            console.log(`    - <em>: ${hasEm ? '✅' : '❌'}`);
                            console.log(`    - <span>: ${hasSpan ? '✅' : '❌'}`);
                            console.log(`    - <code>: ${hasCode ? '✅' : '❌'}`);
                            console.log(`    - <a>: ${hasLink ? '✅' : '❌'}`);

                            const allPreserved = hasStrong && hasEm && (hasSpan || hasCode || hasLink);
                            console.log(`  Overall: ${allPreserved ? '✅ SUCCESS' : '❌ FAILED'}`);
                        }
                    });

                    // Restore original content
                    setTimeout(async () => {
                        await editor.render(currentData);
                        console.log('🔄 Original content restored');
                    }, 2000);

                } catch (saveError) {
                    console.error('❌ Error testing save:', saveError);
                }
            }, 1500);

        } catch (error) {
            console.error('❌ Custom HTML tools test failed:', error);
        }
    },

    /**
     * Debug database content vs template rendering
     */
    async debugDatabaseContent() {
        console.log('🔍 DATABASE CONTENT DEBUG');
        console.log('=====================================');

        // Get current post ID from URL or page
        const urlParams = new URLSearchParams(window.location.search);
        const postId = urlParams.get('id') || urlParams.get('post_id');

        if (!postId) {
            console.log('⚠️ No post ID found in URL. Try this on an edit page.');
            return;
        }

        try {
            // Fetch post data from API
            console.log('📤 Fetching post data for ID:', postId);
            const response = await window.AdZetaApp.apiRequest(`/posts/${postId}`);

            if (response.success && response.post) {
                const post = response.post;

                console.log('📊 POST DATA ANALYSIS:');
                console.log('  Title:', post.title);
                console.log('  Status:', post.status);
                console.log('  Template:', post.template);

                // Check content field
                console.log('\n📄 CONTENT FIELD:');
                if (post.content) {
                    console.log('  Length:', post.content.length);
                    console.log('  Preview:', post.content.substring(0, 300) + '...');

                    // Check for escaped HTML in content
                    const hasEscapedHTML = post.content.includes('&lt;') || post.content.includes('&gt;');
                    console.log('  Contains escaped HTML:', hasEscapedHTML);

                    if (hasEscapedHTML) {
                        console.log('  ❌ ISSUE: Content field has escaped HTML!');
                        const escapedCount = (post.content.match(/&lt;\w+&gt;/g) || []).length;
                        console.log('  Escaped tags found:', escapedCount);
                    }
                } else {
                    console.log('  ⚠️ Content field is empty');
                }

                // Check content_blocks field
                console.log('\n🧩 CONTENT_BLOCKS FIELD:');
                if (post.content_blocks) {
                    try {
                        const blocks = typeof post.content_blocks === 'string' ?
                            JSON.parse(post.content_blocks) : post.content_blocks;

                        console.log('  Type:', typeof blocks);
                        console.log('  Structure:', Object.keys(blocks));

                        if (blocks.blocks && Array.isArray(blocks.blocks)) {
                            console.log('  Blocks count:', blocks.blocks.length);
                            console.log('  Block types:', blocks.blocks.map(b => b.type));

                            // Check first few blocks for HTML
                            blocks.blocks.slice(0, 3).forEach((block, index) => {
                                console.log(`  Block ${index + 1} (${block.type}):`, block.data.text?.substring(0, 100) + '...');

                                if (block.data.text) {
                                    const hasHTML = block.data.text.includes('<');
                                    const hasEscapedHTML = block.data.text.includes('&lt;');
                                    console.log(`    - Contains HTML: ${hasHTML}`);
                                    console.log(`    - Contains escaped HTML: ${hasEscapedHTML}`);
                                }
                            });
                        } else {
                            console.log('  ⚠️ Invalid blocks structure');
                        }
                    } catch (error) {
                        console.log('  ❌ Error parsing content_blocks:', error.message);
                    }
                } else {
                    console.log('  ⚠️ Content_blocks field is empty');
                }

                // Determine what template will use
                console.log('\n🌐 TEMPLATE RENDERING LOGIC:');
                if (post.content_blocks && typeof post.content_blocks === 'object') {
                    console.log('  ✅ Template will use: EditorJS Parser (content_blocks)');
                } else if (post.content_blocks && typeof post.content_blocks === 'string') {
                    console.log('  ✅ Template will use: EditorJS Parser (parsed content_blocks)');
                } else {
                    console.log('  ⚠️ Template will use: Fallback (content field)');
                    console.log('  This is why you\'re seeing escaped HTML!');
                }

            } else {
                console.error('❌ Failed to fetch post data:', response);
            }

        } catch (error) {
            console.error('❌ Database content debug failed:', error);
        }
    },

    /**
     * Debug frontend HTML rendering
     */
    async debugFrontendHTMLRendering() {
        console.log('🔍 FRONTEND HTML RENDERING DEBUG');
        console.log('=====================================');

        // Check if we're on a blog post page
        const postContent = document.querySelector('.post-content, .blog-post-content, .article-content');
        if (!postContent) {
            console.log('⚠️ Not on a blog post page or content container not found');
            return;
        }

        console.log('📝 Found post content container:', postContent.className);

        // Get the raw HTML content
        const htmlContent = postContent.innerHTML;
        console.log('📄 Raw HTML content (first 500 chars):');
        console.log(htmlContent.substring(0, 500) + '...');

        // Check for HTML tags in content
        const hasStrongTags = htmlContent.includes('<strong>');
        const hasEmTags = htmlContent.includes('<em>');
        const hasSpanTags = htmlContent.includes('<span');
        const hasPTags = htmlContent.includes('<p>');

        console.log('\n🔍 HTML Tags Analysis:');
        console.log(`  - <p> tags found: ${hasPTags}`);
        console.log(`  - <strong> tags found: ${hasStrongTags}`);
        console.log(`  - <em> tags found: ${hasEmTags}`);
        console.log(`  - <span> tags found: ${hasSpanTags}`);

        // Check for escaped HTML (text content)
        const textContent = postContent.textContent;
        const hasEscapedStrong = textContent.includes('<strong>');
        const hasEscapedEm = textContent.includes('<em>');

        console.log('\n🔍 Escaped HTML Analysis:');
        console.log(`  - Escaped <strong> found in text: ${hasEscapedStrong}`);
        console.log(`  - Escaped <em> found in text: ${hasEscapedEm}`);

        if (hasEscapedStrong || hasEscapedEm) {
            console.log('❌ ISSUE: HTML tags are being displayed as text instead of rendered!');
            console.log('📝 Text content sample:');
            console.log(textContent.substring(0, 300) + '...');
        } else if (hasStrongTags || hasEmTags) {
            console.log('✅ SUCCESS: HTML tags are being rendered correctly!');
        } else {
            console.log('⚠️ No formatting tags found in content');
        }

        // Check if content is coming from EditorJS or fallback
        const hasEditorJSComment = htmlContent.includes('EditorJS');
        console.log(`\n🔧 Content source: ${hasEditorJSComment ? 'EditorJS Parser' : 'Fallback content field'}`);

        return {
            hasHTMLTags: hasStrongTags || hasEmTags || hasSpanTags,
            hasEscapedHTML: hasEscapedStrong || hasEscapedEm,
            contentSource: hasEditorJSComment ? 'editorjs' : 'fallback',
            htmlContent: htmlContent,
            textContent: textContent
        };
    },

    /**
     * Comprehensive HTML stripping debug
     */
    async debugHTMLStripping() {
        console.log('🔍 COMPREHENSIVE HTML STRIPPING DEBUG');
        console.log('=====================================');

        const testHTML = '<p>Test with <strong>bold</strong> and <em>italic</em> text.</p>';
        console.log('📝 Original HTML:', testHTML);

        // Test 1: Frontend processing
        console.log('\n🔧 TEST 1: Frontend HTML Processing');
        if (window.AdZetaPostEditor) {
            try {
                // Test preserveInlineHTML method
                const preserved = window.AdZetaPostEditor.preserveInlineHTML(testHTML);
                console.log('  preserveInlineHTML result:', preserved);

                // Test cleanAIMarkersFromText method
                const cleaned = window.AdZetaPostEditor.cleanAIMarkersFromText(testHTML);
                console.log('  cleanAIMarkersFromText result:', cleaned);

            } catch (error) {
                console.error('  ❌ Frontend processing error:', error);
            }
        }

        // Test 2: Editor.js behavior
        console.log('\n🔧 TEST 2: Editor.js HTML Handling');
        if (window.AdZetaPostEditor && window.AdZetaPostEditor.state.editor) {
            try {
                // Save current content
                const currentData = await window.AdZetaPostEditor.state.editor.save();
                console.log('  Current editor blocks:', currentData.blocks.length);

                // Test inserting HTML content with formatting
                const testBlocks = {
                    blocks: [
                        {
                            type: 'paragraph',
                            data: {
                                text: 'Test paragraph with <strong>bold</strong> and <em>italic</em> text.'
                            }
                        },
                        {
                            type: 'header',
                            data: {
                                text: 'Header with <strong>bold formatting</strong>',
                                level: 2
                            }
                        }
                    ]
                };

                console.log('  📤 Inserting test blocks with HTML formatting...');
                await window.AdZetaPostEditor.state.editor.render(testBlocks);

                // Check what Editor.js saved after a delay
                setTimeout(async () => {
                    try {
                        const newData = await window.AdZetaPostEditor.state.editor.save();
                        console.log('  📊 Editor.js saved blocks:');

                        newData.blocks.forEach((block, index) => {
                            console.log(`    Block ${index + 1} (${block.type}):`, block.data.text);

                            if (block.data.text) {
                                const hasStrong = block.data.text.includes('<strong>');
                                const hasEm = block.data.text.includes('<em>');
                                console.log(`      - <strong> preserved: ${hasStrong}`);
                                console.log(`      - <em> preserved: ${hasEm}`);

                                if (hasStrong && hasEm) {
                                    console.log('      ✅ HTML formatting PRESERVED!');
                                } else {
                                    console.log('      ❌ HTML formatting STRIPPED!');
                                }
                            }
                        });

                        // Restore original content
                        await window.AdZetaPostEditor.state.editor.render(currentData);
                        console.log('  🔄 Original content restored');

                    } catch (saveError) {
                        console.error('  ❌ Error saving editor data:', saveError);
                    }
                }, 1500);

            } catch (error) {
                console.error('  ❌ Editor.js test error:', error);
            }
        }

        // Test 3: Source view switching
        console.log('\n🔧 TEST 3: Source View Switching');
        this.testSourceViewSwitching(testHTML);

        // Test 4: Backend API
        console.log('\n🔧 TEST 4: Backend API Processing');
        await this.testBackendHTMLPreservation();
    },

    /**
     * Test source view switching
     */
    testSourceViewSwitching(testHTML) {
        console.log('  Testing source view HTML preservation...');

        // Check if source view is available
        const sourceBtn = document.querySelector('button[onclick="AdZetaPostEditor.toggleSourceView()"]');
        if (!sourceBtn) {
            console.log('  ⚠️ Source view button not found');
            return;
        }

        console.log('  📝 Source view button found, testing switching...');

        // Monitor source view changes
        const originalToggle = window.AdZetaPostEditor.toggleSourceView;
        window.AdZetaPostEditor.toggleSourceView = function() {
            console.log('  🔄 Source view toggle called');
            const result = originalToggle.call(this);

            // Check source editor content
            setTimeout(() => {
                const sourceEditor = document.getElementById('sourceEditor');
                if (sourceEditor) {
                    console.log('  📄 Source editor content:', sourceEditor.value.substring(0, 200) + '...');
                }
            }, 500);

            return result;
        };
    },

    /**
     * Test backend HTML preservation
     */
    async testBackendHTMLPreservation() {
        console.log('🔧 Testing backend HTML preservation...');

        // Test content with HTML formatting
        const testData = {
            title: 'HTML Preservation Test',
            content: '<p>This is a test with <strong>bold text</strong> and <em>italic text</em>.</p><p>Here is some <span style="color: red;">colored text</span> and <code>inline code</code>.</p>',
            excerpt: 'Test excerpt with <strong>formatting</strong>',
            meta_description: 'Meta description with <em>emphasis</em>',
            status: 'draft'
        };

        console.log('📝 Original content:', testData.content);

        try {
            // Test creating a post
            console.log('📤 Sending test post to backend...');
            const response = await window.AdZetaApp.apiRequest('/posts', {
                method: 'POST',
                body: JSON.stringify(testData)
            });

            console.log('📥 Backend response:', response);

            if (response.success && response.post) {
                const savedContent = response.post.content;
                console.log('💾 Saved content:', savedContent);

                // Check if HTML tags are preserved
                const hasStrongTags = savedContent.includes('<strong>');
                const hasEmTags = savedContent.includes('<em>');
                const hasSpanTags = savedContent.includes('<span');

                console.log('✅ HTML Preservation Results:');
                console.log(`  - <strong> tags preserved: ${hasStrongTags}`);
                console.log(`  - <em> tags preserved: ${hasEmTags}`);
                console.log(`  - <span> tags preserved: ${hasSpanTags}`);

                if (hasStrongTags && hasEmTags && hasSpanTags) {
                    console.log('🎉 SUCCESS: HTML formatting preserved in backend!');
                } else {
                    console.log('❌ ISSUE: Some HTML tags were stripped in backend');
                }

                // Clean up test post
                try {
                    await window.AdZetaApp.apiRequest(`/posts/${response.post.id}`, {
                        method: 'DELETE'
                    });
                    console.log('🗑️ Test post cleaned up');
                } catch (cleanupError) {
                    console.log('⚠️ Could not clean up test post:', cleanupError);
                }

            } else {
                console.error('❌ Failed to create test post:', response);
            }

        } catch (error) {
            console.error('❌ Backend HTML preservation test failed:', error);
        }
    },

    /**
     * Test HTML preservation in Editor.js
     */
    testHTMLPreservation() {
        console.log('🔧 Testing HTML preservation in Editor.js...');

        // Test content with HTML formatting
        const testHTML = `
            <p>This is a paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>
            <p>Here's some <span style="color: red;">colored text</span> and <code>inline code</code>.</p>
            <h2>This is a <strong>bold heading</strong></h2>
            <p>Testing <mark>highlighted text</mark> and <u>underlined text</u>.</p>
        `;

        console.log('📝 Original HTML:', testHTML);

        if (window.AdZetaPostEditor) {
            try {
                // Test HTML to blocks conversion
                console.log('🔄 Converting HTML to Editor.js blocks...');
                window.AdZetaPostEditor.convertHTMLToEditorBlocks(testHTML);

                // Wait a moment then check the editor content
                setTimeout(async () => {
                    if (window.AdZetaPostEditor.state.editor) {
                        const editorData = await window.AdZetaPostEditor.state.editor.save();
                        console.log('📊 Editor blocks:', editorData.blocks);

                        // Check if HTML is preserved in the blocks
                        editorData.blocks.forEach((block, index) => {
                            console.log(`Block ${index + 1} (${block.type}):`, block.data.text);

                            if (block.data.text) {
                                const hasHTML = /<[^>]+>/.test(block.data.text);
                                console.log(`  ✅ Contains HTML: ${hasHTML}`);

                                if (hasHTML) {
                                    const strongTags = (block.data.text.match(/<strong>/g) || []).length;
                                    const emTags = (block.data.text.match(/<em>/g) || []).length;
                                    const spanTags = (block.data.text.match(/<span[^>]*>/g) || []).length;

                                    console.log(`    - <strong> tags: ${strongTags}`);
                                    console.log(`    - <em> tags: ${emTags}`);
                                    console.log(`    - <span> tags: ${spanTags}`);
                                }
                            }
                        });
                    }
                }, 1000);

            } catch (error) {
                console.error('❌ Error testing HTML preservation:', error);
            }
        } else {
            console.error('❌ AdZetaPostEditor not available');
        }
    },

    /**
     * Quick opacity fix for fullscreen
     */
    fixFullscreenOpacity() {
        console.log('🔧 Fixing fullscreen opacity issues...');

        // Fix block opacity
        const blocks = document.querySelectorAll('.fullscreen-editor .ce-block');
        blocks.forEach(block => {
            block.style.opacity = '1';
            block.style.visibility = 'visible';
            block.style.display = 'block';
        });

        // Fix content opacity
        const contents = document.querySelectorAll('.fullscreen-editor .ce-block__content, .fullscreen-editor .ce-paragraph, .fullscreen-editor .ce-header');
        contents.forEach(content => {
            content.style.opacity = '1';
            content.style.visibility = 'visible';
            content.style.display = 'block';
        });

        // Fix redactor min-height
        const redactor = document.querySelector('.fullscreen-editor .codex-editor__redactor');
        if (redactor) {
            redactor.style.minHeight = '400px';
            redactor.style.height = 'auto';
        }

        console.log(`✅ Fixed opacity for ${blocks.length} blocks and ${contents.length} content elements`);
        console.log('✅ Content should now be visible in fullscreen!');
    },

    /**
     * Run comprehensive debug test
     */
    runFullDebugTest() {
        console.log('🚀 Running Full AI Content Debug Test...');
        console.log('='.repeat(50));

        // Test 1: Custom tools
        this.testCustomTools();
        console.log('-'.repeat(30));

        // Test 2: AI Content Processor
        this.testAIContentProcessing();
        console.log('-'.repeat(30));

        // Test 3: Editor instance
        this.testEditorInstance();
        console.log('-'.repeat(30));

        // Test 4: Editor.js natural behavior
        this.checkEditorJSBehavior();
        console.log('-'.repeat(30));

        // Test 5: Simulate AI insertion
        this.simulateAIContentInsertion();

        console.log('='.repeat(50));
        console.log('🏁 Debug test completed. Check console for results.');
    },

    /**
     * Monitor Editor.js events
     */
    monitorEditorEvents() {
        console.log('👀 Monitoring Editor.js events...');

        if (window.AdZetaPostEditor && window.AdZetaPostEditor.state.editor) {
            const editor = window.AdZetaPostEditor.state.editor;

            // Monitor render events
            const originalRender = editor.render;
            editor.render = function(data) {
                console.log('📝 Editor.render called with:', data);
                return originalRender.call(this, data);
            };

            console.log('✅ Editor events monitoring enabled');
        } else {
            console.error('❌ Cannot monitor - Editor not available');
        }
    }
};

// Auto-run debug test when script loads
console.log('🔧 AI Content Debugger loaded.');
console.log('📝 Available commands:');
console.log('  debugAI.runFullDebugTest() - Run comprehensive test');
console.log('  debugAI.debugHTMLStripping() - 🔥 COMPREHENSIVE HTML stripping debug');
console.log('  debugAI.testHTMLEntityDecoding() - 🔄 Test HTML entity decoding fix');
console.log('  debugAI.testOfficialHTMLTools() - 🔧 Test official Editor.js HTML-preserving tools');
console.log('  debugAI.debugDatabaseContent() - 💾 Debug database content vs template');
console.log('  debugAI.debugFrontendHTMLRendering() - 🌐 Debug frontend HTML rendering');
console.log('  debugAI.checkEditorJSBehavior() - Check Editor.js natural behavior');
console.log('  debugAI.fixToolbarPositioning() - Fix plus button positioning issues');
console.log('  debugAI.debugFullscreen() - Debug fullscreen functionality');
console.log('  debugAI.forceFullscreenVisibility() - Force fullscreen content visible');
console.log('  debugAI.testHTMLPreservation() - Test HTML tag preservation in frontend');
console.log('  debugAI.testBackendHTMLPreservation() - Test HTML tag preservation in backend');
console.log('  debugAI.testPlusButtonVisibility() - Test plus button visibility');
console.log('  debugAI.testAIContentProcessing() - Test AI content processing');

// Add to global scope for easy access
window.debugAI = window.AIContentDebugger;
