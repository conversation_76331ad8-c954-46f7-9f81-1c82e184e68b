<?php
/**
 * AdZeta Website Configuration
 * Main configuration file for the dynamic website system
 */

// Prevent direct access
if (!defined('ADZETA_INIT')) {
    die('Direct access not permitted');
}

// Environment Configuration
define('ENVIRONMENT', 'development'); // development, staging, production

// Database Configuration (DISABLED FOR NOW)
// define('DB_HOST', 'localhost');
// define('DB_NAME', 'adzeta_website');
// define('DB_USER', 'root');
// define('DB_PASS', '');
// define('DB_CHARSET', 'utf8mb4');
define('USE_DATABASE', false);

// Site Configuration
define('SITE_NAME', 'AdZeta');
define('SITE_TAGLINE', 'AI-Powered Value-Based Bidding for E-commerce Growth');
define('SITE_URL', 'http://localhost/adzeta');
define('ADMIN_EMAIL', '<EMAIL>');

// Path Configuration
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('TEMPLATES_PATH', ROOT_PATH . '/templates');
define('CACHE_PATH', ROOT_PATH . '/cache');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// URL Configuration - Updated for localhost development
if (!defined('BASE_URL')) {
    // Auto-detect localhost configuration
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

    // Extract directory from script name (e.g., /adzeta/ from /adzeta/index.php)
    $directory = dirname($scriptName);
    if ($directory === '/' || $directory === '\\') {
        $directory = '';
    }

    // For localhost development, you can also manually set this:
    // define('BASE_URL', 'http://localhost/adzeta/');
    // Or if using XAMPP/WAMP with different port:
    // define('BASE_URL', 'http://localhost:8080/adzeta/');

    // Auto-detection (recommended for flexibility)
    define('BASE_URL', $protocol . $host . $directory . '/');
}

// Twig Configuration
define('TWIG_CACHE', ENVIRONMENT === 'production');
define('TWIG_DEBUG', ENVIRONMENT === 'development');

// SEO Defaults
define('DEFAULT_META_DESCRIPTION', 'Maximize e-commerce profitability with AdZeta\'s AI-powered Value-Based Bidding. Our predictive algorithms target high-LTV customers for sustainable growth and lower CAC.');
define('DEFAULT_META_KEYWORDS', 'value-based bidding, e-commerce advertising, AI predictive bidding, D2C growth agency, sustainable e-commerce growth, AI marketing solutions, lower CAC e-commerce, increase ROAS e-commerce');
define('DEFAULT_OG_IMAGE', BASE_URL . 'images/adzeta-social-share.jpg');

// Security Configuration
define('SESSION_LIFETIME', 3600); // 1 hour
define('CSRF_TOKEN_NAME', 'adzeta_csrf_token');
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Cache Configuration
define('CACHE_ENABLED', ENVIRONMENT === 'production');
define('CACHE_LIFETIME', 3600); // 1 hour

// Error Reporting
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Timezone
date_default_timezone_set('UTC');

// Auto-create directories if they don't exist
$directories = [CACHE_PATH, UPLOADS_PATH];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
