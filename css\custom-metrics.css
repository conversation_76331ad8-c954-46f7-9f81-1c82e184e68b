/* Professional Enterprise Metrics Styles */

/* Metrics section container */
.metrics-section {
    padding: 60px 0;
    margin-bottom: 40px;
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
}

/* Metric card */
.metric-card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
}

/* Metric badge */
.metric-badge {
    font-size: 13px;
    font-weight: 600;
    color: #555;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    margin-bottom: 20px;
    display: block;
}

/* Metric value */
.metric-value {
    font-size: 56px;
    line-height: 1;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #8f76f5, #e958a1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Trend indicator */
.trend-indicator {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 20px;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.03);
    width: fit-content;
}

.trend-up {
    color: #2ecc71;
}

.trend-down {
    color: #3498db;
}

.trend-indicator i {
    margin-right: 6px;
    font-size: 14px;
}

/* Description text */
.metric-description {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 25px;
}

/* Graph styling */
.metric-graph {
    position: relative;
    height: 60px;
    margin-top: auto;
    border-top: 1px solid rgba(0,0,0,0.05);
    padding-top: 15px;
}

.metric-graph svg {
    width: 100%;
    height: 100%;
}

.metric-graph .area {
    fill: rgba(143, 118, 245, 0.1);
}

.metric-graph .line {
    stroke: #8f76f5;
    stroke-width: 2;
    fill: none;
}

/* Color variations for each card */
.row > div:nth-child(1) .metric-card {
    border-left-color: #8f76f5;
}

.row > div:nth-child(2) .metric-card {
    border-left-color: #e958a1;
}

.row > div:nth-child(3) .metric-card {
    border-left-color: #8f76f5;
}

.row > div:nth-child(4) .metric-card {
    border-left-color: #e958a1;
}

.row > div:nth-child(1) .metric-graph .line {
    stroke: #8f76f5;
}

.row > div:nth-child(2) .metric-graph .line {
    stroke: #e958a1;
}

.row > div:nth-child(3) .metric-graph .line {
    stroke: #8f76f5;
}

.row > div:nth-child(4) .metric-graph .line {
    stroke: #e958a1;
}

.row > div:nth-child(1) .metric-graph .area {
    fill: rgba(143, 118, 245, 0.1);
}

.row > div:nth-child(2) .metric-graph .area {
    fill: rgba(233, 88, 161, 0.1);
}

.row > div:nth-child(3) .metric-graph .area {
    fill: rgba(143, 118, 245, 0.1);
}

.row > div:nth-child(4) .metric-graph .area {
    fill: rgba(233, 88, 161, 0.1);
}

/* Metrics footer */
.metrics-footer {
    text-align: center;
    color: #666;
    font-size: 13px;
    margin-top: 40px;
    padding-top: 15px;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.metrics-footer a {
    color: #8f76f5;
    text-decoration: none;
    font-weight: 500;
}

.metrics-footer a:hover {
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .metrics-section {
        padding: 50px 0;
    }

    .metric-value {
        font-size: 48px;
    }

    .metric-card {
        margin-bottom: 25px;
    }
}

@media (max-width: 767px) {
    .metrics-section {
        padding: 40px 0;
    }

    .metric-value {
        font-size: 42px;
    }

    .metric-graph {
        height: 50px;
    }

    .metric-card {
        padding: 25px;
    }
}
