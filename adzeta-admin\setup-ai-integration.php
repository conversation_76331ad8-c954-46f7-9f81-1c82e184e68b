<?php
/**
 * AdZeta AI Integration Setup
 * Sets up database tables and default settings for AI integration
 */

require_once __DIR__ . '/bootstrap.php';

try {
    echo "Setting up AdZeta AI Integration...\n";

    // Get database connection
    global $admin_db;
    if (!$admin_db) {
        throw new Exception('Database connection not available');
    }

    // Check if settings table exists
    $tableExists = $admin_db->fetchColumn("SHOW TABLES LIKE 'settings'");
    if (!$tableExists) {
        echo "Creating settings table...\n";
        $admin_db->execute("
            CREATE TABLE settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value LONGTEXT,
                setting_type ENUM('string', 'text', 'number', 'boolean', 'json') DEFAULT 'string',
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_key (setting_key)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    // Insert default AI settings
    $defaultSettings = [
        [
            'setting_key' => 'gemini_api_keys',
            'setting_value' => '[]',
            'setting_type' => 'json',
            'description' => 'Google Gemini API keys configuration with automatic failover'
        ],
        [
            'setting_key' => 'ai_gemini_model',
            'setting_value' => 'gemini-2.5-flash',
            'setting_type' => 'string',
            'description' => 'Selected Gemini model for AI content generation'
        ],
        [
            'setting_key' => 'ai_default_temperature',
            'setting_value' => '0.7',
            'setting_type' => 'number',
            'description' => 'Default temperature for AI content generation (0-1, higher = more creative)'
        ],
        [
            'setting_key' => 'ai_max_output_tokens',
            'setting_value' => '2048',
            'setting_type' => 'number',
            'description' => 'Maximum number of tokens for AI responses'
        ],
        [
            'setting_key' => 'ai_auto_suggestions_enabled',
            'setting_value' => '0',
            'setting_type' => 'boolean',
            'description' => 'Enable automatic AI suggestions while typing'
        ],
        [
            'setting_key' => 'ai_seo_analysis_enabled',
            'setting_value' => '1',
            'setting_type' => 'boolean',
            'description' => 'Enable automatic SEO analysis and recommendations'
        ],
        [
            'setting_key' => 'ai_content_assistance_enabled',
            'setting_value' => '1',
            'setting_type' => 'boolean',
            'description' => 'Enable AI content writing assistance'
        ],
        [
            'setting_key' => 'ai_title_generation_enabled',
            'setting_value' => '1',
            'setting_type' => 'boolean',
            'description' => 'Enable AI title generation suggestions'
        ],
        [
            'setting_key' => 'ai_meta_generation_enabled',
            'setting_value' => '1',
            'setting_type' => 'boolean',
            'description' => 'Enable AI meta description generation'
        ],
        [
            'setting_key' => 'ai_tag_generation_enabled',
            'setting_value' => '1',
            'setting_type' => 'boolean',
            'description' => 'Enable AI tag suggestions'
        ]
    ];

    echo "Inserting default AI settings...\n";
    foreach ($defaultSettings as $setting) {
        // Check if setting already exists
        $existing = $admin_db->fetch(
            "SELECT id FROM settings WHERE setting_key = ?",
            [$setting['setting_key']]
        );

        if (!$existing) {
            $admin_db->insert('settings', $setting);
            echo "  ✓ Added setting: {$setting['setting_key']}\n";
        } else {
            echo "  - Setting already exists: {$setting['setting_key']}\n";
        }
    }

    // Create AI usage logs table (optional, for tracking usage)
    echo "Creating AI usage logs table...\n";
    $admin_db->execute("
        CREATE TABLE IF NOT EXISTS ai_usage_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action_type VARCHAR(50) NOT NULL,
            prompt_tokens INT DEFAULT 0,
            completion_tokens INT DEFAULT 0,
            total_tokens INT DEFAULT 0,
            model_used VARCHAR(100),
            success BOOLEAN DEFAULT TRUE,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_action_type (action_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    // Create AI content cache table (for caching AI responses)
    echo "Creating AI content cache table...\n";
    $admin_db->execute("
        CREATE TABLE IF NOT EXISTS ai_content_cache (
            id INT AUTO_INCREMENT PRIMARY KEY,
            cache_key VARCHAR(255) NOT NULL UNIQUE,
            prompt_hash VARCHAR(64) NOT NULL,
            response_content LONGTEXT NOT NULL,
            model_used VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL DEFAULT NULL,
            INDEX idx_cache_key (cache_key),
            INDEX idx_prompt_hash (prompt_hash),
            INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");

    echo "\n✅ AI Integration setup completed successfully!\n\n";
    echo "Next steps:\n";
    echo "1. Go to Admin Panel > AI Assistant\n";
    echo "2. Add your Google Gemini API keys\n";
    echo "3. Test the connection\n";
    echo "4. Start using AI features in the post editor\n\n";
    echo "Features available:\n";
    echo "- AI-powered title generation\n";
    echo "- Automatic meta description creation\n";
    echo "- Content tag suggestions\n";
    echo "- SEO analysis and recommendations\n";
    echo "- Content writing assistance\n\n";

} catch (Exception $e) {
    echo "❌ Error setting up AI integration: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
