<?php
/**
 * AdZeta Admin Panel - Users API
 * Handles user listing, search, and management
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Database configuration
$host = 'localhost';
$dbname = 'adzetadb';
$username = 'adzetauser';
$password = 'Crazy1395#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        handleGetUsers($pdo);
        break;
    case 'POST':
        handleCreateUser($pdo);
        break;
    case 'PUT':
        handleUpdateUser($pdo);
        break;
    case 'DELETE':
        handleDeleteUser($pdo);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}

function handleGetUsers($pdo) {
    try {
        // Create users table if it doesn't exist
        createUsersTable($pdo);
        
        // Get parameters
        $page = max(1, intval($_GET['page'] ?? 1));
        $limit = max(1, min(50, intval($_GET['limit'] ?? 20)));
        $search = trim($_GET['search'] ?? '');
        $role = trim($_GET['role'] ?? '');
        $status = trim($_GET['status'] ?? '');
        $offset = ($page - 1) * $limit;

        // Build WHERE clause
        $whereConditions = [];
        $params = [];
        
        if (!empty($search)) {
            $whereConditions[] = '(name LIKE ? OR email LIKE ? OR bio LIKE ?)';
            $searchTerm = "%$search%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        if (!empty($role) && $role !== 'all') {
            $whereConditions[] = 'role = ?';
            $params[] = $role;
        }
        
        if (!empty($status) && $status !== 'all') {
            $whereConditions[] = 'status = ?';
            $params[] = $status;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Get total count
        $countSQL = "SELECT COUNT(*) FROM users $whereClause";
        $countStmt = $pdo->prepare($countSQL);
        $countStmt->execute($params);
        $totalItems = $countStmt->fetchColumn();
        $totalPages = ceil($totalItems / $limit);

        // Get users
        $usersSQL = "
            SELECT id, name, email, role, status, avatar, bio, 
                   created_at, updated_at, last_login,
                   (SELECT COUNT(*) FROM posts WHERE author_id = users.id) as posts_count
            FROM users 
            $whereClause
            ORDER BY created_at DESC 
            LIMIT $limit OFFSET $offset
        ";
        
        $usersStmt = $pdo->prepare($usersSQL);
        $usersStmt->execute($params);
        $users = $usersStmt->fetchAll(PDO::FETCH_ASSOC);

        // Format users data
        $formattedUsers = array_map(function($user) {
            return [
                'id' => (int)$user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role'],
                'status' => $user['status'],
                'avatar' => $user['avatar'] ?: '/adzeta-admin/assets/images/default-avatar.svg',
                'bio' => $user['bio'],
                'posts_count' => (int)$user['posts_count'],
                'created_at' => $user['created_at'],
                'updated_at' => $user['updated_at'],
                'last_login' => $user['last_login']
            ];
        }, $users);

        echo json_encode([
            'success' => true,
            'users' => $formattedUsers,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $totalItems,
                'items_per_page' => $limit,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ],
            'filters' => [
                'search' => $search,
                'role' => $role,
                'status' => $status
            ]
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleCreateUser($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        $requiredFields = ['name', 'email', 'password', 'role'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                echo json_encode(['success' => false, 'message' => "Field '$field' is required"]);
                return;
            }
        }

        // Check if email already exists
        $checkStmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $checkStmt->execute([$input['email']]);
        if ($checkStmt->fetchColumn()) {
            echo json_encode(['success' => false, 'message' => 'Email already exists']);
            return;
        }

        // Create user
        $stmt = $pdo->prepare("
            INSERT INTO users (name, email, password, role, status, bio, avatar, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $input['name'],
            $input['email'],
            password_hash($input['password'], PASSWORD_DEFAULT),
            $input['role'],
            $input['status'] ?? 'active',
            $input['bio'] ?? '',
            $input['avatar'] ?? null
        ]);

        $userId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'User created successfully',
            'user_id' => $userId
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleUpdateUser($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $userId = $input['id'] ?? null;
        
        if (!$userId) {
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            return;
        }

        // Build update query
        $updateFields = [];
        $params = [];
        
        $allowedFields = ['name', 'email', 'role', 'status', 'bio', 'avatar'];
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        if (isset($input['password']) && !empty($input['password'])) {
            $updateFields[] = "password = ?";
            $params[] = password_hash($input['password'], PASSWORD_DEFAULT);
        }
        
        if (empty($updateFields)) {
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            return;
        }
        
        $updateFields[] = "updated_at = NOW()";
        $params[] = $userId;
        
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        echo json_encode([
            'success' => true,
            'message' => 'User updated successfully'
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function handleDeleteUser($pdo) {
    try {
        $userId = $_GET['id'] ?? null;
        
        if (!$userId) {
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            return;
        }

        // Check if user exists
        $checkStmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
        $checkStmt->execute([$userId]);
        if (!$checkStmt->fetchColumn()) {
            echo json_encode(['success' => false, 'message' => 'User not found']);
            return;
        }

        // Delete user
        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
        $stmt->execute([$userId]);

        echo json_encode([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);

    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}

function createUsersTable($pdo) {
    // First, check if table exists and get its structure
    $tableExists = false;
    try {
        $checkStmt = $pdo->query("DESCRIBE users");
        $tableExists = true;
        $columns = $checkStmt->fetchAll(PDO::FETCH_COLUMN);

        // Check if we need to add missing columns
        $requiredColumns = ['name', 'email', 'password', 'role', 'status', 'avatar', 'bio', 'created_at', 'updated_at', 'last_login'];
        $missingColumns = array_diff($requiredColumns, $columns);

        if (!empty($missingColumns)) {
            // Add missing columns
            foreach ($missingColumns as $column) {
                switch ($column) {
                    case 'name':
                        $pdo->exec("ALTER TABLE users ADD COLUMN name VARCHAR(255) NOT NULL DEFAULT ''");
                        break;
                    case 'email':
                        $pdo->exec("ALTER TABLE users ADD COLUMN email VARCHAR(255) UNIQUE NOT NULL DEFAULT ''");
                        break;
                    case 'password':
                        $pdo->exec("ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT ''");
                        break;
                    case 'role':
                        $pdo->exec("ALTER TABLE users ADD COLUMN role ENUM('admin', 'editor', 'author', 'contributor') DEFAULT 'author'");
                        break;
                    case 'status':
                        $pdo->exec("ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'pending') DEFAULT 'active'");
                        break;
                    case 'avatar':
                        $pdo->exec("ALTER TABLE users ADD COLUMN avatar VARCHAR(500)");
                        break;
                    case 'bio':
                        $pdo->exec("ALTER TABLE users ADD COLUMN bio TEXT");
                        break;
                    case 'created_at':
                        $pdo->exec("ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
                        break;
                    case 'updated_at':
                        $pdo->exec("ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                        break;
                    case 'last_login':
                        $pdo->exec("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL");
                        break;
                }
            }
        }
    } catch (PDOException $e) {
        // Table doesn't exist, create it
        $tableExists = false;
    }

    if (!$tableExists) {
        $createTableSQL = "
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'editor', 'author', 'contributor') DEFAULT 'author',
                status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
                avatar VARCHAR(500),
                bio TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,
                INDEX idx_email (email),
                INDEX idx_role (role),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        $pdo->exec($createTableSQL);
    }

    // Create posts table if it doesn't exist (for posts_count)
    $createPostsTableSQL = "
        CREATE TABLE IF NOT EXISTS posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            author_id INT,
            status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_author (author_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createPostsTableSQL);

    // Create sample users if table is empty
    $countStmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $countStmt->fetchColumn();

    if ($userCount == 0) {
        $sampleUsers = [
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'admin',
                'status' => 'active',
                'bio' => 'System administrator with full access to all features and settings.',
                'avatar' => '/adzeta-admin/assets/images/default-avatar.svg'
            ],
            [
                'name' => 'John Editor',
                'email' => '<EMAIL>',
                'password' => password_hash('editor123', PASSWORD_DEFAULT),
                'role' => 'editor',
                'status' => 'active',
                'bio' => 'Professional content editor with 5+ years of experience in digital marketing.',
                'avatar' => '/adzeta-admin/assets/images/default-avatar.svg'
            ],
            [
                'name' => 'Jane Author',
                'email' => '<EMAIL>',
                'password' => password_hash('author123', PASSWORD_DEFAULT),
                'role' => 'author',
                'status' => 'active',
                'bio' => 'Creative writer and content creator specializing in technology and business topics.',
                'avatar' => '/adzeta-admin/assets/images/default-avatar.svg'
            ]
        ];

        $insertStmt = $pdo->prepare("
            INSERT INTO users (name, email, password, role, status, bio, avatar, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");

        foreach ($sampleUsers as $user) {
            $insertStmt->execute([
                $user['name'],
                $user['email'],
                $user['password'],
                $user['role'],
                $user['status'],
                $user['bio'],
                $user['avatar']
            ]);
        }
    }
}
?>
