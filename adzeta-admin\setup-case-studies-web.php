<?php
/**
 * Case Studies Web Setup
 * Browser-accessible setup for case studies feature
 */

// Only allow access from localhost for security
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1', 'localhost'])) {
    http_response_code(403);
    die('Access denied. This setup can only be run from localhost.');
}

require_once __DIR__ . '/bootstrap.php';

$message = '';
$success = false;

if ($_POST['action'] ?? '' === 'setup') {
    try {
        // Read the schema file
        $schemaFile = __DIR__ . '/database/case-studies-schema.sql';
        if (!file_exists($schemaFile)) {
            throw new Exception("Schema file not found: $schemaFile");
        }
        
        $schema = file_get_contents($schemaFile);
        if (!$schema) {
            throw new Exception("Could not read schema file");
        }
        
        // Execute the schema using the PDO connection directly
        $pdo = $admin_db->getPdo();
        $pdo->exec($schema);

        // Check if table exists and has data
        $result = $admin_db->fetch("SELECT COUNT(*) as count FROM case_studies");
        
        // Create a test case study if none exist
        if ($result['count'] == 0) {
            $testData = [
                'title' => 'Sample Case Study',
                'slug' => 'sample-case-study',
                'client_name' => 'Sample Client',
                'industry' => 'Technology',
                'hero_title' => 'Sample Client Achieves 300% Growth with AI-Powered Solutions',
                'hero_description' => 'This is a sample case study demonstrating the case study editor functionality.',
                'status' => 'draft',
                'template' => 'luminous-skin-clinic',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $admin_db->insert('case_studies', $testData);
        }
        
        $message = "Case studies setup completed successfully! You can now use the case studies feature.";
        $success = true;
        
    } catch (Exception $e) {
        $message = "Setup failed: " . $e->getMessage();
        $success = false;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Case Studies Setup - AdZeta Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Case Studies Setup
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?= $success ? 'success' : 'danger' ?>" role="alert">
                                <i class="fas fa-<?= $success ? 'check-circle' : 'exclamation-triangle' ?> me-2"></i>
                                <?= htmlspecialchars($message) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="text-center">
                                <h5 class="text-success mb-3">Setup Complete!</h5>
                                <p class="mb-4">The case studies feature is now ready to use.</p>
                                <div class="d-flex justify-content-center gap-3">
                                    <a href="?view=case-studies" class="btn btn-primary">
                                        <i class="fas fa-chart-line me-2"></i>View Case Studies
                                    </a>
                                    <a href="?view=add-case-study" class="btn btn-success">
                                        <i class="fas fa-plus me-2"></i>Add Case Study
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="text-center">
                                <i class="fas fa-tools fa-4x text-primary mb-4"></i>
                                <h5>Set Up Case Studies Feature</h5>
                                <p class="text-muted mb-4">
                                    This will create the necessary database tables and sample data for the case studies feature.
                                </p>
                                
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="action" value="setup">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-cog me-2"></i>Run Setup
                                    </button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <a href="/" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Admin
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
