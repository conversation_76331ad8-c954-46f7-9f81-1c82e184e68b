<?php
/**
 * AdZeta Admin REST API
 * Production-ready API for Vue.js frontend
 */

// CORS Headers for development and production
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Security check
if (!defined('ADZETA_ADMIN')) {
    define('ADZETA_ADMIN', true);
}

// Initialize the system
require_once __DIR__ . '/../bootstrap.php';

use AdZetaAdmin\API\Router;
use AdZetaAdmin\API\AuthController;
use AdZetaAdmin\API\DashboardController;
use AdZetaAdmin\API\PostsController;
use AdZetaAdmin\API\PagesController;
use AdZetaAdmin\API\SettingsController;
use AdZetaAdmin\API\MediaController;
use AdZetaAdmin\API\SEOController;
use AdZetaAdmin\API\PageSEOController;
use AdZetaAdmin\API\CacheController;
use AdZetaAdmin\API\AIController;
use AdZetaAdmin\API\ErrorLogsController;
use AdZetaAdmin\API\UsersController;
use AdZetaAdmin\API\CaseStudiesController;

try {
    // Initialize API Router
    $router = new Router();

    // Authentication routes
    $router->post('/auth/login', [AuthController::class, 'login']);
    $router->post('/auth/logout', [AuthController::class, 'logout']);
    $router->post('/auth/verify', [AuthController::class, 'verify']);
    $router->post('/auth/refresh', [AuthController::class, 'refresh']);

    // Dashboard routes
    $router->get('/dashboard/stats', [DashboardController::class, 'getStats']);
    $router->get('/dashboard/recent-activity', [DashboardController::class, 'getRecentActivity']);

    // Blog posts routes
    $router->get('/posts', [PostsController::class, 'index']);
    $router->get('/posts/{id}', [PostsController::class, 'show']);
    $router->post('/posts', [PostsController::class, 'store']);
    $router->put('/posts/{id}', [PostsController::class, 'update']);
    $router->delete('/posts/{id}', [PostsController::class, 'destroy']);
    $router->post('/posts/{id}/publish', [PostsController::class, 'publish']);
    $router->post('/posts/{id}/unpublish', [PostsController::class, 'unpublish']);
    $router->post('/posts/autosave', [PostsController::class, 'autosave']);
    $router->post('/posts/bulk', [PostsController::class, 'bulk']);

    // Pages routes
    $router->get('/pages', [PagesController::class, 'index']);
    $router->get('/pages/{id}', [PagesController::class, 'show']);
    $router->post('/pages', [PagesController::class, 'store']);
    $router->put('/pages/{id}', [PagesController::class, 'update']);
    $router->delete('/pages/{id}', [PagesController::class, 'destroy']);

    // Settings routes
    $router->get('/settings', [SettingsController::class, 'index']);
    $router->put('/settings', [SettingsController::class, 'update']);
    $router->get('/settings/{key}', [SettingsController::class, 'show']);

    // Media routes (modern RESTful)
    $router->get('/media', [MediaController::class, 'index']);
    $router->post('/media/upload', [MediaController::class, 'upload']);
    $router->post('/media/upload-url', [MediaController::class, 'uploadByUrl']);
    $router->delete('/media/{id}', [MediaController::class, 'destroy']);

    // Legacy compatibility routes (for gradual migration)
    $router->post('/media/upload.php', [MediaController::class, 'uploadLegacy']);
    $router->post('/media/upload-by-url.php', [MediaController::class, 'uploadByUrlLegacy']);
    $router->get('/media/index.php', [MediaController::class, 'indexLegacy']);

    // Categories routes
    $router->get('/categories', [PostsController::class, 'getCategories']);
    $router->post('/categories', [PostsController::class, 'createCategory']);

    // Users routes
    $router->get('/users', [UsersController::class, 'index']);
    $router->get('/users/{id}', [UsersController::class, 'show']);
    $router->post('/users', [UsersController::class, 'store']);
    $router->put('/users/{id}', [UsersController::class, 'update']);
    $router->delete('/users/{id}', [UsersController::class, 'destroy']);



    // Page SEO routes
    $router->get('/page-seo', [PageSEOController::class, 'index']);
    $router->get('/page-seo/{id}', [PageSEOController::class, 'show']);
    $router->post('/page-seo', [PageSEOController::class, 'store']);
    $router->put('/page-seo/{id}', [PageSEOController::class, 'update']);
    $router->delete('/page-seo/{id}', [PageSEOController::class, 'destroy']);
    $router->post('/page-seo/{id}/toggle-status', [PageSEOController::class, 'toggleStatus']);
    $router->get('/seo-settings', [PageSEOController::class, 'getSettings']);
    $router->post('/seo-settings', [PageSEOController::class, 'updateSettings']);

    // SEO routes
    $router->post('/seo/analyze', [SEOController::class, 'analyze']);
    $router->get('/seo/score/{id}', [SEOController::class, 'getScore']);
    $router->post('/seo/social-preview', [SEOController::class, 'socialPreview']);
    $router->post('/seo/schema', [SEOController::class, 'generateSchema']);
    $router->post('/seo/sitemap', [SEOController::class, 'generateSitemap']);

    // New Sitemap routes
    $router->post('/sitemap/generate', [SEOController::class, 'generateSitemapNew']);
    $router->get('/sitemap/status', [SEOController::class, 'getSitemapStatus']);

    // Robots.txt routes
    $router->get('/robots/get', [SEOController::class, 'getRobotsContent']);
    $router->post('/robots/save', [SEOController::class, 'saveRobotsContent']);
    $router->post('/robots/validate', [SEOController::class, 'validateRobotsContent']);
    $router->post('/robots/template', [SEOController::class, 'generateRobotsTemplate']);
    $router->get('/robots/test', [SEOController::class, 'testRobotsAccessibility']);

    // AI routes
    $router->post('/ai/test-connection', [AIController::class, 'testConnection']);
    $router->post('/ai/generate-content', [AIController::class, 'generateContent']);
    $router->post('/ai/generate-titles', [AIController::class, 'generateTitles']);
    $router->post('/ai/generate-meta-description', [AIController::class, 'generateMetaDescription']);
    $router->post('/ai/generate-tags', [AIController::class, 'generateTags']);
    $router->post('/ai/analyze-seo', [AIController::class, 'analyzeSEO']);
    $router->get('/ai/settings', [AIController::class, 'getSettings']);
    $router->post('/ai/settings', [AIController::class, 'updateSettings']);
    $router->get('/ai/usage-stats', [AIController::class, 'getUsageStats']);

    // Error Logs routes
    $router->get('/error-logs', function() {
        global $admin_db;
        $controller = new ErrorLogsController($admin_db);
        return $controller->index();
    });
    $router->get('/error-logs/stats', function() {
        global $admin_db;
        $controller = new ErrorLogsController($admin_db);
        return $controller->stats();
    });
    $router->get('/error-logs/{id}', function($id) {
        global $admin_db;
        $controller = new ErrorLogsController($admin_db);
        return $controller->show($id);
    });
    $router->post('/error-logs', function() {
        global $admin_db;
        $controller = new ErrorLogsController($admin_db);
        return $controller->store();
    });
    $router->post('/error-logs/{id}/resolve', function($id) {
        global $admin_db;
        $controller = new ErrorLogsController($admin_db);
        return $controller->resolve($id);
    });
    $router->delete('/error-logs/{id}', function($id) {
        global $admin_db;
        $controller = new ErrorLogsController($admin_db);
        return $controller->destroy($id);
    });
    $router->delete('/error-logs/clear', function() {
        global $admin_db;
        $controller = new ErrorLogsController($admin_db);
        return $controller->clearOld();
    });

    // Production Cache routes - REMOVED (using CacheController instead)

    // Simple test route
    $router->get('/cache/simple-test', function() {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Simple test working',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    });

    // Simple cache stats endpoint - REMOVED (using CacheController instead)

    // Old JSON-based cache endpoints removed - now using CacheController

    // Simple cache clear endpoint - REMOVED (using CacheController instead)

    // Simple cache preload endpoint - REMOVED (using CacheController instead)

    // Template Management routes
    $router->get('/templates', function() {
        require_once __DIR__ . '/../src/API/TemplateController.php';
        $controller = new \AdZetaAdmin\API\TemplateController();
        echo json_encode($controller->getTemplates());
    });

    $router->get('/templates/by-type', function() {
        require_once __DIR__ . '/../src/API/TemplateController.php';
        $controller = new \AdZetaAdmin\API\TemplateController();
        echo json_encode($controller->getTemplatesByType());
    });

    $router->post('/templates/settings', function() {
        require_once __DIR__ . '/../src/API/TemplateController.php';
        $controller = new \AdZetaAdmin\API\TemplateController();
        echo json_encode($controller->saveTemplateSettings());
    });

    $router->get('/templates/preview', function() {
        require_once __DIR__ . '/../src/API/TemplateController.php';
        $controller = new \AdZetaAdmin\API\TemplateController();
        echo json_encode($controller->getTemplatePreview());
    });

    $router->post('/templates/post', function() {
        require_once __DIR__ . '/../src/API/TemplateController.php';
        $controller = new \AdZetaAdmin\API\TemplateController();
        echo json_encode($controller->setPostTemplate());
    });

    $router->get('/templates/selector', function() {
        require_once __DIR__ . '/../src/API/TemplateController.php';
        $controller = new \AdZetaAdmin\API\TemplateController();
        echo json_encode($controller->getTemplateSelector());
    });

    $router->get('/templates/stats', function() {
        require_once __DIR__ . '/../src/API/TemplateController.php';
        $controller = new \AdZetaAdmin\API\TemplateController();
        echo json_encode($controller->getTemplateStats());
    });

    // AI Blog Post Generation routes (using existing AI controller)
    $router->post('/ai/generate-post', [AIController::class, 'generateBlogPost']);
    $router->post('/ai/generate-complete-post', [AIController::class, 'generateCompletePost']);
    $router->get('/ai/topic-suggestions', [AIController::class, 'generateTopicSuggestions']);

    // AI Case Study Generation routes
    $router->post('/ai/generate-case-study', [AIController::class, 'generateCaseStudy']);
    $router->post('/ai/generate-case-study-section', [AIController::class, 'generateCaseStudySection']);
    $router->post('/ai/generate-case-study-titles', [AIController::class, 'generateCaseStudyTitles']);
    $router->post('/ai/generate-case-study-meta', [AIController::class, 'generateCaseStudyMeta']);

    // Case Studies routes
    $router->get('/case-studies', [CaseStudiesController::class, 'index']);
    $router->get('/case-studies/stats', [CaseStudiesController::class, 'getStats']);
    $router->get('/case-studies/slug/{slug}', [CaseStudiesController::class, 'getBySlug']);
    $router->get('/case-studies/{id}', [CaseStudiesController::class, 'show']);
    $router->post('/case-studies', [CaseStudiesController::class, 'store']);
    $router->put('/case-studies/{id}', [CaseStudiesController::class, 'update']);
    $router->delete('/case-studies/{id}', [CaseStudiesController::class, 'delete']);
    $router->post('/case-studies/{id}/publish', [CaseStudiesController::class, 'publish']);
    $router->post('/case-studies/{id}/unpublish', [CaseStudiesController::class, 'unpublish']);
    $router->post('/case-studies/autosave', [CaseStudiesController::class, 'autosave']);

    // Cache routes (using new CacheController)
    $router->get('/cache/test', [CacheController::class, 'test']);
    $router->get('/cache/stats', [CacheController::class, 'stats']);
    $router->get('/cache/settings', [CacheController::class, 'getSettings']);
    $router->post('/cache/settings', [CacheController::class, 'saveSettings']);
    $router->post('/cache/clear', [CacheController::class, 'clear']);
    $router->post('/cache/preload', [CacheController::class, 'preload']);

    // Legacy cache routes - REMOVED (using CacheController instead)

    // Handle the request
    $router->dispatch();

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => ADZETA_DEBUG ? $e->getMessage() : 'An error occurred'
    ]);
}
?>
