/* Integrated Metrics Section Styles */
.metrics-section {
    position: relative;
    background: linear-gradient(180deg, rgba(27, 11, 36, 0.02) 0%, rgba(27, 11, 36, 0.05) 100%);
    padding: 80px 0;
    overflow: hidden;
}

/* Subtle background pattern */
.metrics-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 10% 20%, rgba(143, 118, 245, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(233, 88, 161, 0.05) 0%, transparent 50%);
    z-index: 0;
}

/* Section connector - top curve */
.section-connector {
    position: absolute;
    top: -2px;
    left: 0;
    width: 100%;
    height: 80px;
    background-color: transparent;
    z-index: 1;
    overflow: hidden;
}

.section-connector::before {
    content: '';
    position: absolute;
    top: 0;
    left: -5%;
    width: 110%;
    height: 100px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.03);
}

/* Section heading styles */
.metrics-heading {
    position: relative;
    margin-bottom: 40px;
    text-align: center;
}

.metrics-heading h2 {
    font-weight: 700;
    margin-bottom: 15px;
}

.metrics-heading p {
    font-size: 18px;
    line-height: 1.6;
    color: #555;
    max-width: 800px;
    margin: 0 auto 20px;
}

.metrics-intro {
    background: #fff;
    border-radius: 12px;
    padding: 25px 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
}

.metrics-intro p {
    font-size: 16px;
    line-height: 1.6;
    color: #444;
    margin-bottom: 0;
}

.metrics-intro strong {
    color: #333;
}

/* Metrics grid container */
.metrics-grid {
    position: relative;
    z-index: 1;
}

/* Modern metric card styles */
.metric-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 30px;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0,0,0,0.03);
}



.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.metric-card:hover::before {
    opacity: 1;
}

/* Card header */
.metric-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.metric-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    line-height: 1.3;
}

.metric-badge {
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 20px;
    background: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
}

.metric-badge i {
    margin-right: 4px;
    font-size: 10px;
}

/* Metric value styling */
.metric-value-container {
    margin-bottom: 15px;
}

.metric-value {
    font-size: 42px;
    line-height: 1;
    font-weight: 700;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.metric-value .counter-number {
    background: linear-gradient(135deg, #8f76f5, #e958a1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 1;
}

.metric-value .counter-number::after {
    content: attr(data-unit);
    position: relative;
    margin-left: 2px;
    font-size: 0.8em;
    opacity: 0.9;
}

.metric-value .trend {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.trend-up {
    color: #2ecc71;
}

.trend-down {
    color: #3498db;
}

.trend i {
    margin-right: 3px;
}

.metric-description {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    margin-bottom: 20px;
}

/* Mini chart container */
.mini-chart {
    margin-top: auto;
    height: 60px;
    position: relative;
    border-top: 1px solid rgba(0,0,0,0.05);
    padding-top: 15px;
}

.mini-chart canvas {
    width: 100%;
    height: 100%;
}

/* Connecting elements */
.metrics-connector {
    position: relative;
    margin: 30px 0;
    text-align: center;
}

.connector-line {
    height: 2px;
    background: linear-gradient(90deg, rgba(143, 118, 245, 0.2), rgba(233, 88, 161, 0.2));
    position: relative;
    margin: 0 auto;
    max-width: 200px;
}

.connector-text {
    position: relative;
    top: -10px;
    background: #fff;
    padding: 0 15px;
    font-size: 14px;
    color: #666;
    display: inline-block;
}

/* Comparison section */
.comparison-section {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 30px;
    margin-top: 40px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(0,0,0,0.03);
}

.comparison-header {
    text-align: center;
    margin-bottom: 30px;
}

.comparison-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.comparison-subtitle {
    font-size: 16px;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

/* Chart container */
.chart-container {
    height: 250px;
    position: relative;
}

/* Comparison chart */
.comparison-chart {
    background: #ffffff;
    border-radius: 16px;
    padding: 25px;
    height: 100%;
    border: 1px solid rgba(0,0,0,0.03);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.comparison-chart::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #8f76f5, #e958a1);
    opacity: 0.8;
}

.comparison-chart:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.comparison-chart-header {
    margin-bottom: 20px;
}

.comparison-chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.comparison-chart-subtitle {
    font-size: 14px;
    color: #666;
}

/* Chart legend */
.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
    font-size: 12px;
    color: #666;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 5px;
}

/* Donut chart */
.donut-chart {
    position: relative;
    height: 200px;
}

.donut-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.donut-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    line-height: 1;
}

.donut-label {
    font-size: 14px;
    color: #666;
}

/* Practical impact section */
.practical-impact {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 30px;
    margin-top: 40px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(0,0,0,0.03);
    overflow: hidden;
}

.practical-impact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #8f76f5, #e958a1);
    opacity: 0.8;
}

.impact-header {
    text-align: center;
    margin-bottom: 30px;
}

.impact-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.impact-subtitle {
    font-size: 16px;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

.impact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.impact-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.impact-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(143, 118, 245, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.impact-icon i {
    color: #8f76f5;
    font-size: 18px;
}

.impact-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.impact-content p {
    font-size: 14px;
    color: #666;
    margin-bottom: 0;
    line-height: 1.5;
}

/* Data source footer */
.metrics-footer {
    margin-top: 40px;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 10px;
    font-size: 12px;
    color: #777;
}

.metrics-footer a {
    color: #8f76f5;
    text-decoration: none;
    font-weight: 500;
}

.metrics-footer a:hover {
    text-decoration: underline;
}

/* Testimonial card */
.testimonial-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 30px;
    margin-top: 60px;
    position: relative;
    border: 1px solid rgba(0,0,0,0.03);
    overflow: hidden;
    transition: all 0.3s ease;
}

.testimonial-card::after {
    content: '\201C'; /* Opening quote mark */
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 120px;
    font-family: Georgia, serif;
    color: rgba(143, 118, 245, 0.1);
    line-height: 1;
}

.testimonial-card:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
}

.testimonial-content {
    position: relative;
    padding-left: 25px;
    margin-bottom: 20px;
}

.testimonial-content::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, #e958a1, #8f76f5);
    border-radius: 3px;
}

.testimonial-quote {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
    border: 2px solid #fff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.testimonial-info {
    display: flex;
    flex-direction: column;
}

.testimonial-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.testimonial-position {
    font-size: 14px;
    color: #666;
}

.testimonial-nav {
    display: flex;
    justify-content: center;
}

.testimonial-nav-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.testimonial-nav-btn:hover {
    background: #e9ecef;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .metrics-section {
        padding: 60px 0;
    }

    .metric-card {
        margin-bottom: 20px;
    }

    .metric-value {
        font-size: 36px;
    }

    .comparison-chart {
        margin-bottom: 20px;
    }
}

@media (max-width: 767px) {
    .metrics-heading {
        margin-bottom: 30px;
    }

    .metric-card {
        padding: 20px;
    }

    .metric-value {
        font-size: 32px;
    }

    .impact-item {
        flex-direction: column;
    }

    .impact-icon {
        margin-bottom: 10px;
    }
}
