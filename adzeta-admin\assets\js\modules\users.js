/**
 * AdZeta Admin Panel - Users Management Module
 * Complete user management system with profiles and roles
 */

window.AdZetaUsers = {
    // State
    state: {
        users: [],
        currentUser: null,
        currentPage: 1,
        totalPages: 1,
        searchQuery: '',
        roleFilter: 'all',
        loading: false,
        editingUser: null
    },

    // Initialize users module
    init() {
        console.log('Users module initialized');
    },

    // Load users view
    load() {
        console.log('Loading Users view');
        this.renderUsersView();
        this.bindEvents();
        this.loadUsers();
    },

    // Render users management interface
    renderUsersView() {
        const usersView = document.getElementById('usersView');
        if (!usersView) return;

        usersView.innerHTML = `
            <!-- Users Header -->
            <div class="users-header mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center gap-3">
                            <h2 class="h4 mb-0">Users</h2>
                            <span class="badge bg-secondary" id="usersCount">0 users</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end gap-2">
                            <button class="btn btn-outline-primary" id="exportUsersBtn">
                                <i class="fas fa-download me-1"></i>
                                Export
                            </button>
                            <button class="btn btn-primary" id="addUserBtn">
                                <i class="fas fa-user-plus me-1"></i>
                                Add User
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Toolbar -->
            <div class="users-toolbar mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="usersSearch" 
                                   placeholder="Search users...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="roleFilter">
                            <option value="all">All Roles</option>
                            <option value="admin">Administrator</option>
                            <option value="editor">Editor</option>
                            <option value="author">Author</option>
                            <option value="contributor">Contributor</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter">
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="users-container">
                <div class="card">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="60">Avatar</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Posts</th>
                                    <th>Last Login</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <!-- Users will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Loading State -->
                <div class="text-center py-5" id="usersLoading" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading users...</p>
                </div>
                
                <!-- Empty State -->
                <div class="text-center py-5" id="usersEmpty" style="display: none;">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4>No users found</h4>
                    <p class="text-muted">Add your first user to get started.</p>
                    <button class="btn btn-primary" onclick="AdZetaUsers.openAddUserModal()">
                        <i class="fas fa-user-plus me-1"></i>
                        Add User
                    </button>
                </div>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4" id="usersPagination" style="display: none;">
                <div class="text-muted">
                    Showing <span id="usersShowingStart">1</span> to <span id="usersShowingEnd">20</span> 
                    of <span id="usersTotalItems">0</span> users
                </div>
                <nav>
                    <ul class="pagination mb-0" id="usersPaginationList">
                        <!-- Pagination will be rendered here -->
                    </ul>
                </nav>
            </div>
        `;

        // Update page header
        this.updatePageHeader();
    },

    // Update page header
    updatePageHeader() {
        const pageTitle = document.getElementById('pageTitle');
        const pageSubtitle = document.getElementById('pageSubtitle');
        const pageActions = document.getElementById('pageActions');

        if (pageTitle) {
            pageTitle.innerHTML = '<i class="fas fa-users me-2"></i>Users';
        }
        if (pageSubtitle) {
            pageSubtitle.textContent = 'Manage user accounts, roles, and permissions.';
        }
        if (pageActions) {
            pageActions.innerHTML = `
                <button class="btn btn-primary" onclick="AdZetaUsers.openAddUserModal()">
                    <i class="fas fa-user-plus me-1"></i>
                    Add User
                </button>
            `;
        }
    },

    // Bind events
    bindEvents() {
        // Search
        const searchInput = document.getElementById('usersSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.state.searchQuery = e.target.value;
                this.state.currentPage = 1;
                this.loadUsers();
            }, 300));
        }

        // Role filter
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.state.roleFilter = e.target.value;
                this.state.currentPage = 1;
                this.loadUsers();
            });
        }

        // Add user button
        const addUserBtn = document.getElementById('addUserBtn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => this.openAddUserModal());
        }
    },

    // Load users with current filters
    async loadUsers() {
        this.state.loading = true;
        this.showLoading();

        try {
            const params = new URLSearchParams({
                page: this.state.currentPage,
                limit: 20,
                search: this.state.searchQuery,
                role: this.state.roleFilter
            });

            const response = await fetch(`/adzeta-admin/api/users/index.php?${params}`);
            const result = await response.json();

            if (result.success) {
                this.state.users = result.users || [];
                this.state.currentPage = result.pagination?.current_page || 1;
                this.state.totalPages = result.pagination?.total_pages || 1;

                this.renderUsers();
                this.renderPagination();
                this.updateUsersCount(result.pagination?.total_items || 0);
            } else {
                throw new Error(result.message || 'Failed to load users');
            }
        } catch (error) {
            console.error('Failed to load users:', error);
            this.showEmpty();

            // Show error notification
            if (window.AdZetaApp) {
                window.AdZetaApp.showNotification('Failed to load users: ' + error.message, 'danger');
            }
        } finally {
            this.state.loading = false;
            this.hideLoading();
        }
    },

    // Render users table
    renderUsers() {
        const tbody = document.getElementById('usersTableBody');
        const emptyState = document.getElementById('usersEmpty');
        
        if (this.state.users.length === 0) {
            tbody.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }
        
        emptyState.style.display = 'none';
        
        tbody.innerHTML = this.state.users.map(user => `
            <tr data-user-id="${user.id}">
                <td>
                    <img src="${user.avatar}" alt="${user.name}" class="user-avatar">
                </td>
                <td>
                    <div class="user-name-cell">
                        <div class="fw-medium">${user.name}</div>
                        <small class="text-muted">ID: ${user.id}</small>
                    </div>
                </td>
                <td>${user.email}</td>
                <td>
                    <span class="badge bg-${this.getRoleBadgeColor(user.role)}">${this.formatRole(user.role)}</span>
                </td>
                <td>
                    <span class="badge bg-${this.getStatusBadgeColor(user.status)}">${this.formatStatus(user.status)}</span>
                </td>
                <td>
                    <span class="text-muted">${user.posts_count} posts</span>
                </td>
                <td>
                    <small class="text-muted">${this.formatDate(user.last_login)}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="AdZetaUsers.editUser(${user.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="AdZetaUsers.viewUserProfile(${user.id})" title="Profile">
                            <i class="fas fa-user"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="AdZetaUsers.deleteUser(${user.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    showLoading() {
        document.getElementById('usersLoading').style.display = 'block';
        document.querySelector('.users-container .card').style.display = 'none';
    },

    hideLoading() {
        document.getElementById('usersLoading').style.display = 'none';
        document.querySelector('.users-container .card').style.display = 'block';
    },

    showEmpty() {
        document.getElementById('usersEmpty').style.display = 'block';
        document.querySelector('.users-container .card').style.display = 'none';
    },

    updateUsersCount(count) {
        const usersCount = document.getElementById('usersCount');
        if (usersCount) {
            usersCount.textContent = `${count} user${count !== 1 ? 's' : ''}`;
        }
    },

    formatRole(role) {
        const roles = {
            admin: 'Administrator',
            editor: 'Editor',
            author: 'Author',
            contributor: 'Contributor'
        };
        return roles[role] || role;
    },

    formatStatus(status) {
        const statuses = {
            active: 'Active',
            inactive: 'Inactive',
            pending: 'Pending'
        };
        return statuses[status] || status;
    },

    getRoleBadgeColor(role) {
        const colors = {
            admin: 'danger',
            editor: 'warning',
            author: 'primary',
            contributor: 'secondary'
        };
        return colors[role] || 'secondary';
    },

    getStatusBadgeColor(status) {
        const colors = {
            active: 'success',
            inactive: 'secondary',
            pending: 'warning'
        };
        return colors[status] || 'secondary';
    },

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    renderPagination() {
        // TODO: Implement pagination similar to media manager
        const pagination = document.getElementById('usersPagination');
        if (pagination) {
            pagination.style.display = 'none'; // Hide for now with mock data
        }
    },

    // User actions
    openAddUserModal() {
        this.state.editingUser = null;
        this.showUserModal('Add User', {
            name: '',
            email: '',
            role: 'author',
            status: 'active',
            bio: '',
            avatar: ''
        });
    },



    viewUserProfile(userId) {
        const user = this.state.users.find(u => u.id === userId);
        if (user) {
            this.showUserProfileModal(user);
        } else {
            window.AdZetaApp.showNotification('User not found', 'danger');
        }
    },

    async deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/adzeta-admin/api/users/index.php?id=${userId}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const result = await response.json();

            if (result.success) {
                window.AdZetaApp.showNotification('User deleted successfully', 'success');
                this.loadUsers(); // Refresh the list
            } else {
                throw new Error(result.message || 'Failed to delete user');
            }
        } catch (error) {
            console.error('Failed to delete user:', error);
            window.AdZetaApp.showNotification('Failed to delete user: ' + error.message, 'danger');
        }
    },

    // Show user modal (add/edit)
    showUserModal(title, userData) {
        const isEdit = this.state.editingUser !== null;

        // Remove any existing modal and backdrop
        this.closeAllModals();

        const modalHTML = `
            <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true" data-bs-backdrop="true" data-bs-keyboard="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="userModalLabel">
                                <i class="fas fa-user me-2"></i>${title}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="userForm">
                                <div class="row">
                                    <!-- Basic Information -->
                                    <div class="col-md-8">
                                        <div class="user-form-section">
                                            <h6><i class="fas fa-user me-2"></i>Basic Information</h6>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="userName" class="form-label">Full Name *</label>
                                                    <input type="text" class="form-control" id="userName"
                                                           value="${userData.name}" required>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="userEmail" class="form-label">Email Address *</label>
                                                    <input type="email" class="form-control" id="userEmail"
                                                           value="${userData.email}" required>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="userRole" class="form-label">Role *</label>
                                                    <select class="form-select" id="userRole" required>
                                                        <option value="admin" ${userData.role === 'admin' ? 'selected' : ''}>Administrator</option>
                                                        <option value="editor" ${userData.role === 'editor' ? 'selected' : ''}>Editor</option>
                                                        <option value="author" ${userData.role === 'author' ? 'selected' : ''}>Author</option>
                                                        <option value="contributor" ${userData.role === 'contributor' ? 'selected' : ''}>Contributor</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="userStatus" class="form-label">Status</label>
                                                    <select class="form-select" id="userStatus">
                                                        <option value="active" ${userData.status === 'active' ? 'selected' : ''}>Active</option>
                                                        <option value="inactive" ${userData.status === 'inactive' ? 'selected' : ''}>Inactive</option>
                                                        <option value="pending" ${userData.status === 'pending' ? 'selected' : ''}>Pending</option>
                                                    </select>
                                                </div>
                                            </div>

                                            ${!isEdit ? `
                                                <div class="mb-3">
                                                    <label for="userPassword" class="form-label">Password *</label>
                                                    <input type="password" class="form-control" id="userPassword" required>
                                                    <div class="form-text">Minimum 8 characters</div>
                                                </div>
                                            ` : `
                                                <div class="mb-3">
                                                    <label for="userPassword" class="form-label">New Password</label>
                                                    <input type="password" class="form-control" id="userPassword">
                                                    <div class="form-text">Leave blank to keep current password</div>
                                                </div>
                                            `}
                                        </div>

                                        <!-- Bio Section -->
                                        <div class="user-form-section">
                                            <h6><i class="fas fa-edit me-2"></i>Author Bio</h6>
                                            <div class="mb-3">
                                                <label for="userBio" class="form-label">Biography</label>
                                                <textarea class="form-control bio-editor" id="userBio" rows="4"
                                                          placeholder="Write a brief bio for this user...">${userData.bio || ''}</textarea>
                                                <div class="form-text">This will be displayed on author pages and posts</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Avatar Section -->
                                    <div class="col-md-4">
                                        <div class="user-form-section">
                                            <h6><i class="fas fa-camera me-2"></i>Profile Picture</h6>

                                            <div class="avatar-upload-area" onclick="AdZetaUsers.openAvatarSelector()">
                                                <div class="text-center">
                                                    <img src="${userData.avatar || '/adzeta-admin/assets/images/default-avatar.svg'}"
                                                         alt="Avatar" class="avatar-preview" id="avatarPreview">
                                                    <div class="mt-2">
                                                        <i class="fas fa-camera fa-2x text-muted mb-2"></i>
                                                        <p class="mb-0 text-muted">Click to change</p>
                                                        <small class="text-muted">JPG, PNG or GIF</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <input type="hidden" id="userAvatar" value="${userData.avatar || ''}">
                                        </div>

                                        <!-- Role Permissions Info -->
                                        <div class="user-form-section">
                                            <h6><i class="fas fa-shield-alt me-2"></i>Permissions</h6>
                                            <div id="rolePermissions">
                                                ${this.getRolePermissionsHTML(userData.role)}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="AdZetaUsers.saveUser()">
                                <i class="fas fa-save me-1"></i>
                                ${isEdit ? 'Update User' : 'Create User'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal and backdrop
        this.closeAllModals();

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get modal element
        const modalElement = document.getElementById('userModal');

        // Show modal with proper configuration
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: true,
            keyboard: true,
            focus: true
        });

        // Store modal instance for later use
        this.currentModal = modal;

        // Bind events after modal is shown
        modalElement.addEventListener('shown.bs.modal', () => {
            // Bind role change event
            const roleSelect = document.getElementById('userRole');
            if (roleSelect) {
                roleSelect.addEventListener('change', (e) => {
                    const permissionsDiv = document.getElementById('rolePermissions');
                    if (permissionsDiv) {
                        permissionsDiv.innerHTML = this.getRolePermissionsHTML(e.target.value);
                    }
                });
            }
        }, { once: true });

        // Handle modal cleanup when hidden
        modalElement.addEventListener('hidden.bs.modal', () => {
            this.closeAllModals();
        }, { once: true });

        modal.show();
    },

    // Get role permissions HTML
    getRolePermissionsHTML(role) {
        const permissions = {
            admin: [
                'Full system access',
                'User management',
                'Settings configuration',
                'All content operations',
                'Media library management'
            ],
            editor: [
                'Edit all posts',
                'Publish content',
                'Manage categories',
                'Media library access',
                'User profile editing'
            ],
            author: [
                'Create own posts',
                'Edit own posts',
                'Upload media',
                'Profile management'
            ],
            contributor: [
                'Create draft posts',
                'Edit own drafts',
                'Basic profile access'
            ]
        };

        const rolePermissions = permissions[role] || [];

        return `
            <ul class="permission-list">
                ${rolePermissions.map(permission => `
                    <li><i class="fas fa-check text-success"></i> ${permission}</li>
                `).join('')}
            </ul>
        `;
    },

    // Open avatar selector
    openAvatarSelector() {
        if (window.AdZetaMediaLibrary) {
            // Store reference to current user modal and its instance
            const userModal = document.getElementById('userModal');
            const userModalInstance = bootstrap.Modal.getInstance(userModal);

            // Prevent user modal from being affected by media library
            if (userModalInstance) {
                // Temporarily disable backdrop clicks on user modal
                userModal.setAttribute('data-bs-backdrop', 'static');
            }

            window.AdZetaMediaLibrary.open((selectedImage) => {
                // Update avatar fields
                const avatarInput = document.getElementById('userAvatar');
                const avatarPreview = document.getElementById('avatarPreview');

                if (avatarInput) avatarInput.value = selectedImage.file_url;
                if (avatarPreview) avatarPreview.src = selectedImage.file_url;

                console.log('Avatar updated:', selectedImage.file_url);

                // Re-enable backdrop clicks on user modal
                if (userModalInstance) {
                    userModal.setAttribute('data-bs-backdrop', 'true');
                }

                // Show success message
                if (window.AdZetaApp) {
                    window.AdZetaApp.showNotification('Avatar updated successfully', 'success');
                }
            });
        } else {
            window.AdZetaApp.showNotification('Media library not available', 'warning');
        }
    },

    // Save user (create or update)
    async saveUser() {
        console.log('saveUser called, editingUser:', this.state.editingUser);

        const form = document.getElementById('userForm');
        const isEdit = this.state.editingUser !== null;

        // Show loading state
        const saveButton = document.querySelector('#userModal .btn-primary');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';
        saveButton.disabled = true;

        try {
            // Get form data
            const userData = {
                name: document.getElementById('userName').value.trim(),
                email: document.getElementById('userEmail').value.trim(),
                role: document.getElementById('userRole').value,
                status: document.getElementById('userStatus').value,
                bio: document.getElementById('userBio').value.trim(),
                avatar: document.getElementById('userAvatar').value
            };

            const password = document.getElementById('userPassword').value;
            if (password) {
                userData.password = password;
            }

            console.log('Form data collected:', userData);

            // Validate required fields
            if (!userData.name || !userData.email) {
                window.AdZetaApp.showNotification('Please fill in all required fields', 'warning');
                return;
            }

            if (!isEdit && !password) {
                window.AdZetaApp.showNotification('Password is required for new users', 'warning');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(userData.email)) {
                window.AdZetaApp.showNotification('Please enter a valid email address', 'warning');
                return;
            }

            let url = '/adzeta-admin/api/users/index.php';
            let method = 'POST';

            if (isEdit) {
                userData.id = this.state.editingUser.id;
                method = 'PUT';
            }

            console.log('Making API request:', { url, method, userData });

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(userData)
            });

            console.log('API response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('API response data:', result);

            if (result.success) {
                window.AdZetaApp.showNotification(
                    isEdit ? 'User updated successfully' : 'User created successfully',
                    'success'
                );

                // Close only the user modal, not all modals
                this.closeUserModal();

                // Refresh users list and clear editing state
                this.state.editingUser = null;
                await this.loadUsers();
            } else {
                throw new Error(result.message || 'Failed to save user');
            }
        } catch (error) {
            console.error('Failed to save user:', error);
            window.AdZetaApp.showNotification('Failed to save user: ' + error.message, 'danger');
        } finally {
            // Restore button state
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
        }
    },

    // Show user profile modal
    showUserProfileModal(user) {
        const modalHTML = `
            <div class="modal fade" id="userProfileModal" tabindex="-1" aria-labelledby="userProfileModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header user-profile-header">
                            <div class="d-flex align-items-center">
                                <img src="${user.avatar}" alt="${user.name}" class="user-profile-avatar me-3">
                                <div class="user-profile-info">
                                    <h3 class="mb-0">${user.name}</h3>
                                    <div class="user-profile-meta">
                                        <span class="badge bg-light text-dark me-2">${this.formatRole(user.role)}</span>
                                        <span class="badge bg-${this.getStatusBadgeColor(user.status)} me-2">${this.formatStatus(user.status)}</span>
                                        <span><i class="fas fa-envelope me-1"></i>${user.email}</span>
                                    </div>
                                    <div class="user-stats">
                                        <div class="user-stat">
                                            <span class="user-stat-number">${user.posts_count}</span>
                                            <span class="user-stat-label">Posts</span>
                                        </div>
                                        <div class="user-stat">
                                            <span class="user-stat-number">${this.formatDate(user.created_at).split(',')[0]}</span>
                                            <span class="user-stat-label">Joined</span>
                                        </div>
                                        <div class="user-stat">
                                            <span class="user-stat-number">${user.last_login ? this.formatDate(user.last_login).split(',')[0] : 'Never'}</span>
                                            <span class="user-stat-label">Last Login</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5><i class="fas fa-user me-2"></i>About ${user.name}</h5>
                                    <div class="mb-4">
                                        ${user.bio ? `<p class="text-muted">${user.bio}</p>` : '<p class="text-muted fst-italic">No bio available.</p>'}
                                    </div>

                                    <h5><i class="fas fa-info-circle me-2"></i>Account Details</h5>
                                    <div class="row">
                                        <div class="col-sm-6 mb-3">
                                            <strong>User ID:</strong><br>
                                            <span class="text-muted">#${user.id}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>Email:</strong><br>
                                            <span class="text-muted">${user.email}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>Role:</strong><br>
                                            <span class="badge bg-${this.getRoleBadgeColor(user.role)}">${this.formatRole(user.role)}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>Status:</strong><br>
                                            <span class="badge bg-${this.getStatusBadgeColor(user.status)}">${this.formatStatus(user.status)}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>Joined:</strong><br>
                                            <span class="text-muted">${this.formatDate(user.created_at)}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>Last Login:</strong><br>
                                            <span class="text-muted">${user.last_login ? this.formatDate(user.last_login) : 'Never logged in'}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <h5><i class="fas fa-chart-bar me-2"></i>Statistics</h5>
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <div class="mb-3">
                                                <h3 class="text-primary mb-0">${user.posts_count}</h3>
                                                <small class="text-muted">Total Posts</small>
                                            </div>
                                            <div class="mb-3">
                                                <h3 class="text-success mb-0">${Math.floor(Math.random() * 1000) + 100}</h3>
                                                <small class="text-muted">Total Views</small>
                                            </div>
                                            <div class="mb-0">
                                                <h3 class="text-info mb-0">${Math.floor(Math.random() * 50) + 10}</h3>
                                                <small class="text-muted">Comments</small>
                                            </div>
                                        </div>
                                    </div>

                                    <h5 class="mt-4"><i class="fas fa-shield-alt me-2"></i>Permissions</h5>
                                    <div class="card">
                                        <div class="card-body">
                                            ${this.getRolePermissionsHTML(user.role)}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="AdZetaUsers.editUser(${user.id}); bootstrap.Modal.getInstance(document.getElementById('userProfileModal')).hide();">
                                <i class="fas fa-edit me-1"></i>
                                Edit User
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal
        const existingModal = document.getElementById('userProfileModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('userProfileModal'));
        modal.show();
    },

    // Debug function to test user updates
    async testUserUpdate(userId) {
        console.log('Testing user update for ID:', userId);

        try {
            const testData = {
                id: userId,
                name: 'Test Update ' + Date.now(),
                bio: 'Updated bio at ' + new Date().toLocaleString()
            };

            const response = await fetch('/adzeta-admin/api/users/index.php', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(testData)
            });

            const result = await response.json();
            console.log('Test update result:', result);

            if (result.success) {
                window.AdZetaApp.showNotification('Test update successful', 'success');
                this.loadUsers();
            } else {
                window.AdZetaApp.showNotification('Test update failed: ' + result.message, 'danger');
            }
        } catch (error) {
            console.error('Test update error:', error);
            window.AdZetaApp.showNotification('Test update error: ' + error.message, 'danger');
        }
    },

    // Close user-specific modals only
    closeAllModals() {
        // Only close user-related modals, not media library or other system modals
        const userModals = ['userModal', 'userProfileModal'];

        userModals.forEach(modalId => {
            const modalElement = document.getElementById(modalId);
            if (modalElement) {
                const modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    try {
                        modalInstance.hide();
                        modalInstance.dispose();
                    } catch (error) {
                        console.warn('Error disposing modal:', modalId, error);
                    }
                }
                modalElement.remove();
            }
        });

        // Only remove backdrops if no other modals are open
        const remainingModals = document.querySelectorAll('.modal.show');
        if (remainingModals.length === 0) {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());

            // Remove modal-open class from body only if no modals remain
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }

        // Clear current modal reference
        this.currentModal = null;
    },

    // Close only the user modal (safer method)
    closeUserModal() {
        const userModal = document.getElementById('userModal');
        if (userModal) {
            const modalInstance = bootstrap.Modal.getInstance(userModal);
            if (modalInstance) {
                try {
                    modalInstance.hide();

                    // Wait for hide animation to complete, then dispose
                    userModal.addEventListener('hidden.bs.modal', () => {
                        try {
                            modalInstance.dispose();
                            userModal.remove();
                        } catch (error) {
                            console.warn('Error disposing user modal:', error);
                        }
                    }, { once: true });

                } catch (error) {
                    console.warn('Error hiding user modal:', error);
                    // Fallback: just remove the modal
                    userModal.remove();
                }
            } else {
                // No instance, just remove the element
                userModal.remove();
            }
        }

        // Clear current modal reference
        this.currentModal = null;
    },

    // Refresh user data after update
    async refreshUserData(userId) {
        try {
            // Find the user in current state
            const userIndex = this.state.users.findIndex(u => u.id === userId);
            if (userIndex === -1) return;

            // For now, just reload all users to get fresh data
            // In a real app, you might want to fetch just this user
            await this.loadUsers();

        } catch (error) {
            console.error('Failed to refresh user data:', error);
        }
    },

    // Edit user with fresh data
    editUser(userId) {
        // Always reload users first to get fresh data
        this.loadUsers().then(() => {
            const user = this.state.users.find(u => u.id === userId);
            if (user) {
                this.state.editingUser = user;
                this.showUserModal('Edit User', user);
            } else {
                window.AdZetaApp.showNotification('User not found', 'danger');
            }
        });
    }
};
