// Simple Metrics Charts and Animations
document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple metrics script loaded');
    
    // Animate counter numbers
    function animateCounters() {
        console.log('Animating counters');
        const counters = document.querySelectorAll('.counter-number');
        
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const unit = counter.getAttribute('data-unit') || '';
            let current = 0;
            const increment = Math.ceil(target / 50); // Adjust for animation speed
            
            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    if (current > target) current = target;
                    counter.textContent = current + unit;
                    setTimeout(updateCounter, 30);
                }
            };
            
            updateCounter();
        });
    }
    
    // Initialize simple charts
    function initSimpleCharts() {
        console.log('Initializing simple charts');
        
        // ROAS Chart
        const roasChartEl = document.getElementById('roas-chart');
        if (roasChartEl) {
            console.log('Found ROAS chart element');
            try {
                new Chart(roasChartEl, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'ROAS',
                            data: [1.2, 1.8, 2.3, 2.9, 3.5, 4.0],
                            borderColor: '#2ecc71',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            pointRadius: 0,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                display: false
                            },
                            y: {
                                display: false
                            }
                        }
                    }
                });
                console.log('ROAS chart initialized');
            } catch (error) {
                console.error('Error initializing ROAS chart:', error);
            }
        } else {
            console.warn('ROAS chart element not found');
        }
        
        // CAC Chart
        const cacChartEl = document.getElementById('cac-chart');
        if (cacChartEl) {
            try {
                new Chart(cacChartEl, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'CAC',
                            data: [100, 90, 82, 75, 65, 64],
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            pointRadius: 0,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                display: false
                            },
                            y: {
                                display: false,
                                reverse: true
                            }
                        }
                    }
                });
                console.log('CAC chart initialized');
            } catch (error) {
                console.error('Error initializing CAC chart:', error);
            }
        }
        
        // Revenue Chart
        const revenueChartEl = document.getElementById('revenue-chart');
        if (revenueChartEl) {
            try {
                new Chart(revenueChartEl, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Revenue',
                            data: [100, 110, 115, 122, 125, 128],
                            borderColor: '#2ecc71',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            pointRadius: 0,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                display: false
                            },
                            y: {
                                display: false
                            }
                        }
                    }
                });
                console.log('Revenue chart initialized');
            } catch (error) {
                console.error('Error initializing Revenue chart:', error);
            }
        }
        
        // Ad Waste Chart
        const adWasteChartEl = document.getElementById('ad-waste-chart');
        if (adWasteChartEl) {
            try {
                new Chart(adWasteChartEl, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Ad Waste',
                            data: [100, 90, 80, 70, 60, 58],
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            pointRadius: 0,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                display: false
                            },
                            y: {
                                display: false,
                                reverse: true
                            }
                        }
                    }
                });
                console.log('Ad Waste chart initialized');
            } catch (error) {
                console.error('Error initializing Ad Waste chart:', error);
            }
        }
        
        // Comparison Chart
        const comparisonChartEl = document.getElementById('comparison-chart');
        if (comparisonChartEl) {
            try {
                new Chart(comparisonChartEl, {
                    type: 'bar',
                    data: {
                        labels: ['Traditional', 'Adzeta AI'],
                        datasets: [{
                            label: 'ROAS',
                            data: [1, 4],
                            backgroundColor: [
                                'rgba(149, 165, 166, 0.7)',
                                'rgba(143, 118, 245, 0.7)'
                            ],
                            borderColor: [
                                'rgba(149, 165, 166, 1)',
                                'rgba(143, 118, 245, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                console.log('Comparison chart initialized');
            } catch (error) {
                console.error('Error initializing Comparison chart:', error);
            }
        }
        
        // Donut Chart
        const donutChartEl = document.getElementById('donut-chart');
        if (donutChartEl) {
            try {
                new Chart(donutChartEl, {
                    type: 'doughnut',
                    data: {
                        labels: ['Wasted Spend', 'Effective Spend'],
                        datasets: [{
                            data: [42, 58],
                            backgroundColor: [
                                'rgba(231, 76, 60, 0.7)',
                                'rgba(46, 204, 113, 0.7)'
                            ],
                            borderColor: [
                                'rgba(231, 76, 60, 1)',
                                'rgba(46, 204, 113, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '70%',
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
                console.log('Donut chart initialized');
            } catch (error) {
                console.error('Error initializing Donut chart:', error);
            }
        }
    }
    
    // Handle testimonial navigation
    function setupTestimonialNav() {
        console.log('Setting up testimonial navigation');
        const prevBtn = document.querySelector('.testimonial-prev');
        const nextBtn = document.querySelector('.testimonial-next');
        const testimonials = document.querySelectorAll('.testimonial-item');
        
        if (!prevBtn || !nextBtn || testimonials.length === 0) {
            console.warn('Testimonial elements not found');
            return;
        }
        
        let currentIndex = 0;
        
        // Hide all testimonials except the first one
        testimonials.forEach((testimonial, index) => {
            if (index !== 0) {
                testimonial.style.display = 'none';
            }
        });
        
        prevBtn.addEventListener('click', () => {
            testimonials[currentIndex].style.display = 'none';
            currentIndex = (currentIndex - 1 + testimonials.length) % testimonials.length;
            testimonials[currentIndex].style.display = 'block';
            console.log('Switched to testimonial', currentIndex);
        });
        
        nextBtn.addEventListener('click', () => {
            testimonials[currentIndex].style.display = 'none';
            currentIndex = (currentIndex + 1) % testimonials.length;
            testimonials[currentIndex].style.display = 'block';
            console.log('Switched to testimonial', currentIndex);
        });
    }
    
    // Initialize everything
    console.log('Initializing metrics section');
    animateCounters();
    initSimpleCharts();
    setupTestimonialNav();
});
