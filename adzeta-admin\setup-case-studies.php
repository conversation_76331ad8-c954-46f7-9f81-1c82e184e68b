<?php
/**
 * Case Studies Setup Script
 * Creates the case_studies table and inserts sample data
 */

// Database configuration
$host = 'localhost';
$dbname = 'adzetadb';
$username = 'adzetauser';
$password = 'Crazy1395#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    echo "Connected to database successfully.\n";

    // Read and execute the schema
    $schemaFile = __DIR__ . '/database/case-studies-schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }

    $sql = file_get_contents($schemaFile);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "✓ Executed SQL statement successfully\n";
            } catch (PDOException $e) {
                // Skip if table already exists
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    echo "⚠ Table already exists, skipping creation\n";
                } else {
                    throw $e;
                }
            }
        }
    }

    // Check if case studies table was created
    $result = $pdo->query("SHOW TABLES LIKE 'case_studies'");
    if ($result->rowCount() > 0) {
        echo "✓ Case studies table created/verified successfully\n";
        
        // Check if sample data exists
        $count = $pdo->query("SELECT COUNT(*) as count FROM case_studies")->fetch();
        echo "📊 Current case studies count: " . $count['count'] . "\n";
        
        if ($count['count'] > 0) {
            echo "✓ Sample case study data already exists\n";
        }
    } else {
        throw new Exception("Failed to create case_studies table");
    }

    echo "\n🎉 Case Studies setup completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Visit http://localhost/adzeta-admin/?view=case-studies\n";
    echo "2. Click 'New Case Study' to test the editor\n";
    echo "3. Use the AI assistant to generate content\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
