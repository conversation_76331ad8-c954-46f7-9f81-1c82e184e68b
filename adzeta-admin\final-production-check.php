<?php
/**
 * SEO System Production Check - Verify SEO system is ready for production
 * This is the only remaining test file for production verification
 */

header('Content-Type: text/html');

echo "<h1>🚀 Final Production Readiness Check</h1>";

$checks = [];
$errors = [];
$warnings = [];

// Check 1: Database and Tables
echo "<h2>1. Database & Tables</h2>";

try {
    $pdo = new PDO(
        'mysql:host=localhost;dbname=adzetadb;charset=utf8mb4',
        'adzetauser',
        'Crazy1395#',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $checks[] = "Database connection successful";
    
    // Check tables
    $tables = ['page_seo', 'seo_settings'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $countStmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $countStmt->fetch()['count'];
            $checks[] = "Table '$table' exists with $count records";
        } else {
            $errors[] = "Table '$table' missing";
        }
    }
    
} catch (PDOException $e) {
    $errors[] = "Database error: " . $e->getMessage();
}

// Check 2: Critical Files
echo "<h2>2. Critical Files</h2>";

$criticalFiles = [
    'PageSEOController' => __DIR__ . '/src/API/PageSEOController.php',
    'SEO Functions' => __DIR__ . '/../includes/seo-functions.php',
    'Bootstrap' => __DIR__ . '/bootstrap.php',
    'BaseController' => __DIR__ . '/src/API/BaseController.php'
];

foreach ($criticalFiles as $name => $path) {
    if (file_exists($path)) {
        $checks[] = "$name file exists";
    } else {
        $errors[] = "$name file missing: $path";
    }
}

// Check 3: PageSEOController Loading
echo "<h2>3. PageSEOController Verification</h2>";

$controllerPath = __DIR__ . '/src/API/PageSEOController.php';

if (file_exists($controllerPath)) {
    try {
        require_once __DIR__ . '/bootstrap.php';
        require_once $controllerPath;
        
        $controller = new \AdZetaAdmin\API\PageSEOController();
        $checks[] = "PageSEOController loads and instantiates successfully";
        
        // Check required methods
        $requiredMethods = ['index', 'show', 'store', 'update', 'destroy', 'toggleStatus', 'getSettings', 'updateSettings'];
        $missingMethods = [];
        
        foreach ($requiredMethods as $method) {
            if (!method_exists($controller, $method)) {
                $missingMethods[] = $method;
            }
        }
        
        if (empty($missingMethods)) {
            $checks[] = "All required controller methods exist";
        } else {
            $errors[] = "Missing controller methods: " . implode(', ', $missingMethods);
        }
        
    } catch (Exception $e) {
        $errors[] = "PageSEOController loading error: " . $e->getMessage();
    }
} else {
    $errors[] = "PageSEOController file missing";
}

// Check 4: API Endpoints
echo "<h2>4. API Endpoints</h2>";

$endpoints = [
    '/adzeta-admin/api/page-seo' => 'Page SEO listing',
    '/adzeta-admin/api/seo-settings' => 'SEO settings'
];

foreach ($endpoints as $endpoint => $description) {
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents("http://localhost$endpoint", false, $context);
        
        if ($response !== false) {
            $json = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($json['success'])) {
                $checks[] = "$description API endpoint working";
            } else {
                $warnings[] = "$description API endpoint returns invalid JSON";
            }
        } else {
            $warnings[] = "$description API endpoint not accessible";
        }
    } catch (Exception $e) {
        $warnings[] = "$description API endpoint error: " . $e->getMessage();
    }
}

// Check 5: SEO Functions
echo "<h2>5. SEO Functions</h2>";

$seoFunctionsPath = __DIR__ . '/../includes/seo-functions.php';

if (file_exists($seoFunctionsPath)) {
    try {
        require_once $seoFunctionsPath;
        $checks[] = "SEO functions library loaded";
        
        // Test function
        $_SERVER['REQUEST_URI'] = '/platform.php';
        $seoData = getCurrentPageSEO();
        if ($seoData && !empty($seoData['page_title'])) {
            $checks[] = "SEO data retrieval working";
        } else {
            $warnings[] = "SEO data retrieval returns empty results";
        }
        
        // Test SEO output
        $seoOutput = renderSEOTags($seoData);
        if (!empty($seoOutput)) {
            $checks[] = "SEO tags generation working";
        } else {
            $warnings[] = "SEO tags generation returns empty output";
        }
        
    } catch (Exception $e) {
        $errors[] = "SEO functions error: " . $e->getMessage();
    }
} else {
    $errors[] = "SEO functions library missing";
}

// Check 6: Header Files
echo "<h2>6. Header Files</h2>";

$headerFiles = [
    'header.php' => __DIR__ . '/../header.php',
    'header-light.php' => __DIR__ . '/../header-light.php'
];

foreach ($headerFiles as $name => $path) {
    if (file_exists($path)) {
        $checks[] = "$name exists";
        
        $content = file_get_contents($path);
        if (strpos($content, 'blog-list-dynamic.php') !== false) {
            $checks[] = "$name has updated blog links";
        } else {
            $warnings[] = "$name may not have updated blog links";
        }
    } else {
        $warnings[] = "$name missing";
    }
}

// Display Results
echo "<hr>";
echo "<h2>📊 Final Results</h2>";

if (!empty($checks)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
    echo "<h3>✅ Successful Checks (" . count($checks) . ")</h3>";
    echo "<ul>";
    foreach ($checks as $check) {
        echo "<li>$check</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($warnings)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404; margin: 10px 0;'>";
    echo "<h3>⚠️ Warnings (" . count($warnings) . ")</h3>";
    echo "<ul>";
    foreach ($warnings as $warning) {
        echo "<li>$warning</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
    echo "<h3>❌ Critical Errors (" . count($errors) . ")</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Final Production Status
$isProductionReady = empty($errors);
$totalIssues = count($errors);

echo "<hr>";
if ($isProductionReady) {
    echo "<div style='background: #d4edda; padding: 25px; border-radius: 5px; color: #155724; text-align: center;'>";
    echo "<h1>🎉 PRODUCTION READY!</h1>";
    echo "<p style='font-size: 18px;'><strong>The SEO management system is fully operational and ready for production deployment.</strong></p>";
    
    echo "<h3>🚀 Deployment Steps:</h3>";
    echo "<div style='text-align: left; max-width: 600px; margin: 0 auto;'>";
    echo "<ol>";
    echo "<li><strong>Access Admin Panel:</strong> <a href='/adzeta-admin/?view=seo' target='_blank' style='color: #155724; font-weight: bold;'>SEO Management Interface</a></li>";
    echo "<li><strong>Integrate Headers:</strong> Add SEO functions to your page templates</li>";
    echo "<li><strong>Test Frontend:</strong> Verify SEO data appears on your pages</li>";
    echo "<li><strong>Configure Settings:</strong> Customize global SEO settings</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>📝 Integration Code:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: left; font-size: 12px; max-width: 500px; margin: 0 auto;'>";
    echo htmlspecialchars("<?php
// Add to your header.php file
require_once 'includes/seo-functions.php';
echo renderSEOTags();
?>");
    echo "</pre>";
    
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 25px; border-radius: 5px; color: #721c24; text-align: center;'>";
    echo "<h1>❌ NOT PRODUCTION READY</h1>";
    echo "<p style='font-size: 18px;'><strong>Critical issues must be resolved before production deployment.</strong></p>";
    echo "<p><strong>Critical Issues Found:</strong> $totalIssues</p>";
    
    echo "<h3>🔧 Troubleshooting:</h3>";
    echo "<div style='text-align: left; max-width: 600px; margin: 0 auto;'>";
    echo "<ol>";
    echo "<li><strong>Check Files:</strong> <a href='/adzeta-admin/check-files.php' target='_blank' style='color: #721c24; font-weight: bold;'>File Existence Check</a></li>";
    echo "<li><strong>Debug API:</strong> <a href='/adzeta-admin/debug-api-endpoints.php' target='_blank' style='color: #721c24; font-weight: bold;'>API Endpoint Debug</a></li>";
    echo "<li><strong>Fix Issues:</strong> <a href='/adzeta-admin/fix-seo-production.php' target='_blank' style='color: #721c24; font-weight: bold;'>Run Fix Script</a></li>";
    echo "</ol>";
    echo "</div>";
    echo "</div>";
}

echo "<hr>";
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; color: #495057; text-align: center;'>";
echo "<p><strong>SEO Management System</strong> | Production Readiness Check</p>";
echo "<p>Generated: " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";
?>
