<?php
/**
 * AdZeta Admin Panel - Main Entry Point
 * Professional Blog Management System
 */

// Configuration
$config = [
    'site_name' => 'AdZeta Blog',
    'api_url' => '/adzeta-admin/api',
    'base_url' => '/adzeta-admin',
    'version' => '1.0.0'
];

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($config['site_name']); ?> - Admin Panel</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/apple-cache-styles.css" rel="stylesheet">

    <!-- Editor.js Dependencies (Local files for reliability) -->
    <script src="assets/js/vendor/editorjs.min.js"></script>
    <script src="assets/js/vendor/header.min.js"></script>
    <script src="assets/js/vendor/list.min.js"></script>
    <script src="assets/js/vendor/paragraph.min.js"></script>
    <script src="assets/js/vendor/quote.min.js"></script>
    <script src="assets/js/vendor/delimiter.min.js"></script>
    <script src="assets/js/vendor/image.min.js"></script>

    <!-- Custom AdZeta Editor.js Tools -->
    <script src="assets/js/editor-tools/bold-tool.js"></script>
    <script src="assets/js/editor-tools/italic-tool.js"></script>
    <script src="assets/js/editor-tools/underline-tool.js"></script>
    <script src="assets/js/editor-tools/styled-card-tool.js"></script>
    <script src="assets/js/editor-tools/statistics-tool.js"></script>
    <script src="assets/js/editor-tools/cta-tool.js"></script>
    <script src="assets/js/editor-tools/highlight-tool.js"></script>

    <!-- AI Content Processor -->
    <script src="assets/js/modules/ai-content-processor.js"></script>

    <!-- Debug Tools (Development) -->
    <script src="assets/js/debug/ai-content-debug.js"></script>

    <!-- Custom Styles -->
    <link href="assets/css/admin.css" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading" class="loading-screen">
        <div class="text-center">
            <div class="brand-logo mb-3">AZ</div>
            <div class="spinner mb-3"></div>
            <p class="text-white">Loading Admin Panel...</p>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app">
        <!-- Login Page -->
        <div id="loginPage" class="login-page">
            <div class="login-container">
                <div class="text-center">
                    <div class="brand-logo">AZ</div>
                    <h1 class="h3 mb-1"><?php echo htmlspecialchars($config['site_name']); ?></h1>
                    <p class="text-muted mb-4">Welcome back! Please sign in to your account.</p>
                </div>

                <div id="loginError" class="alert alert-danger" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="loginErrorText"></span>
                </div>

                <form id="loginForm">
                    <div class="mb-3">
                        <label class="form-label">Username</label>
                        <input type="text" id="username" class="form-control" placeholder="Enter your username" value="admin" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" id="password" class="form-control" placeholder="Enter your password" value="admin123" required>
                    </div>

                    <div class="mb-4">
                        <label class="d-flex align-items-center">
                            <input type="checkbox" id="remember" class="me-2">
                            <span class="text-sm text-muted">Remember me for 30 days</span>
                        </label>
                    </div>

                    <button type="submit" id="loginBtn" class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <span id="loginBtnText">Sign In</span>
                    </button>
                </form>

                <div class="text-center mt-4">
                    <small class="text-muted">Demo credentials: <strong>admin</strong> / <strong>admin123</strong></small>
                </div>
            </div>
        </div>

        <!-- Admin Dashboard -->
        <div id="adminDashboard" class="admin-layout" style="display: none;">
            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <a href="#" class="sidebar-brand">
                        <div class="brand-icon">AZ</div>
                        <span class="brand-text"><?php echo htmlspecialchars($config['site_name']); ?></span>
                    </a>
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <nav class="sidebar-nav">
                    <div class="nav-item">
                        <a href="#" data-view="dashboard" class="nav-link active">
                            <i class="fas fa-home"></i>
                            <span class="nav-link-text">Dashboard</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="add-post" class="nav-link">
                            <i class="fas fa-plus-circle"></i>
                            <span class="nav-link-text">Add Post</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="posts" class="nav-link">
                            <i class="fas fa-edit"></i>
                            <span class="nav-link-text">Blog Posts</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="add-case-study" class="nav-link">
                            <i class="fas fa-plus-circle"></i>
                            <span class="nav-link-text">Add Case Study</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="case-studies" class="nav-link">
                            <i class="fas fa-chart-line"></i>
                            <span class="nav-link-text">Case Studies</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="whitepapers" class="nav-link">
                            <i class="fas fa-file-pdf"></i>
                            <span class="nav-link-text">Whitepapers</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="pages" class="nav-link">
                            <i class="fas fa-file-alt"></i>
                            <span class="nav-link-text">Pages</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="media" class="nav-link">
                            <i class="fas fa-images"></i>
                            <span class="nav-link-text">Media Library</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="seo" class="nav-link">
                            <i class="fas fa-search"></i>
                            <span class="nav-link-text">SEO Management</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="users" class="nav-link">
                            <i class="fas fa-users"></i>
                            <span class="nav-link-text">Users</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="ai-settings" class="nav-link">
                            <i class="fas fa-robot"></i>
                            <span class="nav-link-text">AI Assistant</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="error-logs" class="nav-link">
                            <i class="fas fa-bug text-danger"></i>
                            <span class="nav-link-text">Error Logs</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" data-view="settings" class="nav-link">
                            <i class="fas fa-cog"></i>
                            <span class="nav-link-text">Settings</span>
                        </a>
                    </div>
                </nav>

                <div class="sidebar-footer">
                    <div class="dropdown">
                        <a href="#" class="nav-link d-flex align-items-center" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i>
                            <span class="user-details">
                                <span id="currentUser">Admin</span>
                                <i class="fas fa-chevron-down ms-auto"></i>
                            </span>
                        </a>
                        <div class="dropdown-menu">
                            <a href="#" class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content" id="mainContent">
                <!-- Content Header -->
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0" id="pageTitle">
                                <i class="fas fa-home me-2"></i>
                                Dashboard
                            </h1>
                            <p class="text-muted mb-0" id="pageSubtitle">
                                Welcome back! Here's what's happening with your blog.
                            </p>
                        </div>
                        <div id="pageActions">
                            <!-- Dynamic action buttons -->
                        </div>
                    </div>
                </div>

                <!-- Content Body -->
                <div class="content-body">
                    <!-- Dashboard View -->
                    <div id="dashboardView" class="view-content">
                        <!-- Content loaded by dashboard.js -->
                    </div>

                    <!-- Blog Posts View -->
                    <div id="postsView" class="view-content" style="display: none;">
                        <!-- Content loaded by posts.js -->
                    </div>

                    <!-- Add Case Study View -->
                    <div id="add-case-studyView" class="view-content" style="display: none;">
                        <!-- Content loaded by case-study-editor.js -->
                    </div>

                    <!-- Case Studies View -->
                    <div id="case-studiesView" class="view-content" style="display: none;">
                        <!-- Content loaded by case-studies.js -->
                    </div>

                    <!-- Whitepapers View -->
                    <div id="whitepapersView" class="view-content" style="display: none;">
                        <!-- Content loaded by whitepapers.js -->
                    </div>

                    <!-- Post Editor View -->
                    <div id="postEditorView" class="view-content" style="display: none;">
                        <!-- Content loaded by post-editor.js -->
                    </div>

                    <!-- Other Views -->
                    <div id="pagesView" class="view-content" style="display: none;">
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h4>Pages Management</h4>
                            <p class="text-muted">Coming soon...</p>
                        </div>
                    </div>

                    <!-- Media Library View -->
                    <div id="mediaView" class="view-content" style="display: none;">
                        <!-- Content loaded by media-manager.js -->
                    </div>

                    <!-- Users View -->
                    <div id="usersView" class="view-content" style="display: none;">
                        <!-- Content loaded by users.js -->
                    </div>

                    <!-- SEO Management View -->
                    <div id="seoView" class="view-content" style="display: none;">
                        <!-- Content loaded by seo-manager.js -->
                    </div>

                    <!-- AI Settings View -->
                    <div id="ai-settingsView" class="view-content" style="display: none;">
                        <!-- AI Settings content will be loaded by ai-settings.js -->
                    </div>

                    <!-- Error Logs View -->
                    <div id="error-logsView" class="view-content" style="display: none;">
                        <!-- Error Logs content will be loaded by error-logs.js -->
                    </div>

                    <!-- Settings View -->
                    <div id="settingsView" class="view-content" style="display: none;">
                        <!-- Settings content will be loaded by settings.js -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <!-- Core Application Scripts -->
    <script>
        // Global configuration
        window.AdZetaConfig = <?php echo json_encode($config); ?>;
    </script>
    <script src="assets/js/core/api-logger.js"></script>
    <script src="assets/js/core/error-monitor.js"></script>
    <script src="assets/js/core/app.js"></script>
    <script src="assets/js/core/auth.js"></script>
    <script src="assets/js/core/navigation.js"></script>
    <script src="assets/js/modules/dashboard.js"></script>
    <script src="assets/js/modules/posts.js"></script>
    <script src="assets/js/modules/case-studies.js"></script>
    <script src="assets/js/modules/case-study-editor.js"></script>
    <script src="assets/js/modules/whitepapers.js"></script>
    <script src="assets/js/modules/templates.js"></script>
    <script src="assets/js/modules/media-library.js"></script>
    <script src="assets/js/modules/media-manager.js"></script>
    <script src="assets/js/modules/users.js"></script>
    <script src="assets/js/modules/post-editor.js"></script>
    <script src="assets/js/modules/seo-analyzer.js"></script>
    <script src="assets/js/modules/seo-manager.js"></script>
    <script src="assets/js/modules/error-logs.js"></script>
    <script src="assets/js/modules/simple-cache-settings.js"></script>
    <script src="assets/js/modules/settings.js"></script>
    <script src="assets/js/modules/ai-assistant.js"></script>
    <script src="assets/js/modules/ai-settings.js"></script>

    <!-- Initialize Application -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Debug: Check if modules are loaded
            console.log('Checking module availability:');
            console.log('- AdZetaApp:', typeof window.AdZetaApp);
            console.log('- AdZetaErrorLogs:', typeof window.AdZetaErrorLogs);
            console.log('- AdZetaNavigation:', typeof window.AdZetaNavigation);

            AdZetaApp.init();
        });
    </script>
</body>
</html>
