<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Case Studies</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Case Studies Debug</h1>
        <div id="debug-output"></div>
        <div id="api-response"></div>
        <div id="case-studies-container"></div>
    </div>

    <script>
        async function debugCaseStudies() {
            const debugOutput = document.getElementById('debug-output');
            const apiResponse = document.getElementById('api-response');
            const container = document.getElementById('case-studies-container');
            
            try {
                debugOutput.innerHTML = '<p>Testing API endpoint...</p>';
                
                // Test the API directly
                const response = await fetch('/adzeta-admin/api/case-studies');
                const data = await response.json();
                
                apiResponse.innerHTML = `
                    <h3>API Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                if (data.success && data.case_studies) {
                    debugOutput.innerHTML = `
                        <div class="alert alert-success">
                            ✅ API working! Found ${data.case_studies.length} case studies
                        </div>
                    `;
                    
                    // Display the case studies
                    container.innerHTML = `
                        <h3>Case Studies:</h3>
                        <div class="row">
                            ${data.case_studies.map(cs => `
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title">${cs.title}</h5>
                                            <p class="card-text">Client: ${cs.client_name}</p>
                                            <p class="card-text">Industry: ${cs.industry}</p>
                                            <p class="card-text">Status: ${cs.status}</p>
                                            <small class="text-muted">Created: ${cs.created_at}</small>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                } else {
                    debugOutput.innerHTML = `
                        <div class="alert alert-danger">
                            ❌ API Error: ${data.message || 'Unknown error'}
                        </div>
                    `;
                }
                
            } catch (error) {
                debugOutput.innerHTML = `
                    <div class="alert alert-danger">
                        ❌ Error: ${error.message}
                    </div>
                `;
                console.error('Debug error:', error);
            }
        }
        
        // Run debug when page loads
        document.addEventListener('DOMContentLoaded', debugCaseStudies);
    </script>
</body>
</html>
