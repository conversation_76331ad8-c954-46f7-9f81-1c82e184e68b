<?php

namespace AdZetaAdmin\Frontend;

/**
 * WordPress-Inspired Template Engine
 * Supports multiple post templates and dynamic template selection
 */
class TemplateEngine {
    private $templateDir;
    private $availableTemplates;

    public function __construct() {
        $this->templateDir = __DIR__ . '/../../templates/';
        $this->loadAvailableTemplates();
    }

    /**
     * Load available templates
     */
    private function loadAvailableTemplates() {
        $this->availableTemplates = [
            'blog-post' => [
                'professional-enhanced' => [
                    'name' => 'Professional Enhanced',
                    'description' => 'Premium blog template inspired by Medium, NYT, and HBR for maximum readability',
                    'file' => 'blog-post-professional-enhanced.php',
                    'preview' => 'professional-enhanced-preview.jpg'
                ]
                // Other templates removed - only Professional Enhanced available
            ],
            'case-study' => [
                'professional-case-study' => [
                    'name' => 'Professional Case Study',
                    'description' => 'Comprehensive case study template inspired by <PERSON><PERSON><PERSON><PERSON><PERSON>, BCG, and HBR',
                    'file' => 'case-study-professional.php',
                    'preview' => 'case-study-preview.jpg'
                ]
            ],
            'blog-list' => [
                'modern-grid' => [
                    'name' => 'Modern Grid',
                    'description' => 'Card-based grid layout',
                    'file' => 'blog-list-grid.php',
                    'preview' => 'grid-preview.jpg'
                ],
                'magazine-style' => [
                    'name' => 'Magazine Style',
                    'description' => 'Magazine-inspired listing',
                    'file' => 'blog-list-magazine.php',
                    'preview' => 'magazine-list-preview.jpg'
                ],
                'minimal-list' => [
                    'name' => 'Minimal List',
                    'description' => 'Clean, simple list view',
                    'file' => 'blog-list-minimal.php',
                    'preview' => 'minimal-list-preview.jpg'
                ]
            ]
        ];
    }

    /**
     * Render blog post with selected template
     */
    public function renderBlogPost($data) {
        $template = $data['template'] ?? 'professional-enhanced';
        $post = $data['post'];

        if (!$post) {
            return false;
        }

        // Get template file
        $templateFile = $this->getTemplateFile('blog-post', $template);

        if (!$templateFile || !file_exists($templateFile)) {
            // Fallback to default template
            $templateFile = $this->getTemplateFile('blog-post', 'professional-enhanced');
        }

        // Set up template data
        $this->setupTemplateData($post, $template);

        // Include template
        if ($templateFile && file_exists($templateFile)) {
            include $templateFile;
            return true;
        }

        // Ultimate fallback - use current blog-post.php
        if (file_exists(__DIR__ . '/../../../blog-post.php')) {
            // Set the slug for the old template
            $_GET['slug'] = $post['slug'];
            include __DIR__ . '/../../../blog-post.php';
            return true;
        }

        return false;
    }

    /**
     * Render blog list with selected template
     */
    public function renderBlogList($data) {
        $template = $data['template'] ?? 'modern-grid';
        $page = $data['page'] ?? 1;
        $category = $data['category'] ?? null;
        $tag = $data['tag'] ?? null;
        $search = $data['search'] ?? null;

        // Get template file
        $templateFile = $this->getTemplateFile('blog-list', $template);

        if (!$templateFile || !file_exists($templateFile)) {
            // Fallback to default template
            $templateFile = $this->getTemplateFile('blog-list', 'modern-grid');
        }

        // Set up template data
        $this->setupBlogListData($page, $category, $tag, $search, $template);

        // Include template
        if ($templateFile && file_exists($templateFile)) {
            include $templateFile;
            return true;
        }

        // Ultimate fallback - use current blog-list-dynamic.php
        if (file_exists(__DIR__ . '/../../../blog-list-dynamic.php')) {
            // Set query parameters for the old template
            $_GET['page'] = $page;
            if ($category) $_GET['category'] = $category;
            if ($tag) $_GET['tag'] = $tag;
            if ($search) $_GET['search'] = $search;

            include __DIR__ . '/../../../blog-list-dynamic.php';
            return true;
        }

        return false;
    }

    /**
     * Get template file path
     */
    private function getTemplateFile($type, $template) {
        if (!isset($this->availableTemplates[$type][$template])) {
            return null;
        }

        $templateInfo = $this->availableTemplates[$type][$template];
        return $this->templateDir . $templateInfo['file'];
    }

    /**
     * Setup template data for blog post
     */
    private function setupTemplateData($post, $template) {
        // Make post data globally available
        global $currentPost, $currentTemplate, $templateEngine;

        $currentPost = $post;
        $currentTemplate = $template;
        $templateEngine = $this;

        // Set up SEO data
        global $pageData;
        $pageData = [
            'title' => $post['meta_title'] ?: $post['title'] . ' - AdZeta Blog',
            'description' => $post['meta_description'] ?: $post['excerpt'],
            'keywords' => $post['meta_keywords'],
            'canonical' => $this->getBaseUrl() . 'blog/' . $post['slug'],
            'og_title' => $post['og_title'] ?: $post['title'],
            'og_description' => $post['og_description'] ?: $post['excerpt'],
            'og_image' => $post['featured_image'] ? $this->getBaseUrl() . ltrim($post['featured_image'], '/') : $this->getBaseUrl() . 'images/adzeta-og-default.jpg',
            'og_type' => 'article',
            'article_author' => $post['first_name'] . ' ' . $post['last_name'],
            'article_published_time' => $post['published_at'],
            'article_modified_time' => $post['updated_at'],
            'twitter_card' => $post['twitter_card_type'] ?: 'summary_large_image'
        ];
    }

    /**
     * Setup template data for blog list
     */
    private function setupBlogListData($page, $category, $tag, $search, $template) {
        // Make data globally available
        global $currentPage, $currentCategory, $currentTag, $currentSearch, $currentTemplate, $templateEngine;

        $currentPage = $page;
        $currentCategory = $category;
        $currentTag = $tag;
        $currentSearch = $search;
        $currentTemplate = $template;
        $templateEngine = $this;

        // Load blog data
        require_once __DIR__ . '/../../../includes/BlogDatabase.php';

        $postsPerPage = 12;

        // Get blog posts
        global $blogPosts, $totalPosts, $totalPages, $categories;

        $blogPosts = getBlogPosts([
            'page' => $page,
            'limit' => $postsPerPage,
            'category' => $category,
            'tag' => $tag,
            'search' => $search
        ]);

        $totalPosts = getBlogDatabase()->getBlogPostsCount([
            'category' => $category,
            'tag' => $tag,
            'search' => $search
        ]);

        $totalPages = ceil($totalPosts / $postsPerPage);
        $categories = getBlogCategories();

        // Set up SEO data
        global $pageData;
        $pageTitle = 'Performance Marketing Blog - AdZeta';
        $pageDescription = 'Latest insights on e-commerce growth, AI marketing, value-based bidding, and performance optimization strategies.';

        if ($category) {
            $categoryName = '';
            foreach ($categories as $cat) {
                if ($cat['slug'] === $category) {
                    $categoryName = $cat['name'];
                    break;
                }
            }
            if ($categoryName) {
                $pageTitle = $categoryName . ' - AdZeta Blog';
                $pageDescription = "Latest {$categoryName} insights and strategies for e-commerce growth.";
            }
        }

        if ($search) {
            $pageTitle = "Search Results for '{$search}' - AdZeta Blog";
            $pageDescription = "Search results for '{$search}' in our performance marketing blog.";
        }

        $pageData = [
            'title' => $pageTitle,
            'description' => $pageDescription,
            'keywords' => 'performance marketing blog, e-commerce growth, AI marketing, value-based bidding, customer lifetime value, marketing attribution',
            'canonical' => $this->getBaseUrl() . 'blog/' . ($category ? "category/{$category}/" : '') . ($page > 1 ? "page/{$page}/" : ''),
            'og_image' => $this->getBaseUrl() . 'images/blog-og-image.jpg'
        ];
    }

    /**
     * Get available templates for admin interface
     */
    public function getAvailableTemplates($type = null) {
        if ($type) {
            return $this->availableTemplates[$type] ?? [];
        }

        return $this->availableTemplates;
    }

    /**
     * Get template preview URL
     */
    public function getTemplatePreview($type, $template) {
        if (!isset($this->availableTemplates[$type][$template])) {
            return null;
        }

        $templateInfo = $this->availableTemplates[$type][$template];
        return $this->getBaseUrl() . 'adzeta-admin/assets/images/template-previews/' . $templateInfo['preview'];
    }

    /**
     * Save template settings
     */
    public function saveTemplateSettings($settings) {
        $settingsFile = __DIR__ . '/../../cache/template-settings.json';
        $cacheDir = dirname($settingsFile);

        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }

        return file_put_contents($settingsFile, json_encode($settings, JSON_PRETTY_PRINT)) !== false;
    }

    /**
     * Load template settings
     */
    public function loadTemplateSettings() {
        $settingsFile = __DIR__ . '/../../cache/template-settings.json';

        if (file_exists($settingsFile)) {
            $settings = json_decode(file_get_contents($settingsFile), true);
            return $settings ?: [];
        }

        return [
            'blog-list' => 'modern-grid',
            'blog-category' => 'modern-grid',
            'blog-tag' => 'modern-grid',
            'blog-post' => 'professional-enhanced'  // Only template available
        ];
    }

    /**
     * Get base URL
     */
    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . '/';
    }

    /**
     * Render template selector for admin
     */
    public function renderTemplateSelector($type, $currentTemplate = null) {
        $templates = $this->getAvailableTemplates($type);

        if (empty($templates)) {
            return '<p>No templates available for this content type.</p>';
        }

        $html = '<div class="template-selector">';

        foreach ($templates as $templateKey => $template) {
            $isSelected = $currentTemplate === $templateKey;
            $selectedClass = $isSelected ? 'selected' : '';

            $html .= '<div class="template-option ' . $selectedClass . '" data-template="' . $templateKey . '">';
            $html .= '<div class="template-preview">';
            $html .= '<img src="' . $this->getTemplatePreview($type, $templateKey) . '" alt="' . htmlspecialchars($template['name']) . '">';
            $html .= '</div>';
            $html .= '<div class="template-info">';
            $html .= '<h4>' . htmlspecialchars($template['name']) . '</h4>';
            $html .= '<p>' . htmlspecialchars($template['description']) . '</p>';
            $html .= '</div>';
            if ($isSelected) {
                $html .= '<div class="template-selected-badge">✓ Selected</div>';
            }
            $html .= '</div>';
        }

        $html .= '</div>';

        return $html;
    }
}
