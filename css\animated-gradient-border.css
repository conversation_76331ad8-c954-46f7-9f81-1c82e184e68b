/**
 * Animated Gradient Border
 * Based on the technique from ibelick.com
 */

/* Property-based approach for modern browsers */
@property --angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

/* High-tech animated border with moving gradient */
.high-tech-border {
  position: relative;
  border-radius: 20px;
  padding: 1px; /* Creates space for the border */
  background: linear-gradient(var(--angle), #e958a1, #ff5d74, #8f76f5, #e958a1);
  background-size: 400% 400%;
  animation: rotate-gradient 4s linear infinite;
  z-index: 0;
}

/* Inner content area */
.high-tech-border::after {
  content: "";
  display: block;
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  border-radius: 19px;
  background: rgba(255, 255, 255, 0.30);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: -1;
}

/* Animation for rotating gradient */
@keyframes rotate-gradient {
  to {
    --angle: 360deg;
  }
}

/* Fallback for browsers that don't support @property */
@supports not (background: linear-gradient(var(--angle), #e958a1, #ff5d74)) {
  .high-tech-border {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
  }
  
  .high-tech-border::before {
    content: "";
    position: absolute;
    z-index: -2;
    left: -50%;
    top: -50%;
    width: 200%;
    height: 200%;
    background-color: transparent;
    background-repeat: no-repeat;
    background-size: 100% 100%, 50% 50%;
    background-position: 0 0, 100% 0, 100% 100%, 0 100%;
    background-image: linear-gradient(90deg, #e958a1, #ff5d74, #8f76f5, #e958a1);
    animation: rotate-fallback 4s linear infinite;
  }
  
  .high-tech-border::after {
    content: "";
    position: absolute;
    z-index: -1;
    left: 1px;
    top: 1px;
    width: calc(100% - 2px);
    height: calc(100% - 2px);
    background: rgba(255, 255, 255, 0.30);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 19px;
  }
  
  @keyframes rotate-fallback {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

/* Ensure content is positioned correctly */
.high-tech-border > * {
  position: relative;
  z-index: 1;
}

/* Add subtle glow effect */
.high-tech-border {
  box-shadow: 0 0 15px rgba(233, 88, 161, 0.2);
}
