<?php

namespace AdZetaAdmin\Core;

/**
 * Authentication and Authorization Class
 * Handles user login, permissions, and security
 */
class Auth
{
    private $db;
    private $maxAttempts;
    private $lockoutDuration;

    public function __construct()
    {
        global $admin_db;
        $this->db = $admin_db;
        $this->maxAttempts = $GLOBALS['adzeta_admin_config']['max_login_attempts'] ?? 5;
        $this->lockoutDuration = $GLOBALS['adzeta_admin_config']['lockout_duration'] ?? 900;
    }

    public function login($username, $password)
    {
        // Check if account is locked
        if ($this->isAccountLocked($username)) {
            return [
                'success' => false,
                'message' => 'Account is temporarily locked due to too many failed attempts.'
            ];
        }

        // Validate credentials
        $user = $this->validateCredentials($username, $password);
        
        if (!$user) {
            $this->recordFailedAttempt($username);
            return [
                'success' => false,
                'message' => 'Invalid username or password.'
            ];
        }

        // Check if user is active
        if ($user['status'] !== 'active') {
            return [
                'success' => false,
                'message' => 'Account is not active.'
            ];
        }

        // Successful login
        $this->createSession($user);
        $this->updateLastLogin($user['id']);
        $this->clearFailedAttempts($username);

        return [
            'success' => true,
            'message' => 'Login successful.'
        ];
    }

    public function logout()
    {
        session_destroy();
        session_start();
    }

    public function isLoggedIn()
    {
        return isset($_SESSION['admin_user_id']) && 
               isset($_SESSION['admin_logged_in']) && 
               $_SESSION['admin_logged_in'] === true;
    }

    public function getCurrentUser()
    {
        if (!$this->isLoggedIn()) {
            return null;
        }

        $userId = $_SESSION['admin_user_id'];
        return $this->db->fetch(
            "SELECT id, username, email, first_name, last_name, role, avatar
             FROM users WHERE id = ? AND status = 'active'",
            [$userId]
        );
    }

    public function hasPermission($permission)
    {
        $user = $this->getCurrentUser();
        if (!$user) {
            return false;
        }

        // Admin has all permissions
        if ($user['role'] === 'admin') {
            return true;
        }

        // Define role-based permissions
        $permissions = [
            'editor' => [
                'view_dashboard',
                'manage_posts',
                'manage_media',
                'view_analytics',
                'manage_seo'
            ],
            'author' => [
                'view_dashboard',
                'create_posts',
                'edit_own_posts',
                'upload_media'
            ]
        ];

        return in_array($permission, $permissions[$user['role']] ?? []);
    }

    private function validateCredentials($username, $password)
    {
        $user = $this->db->fetch(
            "SELECT id, username, email, password_hash, role, status 
             FROM users 
             WHERE (username = ? OR email = ?) AND status != 'suspended'",
            [$username, $username]
        );

        if ($user && password_verify($password, $user['password_hash'])) {
            return $user;
        }

        return false;
    }

    private function isAccountLocked($username)
    {
        $user = $this->db->fetch(
            "SELECT login_attempts, locked_until 
             FROM users 
             WHERE username = ? OR email = ?",
            [$username, $username]
        );

        if (!$user) {
            return false;
        }

        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            return true;
        }

        return $user['login_attempts'] >= $this->maxAttempts;
    }

    private function recordFailedAttempt($username)
    {
        $this->db->execute(
            "UPDATE users 
             SET login_attempts = login_attempts + 1,
                 locked_until = CASE 
                     WHEN login_attempts + 1 >= ? THEN DATE_ADD(NOW(), INTERVAL ? SECOND)
                     ELSE locked_until 
                 END
             WHERE username = ? OR email = ?",
            [$this->maxAttempts, $this->lockoutDuration, $username, $username]
        );
    }

    private function clearFailedAttempts($username)
    {
        $this->db->execute(
            "UPDATE users 
             SET login_attempts = 0, locked_until = NULL 
             WHERE username = ? OR email = ?",
            [$username, $username]
        );
    }

    private function createSession($user)
    {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_user_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_role'] = $user['role'];
        $_SESSION['admin_login_time'] = time();
    }

    private function updateLastLogin($userId)
    {
        $this->db->execute(
            "UPDATE users SET last_login = NOW() WHERE id = ?",
            [$userId]
        );
    }
}
