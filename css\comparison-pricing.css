/* Comparison Pricing Table Styling */

.comparison-pricing-section {
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.comparison-pricing-section .section-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 2;
}

.comparison-pricing-section .section-header h2 {
    font-family: var(--alt-font);
    font-weight: 700;
    font-size: 42px;
    line-height: 1.2;
    color: var(--dark-gray);
    margin-bottom: 20px;
    letter-spacing: -0.02em;
}

.comparison-pricing-section .section-header h2 span {
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.comparison-pricing-section .section-header p {
    font-family: var(--primary-font);
    font-size: 18px;
    line-height: 1.6;
    color: var(--medium-gray);
    max-width: 700px;
    margin: 0 auto;
}

/* Pricing Table Container */
.pricing-table-container {
    position: relative;
    margin: 40px 0;
}

/* Pricing Table Styles */
.pricing-table-style-comparison {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.pricing-table-item {
    flex: 1;
    min-width: 300px;
    max-width: 380px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    transition: transform 0.4s ease, box-shadow 0.4s ease;
    position: relative;
}

.pricing-table-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12);
}

.pricing-table-item.featured {
    border: 2px solid transparent;
    border-image: linear-gradient(135deg, #e958a1, #8f76f5);
    border-image-slice: 1;
    transform: scale(1.05);
    z-index: 2;
}

.pricing-table-item.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

/* Header */
.pricing-header {
    padding: 30px;
    text-align: center;
    position: relative;
}

.pricing-title {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 8px 20px;
    border-radius: 30px;
    margin-bottom: 20px;
    background: #f8f9fa;
    color: var(--dark-gray);
}

.pricing-table-item.traditional .pricing-title {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.pricing-table-item.adzeta .pricing-title {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.pricing-header h3 {
    font-size: 24px;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 15px;
}

.pricing-header p {
    font-size: 16px;
    color: var(--medium-gray);
    margin-bottom: 0;
}

/* Popular Badge */
.popular-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 5px 15px;
    border-radius: 20px;
    text-transform: uppercase;
}

/* Features List */
.pricing-features {
    padding: 0 30px 30px;
}

.pricing-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pricing-features li {
    padding: 15px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features i {
    margin-right: 10px;
    font-size: 18px;
}

.pricing-features i.positive {
    color: #4CAF50;
}

.pricing-features i.negative {
    color: #F44336;
}

/* Footer */
.pricing-footer {
    padding: 20px 30px 30px;
    text-align: center;
}

.pricing-button {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    transition: all 0.3s ease;
    width: 100%;
}

.pricing-table-item.traditional .pricing-button {
    background: #f8f9fa;
    color: var(--dark-gray);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.pricing-table-item.traditional .pricing-button:hover {
    background: #e9ecef;
}

.pricing-table-item.adzeta .pricing-button {
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
}

.pricing-table-item.adzeta .pricing-button:hover {
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.4);
    transform: translateY(-3px);
}

/* VS Badge */
.vs-badge-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vs-badge {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-weight: 700;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
    animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(233, 88, 161, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
    }
}

/* Results Section */
.results-section {
    margin-top: 80px;
    text-align: center;
}

.results-section h3 {
    font-size: 32px;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: 40px;
}

.stats-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.stat-item {
    flex: 1;
    min-width: 200px;
    max-width: 300px;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-10px);
}

.stat-value {
    font-size: 48px;
    font-weight: 700;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    line-height: 1;
    margin-bottom: 15px;
}

.stat-label {
    font-size: 16px;
    color: var(--medium-gray);
}

.action-button {
    display: inline-flex;
    align-items: center;
    padding: 15px 30px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-weight: 600;
    font-size: 16px;
    border-radius: 30px;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
}

.action-button i {
    margin-left: 10px;
    transition: transform 0.3s ease;
}

.action-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.4);
}

.action-button:hover i {
    transform: translateX(5px);
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.active {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-left.active {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-right.active {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.scale-in.active {
    opacity: 1;
    transform: scale(1);
}

/* Background Elements */
.bg-shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
    z-index: 0;
}

.bg-shape-1 {
    top: -100px;
    right: -100px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(233, 88, 161, 0.3) 0%, rgba(233, 88, 161, 0) 70%);
    animation: float 15s infinite ease-in-out;
}

.bg-shape-2 {
    bottom: -150px;
    left: -100px;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(143, 118, 245, 0.3) 0%, rgba(143, 118, 245, 0) 70%);
    animation: float 18s infinite ease-in-out reverse;
}

@keyframes float {
    0% {
        transform: translate(0, 0);
    }
    50% {
        transform: translate(20px, 20px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .comparison-pricing-section .section-header h2 {
        font-size: 36px;
    }
    
    .pricing-table-style-comparison {
        flex-direction: column;
        align-items: center;
    }
    
    .pricing-table-item {
        width: 100%;
        max-width: 450px;
        margin-bottom: 30px;
    }
    
    .pricing-table-item.featured {
        transform: scale(1);
    }
    
    .pricing-table-item.featured:hover {
        transform: translateY(-10px);
    }
    
    .vs-badge-container {
        position: relative;
        margin: 20px auto;
        transform: none;
        left: auto;
        top: auto;
    }
    
    .stats-container {
        flex-direction: column;
        align-items: center;
    }
    
    .stat-item {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 767px) {
    .comparison-pricing-section .section-header h2 {
        font-size: 30px;
    }
    
    .comparison-pricing-section .section-header p {
        font-size: 16px;
    }
    
    .pricing-header h3 {
        font-size: 22px;
    }
    
    .pricing-features {
        padding: 0 20px 20px;
    }
    
    .pricing-footer {
        padding: 20px;
    }
    
    .stat-value {
        font-size: 36px;
    }
    
    .action-button {
        padding: 12px 25px;
        font-size: 14px;
    }
}
