<?php

namespace AdZetaAdmin\API;

use PDO;

class ErrorLogsController
{
    private $db;

    public function __construct($db)
    {
        $this->db = $db instanceof PDO ? $db : $db->getPdo();
    }

    /**
     * Get error logs (GET /error-logs)
     */
    public function index()
    {
        return $this->getErrorLogs();
    }

    /**
     * Get error statistics (GET /error-logs/stats)
     */
    public function stats()
    {
        return $this->getErrorStats();
    }

    /**
     * Get single error log (GET /error-logs/{id})
     */
    public function show($id)
    {
        return $this->getErrorLog($id);
    }

    /**
     * Create error log (POST /error-logs)
     */
    public function store()
    {
        return $this->createErrorLog();
    }

    /**
     * Resolve error (POST /error-logs/{id}/resolve)
     */
    public function resolve($id)
    {
        return $this->resolveError($id);
    }

    /**
     * Delete error log (DELETE /error-logs/{id})
     */
    public function destroy($id)
    {
        return $this->deleteErrorLog($id);
    }

    /**
     * Clear old logs (DELETE /error-logs/clear)
     */
    public function clearOld()
    {
        return $this->clearOldLogs();
    }

    /**
     * Get error logs with filtering and pagination
     */
    private function getErrorLogs()
    {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 50);
        $severity = $_GET['severity'] ?? null;
        $category = $_GET['category'] ?? null;
        $status = $_GET['status'] ?? null;
        $search = $_GET['search'] ?? null;
        $dateFrom = $_GET['date_from'] ?? null;
        $dateTo = $_GET['date_to'] ?? null;

        $offset = ($page - 1) * $limit;

        // Build WHERE clause
        $where = ['1=1'];
        $params = [];

        if ($severity) {
            $where[] = 'severity = ?';
            $params[] = $severity;
        }

        if ($category) {
            $where[] = 'category = ?';
            $params[] = $category;
        }

        if ($status === 'resolved') {
            $where[] = 'is_resolved = 1';
        } elseif ($status === 'unresolved') {
            $where[] = 'is_resolved = 0';
        }

        if ($search) {
            $where[] = '(error_message LIKE ? OR endpoint LIKE ? OR error_type LIKE ?)';
            $searchTerm = "%{$search}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if ($dateFrom) {
            $where[] = 'timestamp >= ?';
            $params[] = $dateFrom;
        }

        if ($dateTo) {
            $where[] = 'timestamp <= ?';
            $params[] = $dateTo . ' 23:59:59';
        }

        $whereClause = implode(' AND ', $where);

        // Get total count
        $countSQL = "SELECT COUNT(*) FROM api_error_logs WHERE {$whereClause}";
        $stmt = $this->db->prepare($countSQL);
        $stmt->execute($params);
        $total = $stmt->fetchColumn();

        // Get logs
        $sql = "
            SELECT 
                id, request_id, timestamp, method, endpoint, status_code,
                error_type, error_message, severity, category, is_resolved,
                page_url, response_time, user_agent,
                resolved_by, resolved_at, resolution_notes
            FROM api_error_logs 
            WHERE {$whereClause}
            ORDER BY timestamp DESC 
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'logs' => $logs,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ];
    }

    /**
     * Get detailed error log
     */
    private function getErrorLog($id)
    {
        $sql = "SELECT * FROM api_error_logs WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        $log = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$log) {
            http_response_code(404);
            return ['success' => false, 'message' => 'Error log not found'];
        }

        // Parse JSON fields
        $log['request_data'] = $log['request_data'] ? json_decode($log['request_data'], true) : null;
        $log['request_headers'] = $log['request_headers'] ? json_decode($log['request_headers'], true) : null;
        $log['error_context'] = $log['error_context'] ? json_decode($log['error_context'], true) : null;
        $log['browser_info'] = $log['browser_info'] ? json_decode($log['browser_info'], true) : null;

        return [
            'success' => true,
            'log' => $log
        ];
    }

    /**
     * Get error statistics
     */
    private function getErrorStats()
    {
        $stats = [];

        // Error count by severity
        $sql = "SELECT severity, COUNT(*) as count FROM api_error_logs GROUP BY severity";
        $stmt = $this->db->query($sql);
        $stats['by_severity'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        // Error count by category
        $sql = "SELECT category, COUNT(*) as count FROM api_error_logs GROUP BY category ORDER BY count DESC LIMIT 10";
        $stmt = $this->db->query($sql);
        $stats['by_category'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        // Error count by endpoint
        $sql = "SELECT endpoint, COUNT(*) as count FROM api_error_logs GROUP BY endpoint ORDER BY count DESC LIMIT 10";
        $stmt = $this->db->query($sql);
        $stats['by_endpoint'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        // Recent error trends (last 7 days)
        $sql = "
            SELECT 
                DATE(timestamp) as date,
                COUNT(*) as count,
                COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_count
            FROM api_error_logs 
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
        ";
        $stmt = $this->db->query($sql);
        $stats['recent_trends'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Resolution stats
        $sql = "
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN is_resolved = 1 THEN 1 END) as resolved,
                COUNT(CASE WHEN is_resolved = 0 THEN 1 END) as unresolved
            FROM api_error_logs
        ";
        $stmt = $this->db->query($sql);
        $stats['resolution'] = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'stats' => $stats
        ];
    }

    /**
     * Create new error log
     */
    private function createErrorLog()
    {
        $input = json_decode(file_get_contents('php://input'), true);

        $sql = "
            INSERT INTO api_error_logs (
                request_id, method, endpoint, full_url, request_data, request_headers,
                status_code, response_data, response_time, error_type, error_message,
                error_stack, error_context, user_agent, ip_address, page_url,
                severity, category, browser_info, device_info, admin_version
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $input['request_id'] ?? null,
            $input['method'] ?? null,
            $input['endpoint'] ?? null,
            $input['full_url'] ?? null,
            isset($input['request_data']) ? json_encode($input['request_data']) : null,
            isset($input['request_headers']) ? json_encode($input['request_headers']) : null,
            $input['status_code'] ?? null,
            $input['response_data'] ?? null,
            $input['response_time'] ?? null,
            $input['error_type'] ?? null,
            $input['error_message'] ?? null,
            $input['error_stack'] ?? null,
            isset($input['error_context']) ? json_encode($input['error_context']) : null,
            $input['user_agent'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $input['page_url'] ?? null,
            $this->determineSeverity($input),
            $this->determineCategory($input),
            isset($input['browser_info']) ? json_encode($input['browser_info']) : null,
            $input['device_info'] ?? null,
            '2.0.0'
        ]);

        if ($result) {
            return [
                'success' => true,
                'id' => $this->db->lastInsertId(),
                'message' => 'Error log created successfully'
            ];
        } else {
            http_response_code(500);
            return ['success' => false, 'message' => 'Failed to create error log'];
        }
    }

    /**
     * Resolve an error
     */
    private function resolveError($id)
    {
        $input = json_decode(file_get_contents('php://input'), true);

        $sql = "
            UPDATE api_error_logs 
            SET is_resolved = 1, resolved_by = ?, resolved_at = NOW(), resolution_notes = ?
            WHERE id = ?
        ";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $input['resolved_by'] ?? 1, // Default to admin user
            $input['resolution_notes'] ?? '',
            $id
        ]);

        if ($result) {
            return ['success' => true, 'message' => 'Error marked as resolved'];
        } else {
            http_response_code(500);
            return ['success' => false, 'message' => 'Failed to resolve error'];
        }
    }

    /**
     * Delete error log
     */
    private function deleteErrorLog($id)
    {
        $sql = "DELETE FROM api_error_logs WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([$id]);

        if ($result) {
            return ['success' => true, 'message' => 'Error log deleted'];
        } else {
            http_response_code(500);
            return ['success' => false, 'message' => 'Failed to delete error log'];
        }
    }

    /**
     * Clear old logs (older than 30 days)
     */
    private function clearOldLogs()
    {
        $sql = "DELETE FROM api_error_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute();

        $deletedCount = $stmt->rowCount();

        return [
            'success' => true,
            'message' => "Deleted {$deletedCount} old error logs",
            'deleted_count' => $deletedCount
        ];
    }

    /**
     * Determine error severity based on status code and error type
     */
    private function determineSeverity($input)
    {
        $statusCode = $input['status_code'] ?? 0;
        $errorType = strtolower($input['error_type'] ?? '');

        if ($statusCode >= 500 || strpos($errorType, 'critical') !== false) {
            return 'critical';
        } elseif ($statusCode >= 400 || strpos($errorType, 'error') !== false) {
            return 'high';
        } elseif (strpos($errorType, 'warning') !== false) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Determine error category based on endpoint and error type
     */
    private function determineCategory($input)
    {
        $endpoint = $input['endpoint'] ?? '';
        $errorType = strtolower($input['error_type'] ?? '');

        if (strpos($endpoint, '/posts') !== false) {
            return 'posts';
        } elseif (strpos($endpoint, '/media') !== false) {
            return 'media';
        } elseif (strpos($endpoint, '/auth') !== false) {
            return 'authentication';
        } elseif (strpos($errorType, 'validation') !== false) {
            return 'validation';
        } elseif (strpos($errorType, 'database') !== false) {
            return 'database';
        } else {
            return 'general';
        }
    }
}
?>
