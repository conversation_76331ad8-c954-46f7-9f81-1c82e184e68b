/**
 * Case Study Editor Mo<PERSON>le
 * Handles case study creation and editing with AI integration
 */

const AdZetaCaseStudyEditor = {
    state: {
        currentCaseStudy: null,
        templateSections: null,
        isDirty: false,
        autoSaveInterval: null,
        isLoading: false
    },

    // Initialize the case study editor
    init() {
        console.log('Case Study Editor initialized');
        this.bindEvents();
        this.loadTemplateSections();

        // Debug: Check if we're on the case studies page
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('view') === 'case-studies') {
            console.log('On case studies page, action:', urlParams.get('action'));
        }
    },

    // Bind event listeners
    bindEvents() {
        // Auto-save on form changes
        document.addEventListener('input', (e) => {
            if (e.target.closest('#caseStudyEditor')) {
                this.markDirty();
                this.scheduleAutoSave();
            }
        });

        // Prevent accidental navigation when unsaved changes exist
        window.addEventListener('beforeunload', (e) => {
            if (this.state.isDirty) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                return e.returnValue;
            }
        });
    },

    // Create new case study
    createNew() {
        this.state.currentCaseStudy = {
            id: null,
            title: '',
            slug: '',
            client_name: '',
            industry: '',
            hero_title: '',
            hero_subtitle: '',
            hero_description: '',
            hero_image: '',
            hero_badge_text: 'AI-DRIVEN CLIENT ACQUISITION',
            
            // Highlights (6 sections)
            highlight_1_title: 'Client Profile',
            highlight_1_content: '',
            highlight_1_icon: 'bi-heart-pulse',
            highlight_2_title: 'The Core Challenge',
            highlight_2_content: '',
            highlight_2_icon: 'bi-question-circle',
            highlight_3_title: 'Previous Approach',
            highlight_3_content: '',
            highlight_3_icon: 'bi-compass',
            highlight_4_title: 'Our Solution',
            highlight_4_content: '',
            highlight_4_icon: 'bi-cpu',
            highlight_5_title: 'Methodology',
            highlight_5_content: '',
            highlight_5_icon: 'bi-gear-wide-connected',
            highlight_6_title: 'Key Outcomes',
            highlight_6_content: '',
            highlight_6_icon: 'bi-trophy',
            
            // Challenge section
            challenge_title: 'The Challenge',
            challenge_subtitle: 'Understanding the Problem',
            challenge_description: '',
            
            // Results
            results_data: {},
            
            // SEO
            meta_title: '',
            meta_description: '',
            focus_keyword: '',
            
            // Standard fields
            excerpt: '',
            featured_image: '',
            status: 'draft',
            template: 'luminous-skin-clinic',
            author_id: null
        };

        this.showEditor();
        this.renderEditor();
        this.updatePageHeader('New Case Study');
    },

    // Edit existing case study
    async edit(caseStudyId) {
        try {
            this.state.isLoading = true;
            this.showLoadingState();

            const response = await window.AdZetaApp.apiRequest(`/case-studies/${caseStudyId}`);
            
            if (response.success) {
                this.state.currentCaseStudy = response.case_study;
                this.showEditor();
                this.renderEditor();
                this.updatePageHeader('Edit Case Study');
            } else {
                throw new Error(response.message || 'Failed to load case study');
            }
        } catch (error) {
            console.error('Failed to load case study:', error);
            window.AdZetaApp.showNotification('Failed to load case study: ' + error.message, 'danger');
            window.AdZetaNavigation.showView('case-studies');
        } finally {
            this.state.isLoading = false;
        }
    },

    // Show the editor interface
    showEditor() {
        // Use the dedicated add-case-study view container
        const editorView = document.getElementById('add-case-studyView');
        if (!editorView) {
            console.error('add-case-studyView container not found');
            return;
        }

        // Don't call navigation.showView here to avoid infinite loop
        // The navigation system should already have shown the view
        console.log('Editor view ready for rendering');
    },

    // Render the editor interface
    renderEditor() {
        const container = document.getElementById('add-case-studyView');
        if (!container) {
            console.error('add-case-studyView container not found');
            return;
        }


        container.innerHTML = `
            <div class="case-study-editor">
                <!-- Clean Header -->
                <div class="editor-header-clean">
                    <div class="header-content">
                        <div class="header-left">
                            <h1 class="editor-title">${this.state.currentCaseStudy.id ? 'Edit' : 'New'} Case Study</h1>
                            <div class="status-badge ${this.state.isDirty ? 'status-unsaved' : 'status-saved'}">
                                <div class="status-dot"></div>
                                <span class="status-text">${this.state.isDirty ? 'Unsaved changes' : 'All changes saved'}</span>
                            </div>
                        </div>
                        <div class="header-actions">
                            <button class="btn-clean btn-secondary" onclick="AdZetaCaseStudyEditor.previewCaseStudy()">
                                <i class="fas fa-eye"></i>
                                <span>Preview</span>
                            </button>
                            <button class="btn-clean btn-primary" onclick="AdZetaCaseStudyEditor.saveCaseStudy()">
                                <i class="fas fa-save"></i>
                                <span>Save</span>
                            </button>
                            <button class="btn-clean btn-success" onclick="AdZetaCaseStudyEditor.publishCaseStudy()">
                                <i class="fas fa-rocket"></i>
                                <span>Publish</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- AI Assistant Card -->
                <div class="ai-assistant-card">
                    <div class="ai-card-header">
                        <div class="ai-icon">
                            <i class="fas fa-sparkles"></i>
                        </div>
                        <div class="ai-title">
                            <h3>AI Assistant</h3>
                            <p>Generate professional case study content automatically</p>
                        </div>
                    </div>
                    <div class="ai-card-content">
                        <div class="ai-inputs-grid">
                            <div class="input-group-clean">
                                <label>Client Name</label>
                                <input type="text" class="input-clean" id="aiClientName"
                                       placeholder="e.g., Luminous Skin Clinic"
                                       value="${this.state.currentCaseStudy.client_name || ''}">
                            </div>
                            <div class="input-group-clean">
                                <label>Industry</label>
                                <input type="text" class="input-clean" id="aiIndustry"
                                       placeholder="e.g., Healthcare, Beauty"
                                       value="${this.state.currentCaseStudy.industry || ''}">
                            </div>
                            <div class="input-group-clean">
                                <label>Main Challenge</label>
                                <input type="text" class="input-clean" id="aiChallenge"
                                       placeholder="e.g., Low client retention">
                            </div>
                            <div class="input-group-clean">
                                <label>Solution Provided</label>
                                <input type="text" class="input-clean" id="aiSolution"
                                       placeholder="e.g., AI-powered targeting">
                            </div>
                        </div>
                        <div class="ai-actions-clean">
                            <button class="btn-ai-primary" onclick="AdZetaCaseStudyEditor.generateAllContent()">
                                <i class="fas fa-magic"></i>
                                <span>Generate All Content</span>
                            </button>
                            <button class="btn-ai-secondary" onclick="AdZetaCaseStudyEditor.generateTitles()">
                                <i class="fas fa-lightbulb"></i>
                                <span>Generate Titles</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Horizontal Tab Navigation -->
                <div class="editor-tabs-container">
                    <ul class="editor-tabs" id="editorTabs">
                        <li>
                            <a class="nav-link active" data-section="basic" href="#basic">
                                <i class="fas fa-info-circle"></i> Basic
                            </a>
                        </li>
                        <li>
                            <a class="nav-link" data-section="hero" href="#hero">
                                <i class="fas fa-star"></i> Hero
                            </a>
                        </li>
                        <li>
                            <a class="nav-link" data-section="highlights" href="#highlights">
                                <i class="fas fa-list"></i> Highlights
                            </a>
                        </li>
                        <li>
                            <a class="nav-link" data-section="challenge" href="#challenge">
                                <i class="fas fa-exclamation-triangle"></i> Challenge
                            </a>
                        </li>
                        <li>
                            <a class="nav-link" data-section="solution" href="#solution">
                                <i class="fas fa-lightbulb"></i> Solution
                            </a>
                        </li>
                        <li>
                            <a class="nav-link" data-section="results" href="#results">
                                <i class="fas fa-chart-line"></i> Results
                            </a>
                        </li>
                        <li>
                            <a class="nav-link" data-section="testimonial" href="#testimonial">
                                <i class="fas fa-quote-left"></i> Review
                            </a>
                        </li>
                        <li>
                            <a class="nav-link" data-section="cta" href="#cta">
                                <i class="fas fa-bullhorn"></i> CTA
                            </a>
                        </li>
                        <li>
                            <a class="nav-link" data-section="seo" href="#seo">
                                <i class="fas fa-search"></i> SEO
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Editor Content Area -->
                <div class="editor-content">
                    ${this.renderEditorSections()}
                </div>
            </div>
        `;

        // Initialize editor functionality
        setTimeout(() => {
            this.initializeEditorEvents();
        }, 100);
    },

    // Render editor sections
    renderEditorSections() {
        return `
            <!-- Basic Info Section -->
            <div class="editor-section active" id="basic">
                <h4>Basic Information</h4>
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <label class="form-label">Case Study Title *</label>
                        <input type="text" class="form-control" name="title"
                               value="${this.state.currentCaseStudy.title || ''}"
                               placeholder="Client Name Achieves X% Growth Through...">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status">
                            <option value="draft" ${this.state.currentCaseStudy.status === 'draft' ? 'selected' : ''}>Draft</option>
                            <option value="published" ${this.state.currentCaseStudy.status === 'published' ? 'selected' : ''}>Published</option>
                            <option value="archived" ${this.state.currentCaseStudy.status === 'archived' ? 'selected' : ''}>Archived</option>
                        </select>
                    </div>
                    <div class="col-md-8 mb-3">
                        <label class="form-label">URL Slug</label>
                        <div class="input-group">
                            <span class="input-group-text">/case-studies/</span>
                            <input type="text" class="form-control" name="slug"
                                   value="${this.state.currentCaseStudy.slug || ''}"
                                   placeholder="client-name-achieves-growth">
                            <button class="btn btn-outline-secondary" type="button" onclick="AdZetaCaseStudyEditor.generateSlug()">
                                <i class="fas fa-magic"></i>
                            </button>
                        </div>
                        <div class="form-text">URL-friendly version of the title. Leave blank to auto-generate.</div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Featured Image</label>
                        <input type="text" class="form-control" name="featured_image"
                               value="${this.state.currentCaseStudy.featured_image || ''}"
                               placeholder="Image URL or upload">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Client Name *</label>
                        <input type="text" class="form-control" name="client_name" 
                               value="${this.state.currentCaseStudy.client_name || ''}" 
                               placeholder="Company or Client Name">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Industry</label>
                        <input type="text" class="form-control" name="industry" 
                               value="${this.state.currentCaseStudy.industry || ''}" 
                               placeholder="Healthcare, Technology, E-commerce, etc.">
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">Excerpt</label>
                        <textarea class="form-control" name="excerpt" rows="3" 
                                  placeholder="Brief summary of the case study...">${this.state.currentCaseStudy.excerpt || ''}</textarea>
                    </div>
                </div>
            </div>

            <!-- Hero Section -->
            <div class="editor-section" id="hero">
                <h4>Hero Section</h4>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Badge Text</label>
                        <input type="text" class="form-control" name="hero_badge_text" 
                               value="${this.state.currentCaseStudy.hero_badge_text || ''}" 
                               placeholder="AI-DRIVEN CLIENT ACQUISITION">
                        <button class="btn btn-sm btn-outline-primary mt-1" 
                                onclick="AdZetaCaseStudyEditor.generateSection('hero')">
                            <i class="fas fa-magic me-1"></i> AI Generate
                        </button>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Hero Image</label>
                        <input type="text" class="form-control" name="hero_image" 
                               value="${this.state.currentCaseStudy.hero_image || ''}" 
                               placeholder="images/case-studies/client-name.png">
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">Main Title</label>
                        <textarea class="form-control" name="hero_title" rows="2" 
                                  placeholder="Compelling main title for the case study...">${this.state.currentCaseStudy.hero_title || ''}</textarea>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="hero_description" rows="4" 
                                  placeholder="2-3 sentence overview of the case study...">${this.state.currentCaseStudy.hero_description || ''}</textarea>
                    </div>
                </div>
            </div>

            ${this.renderHighlightsSection()}
            ${this.renderChallengeSection()}
            ${this.renderSolutionSection()}
            ${this.renderResultsSection()}
            ${this.renderTestimonialSection()}
            ${this.renderCTASection()}
            ${this.renderSEOSection()}
        `;
    },

    // Render highlights section (6 sections)
    renderHighlightsSection() {
        let html = `
            <div class="editor-section" id="highlights">
                <h4>Project Highlights</h4>
                <p class="text-muted mb-4">Six key sections that tell your case study story</p>
        `;

        const highlightTitles = [
            'Client Profile', 'The Core Challenge', 'Previous Approach',
            'Our Solution', 'Methodology', 'Key Outcomes'
        ];

        for (let i = 1; i <= 6; i++) {
            const title = this.state.currentCaseStudy[`highlight_${i}_title`] || highlightTitles[i-1];
            const content = this.state.currentCaseStudy[`highlight_${i}_content`] || '';
            const icon = this.state.currentCaseStudy[`highlight_${i}_icon`] || 'bi-heart-pulse';

            html += `
                <div class="highlight-section mb-4">
                    <h5>Highlight ${i}</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Title</label>
                            <input type="text" class="form-control" name="highlight_${i}_title" 
                                   value="${title}" placeholder="${highlightTitles[i-1]}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Icon</label>
                            <select class="form-select" name="highlight_${i}_icon">
                                ${this.renderIconOptions(icon)}
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">Content</label>
                            <textarea class="form-control" name="highlight_${i}_content" rows="3" 
                                      placeholder="Detailed content for this section...">${content}</textarea>
                            <button class="btn btn-sm btn-outline-primary mt-1" 
                                    onclick="AdZetaCaseStudyEditor.generateSection('highlight', ${i})">
                                <i class="fas fa-magic me-1"></i> AI Generate
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        html += '</div>';
        return html;
    },

    // Render challenge section
    renderChallengeSection() {
        return `
            <div class="editor-section" id="challenge">
                <h4>Challenge Section</h4>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Challenge Title</label>
                        <input type="text" class="form-control" name="challenge_title"
                               value="${this.state.currentCaseStudy.challenge_title || ''}"
                               placeholder="The Challenge: Main Problem Statement">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Challenge Subtitle</label>
                        <input type="text" class="form-control" name="challenge_subtitle"
                               value="${this.state.currentCaseStudy.challenge_subtitle || ''}"
                               placeholder="Understanding the Problem">
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">Challenge Description</label>
                        <textarea class="form-control" name="challenge_description" rows="5"
                                  placeholder="Detailed explanation of the challenge...">${this.state.currentCaseStudy.challenge_description || ''}</textarea>
                        <button class="btn btn-sm btn-outline-primary mt-1"
                                onclick="AdZetaCaseStudyEditor.generateSection('challenge')">
                            <i class="fas fa-magic me-1"></i> AI Generate
                        </button>
                    </div>
                </div>
            </div>
        `;
    },

    // Render solution section
    renderSolutionSection() {
        return `
            <div class="editor-section" id="solution">
                <h4>Solution Section</h4>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Solution Title</label>
                        <input type="text" class="form-control" name="solution_title"
                               value="${this.state.currentCaseStudy.solution_title || ''}"
                               placeholder="ADZETA'S SOLUTION">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Solution Subtitle</label>
                        <input type="text" class="form-control" name="solution_subtitle"
                               value="${this.state.currentCaseStudy.solution_subtitle || ''}"
                               placeholder="The Approach: Precision LTV Targeting...">
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">Solution Description</label>
                        <textarea class="form-control" name="solution_description" rows="3"
                                  placeholder="Overview of the solution approach...">${this.state.currentCaseStudy.solution_description || ''}</textarea>
                    </div>
                </div>

                <h5 class="mt-4 mb-3">Solution Points</h5>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Point 1 Title</label>
                        <input type="text" class="form-control" name="solution_point_1_title"
                               value="${this.state.currentCaseStudy.solution_point_1_title || ''}"
                               placeholder="Advanced Predictive AI Modeling">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Point 1 Description</label>
                        <textarea class="form-control" name="solution_point_1_description" rows="2"
                                  placeholder="Description of the first solution point...">${this.state.currentCaseStudy.solution_point_1_description || ''}</textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Point 2 Title</label>
                        <input type="text" class="form-control" name="solution_point_2_title"
                               value="${this.state.currentCaseStudy.solution_point_2_title || ''}"
                               placeholder="Dynamic Value-Based Bidding">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Point 2 Description</label>
                        <textarea class="form-control" name="solution_point_2_description" rows="2"
                                  placeholder="Description of the second solution point...">${this.state.currentCaseStudy.solution_point_2_description || ''}</textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Point 3 Title</label>
                        <input type="text" class="form-control" name="solution_point_3_title"
                               value="${this.state.currentCaseStudy.solution_point_3_title || ''}"
                               placeholder="Unified Data Integration">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Point 3 Description</label>
                        <textarea class="form-control" name="solution_point_3_description" rows="2"
                                  placeholder="Description of the third solution point...">${this.state.currentCaseStudy.solution_point_3_description || ''}</textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Point 4 Title</label>
                        <input type="text" class="form-control" name="solution_point_4_title"
                               value="${this.state.currentCaseStudy.solution_point_4_title || ''}"
                               placeholder="Continuous Optimization">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Point 4 Description</label>
                        <textarea class="form-control" name="solution_point_4_description" rows="2"
                                  placeholder="Description of the fourth solution point...">${this.state.currentCaseStudy.solution_point_4_description || ''}</textarea>
                    </div>
                </div>
                <button class="btn btn-sm btn-outline-primary"
                        onclick="AdZetaCaseStudyEditor.generateSection('solution')">
                    <i class="fas fa-magic me-1"></i> AI Generate Solution
                </button>
            </div>
        `;
    },

    // Render results section
    renderResultsSection() {
        const results = this.state.currentCaseStudy.results_data || {};
        return `
            <div class="editor-section" id="results">
                <h4>Results & Metrics</h4>
                <p class="text-muted mb-4">Four key metrics that showcase the case study results</p>

                <!-- Metric 1: Ad Spend Increase -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">Metric 1 Value</label>
                        <input type="text" class="form-control" name="results_metric_1_value"
                               value="${results.metric_1_value || ''}"
                               placeholder="64%">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Metric 1 Title</label>
                        <input type="text" class="form-control" name="results_metric_1_title"
                               value="${results.metric_1_title || ''}"
                               placeholder="Increase in Ad Spend">
                    </div>
                    <div class="col-md-5 mb-3">
                        <label class="form-label">Metric 1 Description</label>
                        <input type="text" class="form-control" name="results_metric_1_description"
                               value="${results.metric_1_description || ''}"
                               placeholder="While maintaining and improving profitability">
                    </div>
                </div>

                <!-- Metric 2: Client Retention -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">Metric 2 Value</label>
                        <input type="text" class="form-control" name="results_metric_2_value"
                               value="${results.metric_2_value || ''}"
                               placeholder="56%">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Metric 2 Title</label>
                        <input type="text" class="form-control" name="results_metric_2_title"
                               value="${results.metric_2_title || ''}"
                               placeholder="Boost in Client Retention">
                    </div>
                    <div class="col-md-5 mb-3">
                        <label class="form-label">Metric 2 Description</label>
                        <input type="text" class="form-control" name="results_metric_2_description"
                               value="${results.metric_2_description || ''}"
                               placeholder="Across Premium Treatment Packages">
                    </div>
                </div>

                <!-- Metric 3: LTV Increase -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">Metric 3 Value</label>
                        <input type="text" class="form-control" name="results_metric_3_value"
                               value="${results.metric_3_value || ''}"
                               placeholder="47%">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Metric 3 Title</label>
                        <input type="text" class="form-control" name="results_metric_3_title"
                               value="${results.metric_3_title || ''}"
                               placeholder="Increase in LTV">
                    </div>
                    <div class="col-md-5 mb-3">
                        <label class="form-label">Metric 3 Description</label>
                        <input type="text" class="form-control" name="results_metric_3_description"
                               value="${results.metric_3_description || ''}"
                               placeholder="Per Acquired Client">
                    </div>
                </div>

                <!-- Metric 4: ROI -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">Metric 4 Value</label>
                        <input type="text" class="form-control" name="results_metric_4_value"
                               value="${results.metric_4_value || ''}"
                               placeholder="3.2X">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Metric 4 Title</label>
                        <input type="text" class="form-control" name="results_metric_4_title"
                               value="${results.metric_4_title || ''}"
                               placeholder="Return on Investment">
                    </div>
                    <div class="col-md-5 mb-3">
                        <label class="form-label">Metric 4 Description</label>
                        <input type="text" class="form-control" name="results_metric_4_description"
                               value="${results.metric_4_description || ''}"
                               placeholder="ROI on Advertising Spend">
                    </div>
                </div>

                <!-- Results Section Content -->
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Results Section Title</label>
                        <input type="text" class="form-control" name="results_title"
                               value="${this.state.currentCaseStudy.results_title || ''}"
                               placeholder="MEASURABLE IMPACT">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Results Section Subtitle</label>
                        <input type="text" class="form-control" name="results_subtitle"
                               value="${this.state.currentCaseStudy.results_subtitle || ''}"
                               placeholder="Superior Client Quality, Reduced Waste & Maximized ROI">
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">Results Description</label>
                        <textarea class="form-control" name="results_description" rows="3"
                                  placeholder="Description of the overall impact and results...">${this.state.currentCaseStudy.results_description || ''}</textarea>
                    </div>
                </div>

                <button class="btn btn-sm btn-outline-primary"
                        onclick="AdZetaCaseStudyEditor.generateSection('results')">
                    <i class="fas fa-magic me-1"></i> AI Generate Metrics
                </button>
            </div>
        `;
    },

    // Render testimonial section
    renderTestimonialSection() {
        return `
            <div class="editor-section" id="testimonial">
                <h4>Client Testimonial</h4>
                <div class="row">
                    <div class="col-12 mb-3">
                        <label class="form-label">Testimonial Quote</label>
                        <textarea class="form-control" name="testimonial_quote" rows="4"
                                  placeholder="Client's testimonial about the results...">${this.state.currentCaseStudy.testimonial_quote || ''}</textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Author Name</label>
                        <input type="text" class="form-control" name="testimonial_author_name"
                               value="${this.state.currentCaseStudy.testimonial_author_name || ''}"
                               placeholder="Dr. Sophia Williams">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Author Title</label>
                        <input type="text" class="form-control" name="testimonial_author_title"
                               value="${this.state.currentCaseStudy.testimonial_author_title || ''}"
                               placeholder="Founder & Lead Esthetician">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Company Name</label>
                        <input type="text" class="form-control" name="testimonial_author_company"
                               value="${this.state.currentCaseStudy.testimonial_author_company || ''}"
                               placeholder="Luminous Skin Clinic">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Author Image</label>
                        <input type="text" class="form-control" name="testimonial_author_image"
                               value="${this.state.currentCaseStudy.testimonial_author_image || ''}"
                               placeholder="images/testimonials/author.jpg">
                    </div>
                </div>
                <button class="btn btn-sm btn-outline-primary"
                        onclick="AdZetaCaseStudyEditor.generateSection('testimonial')">
                    <i class="fas fa-magic me-1"></i> AI Generate Testimonial
                </button>
            </div>
        `;
    },

    // Render CTA section
    renderCTASection() {
        return `
            <div class="editor-section" id="cta">
                <h4>Call to Action</h4>
                <div class="row">
                    <div class="col-12 mb-3">
                        <label class="form-label">CTA Title</label>
                        <input type="text" class="form-control" name="cta_title"
                               value="${this.state.currentCaseStudy.cta_title || ''}"
                               placeholder="Ready to Build Similar Growth for Your Business?">
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">CTA Description</label>
                        <textarea class="form-control" name="cta_description" rows="3"
                                  placeholder="Compelling description that encourages action...">${this.state.currentCaseStudy.cta_description || ''}</textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Button Text</label>
                        <input type="text" class="form-control" name="cta_button_text"
                               value="${this.state.currentCaseStudy.cta_button_text || ''}"
                               placeholder="Let's Talk">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Button URL</label>
                        <input type="text" class="form-control" name="cta_button_url"
                               value="${this.state.currentCaseStudy.cta_button_url || ''}"
                               placeholder="free-ad-audit.php">
                    </div>
                </div>
                <button class="btn btn-sm btn-outline-primary"
                        onclick="AdZetaCaseStudyEditor.generateSection('cta')">
                    <i class="fas fa-magic me-1"></i> AI Generate CTA
                </button>
            </div>
        `;
    },

    // Render SEO section
    renderSEOSection() {
        return `
            <div class="editor-section" id="seo">
                <h4>SEO & Meta Data</h4>
                <div class="row">
                    <div class="col-12 mb-3">
                        <label class="form-label">Meta Title</label>
                        <input type="text" class="form-control" name="meta_title"
                               value="${this.state.currentCaseStudy.meta_title || ''}"
                               placeholder="Case Study Title | Company Name">
                        <small class="form-text text-muted">Recommended: 50-60 characters</small>
                    </div>
                    <div class="col-12 mb-3">
                        <label class="form-label">Meta Description</label>
                        <textarea class="form-control" name="meta_description" rows="3"
                                  placeholder="Brief description for search engines...">${this.state.currentCaseStudy.meta_description || ''}</textarea>
                        <small class="form-text text-muted">Recommended: 150-160 characters</small>
                        <button class="btn btn-sm btn-outline-primary mt-1"
                                onclick="AdZetaCaseStudyEditor.generateMetaDescription()">
                            <i class="fas fa-magic me-1"></i> AI Generate
                        </button>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Focus Keyword</label>
                        <input type="text" class="form-control" name="focus_keyword"
                               value="${this.state.currentCaseStudy.focus_keyword || ''}"
                               placeholder="main keyword phrase">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Canonical URL</label>
                        <input type="text" class="form-control" name="canonical_url"
                               value="${this.state.currentCaseStudy.canonical_url || ''}"
                               placeholder="https://example.com/case-studies/slug">
                    </div>
                </div>
            </div>
        `;
    },

    // Render icon options for highlights
    renderIconOptions(selectedIcon) {
        const icons = {
            'bi-heart-pulse': 'Heart Pulse',
            'bi-question-circle': 'Question Circle',
            'bi-compass': 'Compass',
            'bi-cpu': 'CPU',
            'bi-gear-wide-connected': 'Gear Connected',
            'bi-trophy': 'Trophy',
            'bi-chart-line': 'Chart Line',
            'bi-target': 'Target',
            'bi-lightbulb': 'Lightbulb',
            'bi-rocket': 'Rocket'
        };

        let options = '';
        for (const [value, label] of Object.entries(icons)) {
            const selected = value === selectedIcon ? 'selected' : '';
            options += `<option value="${value}" ${selected}>${label}</option>`;
        }
        return options;
    },

    // Initialize editor events
    initializeEditorEvents() {
        // Section navigation
        const tabLinks = document.querySelectorAll('#editorTabs .nav-link');

        tabLinks.forEach((link, index) => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.target.closest('.nav-link');
                const sectionName = target.dataset.section;
                if (sectionName) {
                    this.switchSection(sectionName);
                }
            });
        });

        // Form field updates
        document.querySelectorAll('#add-case-studyView input, #add-case-studyView textarea, #add-case-studyView select').forEach(field => {
            field.addEventListener('input', (e) => {
                this.updateCaseStudyData(e.target.name, e.target.value);

                // Auto-generate slug when title changes
                if (e.target.name === 'title') {
                    this.autoGenerateSlug(e.target.value);
                }
            });
        });
    },

    // Switch between editor sections
    switchSection(sectionName) {

        // Update navigation
        document.querySelectorAll('#editorTabs .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const activeTab = document.querySelector(`#editorTabs [data-section="${sectionName}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }

        // Update content
        document.querySelectorAll('.editor-section').forEach(section => {
            section.classList.remove('active');
        });

        const activeSection = document.getElementById(sectionName);
        if (activeSection) {
            activeSection.classList.add('active');
        } else {
            console.error('Section not found:', sectionName);
        }
    },

    // Generate slug from title
    generateSlug() {
        const titleField = document.querySelector('input[name="title"]');
        if (titleField && titleField.value) {
            const slug = this.createSlugFromText(titleField.value);
            const slugField = document.querySelector('input[name="slug"]');
            if (slugField) {
                slugField.value = slug;
                this.updateCaseStudyData('slug', slug);
            }
        }
    },

    // Auto-generate slug when title changes (only if slug is empty)
    autoGenerateSlug(title) {
        const slugField = document.querySelector('input[name="slug"]');
        if (slugField && !slugField.value && title) {
            const slug = this.createSlugFromText(title);
            slugField.value = slug;
            this.updateCaseStudyData('slug', slug);
        }
    },

    // Create URL-friendly slug from text
    createSlugFromText(text) {
        return text
            .toLowerCase()
            .trim()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
            .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
    },

    // Update case study data
    updateCaseStudyData(fieldName, value) {
        if (fieldName.startsWith('results_')) {
            // Handle results data specially
            if (!this.state.currentCaseStudy.results_data) {
                this.state.currentCaseStudy.results_data = {};
            }
            const metricName = fieldName.replace('results_', '') + '_metric';
            this.state.currentCaseStudy.results_data[metricName] = value;
        } else {
            this.state.currentCaseStudy[fieldName] = value;
        }
        this.markDirty();
    },

    // Mark editor as having unsaved changes
    markDirty() {
        this.state.isDirty = true;
        this.updateStatusIndicator();
    },

    // Update status indicator
    updateStatusIndicator() {
        const indicator = document.querySelector('.status-indicator');
        if (indicator) {
            if (this.state.isDirty) {
                indicator.className = 'status-indicator unsaved';
                indicator.textContent = 'Unsaved changes';
            } else {
                indicator.className = 'status-indicator saved';
                indicator.textContent = 'All changes saved';
            }
        }
    },

    // Schedule auto-save
    scheduleAutoSave() {
        if (this.state.autoSaveInterval) {
            clearTimeout(this.state.autoSaveInterval);
        }

        this.state.autoSaveInterval = setTimeout(() => {
            this.autoSave();
        }, 5000); // Auto-save after 5 seconds of inactivity
    },

    // Auto-save case study
    async autoSave() {
        if (!this.state.isDirty) return;

        try {
            const response = await window.AdZetaApp.apiRequest('/case-studies/autosave', {
                method: 'POST',
                body: JSON.stringify(this.state.currentCaseStudy)
            });

            if (response.success) {
                this.state.currentCaseStudy = response.case_study;
                this.state.isDirty = false;
                this.updateStatusIndicator();
                console.log('Auto-saved case study');
            }
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    },

    // Generate all content using AI
    async generateAllContent() {
        const clientName = document.getElementById('aiClientName').value;
        const industry = document.getElementById('aiIndustry').value;
        const challenge = document.getElementById('aiChallenge').value;
        const solution = document.getElementById('aiSolution').value;

        if (!clientName || !industry || !challenge || !solution) {
            window.AdZetaApp.showNotification('Please fill in all AI context fields', 'warning');
            return;
        }

        try {
            this.showAILoading('Generating complete case study content...');

            const response = await window.AdZetaApp.apiRequest('/ai/generate-case-study', {
                method: 'POST',
                body: JSON.stringify({
                    client_name: clientName,
                    industry: industry,
                    challenge: challenge,
                    solution: solution,
                    results: this.state.currentCaseStudy.results_data || {}
                })
            });

            if (response.success) {
                let caseStudyData = null;

                // Check if we have direct case_study_data
                if (response.case_study_data) {
                    caseStudyData = response.case_study_data;
                }
                // Check if we have raw_content that needs parsing
                else if (response.raw_content) {
                    console.log('Parsing raw content from AI response...');
                    caseStudyData = this.parseAIRawContent(response.raw_content);
                }

                if (caseStudyData) {
                    this.populateFromAI(caseStudyData);
                    window.AdZetaApp.showNotification('Content generated successfully!', 'success');
                } else {
                    throw new Error('No valid case study data found in response');
                }
            } else {
                throw new Error(response.message || 'Failed to generate content');
            }
        } catch (error) {
            console.error('AI generation failed:', error);
            window.AdZetaApp.showNotification('AI generation failed: ' + error.message, 'danger');
        } finally {
            this.hideAILoading();
        }
    },

    // Parse AI raw content that may be wrapped in markdown code blocks
    parseAIRawContent(rawContent) {
        try {
            // Remove markdown code block wrapper if present
            let cleanContent = rawContent.trim();

            // Check if content is wrapped in ```json ... ```
            if (cleanContent.startsWith('```json') && cleanContent.endsWith('```')) {
                cleanContent = cleanContent.slice(7, -3).trim(); // Remove ```json and ```
            } else if (cleanContent.startsWith('```') && cleanContent.endsWith('```')) {
                cleanContent = cleanContent.slice(3, -3).trim(); // Remove ``` and ```
            }

            // Parse the JSON
            const parsedData = JSON.parse(cleanContent);
            console.log('Successfully parsed AI content:', parsedData);
            return parsedData;
        } catch (error) {
            console.error('Failed to parse AI raw content:', error);
            console.log('Raw content was:', rawContent);
            return null;
        }
    },

    // Generate specific section content
    async generateSection(sectionType, highlightIndex = null) {
        const context = this.buildAIContext();

        try {
            this.showAILoading(`Generating ${sectionType} content...`);

            const response = await window.AdZetaApp.apiRequest('/ai/generate-case-study-section', {
                method: 'POST',
                body: JSON.stringify({
                    section_type: sectionType,
                    context: context
                })
            });

            if (response.success && response.section_data) {
                this.populateSectionFromAI(sectionType, response.section_data, highlightIndex);
                window.AdZetaApp.showNotification('Section generated successfully!', 'success');
            } else {
                throw new Error(response.message || 'Failed to generate section');
            }
        } catch (error) {
            console.error('Section generation failed:', error);
            window.AdZetaApp.showNotification('Section generation failed: ' + error.message, 'danger');
        } finally {
            this.hideAILoading();
        }
    },

    // Generate title suggestions
    async generateTitles() {
        const clientName = document.getElementById('aiClientName').value;
        const industry = document.getElementById('aiIndustry').value;
        const results = JSON.stringify(this.state.currentCaseStudy.results_data || {});

        if (!clientName || !industry) {
            window.AdZetaApp.showNotification('Please fill in client name and industry', 'warning');
            return;
        }

        try {
            this.showAILoading('Generating title suggestions...');

            const response = await window.AdZetaApp.apiRequest('/ai/generate-case-study-titles', {
                method: 'POST',
                body: JSON.stringify({
                    client_name: clientName,
                    industry: industry,
                    results: results,
                    count: 5
                })
            });

            if (response.success) {
                this.showTitleSuggestions(response.titles);
            } else {
                throw new Error(response.message || 'Failed to generate titles');
            }
        } catch (error) {
            console.error('Title generation failed:', error);
            window.AdZetaApp.showNotification('Title generation failed: ' + error.message, 'danger');
        } finally {
            this.hideAILoading();
        }
    },

    // Generate meta description
    async generateMetaDescription() {
        const title = this.state.currentCaseStudy.title;
        const clientName = this.state.currentCaseStudy.client_name;
        const results = JSON.stringify(this.state.currentCaseStudy.results_data || {});

        if (!title || !clientName) {
            window.AdZetaApp.showNotification('Please fill in title and client name first', 'warning');
            return;
        }

        try {
            this.showAILoading('Generating meta description...');

            const response = await window.AdZetaApp.apiRequest('/ai/generate-case-study-meta', {
                method: 'POST',
                body: JSON.stringify({
                    title: title,
                    client_name: clientName,
                    results: results
                })
            });

            if (response.success) {
                document.querySelector('textarea[name="meta_description"]').value = response.meta_description;
                this.updateCaseStudyData('meta_description', response.meta_description);
                window.AdZetaApp.showNotification('Meta description generated!', 'success');
            } else {
                throw new Error(response.message || 'Failed to generate meta description');
            }
        } catch (error) {
            console.error('Meta description generation failed:', error);
            window.AdZetaApp.showNotification('Meta generation failed: ' + error.message, 'danger');
        } finally {
            this.hideAILoading();
        }
    },

    // Build AI context from current case study data
    buildAIContext() {
        const cs = this.state.currentCaseStudy;
        return `Case Study: ${cs.title || 'Untitled'}
Client: ${cs.client_name || 'Unknown'}
Industry: ${cs.industry || 'Unknown'}
Challenge: ${cs.challenge_description || 'Not specified'}
Results: ${JSON.stringify(cs.results_data || {})}`;
    },

    // Populate form from AI generated data
    populateFromAI(aiData) {
        console.log('Populating form with AI data:', aiData);
        if (aiData.hero_title) {
            document.querySelector('textarea[name="hero_title"]').value = aiData.hero_title;
            this.updateCaseStudyData('hero_title', aiData.hero_title);
        }

        if (aiData.hero_description) {
            document.querySelector('textarea[name="hero_description"]').value = aiData.hero_description;
            this.updateCaseStudyData('hero_description', aiData.hero_description);
        }

        if (aiData.highlights && Array.isArray(aiData.highlights)) {
            aiData.highlights.forEach((highlight, index) => {
                const i = index + 1;
                if (i <= 6) {
                    if (highlight.title) {
                        document.querySelector(`input[name="highlight_${i}_title"]`).value = highlight.title;
                        this.updateCaseStudyData(`highlight_${i}_title`, highlight.title);
                    }
                    if (highlight.content) {
                        document.querySelector(`textarea[name="highlight_${i}_content"]`).value = highlight.content;
                        this.updateCaseStudyData(`highlight_${i}_content`, highlight.content);
                    }
                }
            });
        }

        if (aiData.challenge_title) {
            document.querySelector('input[name="challenge_title"]').value = aiData.challenge_title;
            this.updateCaseStudyData('challenge_title', aiData.challenge_title);
        }

        if (aiData.challenge_description) {
            document.querySelector('textarea[name="challenge_description"]').value = aiData.challenge_description;
            this.updateCaseStudyData('challenge_description', aiData.challenge_description);
        }

        if (aiData.results_metrics) {
            Object.entries(aiData.results_metrics).forEach(([key, value]) => {
                const fieldName = key.replace('_metric', '');
                const field = document.querySelector(`input[name="results_${fieldName}"]`);
                if (field) {
                    field.value = value;
                    this.updateCaseStudyData(`results_${fieldName}`, value);
                }
            });
        }

        // Handle Solution section
        if (aiData.solution_title) {
            const field = document.querySelector('input[name="solution_title"]');
            if (field) {
                field.value = aiData.solution_title;
                this.updateCaseStudyData('solution_title', aiData.solution_title);
            }
        }

        if (aiData.solution_subtitle) {
            const field = document.querySelector('input[name="solution_subtitle"]');
            if (field) {
                field.value = aiData.solution_subtitle;
                this.updateCaseStudyData('solution_subtitle', aiData.solution_subtitle);
            }
        }

        if (aiData.solution_description) {
            const field = document.querySelector('textarea[name="solution_description"]');
            if (field) {
                field.value = aiData.solution_description;
                this.updateCaseStudyData('solution_description', aiData.solution_description);
            }
        }

        // Handle Solution Points
        if (aiData.solution_points && Array.isArray(aiData.solution_points)) {
            aiData.solution_points.forEach((point, index) => {
                const pointIndex = index + 1;
                if (pointIndex <= 4) {
                    if (point.title) {
                        const titleField = document.querySelector(`input[name="solution_point_${pointIndex}_title"]`);
                        if (titleField) {
                            titleField.value = point.title;
                            this.updateCaseStudyData(`solution_point_${pointIndex}_title`, point.title);
                        }
                    }
                    if (point.description) {
                        const descField = document.querySelector(`textarea[name="solution_point_${pointIndex}_description"]`);
                        if (descField) {
                            descField.value = point.description;
                            this.updateCaseStudyData(`solution_point_${pointIndex}_description`, point.description);
                        }
                    }
                }
            });
        }

        // Handle Results section with 4 metrics
        if (aiData.results_data) {
            const results = aiData.results_data;

            // Handle the 4 metrics
            for (let i = 1; i <= 4; i++) {
                const metricKey = `metric_${i}`;
                if (results[metricKey]) {
                    const metric = results[metricKey];

                    if (metric.value) {
                        const valueField = document.querySelector(`input[name="results_metric_${i}_value"]`);
                        if (valueField) {
                            valueField.value = metric.value;
                            this.updateResultsData(`metric_${i}_value`, metric.value);
                        }
                    }
                    if (metric.title) {
                        const titleField = document.querySelector(`input[name="results_metric_${i}_title"]`);
                        if (titleField) {
                            titleField.value = metric.title;
                            this.updateResultsData(`metric_${i}_title`, metric.title);
                        }
                    }
                    if (metric.description) {
                        const descField = document.querySelector(`input[name="results_metric_${i}_description"]`);
                        if (descField) {
                            descField.value = metric.description;
                            this.updateResultsData(`metric_${i}_description`, metric.description);
                        }
                    }
                }
            }

            // Handle results section content
            if (results.section) {
                if (results.section.title) {
                    const field = document.querySelector('input[name="results_title"]');
                    if (field) {
                        field.value = results.section.title;
                        this.updateCaseStudyData('results_title', results.section.title);
                    }
                }
                if (results.section.subtitle) {
                    const field = document.querySelector('input[name="results_subtitle"]');
                    if (field) {
                        field.value = results.section.subtitle;
                        this.updateCaseStudyData('results_subtitle', results.section.subtitle);
                    }
                }
                if (results.section.description) {
                    const field = document.querySelector('textarea[name="results_description"]');
                    if (field) {
                        field.value = results.section.description;
                        this.updateCaseStudyData('results_description', results.section.description);
                    }
                }
            }
        }

        // Handle Testimonial section
        if (aiData.testimonial) {
            const testimonial = aiData.testimonial;

            if (testimonial.quote) {
                const field = document.querySelector('textarea[name="testimonial_quote"]');
                if (field) {
                    field.value = testimonial.quote;
                    this.updateCaseStudyData('testimonial_quote', testimonial.quote);
                }
            }
            if (testimonial.author_name) {
                const field = document.querySelector('input[name="testimonial_author_name"]');
                if (field) {
                    field.value = testimonial.author_name;
                    this.updateCaseStudyData('testimonial_author_name', testimonial.author_name);
                }
            }
            if (testimonial.author_title) {
                const field = document.querySelector('input[name="testimonial_author_title"]');
                if (field) {
                    field.value = testimonial.author_title;
                    this.updateCaseStudyData('testimonial_author_title', testimonial.author_title);
                }
            }
            if (testimonial.company) {
                const field = document.querySelector('input[name="testimonial_author_company"]');
                if (field) {
                    field.value = testimonial.company;
                    this.updateCaseStudyData('testimonial_author_company', testimonial.company);
                }
            }
        }

        // Handle CTA section
        if (aiData.cta) {
            const cta = aiData.cta;

            if (cta.title) {
                const field = document.querySelector('input[name="cta_title"]');
                if (field) {
                    field.value = cta.title;
                    this.updateCaseStudyData('cta_title', cta.title);
                }
            }
            if (cta.description) {
                const field = document.querySelector('textarea[name="cta_description"]');
                if (field) {
                    field.value = cta.description;
                    this.updateCaseStudyData('cta_description', cta.description);
                }
            }
            if (cta.button_text) {
                const field = document.querySelector('input[name="cta_button_text"]');
                if (field) {
                    field.value = cta.button_text;
                    this.updateCaseStudyData('cta_button_text', cta.button_text);
                }
            }
            if (cta.button_url) {
                const field = document.querySelector('input[name="cta_button_url"]');
                if (field) {
                    field.value = cta.button_url;
                    this.updateCaseStudyData('cta_button_url', cta.button_url);
                }
            }
        }

        // Handle SEO section
        if (aiData.seo) {
            const seo = aiData.seo;

            if (seo.meta_title) {
                const field = document.querySelector('input[name="meta_title"]');
                if (field) {
                    field.value = seo.meta_title;
                    this.updateCaseStudyData('meta_title', seo.meta_title);
                }
            }
            if (seo.meta_description) {
                const field = document.querySelector('textarea[name="meta_description"]');
                if (field) {
                    field.value = seo.meta_description;
                    this.updateCaseStudyData('meta_description', seo.meta_description);
                }
            }
            if (seo.focus_keyword) {
                const field = document.querySelector('input[name="focus_keyword"]');
                if (field) {
                    field.value = seo.focus_keyword;
                    this.updateCaseStudyData('focus_keyword', seo.focus_keyword);
                }
            }
        }

        console.log('All sections populated from AI data');
    },

    // Collect all form data into state
    collectAllFormData() {
        // Get all form fields in the case study editor
        const formFields = document.querySelectorAll('#add-case-studyView input, #add-case-studyView textarea, #add-case-studyView select');

        formFields.forEach(field => {
            if (field.name && field.value !== undefined) {
                this.updateCaseStudyData(field.name, field.value);
            }
        });

        // Also collect data from AI assistant fields if main fields are empty
        const aiClientName = document.getElementById('aiClientName');
        const aiIndustry = document.getElementById('aiIndustry');

        if (aiClientName && aiClientName.value && !this.state.currentCaseStudy.client_name) {
            this.state.currentCaseStudy.client_name = aiClientName.value;
        }

        if (aiIndustry && aiIndustry.value && !this.state.currentCaseStudy.industry) {
            this.state.currentCaseStudy.industry = aiIndustry.value;
        }

        // Validate required fields
        const missingFields = [];
        if (!this.state.currentCaseStudy.title || this.state.currentCaseStudy.title.trim() === '') {
            missingFields.push('Title');
        }
        if (!this.state.currentCaseStudy.client_name || this.state.currentCaseStudy.client_name.trim() === '') {
            missingFields.push('Client Name');
        }

        if (missingFields.length > 0) {
            throw new Error(`Please fill in the required fields: ${missingFields.join(', ')}`);
        }

        // Ensure other fields have default values
        if (!this.state.currentCaseStudy.industry) {
            this.state.currentCaseStudy.industry = '';
        }
        if (!this.state.currentCaseStudy.status) {
            this.state.currentCaseStudy.status = 'draft';
        }
    },

    // Save case study
    async saveCaseStudy() {
        try {
            this.showSaveLoading();

            // Collect all form data before saving
            this.collectAllFormData();

            const method = this.state.currentCaseStudy.id ? 'PUT' : 'POST';
            const url = this.state.currentCaseStudy.id
                ? `/case-studies/${this.state.currentCaseStudy.id}`
                : '/case-studies';

            console.log('Saving case study data:', this.state.currentCaseStudy);

            const response = await window.AdZetaApp.apiRequest(url, {
                method: method,
                body: JSON.stringify(this.state.currentCaseStudy)
            });

            if (response.success) {
                this.state.currentCaseStudy = response.case_study;
                this.state.isDirty = false;
                this.updateStatusIndicator();
                window.AdZetaApp.showNotification('Case study saved successfully!', 'success');
            } else {
                throw new Error(response.message || 'Failed to save case study');
            }
        } catch (error) {
            console.error('Save failed:', error);
            window.AdZetaApp.showNotification('Save failed: ' + error.message, 'danger');
        } finally {
            this.hideSaveLoading();
        }
    },

    // Publish case study
    async publishCaseStudy() {
        try {
            // First save the case study to ensure all data is persisted
            await this.saveCaseStudy();

            // Then publish it using the dedicated publish endpoint
            if (this.state.currentCaseStudy.id) {
                const response = await window.AdZetaApp.apiRequest(`/case-studies/${this.state.currentCaseStudy.id}/publish`, {
                    method: 'POST'
                });

                if (response.success) {
                    this.state.currentCaseStudy.status = 'published';
                    this.state.currentCaseStudy.published_at = new Date().toISOString();
                    this.updatePageHeader();
                    window.AdZetaApp.showNotification('Case study published successfully!', 'success');
                } else {
                    throw new Error(response.message || 'Failed to publish case study');
                }
            } else {
                throw new Error('Please save the case study first');
            }
        } catch (error) {
            console.error('Publish failed:', error);
            window.AdZetaApp.showNotification('Publish failed: ' + error.message, 'danger');
        }
    },

    // Preview case study
    previewCaseStudy() {
        if (this.state.currentCaseStudy.slug) {
            const previewUrl = `/case-studies/${this.state.currentCaseStudy.slug}`;
            window.open(previewUrl, '_blank');
        } else {
            window.AdZetaApp.showNotification('Please save the case study first to preview', 'warning');
        }
    },

    // Show loading states
    showAILoading(message) {
        // Implementation for AI loading indicator
        console.log('AI Loading:', message);
    },

    hideAILoading() {
        // Implementation to hide AI loading
        console.log('AI Loading complete');
    },

    showSaveLoading() {
        const saveBtn = document.querySelector('button[onclick="AdZetaCaseStudyEditor.saveCaseStudy()"]');
        if (saveBtn) {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Saving...';
        }
    },

    hideSaveLoading() {
        const saveBtn = document.querySelector('button[onclick="AdZetaCaseStudyEditor.saveCaseStudy()"]');
        if (saveBtn) {
            saveBtn.disabled = false;
            saveBtn.innerHTML = '<i class="fas fa-save me-1"></i> Save';
        }
    },

    // Update page header
    updatePageHeader(title) {
        const header = document.querySelector('.page-header h1');
        if (header) {
            header.textContent = title;
        }
    },

    // Show loading state
    showLoadingState() {
        const container = document.getElementById('add-case-studyView');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Loading case study...</p>
                </div>
            `;
        }
    },

    // Load template sections (placeholder for future expansion)
    loadTemplateSections() {
        // This could load template configuration from the server
        console.log('Template sections loaded');
    },

    // Populate specific section from AI data
    populateSectionFromAI(sectionType, aiData, highlightIndex = null) {
        console.log(`Populating ${sectionType} section with AI data:`, aiData);

        switch (sectionType) {
            case 'hero':
                if (aiData.title) {
                    const titleField = document.querySelector('textarea[name="hero_title"]');
                    if (titleField) {
                        titleField.value = aiData.title;
                        this.updateCaseStudyData('hero_title', aiData.title);
                    }
                }
                if (aiData.subtitle) {
                    const subtitleField = document.querySelector('input[name="hero_badge_text"]');
                    if (subtitleField) {
                        subtitleField.value = aiData.subtitle;
                        this.updateCaseStudyData('hero_badge_text', aiData.subtitle);
                    }
                }
                if (aiData.description) {
                    const descField = document.querySelector('textarea[name="hero_description"]');
                    if (descField) {
                        descField.value = aiData.description;
                        this.updateCaseStudyData('hero_description', aiData.description);
                    }
                }
                break;

            case 'solution':
                if (aiData.title) {
                    const titleField = document.querySelector('input[name="solution_title"]');
                    if (titleField) {
                        titleField.value = aiData.title;
                        this.updateCaseStudyData('solution_title', aiData.title);
                    }
                }
                if (aiData.subtitle) {
                    const subtitleField = document.querySelector('input[name="solution_subtitle"]');
                    if (subtitleField) {
                        subtitleField.value = aiData.subtitle;
                        this.updateCaseStudyData('solution_subtitle', aiData.subtitle);
                    }
                }
                if (aiData.description) {
                    const descField = document.querySelector('textarea[name="solution_description"]');
                    if (descField) {
                        descField.value = aiData.description;
                        this.updateCaseStudyData('solution_description', aiData.description);
                    }
                }
                if (aiData.points && Array.isArray(aiData.points)) {
                    aiData.points.forEach((point, index) => {
                        const pointIndex = index + 1;
                        const titleField = document.querySelector(`input[name="solution_point_${pointIndex}_title"]`);
                        const descField = document.querySelector(`textarea[name="solution_point_${pointIndex}_description"]`);

                        if (titleField && point.title) {
                            titleField.value = point.title;
                            this.updateCaseStudyData(`solution_point_${pointIndex}_title`, point.title);
                        }
                        if (descField && point.description) {
                            descField.value = point.description;
                            this.updateCaseStudyData(`solution_point_${pointIndex}_description`, point.description);
                        }
                    });
                }
                break;

            case 'results':
                // Handle the 4 metrics
                for (let i = 1; i <= 4; i++) {
                    const metricKey = `metric_${i}`;
                    if (aiData[metricKey]) {
                        const metric = aiData[metricKey];

                        const valueField = document.querySelector(`input[name="results_metric_${i}_value"]`);
                        const titleField = document.querySelector(`input[name="results_metric_${i}_title"]`);
                        const descField = document.querySelector(`input[name="results_metric_${i}_description"]`);

                        if (valueField && metric.value) {
                            valueField.value = metric.value;
                            this.updateResultsData(`metric_${i}_value`, metric.value);
                        }
                        if (titleField && metric.title) {
                            titleField.value = metric.title;
                            this.updateResultsData(`metric_${i}_title`, metric.title);
                        }
                        if (descField && metric.description) {
                            descField.value = metric.description;
                            this.updateResultsData(`metric_${i}_description`, metric.description);
                        }
                    }
                }
                break;

            case 'testimonial':
                if (aiData.quote) {
                    const quoteField = document.querySelector('textarea[name="testimonial_quote"]');
                    if (quoteField) {
                        quoteField.value = aiData.quote;
                        this.updateCaseStudyData('testimonial_quote', aiData.quote);
                    }
                }
                if (aiData.author_name) {
                    const nameField = document.querySelector('input[name="testimonial_author_name"]');
                    if (nameField) {
                        nameField.value = aiData.author_name;
                        this.updateCaseStudyData('testimonial_author_name', aiData.author_name);
                    }
                }
                break;

            case 'cta':
                if (aiData.title) {
                    const titleField = document.querySelector('input[name="cta_title"]');
                    if (titleField) {
                        titleField.value = aiData.title;
                        this.updateCaseStudyData('cta_title', aiData.title);
                    }
                }
                if (aiData.description) {
                    const descField = document.querySelector('textarea[name="cta_description"]');
                    if (descField) {
                        descField.value = aiData.description;
                        this.updateCaseStudyData('cta_description', aiData.description);
                    }
                }
                break;

            default:
                console.warn(`Unknown section type: ${sectionType}`);
        }
    },

    // Helper method to update results data
    updateResultsData(fieldName, value) {
        if (!this.state.currentCaseStudy.results_data) {
            this.state.currentCaseStudy.results_data = {};
        }
        this.state.currentCaseStudy.results_data[fieldName] = value;
        this.markDirty();
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.AdZetaCaseStudyEditor === 'undefined') {
        window.AdZetaCaseStudyEditor = AdZetaCaseStudyEditor;
        AdZetaCaseStudyEditor.init();
    }
});
