# AdZeta Website Environment Configuration
# Copy this file to .env and update the values

# Application Environment
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=adzeta_website
DB_USERNAME=root
DB_PASSWORD=

# Site Configuration
SITE_NAME="AdZeta"
SITE_TAGLINE="AI-Powered Value-Based Bidding for E-commerce Growth"
ADMIN_EMAIL=<EMAIL>

# Security
SESSION_LIFETIME=3600
CSRF_TOKEN_NAME=adzeta_csrf_token
MAX_LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_TIME=900

# Cache Configuration
CACHE_ENABLED=false
CACHE_LIFETIME=3600

# Email Configuration (for contact forms, notifications)
MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="AdZeta"

# Analytics & Tracking
GOOGLE_ANALYTICS_ID=
FACEBOOK_PIXEL_ID=
GOOGLE_TAG_MANAGER_ID=

# Social Media
FACEBOOK_URL=https://www.facebook.com/adzeta
TWITTER_URL=https://twitter.com/adzeta
LINKEDIN_URL=https://www.linkedin.com/company/adzeta

# API Keys (for integrations)
RECAPTCHA_SITE_KEY=
RECAPTCHA_SECRET_KEY=

# File Upload
MAX_UPLOAD_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Logging
LOG_LEVEL=debug
LOG_CHANNEL=daily
