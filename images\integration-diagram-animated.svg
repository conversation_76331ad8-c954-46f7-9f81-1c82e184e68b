<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="500" viewBox="0 0 1000 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions -->
  <defs>
    <linearGradient id="centerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#ff5d74"/>
      <stop offset="100%" stop-color="#e958a1"/>
    </linearGradient>
    <linearGradient id="pinkLine" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#ff5d74"/>
      <stop offset="100%" stop-color="#ff5d74" stop-opacity="0.2"/>
    </linearGradient>
    <linearGradient id="purpleLine" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#e958a1"/>
      <stop offset="100%" stop-color="#e958a1" stop-opacity="0.2"/>
    </linearGradient>
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="2" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
  </defs>

  <!-- Background - transparent to match the dark background -->
  <rect width="1000" height="500" fill="transparent"/>

  <!-- Center Platform -->
  <g transform="translate(500, 250)">
    <circle cx="0" cy="0" r="80" fill="#1B0B24" filter="url(#shadow)"/>
    <circle cx="0" cy="0" r="78" fill="#1B0B24" stroke="url(#centerGradient)" stroke-width="3" filter="url(#glow)">
      <animate attributeName="r" values="78;80;78" dur="3s" repeatCount="indefinite"/>
    </circle>

    <!-- Logo Placeholder -->
    <rect x="-40" y="-40" width="80" height="80" rx="10" fill="rgba(255,255,255,0.1)" stroke="url(#centerGradient)" stroke-width="2">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite"/>
    </rect>

    <text x="0" y="-10" font-family="SF Pro Display, Arial, sans-serif" font-size="20" font-weight="600" text-anchor="middle" fill="white">ADZETA AI</text>
    <text x="0" y="15" font-family="SF Pro Display, Arial, sans-serif" font-size="14" font-weight="400" text-anchor="middle" fill="white">Platform</text>

    <!-- Animated Pulse -->
    <circle cx="0" cy="0" r="90" fill="none" stroke="url(#centerGradient)" stroke-width="1" opacity="0">
      <animate attributeName="r" values="80;100;120" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.5;0.2;0" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- E-commerce Platforms Card -->
  <g transform="translate(200, 150)">
    <rect x="-120" y="-70" width="240" height="140" rx="20" fill="rgba(255,255,255,0.9)" filter="url(#shadow)">
      <animate attributeName="opacity" values="0.9;1;0.9" dur="4s" repeatCount="indefinite"/>
    </rect>
    <text x="0" y="-40" font-family="SF Pro Display, Arial, sans-serif" font-size="16" font-weight="600" text-anchor="middle" fill="#1B0B24">E-commerce Platform Data</text>

    <!-- Image Placeholder 1 -->
    <g transform="translate(-70, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#95BF47" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="0s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#95BF47">Image</text>
    </g>

    <!-- Image Placeholder 2 -->
    <g transform="translate(0, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#ff5d74" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="1s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#ff5d74">Image</text>
    </g>

    <!-- Image Placeholder 3 -->
    <g transform="translate(70, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#7f54b3" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="2s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#7f54b3">Image</text>
    </g>

    <!-- Decorative Element -->
    <circle cx="120" cy="-70" r="5" fill="#ff5d74" opacity="0.7">
      <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Data Warehouses & Lakes Card -->
  <g transform="translate(200, 350)">
    <rect x="-120" y="-70" width="240" height="140" rx="20" fill="rgba(255,255,255,0.9)" filter="url(#shadow)">
      <animate attributeName="opacity" values="0.9;1;0.9" dur="4s" repeatCount="indefinite"/>
    </rect>
    <text x="0" y="-40" font-family="SF Pro Display, Arial, sans-serif" font-size="16" font-weight="600" text-anchor="middle" fill="#1B0B24">Data Warehouses &amp; Lakes</text>

    <!-- Image Placeholder 1 -->
    <g transform="translate(-70, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#29B5E8" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="0s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#29B5E8">Image</text>
    </g>

    <!-- Image Placeholder 2 -->
    <g transform="translate(0, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#4285F4" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="1s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#4285F4">Image</text>
    </g>

    <!-- Image Placeholder 3 -->
    <g transform="translate(70, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#8C4FFF" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="2s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#8C4FFF">Image</text>
    </g>

    <!-- Decorative Element -->
    <circle cx="-120" cy="70" r="5" fill="#e958a1" opacity="0.7">
      <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Website & App Analytics Card -->
  <g transform="translate(800, 150)">
    <rect x="-120" y="-70" width="240" height="140" rx="20" fill="rgba(255,255,255,0.9)" filter="url(#shadow)">
      <animate attributeName="opacity" values="0.9;1;0.9" dur="4s" repeatCount="indefinite"/>
    </rect>
    <text x="0" y="-40" font-family="SF Pro Display, Arial, sans-serif" font-size="16" font-weight="600" text-anchor="middle" fill="#1B0B24">Website &amp; App Analytics</text>

    <!-- Image Placeholder 1 -->
    <g transform="translate(-70, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#F9AB00" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="0s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#F9AB00">Image</text>
    </g>

    <!-- Image Placeholder 2 -->
    <g transform="translate(0, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#52BD95" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="1s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#52BD95">Image</text>
    </g>

    <!-- Image Placeholder 3 -->
    <g transform="translate(70, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#4069E5" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="2s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="10" font-weight="500" text-anchor="middle" fill="#4069E5">Image</text>
    </g>

    <!-- Decorative Element -->
    <circle cx="120" cy="-70" r="5" fill="#ff5d74" opacity="0.7">
      <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- CRM & Customer Data Platforms Card -->
  <g transform="translate(800, 350)">
    <rect x="-120" y="-70" width="240" height="140" rx="20" fill="rgba(255,255,255,0.9)" filter="url(#shadow)">
      <animate attributeName="opacity" values="0.9;1;0.9" dur="4s" repeatCount="indefinite"/>
    </rect>
    <text x="0" y="-40" font-family="SF Pro Display, Arial, sans-serif" font-size="16" font-weight="600" text-anchor="middle" fill="#1B0B24">CRM &amp; Customer Data Platforms</text>

    <!-- HubSpot -->
    <g transform="translate(-70, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#FF7A59" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="0s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="11" font-weight="500" text-anchor="middle" fill="#FF7A59">HubSpot</text>
    </g>

    <!-- Salesforce -->
    <g transform="translate(0, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#00A1E0" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="1s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="11" font-weight="500" text-anchor="middle" fill="#00A1E0">Salesforce</text>
    </g>

    <!-- Klaviyo -->
    <g transform="translate(70, 0)">
      <rect x="-30" y="-25" width="60" height="50" rx="10" fill="white" stroke="#1772CE" stroke-width="1.5">
        <animate attributeName="stroke-width" values="1.5;2;1.5" dur="3s" begin="2s" repeatCount="indefinite"/>
      </rect>
      <text x="0" y="5" font-family="SF Pro Display, Arial, sans-serif" font-size="11" font-weight="500" text-anchor="middle" fill="#1772CE">Klaviyo</text>
    </g>

    <!-- Decorative Element -->
    <circle cx="120" cy="-70" r="5" fill="#e958a1" opacity="0.7">
      <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.7;0.9;0.7" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Connection Lines -->
  <!-- Ad Platforms to Center -->
  <path d="M320,150 Q410,200 430,250" stroke="url(#pinkLine)" stroke-width="2" stroke-dasharray="5 5" stroke-linecap="round"/>

  <!-- Animated Dots on Ad Platforms Line -->
  <circle cx="320" cy="150" r="3" fill="#e958a1">
    <animate attributeName="cx" values="320;375;430" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="150;200;250" dur="3s" repeatCount="indefinite"/>
  </circle>

  <circle cx="320" cy="150" r="3" fill="#e958a1" opacity="0.7">
    <animate attributeName="cx" values="320;375;430" dur="3s" begin="1s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="150;200;250" dur="3s" begin="1s" repeatCount="indefinite"/>
  </circle>

  <circle cx="320" cy="150" r="3" fill="#e958a1" opacity="0.4">
    <animate attributeName="cx" values="320;375;430" dur="3s" begin="2s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="150;200;250" dur="3s" begin="2s" repeatCount="indefinite"/>
  </circle>

  <!-- Data Sources to Center -->
  <path d="M320,350 Q410,300 430,250" stroke="url(#pinkLine)" stroke-width="2" stroke-dasharray="5 5" stroke-linecap="round"/>

  <!-- Animated Dots on Data Sources Line -->
  <circle cx="320" cy="350" r="3" fill="#e958a1">
    <animate attributeName="cx" values="320;375;430" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="350;300;250" dur="3s" repeatCount="indefinite"/>
  </circle>

  <circle cx="320" cy="350" r="3" fill="#e958a1" opacity="0.7">
    <animate attributeName="cx" values="320;375;430" dur="3s" begin="1s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="350;300;250" dur="3s" begin="1s" repeatCount="indefinite"/>
  </circle>

  <circle cx="320" cy="350" r="3" fill="#e958a1" opacity="0.4">
    <animate attributeName="cx" values="320;375;430" dur="3s" begin="2s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="350;300;250" dur="3s" begin="2s" repeatCount="indefinite"/>
  </circle>

  <!-- Center to Security -->
  <path d="M570,250 Q590,200 680,150" stroke="url(#purpleLine)" stroke-width="2" stroke-dasharray="5 5" stroke-linecap="round"/>

  <!-- Animated Dots on Security Line -->
  <circle cx="570" cy="250" r="3" fill="#8f76f5">
    <animate attributeName="cx" values="570;625;680" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="250;200;150" dur="3s" repeatCount="indefinite"/>
  </circle>

  <circle cx="570" cy="250" r="3" fill="#8f76f5" opacity="0.7">
    <animate attributeName="cx" values="570;625;680" dur="3s" begin="1s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="250;200;150" dur="3s" begin="1s" repeatCount="indefinite"/>
  </circle>

  <circle cx="570" cy="250" r="3" fill="#8f76f5" opacity="0.4">
    <animate attributeName="cx" values="570;625;680" dur="3s" begin="2s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="250;200;150" dur="3s" begin="2s" repeatCount="indefinite"/>
  </circle>

  <!-- Center to Business Outcomes -->
  <path d="M570,250 Q590,300 680,350" stroke="url(#purpleLine)" stroke-width="2" stroke-dasharray="5 5" stroke-linecap="round"/>

  <!-- Animated Dots on Business Outcomes Line -->
  <circle cx="570" cy="250" r="3" fill="#8f76f5">
    <animate attributeName="cx" values="570;625;680" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="250;300;350" dur="3s" repeatCount="indefinite"/>
  </circle>

  <circle cx="570" cy="250" r="3" fill="#8f76f5" opacity="0.7">
    <animate attributeName="cx" values="570;625;680" dur="3s" begin="1s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="250;300;350" dur="3s" begin="1s" repeatCount="indefinite"/>
  </circle>

  <circle cx="570" cy="250" r="3" fill="#8f76f5" opacity="0.4">
    <animate attributeName="cx" values="570;625;680" dur="3s" begin="2s" repeatCount="indefinite"/>
    <animate attributeName="cy" values="250;300;350" dur="3s" begin="2s" repeatCount="indefinite"/>
  </circle>

  <!-- Decorative Elements -->
  <circle cx="500" cy="400" r="3" fill="#e958a1" opacity="0.5">
    <animate attributeName="r" values="3;4;3" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite"/>
  </circle>

  <circle cx="500" cy="100" r="3" fill="#8f76f5" opacity="0.5">
    <animate attributeName="r" values="3;4;3" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.5;0.8;0.5" dur="4s" repeatCount="indefinite"/>
  </circle>
</svg>
