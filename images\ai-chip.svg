 <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="pulse">
  <style>
  /* HOMEPAGE */

/* AI Icon Animation */
.ai-icon {
    display: inline-flex;
    margin-right: 6px;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

.ai-icon svg {
    width: 16px;
    height: 16px;
    filter: drop-shadow(0 0 3px rgba(233, 88, 161, 0.3));
}

.ai-icon .pulse {
    animation: ai-pulse 3s infinite ease-in-out;
}

.ai-icon .sparkle {
    animation: ai-sparkle 3s infinite ease-in-out;
    transform-origin: center;
}

/* Hero image pulsating animation */
.platform-animation-container img.apple-inspired-animation {
    animation: hero-image-pulse 3s infinite ease-in-out;
    transform-origin: center center;
}

@keyframes hero-image-pulse {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.02);
        filter: brightness(1.05);
    }
    100% {
        transform: scale(1);
        filter: brightness(1);
    }
}

/* Circuit animations with different timings for a more tech feel */
.ai-icon [class^="circuit"] {
    animation: ai-circuit-pulse 2s infinite ease-in-out;
    stroke-dasharray: 0;
    stroke-dashoffset: 0;
}

.ai-icon .circuit1 { animation-delay: 0.0s; }
.ai-icon .circuit2 { animation-delay: 0.2s; }
.ai-icon .circuit3 { animation-delay: 0.4s; }
.ai-icon .circuit4 { animation-delay: 0.6s; }
.ai-icon .circuit5 { animation-delay: 0.8s; }
.ai-icon .circuit6 { animation-delay: 1.0s; }
.ai-icon .circuit7 { animation-delay: 1.2s; }
.ai-icon .circuit8 { animation-delay: 1.4s; }
.ai-icon .circuit9 { animation-delay: 1.6s; }
.ai-icon .circuit10 { animation-delay: 1.8s; }
.ai-icon .circuit11 { animation-delay: 2.0s; }
.ai-icon .circuit12 { animation-delay: 2.2s; }

.ai-icon .grid {
    animation: ai-grid-pulse 3s infinite ease-in-out;
}

.ai-icon .core {
    animation: ai-core-pulse 2s infinite ease-in-out;
}

@keyframes ai-pulse {
    0% {
        opacity: 0.9;
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
        filter: brightness(1.2);
    }
    100% {
        opacity: 0.9;
        transform: scale(1);
        filter: brightness(1);
    }
}

@keyframes ai-sparkle {
    0% {
        opacity: 0.3;
        stroke-dasharray: 1 3;
    }
    50% {
        opacity: 0.7;
        stroke-dasharray: 2 2;
    }
    100% {
        opacity: 0.3;
        stroke-dasharray: 1 3;
    }
}

@keyframes ai-circuit-pulse {
    0% {
        opacity: 0.3;
        stroke-width: 1;
    }
    50% {
        opacity: 1;
        stroke-width: 2;
    }
    100% {
        opacity: 0.3;
        stroke-width: 1;
    }
}

@keyframes ai-grid-pulse {
    0% {
        opacity: 0.3;
        stroke-dasharray: 0.5 2.5;
    }
    50% {
        opacity: 0.7;
        stroke-dasharray: 1 2;
    }
    100% {
        opacity: 0.3;
        stroke-dasharray: 0.5 2.5;
    }
}

@keyframes ai-core-pulse {
    0% {
        opacity: 0.7;
        filter: brightness(0.9);
    }
    50% {
        opacity: 1;
        filter: brightness(1.3);
    }
    100% {
        opacity: 0.7;
        filter: brightness(0.9);
    }
}

  </style>
 
	<!-- Modern AI chip/processor shape -->
	<rect x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5"/>
	<!-- Circuit lines -->
	<path class="circuit1" d="M8 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit2" d="M12 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit3" d="M16 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit4" d="M8 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit5" d="M12 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit6" d="M16 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit7" d="M2 8H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit8" d="M2 12H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit9" d="M2 16H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit10" d="M20 8H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit11" d="M20 12H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<path class="circuit12" d="M20 16H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
	<!-- Inner processor grid -->
	<path class="grid" d="M8 8H16M8 12H16M8 16H16M8 8V16M12 8V16M16 8V16" stroke="url(#paint0_linear_ai_icon)" stroke-width="0.75" stroke-linecap="round" stroke-dasharray="1 2"/>
	<!-- Central core -->
	<rect class="core" x="10" y="10" width="4" height="4" fill="url(#paint0_linear_ai_icon)"/>
	<!-- Sparkle overlay -->
	<rect class="sparkle" x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint1_linear_ai_icon)" stroke-width="1" stroke-opacity="0.7"/>
	<defs>
		<linearGradient id="paint0_linear_ai_icon" x1="2" y1="2" x2="22" y2="22" gradientUnits="userSpaceOnUse">
			<stop offset="0" stop-color="#e958a1"/>
			<stop offset="0.5" stop-color="#8f76f5"/>
			<stop offset="1" stop-color="#4a9eff"/>
		</linearGradient>
		<linearGradient id="paint1_linear_ai_icon" x1="20" y1="4" x2="4" y2="20" gradientUnits="userSpaceOnUse">
			<stop offset="0" stop-color="#ffffff"/>
			<stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
		</linearGradient>
	</defs>
</svg>