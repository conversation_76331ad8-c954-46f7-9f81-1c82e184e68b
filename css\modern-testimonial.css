/* Modern, Professional Testimonial Design */
.testimonial-section {
    margin: 60px 0;
    position: relative;
    padding: 20px 0;
    background-image: radial-gradient(rgba(244, 88, 136, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
}

.modern-testimonial {
    max-width: 900px;
    margin: 0 auto;
    background-color: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.02);
}

.testimonial-header {
    display: flex;
    align-items: center;
    padding: 30px 40px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

.testimonial-profile {
    display: flex;
    align-items: center;
    flex: 1;
}

.profile-pic {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 16px;
    flex-shrink: 0;
    background-color: #f8f8f8;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.profile-pic img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    flex: 1;
}

.testimonial-author {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.testimonial-position {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

.testimonial-company {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #666;
}

.testimonial-company img {
    height: 14px;
    margin-right: 6px;
    opacity: 0.9;
}

.testimonial-body {
    padding: 30px 40px;
}

.testimonial-quote {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    margin-bottom: 30px;
    font-weight: 400;
    font-style: italic;
    position: relative;
    padding-left: 25px;
}

.testimonial-quote:before {
    content: """;
    position: absolute;
    left: 0;
    top: -5px;
    font-size: 40px;
    color: rgba(244, 88, 136, 0.15);
    font-family: Georgia, serif;
    line-height: 1;
}

.testimonial-metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    background-color: #f9f9f9;
    border-radius: 12px;
    padding: 20px 10px;
}

.metric-card {
    text-align: center;
    flex: 1;
    position: relative;
    padding: 0 15px;
}

.metric-card:not(:last-child):after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 70%;
    width: 1px;
    background-color: rgba(0, 0, 0, 0.08);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #f45888;
    margin-bottom: 8px;
    line-height: 1;
    background: linear-gradient(135deg, #f45888, #ff5d74);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.metric-label {
    font-size: 13px;
    color: #666;
    font-weight: 400;
}

.testimonial-footer {
    padding: 0 40px 30px;
    display: flex;
    justify-content: flex-start;
}

.case-study-link {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 10px 20px;
    border-radius: 30px;
    background: linear-gradient(135deg, #f45888, #ff5d74);
    box-shadow: 0 4px 15px rgba(244, 88, 136, 0.2);
}

.case-study-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 88, 136, 0.25);
}

.case-study-link i {
    margin-left: 8px;
    font-size: 12px;
    transition: transform 0.2s ease;
}

.case-study-link:hover i {
    transform: translateX(3px);
}

/* Responsive Styles */
@media (max-width: 991px) {
    .testimonial-metrics {
        padding: 15px 5px;
    }

    .metric-value {
        font-size: 22px;
    }
}

@media (max-width: 767px) {
    .testimonial-header {
        padding: 25px;
    }

    .testimonial-body {
        padding: 25px;
    }

    .testimonial-footer {
        padding: 0 25px 25px;
        justify-content: center;
    }

    .testimonial-metrics {
        flex-direction: column;
        gap: 0;
        padding: 5px 15px;
        background-color: transparent;
    }

    .metric-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
        padding: 15px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }

    .metric-card:last-child {
        border-bottom: none;
    }

    .metric-card:not(:last-child):after {
        display: none;
    }

    .metric-value {
        font-size: 20px;
        margin-bottom: 0;
    }

    .case-study-link {
        width: 100%;
        justify-content: center;
    }
}
