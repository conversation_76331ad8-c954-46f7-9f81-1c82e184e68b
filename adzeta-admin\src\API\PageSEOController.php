<?php

namespace AdZetaAdmin\API;

/**
 * Page SEO Management API Controller - Production Ready
 * Handles SEO data for static pages and blog posts
 */
class PageSEOController extends BaseController
{
    private function getDatabase()
    {
        static $pdo = null;
        
        if ($pdo === null) {
            try {
                $pdo = new \PDO(
                    'mysql:host=localhost;dbname=adzetadb;charset=utf8mb4',
                    'adzetauser',
                    'Crazy1395#',
                    [
                        \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                        \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC
                    ]
                );
            } catch (\PDOException $e) {
                throw new \Exception("Database connection failed: " . $e->getMessage());
            }
        }
        
        return $pdo;
    }

    /**
     * Get all pages with SEO data
     */
    public function index()
    {
        try {
            $db = $this->getDatabase();
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = max(1, min(50, intval($_GET['limit'] ?? 20)));
            $search = trim($_GET['search'] ?? '');
            $offset = ($page - 1) * $limit;

            $whereClause = '';
            $params = [];
            
            if (!empty($search)) {
                $whereClause = 'WHERE page_url LIKE ? OR page_title LIKE ? OR meta_description LIKE ?';
                $searchTerm = "%$search%";
                $params = [$searchTerm, $searchTerm, $searchTerm];
            }

            // Get total count
            $countSQL = "SELECT COUNT(*) FROM page_seo $whereClause";
            $countStmt = $db->prepare($countSQL);
            $countStmt->execute($params);
            $totalItems = $countStmt->fetchColumn();
            $totalPages = ceil($totalItems / $limit);

            // Get pages
            $pagesSQL = "
                SELECT id, page_url, page_title, meta_description, meta_keywords,
                       og_title, og_description, og_image, is_active, updated_at
                FROM page_seo
                $whereClause
                ORDER BY page_url ASC
                LIMIT $limit OFFSET $offset
            ";
            
            $pagesStmt = $db->prepare($pagesSQL);
            $pagesStmt->execute($params);
            $pages = $pagesStmt->fetchAll(\PDO::FETCH_ASSOC);

            return $this->success([
                'pages' => $pages,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_items' => $totalItems,
                    'items_per_page' => $limit,
                    'has_next' => $page < $totalPages,
                    'has_prev' => $page > 1
                ],
                'search' => $search
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to load pages: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get a specific page by ID
     */
    public function show($id)
    {
        try {
            $db = $this->getDatabase();
            $stmt = $db->prepare("SELECT * FROM page_seo WHERE id = ?");
            $stmt->execute([$id]);
            $page = $stmt->fetch();
            
            if (!$page) {
                return $this->error('Page not found', 404);
            }

            // Parse JSON fields
            if ($page['schema_markup']) {
                $page['schema_markup'] = json_decode($page['schema_markup'], true);
            }

            return $this->success(['page' => $page]);

        } catch (\Exception $e) {
            return $this->error('Failed to load page: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Create a new page SEO entry
     */
    public function store()
    {
        try {
            $data = json_decode(file_get_contents('php://input'), true) ?: $_POST;
            $db = $this->getDatabase();
            
            // Validate required fields
            if (empty($data['page_url']) || empty($data['page_title'])) {
                return $this->error('Page URL and title are required', 400);
            }

            // Normalize page URL
            $pageUrl = $this->normalizePageUrl($data['page_url']);

            // Check if page already exists
            $existingStmt = $db->prepare("SELECT id FROM page_seo WHERE page_url = ?");
            $existingStmt->execute([$pageUrl]);
            if ($existingStmt->fetch()) {
                return $this->error('A page with this URL already exists', 400);
            }
            
            // Insert new page
            $insertStmt = $db->prepare("
                INSERT INTO page_seo (
                    page_url, page_title, meta_description, meta_keywords, 
                    og_title, og_description, og_image, og_type,
                    twitter_card, twitter_title, twitter_description, twitter_image,
                    canonical_url, robots, schema_markup, custom_head_tags, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $success = $insertStmt->execute([
                $pageUrl,
                $data['page_title'],
                $data['meta_description'] ?? '',
                $data['meta_keywords'] ?? '',
                $data['og_title'] ?? $data['page_title'],
                $data['og_description'] ?? $data['meta_description'] ?? '',
                $data['og_image'] ?? '',
                $data['og_type'] ?? 'website',
                $data['twitter_card'] ?? 'summary_large_image',
                $data['twitter_title'] ?? '',
                $data['twitter_description'] ?? '',
                $data['twitter_image'] ?? '',
                $data['canonical_url'] ?? '',
                $data['robots'] ?? 'index,follow',
                !empty($data['schema_markup']) ? json_encode($data['schema_markup']) : null,
                $data['custom_head_tags'] ?? '',
                isset($data['is_active']) ? (bool)$data['is_active'] : true
            ]);
            
            if ($success) {
                $pageId = $db->lastInsertId();
                $pageStmt = $db->prepare("SELECT * FROM page_seo WHERE id = ?");
                $pageStmt->execute([$pageId]);
                $page = $pageStmt->fetch();

                return $this->success([
                    'message' => 'Page SEO data created successfully',
                    'page' => $page
                ], 201);
            } else {
                return $this->error('Failed to create page SEO data', 500);
            }

        } catch (\Exception $e) {
            return $this->error('Failed to create page: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get SEO settings
     */
    public function getSettings()
    {
        try {
            $db = $this->getDatabase();
            $stmt = $db->query("SELECT setting_key, setting_value, setting_description FROM seo_settings ORDER BY setting_key");
            $settings = [];
            
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $settings[$row['setting_key']] = [
                    'value' => $row['setting_value'],
                    'description' => $row['setting_description']
                ];
            }
            
            return $this->success(['settings' => $settings]);

        } catch (\Exception $e) {
            return $this->error('Failed to load SEO settings: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update SEO settings
     */
    public function updateSettings()
    {
        try {
            $data = json_decode(file_get_contents('php://input'), true) ?: $_POST;
            $db = $this->getDatabase();
            
            if (empty($data['settings'])) {
                return $this->error('No settings provided', 400);
            }

            $updateStmt = $db->prepare("UPDATE seo_settings SET setting_value = ? WHERE setting_key = ?");

            foreach ($data['settings'] as $key => $value) {
                $updateStmt->execute([$value, $key]);
            }

            return $this->success(['message' => 'SEO settings updated successfully']);

        } catch (\Exception $e) {
            return $this->error('Failed to update SEO settings: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update an existing page SEO entry
     */
    public function update($id)
    {
        try {
            $data = json_decode(file_get_contents('php://input'), true) ?: $_POST;
            $db = $this->getDatabase();

            // Check if page exists
            $existingStmt = $db->prepare("SELECT * FROM page_seo WHERE id = ?");
            $existingStmt->execute([$id]);
            $existing = $existingStmt->fetch();

            if (!$existing) {
                return $this->error('Page not found', 404);
            }

            // Validate required fields
            if (empty($data['page_title'])) {
                return $this->error('Page title is required', 400);
            }

            // Update page
            $updateStmt = $db->prepare("
                UPDATE page_seo SET
                page_title = ?, meta_description = ?, meta_keywords = ?,
                og_title = ?, og_description = ?, og_image = ?, og_type = ?,
                twitter_card = ?, twitter_title = ?, twitter_description = ?, twitter_image = ?,
                canonical_url = ?, robots = ?, schema_markup = ?, custom_head_tags = ?, is_active = ?
                WHERE id = ?
            ");

            $success = $updateStmt->execute([
                $data['page_title'],
                $data['meta_description'] ?? '',
                $data['meta_keywords'] ?? '',
                $data['og_title'] ?? $data['page_title'],
                $data['og_description'] ?? $data['meta_description'] ?? '',
                $data['og_image'] ?? '',
                $data['og_type'] ?? 'website',
                $data['twitter_card'] ?? 'summary_large_image',
                $data['twitter_title'] ?? '',
                $data['twitter_description'] ?? '',
                $data['twitter_image'] ?? '',
                $data['canonical_url'] ?? '',
                $data['robots'] ?? 'index,follow',
                !empty($data['schema_markup']) ? json_encode($data['schema_markup']) : null,
                $data['custom_head_tags'] ?? '',
                isset($data['is_active']) ? (bool)$data['is_active'] : true,
                $id
            ]);

            if ($success) {
                $pageStmt = $db->prepare("SELECT * FROM page_seo WHERE id = ?");
                $pageStmt->execute([$id]);
                $page = $pageStmt->fetch();

                return $this->success([
                    'message' => 'Page SEO data updated successfully',
                    'page' => $page
                ]);
            } else {
                return $this->error('Failed to update page SEO data', 500);
            }

        } catch (\Exception $e) {
            return $this->error('Failed to update page: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete a page SEO entry
     */
    public function destroy($id)
    {
        try {
            $db = $this->getDatabase();
            $pageStmt = $db->prepare("SELECT * FROM page_seo WHERE id = ?");
            $pageStmt->execute([$id]);
            $page = $pageStmt->fetch();

            if (!$page) {
                return $this->error('Page not found', 404);
            }

            $deleteStmt = $db->prepare("DELETE FROM page_seo WHERE id = ?");
            $success = $deleteStmt->execute([$id]);

            if ($success) {
                return $this->success(['message' => 'Page SEO data deleted successfully']);
            } else {
                return $this->error('Failed to delete page SEO data', 500);
            }

        } catch (\Exception $e) {
            return $this->error('Failed to delete page: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Toggle page active status
     */
    public function toggleStatus($id)
    {
        try {
            $db = $this->getDatabase();
            $pageStmt = $db->prepare("SELECT * FROM page_seo WHERE id = ?");
            $pageStmt->execute([$id]);
            $page = $pageStmt->fetch();

            if (!$page) {
                return $this->error('Page not found', 404);
            }

            $newStatus = !$page['is_active'];
            $updateStmt = $db->prepare("UPDATE page_seo SET is_active = ? WHERE id = ?");
            $success = $updateStmt->execute([$newStatus, $id]);

            if ($success) {
                return $this->success([
                    'message' => 'Page status updated successfully',
                    'is_active' => $newStatus
                ]);
            } else {
                return $this->error('Failed to update page status', 500);
            }

        } catch (\Exception $e) {
            return $this->error('Failed to toggle page status: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Normalize page URL for consistent storage
     */
    private function normalizePageUrl($url)
    {
        // Remove domain and protocol
        $url = parse_url($url, PHP_URL_PATH);

        // Handle root URL
        if ($url === '/' || $url === '') {
            return '/';
        }

        // Add leading slash if missing
        if (substr($url, 0, 1) !== '/') {
            $url = '/' . $url;
        }

        return $url;
    }
}
?>
