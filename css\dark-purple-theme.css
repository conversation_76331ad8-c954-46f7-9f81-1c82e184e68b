/* Dark Purple Theme Enhancement - More black and purple styling */

/* Enhanced Hero Section Gradient - More professional dark theme */
.apple-inspired-gradient .gradient-backdrop {
    background: linear-gradient(166deg, #000000 0%, #050510 20%, #0A0A1A 40%, #10081E 60%, #180A28 80%, #1E0C32 100%);
    opacity: 1;
}

/* Enhanced accent glows - More professional and subtle */
.accent-glow.top-right {
    background: radial-gradient(circle, rgba(143, 118, 245, 0.15) 0%, rgba(31, 33, 42, 0) 80%);
    opacity: 0.7;
    width: 700px;
    height: 700px;
    filter: blur(60px);
}

.accent-glow.bottom-left {
    background: radial-gradient(circle, rgba(233, 88, 161, 0.12) 0%, rgba(31, 33, 42, 0) 80%);
    opacity: 0.7;
    width: 700px;
    height: 700px;
    filter: blur(60px);
}

/* Enhanced tech grid */
.tech-grid {
    background-image: linear-gradient(rgba(143, 118, 245, 0.06) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(143, 118, 245, 0.06) 1px, transparent 1px);
    background-size: 60px 60px;
    opacity: 0.3;
}

/* Enhanced tech dots */
.tech-dots {
    background-image: radial-gradient(rgba(143, 118, 245, 0.08) 1px, transparent 1px);
    background-size: 30px 30px;
    opacity: 0.25;
}

/* Enhanced digital circuit elements */
.digital-circuit {
    opacity: 0.12;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10,10 L30,10 L30,30 L50,30 L50,50 L70,50 L70,70 L90,70' stroke='%238f76f5' fill='none' stroke-width='0.9'/%3E%3Cpath d='M10,90 L30,90 L30,70 L50,70 L50,50 L70,50 L70,30 L90,30' stroke='%23e958a1' fill='none' stroke-width='0.9'/%3E%3Ccircle cx='30' cy='30' r='2' fill='%238f76f5' /%3E%3Ccircle cx='50' cy='50' r='2' fill='%23e958a1' /%3E%3Ccircle cx='70' cy='70' r='2' fill='%238f76f5' /%3E%3Ccircle cx='30' cy='70' r='2' fill='%23e958a1' /%3E%3Ccircle cx='70' cy='30' r='2' fill='%238f76f5' /%3E%3C/svg%3E");
    background-size: 180px 180px;
}

/* Enhanced gradient text styling - Professional continuous gradient */
/* New heading gradient style */
.heading-gradient {
    background-image: linear-gradient(166deg, #ffffff 0%, #e0e0e0 30%, #e958a1 70%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: none;
    font-weight: 700;
    letter-spacing: -3px;
    line-height: 1;
}

/* Legacy gradient text styling - kept for compatibility */
.modern-gradient-text .gradient-phrase {
    background-image: linear-gradient(166deg, #ffffff 0%, #e958a1 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: none;
}

.modern-gradient-text .highlight-word {
    background-image: linear-gradient(166deg, #e958a1 0%, #6a4fd9 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: none;
}

/* Enhanced platform section backgrounds */
.platform-section.bg-dark {
    background: linear-gradient(135deg, #000000 0%, #0A0A14 30%, #12081E 60%, #1A0A28 90%);
}

.platform-section.bg-dark::before {
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.25) 0%, rgba(31, 33, 42, 0) 60%);
    opacity: 0.9;
}

.platform-section.bg-dark::after {
    background: radial-gradient(circle at bottom left, rgba(233, 88, 161, 0.25) 0%, rgba(31, 33, 42, 0) 60%);
    opacity: 0.9;
}

/* Enhanced CTA section */
.platform-cta {
    background: linear-gradient(135deg, #000000 0%, #0A0A14 30%, #12081E 60%, #1A0A28 90%);
}

.platform-cta::before {
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.3) 0%, rgba(31, 33, 42, 0) 70%);
    opacity: 0.9;
}

.platform-cta::after {
    background: radial-gradient(ellipse at bottom left, rgba(233, 88, 161, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
    opacity: 0.9;
}






/* Enhanced particles */
#particles-style-03 {
    opacity: 1;
}

/* Enhanced floating elements */
.floating-element {
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.15) 0%, rgba(143, 118, 245, 0.15) 100%);
}

/* Enhanced feature boxes */
.platform-feature-box .icon-wrapper {
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.15) 0%, rgba(143, 118, 245, 0.15) 100%);
}

.platform-feature-box:hover .icon-wrapper {
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.2) 0%, rgba(143, 118, 245, 0.2) 100%);
}

/* Enhanced benefit cards */
.benefit-card.positive::before {
    background: linear-gradient(90deg, #e958a1 0%, #d15ec7 100%);
}

.benefit-card.reduction::before {
    background: linear-gradient(90deg, #8f76f5 0%, #6a4fd9 100%);
}

.benefit-card.positive .metric {
    background: linear-gradient(90deg, #e958a1 0%, #d15ec7 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.benefit-card.reduction .metric {
    background: linear-gradient(90deg, #8f76f5 0%, #6a4fd9 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* Enhanced process steps */
.process-step .step-number {
    background: linear-gradient(135deg, #8f76f5 0%, #d15ec7 100%);
    box-shadow: 0 10px 20px rgba(143, 118, 245, 0.4);
}

/* Enhanced Highlights Section Styling */
.bg-black-pearl-blue-dark {
    background: linear-gradient(145deg, #12081E 0%, #0A0A14 100%);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(143, 118, 245, 0.1);
    backdrop-filter: blur(5px);
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.bg-black-pearl-blue-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(135deg, rgba(143, 118, 245, 0.2), rgba(209, 94, 199, 0.2));
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.hover-box.dark-hover:hover .bg-black-pearl-blue-dark::before {
    opacity: 0.8;
}

.hover-box.dark-hover:hover {
    transform: translateY(-5px);
    transition: transform 0.3s ease;
}

.text-purple {
    color: #8f76f5;
}

.text-light-opacity {
    color: rgba(255, 255, 255, 0.7);
}

.feature-box-icon i.text-gradient-pink-orchid {
    filter: drop-shadow(0 0 10px rgba(233, 88, 161, 0.3));
    transition: filter 0.3s ease, transform 0.3s ease;
}

.feature-box-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.feature-box-content p {
    margin-bottom: 0;
}

.icon-with-text-style-07 {
    height: 100%;
    display: flex;
}

/* Equal height feature boxes - using original CSS structure */
.row-equal-height {
    display: flex;
    flex-wrap: wrap;
}

.row-equal-height > .col {
    display: flex;
    flex: 0 0 auto;
    width: 33.333333%;
    padding: 15px;
}

@media (max-width: 991px) {
    .row-equal-height > .col {
        width: 50%;
    }
}

@media (max-width: 767px) {
    .row-equal-height > .col {
        width: 100%;
    }
}

/* Maintain original feature box structure */
.feature-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    position: relative;
    z-index: 1;
    height: 100%;
}

.feature-box.text-start {
    align-items: flex-start;
    text-align: left;
    justify-content: flex-start;
}

.icon-with-text-style-07 {
    height: 100%;
}

.icon-with-text-style-07 .hover-box {
    width: 100%;
    height: 100%;
}

/* JavaScript solution for equal heights */
.js-equal-height {
    height: auto !important; /* Will be set by JS */
}

.hover-box.dark-hover:hover .feature-box-icon i.text-gradient-pink-orchid {
    filter: drop-shadow(0 0 15px rgba(233, 88, 161, 0.5));
    transform: scale(1.05);
}

.text-gradient-pink-orchid {
    background: linear-gradient(135deg, #e958a1 0%, #d15ec7 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.bg-gradient-pink-orchid {
    background: linear-gradient(135deg, #e958a1 0%, #d15ec7 100%);
}

.icon-extra-large {
    font-size: 3.5rem;
}

.hover-box.dark-hover:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.hover-box.dark-hover:hover .feature-box-overlay {
    opacity: 0.9;
}

.hover-box.dark-hover:hover .feature-box-icon i {
    transform: scale(1.1);
}

.feature-box-icon i {
    transition: all 0.5s ease;
}

.box-shadow-double-large-hover {
    transition: all 0.5s ease;
}

/* Particle effects for highlights section */
.professional-gradient-container {
    background: linear-gradient(135deg, #000000 0%, #0A0A14 30%, #12081E 60%, #1A0A28 90%);
}

.professional-gradient-container .corner-gradient.top-left {
    background: radial-gradient(circle at 30% 30%, rgba(143, 118, 245, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
}

.professional-gradient-container .corner-gradient.top-right {
    background: radial-gradient(circle at 70% 30%, rgba(233, 88, 161, 0.25) 0%, rgba(0, 0, 0, 0) 70%);
}

.professional-gradient-container .corner-gradient.bottom-left {
    background: radial-gradient(circle at 30% 70%, rgba(143, 118, 245, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
}

.professional-gradient-container .corner-gradient.bottom-right {
    background: radial-gradient(circle at 70% 70%, rgba(233, 88, 161, 0.15) 0%, rgba(0, 0, 0, 0) 70%);
}

.professional-gradient-container .diagonal-gradient {
    background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.95) 0%,
        rgba(10, 10, 20, 0.9) 20%,
        rgba(18, 8, 30, 0.85) 40%,
        rgba(26, 10, 40, 0.8) 60%,
        rgba(34, 12, 50, 0.75) 80%,
        rgba(42, 14, 60, 0.7) 100%);
}

.professional-gradient-container .mesh-overlay {
    background:
        linear-gradient(90deg, rgba(143, 118, 245, 0.05) 1px, transparent 1px),
        linear-gradient(0deg, rgba(143, 118, 245, 0.05) 1px, transparent 1px);
    background-size: 30px 30px;
}
