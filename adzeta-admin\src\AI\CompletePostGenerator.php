<?php

namespace AdZetaAdmin\AI;

/**
 * Complete Post Generator - Generates comprehensive blog posts with all SEO data
 */
class CompletePostGenerator
{
    private $db;
    private $geminiService;

    public function __construct($db)
    {
        $this->db = $db;
        
        // Initialize Gemini AI service
        require_once __DIR__ . '/../Services/GeminiAIService.php';
        $this->geminiService = new \AdZetaAdmin\Services\GeminiAIService($this->db);
    }

    /**
     * Generate a complete blog post with ALL data: content, SEO, tags, keywords, social media
     */
    public function generateCompletePost($topic, $template = 'professional-article', $options = []) {
        try {
            // Get comprehensive prompt for complete post generation
            $prompt = $this->getCompletePostPrompt($template, $topic, $options);

            // Generate complete content using Gemini AI
            $response = $this->geminiService->generateContent($prompt, [
                'temperature' => $options['temperature'] ?? 0.7,
                'maxOutputTokens' => $options['maxOutputTokens'] ?? 3000 // Increased for complete data
            ]);

            if (!$response || !$response['success']) {
                throw new \Exception('Failed to generate complete post: ' . ($response['error'] ?? 'Unknown error'));
            }

            // Parse the comprehensive response
            $parsedData = $this->parseCompletePostResponse($response['content'], $template);

            return [
                'success' => true,
                'data' => $parsedData
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get comprehensive prompt for complete post generation
     */
    private function getCompletePostPrompt($template, $topic, $options) {
        $basePrompt = "Generate a COMPLETE blog post with ALL metadata for the topic: \"$topic\"

IMPORTANT: You must provide ALL of the following sections in your response:

=== TITLE ===
[Provide an engaging, SEO-optimized title]

=== EXCERPT ===
[Provide a compelling 150-160 character excerpt/summary]

=== CONTENT ===
[Provide the full blog post content with HTML formatting, headers, paragraphs, lists, etc.]

=== SEO_META_TITLE ===
[Provide an SEO-optimized meta title (50-60 characters)]

=== SEO_META_DESCRIPTION ===
[Provide an SEO meta description (150-160 characters)]

=== SEO_META_KEYWORDS ===
[Provide 5-8 relevant SEO keywords, comma-separated]

=== FOCUS_KEYWORD ===
[Provide the primary focus keyword for SEO]

=== OG_TITLE ===
[Provide Open Graph title for social media]

=== OG_DESCRIPTION ===
[Provide Open Graph description for social media]

=== TAGS ===
[Provide 5-10 relevant tags, comma-separated]

=== KEYWORDS ===
[Provide 8-12 searchable keywords, comma-separated]

";

        // Add template-specific instructions
        switch ($template) {
            case 'professional-article':
                $basePrompt .= "
TEMPLATE: Professional Article
- Use professional, authoritative tone
- Include data and statistics where relevant
- Structure with clear headers (H2, H3)
- Add actionable insights and takeaways
- Include industry best practices
";
                break;

            case 'case-study':
                $basePrompt .= "
TEMPLATE: Case Study
- Follow problem-solution-results structure
- Include specific metrics and outcomes
- Use real-world examples and scenarios
- Add before/after comparisons
- Include lessons learned section
";
                break;

            case 'whitepaper':
                $basePrompt .= "
TEMPLATE: Whitepaper
- Use formal, research-based tone
- Include detailed analysis and insights
- Structure with executive summary
- Add charts/data references
- Include methodology and conclusions
";
                break;

            default:
                $basePrompt .= "
TEMPLATE: Professional Article (Default)
- Use engaging, informative tone
- Include practical examples
- Structure with clear sections
- Add actionable advice
";
        }

        $basePrompt .= "

CONTENT REQUIREMENTS:
- Minimum 800 words
- Use HTML formatting (h2, h3, p, ul, ol, strong, em)
- Include relevant industry keywords naturally
- Make it engaging and valuable to readers
- Optimize for search engines
- Include call-to-action elements

SEO REQUIREMENTS:
- Focus keyword should appear in title, meta description, and content
- Meta title should be compelling and under 60 characters
- Meta description should be compelling and 150-160 characters
- Keywords should be relevant and searchable
- Tags should help with content discovery

SOCIAL MEDIA REQUIREMENTS:
- OG title should be engaging for social sharing
- OG description should encourage clicks
- Content should be shareable and valuable

Please generate comprehensive, high-quality content that follows these guidelines exactly.";

        return $basePrompt;
    }

    /**
     * Parse the complete post response from AI
     */
    private function parseCompletePostResponse($content, $template) {
        $data = [
            'title' => '',
            'excerpt' => '',
            'content' => '',
            'template' => $template,
            'seo' => [
                'meta_title' => '',
                'meta_description' => '',
                'meta_keywords' => '',
                'focus_keyword' => '',
                'og_title' => '',
                'og_description' => '',
                'twitter_card_type' => 'summary_large_image'
            ],
            'tags' => [],
            'keywords' => []
        ];

        // Parse each section using regex
        $sections = [
            'title' => '/=== TITLE ===\s*\n(.*?)(?=\n=== |$)/s',
            'excerpt' => '/=== EXCERPT ===\s*\n(.*?)(?=\n=== |$)/s',
            'content' => '/=== CONTENT ===\s*\n(.*?)(?=\n=== |$)/s',
            'meta_title' => '/=== SEO_META_TITLE ===\s*\n(.*?)(?=\n=== |$)/s',
            'meta_description' => '/=== SEO_META_DESCRIPTION ===\s*\n(.*?)(?=\n=== |$)/s',
            'meta_keywords' => '/=== SEO_META_KEYWORDS ===\s*\n(.*?)(?=\n=== |$)/s',
            'focus_keyword' => '/=== FOCUS_KEYWORD ===\s*\n(.*?)(?=\n=== |$)/s',
            'og_title' => '/=== OG_TITLE ===\s*\n(.*?)(?=\n=== |$)/s',
            'og_description' => '/=== OG_DESCRIPTION ===\s*\n(.*?)(?=\n=== |$)/s',
            'tags' => '/=== TAGS ===\s*\n(.*?)(?=\n=== |$)/s',
            'keywords' => '/=== KEYWORDS ===\s*\n(.*?)(?=\n=== |$)/s'
        ];

        foreach ($sections as $key => $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $value = trim($matches[1]);
                
                switch ($key) {
                    case 'title':
                    case 'excerpt':
                    case 'content':
                        $data[$key] = $value;
                        break;
                        
                    case 'meta_title':
                    case 'meta_description':
                    case 'meta_keywords':
                    case 'focus_keyword':
                    case 'og_title':
                    case 'og_description':
                        $data['seo'][$key] = $value;
                        break;
                        
                    case 'tags':
                    case 'keywords':
                        // Convert comma-separated string to array
                        $items = array_map('trim', explode(',', $value));
                        $data[$key] = array_filter($items); // Remove empty items
                        break;
                }
            }
        }

        // Fallback: if sections not found, try to extract from unstructured content
        if (empty($data['title']) || empty($data['content'])) {
            $this->extractFallbackData($content, $data);
        }

        return $data;
    }

    /**
     * Extract data from unstructured content as fallback
     */
    private function extractFallbackData($content, &$data) {
        // Try to extract title from first line or heading
        $lines = explode("\n", $content);
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && empty($data['title'])) {
                // Remove markdown headers if present
                $data['title'] = preg_replace('/^#+\s*/', '', $line);
                break;
            }
        }

        // Use content as-is if no structured content found
        if (empty($data['content'])) {
            $data['content'] = $content;
        }

        // Generate basic SEO data if missing
        if (empty($data['seo']['meta_title']) && !empty($data['title'])) {
            $data['seo']['meta_title'] = substr($data['title'], 0, 60);
        }

        if (empty($data['excerpt'])) {
            // Extract first paragraph as excerpt
            $contentText = strip_tags($data['content']);
            $data['excerpt'] = substr($contentText, 0, 160) . '...';
        }
    }
}
