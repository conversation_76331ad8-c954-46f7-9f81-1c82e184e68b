/**
 * Comparison Pricing Section Animations
 * Creates engaging animations for the comparison pricing section
 */

document.addEventListener('DOMContentLoaded', function() {
    // Animate elements when they come into view
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementBottom = element.getBoundingClientRect().bottom;
            const windowHeight = window.innerHeight;
            
            // Check if element is in viewport
            if (elementTop < windowHeight * 0.9 && elementBottom > 0) {
                element.classList.add('active');
            }
        });
    };
    
    // Initial check for elements in viewport
    animateOnScroll();
    
    // Listen for scroll events
    window.addEventListener('scroll', animateOnScroll);
    
    // Animate stats with counting effect
    const animateStats = () => {
        const stats = document.querySelectorAll('.stat-value');
        
        stats.forEach(stat => {
            const targetValue = parseInt(stat.getAttribute('data-value'));
            const suffix = stat.getAttribute('data-suffix') || '';
            const duration = 2000; // 2 seconds
            const startTime = Date.now();
            const startValue = 0;
            
            const updateValue = () => {
                const currentTime = Date.now();
                const elapsed = currentTime - startTime;
                
                if (elapsed < duration) {
                    const value = Math.floor(easeOutQuad(elapsed, startValue, targetValue, duration));
                    stat.textContent = value + suffix;
                    requestAnimationFrame(updateValue);
                } else {
                    stat.textContent = targetValue + suffix;
                }
            };
            
            // Easing function for smoother animation
            const easeOutQuad = (t, b, c, d) => {
                t /= d;
                return -c * t * (t - 2) + b;
            };
            
            // Start animation when stat comes into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateValue();
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(stat);
        });
    };
    
    // Add pulse effect to pricing tables on hover
    const pricingTables = document.querySelectorAll('.pricing-table-item');
    pricingTables.forEach(table => {
        table.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.pricing-title');
            if (icon) {
                icon.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1)';
                }, 300);
            }
        });
    });
    
    // Initialize animations when comparison section is in view
    const comparisonSection = document.querySelector('.comparison-pricing-section');
    if (comparisonSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateStats();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });
        
        observer.observe(comparisonSection);
    }
});
