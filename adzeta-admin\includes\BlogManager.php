<?php
/**
 * Professional Blog Manager Class
 * Integrates caching, templates, and admin panel
 */

class BlogManager {
    private $pdo;
    private $cacheDir;
    private $cacheTime;
    private $templateDir;
    
    public function __construct($pdo, $cacheDir = '../cache/blog/', $cacheTime = 300) {
        $this->pdo = $pdo;
        $this->cacheDir = $cacheDir;
        $this->cacheTime = $cacheTime;
        $this->templateDir = '../templates/blog/';
        
        // Create directories if they don't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        if (!is_dir($this->templateDir)) {
            mkdir($this->templateDir, 0755, true);
        }
    }
    
    /**
     * Get blog posts with intelligent caching
     */
    public function getPosts($page = 1, $postsPerPage = 8, $filters = []) {
        $cacheKey = $this->generateCacheKey('posts', $page, $filters);
        
        // Try cache first
        $cached = $this->getFromCache($cacheKey);
        if ($cached !== false && empty($filters['search'])) {
            return $cached;
        }
        
        // Get fresh data
        $data = $this->getPostsFromDatabase($page, $postsPerPage, $filters);
        
        // Cache if not search
        if (empty($filters['search'])) {
            $this->saveToCache($cacheKey, $data);
        }
        
        return $data;
    }
    
    /**
     * Get single post with caching
     */
    public function getPost($slug) {
        $cacheKey = $this->generateCacheKey('post', $slug);
        
        // Try cache first
        $cached = $this->getFromCache($cacheKey);
        if ($cached !== false) {
            return $cached;
        }
        
        // Get fresh data
        $data = $this->getPostFromDatabase($slug);
        
        // Cache the result
        if ($data) {
            $this->saveToCache($cacheKey, $data);
        }
        
        return $data;
    }
    
    /**
     * Clear cache when posts are updated (called from admin)
     */
    public function clearCache($postId = null) {
        if ($postId) {
            // Clear specific post cache
            $post = $this->getPostFromDatabase(null, $postId);
            if ($post) {
                $cacheKey = $this->generateCacheKey('post', $post['slug']);
                $this->deleteFromCache($cacheKey);
            }
        }
        
        // Clear all posts listing cache
        $files = glob($this->cacheDir . 'posts_*.json');
        foreach ($files as $file) {
            unlink($file);
        }
        
        // Clear categories cache
        $this->deleteFromCache('categories');
        
        return true;
    }
    
    /**
     * Get cache statistics for admin panel
     */
    public function getCacheStats() {
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'cache_hits' => $this->getCacheHits(),
            'last_cleared' => $this->getLastClearTime()
        ];
        
        if (is_dir($this->cacheDir)) {
            $files = glob($this->cacheDir . '*.json');
            $stats['total_files'] = count($files);
            
            foreach ($files as $file) {
                $stats['total_size'] += filesize($file);
            }
        }
        
        return $stats;
    }
    
    /**
     * Render blog list with template
     */
    public function renderBlogList($page = 1, $filters = []) {
        $data = $this->getPosts($page, 8, $filters);
        $categories = $this->getCategories();
        
        // Load template
        $template = $this->loadTemplate('blog-list');
        
        // Replace template variables
        $html = $this->renderTemplate($template, [
            'posts' => $data['posts'],
            'pagination' => $data['pagination'],
            'categories' => $categories,
            'filters' => $filters,
            'cache_info' => $data['from_cache'] ? 'Cached' : 'Fresh'
        ]);
        
        return $html;
    }
    
    /**
     * Render single post with template
     */
    public function renderPost($slug) {
        $post = $this->getPost($slug);
        if (!$post) {
            return false;
        }
        
        $relatedPosts = $this->getRelatedPosts($post['id'], $post['category_id']);
        
        // Load template
        $template = $this->loadTemplate('blog-post');
        
        // Replace template variables
        $html = $this->renderTemplate($template, [
            'post' => $post,
            'related_posts' => $relatedPosts,
            'meta_tags' => $this->generateMetaTags($post)
        ]);
        
        return $html;
    }
    
    // Private helper methods
    private function getPostsFromDatabase($page, $postsPerPage, $filters) {
        $offset = ($page - 1) * $postsPerPage;
        
        // Build conditions
        $conditions = ["p.status = 'published'"];
        $params = [];
        
        if (!empty($filters['search'])) {
            $conditions[] = "(p.title LIKE :search OR p.excerpt LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($filters['category'])) {
            $conditions[] = "p.category_id = :category";
            $params['category'] = $filters['category'];
        }
        
        $whereClause = implode(' AND ', $conditions);
        
        // Optimized query
        $sql = "SELECT 
                    p.id, p.title, p.slug, p.excerpt, p.content,
                    p.featured_image, p.published_at, p.reading_time,
                    p.meta_title, p.meta_description,
                    CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as author_name,
                    COALESCE(c.name, 'Uncategorized') as category_name,
                    COUNT(*) OVER() as total_posts
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE $whereClause
                ORDER BY p.published_at DESC
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->pdo->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $postsPerPage, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $totalPosts = !empty($posts) ? $posts[0]['total_posts'] : 0;
        
        return [
            'posts' => $posts,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($totalPosts / $postsPerPage),
                'total_posts' => $totalPosts,
                'posts_per_page' => $postsPerPage
            ],
            'from_cache' => false
        ];
    }
    
    private function getPostFromDatabase($slug, $id = null) {
        $condition = $id ? "p.id = :identifier" : "p.slug = :identifier";
        
        $sql = "SELECT p.*, 
                       CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as author_name,
                       u.bio as author_bio,
                       c.name as category_name
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE $condition AND p.status = 'published'";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue(':identifier', $id ?: $slug);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getCategories() {
        $cacheKey = 'categories';
        $cached = $this->getFromCache($cacheKey);
        if ($cached !== false) {
            return $cached;
        }
        
        $sql = "SELECT c.id, c.name, c.slug,
                       COUNT(p.id) as post_count
                FROM blog_categories c
                LEFT JOIN blog_posts p ON c.id = p.category_id AND p.status = 'published'
                GROUP BY c.id, c.name, c.slug
                HAVING post_count > 0
                ORDER BY c.name";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $this->saveToCache($cacheKey, $categories);
        return $categories;
    }
    
    private function generateCacheKey($type, ...$params) {
        // Convert arrays to strings safely
        $stringParams = [];
        foreach ($params as $param) {
            if (is_array($param)) {
                $stringParams[] = serialize($param);
            } else {
                $stringParams[] = (string)$param;
            }
        }
        return $type . '_' . md5(implode('_', $stringParams));
    }
    
    private function getFromCache($key) {
        $file = $this->cacheDir . $key . '.json';
        
        if (file_exists($file)) {
            $age = time() - filemtime($file);
            if ($age < $this->cacheTime) {
                $data = json_decode(file_get_contents($file), true);
                if ($data) {
                    $data['from_cache'] = true;
                    return $data;
                }
            }
        }
        
        return false;
    }
    
    private function saveToCache($key, $data) {
        $file = $this->cacheDir . $key . '.json';
        $data['cached_at'] = time();
        file_put_contents($file, json_encode($data));
    }
    
    private function deleteFromCache($key) {
        $file = $this->cacheDir . $key . '.json';
        if (file_exists($file)) {
            unlink($file);
        }
    }
    
    private function getCacheHits() {
        // Simple cache hit counter (you could implement this with a file or database)
        return rand(85, 95); // Mock data for now
    }
    
    private function getLastClearTime() {
        $logFile = $this->cacheDir . 'last_clear.txt';
        if (file_exists($logFile)) {
            return file_get_contents($logFile);
        }
        return 'Never';
    }
    
    private function loadTemplate($templateName) {
        $file = $this->templateDir . $templateName . '.php';
        if (file_exists($file)) {
            return file_get_contents($file);
        }
        
        // Return basic template if file doesn't exist
        return $this->getDefaultTemplate($templateName);
    }
    
    private function renderTemplate($template, $vars) {
        // Simple template variable replacement
        foreach ($vars as $key => $value) {
            if (is_string($value)) {
                $template = str_replace('{{' . $key . '}}', $value, $template);
            }
        }
        
        return $template;
    }
    
    private function getDefaultTemplate($templateName) {
        // Return basic templates if files don't exist
        if ($templateName === 'blog-list') {
            return '<div class="blog-posts">{{posts}}</div>';
        } elseif ($templateName === 'blog-post') {
            return '<article class="blog-post">{{post}}</article>';
        }
        
        return '<div>{{content}}</div>';
    }
    
    private function generateMetaTags($post) {
        return [
            'title' => $post['meta_title'] ?: $post['title'],
            'description' => $post['meta_description'] ?: $post['excerpt'],
            'og_title' => $post['og_title'] ?: $post['title'],
            'og_description' => $post['og_description'] ?: $post['excerpt'],
            'og_image' => $post['og_image'] ?: $post['featured_image']
        ];
    }
    
    public function getRelatedPosts($currentId, $categoryId, $limit = 3) {
        $sql = "SELECT id, title, slug, excerpt, featured_image, published_at
                FROM blog_posts 
                WHERE status = 'published' 
                AND id != :current_id 
                AND category_id = :category_id
                ORDER BY published_at DESC 
                LIMIT :limit";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue(':current_id', $currentId);
        $stmt->bindValue(':category_id', $categoryId);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
