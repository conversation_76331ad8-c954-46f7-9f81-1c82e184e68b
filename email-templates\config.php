<?php
/**
 * Email Configuration for Form Handlers
 * Centralized configuration for all form email functionality
 */

// Email settings
define('ADMIN_EMAIL', '<EMAIL>');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'AdZeta Website');
define('SITE_URL', 'https://designgear.in/adzeta/');

// Security settings
define('ENABLE_CSRF', true);
define('ENABLE_RATE_LIMITING', true);
define('MAX_SUBMISSIONS_PER_HOUR', 10);

// Logging settings
define('ENABLE_LOGGING', true);
define('LOG_FILE', '../logs/form_submissions.log');

// Email templates
function getEmailTemplate($type, $data) {
    $baseStyle = "
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .email-container { max-width: 600px; margin: 0 auto; background: #ffffff; }
            .header { background: linear-gradient(135deg, #de347f, #ff5d74); color: white; padding: 30px 20px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
            .header p { margin: 5px 0 0 0; opacity: 0.9; }
            .content { padding: 30px 20px; }
            .field-group { margin-bottom: 25px; }
            .field-group h3 { color: #de347f; margin-bottom: 15px; font-size: 18px; border-bottom: 2px solid #f0f0f0; padding-bottom: 8px; }
            .field { margin-bottom: 12px; display: flex; }
            .field-label { font-weight: 600; color: #555; min-width: 140px; }
            .field-value { color: #333; }
            .field-value a { color: #de347f; text-decoration: none; }
            .priority-badge { background: #de347f; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #e9ecef; }
            .next-steps { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .next-steps h4 { color: #de347f; margin-bottom: 15px; }
            .next-steps ul { margin: 0; padding-left: 20px; }
            .next-steps li { margin-bottom: 8px; }
        </style>
    ";
    
    switch ($type) {
        case 'free_audit':
            return "
            <html>
            <head>
                <title>New Free Audit Request</title>
                {$baseStyle}
            </head>
            <body>
                <div class='email-container'>
                    <div class='header'>
                        <h1>🎯 New ValueGap Audit Request</h1>
                        <p>High-priority lead from AdZeta website</p>
                    </div>
                    
                    <div class='content'>
                        <div class='field-group'>
                            <h3>👤 Contact Information</h3>
                            <div class='field'><span class='field-label'>Name:</span><span class='field-value'>{$data['first_name']} {$data['last_name']}</span></div>
                            <div class='field'><span class='field-label'>Email:</span><span class='field-value'><a href='mailto:{$data['work_email']}'>{$data['work_email']}</a></span></div>
                            <div class='field'><span class='field-label'>Phone:</span><span class='field-value'>" . ($data['phone_number'] ?: 'Not provided') . "</span></div>
                        </div>
                        
                        <div class='field-group'>
                            <h3>🏢 Company Details</h3>
                            <div class='field'><span class='field-label'>Company:</span><span class='field-value'>{$data['company_name']}</span></div>
                            <div class='field'><span class='field-label'>Website:</span><span class='field-value'><a href='{$data['website_url']}' target='_blank'>{$data['website_url']}</a></span></div>
                            <div class='field'><span class='field-label'>Ad Spend:</span><span class='field-value'><span class='priority-badge'>{$data['monthly_ad_spend']}</span></span></div>
                        </div>
                        
                        <div class='field-group'>
                            <h3>💡 Additional Information</h3>
                            <div class='field'><span class='field-label'>Challenge:</span><span class='field-value'>" . ($data['marketing_challenge'] ?: 'Not specified') . "</span></div>
                            <div class='field'><span class='field-label'>Source:</span><span class='field-value'>" . ($data['lead_source'] ?: 'website_free_audit') . "</span></div>
                        </div>
                        
                        <div class='next-steps'>
                            <h4>🚀 Recommended Next Steps</h4>
                            <ul>
                                <li><strong>Priority:</strong> High - Free audit request (qualified lead)</li>
                                <li><strong>Timeline:</strong> Contact within 2-4 hours for best conversion</li>
                                <li><strong>Approach:</strong> Reference their specific ad spend tier and website</li>
                                <li><strong>Value Prop:</strong> Focus on ValueGap analysis and LTV optimization</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class='footer'>
                        <p><strong>Submission Details</strong></p>
                        <p>Submitted: {$data['submitted_at']} | IP: {$data['ip_address']}</p>
                        <p>Form: Free ValueGap Audit | Source: AdZeta Website</p>
                    </div>
                </div>
            </body>
            </html>
            ";
            
        case 'homepage_cta':
            return "
            <html>
            <head>
                <title>New Expert Consultation Request</title>
                {$baseStyle}
            </head>
            <body>
                <div class='email-container'>
                    <div class='header'>
                        <h1>💬 Expert Consultation Request</h1>
                        <p>Homepage CTA conversion - High intent lead</p>
                    </div>
                    
                    <div class='content'>
                        <div class='field-group'>
                            <h3>👤 Contact Information</h3>
                            <div class='field'><span class='field-label'>Name:</span><span class='field-value'>" . ($data['name'] ?: 'Not provided') . "</span></div>
                            <div class='field'><span class='field-label'>Email:</span><span class='field-value'><a href='mailto:{$data['email']}'>{$data['email']}</a></span></div>
                        </div>
                        
                        <div class='field-group'>
                            <h3>📊 Lead Context</h3>
                            <div class='field'><span class='field-label'>CTA Location:</span><span class='field-value'><span class='priority-badge'>{$data['cta_location']}</span></span></div>
                            <div class='field'><span class='field-label'>Intent Level:</span><span class='field-value'>High (clicked main CTA)</span></div>
                            <div class='field'><span class='field-label'>Request Type:</span><span class='field-value'>Talk to an Expert</span></div>
                        </div>
                        
                        <div class='next-steps'>
                            <h4>🎯 Recommended Action Plan</h4>
                            <ul>
                                <li><strong>Priority:</strong> Very High - Homepage CTA conversion</li>
                                <li><strong>Response Time:</strong> Within 1-2 hours (strike while hot)</li>
                                <li><strong>Approach:</strong> Direct, consultative - they want to talk to an expert</li>
                                <li><strong>Focus:</strong> Understand their growth challenges and position AdZeta AI</li>
                                <li><strong>Next Step:</strong> Schedule discovery call or demo</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class='footer'>
                        <p><strong>Submission Details</strong></p>
                        <p>Submitted: {$data['submitted_at']} | IP: {$data['ip_address']}</p>
                        <p>Form: Homepage CTA | Source: AdZeta Website</p>
                    </div>
                </div>
            </body>
            </html>
            ";
            
        default:
            return '';
    }
}

// Rate limiting function
function checkRateLimit($ip) {
    if (!ENABLE_RATE_LIMITING) return true;
    
    $logFile = LOG_FILE;
    if (!file_exists($logFile)) return true;
    
    $oneHourAgo = time() - 3600;
    $submissions = 0;
    
    $lines = file($logFile, FILE_IGNORE_NEW_LINES);
    foreach ($lines as $line) {
        if (strpos($line, $ip) !== false) {
            $timestamp = strtotime(substr($line, 0, 19));
            if ($timestamp > $oneHourAgo) {
                $submissions++;
            }
        }
    }
    
    return $submissions < MAX_SUBMISSIONS_PER_HOUR;
}

// Logging function
function logSubmission($data) {
    if (!ENABLE_LOGGING) return;
    
    $logDir = dirname(LOG_FILE);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = date('Y-m-d H:i:s') . " - " . $data['form_type'] . " - " . $data['ip_address'] . " - " . json_encode($data) . "\n";
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// Send email function
function sendFormEmail($to, $subject, $content, $replyTo = null) {
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
        'X-Mailer: PHP/' . phpversion(),
        'X-Priority: 1',
        'Importance: High'
    ];
    
    if ($replyTo) {
        $headers[] = 'Reply-To: ' . $replyTo;
    }
    
    return mail($to, $subject, $content, implode("\r\n", $headers));
}
?>
