<?php

namespace AdZetaAdmin\API;

use AdZetaAdmin\Core\JWTAuth;

/**
 * Authentication API Controller
 */
class AuthController extends BaseController
{
    protected $authService;

    public function __construct()
    {
        parent::__construct();
        $this->authService = new JWTAuth();
    }

    /**
     * Login endpoint
     */
    public function login()
    {
        $data = $this->getRequestData();
        
        if (!isset($data['username']) || !isset($data['password'])) {
            return $this->error('Username and password are required', 400);
        }

        $result = $this->authService->login($data['username'], $data['password']);
        
        if ($result['success']) {
            return $this->success([
                'message' => 'Login successful',
                'token' => $result['token'],
                'user' => $result['user'],
                'expires_in' => ADZETA_JWT_EXPIRY
            ]);
        }

        return $this->error($result['message'], 401);
    }

    /**
     * Logout endpoint
     */
    public function logout()
    {
        $this->authService->logout();
        
        return $this->success([
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Verify token endpoint
     */
    public function verify()
    {
        $data = $this->getRequestData();
        
        if (!isset($data['token'])) {
            return $this->error('Token is required', 400);
        }

        $payload = $this->authService->verifyToken($data['token']);
        
        if ($payload) {
            // Get fresh user data
            global $admin_db;
            $user = $admin_db->fetch(
                "SELECT id, username, email, first_name, last_name, role, avatar 
                 FROM users WHERE id = ? AND status = 'active'",
                [$payload['user_id']]
            );

            if ($user) {
                return $this->success([
                    'valid' => true,
                    'user' => $user,
                    'payload' => $payload
                ]);
            }
        }

        return $this->error('Invalid token', 401);
    }

    /**
     * Refresh token endpoint
     */
    public function refresh()
    {
        $data = $this->getRequestData();
        
        if (!isset($data['token'])) {
            return $this->error('Token is required', 400);
        }

        $payload = $this->authService->verifyToken($data['token']);
        
        if ($payload) {
            // Get user data
            global $admin_db;
            $user = $admin_db->fetch(
                "SELECT id, username, email, first_name, last_name, role, status 
                 FROM users WHERE id = ? AND status = 'active'",
                [$payload['user_id']]
            );

            if ($user) {
                // Generate new token
                $newToken = $this->authService->generateToken($user);
                
                return $this->success([
                    'message' => 'Token refreshed',
                    'token' => $newToken,
                    'user' => $user,
                    'expires_in' => ADZETA_JWT_EXPIRY
                ]);
            }
        }

        return $this->error('Invalid token', 401);
    }
}
