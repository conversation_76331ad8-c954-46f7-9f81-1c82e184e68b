<?php

namespace AdZetaAdmin\API;

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../Frontend/TemplateEngine.php';

/**
 * Template Management Controller
 * Handles template selection and configuration
 */
class TemplateController extends BaseController {
    private $templateEngine;

    public function __construct() {
        // Initialize parent (BaseController) properly
        parent::__construct();
        $this->templateEngine = new \AdZetaAdmin\Frontend\TemplateEngine();
    }

    /**
     * Get available templates
     */
    public function getTemplates() {
        try {
            $templates = $this->templateEngine->getAvailableTemplates();

            return $this->success([
                'templates' => $templates,
                'current_settings' => $this->templateEngine->loadTemplateSettings()
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to load templates: ' . $e->getMessage());
        }
    }

    /**
     * Get templates for specific content type
     */
    public function getTemplatesByType() {
        try {
            $type = $_GET['type'] ?? 'blog-post';

            $templates = $this->templateEngine->getAvailableTemplates($type);
            $currentSettings = $this->templateEngine->loadTemplateSettings();

            return $this->success([
                'type' => $type,
                'templates' => $templates,
                'current_template' => $currentSettings[$type] ?? null
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to load templates for type: ' . $e->getMessage());
        }
    }

    /**
     * Save template settings
     */
    public function saveTemplateSettings() {
        try {
            $input = $this->getRequestData();

            if (empty($input)) {
                return $this->error('No template settings provided', 400);
            }

            // Validate template settings
            $validTypes = ['blog-post', 'blog-list', 'blog-category', 'blog-tag'];
            $availableTemplates = $this->templateEngine->getAvailableTemplates();

            foreach ($input as $type => $template) {
                if (!in_array($type, $validTypes)) {
                    return $this->error("Invalid template type: {$type}", 400);
                }

                if (!isset($availableTemplates[$type][$template])) {
                    return $this->error("Invalid template '{$template}' for type '{$type}'", 400);
                }
            }

            // Save settings
            $result = $this->templateEngine->saveTemplateSettings($input);

            if (!$result) {
                throw new \Exception('Failed to save template settings');
            }

            // Clear router cache since templates changed
            $this->clearRouterCache();

            return $this->success([
                'message' => 'Template settings saved successfully',
                'settings' => $input,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to save template settings: ' . $e->getMessage());
        }
    }

    /**
     * Get template preview
     */
    public function getTemplatePreview() {
        try {
            $type = $_GET['type'] ?? '';
            $template = $_GET['template'] ?? '';

            if (empty($type) || empty($template)) {
                return $this->error('Type and template parameters required', 400);
            }

            $previewUrl = $this->templateEngine->getTemplatePreview($type, $template);

            if (!$previewUrl) {
                return $this->error('Template preview not found', 404);
            }

            return $this->success([
                'preview_url' => $previewUrl,
                'type' => $type,
                'template' => $template
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to get template preview: ' . $e->getMessage());
        }
    }

    /**
     * Set template for specific post
     */
    public function setPostTemplate() {
        try {
            $input = $this->getRequestData();

            $postId = $input['post_id'] ?? null;
            $template = $input['template'] ?? null;

            if (!$postId || !$template) {
                return $this->error('Post ID and template are required', 400);
            }

            // Validate template exists
            $availableTemplates = $this->templateEngine->getAvailableTemplates('blog-post');
            if (!isset($availableTemplates[$template])) {
                return $this->error('Invalid template: ' . $template, 400);
            }

            // Update post template in database
            require_once __DIR__ . '/../../includes/BlogDatabase.php';
            $db = getBlogDatabase();

            $result = $db->updatePostTemplate($postId, $template);

            if (!$result) {
                throw new \Exception('Failed to update post template in database');
            }

            // Clear cache for this post
            $this->clearPostCache($postId);

            return $this->success([
                'message' => 'Post template updated successfully',
                'post_id' => $postId,
                'template' => $template
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to set post template: ' . $e->getMessage());
        }
    }

    /**
     * Get template selector HTML for admin
     */
    public function getTemplateSelector() {
        try {
            $type = $_GET['type'] ?? 'blog-post';
            $currentTemplate = $_GET['current'] ?? null;

            $html = $this->templateEngine->renderTemplateSelector($type, $currentTemplate);

            return $this->success([
                'html' => $html,
                'type' => $type,
                'current_template' => $currentTemplate
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to generate template selector: ' . $e->getMessage());
        }
    }

    /**
     * Clear router cache
     */
    private function clearRouterCache() {
        try {
            require_once __DIR__ . '/../Frontend/Router.php';
            $router = new \AdZetaAdmin\Frontend\Router();
            $router->clearCache('*');
        } catch (\Exception $e) {
            // Log but don't fail
            error_log('Failed to clear router cache: ' . $e->getMessage());
        }
    }

    /**
     * Clear cache for specific post
     */
    private function clearPostCache($postId) {
        try {
            // Get post slug
            require_once __DIR__ . '/../../includes/BlogDatabase.php';
            $db = getBlogDatabase();
            $post = $db->getBlogPostById($postId);

            if ($post && $post['slug']) {
                require_once __DIR__ . '/../Frontend/Router.php';
                $router = new \AdZetaAdmin\Frontend\Router();
                $router->clearCache('blog-post-' . $post['slug']);
            }
        } catch (\Exception $e) {
            // Log but don't fail
            error_log('Failed to clear post cache: ' . $e->getMessage());
        }
    }

    /**
     * Get template statistics
     */
    public function getTemplateStats() {
        try {
            // Get template usage statistics
            require_once __DIR__ . '/../../includes/BlogDatabase.php';
            $db = getBlogDatabase();

            $stats = [
                'total_posts' => 0,
                'template_usage' => [],
                'default_templates' => $this->templateEngine->loadTemplateSettings()
            ];

            // Get template usage from posts
            $posts = $db->getAllBlogPosts(['status' => 'published']);
            $stats['total_posts'] = count($posts);

            foreach ($posts as $post) {
                $template = $post['template'] ?? 'professional-article';
                $stats['template_usage'][$template] = ($stats['template_usage'][$template] ?? 0) + 1;
            }

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->error('Failed to get template statistics: ' . $e->getMessage());
        }
    }
}
