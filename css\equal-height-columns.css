
.getting-started-section {
background: linear-gradient(to bottom, #F8F7F6 0%, #ffffff 100%);
}

.getting-started-journey {
    display: flex;
    flex-direction: column;
    gap: 30px;
    max-width: 900px;
    margin: 0 auto;
}

.journey-step {
    display: flex;
    align-items: flex-start;
    background-color: #fff;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.journey-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
    border-left: 3px solid #e958a1;
}

.step-icon-container {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1), rgba(255, 93, 116, 0.05));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-icon-container i {
    font-size: 24px;
    color: #e958a1;
}

.step-number {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    color: white;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(233, 88, 161, 0.3);
}

.step-content {
    flex: 1;
}

.step-content h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 18px;
}

.step-content p {
    margin-bottom: 0;
    color: #555;
    font-size: 15px;
    line-height: 1.5;
}

@media (max-width: 767px) {
    .journey-step {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px 15px;
    }

    .step-icon-container {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .step-content h4 {
        font-size: 16px;
    }

    .step-content p {
        font-size: 14px;
    }
}

.scale-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f0f0f0' fill-opacity='0.4' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
    pointer-events: none;
}

.scale-section .container {
    position: relative;
    z-index: 1;
}

.scale-section .row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.scale-section .row:last-child {
    margin-bottom: 0;
}

.scale-section .row > [class*='col-'] {
    display: flex;
    flex-direction: column;
}

#accordion-scale {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.scale-metrics-container {
    height: 100%;
}

.scale-metrics-container .row {
    height: 100%;
    margin-bottom: 20px;
}

.scale-metrics-container .row:last-child {
    margin-bottom: 0;
}

.apple-style-visualization {
    margin-bottom: 20px;
    border-radius: 16px;
    overflow: hidden;
    background-color: #ffffff;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

@media (max-width: 991px) {
    .scale-section .row > [class*='col-'] {
        height: auto;
    }

    .scale-section {
        padding-bottom: 60px;
    }
}

@media (max-width: 767px) {
    .scale-section {
        padding-bottom: 40px;
    }
}
