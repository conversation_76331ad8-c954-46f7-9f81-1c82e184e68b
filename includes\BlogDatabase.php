<?php
/**
 * Blog Database Helper
 * Provides database connection and blog-specific functions for frontend
 */

// Prevent direct access
if (!defined('ADZETA_INIT')) {
    die('Direct access not permitted');
}

class BlogDatabase {
    private static $instance = null;
    private $pdo;

    private function __construct() {
        try {
            // Use the same database configuration as admin panel
            $dsn = "mysql:host=localhost;dbname=adzetadb;charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, 'adzetauser', 'Crazy1395#', $options);

            // Test connection
            $this->pdo->query("SELECT 1");

        } catch (PDOException $e) {
            error_log("Blog database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        return $this->pdo;
    }

    /**
     * Get published blog posts with pagination
     */
    public function getBlogPosts($options = []) {
        $limit = $options['limit'] ?? 12;
        $page = $options['page'] ?? 1;
        $category = $options['category'] ?? null;
        $tag = $options['tag'] ?? null;
        $search = $options['search'] ?? null;
        $offset = ($page - 1) * $limit;

        $sql = "SELECT p.*, u.first_name, u.last_name, c.name as category_name, c.slug as category_slug
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE p.status = 'published'";

        $params = [];

        // Add category filter
        if ($category) {
            $sql .= " AND c.slug = :category";
            $params['category'] = $category;
        }

        // Add tag filter
        if ($tag) {
            $sql .= " AND p.id IN (
                SELECT pt.post_id FROM post_tags pt
                JOIN blog_tags t ON pt.tag_id = t.id
                WHERE t.slug = :tag
            )";
            $params['tag'] = $tag;
        }

        // Add search filter
        if ($search) {
            $sql .= " AND (p.title LIKE :search OR p.content LIKE :search OR p.excerpt LIKE :search)";
            $params['search'] = '%' . $search . '%';
        }

        $sql .= " ORDER BY p.published_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->pdo->prepare($sql);

        // Bind parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();
        $posts = $stmt->fetchAll();

        // Get tags for each post
        foreach ($posts as &$post) {
            $post['tags'] = $this->getPostTags($post['id']);

            // Decode content blocks if they exist
            if ($post['content_blocks']) {
                $post['content_blocks'] = json_decode($post['content_blocks'], true);
            }
        }

        return $posts;
    }

    /**
     * Get total count of published posts (for pagination)
     */
    public function getBlogPostsCount($options = []) {
        $category = $options['category'] ?? null;
        $tag = $options['tag'] ?? null;
        $search = $options['search'] ?? null;

        $sql = "SELECT COUNT(*) FROM blog_posts p
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE p.status = 'published'";

        $params = [];

        if ($category) {
            $sql .= " AND c.slug = :category";
            $params['category'] = $category;
        }

        if ($tag) {
            $sql .= " AND p.id IN (
                SELECT pt.post_id FROM post_tags pt
                JOIN blog_tags t ON pt.tag_id = t.id
                WHERE t.slug = :tag
            )";
            $params['tag'] = $tag;
        }

        if ($search) {
            $sql .= " AND (p.title LIKE :search OR p.content LIKE :search OR p.excerpt LIKE :search)";
            $params['search'] = '%' . $search . '%';
        }

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn();
    }

    /**
     * Get a single blog post by slug
     */
    public function getBlogPostBySlug($slug) {
        $sql = "SELECT p.*,
                       u.first_name, u.last_name, u.name as author_name, u.avatar as author_avatar, u.bio as author_bio, u.role as author_role,
                       c.name as category_name, c.slug as category_slug
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE p.slug = :slug AND p.status = 'published'";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['slug' => $slug]);

        $post = $stmt->fetch();

        if ($post) {
            // Get tags
            $post['tags'] = $this->getPostTags($post['id']);

            // Decode content blocks
            if ($post['content_blocks']) {
                $post['content_blocks'] = json_decode($post['content_blocks'], true);
            }

            // Increment view count
            $this->incrementViewCount($post['id']);
        }

        return $post;
    }

    /**
     * Get tags for a specific post
     */
    public function getPostTags($postId) {
        $sql = "SELECT t.* FROM blog_tags t
                JOIN post_tags pt ON t.id = pt.tag_id
                WHERE pt.post_id = :post_id
                ORDER BY t.name";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['post_id' => $postId]);

        return $stmt->fetchAll();
    }

    /**
     * Get all categories with post counts
     */
    public function getCategories() {
        $sql = "SELECT c.*, COUNT(p.id) as post_count
                FROM blog_categories c
                LEFT JOIN blog_posts p ON c.id = p.category_id AND p.status = 'published'
                GROUP BY c.id
                ORDER BY c.name";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Get all tags with post counts
     */
    public function getTags() {
        $sql = "SELECT t.*, COUNT(pt.post_id) as post_count
                FROM blog_tags t
                LEFT JOIN post_tags pt ON t.id = pt.tag_id
                LEFT JOIN blog_posts p ON pt.post_id = p.id AND p.status = 'published'
                GROUP BY t.id
                ORDER BY t.name";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Get related posts based on category and tags
     */
    public function getRelatedPosts($postId, $categoryId, $limit = 4) {
        try {
            // Simplified query to avoid parameter conflicts
            $sql = "SELECT DISTINCT p.*, u.first_name, u.last_name, c.name as category_name
                    FROM blog_posts p
                    LEFT JOIN users u ON p.author_id = u.id
                    LEFT JOIN blog_categories c ON p.category_id = c.id
                    WHERE p.status = 'published'
                    AND p.id != ?
                    AND p.category_id = ?
                    ORDER BY p.published_at DESC
                    LIMIT ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$postId, $categoryId, $limit]);

            $results = $stmt->fetchAll();

            // If we don't have enough results, get more from other categories
            if (count($results) < $limit) {
                $remaining = $limit - count($results);
                $excludeIds = array_column($results, 'id');
                $excludeIds[] = $postId;

                $placeholders = str_repeat('?,', count($excludeIds) - 1) . '?';

                $sql2 = "SELECT p.*, u.first_name, u.last_name, c.name as category_name
                        FROM blog_posts p
                        LEFT JOIN users u ON p.author_id = u.id
                        LEFT JOIN blog_categories c ON p.category_id = c.id
                        WHERE p.status = 'published'
                        AND p.id NOT IN ($placeholders)
                        ORDER BY p.published_at DESC
                        LIMIT ?";

                $stmt2 = $this->pdo->prepare($sql2);
                $params = array_merge($excludeIds, [$remaining]);
                $stmt2->execute($params);

                $additionalResults = $stmt2->fetchAll();
                $results = array_merge($results, $additionalResults);
            }

            return $results;

        } catch (PDOException $e) {
            error_log("Error in getRelatedPosts: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Increment view count for a post
     */
    private function incrementViewCount($postId) {
        $sql = "UPDATE blog_posts SET view_count = view_count + 1 WHERE id = :id";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['id' => $postId]);
    }

    /**
     * Search posts
     */
    public function searchPosts($query, $limit = 10) {
        $sql = "SELECT p.*, u.first_name, u.last_name, c.name as category_name
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE p.status = 'published'
                AND (p.title LIKE :query OR p.content LIKE :query OR p.excerpt LIKE :query)
                ORDER BY p.published_at DESC
                LIMIT :limit";

        $stmt = $this->pdo->prepare($sql);
        $stmt->bindValue(':query', '%' . $query . '%');
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }
}

// Helper functions for easy access
function getBlogDatabase() {
    return BlogDatabase::getInstance();
}

function getBlogPosts($options = []) {
    return getBlogDatabase()->getBlogPosts($options);
}

function getBlogPostBySlug($slug) {
    return getBlogDatabase()->getBlogPostBySlug($slug);
}

function getBlogCategories() {
    return getBlogDatabase()->getCategories();
}

function getBlogTags() {
    return getBlogDatabase()->getTags();
}
?>
