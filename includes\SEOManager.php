<?php
/**
 * SEO Management Class
 * Handles dynamic SEO meta tags, Open Graph, Twitter Cards, and Schema.org
 */

class SEOManager {
    private $title;
    private $description;
    private $keywords;
    private $canonicalUrl;
    private $ogImage;
    private $ogType = 'website';
    private $twitterCard = 'summary_large_image';
    private $schema = [];
    private $customMeta = [];

    public function __construct($pageData = []) {
        // If no page data provided, try to get from static config
        if (empty($pageData)) {
            $pageData = getPageConfig();
        }

        // Set defaults
        $this->title = $pageData['title'] ?? SITE_NAME;
        $this->description = $pageData['description'] ?? DEFAULT_META_DESCRIPTION;
        $this->keywords = $pageData['keywords'] ?? DEFAULT_META_KEYWORDS;
        $this->canonicalUrl = $pageData['canonical'] ?? $pageData['canonical_url'] ?? $this->getCurrentUrl();
        $this->ogImage = $pageData['og_image'] ?? DEFAULT_OG_IMAGE;
        $this->ogType = $pageData['og_type'] ?? 'website';
        $this->twitterCard = $pageData['twitter_card'] ?? 'summary_large_image';

        // Add blog-specific meta tags
        if (isset($pageData['article_author'])) {
            $this->addCustomMeta('article:author', $pageData['article_author'], true);
        }
        if (isset($pageData['article_published_time'])) {
            $this->addCustomMeta('article:published_time', $pageData['article_published_time'], true);
        }
        if (isset($pageData['article_modified_time'])) {
            $this->addCustomMeta('article:modified_time', $pageData['article_modified_time'], true);
        }

        // Set schema based on content type
        if ($this->ogType === 'article') {
            $this->setArticleSchema($pageData);
        } else {
            $this->setDefaultSchema();
        }
    }

    public function setTitle($title, $appendSiteName = true) {
        $this->title = $appendSiteName ? $title . ' | ' . SITE_NAME : $title;
        return $this;
    }

    public function setDescription($description) {
        $this->description = $description;
        return $this;
    }

    public function setKeywords($keywords) {
        $this->keywords = is_array($keywords) ? implode(', ', $keywords) : $keywords;
        return $this;
    }

    public function setCanonicalUrl($url) {
        $this->canonicalUrl = $url;
        return $this;
    }

    public function setOgImage($image) {
        $this->ogImage = $image;
        return $this;
    }

    public function setOgType($type) {
        $this->ogType = $type;
        return $this;
    }

    public function addCustomMeta($name, $content, $property = false) {
        $this->customMeta[] = [
            'name' => $name,
            'content' => $content,
            'property' => $property
        ];
        return $this;
    }

    public function setSchema($schema) {
        $this->schema = $schema;
        return $this;
    }

    public function addSchema($schema) {
        $this->schema[] = $schema;
        return $this;
    }

    public function renderMetaTags() {
        $html = '';

        // Basic meta tags
        $html .= "<title>{$this->title}</title>\n";
        $html .= "<meta name=\"description\" content=\"{$this->description}\">\n";
        $html .= "<meta name=\"keywords\" content=\"{$this->keywords}\">\n";
        $html .= "<link rel=\"canonical\" href=\"{$this->canonicalUrl}\">\n";

        // Open Graph
        $html .= "<meta property=\"og:type\" content=\"{$this->ogType}\">\n";
        $html .= "<meta property=\"og:url\" content=\"{$this->canonicalUrl}\">\n";
        $html .= "<meta property=\"og:title\" content=\"{$this->title}\">\n";
        $html .= "<meta property=\"og:description\" content=\"{$this->description}\">\n";
        $html .= "<meta property=\"og:image\" content=\"{$this->ogImage}\">\n";

        // Twitter Cards
        $html .= "<meta property=\"twitter:card\" content=\"{$this->twitterCard}\">\n";
        $html .= "<meta property=\"twitter:url\" content=\"{$this->canonicalUrl}\">\n";
        $html .= "<meta property=\"twitter:title\" content=\"{$this->title}\">\n";
        $html .= "<meta property=\"twitter:description\" content=\"{$this->description}\">\n";
        $html .= "<meta property=\"twitter:image\" content=\"{$this->ogImage}\">\n";

        // Custom meta tags
        foreach ($this->customMeta as $meta) {
            $attribute = $meta['property'] ? 'property' : 'name';
            $html .= "<meta {$attribute}=\"{$meta['name']}\" content=\"{$meta['content']}\">\n";
        }

        return $html;
    }

    public function renderSchema() {
        if (empty($this->schema)) {
            return '';
        }

        $schemaJson = json_encode($this->schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        return "<script type=\"application/ld+json\">\n{$schemaJson}\n</script>\n";
    }

    private function setDefaultSchema() {
        $this->schema = [
            "@context" => "https://schema.org",
            "@type" => "Organization",
            "name" => SITE_NAME,
            "url" => SITE_URL,
            "logo" => SITE_URL . "/images/adzeta-logo-black.svg",
            "description" => "AI-powered Value-Based Bidding platform for e-commerce growth and profitability",
            "address" => [
                "@type" => "PostalAddress",
                "addressLocality" => "Austin",
                "addressRegion" => "TX",
                "addressCountry" => "US"
            ],
            "contactPoint" => [
                "@type" => "ContactPoint",
                "telephone" => "******-572-7784",
                "contactType" => "customer service"
            ],
            "sameAs" => [
                "https://www.linkedin.com/company/adzeta",
                "https://twitter.com/adzeta",
                "https://www.facebook.com/adzeta"
            ]
        ];
    }

    private function setArticleSchema($pageData) {
        $this->schema = [
            "@context" => "https://schema.org",
            "@type" => "Article",
            "headline" => $this->title,
            "description" => $this->description,
            "image" => $this->ogImage,
            "author" => [
                "@type" => "Person",
                "name" => $pageData['article_author'] ?? "AdZeta Team"
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => SITE_NAME,
                "logo" => [
                    "@type" => "ImageObject",
                    "url" => SITE_URL . "/images/adzeta-logo-black.svg"
                ]
            ],
            "datePublished" => $pageData['article_published_time'] ?? date('c'),
            "dateModified" => $pageData['article_modified_time'] ?? date('c'),
            "mainEntityOfPage" => [
                "@type" => "WebPage",
                "@id" => $this->canonicalUrl
            ]
        ];
    }

    private function getCurrentUrl() {
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        return $protocol . $host . $uri;
    }
}
