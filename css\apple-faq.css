/* Homepage FAQ styling start*/
.faq-section {
  
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}


.faq-section .text-gradient-pink-blue {
    background: linear-gradient(to right, #f45888, #4a7ab5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.apple-style-faq {
    margin-top: 30px;
}

.faq-item {
    background-color: #ffffff;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column-reverse;
}

.faq-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.faq-header {
    padding: 0;
    border: none;
}

.faq-toggle {
    display: block;
    padding: 0;
    text-decoration: none;
    width: 100%;
}

.faq-title {
    display: flex;
    align-items: center;
    padding: 24px 30px;
    position: relative;
}

.faq-number {
    font-size: 14px;
    font-weight: 600;
    color: #f45888;
    margin-right: 20px;
    opacity: 0.8;
    min-width: 28px;
}

.faq-title h3 {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin: 0;
    flex-grow: 1;
}

.faq-icon {
    position: relative;
    width: 20px;
    height: 20px;
}

.faq-icon span {
    position: absolute;
    background-color: #333;
    transition: all 0.3s ease;
}

.faq-icon span:first-child {
    top: 9px;
    left: 0;
    width: 100%;
    height: 2px;
}

.faq-icon span:last-child {
    top: 0;
    left: 9px;
    width: 2px;
    height: 100%;
}

.faq-toggle[aria-expanded="true"] .faq-icon span:last-child {
    transform: rotate(90deg);
    opacity: 0;
}

.faq-body {
    padding: 0 30px 24px 78px;
    order: 2;
}

.faq-header {
    order: 1;
}

.faq-body p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

.faq-body p strong {
    color: #333;
    font-weight: 500;
}

.faq-item {
    transition: all 0.3s ease;
}

.faq-item.active {
    background-color: #ffffff;
}

@media (max-width: 767px) {
    .faq-title {
        padding: 20px;
    }

    .faq-body {
        padding: 0 20px 20px 60px;
    }

    .faq-number {
        margin-right: 12px;
        min-width: 24px;
    }
}
/* Homepage FAQ styling ends*/