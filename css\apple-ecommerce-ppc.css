/* Apple-Ecommerce PPC Theme - iOS 17 Inspired Design
   This file contains Apple-inspired design elements specifically for the ecommerce-ppc page
   Following Apple's design methodology with clean, practical visuals and consistent brand theming */

/* ===================================
   Global Theme Styles
==================================== */

/* Body class to scope all styles to this page only */
.apple-ecommerce-theme {
    font-family: var(--primary-font);
    color: #1d1d1f;
    overflow-x: hidden;
}

/* ===================================
   Hero Section - Dark iOS 17 Inspired
==================================== */

/* Main hero background with iOS 17 dark mode aesthetic */
.apple-ecommerce-theme .hero-section .professional-gradient-container {
    background: linear-gradient(145deg, #000000 0%, #0A0A14 40%, #1A0A28 70%, #220C32 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
}

/* Corner gradients inspired by Apple's subtle UI accents */
.apple-ecommerce-theme .hero-section .corner-gradient.top-left {
    background: radial-gradient(circle at top left, rgba(10, 132, 255, 0.15) 0%, rgba(28, 28, 30, 0) 70%);
    width: 800px;
    height: 800px;
    filter: blur(80px);
    opacity: 0.7;
}

.apple-ecommerce-theme .hero-section .corner-gradient.top-right {
    background: radial-gradient(circle at top right, rgba(94, 92, 230, 0.12) 0%, rgba(28, 28, 30, 0) 70%);
    width: 800px;
    height: 800px;
    filter: blur(80px);
    opacity: 0.7;
}

.apple-ecommerce-theme .hero-section .corner-gradient.bottom-left {
    background: radial-gradient(circle at bottom left, rgba(255, 55, 95, 0.12) 0%, rgba(28, 28, 30, 0) 70%);
    width: 800px;
    height: 800px;
    filter: blur(80px);
    opacity: 0.7;
}

.apple-ecommerce-theme .hero-section .corner-gradient.bottom-right {
    background: radial-gradient(circle at bottom right, rgba(48, 209, 88, 0.10) 0%, rgba(28, 28, 30, 0) 70%);
    width: 800px;
    height: 800px;
    filter: blur(80px);
    opacity: 0.7;
}

/* Diagonal gradient with iOS accent colors */
.apple-ecommerce-theme .hero-section .diagonal-gradient {
    background: linear-gradient(135deg,
        rgba(10, 132, 255, 0.07) 0%,
        rgba(94, 92, 230, 0.07) 25%,
        rgba(255, 55, 95, 0.07) 50%,
        rgba(48, 209, 88, 0.07) 75%,
        rgba(255, 214, 10, 0.07) 100%);
    opacity: 0.8;
}

/* Mesh overlay with Apple-like noise texture */
.apple-ecommerce-theme .hero-section .mesh-overlay {
    background-image: url('images/noise-texture-dark.png');
    opacity: 0.04;
    mix-blend-mode: overlay;
}

/* Vignette overlay for depth */
.apple-ecommerce-theme .hero-section .vignette-overlay {
    background: radial-gradient(ellipse at center, rgba(0,0,0,0) 50%, rgba(0,0,0,0.5) 100%);
    opacity: 0.7;
}

/* ===================================
   Typography - Apple Inspired
==================================== */

/* Heading styles with Apple-like typography */
.apple-ecommerce-theme h1, 
.apple-ecommerce-theme h2, 
.apple-ecommerce-theme h3, 
.apple-ecommerce-theme h4, 
.apple-ecommerce-theme h5, 
.apple-ecommerce-theme h6 {
    font-family: var(--primary-font);
    letter-spacing: -0.022em;
    line-height: 1.1;
}

/* Main heading with iOS 17 gradient */
.apple-ecommerce-theme .hero-heading {
    font-weight: 600;
    letter-spacing: -0.03em;
    line-height: 1.1;
    color: #ffffff;
}

/* Modern heading gradient - iOS 17 inspired */
.apple-ecommerce-theme .modern-heading-gradient {
    background-image: linear-gradient(166deg, #ffffff 0%, #f5f5f5 40%, #ff8cc6 60%, #df367d 75%, #ee5c46 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    letter-spacing: -0.03em;
    font-weight: 600;
    line-height: 1.1;
    position: relative;
    display: inline-block;
}

/* Section titles with Apple-like styling */
.apple-ecommerce-theme .section-title {
    font-weight: 600;
    letter-spacing: -0.022em;
    color: #1d1d1f;
    margin-bottom: 16px;
}

/* Subtitle styling */
.apple-ecommerce-theme .section-subtitle {
    font-weight: 400;
    color: #86868b;
    letter-spacing: -0.011em;
    line-height: 1.5;
}

/* Text gradient for highlights - iOS accent colors */
.apple-ecommerce-theme .text-gradient-purple {
    background: linear-gradient(to right, #5E5CE6, #0A84FF);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    font-weight: 600;
}

/* ===================================
   Buttons & CTAs - iOS 17 Inspired
==================================== */

/* Primary button with iOS 17 gradient */
.apple-ecommerce-theme .btn-gradient-pink-orange {
    background-image: linear-gradient(to right, #FF375F 0%, #FF453A 100%);
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    font-weight: 600;
    letter-spacing: -0.011em;
    border: none;
    padding: 14px 28px;
    box-shadow: 0 4px 12px rgba(255, 55, 95, 0.2);
}

/* Button hover effect */
.apple-ecommerce-theme .btn-gradient-pink-orange:hover {
    background-image: linear-gradient(to right, #FF453A 0%, #FF375F 100%);
    box-shadow: 0 6px 20px rgba(255, 55, 95, 0.3);
    transform: translateY(-2px);
}

/* Secondary button - iOS 17 style */
.apple-ecommerce-theme .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: #ffffff;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    font-weight: 500;
    letter-spacing: -0.011em;
}

.apple-ecommerce-theme .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

/* ===================================
   Cards & Components - iOS 17 Style
==================================== */

/* Feature box with frosted glass effect */
.apple-ecommerce-theme .platform-feature-box {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    padding: 30px;
}

.apple-ecommerce-theme .platform-feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
}

/* Icon wrapper with iOS accent colors */
.apple-ecommerce-theme .platform-feature-box .icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    background: linear-gradient(135deg, rgba(10, 132, 255, 0.1) 0%, rgba(94, 92, 230, 0.1) 100%);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.apple-ecommerce-theme .platform-feature-box:hover .icon-wrapper {
    background: linear-gradient(135deg, rgba(10, 132, 255, 0.15) 0%, rgba(94, 92, 230, 0.15) 100%);
    transform: scale(1.05);
}

.apple-ecommerce-theme .platform-feature-box .icon-wrapper i {
    font-size: 24px;
    background: linear-gradient(to right, #0A84FF, #5E5CE6);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* Metric cards with iOS 17 styling */
.apple-ecommerce-theme .apple-metric-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    padding: 24px;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.apple-ecommerce-theme .apple-metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
}

.apple-ecommerce-theme .apple-metric-card .metric-value {
    font-size: 42px;
    font-weight: 700;
    letter-spacing: -0.03em;
    line-height: 1;
    margin-bottom: 8px;
}

.apple-ecommerce-theme .apple-metric-card .metric-label {
    font-size: 16px;
    font-weight: 400;
    color: #86868b;
    letter-spacing: -0.011em;
}

.apple-ecommerce-theme .apple-metric-card.positive .metric-value {
    color: #30D158; /* iOS green */
}

.apple-ecommerce-theme .apple-metric-card.reduction .metric-value {
    color: #0A84FF; /* iOS blue */
}

/* ===================================
   Section Backgrounds - iOS Inspired
==================================== */

/* Light section background with subtle gradient */
.apple-ecommerce-theme .light-section {
    background: linear-gradient(to bottom, #F5F5F7 0%, #FFFFFF 100%);
    position: relative;
    overflow: hidden;
}

/* Light section with subtle pattern */
.apple-ecommerce-theme .light-section-pattern {
    background-color: #F5F5F7;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0wIDBoNDB2NDBoLTQweiIvPjxjaXJjbGUgc3Ryb2tlPSIjRTZFNkU2IiBzdHJva2Utd2lkdGg9Ii41IiBjeD0iMjAiIGN5PSIyMCIgcj0iMSIvPjwvZz48L3N2Zz4=');
    position: relative;
    overflow: hidden;
}

/* Dark section background with iOS 17 gradient */
.apple-ecommerce-theme .dark-section {
    background: linear-gradient(145deg, #000000 0%, #0A0A14 40%, #1A0A28 70%, #220C32 100%);
    position: relative;
    overflow: hidden;
    color: #ffffff;
}

/* ===================================
   Hero Animation Elements - iOS Style
==================================== */

/* Animation elements with Apple-like shadows */
.apple-ecommerce-theme .platform-animation-container .animation-element img {
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.15));
    transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.apple-ecommerce-theme .platform-animation-container .animation-element:hover img {
    filter: drop-shadow(0 12px 24px rgba(0, 0, 0, 0.2));
    transform: translateY(-5px) scale(1.02);
}

/* Element labels with iOS styling */
.apple-ecommerce-theme .element-label {
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.05em;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    margin-top: 8px;
    text-align: center;
}

/* Connection lines with iOS-inspired gradients */
.apple-ecommerce-theme .connection-line {
    background: linear-gradient(90deg, rgba(10, 132, 255, 0.7), rgba(94, 92, 230, 0.7));
    height: 2px;
    position: absolute;
    z-index: 1;
}

/* Data particles with iOS accent colors */
.apple-ecommerce-theme .data-particle {
    background: radial-gradient(circle, rgba(10, 132, 255, 0.9) 0%, rgba(94, 92, 230, 0.7) 100%);
    border-radius: 50%;
    position: absolute;
    width: 6px;
    height: 6px;
    z-index: 2;
}

/* ===================================
   Section Tags - iOS 17 Style
==================================== */

/* Section tag container */
.apple-ecommerce-theme .scale-tag-container {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

/* Section tag with iOS gradient */
.apple-ecommerce-theme .scale-tag {
    display: inline-block;
    padding: 6px 16px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    background: linear-gradient(to right, #0A84FF, #5E5CE6);
    color: white;
    border-radius: 20px;
    box-shadow: 0 4px 10px rgba(10, 132, 255, 0.2);
}

/* ===================================
   Testimonials - iOS 17 Style
==================================== */

/* Testimonial card with frosted glass effect */
.apple-ecommerce-theme .simple-testimonial {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    padding: 30px;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.apple-ecommerce-theme .simple-testimonial:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
}

/* Case study link with iOS styling */
.apple-ecommerce-theme .case-study-link {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: #0A84FF;
    text-decoration: none;
    transition: all 0.3s ease;
}

.apple-ecommerce-theme .case-study-link i {
    margin-left: 6px;
    transition: all 0.3s ease;
}

.apple-ecommerce-theme .case-study-link:hover {
    color: #0071E3;
}

.apple-ecommerce-theme .case-study-link:hover i {
    transform: translateX(3px) translateY(-3px);
}

/* ===================================
   Accordions - iOS 17 Style
==================================== */

/* Accordion styling with iOS design principles */
.apple-ecommerce-theme .accordion-style-01 .accordion-item {
    border-radius: 16px;
    margin-bottom: 12px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.apple-ecommerce-theme .accordion-style-01 .accordion-item:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
}

.apple-ecommerce-theme .accordion-style-01 .accordion-header a {
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    color: #1d1d1f;
    text-decoration: none;
    letter-spacing: -0.011em;
}

.apple-ecommerce-theme .accordion-style-01 .accordion-body {
    padding: 0 24px 20px;
    color: #86868b;
    line-height: 1.5;
    letter-spacing: -0.011em;
}

/* Accordion icon */
.apple-ecommerce-theme .accordion-style-01 .accordion-icon {
    width: 20px;
    height: 20px;
    position: relative;
    transition: all 0.3s ease;
}

.apple-ecommerce-theme .accordion-style-01 .accordion-icon:before,
.apple-ecommerce-theme .accordion-style-01 .accordion-icon:after {
    content: '';
    position: absolute;
    background-color: #0A84FF;
    transition: all 0.3s ease;
}

.apple-ecommerce-theme .accordion-style-01 .accordion-icon:before {
    width: 100%;
    height: 2px;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}

.apple-ecommerce-theme .accordion-style-01 .accordion-icon:after {
    width: 2px;
    height: 100%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.apple-ecommerce-theme .accordion-style-01 .accordion-header a[aria-expanded="true"] .accordion-icon:after {
    transform: translateX(-50%) rotate(90deg);
    opacity: 0;
}

/* ===================================
   Responsive Styles
==================================== */

@media (max-width: 1199px) {
    .apple-ecommerce-theme .hero-heading {
        font-size: 42px;
    }
    
    .apple-ecommerce-theme .apple-metric-card .metric-value {
        font-size: 36px;
    }
}

@media (max-width: 991px) {
    .apple-ecommerce-theme .hero-heading {
        font-size: 36px;
    }
    
    .apple-ecommerce-theme .platform-feature-box {
        padding: 24px;
    }
    
    .apple-ecommerce-theme .platform-feature-box .icon-wrapper {
        width: 50px;
        height: 50px;
    }
}

@media (max-width: 767px) {
    .apple-ecommerce-theme .hero-heading {
        font-size: 32px;
    }
    
    .apple-ecommerce-theme .apple-metric-card {
        padding: 20px;
    }
    
    .apple-ecommerce-theme .apple-metric-card .metric-value {
        font-size: 32px;
    }
    
    .apple-ecommerce-theme .simple-testimonial {
        padding: 24px;
    }
}
