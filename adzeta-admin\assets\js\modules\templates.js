/**
 * Templates Module - WordPress-Inspired Template Management
 * Manages blog post templates and template selection system
 */

const AdZetaTemplates = {
    // Module state
    state: {
        templates: {},
        currentSettings: {},
        isLoading: false,
        // Legacy blog templates data
        blogTemplates: []
    },

    // Initialize templates
    init() {
        this.loadLegacyTemplates();
        this.loadTemplateSettings();
        this.bindEvents();

        // Set up simplified template data for blog posts
        this.state.templates['blog-post'] = {
            'professional-enhanced': {
                name: 'Professional Enhanced',
                description: 'Premium blog template inspired by Medium, NYT, and HBR for maximum readability',
                file: 'blog-post-professional-enhanced.php',
                preview: 'professional-enhanced-preview.jpg'
            }
        };

        console.log('Templates module initialized - only Professional Enhanced template available');
    },

    // Load blog templates (only Professional Enhanced)
    loadLegacyTemplates() {
        this.state.blogTemplates = [
            {
                id: 'professional-enhanced',
                name: 'Professional Enhanced',
                description: 'Premium blog template inspired by Medium, NYT, and HBR for maximum readability',
                preview: '/blog/sample-professional-enhanced',
                category: 'Blog',
                features: ['Premium Typography', 'Maximum Readability', 'Professional Design', 'Responsive Layout'],
                structure: {
                    hero: {
                        title: true,
                        featured_image: true,
                        author: true,
                        date: true,
                        category: true,
                        excerpt: true
                    },
                    content: {
                        intro: true,
                        sections: true,
                        quote_blocks: true,
                        highlight_cards: true,
                        cta_sections: true
                    },
                    styling: {
                        premium_typography: true,
                        maximum_readability: true,
                        professional_design: true,
                        responsive_layout: true
                    }
                }
            }
            // Only Professional Enhanced template available
        ];
    },

    // Get all templates
    getTemplates() {
        return this.data.templates;
    },

    // Get template by ID
    getTemplate(templateId) {
        return this.data.templates.find(template => template.id === templateId);
    },

    // Get templates by category
    getTemplatesByCategory(category) {
        return this.data.templates.filter(template => template.category === category);
    },

    // Render template selector
    renderTemplateSelector() {
        const templates = this.getTemplates();

        return `
            <div class="template-selector">
                <h6 class="mb-3">Choose a Blog Template</h6>

                <!-- Templates Grid -->
                <div class="templates-grid">
                    ${templates.map(template => `
                        <div class="template-card" data-template="${template.id}" data-category="${template.category}">
                            <div class="template-preview">
                                <div class="template-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                            </div>
                            <div class="template-info">
                                <h6 class="template-name">${template.name}</h6>
                                <p class="template-description">${template.description}</p>
                                <div class="template-features">
                                    ${template.features.slice(0, 2).map(feature =>
                                        `<span class="badge badge-secondary">${feature}</span>`
                                    ).join('')}
                                </div>
                                <div class="template-actions mt-2">
                                    <button class="btn btn-sm btn-primary select-template" data-template="${template.id}">
                                        Select Template
                                    </button>
                                    ${template.preview !== '#' ?
                                        `<button class="btn btn-sm btn-outline-secondary preview-template" data-preview="${template.preview}">
                                            Preview
                                        </button>` : ''
                                    }
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    // Apply template to post
    applyTemplate(templateId, postData) {
        const template = this.getTemplate(templateId);
        if (!template) {
            console.error('Template not found:', templateId);
            return postData;
        }

        console.log('Applying template:', template.name);

        // Apply template-specific defaults
        const updatedPost = {
            ...postData,
            template: templateId,
            template_data: template.structure,
            url_slug: '/blog/' // Force blog URL slug
        };

        // Set template-specific content structure
        if (templateId === 'professional-blog') {
            updatedPost.content_blocks = this.getProfessionalBlogStructure();
            updatedPost.meta_title = updatedPost.meta_title || `${updatedPost.title} - Professional Blog`;
            updatedPost.excerpt = updatedPost.excerpt || 'Professional insights and expert analysis.';
        } else if (templateId === 'tutorial-blog') {
            updatedPost.content_blocks = this.getTutorialBlogStructure();
            updatedPost.meta_title = updatedPost.meta_title || `${updatedPost.title} - Step-by-Step Tutorial`;
            updatedPost.excerpt = updatedPost.excerpt || 'Learn step-by-step with our comprehensive tutorial.';
        } else if (templateId === 'listicle-blog') {
            updatedPost.content_blocks = this.getListicleBlogStructure();
            updatedPost.meta_title = updatedPost.meta_title || `${updatedPost.title} - Top List`;
            updatedPost.excerpt = updatedPost.excerpt || 'Discover the top insights in our comprehensive list.';
        }

        return updatedPost;
    },

    // Get Professional Blog template structure
    getProfessionalBlogStructure() {
        return {
            blocks: [
                {
                    type: 'paragraph',
                    data: {
                        text: 'Start your professional blog post with an engaging introduction that hooks your readers and clearly states what they will learn.'
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: 'Key Insights',
                        level: 2
                    }
                },
                {
                    type: 'paragraph',
                    data: {
                        text: 'Provide valuable insights and expert analysis on your topic. Use data, examples, and professional experience to support your points.'
                    }
                },
                {
                    type: 'list',
                    data: {
                        style: 'unordered',
                        items: [
                            'Key insight or takeaway #1',
                            'Key insight or takeaway #2',
                            'Key insight or takeaway #3'
                        ]
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: 'Conclusion',
                        level: 2
                    }
                },
                {
                    type: 'paragraph',
                    data: {
                        text: 'Summarize your main points and provide actionable next steps for your readers.'
                    }
                }
            ]
        };
    },

    // Get Tutorial Blog template structure
    getTutorialBlogStructure() {
        return {
            blocks: [
                {
                    type: 'paragraph',
                    data: {
                        text: 'Welcome to this step-by-step tutorial. By the end of this guide, you will have learned how to [describe the outcome].'
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: 'Prerequisites',
                        level: 2
                    }
                },
                {
                    type: 'list',
                    data: {
                        style: 'unordered',
                        items: [
                            'Prerequisite #1',
                            'Prerequisite #2',
                            'Basic understanding of [topic]'
                        ]
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: 'Step 1: Getting Started',
                        level: 2
                    }
                },
                {
                    type: 'paragraph',
                    data: {
                        text: 'Begin with the first step of your tutorial. Be clear and specific about what the reader needs to do.'
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: 'Step 2: Implementation',
                        level: 2
                    }
                },
                {
                    type: 'paragraph',
                    data: {
                        text: 'Continue with the next step, building upon what was learned in Step 1.'
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: 'Step 3: Completion',
                        level: 2
                    }
                },
                {
                    type: 'paragraph',
                    data: {
                        text: 'Finish with the final step and verify that everything is working correctly.'
                    }
                }
            ]
        };
    },

    // Get Listicle Blog template structure
    getListicleBlogStructure() {
        return {
            blocks: [
                {
                    type: 'paragraph',
                    data: {
                        text: 'Introduction to your list post. Explain what readers will discover and why this list is valuable.'
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: '1. First Item on the List',
                        level: 2
                    }
                },
                {
                    type: 'paragraph',
                    data: {
                        text: 'Detailed explanation of the first item. Include why it\'s important and how it benefits the reader.'
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: '2. Second Item on the List',
                        level: 2
                    }
                },
                {
                    type: 'paragraph',
                    data: {
                        text: 'Detailed explanation of the second item with supporting information and examples.'
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: '3. Third Item on the List',
                        level: 2
                    }
                },
                {
                    type: 'paragraph',
                    data: {
                        text: 'Continue with additional items as needed for your list.'
                    }
                },
                {
                    type: 'header',
                    data: {
                        text: 'Quick Takeaways',
                        level: 2
                    }
                },
                {
                    type: 'list',
                    data: {
                        style: 'unordered',
                        items: [
                            'Key takeaway #1',
                            'Key takeaway #2',
                            'Key takeaway #3'
                        ]
                    }
                }
            ]
        };
    },

    // NEW: WordPress-Inspired Template Management Methods

    // Load template settings from API
    async loadTemplateSettings() {
        try {
            this.state.isLoading = true;

            const response = await fetch('/adzeta-admin/api/templates');
            const data = await response.json();

            if (data.success) {
                this.state.templates = data.templates;
                this.state.currentSettings = data.current_settings;
                console.log('Template settings loaded:', this.state.templates);
            } else {
                console.warn('Failed to load template settings:', data.message || data.error);
            }

        } catch (error) {
            console.error('Error loading template settings:', error);
        } finally {
            this.state.isLoading = false;
        }
    },

    // Bind event handlers for template management
    bindEvents() {
        // Template selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.template-option')) {
                console.log('🎯 Template option clicked:', e.target);
                try {
                    this.handleTemplateSelection(e);
                } catch (error) {
                    console.error('❌ Error in handleTemplateSelection:', error);
                    if (window.AdZetaApp) {
                        window.AdZetaApp.showNotification('Error selecting template: ' + error.message, 'danger');
                    }
                }
            }
        });

        // Save template settings
        document.addEventListener('click', (e) => {
            if (e.target.matches('#saveTemplateSettings')) {
                this.saveTemplateSettings();
            }
        });

        // Preview template
        document.addEventListener('click', (e) => {
            if (e.target.matches('.template-preview-btn')) {
                this.previewTemplate(e);
            }
        });
    },

    // Handle template selection
    handleTemplateSelection(e) {
        const templateOption = e.target.closest('.template-option');
        if (!templateOption) {
            console.warn('Template option not found');
            return;
        }

        const contentType = templateOption.dataset.contentType;
        const templateKey = templateOption.dataset.template;

        if (!templateKey) {
            console.warn('Template key not found');
            return;
        }

        // Update selection in UI - try multiple container selectors
        let container = templateOption.closest('.template-selector');
        if (!container) {
            container = templateOption.closest('.row'); // Fallback for post editor
        }
        if (!container) {
            container = templateOption.parentElement; // Final fallback
        }

        if (container) {
            container.querySelectorAll('.template-option').forEach(option => {
                option.classList.remove('selected', 'border-primary', 'bg-light');
            });
            templateOption.classList.add('selected', 'border-primary', 'bg-light');
        }

        // Update state
        if (contentType) {
            this.state.currentSettings[contentType] = templateKey;
        }

        // Also update post editor state if available
        if (window.AdZetaPostEditor && window.AdZetaPostEditor.selectPostTemplate) {
            window.AdZetaPostEditor.selectPostTemplate(templateKey);
        }

        // Show feedback
        if (window.AdZetaApp) {
            window.AdZetaApp.showNotification(`Template selected: ${templateKey}`, 'success');
        }

        console.log('Template selected:', templateKey, 'for content type:', contentType);
    },

    // Save template settings
    async saveTemplateSettings() {
        try {
            const response = await fetch('/adzeta-admin/api/templates/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.state.currentSettings)
            });

            const data = await response.json();

            if (data.success) {
                if (window.AdZetaApp) {
                    window.AdZetaApp.showNotification('Template settings saved successfully!', 'success');
                }

                // Clear cache since templates changed
                this.clearTemplateCache();
            } else {
                throw new Error(data.error || 'Failed to save template settings');
            }

        } catch (error) {
            console.error('Error saving template settings:', error);
            if (window.AdZetaApp) {
                window.AdZetaApp.showNotification('Failed to save template settings: ' + error.message, 'danger');
            }
        }
    },

    // Preview template
    async previewTemplate(e) {
        const type = e.target.dataset.type;
        const template = e.target.dataset.template;

        try {
            const response = await fetch(`/adzeta-admin/api/templates/preview?type=${type}&template=${template}`);
            const data = await response.json();

            if (data.success) {
                // Open preview in new window
                window.open(data.data.preview_url, '_blank');
            } else {
                throw new Error(data.error || 'Failed to get template preview');
            }

        } catch (error) {
            console.error('Error previewing template:', error);
            if (window.AdZetaApp) {
                window.AdZetaApp.showNotification('Failed to preview template: ' + error.message, 'danger');
            }
        }
    },

    // Clear template cache
    async clearTemplateCache() {
        try {
            const response = await fetch('/adzeta-admin/api/cache/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type: 'templates' })
            });

            const data = await response.json();

            if (data.success) {
                console.log('Template cache cleared');
            }

        } catch (error) {
            console.error('Error clearing template cache:', error);
        }
    },

    // Render template management interface
    renderTemplateManagement() {
        const container = document.getElementById('templateManagement');
        if (!container) return;

        container.innerHTML = this.renderTemplateInterface();
    },

    // Render template interface HTML
    renderTemplateInterface() {
        if (this.state.isLoading) {
            return '<div class="text-center p-4"><div class="spinner-border" role="status"></div></div>';
        }

        return `
            <div class="template-management">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-palette fa-2x text-primary me-3"></i>
                                <div>
                                    <h6 class="mb-1">WordPress-Inspired Template System</h6>
                                    <p class="mb-0">Choose different templates for your blog posts and listings to create unique designs.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Blog Post Templates -->
                <div class="row mb-5">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt me-2 text-primary"></i>
                                    Blog Post Templates
                                </h6>
                                <small class="text-muted">Choose the default template for individual blog posts</small>
                            </div>
                            <div class="card-body">
                                ${this.renderTemplateSelector('blog-post')}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Blog List Templates -->
                <div class="row mb-5">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2 text-primary"></i>
                                    Blog List Templates
                                </h6>
                                <small class="text-muted">Choose the template for blog listing pages</small>
                            </div>
                            <div class="card-body">
                                ${this.renderTemplateSelector('blog-list')}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="row">
                    <div class="col-12 text-center">
                        <button id="saveTemplateSettings" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            Save Template Settings
                        </button>
                    </div>
                </div>
            </div>
        `;
    },

    // Render template selector for specific content type
    renderTemplateSelector(contentType) {
        const templates = this.state.templates[contentType] || {};
        const currentTemplate = this.state.currentSettings[contentType] || '';

        if (Object.keys(templates).length === 0) {
            return '<p class="text-muted">No templates available for this content type.</p>';
        }

        let html = '<div class="template-selector row">';

        Object.entries(templates).forEach(([templateKey, template]) => {
            const isSelected = currentTemplate === templateKey;
            const selectedClass = isSelected ? 'selected border-primary' : '';

            html += `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="template-option card h-100 ${selectedClass}"
                         data-content-type="${contentType}"
                         data-template="${templateKey}"
                         style="cursor: pointer; transition: all 0.3s ease;">

                        <div class="template-preview-container position-relative">
                            <img src="/adzeta-admin/assets/images/template-previews/${template.preview || 'default-preview.jpg'}"
                                 class="card-img-top"
                                 alt="${template.name}"
                                 style="height: 200px; object-fit: cover;">

                            ${isSelected ? '<div class="template-selected-badge position-absolute top-0 end-0 m-2"><span class="badge bg-primary">✓ Selected</span></div>' : ''}

                            <div class="template-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center"
                                 style="background: rgba(0,0,0,0.7); opacity: 0; transition: opacity 0.3s ease;">
                                <button class="btn btn-light template-preview-btn"
                                        data-type="${contentType}"
                                        data-template="${templateKey}">
                                    <i class="fas fa-eye me-2"></i>Preview
                                </button>
                            </div>
                        </div>

                        <div class="card-body">
                            <h6 class="card-title">${template.name}</h6>
                            <p class="card-text text-muted small">${template.description}</p>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';

        return html;
    }
};

// Make module globally available
window.AdZetaTemplates = AdZetaTemplates;
