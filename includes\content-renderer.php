<?php
/**
 * Content Renderer Helper
 * Provides a centralized way to render blog post content with EditorJS support
 */

/**
 * Render blog post content with EditorJS support
 * 
 * @param array $post The blog post data
 * @return string Rendered HTML content
 */
function renderPostContent($post) {
    // Process content through EditorJSParser if content_blocks are available
    if (!empty($post['content_blocks']) && is_array($post['content_blocks'])) {
        // Use EditorJSParser to render custom blocks
        require_once __DIR__ . '/../adzeta-admin/src/Services/EditorJSParser.php';
        $parser = new \AdZetaAdmin\Services\EditorJSParser();
        return $parser->parseToHTML($post['content_blocks']);
    } else {
        // Fallback to regular content
        return $post['content'] ?? '';
    }
}

/**
 * Check if post has EditorJS content blocks
 * 
 * @param array $post The blog post data
 * @return bool True if post has content blocks
 */
function hasEditorJSContent($post) {
    return !empty($post['content_blocks']) && is_array($post['content_blocks']);
}

/**
 * Get content blocks count
 * 
 * @param array $post The blog post data
 * @return int Number of content blocks
 */
function getContentBlocksCount($post) {
    if (!hasEditorJSContent($post)) {
        return 0;
    }
    
    return isset($post['content_blocks']['blocks']) ? count($post['content_blocks']['blocks']) : 0;
}

/**
 * Debug content blocks (for development)
 * 
 * @param array $post The blog post data
 * @return string Debug information
 */
function debugContentBlocks($post) {
    if (!hasEditorJSContent($post)) {
        return "<!-- No EditorJS content blocks found -->";
    }
    
    $blocks = $post['content_blocks']['blocks'] ?? [];
    $debug = "<!-- EditorJS Debug Info:\n";
    $debug .= "Total blocks: " . count($blocks) . "\n";
    
    foreach ($blocks as $index => $block) {
        $debug .= "Block " . ($index + 1) . ": " . ($block['type'] ?? 'unknown') . "\n";
    }
    
    $debug .= "-->";
    
    return $debug;
}
