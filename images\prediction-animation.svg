<?xml version="1.0" encoding="UTF-8"?>
<svg id="prediction-svg" width="100%" height="100%" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <style>
    text {
      font-family: 'Proxima Nova', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	
    }
  </style>
  <!-- Definitions for filters and gradients -->
  <defs>
    <!-- Gradient for confidence area -->
    <linearGradient id="confidence-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#8f76f5" stop-opacity="0.7"/>
      <stop offset="100%" stop-color="#8f76f5" stop-opacity="0.1"/>
    </linearGradient>

    <!-- Glow filter for prediction points -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>

    <!-- Gradient for prediction line -->
    <linearGradient id="line-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#8f76f5"/>
      <stop offset="100%" stop-color="#e958a1"/>
      <animate attributeName="x1" values="0%;20%;0%" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="x2" values="100%;80%;100%" dur="10s" repeatCount="indefinite"/>
    </linearGradient>
  </defs>

  <!-- Background grid with fade-in animation -->
  <g class="chart-grid" opacity="0">
    <animate attributeName="opacity" from="0" to="0.8" dur="1s" begin="0.2s" fill="freeze"/>
    <line x1="100" y1="100" x2="100" y2="500" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="200" y1="100" x2="200" y2="500" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="300" y1="100" x2="300" y2="500" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="400" y1="100" x2="400" y2="500" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="500" y1="100" x2="500" y2="500" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="600" y1="100" x2="600" y2="500" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="700" y1="100" x2="700" y2="500" stroke="#e0e0e0" stroke-width="1"/>

    <line x1="100" y1="100" x2="700" y2="100" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="100" y1="200" x2="700" y2="200" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="100" y1="300" x2="700" y2="300" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="100" y1="400" x2="700" y2="400" stroke="#e0e0e0" stroke-width="1"/>
    <line x1="100" y1="500" x2="700" y2="500" stroke="#e0e0e0" stroke-width="1"/>
  </g>

  <!-- Axes with drawing animation -->
  <line x1="100" y1="500" x2="100" y2="500" stroke="#2c2e3c" stroke-width="2" class="x-axis">
    <animate attributeName="x2" from="100" to="700" dur="1s" begin="0.5s" fill="freeze"/>
  </line>
  <line x1="100" y1="500" x2="100" y2="500" stroke="#2c2e3c" stroke-width="2" class="y-axis">
    <animate attributeName="y2" from="500" to="100" dur="1s" begin="0.5s" fill="freeze"/>
  </line>

  <!-- X-axis labels with fade-in animation -->
  <text x="100" y="530" text-anchor="middle" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.5s" fill="freeze"/>
    Now
  </text>
  <text x="300" y="530" text-anchor="middle" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.7s" fill="freeze"/>
    30 Days
  </text>
  <text x="500" y="530" text-anchor="middle" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.9s" fill="freeze"/>
    60 Days
  </text>
  <text x="700" y="530" text-anchor="middle" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="2.1s" fill="freeze"/>
    90 Days
  </text>

  <!-- Y-axis labels with fade-in animation -->
  <text x="80" y="500" text-anchor="end" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.5s" fill="freeze"/>
    $0
  </text>
  <text x="80" y="400" text-anchor="end" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.7s" fill="freeze"/>
    $50
  </text>
  <text x="80" y="300" text-anchor="end" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="1.9s" fill="freeze"/>
    $100
  </text>
  <text x="80" y="200" text-anchor="end" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="2.1s" fill="freeze"/>
    $150
  </text>
  <text x="80" y="100" text-anchor="end" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="2.3s" fill="freeze"/>
    $200
  </text>

  <!-- Present/Future divider with animation -->
  <line x1="100" y1="500" x2="100" y2="500" stroke="#e958a1" stroke-width="1" stroke-dasharray="5,5" class="present-line">
    <animate attributeName="y2" from="500" to="100" dur="0.5s" begin="2.5s" fill="freeze"/>
  </line>
  <text x="120" y="80" text-anchor="middle" fill="#e958a1" font-size="20" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="3s" fill="freeze"/>
    Present
  </text>

  <!-- Data points with sequential appearance -->
  <circle cx="100" cy="450" r="0" fill="#2c2e3c" class="data-point">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="3.2s" fill="freeze"/>
  </circle>
  <circle cx="150" cy="425" r="0" fill="#2c2e3c" class="data-point">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="3.4s" fill="freeze"/>
  </circle>
  <circle cx="200" cy="400" r="0" fill="#2c2e3c" class="data-point">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="3.6s" fill="freeze"/>
  </circle>
  <circle cx="250" cy="350" r="0" fill="#2c2e3c" class="data-point">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="3.8s" fill="freeze"/>
  </circle>

  <!-- Prediction curve with drawing animation -->
  <path d="M100 450 C 200 400, 300 300, 400 250 C 500 200, 600 180, 700 150" fill="none" stroke="url(#line-gradient)" stroke-width="3" class="prediction-line" stroke-dasharray="800" stroke-dashoffset="800">
    <animate attributeName="stroke-dashoffset" from="800" to="0" dur="2s" begin="4s" fill="freeze"/>
  </path>

  <!-- Confidence interval with fade-in animation -->
  <path d="M100 450 C 200 400, 300 300, 400 250 C 500 200, 600 180, 700 150 L 700 200 C 600 230, 500 250, 400 300 C 300 350, 200 450, 100 450 Z" fill="url(#confidence-gradient)" opacity="0" class="confidence-area">
    <animate attributeName="opacity" from="0" to="0.3" dur="1s" begin="6s" fill="freeze"/>
  </path>

  <!-- Prediction points with sequential appearance and glow effect -->
  <circle cx="300" cy="300" r="0" fill="#8f76f5" class="prediction-point" filter="url(#glow)">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="6.2s" fill="freeze"/>
    <animate attributeName="opacity" values="1;0.7;1" dur="2s" begin="6.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="400" cy="250" r="0" fill="#8f76f5" class="prediction-point" filter="url(#glow)">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="6.4s" fill="freeze"/>
    <animate attributeName="opacity" values="1;0.7;1" dur="2s" begin="6.7s" repeatCount="indefinite"/>
  </circle>
  <circle cx="500" cy="200" r="0" fill="#8f76f5" class="prediction-point" filter="url(#glow)">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="6.6s" fill="freeze"/>
    <animate attributeName="opacity" values="1;0.7;1" dur="2s" begin="6.9s" repeatCount="indefinite"/>
  </circle>
  <circle cx="600" cy="180" r="0" fill="#8f76f5" class="prediction-point" filter="url(#glow)">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="6.8s" fill="freeze"/>
    <animate attributeName="opacity" values="1;0.7;1" dur="2s" begin="7.1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="700" cy="150" r="0" fill="#8f76f5" class="prediction-point" filter="url(#glow)">
    <animate attributeName="r" from="0" to="6" dur="0.3s" begin="7.0s" fill="freeze"/>
    <animate attributeName="opacity" values="1;0.7;1" dur="2s" begin="7.3s" repeatCount="indefinite"/>
  </circle>

  <!-- Annotations with fade-in and highlight animation -->
<g xmlns="http://www.w3.org/2000/svg" class="annotation" opacity="0">
  <animate attributeName="opacity" from="0" to="1" dur="1s" begin="7.5s" fill="freeze"/>

  <!-- Line pointing to the box -->
 <line x1="500" y1="200" x2="500" y2="200" stroke="#2c2e3c" stroke-width="1" stroke-dasharray="3,3">
  <animate attributeName="x2" from="500" to="550" dur="0.5s" begin="7.5s" fill="freeze"/>
  <animate attributeName="y2" from="200" to="150" dur="0.5s" begin="7.5s" fill="freeze"/>
</line>


  <!-- Enlarged rectangle for more text space -->
  <rect x="450" y="120" width="0" height="80" rx="16" fill="white" stroke="#e0e0e0" stroke-width="1">
    <animate attributeName="width" from="0" to="200" dur="0.5s" begin="8s" fill="freeze"/>
  </rect>

  <!-- Text lines adjusted for new center -->
  <text x="550" y="150" text-anchor="middle" fill="#2c2e3c" font-size="22" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="8.5s" fill="freeze"/>
    Predicted LTV
  </text>
  <text x="550" y="180" text-anchor="middle" fill="#e958a1" font-size="24" font-weight="700" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="0.5s" begin="8.7s" fill="freeze"/>
    $150 at 60 days
  </text>
</g>



  <!-- Highlight effect for the 60-day mark -->
  <circle cx="500" cy="200" r="15" fill="none" stroke="#e958a1" stroke-width="1" opacity="0">
    <animate attributeName="opacity" values="0;0.7;0" dur="3s" begin="9s;12s;15s" repeatCount="indefinite"/>
    <animate attributeName="r" values="15;25;15" dur="3s" begin="9s;12s;15s" repeatCount="indefinite"/>
  </circle>

  <!-- Animation script -->
  <script type="text/javascript"><![CDATA[
    // This SVG uses SMIL animations and doesn't require JavaScript
    // The animations will play automatically when the SVG is loaded
  ]]></script>
</svg>
