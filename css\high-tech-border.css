/**
 * High-Tech Border Animation
 * Creates a thin border with moving light/glow streak effect
 */

/* Base high-tech border styles */
.high-tech-border {
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 0 5px rgba(233, 88, 161, 0.2);
}

/* Create the moving light streak effect */
.high-tech-border::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 1px solid rgba(233, 88, 161, 0.8);
  border-radius: 20px;
  z-index: 2;
  pointer-events: none;
  clip-path: polygon(0 0, 15% 0, 35% 100%, 0 100%);
  animation: light-streak 4s linear infinite;
  box-shadow: 0 0 10px rgba(233, 88, 161, 0.8), 0 0 20px rgba(143, 118, 245, 0.5);
}

/* Add a second streak for more dynamic effect */
.high-tech-border::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 1px solid rgba(143, 118, 245, 0.8);
  border-radius: 20px;
  z-index: 2;
  pointer-events: none;
  clip-path: polygon(0 0, 15% 0, 35% 100%, 0 100%);
  animation: light-streak 4s linear infinite 2s;
  box-shadow: 0 0 10px rgba(143, 118, 245, 0.8);
}

@keyframes light-streak {
  0% {
    clip-path: polygon(0 0, 15% 0, 35% 100%, 0 100%);
    border-color: rgba(233, 88, 161, 0.8);
  }
  25% {
    clip-path: polygon(25% 0, 40% 0, 60% 100%, 25% 100%);
    border-color: rgba(255, 93, 116, 0.8);
  }
  50% {
    clip-path: polygon(50% 0, 65% 0, 85% 100%, 50% 100%);
    border-color: rgba(143, 118, 245, 0.8);
  }
  75% {
    clip-path: polygon(75% 0, 90% 0, 100% 100%, 75% 100%);
    border-color: rgba(255, 93, 116, 0.8);
  }
  100% {
    clip-path: polygon(100% 0, 100% 0, 100% 100%, 100% 100%);
    border-color: rgba(233, 88, 161, 0.8);
  }
}

/* Light particles that follow the streak */
.high-tech-border .border-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #e958a1;
  box-shadow: 0 0 8px #e958a1, 0 0 15px #e958a1;
  z-index: 2;
  opacity: 0;
  pointer-events: none;
}
