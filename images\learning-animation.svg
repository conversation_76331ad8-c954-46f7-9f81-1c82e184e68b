<?xml version="1.0" encoding="UTF-8"?>
<svg id="learning-svg" width="100%" height="100%" viewBox="110 0 600 600" xmlns="http://www.w3.org/2000/svg">
  <style>
    text {
      font-family: 'Proxima Nova', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .stage-title {
      font-size: 28px;
      font-weight: 700;
    }
    .stage-subtitle {
      font-size: 22px;
      font-weight: 600;
    }
    .core-title {
      font-size: 32px;
      font-weight: 800;
    }
    .core-subtitle {
      font-size: 24px;
      font-weight: 700;
    }
  </style>
  <!-- Definitions for filters, gradients, and markers -->
  <defs>
    <!-- Smaller, more subtle arrow marker -->
    <marker id="arrow" viewBox="0 0 10 10" refX="9" refY="5"
        markerWidth="6" markerHeight="6" orient="auto">
      <path d="M 0 0 L 10 5 L 0 10 z" fill="#2c2e3c" opacity="0.7"/>
    </marker>

    <!-- Animated core gradient -->
    <linearGradient id="core-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8f76f5">
        <animate attributeName="stop-color" values="#8f76f5;#9f86ff;#8f76f5" dur="8s" repeatCount="indefinite"/>
      </stop>
      <stop offset="50%" stop-color="#e958a1">
        <animate attributeName="stop-color" values="#e958a1;#ff68b1;#e958a1" dur="8s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#4a9eff">
        <animate attributeName="stop-color" values="#4a9eff;#5aaeFF;#4a9eff" dur="8s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>

    <!-- Animated pulse gradient -->
    <linearGradient id="pulse-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#e958a1">
        <animate attributeName="stop-color" values="#e958a1;#ff68b1;#e958a1" dur="6s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#8f76f5">
        <animate attributeName="stop-color" values="#8f76f5;#9f86ff;#8f76f5" dur="6s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>

    <!-- Glow filter for active nodes -->
    <filter id="glow" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>

    <!-- Particle filter -->
    <filter id="particle-blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1.5" result="blur"/>
    </filter>

    <!-- Flow animation paths -->
    <path id="flow-collect-to-analyze" d="M440 120 A 180 180 0 0 1 580 260"/>
    <path id="flow-analyze-to-adapt" d="M580 340 A 180 180 0 0 1 440 480"/>
    <path id="flow-adapt-to-improve" d="M360 480 A 180 180 0 0 1 220 340"/>
    <path id="flow-improve-to-collect" d="M220 260 A 180 180 0 0 1 360 120"/>
  </defs>

  <!-- Central circle with rotation animation -->
  <circle cx="400" cy="300" r="200" fill="none" stroke="#2c2e3c" stroke-width="2" stroke-dasharray="5,5" stroke-opacity="0.5" class="cycle-path">
    <animateTransform attributeName="transform" type="rotate" from="0 400 300" to="360 400 300" dur="120s" repeatCount="indefinite"/>
  </circle>

  <!-- Four stages with sequential highlighting - increased size and spacing -->
  <g class="stage" id="collect-stage">
    <circle cx="400" cy="100" r="70" fill="#f5f5f7" stroke="#8f76f5" stroke-width="5" class="cycle-node collect-node">
      <animate attributeName="stroke-width" values="5;7;5" dur="1s" begin="0.5s;10.5s;20.5s;30.5s" repeatCount="indefinite"/>
      <animate attributeName="fill" values="#f5f5f7;#f0f4ff;#f5f5f7" dur="1s" begin="0.5s;10.5s;20.5s;30.5s" repeatCount="indefinite"/>
    </circle>
    <text x="400" y="90" text-anchor="middle" fill="#2c2e3c" class="stage-title">Collect</text>
    <text x="400" y="125" text-anchor="middle" fill="#2c2e3c" class="stage-subtitle">New Data</text>
  </g>

  <g class="stage" id="analyze-stage">
    <circle cx="600" cy="300" r="70" fill="#f5f5f7" stroke="#e958a1" stroke-width="5" class="cycle-node analyze-node">
      <animate attributeName="stroke-width" values="5;7;5" dur="1s" begin="3s;13s;23s;33s" repeatCount="indefinite"/>
      <animate attributeName="fill" values="#f5f5f7;#fff0f5;#f5f5f7" dur="1s" begin="3s;13s;23s;33s" repeatCount="indefinite"/>
    </circle>
    <text x="600" y="290" text-anchor="middle" fill="#2c2e3c" class="stage-title">Analyze</text>
    <text x="600" y="325" text-anchor="middle" fill="#2c2e3c" class="stage-subtitle">Patterns</text>
  </g>

  <g class="stage" id="adapt-stage">
    <circle cx="400" cy="500" r="70" fill="#f5f5f7" stroke="#4a9eff" stroke-width="5" class="cycle-node adapt-node">
      <animate attributeName="stroke-width" values="5;7;5" dur="1s" begin="5.5s;15.5s;25.5s;35.5s" repeatCount="indefinite"/>
      <animate attributeName="fill" values="#f5f5f7;#f0faff;#f5f5f7" dur="1s" begin="5.5s;15.5s;25.5s;35.5s" repeatCount="indefinite"/>
    </circle>
    <text x="400" y="490" text-anchor="middle" fill="#2c2e3c" class="stage-title">Adapt</text>
    <text x="400" y="525" text-anchor="middle" fill="#2c2e3c" class="stage-subtitle">Models</text>
  </g>

  <g class="stage" id="improve-stage">
    <circle cx="200" cy="300" r="70" fill="#f5f5f7" stroke="#ff8cc6" stroke-width="5" class="cycle-node improve-node">
      <animate attributeName="stroke-width" values="5;7;5" dur="1s" begin="8s;18s;28s;38s" repeatCount="indefinite"/>
      <animate attributeName="fill" values="#f5f5f7;#fff0f8;#f5f5f7" dur="1s" begin="8s;18s;28s;38s" repeatCount="indefinite"/>
    </circle>
    <text x="200" y="290" text-anchor="middle" fill="#2c2e3c" class="stage-title">Improve</text>
    <text x="200" y="325" text-anchor="middle" fill="#2c2e3c" class="stage-subtitle">Predictions</text>
  </g>

  <!-- Connecting arrows with animated dash patterns - thinner and more subtle -->
  <path d="M440 120 A 180 180 0 0 1 580 260" fill="none" stroke="#2c2e3c" stroke-width="2" stroke-opacity="0.7" marker-end="url(#arrow)" class="connection collect-to-analyze" stroke-dasharray="300" stroke-dashoffset="300">
    <animate attributeName="stroke-dashoffset" from="300" to="0" dur="1.5s" begin="0.5s;10.5s;20.5s;30.5s" fill="freeze"/>
    <animate attributeName="stroke" values="#2c2e3c;#8f76f5;#2c2e3c" dur="2s" begin="0.5s;10.5s;20.5s;30.5s"/>
  </path>

  <path d="M580 340 A 180 180 0 0 1 440 480" fill="none" stroke="#2c2e3c" stroke-width="2" stroke-opacity="0.7" marker-end="url(#arrow)" class="connection analyze-to-adapt" stroke-dasharray="300" stroke-dashoffset="300">
    <animate attributeName="stroke-dashoffset" from="300" to="0" dur="1.5s" begin="3s;13s;23s;33s" fill="freeze"/>
    <animate attributeName="stroke" values="#2c2e3c;#e958a1;#2c2e3c" dur="2s" begin="3s;13s;23s;33s"/>
  </path>

  <path d="M360 480 A 180 180 0 0 1 220 340" fill="none" stroke="#2c2e3c" stroke-width="2" stroke-opacity="0.7" marker-end="url(#arrow)" class="connection adapt-to-improve" stroke-dasharray="300" stroke-dashoffset="300">
    <animate attributeName="stroke-dashoffset" from="300" to="0" dur="1.5s" begin="5.5s;15.5s;25.5s;35.5s" fill="freeze"/>
    <animate attributeName="stroke" values="#2c2e3c;#4a9eff;#2c2e3c" dur="2s" begin="5.5s;15.5s;25.5s;35.5s"/>
  </path>

  <path d="M220 260 A 180 180 0 0 1 360 120" fill="none" stroke="#2c2e3c" stroke-width="2" stroke-opacity="0.7" marker-end="url(#arrow)" class="connection improve-to-collect" stroke-dasharray="300" stroke-dashoffset="300">
    <animate attributeName="stroke-dashoffset" from="300" to="0" dur="1.5s" begin="8s;18s;28s;38s" fill="freeze"/>
    <animate attributeName="stroke" values="#2c2e3c;#ff8cc6;#2c2e3c" dur="2s" begin="8s;18s;28s;38s"/>
  </path>

  <!-- Larger flow particles -->
  <circle cx="0" cy="0" r="6" fill="#8f76f5" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M440 120 A 180 180 0 0 1 580 260" dur="2s" begin="1s;11s;21s;31s"/>
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1s;11s;21s;31s"/>
  </circle>

  <!-- Central AI core with pulsing animation - larger -->
  <circle cx="400" cy="300" r="90" fill="url(#core-gradient)" class="ai-core">
    <animate attributeName="r" values="90;95;90" dur="4s" repeatCount="indefinite"/>
  </circle>

  <!-- Core text with larger fonts -->
  <text x="400" y="300" text-anchor="middle" fill="white" class="core-title">AI Core</text>
  <text x="400" y="335" text-anchor="middle" fill="white" class="core-subtitle">Continuous</text>
  <text x="400" y="370" text-anchor="middle" fill="white" class="core-subtitle">Learning</text>

  <!-- Larger pulse animations -->
  <circle cx="400" cy="300" r="130" fill="none" stroke="url(#pulse-gradient)" stroke-width="4" opacity="0.5" class="pulse-ring pulse1">
    <animate attributeName="r" values="130;150;130" dur="6s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.5;0.2;0.5" dur="6s" repeatCount="indefinite"/>
  </circle>

  <!-- Larger processing indicators -->
  <g class="processing-animation">
    <circle cx="400" cy="300" r="60" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" stroke-dasharray="10,5">
      <animateTransform attributeName="transform" type="rotate" from="0 400 300" to="360 400 300" dur="20s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>