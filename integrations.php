<?php include 'header.php'; ?>
        <!-- end header -->
        <!-- start hero section -->
        <section class="cover-background top-space-padding pb-0 overflow-hidden position-relative lg-pb-30px hero-section ecom-ppc">
		      <link rel="stylesheet" href="css/ppc-hero-animation.css?v=1.0" />
            <div class="professional-gradient-container">
                <div class="corner-gradient top-left"></div>
                <div class="corner-gradient top-right"></div>
                <div class="corner-gradient bottom-left"></div>
                <div class="corner-gradient bottom-right"></div>
                <div class="diagonal-gradient"></div>
                <div class="mesh-overlay"></div>
                <div class="vignette-overlay"></div>
            </div>
            <div id="particles-style-03" class="h-100 position-absolute left-0px top-0 w-100" data-particle="true" data-particle-options='{"particles": {"number": {"value": 30,"density": {"enable": true,"value_area": 2000}},"color": {"value": ["#ffffff", "#e958a1", "#d15ec7", "#8f76f5", "#6a4fd9"]},"shape": {"type": ["circle"],"stroke":{"width":0,"color":"#000000"}},"opacity": {"value": 0.12,"random": true,"anim": {"enable": true,"speed": 0.15,"sync": false}},"size": {"value": 1.0,"random": true,"anim": {"enable": true,"speed": 0.15,"sync": false}},"line_linked":{"enable":true,"distance":200,"color":"#8f76f5","opacity":0.06,"width":0.4},"move": {"enable": true,"speed":0.2,"direction": "none","random": true,"straight": false,"out_mode": "out","bounce": false,"attract": {"enable": true,"rotateX": 600,"rotateY": 1200}}},"interactivity": {"detect_on": "canvas","events": {"onhover": {"enable": true,"mode": "grab"},"onclick": {"enable": true,"mode": "repulse"},"resize": true},"modes":{"grab":{"distance":180,"line_linked":{"opacity":0.12}},"repulse":{"distance":150,"duration":0.4}}},"retina_detect": true}'></div>
            <div class="container h-100">
                <!-- Removed distracting background elements for a more professional look -->
                <div class="row align-items-center h-100 md-mt-30px md-mb-10px pt-4">
                    <div class="col-xl-6 col-lg-6 mb-9 position-relative z-index-1 ps-lg-5" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                        <div class="d-flex align-items-center mb-15px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
                            <span class="fs-12 fw-light text-white opacity-90 primary-font ls-wide">
                                <span class="ai-icon">
                                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="integration-icon">
									  <style>
										.integration-pulse {
										  animation: pulseGlow 2s infinite ease-in-out;
										}

										.data-flow {
										  stroke-dasharray: 4;
										  stroke-dashoffset: 16;
										  animation: dashFlow 4s linear infinite;
										}

										.core-predict {
										  animation: corePulse 3s infinite ease-in-out;
										}

										@keyframes pulseGlow {
										  0%, 100% { stroke-opacity: 0.4; }
										  50% { stroke-opacity: 1; }
										}

										@keyframes dashFlow {
										  to { stroke-dashoffset: 0; }
										}

										@keyframes corePulse {
										  0%, 100% { transform: scale(1); opacity: 0.6; }
										  50% { transform: scale(1.2); opacity: 1; }
										}
									  </style>

									  <!-- Chip base -->
									  <rect x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" class="integration-pulse"/>

									  <!-- Circuit lines (data flow) -->
									  <path class="data-flow" d="M8 4V2M12 4V2M16 4V2M8 22V20M12 22V20M16 22V20M2 8H4M2 12H4M2 16H4M20 8H22M20 12H22M20 16H22" 
											stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>

									  <!-- Inner processor grid -->
									  <path d="M8 8H16M8 12H16M8 16H16M8 8V16M12 8V16M16 8V16" stroke="url(#paint0_linear_ai_icon)" stroke-width="0.75" stroke-linecap="round" stroke-dasharray="1 2"/>

									  <!-- Central core -->
									  <rect class="core-predict" x="10" y="10" width="4" height="4" fill="url(#paint0_linear_ai_icon)"/>

									  <!-- Sparkle overlay -->
									  <rect x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint1_linear_ai_icon)" stroke-width="1" class="integration-pulse"/>

									  <defs>
										<linearGradient id="paint0_linear_ai_icon" x1="2" y1="2" x2="22" y2="22" gradientUnits="userSpaceOnUse">
										  <stop offset="0" stop-color="#e958a1"/>
										  <stop offset="0.5" stop-color="#8f76f5"/>
										  <stop offset="1" stop-color="#4a9eff"/>
										</linearGradient>
										<linearGradient id="paint1_linear_ai_icon" x1="20" y1="4" x2="4" y2="20" gradientUnits="userSpaceOnUse">
										  <stop offset="0" stop-color="#ffffff"/>
										  <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
										</linearGradient>
									  </defs>
									</svg>

                                </span>
                                <span class="text-gradient-purple-blue ls-3px">SEAMLESS INTEGRATIONS</span>
                            </span>
                        </div>
                        <h1 class="alt-font mb-15px fs-50 md-fs-60 sm-fs-60 xs-fs-45 fw-600  heading-gradient" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 1000, "delay": 300, "easing": "easeOutQuad" }'>
                            <span class="fw-300">Turn First-Party Data into</span><br><span class="heading-gradient">Predictive Growth</span>
                        </h1>
                        <div class="alt-font fw-400 fs-16 w-90 sm-w-100 mb-25px xs-mb-20px text-white opacity-75 lh-1-5" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 800, "easing": "easeOutQuint" }'>Adzeta’s Predictive AI thrives on your valuable first-party data. We integrate securely with your e-commerce stack, CRM, data warehouses, commerce platforms, and measurement tools — turning raw data into actionable insights like customer lifetime value (LTV), and AI-powered ad recommendations. Drive smarter decisions and accelerate growth with seamless, scalable integrations.</div>
                        <!--  <div class="d-flex flex-wrap" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 1000, "easing": "easeOutQuint" }'>
                            <a href="#get-started" class="btn btn-large btn-hover-animation box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-gradient-pink-orange btn-rounded me-15px mt-15px fw-500 alt-font">
                            <span> 
                            <span class="btn-text fs-16 md-fs-16">Consult a Growth Expert</span> 
                            <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span> 
                            </span>
                            </a>
                            </div> -->
                    </div>
                    <div class="col-xl-6 col-lg-6 align-self-center">
                        <div class="platform-animation-container pt-0" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 500 }'>
                            <!-- New Hero Animation Structure with Semicircle Layout -->
                            <div class="hero-animation-container">
                                <!-- Center Element - Adzeta AI Core -->
                                <div class="animation-element adzeta-ai-center">
                                    <div class="position-relative">
                                        <img src="images/adzeta-ai-center.png" alt="Adzeta AI Core" class="w-100">
                                        <div class="element-label label-center">ADZETA AI</div>
                                    </div>
                                </div>
                                <!-- Left Side Elements - Data Input (Semicircle) -->
                                <div class="animation-element adzeta-ai-left-1">
                                    <img src="images/data-source-1.png" alt="Customer Data" class="w-100">
                                    <div class="element-label label-left-1">PRODUCT FEED</div>
                                </div>
                                <div class="animation-element adzeta-ai-left-2">
                                    <img src="images/data-source-2.png" alt="Website Data" class="w-100">
                                    <div class="element-label label-left-2">WEBSITE SIGNALS</div>
                                </div>
                                <div class="animation-element adzeta-ai-left-3">
                                    <img src="images/data-source-3.png" alt="CRM Data" class="w-100">
                                    <div class="element-label label-left-3">CONVERSION DATA</div>
                                </div>
                                <!-- Right Side Elements - Ad Platforms (Semicircle) -->
                                <div class="animation-element adzeta-ai-right-1">
                                    <img src="images/platform-1.png" alt="Predictive LTV Generated" class="w-100">
                                    <div class="element-label label-right-1">Predictive LTV</div>
                                </div>
                                <div class="animation-element adzeta-ai-right-2">
                                    <img src="images/platform-2.png" alt="VBB Enhanced" class="w-100">
                                    <div class="element-label label-right-2">VBB Enhanced</div>
                                </div>
                                <div class="animation-element adzeta-ai-right-3">
                                    <img src="images/platform-3.png" alt="Optimized Profit / ROAS" class="w-100">
                                    <div class="element-label label-right-3">Optimized ROAS</div>
                                </div>
                                <!-- Connection Lines - Left Side -->
                                <div class="connection-line connection-left-1"></div>
                                <div class="connection-line connection-left-2"></div>
                                <div class="connection-line connection-left-3"></div>
                                <!-- Connection Lines - Right Side -->
                                <div class="connection-line connection-right-1"></div>
                                <div class="connection-line connection-right-2"></div>
                                <div class="connection-line connection-right-3"></div>
                                <!-- Data Flow Particles - Left Side -->
                                <div class="data-particle particle-left-1"></div>
                                <div class="data-particle particle-left-1-delay"></div>
                                <div class="data-particle particle-left-2"></div>
                                <div class="data-particle particle-left-2-delay"></div>
                                <div class="data-particle particle-left-3"></div>
                                <div class="data-particle particle-left-3-delay"></div>
                                <!-- Data Flow Particles - Right Side -->
                                <div class="data-particle particle-right-1"></div>
                                <div class="data-particle particle-right-1-delay"></div>
                                <div class="data-particle particle-right-2"></div>
                                <div class="data-particle particle-right-2-delay"></div>
                                <div class="data-particle particle-right-3"></div>
                                <div class="data-particle particle-right-3-delay"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end hero section -->
        <!-- start Data Source Categories & Content -->
        <section id="down-section" class="position-relative overflow-hidden">
            <div class="container">
                <div class="row justify-content-center mb-50px">
                    <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                        <div class="scale-tag-container">
                            <div class="scale-tag">INTEGRATIONS</div>
                        </div>
                        <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Fueling Precision with Your E-commerce Data</span></h3>
                        <p>To build highly accurate LTV models tailored to your business, Adzeta securely integrates with the key data sources you already use. We prioritize leveraging your rich first-party information.</p>
                    </div>
                </div>
                <!-- E-commerce Platforms -->
                <div class="row align-items-center mb-50px">
                    <div class="col-lg-5 md-mb-50px" data-anime='{ "translateY": [0, 0], "perspective": [1200,1200], "scale": [1.1, 1], "rotateX": [50, 0], "opacity": [0,0.7], "duration": 800, "delay": 0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <div class="d-flex flex-column box-shadow-double-large border-radius-8px overflow-hidden">
                            <div class="row row-cols-1 justify-content-center m-0">
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center  justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/shopify.svg" alt="Shopify" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/magento.svg" alt="Magento" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/woocommerce.svg" alt="WooCommerce" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/bigcommerce.svg" alt="BigCommerce" height="30">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5 offset-lg-1 col-lg-6" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay":0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <h3 class="text-dark-gray fw-600 ls-minus-1px">E-commerce Platform Data</h3>
                        <p class="w-95 lg-w-100 mb-35px">Direct connections to your store data provide crucial transactional history, including order values, purchase frequency, product details, and customer information, essential for LTV modeling.</p>
                    </div>
                </div>
                <!-- Data Warehouses & Lakes -->
                <div class="row align-items-center mb-50px flex-row-reverse">
                    <div class="col-lg-5 md-mb-50px" data-anime='{ "translateY": [0, 0], "perspective": [1200,1200], "scale": [1.1, 1], "rotateX": [50, 0], "opacity": [0,0.7], "duration": 800, "delay": 200, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <div class="d-flex flex-column box-shadow-double-large border-radius-8px overflow-hidden">
                            <div class="row row-cols-1 justify-content-center m-0">
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/snowflake.svg" alt="Snowflake" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/bigquery.svg" alt="Google BigQuery" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/redshift.svg" alt="Amazon Redshift" height="30">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5 offset-lg-1 col-lg-6" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay":200, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <h3 class="text-dark-gray fw-600 ls-minus-1px">Data Warehouses & Lakes</h3>
                        <p class="w-95 lg-w-100 mb-35px">Seamlessly connect to your existing data infrastructure. Adzeta can integrate with leading cloud warehouses to access comprehensive historical data for robust model training.</p>
                    </div>
                </div>
                <!-- Website & App Analytics -->
                <div class="row align-items-center mb-50px">
                    <div class="col-lg-5 md-mb-50px" data-anime='{ "translateY": [0, 0], "perspective": [1200,1200], "scale": [1.1, 1], "rotateX": [50, 0], "opacity": [0,0.7], "duration": 800, "delay": 400, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <div class="d-flex flex-column box-shadow-double-large border-radius-8px overflow-hidden">
                            <div class="row row-cols-1 justify-content-center m-0">
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/ga4.svg" alt="Google Analytics (GA4)" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/segment.svg" alt="Segment" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/tealium.svg" alt="Tealium" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/amplitude.svg" alt="Amplitude" height="30">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5 offset-lg-1 col-lg-6" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay":400, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <h3 class="text-dark-gray fw-600 ls-minus-1px">Website & App Analytics</h3>
                        <p class="w-95 lg-w-100 mb-35px">We capture critical behavioral signals – page views, session duration, add-to-carts, custom events – through secure integrations with your analytics tools or via our own lightweight tracking.</p>
                    </div>
                </div>
                <!-- CRM & Customer Data Platforms -->
                <div class="row align-items-center flex-row-reverse">
                    <div class="col-lg-5 md-mb-50px" data-anime='{ "translateY": [0, 0], "perspective": [1200,1200], "scale": [1.1, 1], "rotateX": [50, 0], "opacity": [0,0.7], "duration": 800, "delay": 600, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <div class="d-flex flex-column box-shadow-double-large border-radius-8px overflow-hidden">
                            <div class="row row-cols-1 justify-content-center m-0">
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/hubspot.svg" alt="HubSpot" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/salesforce.svg" alt="Salesforce" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 border-bottom border-color-extra-medium-gray bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/klaviyo.png" alt="Klaviyo" height="30">
                                    </div>
                                </div>
                                <div class="col p-5 bg-white last-paragraph-no-margin">
                                    <div class="d-flex align-items-center justify-content-center text-dark-gray">
                                        <img class="h-50px md-h-45px sm-h-50px" src="images/logos/customerio.png" alt="Customer.io" height="30">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5 offset-lg-1 col-lg-6" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay":600, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <h3 class="text-dark-gray fw-600 ls-minus-1px">CRM & Customer Data Platforms</h3>
                        <p class="w-95 lg-w-100 mb-35px">Enhance LTV predictions by incorporating valuable customer attributes, segmentation data, and lifecycle information from your CRM or CDP.</p>
                    </div>
                </div>
            </div>
            <img src="images/demo-corporate-services-bg-01.webp" class="position-absolute top-80px left-170px opacity-7 z-index-minus-1" data-bottom-top="transform: rotate(0deg) translateY(0)" data-top-bottom="transform:rotate(-15deg) translateY(0)" alt=""/>
        </section>
        <!-- end Data Source Categories & Content -->
        <!-- start section: Ad Networks Integration -->
        <section class="ad-networks-section  position-relative overflow-hidden  pb-0">
            <div class="light-gradient-container position-absolute w-100 h-100"
                style="background: linear-gradient(to bottom, #eae8e6 0%, #ffffff 100%); z-index: 0;"></div>
            <div class="light-accent-gradient position-absolute w-100 h-100"
                style="background: 
                radial-gradient(circle at center, rgba(233,88,161,0.05) 0%, rgba(255,255,255,0) 70%),
                radial-gradient(circle at bottom right, rgba(143,118,245,0.03) 0%, rgba(255,255,255,0) 70%);
                z-index: 0;"></div>
            <div id="particles-style-06" class="h-100 position-absolute left-0px top-0 w-100" style="z-index: 1;" data-particle="true" data-particle-options='{"particles": {"number": {"value": 10,"density": {"enable": true,"value_area": 800}},"color": {"value": ["#f7afbd", "#e958a1", "#c5d8f8", "#8f76f5"]},"shape": {"type": "triangle","stroke":{"width":0,"color":"#000000"}},"opacity": {"value": 0.3,"random": false,"anim": {"enable": false,"speed": 1,"sync": false}},"size": {"value": 20,"random": true,"anim": {"enable": false,"sync": true}},"line_linked":{"enable":false,"distance":0,"color":"#ffffff","opacity":0.4,"width":1},"move": {"enable": true,"speed":1,"direction": "top","random": false,"straight": false}},"interactivity": {"detect_on": "canvas","events": {"onhover": {"enable": false,"mode": "repulse"},"onclick": {"enable": false,"mode": "push"},"resize": true}},"retina_detect": false}'></div>
            <div class="container position-relative" style="z-index: 1;">
                <div class="row justify-content-center mb-0 mt-60px">
                    <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                        <div class="scale-tag-container">
                            <div class="scale-tag">INTEGRATIONS</div>
                        </div>
                        <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Driving Performance on Major Ad Networks</span></h3>
                        <p>Adzeta's predictive LTV signals are seamlessly passed to your key advertising platforms, enhancing their native Value-Based Bidding capabilities for superior profit-driven results.</p>
                    </div>
                </div>
                <!-- Ad Platform Blocks with Central Image -->
                <div class="ad-networks-wrapper position-relative" style="height: 1000px;">
                    <!-- Central Image -->
                    <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 2;">
                        <img class="central-image" src="images/bg-ads-platforms.png" alt="Ad Networks Integration" style="max-width: 550px; animation: fadeInAnimation 1.5s ease-out forwards, floatAnimation 6s ease-in-out infinite 1.5s;">
                      
                    </div>
                    <!-- Block 1: Google Ads - Top Left -->
                    <div class="ad-platform-card position-absolute" style="top: 100px; left: 20px;" data-anime='{ "translateY": [-30, 0], "opacity": [0,1], "duration": 800, "delay": 200 }'>
                        <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                            <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                            <div class="d-flex align-items-center mb-20px position-relative">
                                <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                    <img src="images/logos/google-ads.svg" alt="Google Ads" height="40" style="filter: drop-shadow(0px 2px 4px rgba(0,0,0,0.1));">
                                </div>
                                <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Google Ads</h3>
                            </div>
                            <p class="mb-20px position-relative fs-15 lh-24 text-left">We feed predictive LTV signals directly into Google Ads via secure API integrations to supercharge your tROAS and Maximize Conversion Value campaigns.</p>
                            <div class="text-left position-relative">
                                <a href="/services/ecommerce-ppc" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Google Ads →</a>
                            </div>
                        </div>
                    </div>
                    <!-- Block 2: Meta Ads - Top Right -->
                    <div class="ad-platform-card position-absolute" style="top: 180px; right: 20px;" data-anime='{ "translateY": [-30, 0], "opacity": [0,1], "duration": 800, "delay": 300 }'>
                        <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                            <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                            <div class="d-flex align-items-center mb-20px position-relative">
                                <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                    <img src="images/logos/meta-ads.svg" alt="Meta Ads" height="40" style="filter: drop-shadow(0px 2px 4px rgba(0,0,0,0.1));">
                                </div>
                                <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Meta Ads</h3>
                            </div>
                            <p class="mb-20px position-relative fs-15 lh-24 text-left">Enhance Meta's Value Optimization bidding by providing accurate LTV predictions through the Conversions API (CAPI) for higher-value customers.</p>
                            <div class="text-left position-relative">
                                <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Meta Ads →</a>
                            </div>
                        </div>
                    </div>
                    <!-- Block 3: TikTok Ads - Bottom Left -->
                    <div class="ad-platform-card position-absolute" style="bottom: 180px; left: 20px;" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 400 }'>
                        <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                            <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                            <div class="d-flex align-items-center mb-20px position-relative">
                                <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                    <img src="images/logos/tiktok-ads.svg" alt="TikTok Ads" height="40" style="filter: drop-shadow(0px 2px 4px rgba(0,0,0,0.1));">
                                </div>
                                <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">TikTok Ads</h3>
                            </div>
                            <p class="mb-20px position-relative fs-15 lh-24 text-left">Power your TikTok Value-Based Optimization (VBO) campaigns with predictive LTV, ensuring your budget targets users most likely to deliver long-term returns.</p>
                            <div class="text-left position-relative">
                                <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about TikTok Ads →</a>
                            </div>
                        </div>
                    </div>
                    <!-- Block 4: Programmatic DSPs - Bottom Right -->
                    <div class="ad-platform-card position-absolute" style="bottom: 100px; right: 20px;" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 500 }'>
                        <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                            <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                            <div class="d-flex align-items-center mb-20px position-relative">
                                <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                    <img src="images/logos/programmatic-ads.png" alt="Programmatic Advertising" height="40" style="filter: drop-shadow(0px 2px 4px rgba(0,0,0,0.1));">
                                </div>
                                <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Programmatic DSPs</h3>
                            </div>
                            <p class="mb-20px position-relative fs-15 lh-24 text-left">Extend LTV-driven optimization to your programmatic display and video campaigns through integration with leading DSPs for comprehensive reach.</p>
                            <div class="text-left position-relative">
                                <a href="/services/programmatic" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Programmatic →</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Mobile Version (will be shown only on small screens) -->
                <div class="d-block d-lg-none">
                    <div class="text-center mb-30px">
                        <img class="central-image" src="images/bg-ads-platforms.png" alt="Ad Networks Integration" style="max-width: 400px;">
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-12 mb-30px">
                            <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                                <div class="d-flex align-items-center mb-20px">
                                    <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                        <img src="images/logos/google-ads.svg" alt="Google Ads" height="40">
                                    </div>
                                    <h3 class="alt-font fw-600 fs-20 mb-0">Google Ads</h3>
                                </div>
                                <p class="mb-20px text-left">We feed predictive LTV signals directly into Google Ads via secure API integrations to supercharge your tROAS and Maximize Conversion Value campaigns.</p>
                                <div class="text-left">
                                    <a href="/services/ecommerce-ppc" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Google Ads →</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12 mb-30px">
                            <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                                <div class="d-flex align-items-center mb-20px">
                                    <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                        <img src="images/logos/meta-ads.svg" alt="Meta Ads" height="40">
                                    </div>
                                    <h3 class="alt-font fw-600 fs-20 mb-0">Meta Ads</h3>
                                </div>
                                <p class="mb-20px text-left">Enhance Meta's Value Optimization bidding by providing accurate LTV predictions through the Conversions API (CAPI) for higher-value customers.</p>
                                <div class="text-left">
                                    <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Meta Ads →</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12 mb-30px">
                            <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                                <div class="d-flex align-items-center mb-20px">
                                    <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                        <img src="images/logos/tiktok-ads.svg" alt="TikTok Ads" height="40">
                                    </div>
                                    <h3 class="alt-font fw-600 fs-20 mb-0">TikTok Ads</h3>
                                </div>
                                <p class="mb-20px text-left">Power your TikTok Value-Based Optimization (VBO) campaigns with predictive LTV, ensuring your budget targets users most likely to deliver long-term returns.</p>
                                <div class="text-left">
                                    <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about TikTok Ads →</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12 mb-30px">
                            <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                                <div class="d-flex align-items-center mb-20px">
                                    <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                        <img src="images/logos/programmatic-ads.png" alt="Programmatic Advertising" height="40">
                                    </div>
                                    <h3 class="alt-font fw-600 fs-20 mb-0">Programmatic</h3>
                                </div>
                                <p class="mb-20px text-left">Extend LTV-driven optimization to your programmatic display and video campaigns through integration with leading DSPs for comprehensive reach.</p>
                                <div class="text-left">
                                    <a href="/services/programmatic" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Programmatic →</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
               
                
            </div>
        </section>
        <!-- end section: Ad Networks Integration -->
        <!-- start section 5: Getting Started -->
        <section id="get-started" class="position-relative overflow-hidden pb-0 overflow-hidden">
            <!-- Match the background style from the Ad Networks section -->
            <div class="light-gradient-container position-absolute w-100 h-100"
                style="background: linear-gradient(to bottom, #ffffff 0%, #eae8e6 100%); z-index: 0;"></div>
            <div class="light-accent-gradient position-absolute w-100 h-100"
                style="background: 
                radial-gradient(circle at center, rgba(233,88,161,0.05) 0%, rgba(255,255,255,0) 70%),
                radial-gradient(circle at top right, rgba(143,118,245,0.03) 0%, rgba(255,255,255,0) 70%);
                z-index: 0;"></div>
            <div class="floating-element floating-2" style="bottom: -100px; right: -100px;"></div>
            <div class="container position-relative" style="z-index: 2;">
                <div class="row justify-content-center mb-50px mt-50px">
                    <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                        <div class="scale-tag-container">
                            <div class="scale-tag">UNLOCK HIGHER ROAS</div>
                        </div>
                        <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Seamless Integration for Predictive Ad Spend & LTV Maximization</span></h3>
                        <p class="scale-description">Stop guessing with your ad spend. Adzeta connects your e-commerce data to our AI, empowering ad platforms with the LTV intelligence they need to find your most profitable customers. Our onboarding is secure, swift, and fully supported by e-commerce advertising specialists.</p>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-12" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 100 }'>
                        <div class="enterprise-implementation-process">
                            <div class="row implementation-timeline-container">
                                <!-- Left Column: Timeline Steps -->
                                <div class="col-lg-6 col-md-12">
                                    <div class="process-header text-center">
                                        <h4 class="alt-font fw-600 text-dark-gray">Your 4-Step Path to LTV-Powered Campaigns</h4>
                                        <p class="mb-0">See our streamlined approach to putting your LTV data to work, faster.</p>
                                    </div>
                                    <div class="timeline-steps-wrapper">
                                        <!-- Step 1 -->
                                        <div class="timeline-step active" data-step="1" data-anime='{ "translateX": [-20, 0], "opacity": [0,1], "duration": 600, "delay": 100 }'>
                                            <div class="timeline-step-connector">
                                                <div class="connector-line"></div>
                                                <div class="connector-dot"></div>
                                            </div>
                                            <div class="timeline-step-content">
                                                <div class="step-number">01</div>
                                                <div class="step-details">
                                                    <h5>Secure Data Ingestion</h5>
                                                    <p>Effortlessly link your e-commerce platform (Shopify, WooCommerce, etc.), CRM, and ad accounts via our robust, code-free integrations. Your data privacy is paramount.</p>
                                                    <!-- Revised Timeline: Acknowledges client-side factors and technical setup. -->
                                                    <div class="timeline-indicator">1-2 Business Days (post-access)</div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Step 2 -->
                                        <div class="timeline-step" data-step="2" data-anime='{ "translateX": [-20, 0], "opacity": [0,1], "duration": 600, "delay": 200 }'>
                                            <div class="timeline-step-connector">
                                                <div class="connector-line"></div>
                                                <div class="connector-dot"></div>
                                            </div>
                                            <div class="timeline-step-content">
                                                <div class="step-number">02</div>
                                                <div class="step-details">
                                                    <h5>Predictive LTV Modeling</h5>
                                                    <p>Adzeta’s proprietary AI analyzes your historical customer and sales data to build sophisticated, custom LTV models, fine-tuned for your unique business.</p>
                                                    <!-- Revised Timeline: AI model training and validation takes time, especially "custom" models. -->
                                                    <div class="timeline-indicator">2-4 Business Days (data dependent)</div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Step 3 -->
                                        <div class="timeline-step" data-step="3" data-anime='{ "translateX": [-20, 0], "opacity": [0,1], "duration": 600, "delay": 300 }'>
                                            <div class="timeline-step-connector">
                                                <div class="connector-line"></div>
                                                <div class="connector-dot"></div>
                                            </div>
                                            <div class="timeline-step-content">
                                                <div class="step-number">03</div>
                                                <div class="step-details">
                                                    <h5>Ad Platform Activation</h5>
                                                    <p>We seamlessly feed these dynamic LTV predictions as real-time conversion value signals directly into your Google Ads, Meta Ads, and other key ad platforms.</p>
                                                    <!-- Revised Timeline: Configuration and API sync.-->
                                                    <div class="timeline-indicator">Within 1 Business Day (post-model)</div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Step 4 -->
                                        <div class="timeline-step" data-step="4" data-anime='{ "translateX": [-20, 0], "opacity": [0,1], "duration": 600, "delay": 400 }'>
                                            <div class="timeline-step-connector">
                                                <div class="connector-line"></div>
                                                <div class="connector-dot"></div>
                                            </div>
                                            <div class="timeline-step-content">
                                                <div class="step-number">04</div>
                                                <div class="step-details">
                                                    <h5>Launch & Algorithm Training</h5>
                                                    <p>Activate Value-Based Bidding (VBB). Ad platform algorithms begin leveraging Adzeta's LTV data. Monitor performance as campaigns exit their initial learning phase (typically 5-14 days).</p>
                                                    <!-- Revised Timeline: Explicitly mentions the ad platform learning phase. -->
                                                    <div class="timeline-indicator highlight">Optimization Begins Immediately*</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Right Column: Illustration -->
                                <div class="col-lg-6 col-md-12">
                                    <div class="implementation-illustration" data-anime='{ "opacity": [0,1], "scale": [0.9, 1], "duration": 800, "delay": 500 }'>
                                        <div class="illustration-container">
                                            <div class="implementation-image">
                                                <img src="images/adzeta-step-process.png">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="process-footer text-center">
                                <div class="btn btn-large btn-gradient-pink-orange btn-rounded mb-15px fw-600 alt-font">
                                    <span> 
                                    <span class="btn-text">Dedicated Onboarding & Strategy Support</span> 
                                    <span class="btn-icon"> <i class="bi bi-headset"></i></i></span> 
                                    </span>
                                </div>
                                <!-- Revised Footer: Sums up the initial Adzeta setup time, leading into platform learning. -->
                                <p class="mt-10px fs-15 opacity-7">Expect LTV signals to be live in your ad accounts within approximately 4-7 business days, initiating the platform optimization process. <br> *Improved ROAS typically observed post ad platform learning phase.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Add custom CSS for the timeline indicators and animations -->
			<style>
		
		@keyframes floatAnimation {
		0% { transform: translateY(0px); }
		50% { transform: translateY(-15px); }
		100% { transform: translateY(0px); }
		}
		@keyframes fadeInAnimation {
		0% { opacity: 0; transform: scale(0.95); }
		100% { opacity: 1; transform: scale(1); }
		}
                      
		  @media (max-width: 991px) {
			.ad-networks-wrapper {
			height: auto !important;
			display: none;
			}
			}
			@media (min-width: 992px) {
			.ad-platform-card {
			width: 420px;
			z-index: 3;
			}
			}
			.card-inner {
			transition: transform 0.3s ease, border-color 0.3s ease;
			border: 1px solid rgba(0, 0, 0, 0.05);
			}
			.card-inner:hover {
			transform: translateY(-5px);
			border-color: rgba(233, 88, 161, 0.1);
			}
			.border-radius-24px {
			border-radius: 24px !important;
			}
                /* Timeline indicators */
                .timeline-indicator {
                display: inline-block;
                background: linear-gradient(to right, #e958a1, #ff5d74);
                color: white;
                font-size: 12px;
                padding: 4px 12px;
                border-radius: 20px;
                margin-top: 8px;
                font-weight: 500;
                }
                /* Journey steps styling */
                .onboarding-journey .journey-step {
                position: relative;
                margin-bottom: 30px;
                display: flex;
                align-items: flex-start;
                }
                /* Step icon container */
                .step-icon-container {
                position: relative;
                width: 60px;
                height: 60px;
                background: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 20px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
                z-index: 2;
                flex-shrink: 0;
                }
                /* Step number */
                .step-number {
                position: absolute;
                top: -5px;
                right: -5px;
                width: 24px;
                height: 24px;
                background: linear-gradient(to right, #e958a1, #ff5d74);
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: 600;
                }
                /* Step icon */
                .step-icon-container i {
                font-size: 24px;
                background: linear-gradient(to right, #e958a1, #ff5d74);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
                color: transparent;
                }
                /* Step content */
                .step-content {
                flex-grow: 1;
                padding-top: 5px;
                }
                /* Connecting line between steps */
                .onboarding-journey .journey-step:not(:last-child):after {
                content: '';
                position: absolute;
                top: 60px;
                left: 30px;
                height: calc(100% - 30px);
                width: 2px;
                background: linear-gradient(to bottom, rgba(233, 88, 161, 0.7), rgba(255, 93, 116, 0.3));
                z-index: 1;
                }
                /* Enterprise Implementation Process */
                .enterprise-implementation-process {
                padding: 60px 0;
                max-width: 1200px;
                margin: 0 auto;
                position: relative;
                }
                .process-header {
                margin-bottom: 60px;
                }
                .process-header h4 {
                margin-bottom: 10px;
                font-size: 22px;
                letter-spacing: -0.5px;
                }
                .process-header p {
                color: #666;
                font-size: 17px;
                }
                /* Implementation Timeline Container */
                .implementation-timeline-container {
                position: relative;
                padding: 30px 0;
                margin-bottom: 40px;
                }
                /* Timeline Steps Wrapper */
                .timeline-steps-wrapper {
                position: relative;
                padding: 30px 0;
                margin-right: 30px;
                }
                /* Timeline Step */
                .timeline-step {
                position: relative;
                padding: 25px 0 25px 40px;
                margin-bottom: 20px;
                transition: all 0.3s ease;
                }
                .timeline-step:last-child {
                margin-bottom: 0;
                }
                .timeline-step:hover {
                cursor: pointer;
                }
                /* Timeline Step Connector */
                .timeline-step-connector {
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 30px;
                }
                /* Create a continuous vertical line for all steps */
                .timeline-steps-wrapper::before {
                content: '';
                position: absolute;
                left: 14px;
                top: 0;
                bottom: 0;
                width: 2px;
                background: linear-gradient(to bottom, rgba(233, 88, 161, 0.7), rgba(255, 93, 116, 0.3));
                z-index: 1;
                }
                /* Individual connector lines are now hidden since we use the continuous line */
                .connector-line {
                display: none;
                }
                .connector-dot {
                position: absolute;
                left: 10px;
                top: 65px;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: white;
                border: 2px solid #e958a1;
                z-index: 2;
                transition: all 0.3s ease;
                box-shadow: 0 0 0 4px rgba(233, 88, 161, 0.1);
                }
                .timeline-step.active .connector-dot {
                background: #e958a1;
                box-shadow: 0 0 0 4px rgba(233, 88, 161, 0.2);
                }
                .timeline-step:hover .connector-dot {
                background: #e958a1;
                box-shadow: 0 0 0 4px rgba(233, 88, 161, 0.2);
                }
                /* Timeline Step Content */
                .timeline-step-content {
                display: flex;
                align-items: flex-start;
                background: white;
                border-radius: 24px;
                padding: 25px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.06);
                transition: all 0.4s ease;
                border: 1px solid rgba(233, 88, 161, 0.05);
                }
                .timeline-step.active .timeline-step-content {
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
                border-left: 4px solid #e958a1;
                background: linear-gradient(to right, rgba(233, 88, 161, 0.02), white);
                }
                .timeline-step:hover .timeline-step-content {
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
                border-left: 4px solid #e958a1;
                background: linear-gradient(to right, rgba(233, 88, 161, 0.02), white);
                }
                /* Step Number */
                .step-number {
                width: 56px;
                height: 56px;
                background: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 22px;
                font-weight: 700;
                color: #e958a1;
                margin-right: 22px;
                box-shadow: 0 6px 15px rgba(0, 0, 0, 0.06);
                transition: all 0.3s ease;
                flex-shrink: 0;
                border: 1px solid rgba(233, 88, 161, 0.1);
                position: relative;
                }
                .timeline-step.active .step-number {
                background: linear-gradient(to right, #e958a1, #ff5d74);
                color: white;
                box-shadow: 0 6px 15px rgba(233, 88, 161, 0.3);
                }
                .timeline-step:hover .step-number {
                background: linear-gradient(to right, #e958a1, #ff5d74);
                color: white;
                box-shadow: 0 6px 15px rgba(233, 88, 161, 0.3);
                }
                /* Step Details */
                .step-details {
                flex-grow: 1;
                }
                .step-details h5 {
                font-size: 18px;
                font-weight: 600;
                margin: 0 0 10px 0;
                color: #333;
                }
                .step-details p {
                font-size: 15px;
                color: #666;
                line-height: 1.5;
                margin: 0 0 15px 0;
                }
                /* Timeline Indicator */
                .timeline-indicator {
                display: inline-block;
                background: #f8f8f8;
                color: #666;
                font-size: 13px;
                padding: 8px 18px;
                border-radius: 30px;
                font-weight: 500;
                margin-top: 5px;
                border: 1px solid rgba(0, 0, 0, 0.05);
                }
                .timeline-indicator.highlight {
                background: linear-gradient(to right, #e958a1, #ff5d74);
                color: white;
                box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
                border: none;
                }
                /* Implementation Illustration */
                .implementation-illustration {
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                }
                .illustration-container {
                width: 100%;
                max-width: 500px;
                margin: 0 auto;
                }
                .implementation-svg-container {
                width: 100%;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 300px; /* Provide minimum height */
                }
                .implementation-svg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                }
                /* For image content */
                .implementation-image {
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
                }
                /* SVG Animation Classes */
                .platform-circle {
                animation: gentlePulse 4s infinite ease-in-out;
                filter: drop-shadow(0 0 3px rgba(233, 88, 161, 0.3));
                }
                .connection-line {
                stroke-dasharray: 100;
                stroke-dashoffset: 100;
                animation: none;
                opacity: 0.4;
                transition: all 0.5s ease;
                }
                .connection-circle {
                opacity: 0.6;
                transition: all 0.5s ease;
                }
                .step-icon {
                transition: all 0.5s ease;
                }
                /* SVG Text Styling */
                .implementation-svg text {
                font-family: 'Inter', sans-serif;
                opacity: 0.9;
                }
                /* Timeline indicators styling */
                .timeline-indicators rect {
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                }
                /* Pulse circles for active elements */
                .pulse-circle {
                animation: none;
                transition: all 0.5s ease;
                }
                /* Active state styling */
                .connection-line.active {
                stroke-dashoffset: 0;
                opacity: 1;
                stroke-width: 3;
                filter: drop-shadow(0 0 3px rgba(233, 88, 161, 0.5));
                }
                .connection-circle.active {
                opacity: 1;
                filter: drop-shadow(0 0 5px rgba(233, 88, 161, 0.4));
                }
                .pulse-circle.active {
                animation: pulseFade 2s infinite ease-in-out;
                }
                /* Step-specific animations */
                .data-line.active, .data-circle.active, .step-1-icon.active {
                animation-delay: 0s;
                }
                .ai-line.active, .ai-circle.active, .step-2-icon.active {
                animation-delay: 0.1s;
                }
                .platform-line.active, .platform-circle.active, .step-3-icon.active {
                animation-delay: 0.2s;
                }
                .launch-line.active, .launch-circle.active, .step-4-icon.active {
                animation-delay: 0.3s;
                }
                /* Responsive SVG text adjustments */
                @media (max-width: 767px) {
                .implementation-svg-container {
                padding-bottom: 100%; /* Adjust aspect ratio for mobile */
                }
                .implementation-svg text {
                font-size: 90%; /* Slightly smaller text on mobile */
                }
                }
                @keyframes gentlePulse {
                0% {
                transform: scale(1);
                opacity: 0.9;
                }
                50% {
                transform: scale(1.02);
                opacity: 1;
                }
                100% {
                transform: scale(1);
                opacity: 0.9;
                }
                }
                @keyframes pulseFade {
                0% {
                opacity: 0;
                transform: scale(0.8);
                }
                50% {
                opacity: 0.5;
                transform: scale(1.1);
                }
                100% {
                opacity: 0;
                transform: scale(1.2);
                }
                }
                /* Process Footer */
                .process-footer {
                margin-top: 90px;
                text-align: center;
                }
                .support-badge {
                display: inline-flex;
                align-items: center;
                background: white;
                padding: 12px 25px;
                border-radius: 40px;
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
                font-weight: 500;
                color: #333;
                margin-bottom: 15px;
                font-size: 16px;
                }
                .support-badge i {
                margin-right: 10px;
                color: #e958a1;
                font-size: 20px;
                }
                /* Responsive adjustments */
                @media (max-width: 1199px) {
                .implementation-step {
                width: 24%;
                }
                .step-card {
                padding: 20px 15px;
                }
                .step-header h5 {
                font-size: 17px;
                }
                }
                @media (max-width: 991px) {
                .implementation-timeline-container {
                padding: 20px 0;
                }
                .timeline-steps-wrapper {
                margin-right: 0;
                margin-bottom: 40px;
                }
                .implementation-illustration {
                padding-top: 30px;
                }
                }
                @media (max-width: 767px) {
                .enterprise-implementation-process {
                padding: 40px 0;
                }
                .process-header {
                margin-bottom: 40px;
                }
                .process-header h4 {
                font-size: 24px;
                }
                /* Adjust timeline wrapper for mobile */
                .timeline-steps-wrapper {
                padding-left: 10px;
                }
                /* Adjust the continuous line position for mobile */
                .timeline-steps-wrapper::before {
                left: 12px;
                }
                .timeline-step {
                padding: 15px 0 15px 30px;
                margin-bottom: 25px;
                }
                .timeline-step-content {
                padding: 18px;
                }
                .connector-dot {
                left: -2px;
                top: 45px;
                }
                .step-number {
                width: 45px;
                height: 45px;
                font-size: 18px;
                margin-right: 15px;
                border-radius: 50%;
                }
                .step-details h5 {
                font-size: 16px;
                }
                .step-details p {
                font-size: 14px;
                }
                .timeline-indicator {
                font-size: 12px;
                padding: 6px 14px;
                }
                .implementation-svg-container {
                padding-bottom: 100%; /* Adjust aspect ratio for mobile */
                margin-top: 20px;
                }
                /* Add some space between timeline and illustration on mobile */
                .implementation-illustration {
                margin-top: 20px;
                }
                }
                @media (max-width: 575px) {
                .process-header h4 {
                font-size: 20px;
                }
                .process-header p {
                font-size: 15px;
                }
                /* Adjust timeline wrapper for small screens */
                .timeline-steps-wrapper {
                padding-left: 5px;
                }
                /* Adjust the continuous line position for small screens */
                .timeline-steps-wrapper::before {
                left: 10px;
                }
                .timeline-step {
                padding: 12px 0 12px 25px;
                margin-bottom: 20px;
                }
                .connector-dot {
                left: 2px;
                top: 68px;
                top: 68px;
                width: 8px;
                height: 8px;
                }
                .timeline-step-content {
                padding: 15px;
                border-radius: 18px;
                }
                .step-number {
                width: 40px;
                height: 40px;
                font-size: 16px;
                margin-right: 12px;
                border-radius: 50%;
                }
                .step-details h5 {
                font-size: 15px;
                margin-bottom: 8px;
                }
                .step-details p {
                font-size: 13px;
                margin-bottom: 10px;
                line-height: 1.4;
                }
                .timeline-indicator {
                font-size: 11px;
                padding: 5px 12px;
                }
                /* Keep the horizontal layout for better readability */
                .timeline-step-content {
                flex-direction: row;
                align-items: center;
                }
                /* Adjust illustration for small screens */
                .implementation-svg-container {
                padding-bottom: 120%; /* Taller aspect ratio for small screens */
                }
                .support-badge {
                font-size: 14px;
                padding: 10px 20px;
                }
                }
            
		</style>
                <!-- Timeline Steps Interaction Script -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Get all timeline steps
                const timelineSteps = document.querySelectorAll('.timeline-step');
            
                // Get SVG elements for animation control
                const connectionLines = document.querySelectorAll('.connection-line');
                const connectionCircles = document.querySelectorAll('.connection-circle');
                const stepIcons = document.querySelectorAll('.step-icon');
                const pulseCircles = document.querySelectorAll('.pulse-circle');
            
                // Function to reset all active states
                function resetActiveStates() {
                    connectionLines.forEach(line => line.classList.remove('active'));
                    connectionCircles.forEach(circle => circle.classList.remove('active'));
                    stepIcons.forEach(icon => icon.classList.remove('active'));
                    pulseCircles.forEach(pulse => pulse.classList.remove('active'));
                }
            
                // Function to highlight SVG elements based on active step
                function highlightSvgElements(stepIndex) {
                    // Reset all active states first
                    resetActiveStates();
            
                    // Apply active class based on the step index
                    if (stepIndex >= 0 && stepIndex < connectionLines.length) {
                        // Get the elements for this step
                        const elements = {
                            line: connectionLines[stepIndex],
                            circle: connectionCircles[stepIndex],
                            icon: document.querySelector(`.step-${stepIndex + 1}-icon`),
                            pulse: pulseCircles[stepIndex]
                        };
            
                        // Add active class to each element
                        if (elements.line) elements.line.classList.add('active');
                        if (elements.circle) elements.circle.classList.add('active');
                        if (elements.icon) elements.icon.classList.add('active');
                        if (elements.pulse) elements.pulse.classList.add('active');
                    }
                }
            
                // Add click event to each step
                timelineSteps.forEach((step, index) => {
                    step.addEventListener('click', function() {
                        // Remove active class from all steps
                        timelineSteps.forEach(s => s.classList.remove('active'));
            
                        // Add active class to clicked step
                        this.classList.add('active');
            
                        // Highlight corresponding SVG elements
                        highlightSvgElements(index);
            
                        // Smooth scroll to active step on mobile
                        if (window.innerWidth < 768) {
                            this.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }
                    });
                });
            
                // Initial activation
                if (timelineSteps.length > 0) {
                    timelineSteps[0].classList.add('active');
                    highlightSvgElements(0);
                }
            
                // Add intersection observer for animation on scroll
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            // When the section comes into view, ensure the active step is highlighted
                            const activeStep = document.querySelector('.timeline-step.active');
                            if (activeStep) {
                                const index = Array.from(timelineSteps).indexOf(activeStep);
                                highlightSvgElements(index);
                            } else if (timelineSteps.length > 0) {
                                // If no active step, activate the first one
                                timelineSteps[0].classList.add('active');
                                highlightSvgElements(0);
                            }
                        }
                    });
                }, { threshold: 0.3 });
            
                // Observe the timeline container
                const timelineContainer = document.querySelector('.implementation-timeline-container');
                if (timelineContainer) {
                    observer.observe(timelineContainer);
                }
            });
        </script>
        </section>
        <!-- end section 5 -->
        <!-- start enhanced footer -->
          <?php include 'footer.php'; ?>