/**
 * High-Tech Border Animation
 * Creates an animated gradient border effect
 * Based on the technique from ibelick.com
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add the CSS for animated gradient border
    const style = document.createElement('style');
    style.textContent = `
    /* Animated gradient border */
    .high-tech-border {
      position: relative;
      z-index: 0;
      border-radius: 20px;
      overflow: hidden;
    }

    /* Rotating gradient background */
    .high-tech-border::before {
      content: "";
      position: absolute;
      z-index: -2;
      left: -50%;
      top: -50%;
      width: 200%;
      height: 200%;
      background-color: transparent;
      background-repeat: no-repeat;
      background-size: 100% 100%, 50% 50%;
      background-position: 0 0, 100% 0, 100% 100%, 0 100%;
      background-image: linear-gradient(90deg, #e958a1, #ff5d74, #8f76f5, #e958a1);
      animation: gradient-rotate 4s linear infinite;
    }

    /* Inner background to create border effect */
    .high-tech-border::after {
      content: "";
      position: absolute;
      z-index: -1;
      left: 1px;
      top: 1px;
      width: calc(100% - 2px);
      height: calc(100% - 2px);
      background: rgba(255, 255, 255, 0.30);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border-radius: 19px;
    }

    /* Animation for rotating gradient */
    @keyframes gradient-rotate {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    /* Glow effect for the border */
    .high-tech-border {
      box-shadow: 0 0 15px rgba(233, 88, 161, 0.2);
    }

    /* Sparkle effect */
    .border-sparkle {
      position: absolute;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      background: #fff;
      box-shadow: 0 0 10px #e958a1, 0 0 20px #e958a1;
      z-index: 3;
      opacity: 0;
      pointer-events: none;
      animation: sparkle 1.5s ease-in-out infinite;
    }

    @keyframes sparkle {
      0% {
        opacity: 0;
        transform: scale(0);
      }
      50% {
        opacity: 0.8;
        transform: scale(1);
      }
      100% {
        opacity: 0;
        transform: scale(0);
      }
    }
    `;
    document.head.appendChild(style);

    // Find the high-tech border element
    const borderElement = document.querySelector('.high-tech-border');
    if (!borderElement) return;

    // Add sparkle effects
    function addSparkleEffects() {
        // Create 5 sparkle elements at fixed positions
        const sparklePositions = [
            { top: '0%', left: '25%' },
            { top: '0%', left: '75%' },
            { top: '100%', left: '25%' },
            { top: '100%', left: '75%' },
            { top: '50%', left: '0%' },
            { top: '50%', left: '100%' }
        ];

        sparklePositions.forEach((position, index) => {
            const sparkle = document.createElement('div');
            sparkle.className = 'border-sparkle';
            sparkle.style.top = position.top;
            sparkle.style.left = position.left;
            sparkle.style.animationDelay = `${index * 0.5}s`;

            // Random color from theme
            const colors = ['#e958a1', '#ff5d74', '#8f76f5'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            sparkle.style.background = randomColor;
            sparkle.style.boxShadow = `0 0 10px ${randomColor}, 0 0 20px ${randomColor}`;

            // Add to border element
            borderElement.appendChild(sparkle);
        });
    }

    // Initialize sparkle effects
    addSparkleEffects();
});
