/* Light Purple Theme Enhancement - Lighter background with purple accents */

/* Enhanced Hero Section Gradient */
.apple-inspired-gradient .gradient-backdrop {
    background: linear-gradient(125deg, #f8f9ff 0%, #f0f2ff 25%, #e8eaff 50%, #e0e2ff 75%, #d8daff 100%);
    opacity: 1;
}

/* Enhanced accent glows */
.accent-glow.top-right {
    background: radial-gradient(circle, rgba(143, 118, 245, 0.25) 0%, rgba(31, 33, 42, 0) 70%);
    opacity: 0.7;
    width: 600px;
    height: 600px;
}

.accent-glow.bottom-left {
    background: radial-gradient(circle, rgba(233, 88, 161, 0.2) 0%, rgba(31, 33, 42, 0) 70%);
    opacity: 0.7;
    width: 600px;
    height: 600px;
}

/* Enhanced tech grid */
.tech-grid {
    background-image: linear-gradient(rgba(143, 118, 245, 0.08) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(143, 118, 245, 0.08) 1px, transparent 1px);
    background-size: 60px 60px;
    opacity: 0.5;
}

/* Enhanced tech dots */
.tech-dots {
    background-image: radial-gradient(rgba(143, 118, 245, 0.1) 1px, transparent 1px);
    background-size: 30px 30px;
    opacity: 0.5;
}

/* Enhanced digital circuit elements */
.digital-circuit {
    opacity: 0.15;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10,10 L30,10 L30,30 L50,30 L50,50 L70,50 L70,70 L90,70' stroke='%238f76f5' fill='none' stroke-width='0.9'/%3E%3Cpath d='M10,90 L30,90 L30,70 L50,70 L50,50 L70,50 L70,30 L90,30' stroke='%23e958a1' fill='none' stroke-width='0.9'/%3E%3Ccircle cx='30' cy='30' r='2' fill='%238f76f5' /%3E%3Ccircle cx='50' cy='50' r='2' fill='%23e958a1' /%3E%3Ccircle cx='70' cy='70' r='2' fill='%238f76f5' /%3E%3Ccircle cx='30' cy='70' r='2' fill='%23e958a1' /%3E%3Ccircle cx='70' cy='30' r='2' fill='%238f76f5' /%3E%3C/svg%3E");
    background-size: 180px 180px;
}

/* Enhanced gradient text styling for light background - matching the image exactly */
.heading-gradient {
    background-image: linear-gradient(166deg, #004d5b 0%, #004d5b 40%, #8a3b6a 80%, #e9387c 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: none;
    font-weight: 700;
    letter-spacing: -3px;
    line-height: 1.1;
}

.heading-gradient .highlight-text {
    color: #e9387c;
    -webkit-text-fill-color: #e9387c;
    font-weight: 700;
}

/* Additional text color fixes */
.primary-font.fw-light.fs-16.w-90.sm-w-100.mb-40px.xs-mb-30px.text-white.opacity-85.lh-1-7.ls-wide {
    color: #333 !important;
    opacity: 0.85;
}

/* Text color adjustments for light background */
.hero-section .text-white,
.hero-section .opacity-85,
.hero-section h1,
.hero-section .section-subtitle {
    color: #333 !important;
}

.section-subtitle {
    color: #6a4fd9 !important;
    background: none;
    -webkit-background-clip: initial;
    background-clip: initial;
    -webkit-text-fill-color: initial;
}

.section-subtitle::after {
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
}

/* Enhanced platform section backgrounds */
.platform-section.bg-dark {
    background: linear-gradient(135deg, #f0f2ff 0%, #e8eaff 30%, #e0e2ff 60%, #d8daff 90%);
}

.platform-section.bg-dark::before {
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.15) 0%, rgba(31, 33, 42, 0) 60%);
    opacity: 0.7;
}

.platform-section.bg-dark::after {
    background: radial-gradient(circle at bottom left, rgba(233, 88, 161, 0.15) 0%, rgba(31, 33, 42, 0) 60%);
    opacity: 0.7;
}

/* Text color adjustments for dark sections */
.platform-section.bg-dark h1,
.platform-section.bg-dark h2,
.platform-section.bg-dark h3,
.platform-section.bg-dark p,
.platform-section.bg-dark .text-white {
    color: #333 !important;
}

/* Enhanced CTA section */
.platform-cta {
    background: linear-gradient(135deg, #e0e2ff 0%, #d8daff 30%, #d0d2ff 60%, #c8caff 90%);
}

.platform-cta::before {
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.2) 0%, rgba(31, 33, 42, 0) 70%);
    opacity: 0.7;
}

.platform-cta::after {
    background: radial-gradient(ellipse at bottom left, rgba(233, 88, 161, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
    opacity: 0.7;
}

/* Text color adjustments for CTA section */
.platform-cta .cta-title,
.platform-cta .cta-description,
.platform-cta .text-white {
    color: #333 !important;
}

/* Button styling for light theme */
.platform-cta .btn-white {
    background: linear-gradient(135deg, #6a4fd9 0%, #8f76f5 100%);
    color: white !important;
    border: none;
}

.platform-cta .btn-white:hover {
    background: linear-gradient(135deg, #5a3fd9 0%, #7f66f5 100%);
    transform: translateY(-3px);
}

/* Enhanced footer styling */
footer.bg-gradient-aztec-green {
    background-image: linear-gradient(to right bottom, #f0f2ff, #e8eaff, #e0e2ff, #d8daff, #d0d2ff);
}

footer .background-position-center-top {
    opacity: 0.1;
}

footer .divider-style-03 {
    background: linear-gradient(to right, rgba(143, 118, 245, 0.4), rgba(233, 88, 161, 0.4));
    height: 1px;
}

/* Text color adjustments for footer */
footer p,
footer a,
footer .text-white,
footer .opacity-7 {
    color: #333 !important;
}

footer .alt-font.fw-500.d-block.text-white.mb-10px {
    color: #6a4fd9 !important;
    font-weight: 600;
}

/* Enhanced particles */
#particles-style-03 {
    opacity: 0.7;
}

/* Enhanced floating elements */
.floating-element {
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
}

/* Enhanced feature boxes */
.platform-feature-box {
    background-color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(143, 118, 245, 0.1);
}

.platform-feature-box .icon-wrapper {
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
}

.platform-feature-box:hover .icon-wrapper {
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.15) 0%, rgba(143, 118, 245, 0.15) 100%);
}

/* Enhanced benefit cards */
.benefit-card {
    background-color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(143, 118, 245, 0.1);
}

.benefit-card.positive::before {
    background: linear-gradient(90deg, #e958a1 0%, #d15ec7 100%);
}

.benefit-card.reduction::before {
    background: linear-gradient(90deg, #8f76f5 0%, #6a4fd9 100%);
}

.benefit-card.positive .metric {
    background: linear-gradient(90deg, #e958a1 0%, #d15ec7 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

.benefit-card.reduction .metric {
    background: linear-gradient(90deg, #8f76f5 0%, #6a4fd9 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* Enhanced process steps */
.process-step .step-number {
    background: linear-gradient(135deg, #8f76f5 0%, #d15ec7 100%);
    box-shadow: 0 10px 20px rgba(143, 118, 245, 0.3);
}

/* Navbar adjustments for light theme */
.navbar.navbar-expand-lg.header-transparent.bg-transparent.header-reverse.glass-effect {
    background: rgba(248, 249, 255, 0.8) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.navbar .nav-link,
.navbar .dropdown-toggle {
    color: #333 !important;
}

.navbar .navbar-brand img.default-logo {
    display: none;
}

.navbar .navbar-brand img.alt-logo {
    display: block;
}

/* Button styling for light theme */
.apple-inspired-button {
    background: linear-gradient(135deg, #6a4fd9 0%, #8f76f5 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 8px 20px rgba(106, 79, 217, 0.3) !important;
}

.apple-inspired-button .btn-double-text {
    color: white !important;
}

.apple-inspired-button:hover {
    background: linear-gradient(135deg, #5a3fd9 0%, #7f66f5 100%) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 30px rgba(106, 79, 217, 0.4) !important;
}

/* Get Started button */
.btn-gradient-pink-orange {
    background-image: linear-gradient(to right, #6a4fd9, #8f76f5) !important;
    color: white !important;
    box-shadow: 0 8px 20px rgba(106, 79, 217, 0.3) !important;
}

.btn-gradient-pink-orange:hover {
    background-image: linear-gradient(to right, #5a3fd9, #7f66f5) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 30px rgba(106, 79, 217, 0.4) !important;
}

/* Light background for sections */
.platform-section.bg-light {
    background-color: #ffffff;
}
