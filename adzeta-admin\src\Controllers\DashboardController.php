<?php

namespace AdZetaAdmin\Controllers;

use AdZetaAdmin\Core\AdminApp;

/**
 * Dashboard Controller
 * Handles the main admin dashboard functionality
 */
class DashboardController
{
    private $app;
    private $db;

    public function __construct()
    {
        $this->app = new AdminApp();
        global $admin_db;
        $this->db = $admin_db;
    }

    public function index($params = [])
    {
        $data = [
            'title' => 'Dashboard',
            'stats' => $this->getDashboardStats(),
            'recent_posts' => $this->getRecentPosts(),
            'seo_alerts' => $this->getSEOAlerts(),
            'performance_metrics' => $this->getPerformanceMetrics(),
            'quick_actions' => $this->getQuickActions()
        ];

        $this->app->renderTemplate('dashboard/index', $data);
    }

    private function getDashboardStats()
    {
        $stats = [];

        try {
            // Blog post statistics
            $stats['total_posts'] = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_posts") ?: 0;
            $stats['published_posts'] = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_posts WHERE status = 'published'") ?: 0;
            $stats['draft_posts'] = $this->db->fetchColumn("SELECT COUNT(*) FROM blog_posts WHERE status = 'draft'") ?: 0;
            
            // View statistics
            $stats['total_views'] = $this->db->fetchColumn("SELECT SUM(view_count) FROM blog_posts WHERE view_count IS NOT NULL") ?: 0;
            $stats['avg_views'] = $stats['published_posts'] > 0 ? round($stats['total_views'] / $stats['published_posts']) : 0;
            
            // Recent activity
            $stats['posts_this_month'] = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM blog_posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)"
            ) ?: 0;
            
            $stats['posts_this_week'] = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM blog_posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)"
            ) ?: 0;

        } catch (Exception $e) {
            // Handle database errors gracefully
            error_log("Dashboard stats error: " . $e->getMessage());
            $stats = [
                'total_posts' => 0,
                'published_posts' => 0,
                'draft_posts' => 0,
                'total_views' => 0,
                'avg_views' => 0,
                'posts_this_month' => 0,
                'posts_this_week' => 0
            ];
        }

        return $stats;
    }

    private function getRecentPosts($limit = 5)
    {
        try {
            return $this->db->fetchAll(
                "SELECT id, title, slug, status, created_at, view_count, 
                        CASE 
                            WHEN meta_description IS NOT NULL AND meta_description != '' THEN 1 
                            ELSE 0 
                        END as has_meta_desc,
                        CASE 
                            WHEN featured_image IS NOT NULL AND featured_image != '' THEN 1 
                            ELSE 0 
                        END as has_featured_image
                 FROM blog_posts 
                 ORDER BY created_at DESC 
                 LIMIT ?", 
                [$limit]
            );
        } catch (Exception $e) {
            error_log("Recent posts error: " . $e->getMessage());
            return [];
        }
    }

    private function getSEOAlerts()
    {
        $alerts = [];

        try {
            // Check for posts without meta descriptions
            $noMetaDesc = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM blog_posts 
                 WHERE status = 'published' AND (meta_description IS NULL OR meta_description = '')"
            );
            
            if ($noMetaDesc > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'icon' => 'bi-exclamation-triangle',
                    'message' => "{$noMetaDesc} published posts missing meta descriptions",
                    'action' => ADZETA_ADMIN_URL . '/seo/missing-meta',
                    'action_text' => 'Fix Now'
                ];
            }

            // Check for posts without featured images
            $noFeaturedImage = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM blog_posts 
                 WHERE status = 'published' AND (featured_image IS NULL OR featured_image = '')"
            );
            
            if ($noFeaturedImage > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'icon' => 'bi-image',
                    'message' => "{$noFeaturedImage} published posts missing featured images",
                    'action' => ADZETA_ADMIN_URL . '/media/missing-images',
                    'action_text' => 'Add Images'
                ];
            }

            // Check for posts with short content
            $shortContent = $this->db->fetchColumn(
                "SELECT COUNT(*) FROM blog_posts 
                 WHERE status = 'published' AND CHAR_LENGTH(content) < 1000"
            );
            
            if ($shortContent > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'icon' => 'bi-file-text',
                    'message' => "{$shortContent} published posts have less than 1000 characters",
                    'action' => ADZETA_ADMIN_URL . '/seo/short-content',
                    'action_text' => 'Review Content'
                ];
            }

        } catch (Exception $e) {
            error_log("SEO alerts error: " . $e->getMessage());
        }

        return $alerts;
    }

    private function getPerformanceMetrics()
    {
        try {
            // Calculate average SEO score (placeholder for now)
            $avgSeoScore = 85; // This will be calculated by the SEO analyzer
            
            // Get top performing posts
            $topPosts = $this->db->fetchAll(
                "SELECT title, view_count, slug 
                 FROM blog_posts 
                 WHERE status = 'published' AND view_count > 0 
                 ORDER BY view_count DESC 
                 LIMIT 3"
            );

            return [
                'avg_seo_score' => $avgSeoScore,
                'top_posts' => $topPosts,
                'seo_score_trend' => '+5%', // Placeholder
                'traffic_trend' => '+12%'   // Placeholder
            ];

        } catch (Exception $e) {
            error_log("Performance metrics error: " . $e->getMessage());
            return [
                'avg_seo_score' => 0,
                'top_posts' => [],
                'seo_score_trend' => '0%',
                'traffic_trend' => '0%'
            ];
        }
    }

    private function getQuickActions()
    {
        return [
            [
                'title' => 'Create New Post',
                'description' => 'Start writing a new blog post',
                'icon' => 'bi-plus-circle',
                'url' => ADZETA_ADMIN_URL . '/blog/create',
                'color' => 'primary'
            ],
            [
                'title' => 'SEO Analysis',
                'description' => 'Analyze and optimize your content',
                'icon' => 'bi-search',
                'url' => ADZETA_ADMIN_URL . '/seo',
                'color' => 'success'
            ],
            [
                'title' => 'Media Library',
                'description' => 'Manage your images and files',
                'icon' => 'bi-images',
                'url' => ADZETA_ADMIN_URL . '/media',
                'color' => 'info'
            ],
            [
                'title' => 'Generate Sitemap',
                'description' => 'Update your XML sitemap',
                'icon' => 'bi-diagram-3',
                'url' => ADZETA_ADMIN_URL . '/seo/sitemap',
                'color' => 'warning'
            ]
        ];
    }
}
