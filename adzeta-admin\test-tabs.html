<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Case Study Editor Tabs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/admin.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Case Study Editor Tabs Test</h1>
        
        <div class="case-study-editor">
            <!-- Editor Header -->
            <div class="editor-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2>New Case Study</h2>
                        <div class="editor-status">
                            <span class="status-indicator saved">All changes saved</span>
                        </div>
                    </div>
                    <div class="editor-actions">
                        <button class="btn btn-outline-secondary me-2">
                            <i class="fas fa-eye me-1"></i> Preview
                        </button>
                        <button class="btn btn-primary me-2">
                            <i class="fas fa-save me-1"></i> Save
                        </button>
                    </div>
                </div>
            </div>

            <!-- Horizontal Tab Navigation -->
            <div class="editor-tabs-container">
                <ul class="editor-tabs" id="editorTabs">
                    <li>
                        <a class="nav-link active" data-section="basic" href="#basic">
                            <i class="fas fa-info-circle"></i> Basic Info
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" data-section="hero" href="#hero">
                            <i class="fas fa-star"></i> Hero Section
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" data-section="highlights" href="#highlights">
                            <i class="fas fa-list"></i> Project Highlights
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" data-section="challenge" href="#challenge">
                            <i class="fas fa-exclamation-triangle"></i> Challenge
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" data-section="results" href="#results">
                            <i class="fas fa-chart-line"></i> Results
                        </a>
                    </li>
                    <li>
                        <a class="nav-link" data-section="seo" href="#seo">
                            <i class="fas fa-search"></i> SEO
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Editor Content Area -->
            <div class="editor-content">
                <!-- Basic Info Section -->
                <div class="editor-section active" id="basic">
                    <h4>Basic Information</h4>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label class="form-label">Case Study Title *</label>
                            <input type="text" class="form-control" name="title" 
                                   placeholder="Client Name Achieves X% Growth Through...">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="status">
                                <option value="draft">Draft</option>
                                <option value="published">Published</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Hero Section -->
                <div class="editor-section" id="hero">
                    <h4>Hero Section</h4>
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label class="form-label">Main Title</label>
                            <textarea class="form-control" name="hero_title" rows="2" 
                                      placeholder="Compelling main title for the case study..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Other sections would go here -->
                <div class="editor-section" id="highlights">
                    <h4>Project Highlights</h4>
                    <p>Project highlights content...</p>
                </div>

                <div class="editor-section" id="challenge">
                    <h4>Challenge Section</h4>
                    <p>Challenge content...</p>
                </div>

                <div class="editor-section" id="results">
                    <h4>Results Section</h4>
                    <p>Results content...</p>
                </div>

                <div class="editor-section" id="seo">
                    <h4>SEO Section</h4>
                    <p>SEO content...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple tab functionality for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            // Tab navigation
            document.querySelectorAll('#editorTabs .nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionName = this.dataset.section;
                    console.log('Tab clicked:', sectionName);
                    
                    // Update navigation
                    document.querySelectorAll('#editorTabs .nav-link').forEach(l => {
                        l.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // Update content
                    document.querySelectorAll('.editor-section').forEach(section => {
                        section.classList.remove('active');
                    });
                    
                    const activeSection = document.getElementById(sectionName);
                    if (activeSection) {
                        activeSection.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>
