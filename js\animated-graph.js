/**
 * Refined Animated ROI Visualization JS - Apple-Inspired (Optimized)
 * Creates an elegant, minimalist comparison graph for Adzeta's AI-powered bidding vs. traditional methods.
 * Features clean sequential animation, subtle annotations, and focused visual storytelling.
 * Optimized for performance and to prevent repeated animations on mobile viewport entry.
 */

document.addEventListener('DOMContentLoaded', function () {
    const graphContainer = document.querySelector('.animated-graph-container');
    if (!graphContainer) {
        console.error('Animated graph container not found.');
        return;
    }

    let graphCanvas = document.getElementById('roi-graph-canvas');
    if (!graphCanvas) {
        graphCanvas = document.createElement('canvas');
        graphCanvas.id = 'roi-graph-canvas';
        // Apply styles directly as they were in the original
        graphCanvas.style.width = '100%';
        graphCanvas.style.height = '100%';
        graphCanvas.style.position = 'absolute';
        graphCanvas.style.top = '0';
        graphCanvas.style.left = '0';
        graphCanvas.style.background = 'transparent';
        graphContainer.innerHTML = ''; // Clear container before appending
        graphContainer.appendChild(graphCanvas);
    }

    const ctx = graphCanvas.getContext('2d');

    const colors = {
        adzetaLine: '#e958a1',
        adzetaAreaGradientStart: 'rgba(233, 88, 161, 0.25)',
        adzetaAreaGradientEnd: 'rgba(233, 88, 161, 0)',
        traditionalLine: '#4a9eff',
        traditionalAreaGradientStart: 'rgba(74, 158, 255, 0.15)',
        traditionalAreaGradientEnd: 'rgba(74, 158, 255, 0)',
        profitDifference: 'rgba(233, 88, 161, 0.18)',
        textPrimary: 'rgba(255, 255, 255, 0.95)',
        textSecondary: 'rgba(255, 255, 255, 0.5)',
        grid: 'rgba(255, 255, 255, 0.05)',
        tooltipBackground: 'rgba(28, 28, 32, 0.95)',
        tooltipBorder: 'rgba(233, 88, 161, 0.3)',
        hoverHighlight: 'rgba(255, 255, 255, 0.15)',
        dataPoint: '#8f76f5',
        dataPointGlow: 'rgba(143, 118, 245, 0.5)'
    };

    let animationFrameId;
    let animationProgress = 0;
    let traditionalProgress = 0;
    let adzetaProgress = 0;
    let diffAreaProgress = 0;

    const animationDuration = 2500;
    const traditionalDuration = 1200;
    const adzetaDelay = 600;
    const adzetaDuration = 1600;
    const diffAreaDelay = 1500;
    const diffAreaDuration = 1000;
    const dataPointsDelay = 1000;
    const dataPointsDuration = 1800;

    const dataPoints = [
        { position: 0.15 }, { position: 0.3 }, { position: 0.45 },
        { position: 0.6 }, { position: 0.75 }, { position: 0.9 }
    ];

    const fallingPoints = dataPoints.map(point => ({
        ...point,
        startY: 0, endY: 0, progress: 0,
        delay: point.position * 0.5
    }));

    let startTime;
    let mouseX = -1, mouseY = -1;
    let hoverData = { adzeta: null, traditional: null, scrubPosition: null };
    let tooltipEl = null;

    const pointsCount = 50;
    const adzetaData = [];
    const traditionalData = [];
    for (let i = 0; i <= pointsCount; i++) {
        const x = i / pointsCount;
        adzetaData.push({ x: x, y: 0.15 + 0.7 * (1 - Math.pow(1 - x, 3)) });
        traditionalData.push({ x: x, y: 0.2 + 0.25 * x + 0.1 * Math.sin(x * Math.PI) });
    }

    let animationStarted = false;
    let animationObserver;

    function createTooltip() {
        tooltipEl = document.createElement('div');
        tooltipEl.className = 'graph-tooltip';
        Object.assign(tooltipEl.style, {
            position: 'absolute', backgroundColor: 'rgba(20, 20, 25, 0.85)',
            color: colors.textPrimary, padding: '8px 10px', borderRadius: '6px',
            fontSize: '11px', fontWeight: '500', pointerEvents: 'none',
            opacity: '0', transform: 'translateX(8px)',
            transition: 'opacity 0.15s ease, transform 0.15s ease', zIndex: '100',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.25)', border: '1px solid rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(4px)', maxWidth: '180px', lineHeight: '1.4',
            fontFamily: '"Inter", sans-serif'
        });
        graphContainer.appendChild(tooltipEl);
    }

    graphCanvas.addEventListener('mousemove', (e) => {
        const rect = graphCanvas.getBoundingClientRect();
        mouseX = e.clientX - rect.left;
        mouseY = e.clientY - rect.top;
        findHoverPoints();
        updateTooltip();
    });

    graphCanvas.addEventListener('mouseleave', () => {
        mouseX = -1; mouseY = -1;
        hoverData = { adzeta: null, traditional: null, scrubPosition: null };
        hideTooltip();
    });

    function updateTooltip() {
        if (!tooltipEl) return;
        if (!hoverData.scrubPosition || (!hoverData.adzeta && !hoverData.traditional)) {
            hideTooltip();
            return;
        }

        const scrubX = hoverData.scrubPosition.x;
        const point = hoverData.adzeta || hoverData.traditional;
        const hoverIndex = point.index;
        const adzetaValue = adzetaData[hoverIndex] ? adzetaData[hoverIndex].y : null;
        const traditionalValue = traditionalData[hoverIndex] ? traditionalData[hoverIndex].y : null;
        let profitAdvantage = null;
        if (adzetaValue !== null && traditionalValue !== null) {
            profitAdvantage = Math.round((adzetaValue - traditionalValue) * 100);
        }

        const padding = { top: 30, right: 20, bottom: 40, left: 40 };
        const tooltipY = padding.top + 60; // Keep Y somewhat fixed for stability

        const monthsPoint = Math.round(hoverIndex / pointsCount * 12);
        const baseSpend = 2500;
        const traditionalROI = traditionalValue !== null ? 1.8 + (traditionalValue * 2) : null;
        const adzetaROI = adzetaValue !== null ? 1.8 + (adzetaValue * 2) : null;
        const traditionalRevenue = traditionalROI !== null ? Math.round(baseSpend * traditionalROI) : null;
        const adzetaRevenue = adzetaROI !== null ? Math.round(baseSpend * adzetaROI) : null;
        const revenueDifference = (adzetaRevenue !== null && traditionalRevenue !== null) ? adzetaRevenue - traditionalRevenue : null;

        let tooltipContent = `
            <div style="font-weight: 600; margin-bottom: 4px; color: rgba(255,255,255,0.9);">Month ${monthsPoint}</div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                <span style="color: rgba(255,255,255,0.7);">Adzeta:</span>
                <span style="color: ${colors.adzetaLine}; font-weight: 600;">${adzetaValue !== null ? Math.round(adzetaValue * 100) + '%' : 'N/A'}</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                <span style="color: rgba(255,255,255,0.7);">Traditional:</span>
                <span style="color: ${colors.traditionalLine}; font-weight: 600;">${traditionalValue !== null ? Math.round(traditionalValue * 100) + '%' : 'N/A'}</span>
            </div>`;
        if (profitAdvantage !== null && profitAdvantage > 0) {
            tooltipContent += `
                <div style="display: flex; justify-content: space-between; margin-top: 3px; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 3px;">
                    <span style="color: rgba(255,255,255,0.7);">Advantage:</span>
                    <span style="color: ${colors.adzetaLine}; font-weight: 700;">+${profitAdvantage}%</span>
                </div>`;
        }
        if (revenueDifference !== null && revenueDifference > 0) {
            tooltipContent += `
                <div style="display: flex; justify-content: space-between; margin-top: 3px;">
                    <span style="color: rgba(255,255,255,0.7);">Added Rev:</span>
                    <span style="color: #32CD32; font-weight: 600;">$${revenueDifference.toLocaleString()}</span>
                </div>`;
        }
        tooltipEl.innerHTML = tooltipContent;

        const tooltipWidth = tooltipEl.offsetWidth || 180;
        let adjustedX = scrubX + 15;
        if (adjustedX + tooltipWidth > graphCanvas.offsetWidth - padding.right - 10) { // Added small buffer
            adjustedX = scrubX - tooltipWidth - 15;
        }
         if (adjustedX < padding.left + 10) { // Prevent going off left edge
            adjustedX = padding.left + 10;
        }

        tooltipEl.style.left = `${adjustedX}px`;
        tooltipEl.style.top = `${tooltipY}px`;
        showTooltip();
    }

    function showTooltip() { if (tooltipEl) { tooltipEl.style.opacity = '1'; tooltipEl.style.transform = 'translateY(0)'; } }
    function hideTooltip() { if (tooltipEl) { tooltipEl.style.opacity = '0'; tooltipEl.style.transform = 'translateY(10px)'; } }

    function getCanvasCoords(point, padding) {
        if (!point || typeof point.x !== 'number' || typeof point.y !== 'number' ||
            !padding || typeof padding.left !== 'number' || typeof padding.right !== 'number' ||
            typeof padding.top !== 'number' || typeof padding.bottom !== 'number') {
            // console.error('Invalid point or padding passed to getCanvasCoords:', point, padding);
            return null;
        }
        const graphWidth = graphCanvas.offsetWidth - padding.left - padding.right;
        const graphHeight = graphCanvas.offsetHeight - padding.top - padding.bottom;
        return {
            x: padding.left + point.x * graphWidth,
            y: padding.top + (1 - point.y) * graphHeight
        };
    }

    function easeOutCubic(t) { return 1 - Math.pow(1 - t, 3); }

    function findHoverPoints() {
        const padding = { top: 30, right: 20, bottom: 40, left: 40 };
        const graphWidth = graphCanvas.offsetWidth - padding.left - padding.right;
        hoverData = { adzeta: null, traditional: null, scrubPosition: null };

        if (mouseX < padding.left || mouseX > (graphCanvas.offsetWidth - padding.right) || // Check against canvas width
            mouseY < padding.top || mouseY > (graphCanvas.offsetHeight - padding.bottom)) {
            return;
        }
        const relativeX = Math.max(0, Math.min(1, (mouseX - padding.left) / graphWidth)); // Clamp relativeX
        const closestIndex = Math.min(Math.max(0, Math.round(relativeX * (adzetaData.length - 1))), adzetaData.length - 1);

        hoverData.scrubPosition = { x: padding.left + relativeX * graphWidth, relativeX: relativeX }; // Use calculated scrub X

        const adzetaCoords = getCanvasCoords(adzetaData[closestIndex], padding);
        const traditionalCoords = getCanvasCoords(traditionalData[closestIndex], padding);

        if (adzetaCoords) hoverData.adzeta = { index: closestIndex, coords: adzetaCoords, value: adzetaData[closestIndex].y };
        if (traditionalCoords) hoverData.traditional = { index: closestIndex, coords: traditionalCoords, value: traditionalData[closestIndex].y };
    }

    function drawLineAndArea(data, color, gradientStart, gradientEnd, progress) {
        const easedProgress = easeOutCubic(progress);
        const visiblePointsCount = Math.ceil(data.length * easedProgress);
        if (visiblePointsCount < 2) return;

        const visibleData = data.slice(0, visiblePointsCount);
        const padding = { top: 30, right: 20, bottom: 40, left: 40 };
        ctx.beginPath();
        const startCoords = getCanvasCoords(visibleData[0], padding);
        if (!startCoords) return;
        ctx.moveTo(startCoords.x, startCoords.y);

        for (let i = 1; i < visibleData.length; i++) {
            const currentPoint = visibleData[i]; const prevPoint = visibleData[i - 1];
            const coords = getCanvasCoords(currentPoint, padding); const prevCoords = getCanvasCoords(prevPoint, padding);
            if (coords && prevCoords) {
                const xc = (prevCoords.x + coords.x) / 2; const yc = (prevCoords.y + coords.y) / 2;
                ctx.quadraticCurveTo(prevCoords.x, prevCoords.y, xc, yc);
            } else if (coords) { ctx.lineTo(coords.x, coords.y); }
        }
        const lastCoords = getCanvasCoords(visibleData[visibleData.length - 1], padding);
        const baselineY = getCanvasCoords({ x: 0, y: 0 }, padding).y + 5;
        if (lastCoords) ctx.lineTo(lastCoords.x, lastCoords.y); // Ensure last point is connected for the line itself

        // Area drawing
        if (lastCoords && startCoords) { // Check needed for area
             const areaPath = new Path2D(); // Use Path2D for clarity if preferred, or continue with current ctx path
             areaPath.moveTo(startCoords.x, startCoords.y);
             for (let i = 1; i < visibleData.length; i++) {
                const currentPoint = visibleData[i]; const prevPoint = visibleData[i - 1];
                const coords = getCanvasCoords(currentPoint, padding); const prevCoords = getCanvasCoords(prevPoint, padding);
                if (coords && prevCoords) {
                    const xc = (prevCoords.x + coords.x) / 2; const yc = (prevCoords.y + coords.y) / 2;
                    areaPath.quadraticCurveTo(prevCoords.x, prevCoords.y, xc, yc);
                } else if (coords) { areaPath.lineTo(coords.x, coords.y); }
            }
            areaPath.lineTo(lastCoords.x, baselineY);
            areaPath.lineTo(startCoords.x, baselineY);
            areaPath.closePath();
            const areaGradient = ctx.createLinearGradient(0, 0, 0, graphCanvas.offsetHeight);
            areaGradient.addColorStop(0, gradientStart); areaGradient.addColorStop(1, gradientEnd);
            ctx.fillStyle = areaGradient;
            ctx.fill(areaPath);
        }

        // Line drawing (on top of area)
        ctx.beginPath();
        if (!startCoords) return; // Should have already returned, but defensive
        ctx.moveTo(startCoords.x, startCoords.y);
        for (let i = 1; i < visibleData.length; i++) {
            const currentPoint = visibleData[i]; const prevPoint = visibleData[i - 1];
            const coords = getCanvasCoords(currentPoint, padding); const prevCoords = getCanvasCoords(prevPoint, padding);
            if (coords && prevCoords) {
                const xc = (prevCoords.x + coords.x) / 2; const yc = (prevCoords.y + coords.y) / 2;
                ctx.quadraticCurveTo(prevCoords.x, prevCoords.y, xc, yc);
            } else if (coords) { ctx.lineTo(coords.x, coords.y); }
        }
         if (lastCoords) ctx.lineTo(lastCoords.x, lastCoords.y); // Ensure line reaches the last data point

        Object.assign(ctx, { strokeStyle: color, lineWidth: 2.5, lineCap: 'round', lineJoin: 'round', shadowColor: color, shadowBlur: 8 });
        ctx.stroke();
        Object.assign(ctx, { shadowColor: 'transparent', shadowBlur: 0 });
    }

    function drawGrid() {
        const padding = { top: 30, right: 20, bottom: 40, left: 40 };
        const graphWidth = graphCanvas.offsetWidth - padding.left - padding.right;
        const graphHeight = graphCanvas.offsetHeight - padding.top - padding.bottom;
        const numHorizontalLines = 3; const numVerticalLines = 3;
        Object.assign(ctx, { strokeStyle: colors.grid, lineWidth: 0.5 }); // Thinner for minimal

        for (let i = 0; i <= numHorizontalLines; i++) { // Main percentage lines
            const y = padding.top + (graphHeight * i / numHorizontalLines);
            ctx.beginPath(); ctx.moveTo(padding.left, y); ctx.lineTo(padding.left + graphWidth, y); ctx.stroke();
            if (animationProgress > 0.9) {
                const percentage = Math.round(100 - (i * 100 / numHorizontalLines));
                if (percentage >= 0) {
                    Object.assign(ctx, { fillStyle: colors.textSecondary, font: '10px "Proxima Nova", sans-serif', textAlign: 'right', textBaseline: 'middle'});
                    ctx.fillText(`${percentage}%`, padding.left - 8, y);
                }
            }
        }
        for (let i = 0; i <= numVerticalLines; i++) { // Main month lines
            const x = padding.left + (graphWidth * i / numVerticalLines);
            ctx.beginPath(); ctx.moveTo(x, padding.top); ctx.lineTo(x, padding.top + graphHeight); ctx.stroke();
            if (animationProgress > 0.9) {
                const months = Math.round(i * 12 / numVerticalLines);
                Object.assign(ctx, {fillStyle: colors.textSecondary, font: '10px "Proxima Nova", sans-serif', textAlign: 'center', textBaseline: 'top'});
                ctx.fillText(`${months}mo`, x, padding.top + graphHeight + 8);
            }
        }
        // Axis Labels (subtle)
        if (animationProgress > 0.9) {
             Object.assign(ctx, {fillStyle: colors.textSecondary, font: '11px "Proxima Nova", sans-serif', textAlign: 'center'});
             ctx.fillText('Time', padding.left + graphWidth / 2, padding.top + graphHeight + 25);
             ctx.save();
             ctx.translate(padding.left - 25, padding.top + graphHeight / 2);
             ctx.rotate(-Math.PI / 2);
             ctx.fillText('Profit Growth', 0, 0);
             ctx.restore();
        }
    }

    function drawHoverEffects() {
        const padding = { top: 30, right: 20, bottom: 40, left: 40 };
        if (hoverData.scrubPosition) {
            const x = hoverData.scrubPosition.x;
            Object.assign(ctx, {strokeStyle: 'rgba(255, 255, 255, 0.3)', lineWidth: 1});
            ctx.beginPath(); ctx.setLineDash([3, 3]);
            ctx.moveTo(x, padding.top); ctx.lineTo(x, graphCanvas.offsetHeight - padding.bottom);
            ctx.stroke(); ctx.setLineDash([]);
        }
        ['adzeta', 'traditional'].forEach(type => {
            if (hoverData[type]) {
                const { coords } = hoverData[type];
                const color = type === 'adzeta' ? colors.adzetaLine : colors.traditionalLine;
                if (coords && !isNaN(coords.x)) {
                    ctx.beginPath(); ctx.arc(coords.x, coords.y, 5, 0, Math.PI * 2);
                    ctx.fillStyle = color; ctx.fill();
                    Object.assign(ctx, { shadowColor: color, shadowBlur: 10 });
                    ctx.beginPath(); ctx.arc(coords.x, coords.y, 3, 0, Math.PI * 2); ctx.fill();
                    ctx.shadowBlur = 0; ctx.shadowColor = 'transparent';
                }
            }
        });
        if (hoverData.adzeta && hoverData.traditional && hoverData.adzeta.coords && hoverData.traditional.coords) {
            const { coords: adzetaCoords } = hoverData.adzeta;
            const { coords: traditionalCoords } = hoverData.traditional;
            Object.assign(ctx, {strokeStyle: 'rgba(255, 255, 255, 0.15)', lineWidth: 1});
            ctx.beginPath(); ctx.setLineDash([2, 2]);
            ctx.moveTo(adzetaCoords.x, adzetaCoords.y); ctx.lineTo(traditionalCoords.x, traditionalCoords.y);
            ctx.stroke(); ctx.setLineDash([]);
        }
    }
    
    // Retained the enhanced version of drawProfitDifferenceArea
    function drawProfitDifferenceArea(traditionalProgress, adzetaProgress, diffAreaProgress) {
        if (traditionalProgress < 0.1 || adzetaProgress < 0.1 || diffAreaProgress <= 0) return;

        const padding = { top: 30, right: 20, bottom: 40, left: 40 };
        const traditionalVisible = Math.ceil(traditionalData.length * traditionalProgress);
        const adzetaVisible = Math.ceil(adzetaData.length * adzetaProgress);
        const visiblePoints = Math.min(traditionalVisible, adzetaVisible);

        if (visiblePoints < 2) return;

        const pulseIntensity = diffAreaProgress > 0.95 ? 0.1 * Math.sin(Date.now() / 500) + 0.9 : diffAreaProgress * 0.5;
        ctx.save();
        ctx.globalAlpha = Math.max(0.3, pulseIntensity);

        ctx.beginPath();
        const firstTraditionalCoords = getCanvasCoords(traditionalData[0], padding);
        if (!firstTraditionalCoords) { ctx.restore(); return; }
        ctx.moveTo(firstTraditionalCoords.x, firstTraditionalCoords.y);

        for (let i = 1; i < visiblePoints; i++) {
            const coords = getCanvasCoords(traditionalData[i], padding);
            if (coords) ctx.lineTo(coords.x, coords.y);
        }
        for (let i = visiblePoints - 1; i >= 0; i--) {
            const coords = getCanvasCoords(adzetaData[i], padding);
            if (coords) ctx.lineTo(coords.x, coords.y);
        }
        ctx.closePath();
        ctx.fillStyle = colors.profitDifference;
        ctx.fill();

        if (diffAreaProgress > 0.9) {
            ctx.strokeStyle = 'rgba(233, 88, 161, 0.2)';
            ctx.lineWidth = 1;
            ctx.stroke();
        }

        if (diffAreaProgress > 0.95) {
            const midIndex = Math.floor(visiblePoints * 0.6);
            const adzetaMidPoint = adzetaData[midIndex];
            const traditionalMidPoint = traditionalData[midIndex];
            if (adzetaMidPoint && traditionalMidPoint) {
                const adzetaCoords = getCanvasCoords(adzetaMidPoint, padding);
                const traditionalCoords = getCanvasCoords(traditionalMidPoint, padding);
                if (adzetaCoords && traditionalCoords) {
                    const midX = (adzetaCoords.x + traditionalCoords.x) / 2;
                    const midY = (adzetaCoords.y + traditionalCoords.y) / 2;
                    const advantage = Math.round((adzetaMidPoint.y - traditionalMidPoint.y) * 100);

                    ctx.fillStyle = 'rgba(28, 28, 30, 0.7)';
                    ctx.beginPath(); ctx.roundRect(midX - 70, midY - 15, 140, 30, 5); ctx.fill();
                    ctx.strokeStyle = 'rgba(233, 88, 161, 0.4)'; ctx.lineWidth = 1; ctx.stroke();
                    Object.assign(ctx, {fillStyle: colors.adzetaLine, font: 'bold 12px "Manrope", sans-serif', textAlign: 'center', textBaseline: 'middle'});
                    ctx.fillText(`+${advantage}% Value Unlocked`, midX, midY);
                }
            }
        }
        ctx.restore();
    }

    function drawLegend() {
        if (animationProgress < 0.95) return;
        const padding = { top: 30, right: 20, bottom: 40, left: 40 };
        const legendY = padding.top - 15; // Moved slightly up
        const legendX = padding.left + 10;
        const lineLength = 15; const spacing = 85; // Adjusted spacing

        [{label: 'Adzeta AI', color: colors.adzetaLine, xOffset: 0},
         {label: 'Traditional', color: colors.traditionalLine, xOffset: spacing}]
        .forEach(item => {
            ctx.strokeStyle = item.color; ctx.lineWidth = 2;
            ctx.beginPath(); ctx.moveTo(legendX + item.xOffset, legendY);
            ctx.lineTo(legendX + item.xOffset + lineLength, legendY); ctx.stroke();
            Object.assign(ctx, {fillStyle: 'rgba(255, 255, 255, 0.8)', font: '10px "Proxima Nova", sans-serif', textAlign: 'left', textBaseline: 'middle'});
            ctx.fillText(item.label, legendX + item.xOffset + lineLength + 5, legendY);
        });
    }

    function drawFallingDataPoints(timestamp) {
        const padding = { top: 30, right: 20, bottom: 40, left: 40 };
        const graphHeight = graphCanvas.offsetHeight - padding.top - padding.bottom;

        fallingPoints.forEach(point => {
            if (adzetaProgress < 0.2) return;
            const pointIndex = Math.min(adzetaData.length - 1, Math.floor(point.position * adzetaData.length)); // Ensure valid index
            const dataPoint = adzetaData[pointIndex];
            const traditionalPoint = traditionalData[pointIndex];
            if (!dataPoint || !traditionalPoint) return;
            const coords = getCanvasCoords(dataPoint, padding);
            if (!coords) return;

            if (!point.value) {
                const improvement = Math.round((dataPoint.y - traditionalPoint.y) * 100);
                point.value = `+${improvement}%`;
            }
            if (point.endY === 0) { point.endY = coords.y; point.startY = coords.y - graphHeight * 0.3; }

            const elapsedForPoint = timestamp - startTime;
            if (elapsedForPoint < (dataPointsDelay + (point.delay * 1000))) { point.progress = 0; return; } // Ensure progress is 0 if not started

            const pointProgressVal = Math.min(1, (elapsedForPoint - (dataPointsDelay + (point.delay * 1000))) / (dataPointsDuration / dataPoints.length));
            point.progress = pointProgressVal; // Update point's own progress
            
            const currentY = point.startY + (point.endY - point.startY) * easeOutCubic(point.progress); // Use easing

            ctx.save();
            ctx.beginPath(); ctx.arc(coords.x, currentY, 4, 0, Math.PI * 2);
            ctx.fillStyle = colors.adzetaLine; ctx.fill();
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)'; ctx.lineWidth = 1; ctx.stroke();
            if (point.progress > 0.95) {
                Object.assign(ctx, {fillStyle: colors.adzetaLine, font: 'bold 10px "Inter", sans-serif', textAlign: 'center', textBaseline: 'bottom'});
                ctx.fillText(point.value, coords.x, currentY - 8);
            }
            ctx.restore();
        });
    }

    function initScrollDetection() {
        if (animationObserver) animationObserver.disconnect();
        animationObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !animationStarted) {
                    animationStarted = true;
                    startTime = null;
                    animationProgress = 0; traditionalProgress = 0; adzetaProgress = 0; diffAreaProgress = 0;
                    fallingPoints.forEach(p => { p.progress = 0; p.startY = 0; p.endY = 0;});
                    if (animationFrameId) cancelAnimationFrame(animationFrameId);
                    animate(performance.now());
                    if (window.innerWidth < 768 && animationObserver) {
                        animationObserver.unobserve(graphContainer);
                    }
                }
            });
        }, { threshold: 0.3 });
        animationObserver.observe(graphContainer);
    }

    function animate(timestamp) {
        if (!startTime) startTime = timestamp;
        const elapsed = timestamp - startTime;

        // Update progress only if not already at 1 (to allow forced final state)
        if (animationProgress < 1) animationProgress = Math.min(1, elapsed / animationDuration);
        if (traditionalProgress < 1) traditionalProgress = Math.min(1, elapsed / traditionalDuration);
        if (adzetaProgress < 1) adzetaProgress = elapsed < adzetaDelay ? 0 : Math.min(1, (elapsed - adzetaDelay) / adzetaDuration);
        if (diffAreaProgress < 1) diffAreaProgress = elapsed < diffAreaDelay ? 0 : Math.min(1, (elapsed - diffAreaDelay) / diffAreaDuration);

        ctx.clearRect(0, 0, graphCanvas.offsetWidth, graphCanvas.offsetHeight);
        drawGrid();
        if (traditionalProgress > 0) drawLineAndArea(traditionalData, colors.traditionalLine, colors.traditionalAreaGradientStart, colors.traditionalAreaGradientEnd, traditionalProgress);
        if (diffAreaProgress > 0) drawProfitDifferenceArea(traditionalProgress, adzetaProgress, diffAreaProgress);
        if (adzetaProgress > 0) drawLineAndArea(adzetaData, colors.adzetaLine, colors.adzetaAreaGradientStart, colors.adzetaAreaGradientEnd, adzetaProgress);
        if (adzetaProgress > 0.2) drawFallingDataPoints(timestamp);
        drawLegend();
        drawHoverEffects();
        animationFrameId = requestAnimationFrame(animate);
    }

    const resizeCanvas = () => {
        const isMobile = window.innerWidth < 768;
        const dpr = window.devicePixelRatio || 1;
        const containerRect = graphContainer.getBoundingClientRect();

        graphCanvas.width = containerRect.width * dpr;
        graphCanvas.height = containerRect.height * dpr;
        ctx.scale(dpr, dpr);
        graphCanvas.style.width = `${containerRect.width}px`;
        graphCanvas.style.height = `${containerRect.height}px`;

        if (animationFrameId) cancelAnimationFrame(animationFrameId);

        startTime = null; // Always reset startTime for fresh calculations
        animationProgress = 0; traditionalProgress = 0; adzetaProgress = 0; diffAreaProgress = 0;
        fallingPoints.forEach(p => { p.progress = 0; p.startY = 0; p.endY = 0; });

        if (isMobile) {
            if (animationStarted) { // Was started, now redraw in final state due to resize
                animationProgress = 1; traditionalProgress = 1; adzetaProgress = 1; diffAreaProgress = 1;
                fallingPoints.forEach(p => p.progress = 1);
                // To make 'elapsed' large enough so progress calcs result in 1
                startTime = performance.now() - Math.max(animationDuration, traditionalDuration, adzetaDelay + adzetaDuration, diffAreaDelay + diffAreaDuration, dataPointsDelay + dataPointsDuration) - 100; // ensure it's well past all durations
                animate(performance.now()); // Will draw one frame in final state
            } else { // Not started yet, setup observer
                initScrollDetection();
            }
        } else { // Desktop: always re-animate on resize
            animationStarted = true; // Mark as ready
            // Progresses already reset to 0 above
            animate(performance.now());
        }
    };

    // Initial Setup
    createTooltip();
    resizeCanvas(); // Handles initial sizing and animation/observer setup
    window.addEventListener('resize', resizeCanvas);
});