<?php

namespace AdZetaAdmin\API;

/**
 * Media API Controller
 */
class MediaController extends BaseController
{
    /**
     * Get all media files
     */
    public function index()
    {
        $this->requireAuth();
        
        $params = $this->getQueryParams();
        $page = (int)($params['page'] ?? 1);
        $limit = min(50, (int)($params['limit'] ?? 20));
        $type = $params['type'] ?? null;
        
        try {
            $sql = "SELECT * FROM media_library WHERE 1=1";
            $sqlParams = [];
            
            if ($type) {
                $sql .= " AND file_type = ?";
                $sqlParams[] = $type;
            }
            
            $sql .= " ORDER BY created_at DESC";
            
            // Get total count
            $countSql = "SELECT COUNT(*) FROM media_library WHERE 1=1";
            if ($type) {
                $countSql .= " AND file_type = ?";
            }
            $total = $this->db->fetchColumn($countSql, $sqlParams);
            
            // Get paginated results
            $offset = ($page - 1) * $limit;
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
            
            $media = $this->db->fetchAll($sql, $sqlParams);
            
            return $this->success([
                'media' => $media,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => (int)$total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to load media: ' . $e->getMessage());
        }
    }
    
    /**
     * Upload media file (Editor.js compatible)
     */
    public function upload()
    {
        $this->requirePermission('upload_media');

        // Check for Editor.js image upload
        if (isset($_FILES['image'])) {
            $file = $_FILES['image'];
        } elseif (isset($_FILES['file'])) {
            $file = $_FILES['file'];
        } else {
            return $this->error('No file uploaded');
        }

        if ($file['error'] !== UPLOAD_ERR_OK) {
            return $this->error('File upload error: ' . $this->getUploadErrorMessage($file['error']));
        }

        try {
            // Validate file
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
            if (!in_array($file['type'], $allowedTypes)) {
                return $this->error('File type not allowed');
            }

            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = uniqid() . '.' . $extension;
            $uploadPath = ADMIN_UPLOAD_PATH . '/' . $filename;

            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
                return $this->error('Failed to move uploaded file');
            }

            // Get image dimensions if it's an image
            $width = null;
            $height = null;
            if (in_array($file['type'], ['image/jpeg', 'image/png', 'image/gif', 'image/webp'])) {
                $imageInfo = getimagesize($uploadPath);
                if ($imageInfo) {
                    $width = $imageInfo[0];
                    $height = $imageInfo[1];
                }
            }

            // Create file URL
            $fileUrl = getBaseUrl() . 'adzeta-admin/uploads/' . $filename;

            // Save to database
            $mediaData = [
                'filename' => $filename,
                'original_filename' => $file['name'],
                'file_path' => $uploadPath,
                'file_url' => $fileUrl,
                'file_type' => $this->getFileType($file['type']),
                'file_size' => $file['size'],
                'mime_type' => $file['type'],
                'width' => $width,
                'height' => $height,
                'uploaded_by' => $this->getCurrentUserId()
            ];

            $success = $this->db->insert('media_library', $mediaData);

            if ($success) {
                $mediaId = $this->db->lastInsertId();

                // Return Editor.js compatible response if it's an image upload
                if (isset($_FILES['image'])) {
                    return $this->success([
                        'success' => 1,
                        'file' => [
                            'url' => $fileUrl
                        ]
                    ], 201);
                } else {
                    // Regular media upload response
                    $media = $this->db->fetch("SELECT * FROM media_library WHERE id = ?", [$mediaId]);
                    return $this->success([
                        'message' => 'File uploaded successfully',
                        'media' => $media
                    ], 201);
                }
            } else {
                // Clean up file if database insert failed
                unlink($uploadPath);
                return $this->error('Failed to save file information');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to upload file: ' . $e->getMessage());
        }
    }

    /**
     * Upload by URL (for Editor.js)
     */
    public function uploadByUrl()
    {
        $this->requirePermission('upload_media');
        $data = $this->getRequestData();

        if (empty($data['url'])) {
            return $this->error('No URL provided');
        }

        try {
            $imageUrl = $data['url'];

            // Validate URL
            if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                return $this->error('Invalid URL provided');
            }

            // Download image
            $imageContent = file_get_contents($imageUrl);
            if ($imageContent === false) {
                return $this->error('Failed to download image from URL');
            }

            // Get image info
            $imageInfo = getimagesizefromstring($imageContent);
            if ($imageInfo === false) {
                return $this->error('Invalid image format');
            }

            // Generate filename
            $extension = image_type_to_extension($imageInfo[2], false);
            $filename = uniqid() . '.' . $extension;
            $uploadPath = ADMIN_UPLOAD_PATH . '/' . $filename;
            $fileUrl = getBaseUrl() . 'adzeta-admin/uploads/' . $filename;

            // Save file
            if (file_put_contents($uploadPath, $imageContent) === false) {
                return $this->error('Failed to save downloaded image');
            }

            // Save to database
            $mediaData = [
                'filename' => $filename,
                'original_filename' => basename($imageUrl),
                'file_path' => $uploadPath,
                'file_url' => $fileUrl,
                'file_type' => $extension,
                'file_size' => strlen($imageContent),
                'mime_type' => $imageInfo['mime'],
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'uploaded_by' => $this->getCurrentUserId()
            ];

            $success = $this->db->insert('media_library', $mediaData);

            if ($success) {
                return $this->success([
                    'success' => 1,
                    'file' => [
                        'url' => $fileUrl
                    ]
                ], 201);
            } else {
                unlink($uploadPath);
                return $this->error('Failed to save media information');
            }

        } catch (\Exception $e) {
            return $this->error('Upload by URL failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete media file
     */
    public function destroy($id)
    {
        $this->requirePermission('manage_media');
        
        try {
            $media = $this->db->fetch("SELECT * FROM media_library WHERE id = ?", [$id]);
            
            if (!$media) {
                return $this->error('Media file not found', 404);
            }
            
            // Delete file from filesystem
            if (file_exists($media['file_path'])) {
                unlink($media['file_path']);
            }
            
            // Delete from database
            $success = $this->db->execute("DELETE FROM media_library WHERE id = ?", [$id]);
            
            if ($success) {
                return $this->success(['message' => 'Media file deleted successfully']);
            } else {
                return $this->error('Failed to delete media file');
            }
            
        } catch (\Exception $e) {
            return $this->error('Failed to delete media file: ' . $e->getMessage());
        }
    }
    
    /**
     * Get file type from MIME type
     */
    private function getFileType($mimeType)
    {
        if (strpos($mimeType, 'image/') === 0) {
            return 'image';
        } elseif (strpos($mimeType, 'video/') === 0) {
            return 'video';
        } elseif (strpos($mimeType, 'audio/') === 0) {
            return 'audio';
        } else {
            return 'document';
        }
    }
    
    /**
     * Get current user ID from token
     */
    private function getCurrentUserId()
    {
        $token = $this->getTokenFromHeader();
        if ($token) {
            $payload = $this->auth->verifyToken($token);
            return $payload['user_id'] ?? 1;
        }
        return 1; // Default to admin
    }
    
    /**
     * Legacy upload endpoint (compatible with existing frontend)
     */
    public function uploadLegacy()
    {
        // Skip authentication for legacy compatibility
        // TODO: Add authentication after frontend migration

        try {
            // Handle both 'image' and 'file' fields for compatibility
            if (isset($_FILES['image'])) {
                $file = $_FILES['image'];
            } elseif (isset($_FILES['file'])) {
                $file = $_FILES['file'];
            } else {
                echo json_encode(['success' => 0, 'message' => 'No file uploaded']);
                return;
            }

            if ($file['error'] !== \UPLOAD_ERR_OK) {
                echo json_encode(['success' => 0, 'message' => 'Upload error: ' . $this->getUploadErrorMessage($file['error'])]);
                return;
            }

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                echo json_encode(['success' => 0, 'message' => 'File type not allowed']);
                return;
            }

            // Generate filename (legacy format)
            $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = $originalName . '_' . time() . '_' . uniqid() . '.' . $extension;

            // Use legacy upload directory
            $uploadDir = __DIR__ . '/../../uploads/media/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $filepath = $uploadDir . $filename;
            $fileUrl = '/adzeta-admin/uploads/media/' . $filename;

            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                echo json_encode(['success' => 0, 'message' => 'Failed to save uploaded file']);
                return;
            }

            // Get image dimensions
            $imageInfo = getimagesize($filepath);
            $width = $imageInfo[0] ?? 0;
            $height = $imageInfo[1] ?? 0;

            // Save to legacy database table
            $pdo = $this->getLegacyDatabase();
            $stmt = $pdo->prepare("
                INSERT INTO media (original_filename, filename, file_path, file_url, file_size, mime_type, width, height)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $file['name'],
                $filename,
                $filepath,
                $fileUrl,
                $file['size'],
                $file['type'],
                $width,
                $height
            ]);

            $mediaId = $pdo->lastInsertId();

            // Return legacy-compatible response
            echo json_encode([
                'success' => 1,        // For Editor.js
                'file' => [
                    'url' => $fileUrl,
                    'size' => $file['size'],
                    'name' => $file['name'],
                    'extension' => $extension,
                    'width' => $width,
                    'height' => $height
                ],
                'media' => [
                    'id' => $mediaId,
                    'original_filename' => $file['name'],
                    'filename' => $filename,
                    'file_url' => $fileUrl,
                    'file_size' => $file['size'],
                    'mime_type' => $file['type'],
                    'width' => $width,
                    'height' => $height,
                    'uploaded_at' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            echo json_encode(['success' => 0, 'message' => 'Upload failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Legacy upload by URL endpoint
     */
    public function uploadByUrlLegacy()
    {
        // Skip authentication for legacy compatibility

        $input = json_decode(file_get_contents('php://input'), true);
        $imageUrl = $input['url'] ?? '';

        if (empty($imageUrl)) {
            echo json_encode(['success' => false, 'message' => 'No URL provided']);
            return;
        }

        try {
            // Download and process image (similar to legacy implementation)
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'user_agent' => 'AdZeta Media Library/1.0'
                ]
            ]);

            $imageData = file_get_contents($imageUrl, false, $context);

            if ($imageData === false) {
                echo json_encode(['success' => false, 'message' => 'Failed to download image from URL']);
                return;
            }

            // Process and save (similar to uploadLegacy)
            // ... (implementation similar to legacy upload-by-url.php)

            echo json_encode(['success' => true, 'message' => 'Image uploaded successfully from URL']);

        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Upload failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Legacy media listing endpoint
     */
    public function indexLegacy()
    {
        // Skip authentication for legacy compatibility

        try {
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = max(1, min(50, intval($_GET['limit'] ?? 20)));
            $search = trim($_GET['search'] ?? '');
            $offset = ($page - 1) * $limit;

            $pdo = $this->getLegacyDatabase();

            // Build query
            $whereClause = '';
            $params = [];

            if (!empty($search)) {
                $whereClause = 'WHERE original_filename LIKE ? OR alt_text LIKE ? OR caption LIKE ?';
                $searchTerm = "%$search%";
                $params = [$searchTerm, $searchTerm, $searchTerm];
            }

            // Get total count
            $countSQL = "SELECT COUNT(*) FROM media $whereClause";
            $countStmt = $pdo->prepare($countSQL);
            $countStmt->execute($params);
            $totalItems = $countStmt->fetchColumn();
            $totalPages = ceil($totalItems / $limit);

            // Get media items
            $mediaSQL = "
                SELECT id, original_filename, filename, file_url, file_size, mime_type,
                       width, height, alt_text, caption, uploaded_at
                FROM media
                $whereClause
                ORDER BY uploaded_at DESC
                LIMIT $limit OFFSET $offset
            ";

            $mediaStmt = $pdo->prepare($mediaSQL);
            $mediaStmt->execute($params);
            $media = $mediaStmt->fetchAll(\PDO::FETCH_ASSOC);

            // Return legacy-compatible response
            echo json_encode([
                'success' => true,
                'media' => $media,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_items' => $totalItems,
                    'items_per_page' => $limit,
                    'has_next' => $page < $totalPages,
                    'has_prev' => $page > 1
                ],
                'search' => $search
            ]);

        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Failed to load media: ' . $e->getMessage()]);
        }
    }

    /**
     * Get legacy database connection
     */
    private function getLegacyDatabase()
    {
        static $pdo = null;

        if ($pdo === null) {
            try {
                $pdo = new \PDO(
                    'mysql:host=localhost;dbname=adzetadb;charset=utf8mb4',
                    'adzetauser',
                    'Crazy1395#',
                    [
                        \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                        \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC
                    ]
                );
            } catch (\PDOException $e) {
                throw new \Exception("Database connection failed: " . $e->getMessage());
            }
        }

        return $pdo;
    }

    /**
     * Get upload error message
     */
    private function getUploadErrorMessage($errorCode)
    {
        switch ($errorCode) {
            case \UPLOAD_ERR_INI_SIZE:
                return 'File too large (exceeds upload_max_filesize)';
            case \UPLOAD_ERR_FORM_SIZE:
                return 'File too large (exceeds MAX_FILE_SIZE)';
            case \UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case \UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case \UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case \UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case \UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
}
