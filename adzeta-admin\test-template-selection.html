<!DOCTYPE html>
<html>
<head>
    <title>Template Selection Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Template Selection Test</h2>
        
        <div id="postTemplateSelector">
            <h5>Choose Template</h5>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card template-option h-100" data-template="professional-enhanced" style="cursor: pointer;">
                        <div class="card-body text-center">
                            <div class="template-icon mb-2">
                                <i class="fas fa-file-alt fa-2x text-primary"></i>
                            </div>
                            <h6 class="card-title">Professional Enhanced</h6>
                            <p class="card-text text-muted small">Premium blog template inspired by Medium, NYT, and HBR</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card template-option h-100" data-template="professional-article" style="cursor: pointer;">
                        <div class="card-body text-center">
                            <div class="template-icon mb-2">
                                <i class="fas fa-file-alt fa-2x text-primary"></i>
                            </div>
                            <h6 class="card-title">Professional Article</h6>
                            <p class="card-text text-muted small">Clean, professional layout perfect for business content</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="result" class="mt-4"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple test for template selection
        document.addEventListener('click', (e) => {
            if (e.target.closest('.template-option')) {
                const templateOption = e.target.closest('.template-option');
                const templateKey = templateOption.dataset.template;
                
                console.log('Template clicked:', templateKey);
                
                // Update UI
                document.querySelectorAll('.template-option').forEach(option => {
                    option.classList.remove('border-primary', 'bg-light');
                });
                templateOption.classList.add('border-primary', 'bg-light');
                
                // Show result
                document.getElementById('result').innerHTML = 
                    `<div class="alert alert-success">Selected template: <strong>${templateKey}</strong></div>`;
            }
        });
    </script>
</body>
</html>
