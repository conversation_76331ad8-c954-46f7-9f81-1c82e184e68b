/* Homepage Case Study Cards starts */
.case-study-card {
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    background: linear-gradient(145deg, #ffffff 0%, #f8faff 85%, #f0f5ff 100%);
    border-radius: 16px;
    overflow: hidden;
    border: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02), 0 6px 20px rgba(0, 0, 0, 0.03);
    height: 100%;
    display: grid;
    grid-template-rows: auto 1fr auto;
    position: relative;
}

.case-study-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(240, 245, 255, 0.5) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
    z-index: 1;
}

.case-study-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    background: linear-gradient(145deg, #ffffff 0%, #f5f8ff 85%, #eef3ff 100%);
}

.case-study-card:hover:before {
    opacity: 1;
}


.case-study-card .logo-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px 30px;
    background: transparent;
    text-decoration: none;
    position: relative;
    z-index: 2;
}

.case-study-card .logo-link:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 24px;
    right: 24px;
    height: 1px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.04) 0%, rgba(0, 0, 0, 0.08) 50%, rgba(0, 0, 0, 0.04) 100%);
}

.case-study-card .logo-link img {
    height: 55px;
    width: 100%;
    object-fit: contain;
    max-width: 100%;
}

.case-study-card .logo-link .arrow-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    color: rgba(0, 0, 0, 0.15);
    font-size: 20px;
    transition: all 0.3s ease;
}

.case-study-card .logo-link:hover .arrow-icon {
    color: rgba(0, 0, 0, 0.4);
    transform: translateX(2px);
}

.case-study-card .bg-white {
    background: transparent !important;
    padding: 0;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.case-study-card .ps-30px,
.case-study-card .pe-30px {
    padding-left: 24px !important;
    padding-right: 24px !important;
}

.case-study-card .pt-30px {
    padding-top: 20px !important;
}

.case-study-card .pb-20px {
    padding-bottom: 20px !important;
}

/* Homepage Case Study Cards ends */

/* Homepage Metrics Start */

.key-metric {
    margin: 0 0 20px;
    position: relative;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    gap: 12px;
}


.key-metric-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(244, 88, 136, 0.08);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    color: #f45888;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.key-metric-badge:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
    border-radius: 4px;
}


.key-metric-badge.reduction {
    background-color: rgba(74, 122, 181, 0.08);
    color: #4a7ab5;
}


.key-metric-value {
    font-size: 28px;
    font-weight: 700;
    line-height: 1;
    letter-spacing: -0.5px;
    color: #f45888;
    display: inline-flex;
    align-items: center;
    position: relative;
}


.key-metric-value.reduction {
    color: #4a7ab5;
}


.case-study-card p {
    color: #444;
    font-size: 14px !important;
    line-height: 1.6 !important;
    margin-bottom: 0;
    position: relative;
    padding: 0;
    font-weight: 400;
    font-style: italic;
}


.client-info-container {
    display: flex;
    align-items: center;
    margin-top: 20px;
    padding: 16px 24px;
    background: linear-gradient(to right, rgba(240, 245, 255, 0.7) 0%, rgba(248, 250, 255, 0.7) 50%, rgba(240, 245, 255, 0.7) 100%);
    border-top: 1px solid rgba(0, 0, 0, 0.04);
    position: relative;
    z-index: 2;
}

.client-info-container:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.06) 50%, rgba(0, 0, 0, 0.02) 100%);
}

.client-photo {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 12px;
    border: 2px solid #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.client-info {
    display: flex;
    flex-direction: column;
}

.client-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.client-title {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
    margin-top: 2px;
}

.swiper-slide {
    height: auto;
    padding: 4px;
}

.swiper-wrapper {
    align-items: stretch;
}


.services-box-style-03 {
    height: 100%;
    display: flex;
    flex-direction: column;
}


.services-box-style-03 .bg-white {
    flex: 1;
    display: flex;
    flex-direction: column;
}


.services-box-style-03 .bg-white > div:first-child {
    flex: 1;
    display: flex;
    flex-direction: column;
}


.data-viz-element {
    position: absolute;
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.03) 0%, rgba(244, 88, 136, 0) 70%);
    border-radius: 50%;
    z-index: 0;
    opacity: 0.5;
}

.data-viz-element.circle-1 {
    width: 120px;
    height: 120px;
    top: -60px;
    right: -60px;
}

.data-viz-element.circle-2 {
    width: 80px;
    height: 80px;
    bottom: 40px;
    left: -40px;
    background: linear-gradient(135deg, rgba(74, 122, 181, 0.03) 0%, rgba(74, 122, 181, 0) 70%);
}


.data-point {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: rgba(244, 88, 136, 0.2);
    z-index: 0;
}

.data-point.point-1 {
    top: 30px;
    right: 40px;
}

.data-point.point-2 {
    top: 50px;
    right: 60px;
}

.data-point.point-3 {
    top: 40px;
    right: 80px;
}

.data-point.point-4 {
    bottom: 60px;
    left: 30px;
    background-color: rgba(74, 122, 181, 0.2);
}

.data-point.point-5 {
    bottom: 40px;
    left: 50px;
    background-color: rgba(74, 122, 181, 0.2);
}


.metrics-section {
   background: linear-gradient(to bottom, #f2f0ee 0%, #f6f4f9 100%);
    position: relative;
    border-top: none;
}


.slider-navigation-style-04 {
    width: 48px !important;
    height: 48px !important;
    border-radius: 50% !important;
    background-color: transparent !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 14px !important;
    color: #333 !important;
    transition: all 0.2s ease !important;
    box-shadow: none !important;
}

.slider-navigation-style-04:hover {
    border-color: rgba(0, 0, 0, 0.2) !important;
    transform: scale(1.05) !important;
}


.btn-link.btn-hover-animation-switch {
    padding: 0 !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #333 !important;
    transition: all 0.2s ease !important;
}

.btn-link.btn-hover-animation-switch:hover {
    color: #f45888 !important;
}

.btn-link.btn-hover-animation-switch .btn-icon {
    font-size: 15px !important;
    margin-left: 5px !important;
}

.btn-link.btn-hover-animation-switch:hover .btn-icon {
    transform: translateX(3px) !important;
    transition: transform 0.2s ease !important;
}


.metric-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 28px;
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(10px);
    border: 1px solid transparent;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.metric-card.animate-in {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.metric-card:hover {

    transform: translateY(-5px);
    position: relative;
}

.metric-card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 12px; 
    padding: 1px; 
    background: linear-gradient(to right, rgba(244, 88, 136, 0), rgba(244, 88, 136, 0.5), rgba(106, 140, 175, 0.5), rgba(106, 140, 175, 0));
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.metric-card:hover::before {
    opacity: 1;
}


.metric-title {
    font-size: 16px;
    font-weight: 600;
    color: #666;
    margin: 0 0 24px 0;
    letter-spacing: 0.3px;
    display: block;
    transition: color 0.3s ease;
}

.metric-card:hover .metric-title {
    color: #444;
}


.metric-badge {
    font-size: 11px;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 12px;
    background-color: #f5f5f7;
    color: #888;
    margin-left: 8px;
    display: inline-block;
    vertical-align: middle;
}

.counter-wrapper {
    display: flex;
    align-items: baseline;
    margin-bottom: 8px;
}

.counter-number {
    font-size: 56px;
    line-height: 1;
    font-weight: 700;
    color: #5a5a5a;
    transition: color 0.3s ease;
}

.unit {
    font-size: 32px;
    font-weight: 600;
    margin-left: 2px;
    color: #5a5a5a;
    transition: color 0.3s ease;
}

.metric-card:hover .counter-number,
.metric-card:hover .unit {
    color: #333;
}


.trend {
    font-size: 13px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.trend i {
    margin-right: 4px;
    font-size: 12px;
    transition: color 0.3s ease, transform 0.2s ease;
}

.trend-up {
    color: #f45888; 
}

.trend-down {
    color: #6a8caf; 
}

.text-pink {
    color: #f45888 !important;
    transition: color 0.3s ease;
}

.text-blue {
    color: #6a8caf !important; 
    transition: color 0.3s ease;
}


.metric-card:hover .trend-up i,
.metric-card:hover .text-pink {
    color: #ff3a7a !important; 
}

.metric-card:hover .trend-down i,
.metric-card:hover .text-blue {
    color: #4a7ab5 !important; 
}

.metric-card:hover .trend i {
    transform: scale(1.1);
}


.feather {
    stroke: currentColor;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    fill: none;
}


.metric-description {
    font-size: 14px;
    line-height: 1.5;
    color: #777;
    margin-bottom: 20px;
    transition: color 0.3s ease;
}

.metric-card:hover .metric-description {
    color: #555;
}


.metric-graph {
    position: relative;
    height: 40px;
    margin-top: auto;
    opacity: 0;
    overflow: hidden;
}

.metric-graph.animate-in {
    opacity: 1;
    transition: opacity 0.6s ease;
    transition-delay: 0.2s;
}

.metric-graph svg {
    width: 100%;
    height: 100%;
}


.metric-graph .line {
    stroke-width: 2.5;
    fill: none;
    stroke-linecap: round;
    stroke-linejoin: round;
    filter: drop-shadow(0px 1px 1px rgba(0, 0, 0, 0.05));
    transition: filter 0.3s ease, stroke-width 0.3s ease;
}

.metric-card:hover .metric-graph .line {
    filter: drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.1));
    stroke-width: 3;
}


.metric-graph .positive-line {
    stroke: rgba(244, 88, 136, 0.8); 
}

.metric-graph .positive-area {
    fill: url(#positive-gradient); 
    opacity: 0.9;
    transition: opacity 0.8s ease-in-out, fill 0.3s ease;
}

.metric-card:hover .metric-graph .positive-area {
    opacity: 1;
}


.metric-graph .reduction-line {
    stroke: rgba(106, 140, 175, 0.8); 
}

.metric-graph .reduction-area {
    fill: url(#reduction-gradient); 
    opacity: 0.9;
    transition: opacity 0.8s ease-in-out, fill 0.3s ease;
}

.metric-card:hover .metric-graph .reduction-area {
    opacity: 1;
}


.metric-graph .line {
    transition: stroke-dashoffset 1.5s ease-in-out, filter 0.3s ease;
}


.metrics-footer {
    text-align: center;
    color: #8e8e93;
    font-size: 12px;
    margin-top: 15px;
    padding: 0;
    background-color: transparent;
}

.metrics-footer a {
    color: #6a8caf; 
    text-decoration: none;
    font-weight: 500;
    position: relative;
    padding-bottom: 1px;
}

.metrics-footer a:hover {
    color: #4a7ab5;
    text-decoration: none;
}

.metrics-footer a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background-color: #4a7ab5;
    transition: width 0.2s ease;
}

.metrics-footer a:hover::after {
    width: 100%;
}

.feature-box.hover-box.dark-hover:hover h2[style*="background"] {
    background: none !important;
    -webkit-background-clip: initial !important;
    background-clip: initial !important;
    -webkit-text-fill-color: #ffffff !important;
    color: #ffffff !important;
}

.feature-box.hover-box.dark-hover:hover .text-dark-gray {
    color: #ffffff !important;
}

.feature-box.hover-box.dark-hover .content-slide-up {
    padding: 40px !important;
}



/* Responsive adjustments */
@media (max-width: 991px) {
    .metrics-section {
        padding: 50px 0;
    }

    .counter-number {
        font-size: 48px;
    }

    .unit {
        font-size: 28px;
    }

    .metric-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 767px) {
    .metrics-section {
        padding: 40px 0;
    }

    .counter-number {
        font-size: 42px;
    }

    .unit {
        font-size: 24px;
    }

    .metric-graph {
        height: 30px;
    }

    .metric-card {
        padding: 24px;
    }

    .metrics-footer {
        margin-top: 5px;
        font-size: 11px;
    }

    .metrics-footer {
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }
}
/* Homepage Metrics Ends */