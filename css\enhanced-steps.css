/* Enhanced Step Process Styling */

/* Step Container */
.enhanced-step-process {
  position: relative;
  padding-left: 15px;
}

/* Step Item */
.enhanced-step-item {
  position: relative;
  padding: 25px 0;
  transition: all 0.4s ease;
}

.enhanced-step-item:hover {
  transform: translateX(5px);
}

/* Step Number */
.step-number-container {
  position: relative;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(233, 88, 161, 0.1), rgba(143, 118, 245, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25px;
  transition: all 0.4s ease;
  box-shadow: 0 5px 15px rgba(233, 88, 161, 0.15);
}

.enhanced-step-item:hover .step-number-container {
  transform: scale(1.1);
  background: linear-gradient(135deg, rgba(233, 88, 161, 0.15), rgba(143, 118, 245, 0.15));
  box-shadow: 0 8px 25px rgba(233, 88, 161, 0.25);
}

.step-number {
  font-size: 22px;
  font-weight: 700;
  color: #e958a1;
  position: relative;
  z-index: 2;
  background: linear-gradient(to right, #e958a1, #8f76f5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

.enhanced-step-item:hover .step-number {
  transform: scale(1.1);
}

/* Step Icon */
.step-icon {
  position: absolute;
  width: 24px;
  height: 24px;
  top: 10px;
  right: 5px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.enhanced-step-item:hover .step-icon {
  transform: translateY(-3px) rotate(10deg);
  opacity: 1;
}

/* Step Content */
.step-content {
  padding-left: 10px;
  transition: all 0.3s ease;
}

.step-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c2e3c;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
}

.step-title:after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #e958a1, #8f76f5);
  transition: width 0.4s ease;
}

.enhanced-step-item:hover .step-title:after {
  width: 100%;
}

.enhanced-step-item:hover .step-title {
  color: #e958a1;
}

.step-description {
  font-size: 15px;
  line-height: 1.7;
  color: #797a85;
  margin-bottom: 0;
  transition: all 0.3s ease;
}

.enhanced-step-item:hover .step-description {
  color: #2c2e3c;
}

/* Step Connector */
.step-connector {
  position: absolute;
  left: 35px;
  top: 95px;
  width: 2px;
  height: calc(100% - 140px);
  background: linear-gradient(to bottom, #e958a1, rgba(143, 118, 245, 0.5));
  z-index: 0;
}

/* Step Progress Indicator */
.step-progress {
  position: absolute;
  left: 35px;
  top: 95px;
  width: 2px;
  height: 0;
  background: linear-gradient(to bottom, #e958a1, #8f76f5);
  z-index: 1;
  transition: height 1.5s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.step-process-active .step-progress {
  height: calc(100% - 140px);
}

/* Step Pulse Animation */
.step-pulse {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(233, 88, 161, 0.3);
  left: 26px;
  top: 35px;
  z-index: 0;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  70% {
    transform: scale(1.5);
    opacity: 0;
  }
  100% {
    transform: scale(0.8);
    opacity: 0;
  }
}

/* Step Highlight */
.step-highlight {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(233, 88, 161, 0.03), rgba(143, 118, 245, 0.03));
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.enhanced-step-item:hover .step-highlight {
  opacity: 1;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .step-number-container {
    width: 60px;
    height: 60px;
    margin-right: 20px;
  }
  
  .step-number {
    font-size: 18px;
  }
  
  .step-title {
    font-size: 18px;
  }
  
  .step-description {
    font-size: 14px;
  }
  
  .step-connector, .step-progress {
    left: 30px;
  }
  
  .step-pulse {
    left: 21px;
  }
}

/* Step Icons */
.step-icon-discover {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23e958a1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg>') no-repeat center center;
}

.step-icon-predict {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%238f76f5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline><polyline points="7.5 19.79 7.5 14.6 3 12"></polyline><polyline points="21 12 16.5 14.6 16.5 19.79"></polyline><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>') no-repeat center center;
}

.step-icon-optimize {
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23e958a1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20v-6M6 20V10M18 20V4"></path></svg>') no-repeat center center;
}

/* Animation for step items */
.step-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.step-fade-in.active {
  opacity: 1;
  transform: translateY(0);
}

/* Delay for each step */
.step-fade-in:nth-child(1) {
  transition-delay: 0.1s;
}

.step-fade-in:nth-child(2) {
  transition-delay: 0.3s;
}

.step-fade-in:nth-child(3) {
  transition-delay: 0.5s;
}
