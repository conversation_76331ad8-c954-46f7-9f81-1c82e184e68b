/* Homepage Features Section Start*/
.high-tech-features-section {
    background: linear-gradient(180deg, #1B0B24 0%, #1F1133 100%);
    position: relative;
    overflow: hidden;
}

.tech-bg-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(244, 88, 136, 0.08) 0%, rgba(31, 33, 42, 0) 70%),
        radial-gradient(circle at 80% 80%, rgba(143, 118, 245, 0.05) 0%, rgba(0, 0, 0, 0) 70%);
    opacity: 0.8;
    z-index: 1;
}

.tech-grid-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 30px 30px;
    background-image:
        linear-gradient(to right, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    opacity: 0.4;
    z-index: 1;
}

/* Add subtle animated particles */
.high-tech-features-section:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
    background-size:
        100px 100px,
        150px 150px,
        200px 200px,
        250px 250px,
        300px 300px;
    animation: particleMovement 60s linear infinite;
    opacity: 0.5;
    z-index: 1;
}

@keyframes particleMovement {
    0% {
        background-position:
            0 0,
            0 0,
            0 0,
            0 0,
            0 0;
    }
    100% {
        background-position:
            100px 100px,
            150px 150px,
            200px 200px,
            250px 250px,
            300px 300px;
    }
}

.high-tech-features-content {
    position: relative;
    z-index: 2;
}

.high-tech-features-section .bg-dark-slate-blue {
    background-color: rgba(27, 11, 36, 0.7) !important;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.07);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.high-tech-features-section .feature-box {
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.high-tech-features-section .feature-box:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.03) 0%, rgba(143, 118, 245, 0.03) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.high-tech-features-section .feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.1);
}

.high-tech-features-section .feature-box:hover:before {
    opacity: 1;
}

.high-tech-features-section .feature-box-overlay.bg-cornflower-blue {
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.15) 0%, rgba(143, 118, 245, 0.15) 100%) !important;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.high-tech-features-section .feature-box:hover .feature-box-overlay {
    opacity: 1;
}

.high-tech-features-section .text-light-opacity {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    line-height: 1.6;
    letter-spacing: 0.2px;
    margin-bottom: 0;
}

.high-tech-features-section .feature-box-content span {
    position: relative;
    display: inline-block;
    margin-bottom: 10px !important;
    font-weight: 600 !important;
}

.high-tech-features-section .border-radius-100px.bg-dark-slate-blue {
    background: linear-gradient(90deg, rgba(244, 88, 136, 0.2) 0%, rgba(143, 118, 245, 0.2) 100%) !important;
    border: none;
}


.high-tech-features-section .feature-box-icon {
    position: relative;
    margin-right: 20px;
}



.high-tech-features-section .line-icon-Target:before,
.high-tech-features-section .line-icon-Money-2:before,
.high-tech-features-section .line-icon-Shop-4:before,
.high-tech-features-section .line-icon-Security-Block:before {
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.high-tech-features-section .bi-chat-text:before {
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.high-tech-features-section .icon-double-large {
    font-size: 38px;
    position: relative;
    z-index: 2;
}


.high-tech-features-section a {
    transition: all 0.3s ease;
}

.high-tech-features-section a:hover {
    opacity: 0.8;
}

.high-tech-features-section .feature-box:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2),
                0 0 20px rgba(244, 88, 136, 0.1),
                0 0 30px rgba(143, 118, 245, 0.05);
}



/* Responsive adjustments */
@media (max-width: 991px) {
    .high-tech-features-section .icon-double-large {
        font-size: 32px;
    }
}

@media (max-width: 767px) {
    .high-tech-features-section .icon-double-large {
        font-size: 28px;
    }
}
/* Homepage Features Section Ends*/