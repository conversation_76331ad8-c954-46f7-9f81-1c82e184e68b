<?php

namespace AdZetaAdmin\Models;

/**
 * Case Study Model
 * Handles database operations for case studies
 */
class CaseStudy
{
    private $db;

    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Get all case studies with optional filters
     */
    public function getAll($filters = [])
    {
        $sql = "SELECT * FROM case_studies";
        $params = [];
        $conditions = [];

        // Apply filters
        if (!empty($filters['status'])) {
            $conditions[] = "status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['industry'])) {
            $conditions[] = "industry = ?";
            $params[] = $filters['industry'];
        }

        if (!empty($filters['search'])) {
            $conditions[] = "(title LIKE ? OR client_name LIKE ? OR excerpt LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        $sql .= " ORDER BY updated_at DESC";

        if (!empty($filters['limit'])) {
            $sql .= " LIMIT " . (int)$filters['limit'];
        }

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Get case study by ID
     */
    public function getById($id)
    {
        $sql = "SELECT * FROM case_studies WHERE id = ?";
        return $this->db->fetch($sql, [$id]);
    }

    /**
     * Get case study by slug
     */
    public function getBySlug($slug)
    {
        $sql = "SELECT * FROM case_studies WHERE slug = ? AND status = 'published'";
        $caseStudy = $this->db->fetch($sql, [$slug]);

        if ($caseStudy) {
            // Decode JSON fields
            if ($caseStudy['results_data']) {
                $caseStudy['results_data'] = json_decode($caseStudy['results_data'], true);
            }

            // Increment view count
            $this->incrementViewCount($caseStudy['id']);
        }

        return $caseStudy;
    }

    /**
     * Create new case study
     */
    public function create($data)
    {
        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateSlug($data['title']);
        }

        // Ensure slug is unique
        $data['slug'] = $this->ensureUniqueSlug($data['slug']);

        // Process results_data as JSON
        if (isset($data['results_data']) && is_array($data['results_data'])) {
            $data['results_data'] = json_encode($data['results_data']);
        }

        $fields = [
            'title', 'slug', 'client_name', 'industry',
            'hero_title', 'hero_subtitle', 'hero_description', 'hero_image', 'hero_badge_text',
            'highlight_1_title', 'highlight_1_content', 'highlight_1_icon',
            'highlight_2_title', 'highlight_2_content', 'highlight_2_icon',
            'highlight_3_title', 'highlight_3_content', 'highlight_3_icon',
            'highlight_4_title', 'highlight_4_content', 'highlight_4_icon',
            'highlight_5_title', 'highlight_5_content', 'highlight_5_icon',
            'highlight_6_title', 'highlight_6_content', 'highlight_6_icon',
            'challenge_title', 'challenge_subtitle', 'challenge_description',
            'solution_title', 'solution_description',
            'methodology_title', 'methodology_description',
            'outcomes_title', 'outcomes_description',
            'results_data', 'excerpt', 'featured_image',
            'meta_title', 'meta_description', 'meta_keywords', 'focus_keyword',
            'canonical_url', 'og_title', 'og_description', 'og_image',
            'twitter_card_type', 'status', 'visibility', 'password',
            'published_at', 'template', 'custom_css', 'author_id'
        ];

        $insertData = [];
        $placeholders = [];

        foreach ($fields as $field) {
            if (array_key_exists($field, $data)) {
                $insertData[$field] = $data[$field];
                $placeholders[] = '?';
            }
        }

        if (empty($insertData)) {
            throw new \Exception('No valid data provided for case study creation');
        }

        $sql = "INSERT INTO case_studies (" . implode(', ', array_keys($insertData)) . ") 
                VALUES (" . implode(', ', $placeholders) . ")";

        $this->db->execute($sql, array_values($insertData));
        return $this->db->lastInsertId();
    }

    /**
     * Update case study
     */
    public function update($id, $data)
    {
        // Process results_data as JSON
        if (isset($data['results_data']) && is_array($data['results_data'])) {
            $data['results_data'] = json_encode($data['results_data']);
        }

        // Update slug if title changed
        if (!empty($data['title']) && empty($data['slug'])) {
            $data['slug'] = $this->generateSlug($data['title']);
            $data['slug'] = $this->ensureUniqueSlug($data['slug'], $id);
        }

        $fields = [
            'title', 'slug', 'client_name', 'industry',
            'hero_title', 'hero_subtitle', 'hero_description', 'hero_image', 'hero_badge_text',
            'highlight_1_title', 'highlight_1_content', 'highlight_1_icon',
            'highlight_2_title', 'highlight_2_content', 'highlight_2_icon',
            'highlight_3_title', 'highlight_3_content', 'highlight_3_icon',
            'highlight_4_title', 'highlight_4_content', 'highlight_4_icon',
            'highlight_5_title', 'highlight_5_content', 'highlight_5_icon',
            'highlight_6_title', 'highlight_6_content', 'highlight_6_icon',
            'challenge_title', 'challenge_subtitle', 'challenge_description',
            'solution_title', 'solution_description',
            'methodology_title', 'methodology_description',
            'outcomes_title', 'outcomes_description',
            'results_data', 'excerpt', 'featured_image',
            'meta_title', 'meta_description', 'meta_keywords', 'focus_keyword',
            'canonical_url', 'og_title', 'og_description', 'og_image',
            'twitter_card_type', 'status', 'visibility', 'password',
            'published_at', 'template', 'custom_css'
        ];

        $updateData = [];
        $setParts = [];

        foreach ($fields as $field) {
            if (array_key_exists($field, $data)) {
                $updateData[] = $data[$field];
                $setParts[] = "$field = ?";
            }
        }

        if (empty($updateData)) {
            throw new \Exception('No valid data provided for case study update');
        }

        $updateData[] = $id; // Add ID for WHERE clause

        $sql = "UPDATE case_studies SET " . implode(', ', $setParts) . " WHERE id = ?";
        
        return $this->db->execute($sql, $updateData);
    }

    /**
     * Delete case study
     */
    public function delete($id)
    {
        $sql = "DELETE FROM case_studies WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Get case studies by status
     */
    public function getByStatus($status)
    {
        return $this->getAll(['status' => $status]);
    }

    /**
     * Get published case studies
     */
    public function getPublished($limit = null)
    {
        $filters = ['status' => 'published'];
        if ($limit) {
            $filters['limit'] = $limit;
        }
        return $this->getAll($filters);
    }

    /**
     * Generate URL-friendly slug
     */
    private function generateSlug($title)
    {
        $slug = strtolower(trim($title));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        return trim($slug, '-');
    }

    /**
     * Increment view count for a case study
     */
    private function incrementViewCount($id)
    {
        $sql = "UPDATE case_studies SET view_count = view_count + 1 WHERE id = ?";
        $this->db->execute($sql, [$id]);
    }

    /**
     * Ensure slug is unique
     */
    private function ensureUniqueSlug($slug, $excludeId = null)
    {
        $originalSlug = $slug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slugExists($slug, $excludeId = null)
    {
        $sql = "SELECT id FROM case_studies WHERE slug = ?";
        $params = [$slug];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->db->fetch($sql, $params);
        return !empty($result);
    }

    /**
     * Get case study statistics
     */
    public function getStats()
    {
        $stats = [
            'total' => 0,
            'published' => 0,
            'draft' => 0,
            'archived' => 0,
            'by_industry' => []
        ];

        // Get total counts by status
        $sql = "SELECT status, COUNT(*) as count FROM case_studies GROUP BY status";
        $results = $this->db->fetchAll($sql);

        foreach ($results as $result) {
            $stats[$result['status']] = (int)$result['count'];
            $stats['total'] += (int)$result['count'];
        }

        // Get counts by industry
        $sql = "SELECT industry, COUNT(*) as count FROM case_studies WHERE industry IS NOT NULL GROUP BY industry";
        $results = $this->db->fetchAll($sql);

        foreach ($results as $result) {
            $stats['by_industry'][$result['industry']] = (int)$result['count'];
        }

        return $stats;
    }
}
