<?php

namespace AdZetaAdmin\Core;

/**
 * JWT Authentication Class
 * Handles JWT-based authentication for admin panel
 */
class JWTAuth
{
    private $db;
    private $secretKey;
    private $algorithm = 'HS256';
    private $tokenExpiry = 7200; // 2 hours
    private $cookieName = 'adzeta_admin_token';

    public function __construct()
    {
        global $admin_db;
        $this->db = $admin_db;
        $this->secretKey = $this->getSecretKey();
    }

    /**
     * Generate JWT token for user
     */
    public function generateToken($user)
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => $this->algorithm]);
        $payload = json_encode([
            'user_id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role' => $user['role'],
            'iat' => time(),
            'exp' => time() + $this->tokenExpiry
        ]);

        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->secretKey, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }

    /**
     * Verify and decode JWT token (public method)
     */
    public function verifyToken($token)
    {
        if (!$token) {
            return false;
        }

        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }

        [$header, $payload, $signature] = $parts;

        // Verify signature
        $expectedSignature = hash_hmac('sha256', $header . "." . $payload, $this->secretKey, true);
        $expectedBase64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($expectedSignature));

        if (!hash_equals($expectedBase64Signature, $signature)) {
            return false;
        }

        // Decode payload
        $decodedPayload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);

        if (!$decodedPayload) {
            return false;
        }

        // Check expiration
        if (isset($decodedPayload['exp']) && $decodedPayload['exp'] < time()) {
            return false;
        }

        return $decodedPayload;
    }

    /**
     * Login user and set JWT token
     */
    public function login($username, $password)
    {
        // Validate credentials (reuse existing logic)
        $user = $this->validateCredentials($username, $password);
        
        if (!$user) {
            return [
                'success' => false,
                'message' => 'Invalid username or password.'
            ];
        }

        // Check if user is active
        if ($user['status'] !== 'active') {
            return [
                'success' => false,
                'message' => 'Account is not active.'
            ];
        }

        // Generate JWT token
        $token = $this->generateToken($user);

        // Set token in cookie
        $this->setTokenCookie($token);

        // Update last login
        $this->updateLastLogin($user['id']);

        return [
            'success' => true,
            'message' => 'Login successful.',
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email'],
                'role' => $user['role']
            ]
        ];
    }

    /**
     * Check if user is logged in via JWT
     */
    public function isLoggedIn()
    {
        $token = $this->getTokenFromCookie();
        if (!$token) {
            return false;
        }

        $payload = $this->verifyToken($token);
        return $payload !== false;
    }

    /**
     * Get current user from JWT token
     */
    public function getCurrentUser()
    {
        $token = $this->getTokenFromCookie();
        if (!$token) {
            return null;
        }

        $payload = $this->verifyToken($token);
        if (!$payload) {
            return null;
        }

        // Get fresh user data from database
        return $this->db->fetch(
            "SELECT id, username, email, first_name, last_name, role, avatar 
             FROM users WHERE id = ? AND status = 'active'",
            [$payload['user_id']]
        );
    }

    /**
     * Logout user by clearing token
     */
    public function logout()
    {
        $this->clearTokenCookie();
    }

    /**
     * Check if user has permission
     */
    public function hasPermission($permission)
    {
        $token = $this->getTokenFromCookie();
        if (!$token) {
            return false;
        }

        $payload = $this->verifyToken($token);
        if (!$payload) {
            return false;
        }

        $userRole = $payload['role'] ?? '';
        
        // Admin has all permissions
        if ($userRole === 'admin') {
            return true;
        }

        // Define role-based permissions
        $rolePermissions = [
            'editor' => [
                'view_dashboard', 'manage_posts', 'create_posts', 'view_posts', 
                'manage_seo', 'manage_media', 'upload_media'
            ],
            'author' => [
                'view_dashboard', 'create_posts', 'view_posts', 'upload_media'
            ]
        ];

        return in_array($permission, $rolePermissions[$userRole] ?? []);
    }

    /**
     * Set JWT token in HTTP-only cookie
     */
    private function setTokenCookie($token)
    {
        // Use simple setcookie for better compatibility
        setcookie(
            $this->cookieName,
            $token,
            time() + $this->tokenExpiry,
            '/',
            '',
            false, // secure
            true   // httponly
        );
    }

    /**
     * Get JWT token from cookie
     */
    private function getTokenFromCookie()
    {
        return $_COOKIE[$this->cookieName] ?? null;
    }

    /**
     * Clear JWT token cookie
     */
    private function clearTokenCookie()
    {
        // Use simple setcookie for better compatibility
        setcookie(
            $this->cookieName,
            '',
            time() - 3600,
            '/',
            '',
            false, // secure
            true   // httponly
        );
    }

    /**
     * Get or generate secret key
     */
    private function getSecretKey()
    {
        // In production, store this securely (environment variable, config file, etc.)
        return 'adzeta_admin_jwt_secret_key_2025_' . md5('adzeta_admin_panel');
    }

    /**
     * Validate user credentials
     */
    private function validateCredentials($username, $password)
    {
        $user = $this->db->fetch(
            "SELECT id, username, email, password_hash, role, status 
             FROM users 
             WHERE (username = ? OR email = ?) AND status != 'suspended'",
            [$username, $username]
        );

        if ($user && password_verify($password, $user['password_hash'])) {
            return $user;
        }

        return false;
    }

    /**
     * Update user's last login time
     */
    private function updateLastLogin($userId)
    {
        $this->db->execute(
            "UPDATE users SET last_login = NOW() WHERE id = ?",
            [$userId]
        );
    }
}
