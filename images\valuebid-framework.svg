<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="500" viewBox="0 0 800 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions -->
  <defs>
    <linearGradient id="flowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#e958a1"/>
      <stop offset="100%" stop-color="#8f76f5"/>
    </linearGradient>
    <linearGradient id="boxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#e958a1" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="#8f76f5" stop-opacity="0.1"/>
    </linearGradient>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="500" fill=""/>
  
  <!-- Title -->
  <text x="400" y="40" font-family="Arial, sans-serif" font-size="20" font-weight="bold" text-anchor="middle" fill="#1B0B24">ValueBid™ Framework</text>
  
  <!-- Step 1: LTV Prediction -->
  <g transform="translate(100, 100)">
    <rect x="0" y="0" width="160" height="80" rx="10" fill="url(#boxGradient)" stroke="url(#flowGradient)" stroke-width="2"/>
    <text x="80" y="35" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#1B0B24">LTV Prediction</text>
    <text x="80" y="55" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#1B0B24">Customer Value Scoring</text>
    
    <!-- Animated Pulse -->
    <rect x="0" y="0" width="160" height="80" rx="10" fill="none" stroke="url(#flowGradient)" stroke-width="2" opacity="0">
      <animate attributeName="opacity" values="0;0.5;0" dur="3s" begin="0s" repeatCount="indefinite"/>
      <animate attributeName="width" values="160;170;160" dur="3s" begin="0s" repeatCount="indefinite"/>
      <animate attributeName="height" values="80;90;80" dur="3s" begin="0s" repeatCount="indefinite"/>
      <animate attributeName="x" values="0;-5;0" dur="3s" begin="0s" repeatCount="indefinite"/>
      <animate attributeName="y" values="0;-5;0" dur="3s" begin="0s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Step 2: ValueBid Framework -->
  <g transform="translate(320, 100)">
    <rect x="0" y="0" width="160" height="80" rx="10" fill="url(#boxGradient)" stroke="url(#flowGradient)" stroke-width="2"/>
    <text x="80" y="35" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#1B0B24">ValueBid™ Core</text>
    <text x="80" y="55" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#1B0B24">Bidding Strategy Engine</text>
    
    <!-- Animated Pulse -->
    <rect x="0" y="0" width="160" height="80" rx="10" fill="none" stroke="url(#flowGradient)" stroke-width="2" opacity="0">
      <animate attributeName="opacity" values="0;0.5;0" dur="3s" begin="1s" repeatCount="indefinite"/>
      <animate attributeName="width" values="160;170;160" dur="3s" begin="1s" repeatCount="indefinite"/>
      <animate attributeName="height" values="80;90;80" dur="3s" begin="1s" repeatCount="indefinite"/>
      <animate attributeName="x" values="0;-5;0" dur="3s" begin="1s" repeatCount="indefinite"/>
      <animate attributeName="y" values="0;-5;0" dur="3s" begin="1s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Step 3: API Connection -->
  <g transform="translate(540, 100)">
    <rect x="0" y="0" width="160" height="80" rx="10" fill="url(#boxGradient)" stroke="url(#flowGradient)" stroke-width="2"/>
    <text x="80" y="35" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#1B0B24">API Connection</text>
    <text x="80" y="55" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#1B0B24">Platform Integration</text>
    
    <!-- Animated Pulse -->
    <rect x="0" y="0" width="160" height="80" rx="10" fill="none" stroke="url(#flowGradient)" stroke-width="2" opacity="0">
      <animate attributeName="opacity" values="0;0.5;0" dur="3s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="width" values="160;170;160" dur="3s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="height" values="80;90;80" dur="3s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="x" values="0;-5;0" dur="3s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="y" values="0;-5;0" dur="3s" begin="2s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Ad Platforms -->
  <g transform="translate(200, 250)">
    <rect x="0" y="0" width="120" height="70" rx="10" fill="white" stroke="#4285F4" stroke-width="2"/>
    <text x="60" y="30" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#4285F4">Google Ads</text>
    <text x="60" y="50" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#1B0B24">tROAS / Max Value</text>
  </g>
  
  <g transform="translate(340, 250)">
    <rect x="0" y="0" width="120" height="70" rx="10" fill="white" stroke="#1877F2" stroke-width="2"/>
    <text x="60" y="30" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#1877F2">Meta Ads</text>
    <text x="60" y="50" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#1B0B24">Value Optimization</text>
  </g>
  
  <g transform="translate(480, 250)">
    <rect x="0" y="0" width="145" height="70" rx="10" fill="white" stroke="#000000" stroke-width="2"/>
    <text x="75" y="30" font-family="Arial, sans-serif" font-size="14" font-weight="bold" text-anchor="middle" fill="#000000">Programmatic Ads</text>
    <text x="75" y="50" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#1B0B24">Custom Bidding</text>
  </g>
  
  <!-- Results Box -->
  <g transform="translate(250, 380)">
    <rect x="0" y="0" width="300" height="80" rx="10" fill="url(#flowGradient)" stroke="none" filter="url(#glow)"/>
    <text x="150" y="35" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">Improved Results</text>
    <text x="150" y="55" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">Higher ROAS, Lower CAC, Sustainable Growth</text>
    
    <!-- Animated Pulse -->
    <rect x="0" y="0" width="300" height="80" rx="10" fill="none" stroke="white" stroke-width="2" opacity="0">
      <animate attributeName="opacity" values="0;0.5;0" dur="3s" begin="3s" repeatCount="indefinite"/>
      <animate attributeName="width" values="300;310;300" dur="3s" begin="3s" repeatCount="indefinite"/>
      <animate attributeName="height" values="80;90;80" dur="3s" begin="3s" repeatCount="indefinite"/>
      <animate attributeName="x" values="0;-5;0" dur="3s" begin="3s" repeatCount="indefinite"/>
      <animate attributeName="y" values="0;-5;0" dur="3s" begin="3s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Connecting Arrows -->
  <!-- Step 1 to Step 2 -->
  <path d="M260,140 L300,140" stroke="url(#flowGradient)" stroke-width="2" marker-end="url(#arrowhead)"/>
  <circle cx="290" cy="140" r="3" fill="url(#flowGradient)">
    <animate attributeName="cx" values="260;320;260" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Step 2 to Step 3 -->
  <path d="M480,140 L520,140" stroke="url(#flowGradient)" stroke-width="2" marker-end="url(#arrowhead)"/>
  <circle cx="510" cy="140" r="3" fill="url(#flowGradient)">
    <animate attributeName="cx" values="480;540;480" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Step 3 to Ad Platforms -->
  <path d="M620,180 L620,220 L540,220 L540,250" stroke="url(#flowGradient)" stroke-width="2" marker-end="url(#arrowhead)"/>
  <circle cx="620" cy="220" r="3" fill="url(#flowGradient)">
    <animate attributeName="cy" values="180;220;180" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Ad Platforms to Results -->
  <path d="M260,320 L260,350 L400,350 L400,380" stroke="url(#flowGradient)" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M400,320 L400,380" stroke="url(#flowGradient)" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M540,320 L540,350 L400,350 L400,380" stroke="url(#flowGradient)" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <circle cx="400" cy="350" r="3" fill="url(#flowGradient)">
    <animate attributeName="cy" values="320;380;320" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Arrow Marker Definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="url(#flowGradient)"/>
    </marker>
  </defs>
</svg>
