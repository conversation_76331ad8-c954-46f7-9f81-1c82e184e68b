<?php

namespace AdZetaAdmin\Models;

use PDO;
use Exception;

class Tag
{
    private PDO $db;

    public function __construct(PDO $db)
    {
        $this->db = $db;
    }

    /**
     * Get all tags
     */
    public function getAll(): array
    {
        $sql = "SELECT t.*, COUNT(pt.post_id) as post_count 
                FROM blog_tags t 
                LEFT JOIN post_tags pt ON t.id = pt.tag_id 
                LEFT JOIN blog_posts p ON pt.post_id = p.id AND p.status = 'published'
                GROUP BY t.id 
                ORDER BY t.name ASC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get tag by ID
     */
    public function getById(int $id): ?array
    {
        $sql = "SELECT t.*, COUNT(pt.post_id) as post_count 
                FROM blog_tags t 
                LEFT JOIN post_tags pt ON t.id = pt.tag_id 
                LEFT JOIN blog_posts p ON pt.post_id = p.id AND p.status = 'published'
                WHERE t.id = :id 
                GROUP BY t.id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['id' => $id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Get tag by slug
     */
    public function getBySlug(string $slug): ?array
    {
        $sql = "SELECT t.*, COUNT(pt.post_id) as post_count 
                FROM blog_tags t 
                LEFT JOIN post_tags pt ON t.id = pt.tag_id 
                LEFT JOIN blog_posts p ON pt.post_id = p.id AND p.status = 'published'
                WHERE t.slug = :slug 
                GROUP BY t.id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['slug' => $slug]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Create a new tag
     */
    public function create(array $data): int
    {
        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name']);
        }
        
        $sql = "INSERT INTO blog_tags (name, slug, description) 
                VALUES (:name, :slug, :description)";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            'name' => $data['name'],
            'slug' => $data['slug'],
            'description' => $data['description'] ?? ''
        ]);
        
        return $this->db->lastInsertId();
    }

    /**
     * Update a tag
     */
    public function update(int $id, array $data): bool
    {
        // Generate new slug if name changed
        if (!empty($data['name']) && empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name'], $id);
        }
        
        $fields = [];
        $params = ['id' => $id];
        
        $allowedFields = ['name', 'slug', 'description'];
        
        foreach ($allowedFields as $field) {
            if (array_key_exists($field, $data)) {
                $fields[] = "{$field} = :{$field}";
                $params[$field] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return false;
        }
        
        $sql = "UPDATE blog_tags SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        
        return $stmt->execute($params);
    }

    /**
     * Delete a tag
     */
    public function delete(int $id): bool
    {
        // Delete tag associations first
        $stmt = $this->db->prepare("DELETE FROM post_tags WHERE tag_id = :id");
        $stmt->execute(['id' => $id]);
        
        // Delete the tag
        $stmt = $this->db->prepare("DELETE FROM blog_tags WHERE id = :id");
        return $stmt->execute(['id' => $id]);
    }

    /**
     * Get posts by tag
     */
    public function getPosts(int $tagId, array $options = []): array
    {
        $page = $options['page'] ?? 1;
        $limit = $options['limit'] ?? 10;
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT p.*, u.first_name, u.last_name, c.name as category_name 
                FROM blog_posts p 
                LEFT JOIN users u ON p.author_id = u.id 
                LEFT JOIN blog_categories c ON p.category_id = c.id
                INNER JOIN post_tags pt ON p.id = pt.post_id 
                WHERE pt.tag_id = :tag_id AND p.status = 'published'
                ORDER BY p.published_at DESC 
                LIMIT :limit OFFSET :offset";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':tag_id', $tagId, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get post count for tag
     */
    public function getPostCount(int $tagId): int
    {
        $sql = "SELECT COUNT(*) 
                FROM post_tags pt 
                INNER JOIN blog_posts p ON pt.post_id = p.id 
                WHERE pt.tag_id = :tag_id AND p.status = 'published'";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['tag_id' => $tagId]);
        
        return (int) $stmt->fetchColumn();
    }

    /**
     * Find or create tag by name
     */
    public function findOrCreate(string $name): int
    {
        // Try to find existing tag
        $stmt = $this->db->prepare("SELECT id FROM blog_tags WHERE name = :name");
        $stmt->execute(['name' => $name]);
        
        $tagId = $stmt->fetchColumn();
        
        if ($tagId) {
            return (int) $tagId;
        }
        
        // Create new tag
        return $this->create(['name' => $name]);
    }

    /**
     * Get popular tags
     */
    public function getPopular(int $limit = 10): array
    {
        $sql = "SELECT t.*, COUNT(pt.post_id) as post_count 
                FROM blog_tags t 
                INNER JOIN post_tags pt ON t.id = pt.tag_id 
                INNER JOIN blog_posts p ON pt.post_id = p.id AND p.status = 'published'
                GROUP BY t.id 
                HAVING post_count > 0
                ORDER BY post_count DESC, t.name ASC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Search tags by name
     */
    public function search(string $query, int $limit = 10): array
    {
        $sql = "SELECT * FROM blog_tags 
                WHERE name LIKE :query 
                ORDER BY name ASC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':query', "%{$query}%");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Generate a unique slug
     */
    private function generateUniqueSlug(string $name, int $excludeId = null): string
    {
        $baseSlug = $this->slugify($name);
        $slug = $baseSlug;
        $counter = 1;
        
        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Simple slugify function
     */
    private function slugify(string $text): string
    {
        // Replace non-alphanumeric characters with hyphens
        $text = preg_replace('/[^a-zA-Z0-9]+/', '-', $text);
        // Convert to lowercase
        $text = strtolower($text);
        // Remove leading/trailing hyphens
        $text = trim($text, '-');
        
        return $text;
    }

    /**
     * Check if slug exists
     */
    private function slugExists(string $slug, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) FROM blog_tags WHERE slug = :slug";
        $params = ['slug' => $slug];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Update post count for tag
     */
    public function updatePostCount(int $tagId): void
    {
        $stmt = $this->db->prepare("
            UPDATE blog_tags 
            SET post_count = (
                SELECT COUNT(*) 
                FROM post_tags pt 
                INNER JOIN blog_posts p ON pt.post_id = p.id 
                WHERE pt.tag_id = :tag_id AND p.status = 'published'
            ) 
            WHERE id = :tag_id
        ");
        
        $stmt->execute(['tag_id' => $tagId]);
    }

    /**
     * Get related tags (tags that appear together with the given tag)
     */
    public function getRelated(int $tagId, int $limit = 5): array
    {
        $sql = "SELECT t.*, COUNT(*) as co_occurrence 
                FROM blog_tags t 
                INNER JOIN post_tags pt1 ON t.id = pt1.tag_id 
                INNER JOIN post_tags pt2 ON pt1.post_id = pt2.post_id 
                INNER JOIN blog_posts p ON pt1.post_id = p.id 
                WHERE pt2.tag_id = :tag_id AND t.id != :tag_id AND p.status = 'published'
                GROUP BY t.id 
                ORDER BY co_occurrence DESC, t.name ASC 
                LIMIT :limit";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':tag_id', $tagId, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
