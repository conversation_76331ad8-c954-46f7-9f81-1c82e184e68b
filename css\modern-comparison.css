/* Modern Comparison Section - Latest Design Trends 2024 */

.modern-comparison {
    padding: 100px 0;
    background-color: #fafafa;
    position: relative;
    overflow: hidden;
}

/* Subtle background elements */
.bg-gradient-blob {
    position: absolute;
    width: 800px;
    height: 800px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(233, 88, 161, 0.03) 0%, rgba(143, 118, 245, 0.02) 50%, rgba(0, 0, 0, 0) 70%);
    z-index: 0;
    filter: blur(50px);
}

.blob-1 {
    top: -400px;
    right: -200px;
}

.blob-2 {
    bottom: -300px;
    left: -200px;
}

.comparison-container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section header */
.comparison-header {
    text-align: center;
    margin-bottom: 70px;
}

.comparison-tag {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    color: #e958a1;
    margin-bottom: 15px;
}

.comparison-tag::before {
    content: '';
    display: inline-block;
    width: 25px;
    height: 1px;
    background-color: #e958a1;
    margin-right: 10px;
}

.comparison-title {
    font-size: 42px;
    font-weight: 700;
    line-height: 1.2;
    color: #333;
    margin-bottom: 20px;
}

.comparison-title span {
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.comparison-subtitle {
    font-size: 18px;
    line-height: 1.6;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Cards container */
.cards-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 70px;
}

/* Base card styles */
.comparison-card {
    background: white;
    border-radius: 16px;
    padding: 40px;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

/* Card header */
.card-header {
    margin-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.card-badge {
    display: inline-flex;
    align-items: center;
    font-size: 13px;
    font-weight: 600;
    padding: 6px 15px;
    border-radius: 30px;
    margin-bottom: 20px;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.card-icon i {
    font-size: 24px;
}

.traditional-badge {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.traditional-icon {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.adzeta-badge {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.adzeta-icon {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

.recommended-tag {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 5px 12px;
    border-radius: 20px;
}

.card-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.card-description {
    font-size: 16px;
    color: #666;
    margin-bottom: 0;
}

/* Feature list */
.feature-list {
    margin-top: 30px;
    flex-grow: 1;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.feature-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.feature-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.feature-icon.negative {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.feature-icon.positive {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.feature-text {
    font-size: 16px;
    color: #555;
    line-height: 1.5;
}

/* Traditional card specific */
.traditional-card {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid #eaeaea;
}

.traditional-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
}

/* Adzeta card specific */
.adzeta-card {
    box-shadow: 0 10px 30px rgba(233, 88, 161, 0.08);
    border: 1px solid #eaeaea;
    position: relative;
    z-index: 1;
}

.adzeta-card::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 16px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.adzeta-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(233, 88, 161, 0.15);
    border-color: transparent;
}

.adzeta-card:hover::before {
    opacity: 1;
}

.adzeta-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    border-radius: 15px;
    z-index: -1;
}

/* Results section */
.results-section {
    background: white;
    border-radius: 16px;
    padding: 60px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    text-align: center;
    border: 1px solid #eaeaea;
    position: relative;
    overflow: hidden;
}

.results-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
}

.results-title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 50px;
    position: relative;
    display: inline-block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.stat-item {
    text-align: center;
    position: relative;
}

.stat-value {
    font-size: 48px;
    font-weight: 700;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 15px;
    position: relative;
}

.stat-value::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
}

.stat-label {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .cards-container {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .results-section {
        padding: 40px 30px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .stat-item:not(:last-child) {
        padding-bottom: 30px;
        border-bottom: 1px solid #f0f0f0;
    }
}

@media (max-width: 767px) {
    .modern-comparison {
        padding: 60px 0;
    }
    
    .comparison-header {
        margin-bottom: 40px;
    }
    
    .comparison-title {
        font-size: 32px;
    }
    
    .comparison-card {
        padding: 30px;
    }
    
    .card-title {
        font-size: 22px;
    }
    
    .stat-value {
        font-size: 36px;
    }
}
