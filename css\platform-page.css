/* Platform Page Specific Styles - Inspired by Voyantis.ai */

/* Hero Section */
.platform-hero {
    background: linear-gradient(135deg, #1B0B24 0%, #2D1A3B 100%);
    position: relative;
    overflow: hidden;
    padding: 120px 0 100px;
    color: white;
}

.platform-hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.15) 0%, rgba(31, 33, 42, 0) 60%);
    z-index: 1;
}

.platform-hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40%;
    height: 40%;
    background: radial-gradient(circle at bottom left, rgba(233, 88, 161, 0.15) 0%, rgba(31, 33, 42, 0) 60%);
    z-index: 1;
}

.hero-title {
    font-size: 32px;
    line-height: 1.2;
    margin-bottom: 25px;
    color: white;
}

.hero-subtitle {
    font-size: 18px;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 35px;
    color: rgba(255, 255, 255, 0.9);
}

/* Section Styles */
.platform-section {
    position: relative;
    padding: 100px 0;
    overflow: hidden;
}

.platform-section.bg-light {
    background-color: #f8f9fc;
    position: relative;
}

.platform-section.bg-light::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 40%;
    height: 40%;
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.08) 0%, rgba(31, 33, 42, 0) 70%);
    z-index: 0;
}

.platform-section.bg-dark {
    background: linear-gradient(135deg, #1B0B24 0%, #2D1A3B 100%);
    color: white;
    position: relative;
}

.platform-section.bg-dark::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.15) 0%, rgba(31, 33, 42, 0) 60%);
    z-index: 0;
}

.platform-section.bg-dark::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40%;
    height: 40%;
    background: radial-gradient(circle at bottom left, rgba(233, 88, 161, 0.15) 0%, rgba(31, 33, 42, 0) 60%);
    z-index: 0;
}

.platform-section.bg-gradient {
    background: linear-gradient(135deg, #f8f9fc 0%, #f0f1f7 100%);
}

.section-title-wrapper {
    margin-bottom: 60px;
}

.section-subtitle {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 15px;
    display: inline-block;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.section-subtitle::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
}

.section-title {
    font-size: 36px;
    line-height: 1.3;
    margin-bottom: 20px;
}

.section-description {
    font-size: 18px;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto;
}

/* Feature Box Styles */
.platform-feature-box {
    padding: 40px;
    border-radius: 20px;
    background-color: #ffffff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    height: 100%;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    z-index: 1;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.platform-feature-box::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.03) 0%, rgba(143, 118, 245, 0.03) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.platform-feature-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.platform-feature-box:hover::after {
    opacity: 1;
}

.platform-feature-box .icon-wrapper {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    transition: all 0.3s ease;
}

.platform-feature-box:hover .icon-wrapper {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.15) 0%, rgba(143, 118, 245, 0.15) 100%);
}

.platform-feature-box .icon-wrapper i {
    font-size: 28px;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.platform-feature-box h3 {
    font-size: 22px;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
}

.platform-feature-box h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
}

.platform-feature-box p {
    font-size: 16px;
    line-height: 1.7;
    color: rgba(27, 11, 36, 0.8);
}

/* Dark Feature Box */
.bg-dark .platform-feature-box {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bg-dark .platform-feature-box h3 {
    color: white;
}

.bg-dark .platform-feature-box p {
    color: rgba(255, 255, 255, 0.8);
}

/* Process Steps */
.process-step {
    position: relative;
    padding-left: 80px;
    margin-bottom: 50px;
    transition: all 0.3s ease;
}

.process-step:hover {
    transform: translateX(5px);
}

.process-step .step-number {
    position: absolute;
    left: 0;
    top: 0;
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 24px;
    box-shadow: 0 10px 20px rgba(143, 118, 245, 0.3);
}

.process-step h3 {
    font-size: 22px;
    margin-bottom: 15px;
}

.process-step p {
    font-size: 16px;
    line-height: 1.7;
    color: rgba(27, 11, 36, 0.8);
}

/* Integration Logos */
.integration-section {
    padding: 80px 0;
    background-color: #f8f9fc;
}

.integration-logos {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 30px;
    margin-top: 40px;
}

.integration-logo {
    height: 40px;
    opacity: 0.7;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    filter: grayscale(100%);
}

.integration-logo:hover {
    opacity: 1;
    filter: grayscale(0%);
    transform: scale(1.15);
}

/* Benefit Cards */
.benefit-card {
    padding: 40px;
    border-radius: 20px;
    background-color: #ffffff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    height: 100%;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #e958a1 0%, #8f76f5 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.benefit-card.positive::before {
    background: linear-gradient(90deg, #e958a1 0%, #ff8a9e 100%);
    opacity: 1;
}

.benefit-card.reduction::before {
    background: linear-gradient(90deg, #8f76f5 0%, #6a8fff 100%);
    opacity: 1;
}

.benefit-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.benefit-card .icon-wrapper {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    transition: all 0.3s ease;
}

.benefit-card:hover .icon-wrapper {
    transform: scale(1.1);
}

.benefit-card .icon-wrapper i {
    font-size: 24px;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.benefit-card .metric {
    font-size: 48px;
    font-weight: 700;
    line-height: 1;
    margin: 20px 0;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    display: inline-block;
}

.benefit-card.positive .metric {
    background: linear-gradient(90deg, #e958a1 0%, #ff8a9e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.benefit-card.reduction .metric {
    background: linear-gradient(90deg, #8f76f5 0%, #6a8fff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.benefit-card h3 {
    font-size: 20px;
    margin-bottom: 15px;
}

.benefit-card p {
    font-size: 16px;
    line-height: 1.7;
    color: rgba(27, 11, 36, 0.8);
}

/* Dark Benefit Cards */
.bg-dark .benefit-card {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bg-dark .benefit-card h3 {
    color: white;
}

.bg-dark .benefit-card p {
    color: rgba(255, 255, 255, 0.8);
}

/* SVG Animation Containers */
.svg-animation-container {
    width: 100%;
    height: auto;
    position: relative;
    margin: 40px 0;
    transition: all 0.3s ease;
    z-index: 2;
}

/* Hover effect removed as requested */

.svg-animation-container svg,
.svg-animation-container object {
    width: 100%;
    height: auto;
    filter: drop-shadow(0px 10px 20px rgba(0, 0, 0, 0.15));
}

/* Hero SVG Animation specific styles */
.hero-section .svg-animation-container {
    margin: 0;
    max-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-section .svg-animation-container object {
    max-width: 100%;
    max-height: 500px;
    filter: drop-shadow(0px 10px 30px rgba(0, 0, 0, 0.25));
}

/* Apple-inspired animation styling */
.apple-inspired-animation {
    filter: drop-shadow(0px 15px 35px rgba(0, 0, 0, 0.3));
    transform-origin: center center;
    will-change: transform;
}

/* Hover effect removed as requested */

/* Responsive adjustments for the animation */
@media (max-width: 768px) {
    .svg-animation-container {
        max-height: 350px;
        margin-top: 20px;
        margin-bottom: 20px;
    }
}

/* Apple-inspired button styling */
.apple-inspired-button {
    background: rgba(255, 255, 255, 0.9) !important;
    border: none !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1) !important;
    letter-spacing: 0.5px;
}

.apple-inspired-button:hover {
    transform: translateY(-3px) !important;
    background: rgba(255, 255, 255, 1) !important;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3) !important;
}

.apple-inspired-button:active {
    transform: translateY(0) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Modern gradient text styling for headings */
.modern-gradient-text {
    color: #ffffff;
    font-weight: 700;
    letter-spacing: -3px;
    line-height: 0.95;
    margin-top: 0;
    margin-bottom: 15px;
}

.modern-gradient-text br {
    display: block;
    content: "";
    margin-top: -10px; /* Tighter line spacing */
}

/* Full heading with gradient only on specific words */
.modern-gradient-text .gradient-phrase {
    background-image: linear-gradient(166deg, #ffffff 0%, #e958a1 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline;
    position: relative;
}

/* Specific styling for the "on AI" part */
.modern-gradient-text .highlight-word {
    background-image: linear-gradient(166deg, #e958a1 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline;
    position: relative;
    font-weight: 700;
}

/* Responsive adjustments for the heading */
@media (max-width: 991px) {
    .modern-gradient-text {
        letter-spacing: -2px;
    }
}

@media (max-width: 767px) {
    .modern-gradient-text {
        letter-spacing: -1.5px;
    }

    .modern-gradient-text br {
        margin-top: -3px;
    }
}

/* Apple-inspired gradient for hero section */
.apple-inspired-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.gradient-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(125deg, #0A0A1E 0%, #12102A 40%, #1A1238 70%, #1E1440 100%);
    opacity: 1;
}

.subtle-noise-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAABGJJREFUaEPtmttx2zAQRQ+YinxlKshUEKWCKBXEqSB2BZEriF1B7AoiVxC5gigVRK7A+eKMPEMSJEEsQFrkzOiDM7EW2Ht3gX0Q4o0MvhEOvAP5317JnZF7Rg7MAffWcm+tA0vgvbXeW2vHCrxmvpkxZgzgAcAYwCcAHwB8BPBZ/h4B+CQyqwBWAB4BPCilFjtiyMbsNSPGmDGAKYCJgFAQbQYBLQQUQc2VUqRnp7ETEGYEQD4DmNtA1CRFYAsAcwALpRS/7mTsDMQYw5V/AvDdBkIptfZJG2P4u58yIwuJmZ1AbQVijOHKE+K7/G9tQCiQUmptkjLGcPV/APgG4LdS6nkrGKcgxhiG/08Av0TpF6XUqk1pY8xIYoc2/0UpNXPJ1QnEGMPVIMS0S/mq8saYoWTtJ4AfXbPTCiQAoW1/KaWeuio9lJfvfJPvXbLTCCQA+SmrPu0KUJaXLDIrf3XJTi1IAMLg/hOu6q5AZXmJnT8AfnbJTiVIAMKA/RauqG9lQ3nJDmOHsdMqO6UgAQhXY9oVqE1eYofZ61d2SkGWAL6Gq9EGomteYofB/7VNdnZAJBuMkUlXkD7kJXZmbbJTBBLu9t/7AKnKhLJTOzsFkHC3/9UHSN9ZkdiZSsLYyc4OSAgS7vb0/vqWDWWlADSRQrLIzjYIV4IgDNj/ZQQgUmhyZpWdLEi42//tG6RPuVB2yrKTBQl3e6b+gwCR7DDw6QJkszOUQMJgPwiIMDuUZXbYw9tkJwUJd3vG9cGABLJDWcoys8Pvb7KTgrAhw92+qVPoJBjIhrJbssMGKQVhwDK4/9NJsWehoOywSaLsbIGEuz0bl4MCCWSHssxOvtvvgIRxcXAggewwO5TdZCcFYVwc5IpWZYeyzA7b9i2QsLs4WJBAdijL7OwGfgpysCuRyg5lmZ0NSJjaDx4klB3KMjuU3YAwLo4CJJAdylJ2A8K4OBqQUHYoS9kNSOg3RwUSyA5lKbsB4Q9HA5LKSuxQlrIbkKMCSWWPESRXmI4OJFfpjw4kV/GODiRXeY8OJFfhjw4kV1mPDiRXUY8OJFdJjw4kVzmPDiRXMY8OJFcpjw4kVyGPDiRXGY8OJFcRjw4kVwn7Bom+tcNK2BdI9DUqlq8vkOiLYyxfHyDRV/FYvj5Aoq8HsHxtINE3KFi+NpDoOyYsXxtI9C0clq8NJPqeFMvXBBJ9k4zla5Lf+S4ey9cEMtj5NiLL1wQy2PlOJ8vXBDLY+V4qy9cEMuj5Zi/L1wQy6Pl2NMtXBzLo+YI5y1cHMuj5CjzLVwcy6PlSAstXBzLo+ZoDy1cHMuj56gzLVwUy+Pk6FctXBTL4+cIXy1cFMvj5SiDLVwUy+PlSKctXBTL4+WIuy1cFMvj5cjTLVwUy+PmKOcvXBHIU8yV9lq8J5CjmawMsXxPIUcwXKVi+JpCjmK+WsHxNIEcxX7Zh+ZpAjmK+skTZfwH0pU/8xWKyFAAAAABJRU5ErkJggg==');
    background-repeat: repeat;
    opacity: 0.02;
    mix-blend-mode: overlay;
}

.glass-effect-top {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
    opacity: 0.5;
    border-radius: 0 0 50% 50% / 0 0 100px 100px;
}

.glass-effect-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0));
    opacity: 0.5;
    border-radius: 50% 50% 0 0 / 100px 100px 0 0;
}

.accent-glow {
    position: absolute;
    width: 500px;
    height: 500px;
    border-radius: 50%;
    filter: blur(80px);
}

.accent-glow.top-right {
    top: -250px;
    right: -250px;
    background: radial-gradient(circle, rgba(143, 118, 245, 0.12) 0%, rgba(31, 33, 42, 0) 70%);
    opacity: 0.6;
}

.accent-glow.bottom-left {
    bottom: -250px;
    left: -250px;
    background: radial-gradient(circle, rgba(233, 88, 161, 0.12) 0%, rgba(31, 33, 42, 0) 70%);
    opacity: 0.6;
}

/* High-tech grid background */
.tech-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba(143, 118, 245, 0.02) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(143, 118, 245, 0.02) 1px, transparent 1px);
    background-size: 60px 60px;
    opacity: 0.15;
}

/* Tech dots pattern */
.tech-dots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(rgba(143, 118, 245, 0.03) 1px, transparent 1px);
    background-size: 30px 30px;
    opacity: 0.1;
}

/* Digital circuit elements */
.digital-circuit {
    position: absolute;
    width: 500px;
    height: 500px;
    opacity: 0.05;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M10,10 L30,10 L30,30 L50,30 L50,50 L70,50 L70,70 L90,70' stroke='%238f76f5' fill='none' stroke-width='0.5'/%3E%3Cpath d='M10,90 L30,90 L30,70 L50,70 L50,50 L70,50 L70,30 L90,30' stroke='%23e958a1' fill='none' stroke-width='0.5'/%3E%3Ccircle cx='30' cy='30' r='1.5' fill='%238f76f5' /%3E%3Ccircle cx='50' cy='50' r='1.5' fill='%23e958a1' /%3E%3Ccircle cx='70' cy='70' r='1.5' fill='%238f76f5' /%3E%3Ccircle cx='30' cy='70' r='1.5' fill='%23e958a1' /%3E%3Ccircle cx='70' cy='30' r='1.5' fill='%238f76f5' /%3E%3C/svg%3E");
    background-repeat: repeat;
    background-size: 150px 150px;
}

.digital-circuit.top-left {
    top: -150px;
    left: -150px;
    transform: rotate(45deg);
}

.digital-circuit.bottom-right {
    bottom: -150px;
    right: -150px;
    transform: rotate(-135deg);
}

/* CTA Section */
.platform-cta {
    background: linear-gradient(135deg, #1B0B24 0%, #2D1A3B 100%);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    color: white;
}

.platform-cta::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.2) 0%, rgba(31, 33, 42, 0) 70%);
    z-index: 1;
}

.platform-cta .mesh-overlay {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/subtle-mesh-pattern.png');
    background-size: cover;
    opacity: 0.05;
    z-index: 0;
}

.platform-cta::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50%;
    height: 50%;
    background: radial-gradient(ellipse at bottom left, rgba(233, 88, 161, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
    z-index: 1;
}

.platform-cta .cta-title {
    font-size: 36px;
    margin-bottom: 20px;
    color: white;
}

.platform-cta .cta-description {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.platform-cta .btn {
    padding: 15px 40px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 30px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.platform-cta .btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* Floating Elements */
.floating-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    z-index: 0;
}

.floating-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -150px;
    animation: float 15s ease-in-out infinite;
}

.floating-2 {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: -100px;
    animation: float 20s ease-in-out infinite reverse;
}

@keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(10px, 15px) rotate(5deg); }
    50% { transform: translate(5px, 10px) rotate(0deg); }
    75% { transform: translate(15px, 5px) rotate(-5deg); }
    100% { transform: translate(0, 0) rotate(0deg); }
}

/* Responsive adjustments */
@media (max-width: 1199px) {
    .hero-title {
        font-size: 42px;
    }

    .section-title {
        font-size: 32px;
    }

    .benefit-card .metric {
        font-size: 42px;
    }
}

@media (max-width: 991px) {
    .platform-hero {
        padding: 100px 0 80px;
    }

    .platform-section {
        padding: 80px 0;
    }

    .hero-title {
        font-size: 36px;
    }

    .section-title {
        font-size: 28px;
    }

    .platform-feature-box {
        margin-bottom: 30px;
        padding: 30px;
    }

    .benefit-card {
        margin-bottom: 30px;
    }
}

@media (max-width: 767px) {
    .platform-hero {
        padding: 80px 0 60px;
    }

    .platform-section {
        padding: 60px 0;
    }

    .hero-title {
        font-size: 32px;
    }

    .hero-subtitle {
        font-size: 16px;
    }

    .section-title {
        font-size: 26px;
    }

    .section-description {
        font-size: 16px;
    }

    .process-step {
        padding-left: 70px;
        margin-bottom: 40px;
    }

    .process-step .step-number {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .platform-feature-box {
        padding: 25px;
    }

    .platform-feature-box .icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .benefit-card {
        padding: 30px;
    }

    .benefit-card .metric {
        font-size: 36px;
    }
}
