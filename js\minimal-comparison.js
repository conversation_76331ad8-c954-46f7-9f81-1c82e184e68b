/**
 * Minimal Comparison Section Animations
 * Creates subtle animations for the minimal comparison section
 */

document.addEventListener('DOMContentLoaded', function() {
    // Animate elements when they come into view
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            // Check if element is in viewport
            if (elementTop < windowHeight * 0.85) {
                element.classList.add('active');
            }
        });
    };
    
    // Initial check for elements in viewport
    setTimeout(animateOnScroll, 100);
    
    // Listen for scroll events
    window.addEventListener('scroll', animateOnScroll);
    
    // Animate stats with counting effect
    const animateStats = () => {
        const stats = document.querySelectorAll('.stat-value');
        
        stats.forEach(stat => {
            const targetValue = parseFloat(stat.getAttribute('data-value'));
            const suffix = stat.getAttribute('data-suffix') || '';
            const duration = 1500; // 1.5 seconds
            const startTime = Date.now();
            const startValue = 0;
            const decimalPlaces = (targetValue % 1 !== 0) ? 1 : 0;
            
            const updateValue = () => {
                const currentTime = Date.now();
                const elapsed = currentTime - startTime;
                
                if (elapsed < duration) {
                    const value = easeOutQuad(elapsed, startValue, targetValue, duration);
                    stat.textContent = value.toFixed(decimalPlaces) + suffix;
                    requestAnimationFrame(updateValue);
                } else {
                    stat.textContent = targetValue + suffix;
                }
            };
            
            // Easing function for smoother animation
            const easeOutQuad = (t, b, c, d) => {
                t /= d;
                return -c * t * (t - 2) + b;
            };
            
            // Start animation when stat comes into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateValue();
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(stat);
        });
    };
    
    // Initialize animations when comparison section is in view
    const comparisonSection = document.querySelector('.minimal-comparison-section');
    if (comparisonSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateStats();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });
        
        observer.observe(comparisonSection);
    }
});
