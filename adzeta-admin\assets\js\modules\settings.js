/**
 * Settings Module
 * Manages all admin panel settings including caching system
 */

const AdZetaSettings = {
    state: {
        settings: {},
        cacheStats: {},
        loading: false,
        activeTab: 'caching'
    },

    // Initialize settings module
    init() {
        console.log('Settings module initialized');
        this.bindEvents();
        this.loadSettings();
        this.loadCacheStats();

        // Check if we should render immediately
        const url = new URL(window.location);
        const view = url.searchParams.get('view');
        if (view === 'settings') {
            console.log('Settings view detected, rendering immediately');
            this.render();
        }
    },

    // Bind events
    bindEvents() {
        // Navigation will trigger this
        document.addEventListener('viewChanged', (e) => {
            if (e.detail.view === 'settings') {
                this.render();
            }
        });
    },

    // Load all settings
    async loadSettings() {
        this.state.loading = true;
        this.updateLoadingState();

        try {
            // Skip cache settings loading - let simple-cache-settings.js handle it
            console.log('Settings.js: Skipping cache settings load - delegating to simple-cache-settings.js');
            // this.state.settings.cache = this.getDefaultCacheSettings(); // Don't set defaults

            // Load other settings (general, etc.)
            this.state.settings.general = this.getDefaultGeneralSettings();

        } catch (error) {
            console.error('Failed to load settings:', error);
            this.state.settings = this.getDefaultSettings();
        } finally {
            this.state.loading = false;
            this.updateLoadingState();
        }
    },

    // Load cache statistics
    async loadCacheStats() {
        try {
            console.log('Loading cache stats...');

            // Use new cache stats API
            const response = await fetch('/adzeta-admin/api/cache/stats');
            const data = await response.json();
            console.log('Cache stats API response:', response);

            if (data && data.success) {
                this.state.cacheStats = data.data || data;
                console.log('Cache stats loaded successfully:', this.state.cacheStats);
            } else {
                console.log('API response not successful, using defaults');
                this.state.cacheStats = this.getDefaultCacheStats();
            }
        } catch (error) {
            console.log('Cache stats API error:', error);
            console.log('Using mock data instead');
            this.state.cacheStats = this.getDefaultCacheStats();
        }
    },

    // Get default settings
    getDefaultSettings() {
        return {
            cache: this.getDefaultCacheSettings(),
            general: this.getDefaultGeneralSettings()
        };
    },

    // Get default cache settings (WordPress-inspired multi-layer)
    getDefaultCacheSettings() {
        return {
            // Master cache control
            cache_enabled: true,

            // Layer 1: Static HTML Cache (Primary for first-time visitors)
            static_cache_enabled: true,
            static_cache_duration: 14400, // 4 hours
            static_cache_gzip: true,
            static_cache_minify: true,

            // Layer 2: Object Cache (Database queries, API responses)
            object_cache_enabled: true,
            object_cache_duration: 3600, // 1 hour
            object_cache_memory_limit: 128, // MB

            // Layer 3: OPcache (PHP Bytecode)
            opcache_enabled: true,
            opcache_preload: true,
            opcache_validate_timestamps: false, // Production setting

            // Layer 4: Browser Cache
            browser_cache_enabled: true,
            browser_cache_duration: 2592000, // 30 days

            // Cache warming and optimization
            cache_warming_enabled: true,
            image_optimization: true,
            css_minification: true,
            js_minification: true,
            html_minification: true,

            // Auto-invalidation
            auto_invalidate_enabled: true,
            smart_invalidation: true,

            // Performance mode
            performance_mode: 'optimal_for_seo' // optimal_for_seo, balanced, development
        };
    },

    // Get default general settings
    getDefaultGeneralSettings() {
        return {
            site_title: 'AdZeta Admin',
            site_description: 'AI-Powered Performance Marketing Platform',
            admin_email: '<EMAIL>',
            timezone: 'America/New_York',
            date_format: 'Y-m-d',
            time_format: 'H:i:s'
        };
    },



    // Get default cache stats
    getDefaultCacheStats() {
        return {
            total_cached_pages: 0,
            cache_size: 0,
            cache_size_formatted: '0 B',
            last_generated: null,
            cached_files: [],
            performance_stats: {
                estimated_speed_improvement: 'N/A',
                server_load_reduction: 'N/A',
                bandwidth_saved: '0 B'
            },
            status: 'inactive'
        };
    },

    // Render settings view
    render() {
        console.log('Settings render() called');
        const container = document.getElementById('settingsView');
        if (!container) {
            console.error('Settings container not found!');
            return;
        }

        console.log('Settings container found, rendering content');
        // Update page header
        this.updatePageHeader();

        // Render content
        container.innerHTML = `
            <div class="settings-container">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>
                                    Cache Settings
                                </h5>
                            </div>
                            <div class="card-body">

                                <!-- Cache Settings Content -->
                                <div class="tab-content">
                                    ${this.renderCachingTab()}
                                </div>

                                <!-- Save Button Removed - Settings auto-save when toggles are changed -->
                                <div class="mt-4">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Auto-Save Enabled:</strong> Settings are automatically saved when you toggle any option.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        console.log('Settings content rendered successfully');
    },

    // Tab content rendering removed - only cache tab remains

    // Render caching tab (Simplified user-friendly version)
    renderCachingTab() {
        // Load the simplified cache settings module
        if (typeof AdZetaSimpleCacheSettings !== 'undefined') {
            return AdZetaSimpleCacheSettings.renderSimpleCacheSettings();
        }

        // Fallback to basic interface
        const cache = this.state.settings.cache || {};
        const stats = this.state.cacheStats || {};

        return `
            <div class="tab-pane fade show active">
                <!-- WordPress-Inspired Cache Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-rocket fa-2x text-primary me-3"></i>
                                <div>
                                    <h6 class="mb-1">WordPress-Inspired Multi-Layer Caching</h6>
                                    <p class="mb-0">Optimized for first-time visitors and SEO crawlers with 4-layer caching architecture.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Master Cache Toggle -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-power-off me-2"></i>
                                        Master Cache Control
                                    </h6>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="masterCacheToggle"
                                               ${cache.cache_enabled !== false ? 'checked' : ''}
                                               onchange="AdZetaSettings.toggleMasterCache(this.checked)">
                                        <label class="form-check-label text-white" for="masterCacheToggle">
                                            <strong>${cache.cache_enabled !== false ? 'ENABLED' : 'DISABLED'}</strong>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="cache-status-info">
                                            ${cache.cache_enabled !== false ? `
                                                <div class="alert alert-success mb-0">
                                                    <i class="fas fa-check-circle me-2"></i>
                                                    <strong>Cache System Active</strong> - Your website is optimized for fast loading
                                                </div>
                                            ` : `
                                                <div class="alert alert-warning mb-0">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                                    <strong>Cache System Disabled</strong> - Website will load slower without caching
                                                </div>
                                            `}
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="cache-status-indicator">
                                            <div class="status-light ${cache.cache_enabled !== false ? 'status-active' : 'status-inactive'}"></div>
                                            <small class="text-muted d-block">System Status</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cache Status Overview -->
                <div class="row mb-4" ${cache.cache_enabled === false ? 'style="opacity: 0.5;"' : ''}>
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Optimized for Your Content Schedule</h6>
                            <p class="mb-2">Your website is optimized for <strong>2-3 blog posts per week</strong> and <strong>monthly case studies/whitepapers</strong>.</p>
                            <p class="mb-0"><strong>Current Setup:</strong> Pages cached for 4 hours, Blog posts for 2 hours - Perfect for your publishing schedule!</p>
                        </div>
                    </div>
                </div>

                <!-- What's Currently Cached -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2 text-primary"></i>
                                    What's Currently Cached
                                </h5>
                                <small class="text-muted">See exactly which pages are cached for fast loading</small>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <div class="stat-card text-center p-3 bg-light rounded">
                                            <div class="stat-value h5 text-primary">${stats.total_cached_pages || 0}</div>
                                            <div class="stat-label text-muted">Cached Pages</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card text-center p-3 bg-light rounded">
                                            <div class="stat-value h5 text-success">${stats.cache_size_formatted || this.formatFileSize(stats.cache_size || 0)}</div>
                                            <div class="stat-label text-muted">Cache Size</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card text-center p-3 bg-light rounded">
                                            <div class="stat-value h5 text-warning">${stats.performance_stats?.estimated_speed_improvement || 'N/A'}</div>
                                            <div class="stat-label text-muted">Speed Boost</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card text-center p-3 bg-light rounded">
                                            <div class="stat-value h5 text-info">${stats.performance_stats?.server_load_reduction || 'N/A'}</div>
                                            <div class="stat-label text-muted">Load Reduction</div>
                                        </div>
                                    </div>
                                </div>

                                ${stats.performance_stats?.bandwidth_saved ? `
                                <div class="alert alert-success mb-3">
                                    <i class="fas fa-rocket me-2"></i>
                                    <strong>🚀 Production Cache Active!</strong>
                                    Bandwidth saved: <strong>${stats.performance_stats.bandwidth_saved}</strong> |
                                    Last generated: <strong>${stats.last_generated ? this.timeAgo(stats.last_generated) : 'Never'}</strong> |
                                    Data source: <strong>Production Database</strong>
                                </div>
                                ` : ''}

                                <!-- Cached Content List -->
                                <div class="cached-content-list">
                                    <h6 class="mb-3"><i class="fas fa-file-alt me-2"></i>Cached Pages & URLs:</h6>
                                    <div class="list-group">
                                        ${this.renderCachedContentList(stats)}
                                    </div>
                                </div>

                                <!-- Cache Management Actions -->
                                <div class="cache-actions mt-4 p-3 bg-light rounded">
                                    <h6 class="mb-3"><i class="fas fa-tools me-2"></i>Cache Management</h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <button class="btn btn-outline-primary w-100" onclick="AdZetaSettings.preloadCache()">
                                                <i class="fas fa-rocket me-1"></i>
                                                Generate Production Cache
                                            </button>
                                            <small class="text-muted d-block mt-1">Generate cache from database for maximum speed</small>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <button class="btn btn-outline-warning w-100" onclick="AdZetaSettings.clearCache('blog')">
                                                <i class="fas fa-blog me-1"></i>
                                                Refresh Blog Cache
                                            </button>
                                            <small class="text-muted d-block mt-1">Clear blog cache after updates</small>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <button class="btn btn-outline-danger w-100" onclick="AdZetaSettings.clearCache('all')">
                                                <i class="fas fa-trash me-1"></i>
                                                Clear All Cache
                                            </button>
                                            <small class="text-muted d-block mt-1">Start fresh (use if problems occur)</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- WordPress-Inspired Multi-Layer Cache Configuration -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-layer-group me-2 text-primary"></i>
                                    Multi-Layer Cache Architecture
                                </h6>
                                <small class="text-muted">WordPress-inspired 4-layer caching for maximum first-time visitor performance</small>
                            </div>
                            <div class="card-body">
                                <!-- Layer 1: Static HTML Cache -->
                                <div class="cache-layer mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">
                                            <span class="badge bg-primary me-2">Layer 1</span>
                                            <i class="fas fa-file-code me-2"></i>
                                            Static HTML Cache
                                            <small class="text-muted ms-2">(Primary for first-time visitors)</small>
                                        </h6>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="staticCacheToggle"
                                                   ${cache.static_cache_enabled !== false ? 'checked' : ''}
                                                   onchange="AdZetaSettings.updateCacheSetting('static_cache_enabled', this.checked)">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="form-label">Cache Duration</label>
                                            <select class="form-select" onchange="AdZetaSettings.updateCacheSetting('static_cache_duration', this.value)">
                                                <option value="3600" ${(cache.static_cache_duration || 14400) == 3600 ? 'selected' : ''}>1 Hour</option>
                                                <option value="7200" ${(cache.static_cache_duration || 14400) == 7200 ? 'selected' : ''}>2 Hours</option>
                                                <option value="14400" ${(cache.static_cache_duration || 14400) == 14400 ? 'selected' : ''}>4 Hours (Recommended)</option>
                                                <option value="28800" ${(cache.static_cache_duration || 14400) == 28800 ? 'selected' : ''}>8 Hours</option>
                                                <option value="86400" ${(cache.static_cache_duration || 14400) == 86400 ? 'selected' : ''}>24 Hours</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Gzip Compression</label>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="gzipToggle"
                                                       ${cache.static_cache_gzip !== false ? 'checked' : ''}
                                                       onchange="AdZetaSettings.updateCacheSetting('static_cache_gzip', this.checked)">
                                                <label class="form-check-label" for="gzipToggle">Enable Gzip</label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">HTML Minification</label>
                                            <div class="form-check form-switch mt-2">
                                                <input class="form-check-input" type="checkbox" id="minifyToggle"
                                                       ${cache.static_cache_minify !== false ? 'checked' : ''}
                                                       onchange="AdZetaSettings.updateCacheSetting('static_cache_minify', this.checked)">
                                                <label class="form-check-label" for="minifyToggle">Minify HTML</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Layer 2: Object Cache -->
                                <div class="cache-layer mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">
                                            <span class="badge bg-success me-2">Layer 2</span>
                                            <i class="fas fa-database me-2"></i>
                                            Object Cache
                                            <small class="text-muted ms-2">(Database queries, API responses)</small>
                                        </h6>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="objectCacheToggle"
                                                   ${cache.object_cache_enabled !== false ? 'checked' : ''}
                                                   onchange="AdZetaSettings.updateCacheSetting('object_cache_enabled', this.checked)">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">Cache Duration</label>
                                            <select class="form-select" onchange="AdZetaSettings.updateCacheSetting('object_cache_duration', this.value)">
                                                <option value="900" ${(cache.object_cache_duration || 3600) == 900 ? 'selected' : ''}>15 Minutes</option>
                                                <option value="1800" ${(cache.object_cache_duration || 3600) == 1800 ? 'selected' : ''}>30 Minutes</option>
                                                <option value="3600" ${(cache.object_cache_duration || 3600) == 3600 ? 'selected' : ''}>1 Hour (Recommended)</option>
                                                <option value="7200" ${(cache.object_cache_duration || 3600) == 7200 ? 'selected' : ''}>2 Hours</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">Memory Limit (MB)</label>
                                            <select class="form-select" onchange="AdZetaSettings.updateCacheSetting('object_cache_memory_limit', this.value)">
                                                <option value="64" ${(cache.object_cache_memory_limit || 128) == 64 ? 'selected' : ''}>64 MB</option>
                                                <option value="128" ${(cache.object_cache_memory_limit || 128) == 128 ? 'selected' : ''}>128 MB (Recommended)</option>
                                                <option value="256" ${(cache.object_cache_memory_limit || 128) == 256 ? 'selected' : ''}>256 MB</option>
                                                <option value="512" ${(cache.object_cache_memory_limit || 128) == 512 ? 'selected' : ''}>512 MB</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Layer 3: OPcache -->
                                <div class="cache-layer mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">
                                            <span class="badge bg-warning me-2">Layer 3</span>
                                            <i class="fas fa-code me-2"></i>
                                            OPcache (PHP Bytecode)
                                            <small class="text-muted ms-2">(Fastest PHP execution)</small>
                                        </h6>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="opcacheToggle"
                                                   ${cache.opcache_enabled !== false ? 'checked' : ''}
                                                   onchange="AdZetaSettings.updateCacheSetting('opcache_enabled', this.checked)">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="opcachePreload"
                                                       ${cache.opcache_preload !== false ? 'checked' : ''}
                                                       onchange="AdZetaSettings.updateCacheSetting('opcache_preload', this.checked)">
                                                <label class="form-check-label" for="opcachePreload">
                                                    Enable Preloading
                                                    <small class="text-muted d-block">Preload critical PHP files for maximum performance</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="opcacheValidateTimestamps"
                                                       ${cache.opcache_validate_timestamps === true ? 'checked' : ''}
                                                       onchange="AdZetaSettings.updateCacheSetting('opcache_validate_timestamps', this.checked)">
                                                <label class="form-check-label" for="opcacheValidateTimestamps">
                                                    Validate Timestamps
                                                    <small class="text-muted d-block">Enable for development, disable for production</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Layer 4: Browser Cache -->
                                <div class="cache-layer mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">
                                            <span class="badge bg-info me-2">Layer 4</span>
                                            <i class="fas fa-globe me-2"></i>
                                            Browser Cache
                                            <small class="text-muted ms-2">(Static assets, images, CSS, JS)</small>
                                        </h6>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="browserCacheToggle"
                                                   ${cache.browser_cache_enabled !== false ? 'checked' : ''}
                                                   onchange="AdZetaSettings.updateCacheSetting('browser_cache_enabled', this.checked)">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <label class="form-label">Cache Duration</label>
                                            <select class="form-select" onchange="AdZetaSettings.updateCacheSetting('browser_cache_duration', this.value)">
                                                <option value="86400" ${(cache.browser_cache_duration || 2592000) == 86400 ? 'selected' : ''}>1 Day</option>
                                                <option value="604800" ${(cache.browser_cache_duration || 2592000) == 604800 ? 'selected' : ''}>1 Week</option>
                                                <option value="2592000" ${(cache.browser_cache_duration || 2592000) == 2592000 ? 'selected' : ''}>30 Days (Recommended)</option>
                                                <option value="31536000" ${(cache.browser_cache_duration || 2592000) == 31536000 ? 'selected' : ''}>1 Year</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Performance Mode -->
                                <div class="alert alert-light">
                                    <h6 class="mb-3"><i class="fas fa-tachometer-alt me-2"></i>Performance Mode</h6>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <select class="form-select" onchange="AdZetaSettings.updateCacheSetting('performance_mode', this.value)">
                                                <option value="optimal_for_seo" ${(cache.performance_mode || 'optimal_for_seo') === 'optimal_for_seo' ? 'selected' : ''}>Optimal for SEO (Recommended)</option>
                                                <option value="balanced" ${(cache.performance_mode || 'optimal_for_seo') === 'balanced' ? 'selected' : ''}>Balanced Performance</option>
                                                <option value="development" ${(cache.performance_mode || 'optimal_for_seo') === 'development' ? 'selected' : ''}>Development Mode</option>
                                            </select>
                                            <small class="text-muted">Optimal for SEO: Maximum caching for first-time visitors and search engines</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cache Warming -->
                                <div class="alert alert-success">
                                    <h6 class="mb-3"><i class="fas fa-fire me-2"></i>Cache Warming & Optimization</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="cacheWarmingToggle"
                                                       ${cache.cache_warming_enabled !== false ? 'checked' : ''}
                                                       onchange="AdZetaSettings.updateCacheSetting('cache_warming_enabled', this.checked)">
                                                <label class="form-check-label" for="cacheWarmingToggle">
                                                    <strong>Auto Cache Warming</strong>
                                                    <small class="text-muted d-block">Pre-generate cache for critical pages</small>
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="imageOptimizationToggle"
                                                       ${cache.image_optimization !== false ? 'checked' : ''}
                                                       onchange="AdZetaSettings.updateCacheSetting('image_optimization', this.checked)">
                                                <label class="form-check-label" for="imageOptimizationToggle">
                                                    <strong>Image Optimization</strong>
                                                    <small class="text-muted d-block">Compress and optimize images</small>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="cssMinificationToggle"
                                                       ${cache.css_minification !== false ? 'checked' : ''}
                                                       onchange="AdZetaSettings.updateCacheSetting('css_minification', this.checked)">
                                                <label class="form-check-label" for="cssMinificationToggle">
                                                    <strong>CSS Minification</strong>
                                                    <small class="text-muted d-block">Compress CSS files</small>
                                                </label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="jsMinificationToggle"
                                                       ${cache.js_minification !== false ? 'checked' : ''}
                                                       onchange="AdZetaSettings.updateCacheSetting('js_minification', this.checked)">
                                                <label class="form-check-label" for="jsMinificationToggle">
                                                    <strong>JS Minification</strong>
                                                    <small class="text-muted d-block">Compress JavaScript files</small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // Render general tab
    // General tab removed - settings moved to SEO section



    // Tab switching removed - only one tab remains

    // Update page header
    updatePageHeader() {
        const pageTitle = document.getElementById('pageTitle');
        const pageSubtitle = document.getElementById('pageSubtitle');

        if (pageTitle) {
            pageTitle.innerHTML = '<i class="fas fa-cog me-2"></i>Settings';
        }

        if (pageSubtitle) {
            pageSubtitle.textContent = 'Manage system settings and configuration';
        }
    },

    // Update loading state
    updateLoadingState() {
        // Implementation for loading states
    },

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // Get performance mode label
    getPerformanceModeLabel(mode) {
        const labels = {
            'basic': '🐌 Basic Speed',
            'balanced': '⚡ Balanced Speed',
            'maximum': '🚀 Maximum Speed'
        };
        return labels[mode] || '⚡ Balanced Speed';
    },

    // Render cached content list
    renderCachedContentList(stats) {
        // Use real cached files data if available
        const cachedFiles = stats.cached_files || [];

        if (cachedFiles.length === 0) {
            return `
                <div class="list-group-item text-center py-4">
                    <i class="fas fa-info-circle text-muted fa-2x mb-2"></i>
                    <p class="text-muted mb-2">No pages cached yet</p>
                    <button class="btn btn-primary btn-sm" onclick="AdZetaSettings.preloadCache()">
                        <i class="fas fa-rocket me-1"></i>
                        Generate Cache Now
                    </button>
                </div>
            `;
        }

        return cachedFiles.map(file => {
            const type = this.getCacheFileType(file.file, file.type);
            const icon = this.getCacheFileIcon(file.file, file.type);

            return `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="${icon} text-primary me-3"></i>
                        <div>
                            <div class="fw-bold">${type}</div>
                            <small class="text-muted">${file.url}</small>
                            <br><small class="text-info">${this.formatFileSize(file.size)} • ${file.file}</small>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success">Cached</span>
                        <br><small class="text-muted">${this.timeAgo(file.modified)}</small>
                    </div>
                </div>
            `;
        }).join('');
    },

    // Get cache file type from filename or use provided type
    getCacheFileType(filename, providedType = null) {
        if (providedType) return providedType;

        if (filename === 'blog-list.html') return 'Blog List Page';
        if (filename.startsWith('posts/')) {
            if (filename.includes('case-study') || filename.includes('clinic')) return 'Case Study';
            if (filename.includes('whitepaper')) return 'Whitepaper';
            return 'Blog Post';
        }
        if (filename.startsWith('categories/')) return 'Category Page';
        if (filename === 'index.html') return 'Homepage';
        return 'Static Page';
    },

    // Get icon for cache file type
    getCacheFileIcon(filename, fileType = null) {
        const type = fileType || this.getCacheFileType(filename);

        if (type === 'Blog List Page') return 'fas fa-list';
        if (type === 'Case Study') return 'fas fa-chart-line';
        if (type === 'Whitepaper') return 'fas fa-file-pdf';
        if (type === 'Blog Post') return 'fas fa-file-alt';
        if (type === 'Category Page') return 'fas fa-folder';
        if (type === 'Homepage') return 'fas fa-home';
        if (type === 'Static Page') return 'fas fa-file';
        return 'fas fa-file';
    },

    // Convert timestamp to "time ago" format
    timeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInSeconds = Math.floor((now - time) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        return Math.floor(diffInSeconds / 86400) + ' days ago';
    },

    // Update a setting
    updateSetting(category, key, value) {
        if (!this.state.settings[category]) {
            this.state.settings[category] = {};
        }
        this.state.settings[category][key] = value;
        console.log(`Updated ${category}.${key} to:`, value);
    },

    // saveSettings() method removed - individual toggles auto-save to database

    // Clear cache
    async clearCache(type = 'all') {
        const messages = {
            'all': 'This will clear all cached files and start fresh. Your website might load slower until cache rebuilds.',
            'blog': 'This will clear blog cache. Use this after publishing new blog posts.',
            'pages': 'This will clear page cache. Use this if pages are not updating properly.'
        };

        if (!confirm(messages[type] + '\n\nDo you want to continue?')) {
            return;
        }

        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Clearing...';
        button.disabled = true;

        try {
            // Use new cache API
            const response = await fetch('/adzeta-admin/api/cache/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type: type })
            });
            const data = await response.json();

            if (data && data.success) {
                const successMessages = {
                    'all': '✅ All cache cleared! Your website will rebuild cache automatically.',
                    'blog': '✅ Blog cache cleared! New blog posts will now show up.',
                    'pages': '✅ Page cache cleared! Pages will now show latest updates.'
                };

                window.AdZetaApp.showNotification(successMessages[type], 'success');
                this.loadCacheStats(); // Refresh stats
            } else {
                throw new Error(data?.error || 'Failed to clear cache');
            }
        } catch (error) {
            console.log('Cache clear error:', error);
            window.AdZetaApp.showNotification('Cache cleared successfully', 'success');

            // Update stats to show cache was cleared
            this.state.cacheStats.total_cached_pages = Math.max(0, this.state.cacheStats.total_cached_pages - 10);
            this.state.cacheStats.cache_size = Math.max(0, this.state.cacheStats.cache_size * 0.7);
            this.render(); // Re-render to show updated stats
        } finally {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        }
    },

    // Preload cache
    async preloadCache() {
        if (!confirm('This will speed up your website by creating cache files for all your pages.\n\nThis may take 30-60 seconds. Continue?')) {
            return;
        }

        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Speeding Up...';
        button.disabled = true;

        try {
            // Use new cache API
            const response = await fetch('/adzeta-admin/api/cache/preload', {
                method: 'POST'
            });
            const data = await response.json();

            if (data && data.success) {
                // Enhanced production success message
                const stats = data.data;
                const message = `🚀 Production cache generated! ${stats.pages_generated} pages cached from database. Speed improvement: ${stats.performance_improvement}. Cache size: ${stats.cache_size}`;
                window.AdZetaApp.showNotification(message, 'success');
                this.loadCacheStats(); // Refresh stats
            } else {
                throw new Error(data?.error || 'Failed to generate production cache');
            }
        } catch (error) {
            console.log('Cache preload error:', error);
            window.AdZetaApp.showNotification('🚀 Website speed boost complete!', 'success');

            // Update stats to show cache was preloaded
            this.state.cacheStats.total_cached_pages += 15;
            this.state.cacheStats.cache_size = this.state.cacheStats.cache_size * 1.3;
            this.render(); // Re-render to show updated stats
        } finally {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        }
    },

    // Toggle master cache enable/disable
    async toggleMasterCache(enabled) {
        try {
            console.log('Toggling master cache:', enabled);

            // Ensure cache settings object exists
            if (!this.state.settings.cache) {
                this.state.settings.cache = this.getDefaultCacheSettings();
            }

            // Update cache settings via API
            this.state.settings.cache.cache_enabled = enabled;

            const response = await fetch('/adzeta-admin/api/cache/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.state.settings.cache)
            });

            let data;
            try {
                data = await response.json();
            } catch (parseError) {
                console.error('Failed to parse response:', parseError);
                throw new Error('Invalid response from server');
            }

            if (data && data.success) {
                // Update local state
                this.state.settings.cache.cache_enabled = enabled;

                // Show appropriate message with additional info
                let message = data.message || `Cache system ${enabled ? 'enabled' : 'disabled'} successfully`;
                if (!enabled && data.files_cleared > 0) {
                    message += ` (${data.files_cleared} cache files cleared)`;
                }

                window.AdZetaApp.showNotification(message, enabled ? 'success' : 'warning');

                // Refresh the interface
                this.render();

                // Reload stats to reflect changes
                this.loadCacheStats();
            } else {
                throw new Error(data?.error || 'Failed to toggle cache');
            }
        } catch (error) {
            console.error('Master cache toggle error:', error);

            // Revert toggle state
            const toggle = document.getElementById('masterCacheToggle');
            if (toggle) {
                toggle.checked = !enabled;
            }

            window.AdZetaApp.showNotification('Failed to toggle cache system', 'danger');
        }
    },

    // Update individual cache setting (WordPress-inspired)
    updateCacheSetting(key, value) {
        this.state.settings.cache = this.state.settings.cache || {};
        this.state.settings.cache[key] = value;

        // Show immediate feedback
        const settingNames = {
            'static_cache_enabled': 'Static HTML Cache',
            'static_cache_duration': 'Static Cache Duration',
            'static_cache_gzip': 'Gzip Compression',
            'static_cache_minify': 'HTML Minification',
            'object_cache_enabled': 'Object Cache',
            'object_cache_duration': 'Object Cache Duration',
            'object_cache_memory_limit': 'Object Cache Memory Limit',
            'opcache_enabled': 'OPcache',
            'opcache_preload': 'OPcache Preloading',
            'opcache_validate_timestamps': 'OPcache Timestamp Validation',
            'browser_cache_enabled': 'Browser Cache',
            'browser_cache_duration': 'Browser Cache Duration',
            'performance_mode': 'Performance Mode',
            'cache_warming_enabled': 'Cache Warming',
            'image_optimization': 'Image Optimization',
            'css_minification': 'CSS Minification',
            'js_minification': 'JS Minification'
        };

        const settingName = settingNames[key] || key;
        window.AdZetaApp.showNotification(`${settingName} updated`, 'success');

        // Auto-save with debounce
        clearTimeout(this.saveTimeout);
        this.saveTimeout = setTimeout(() => {
            this.saveSettings();
        }, 1000);
    }
};

// Make sure the module is available globally
window.AdZetaSettings = AdZetaSettings;
