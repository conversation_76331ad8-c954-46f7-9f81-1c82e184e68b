/* Custom adjustments for platform.html */

/* More compact heading with tighter line spacing */
.heading-gradient br {
    display: block;
    content: "";
    margin-top: -10px; /* Tighter line spacing */
}

/* Adjust vertical spacing in hero section */
.hero-section .row {
    margin-top: -15px;
}

/* High-tech light gradient background for sections */
.bg-tech-light-gradient {
    background: linear-gradient(166deg,
        #F2F0EE 0%,
        #F5F5F7 30%,
        #F0F2F5 60%,
        #EFF1F6 80%,
        #EAEDF4 100%);
    position: relative;
    overflow: hidden;
}

/* Add subtle tech pattern overlay */
body:not(.ecom-ppc) .bg-tech-light-gradient::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(233, 88, 161, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(106, 79, 217, 0.05) 0%, transparent 50%);
    opacity: 0.8;
    z-index: 0;
}

.ecom-ppc .bg-tech-light-gradient::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: none;
    z-index: 0;
}

/* Add subtle grid lines */
body:not(.ecom-ppc) .bg-tech-light-gradient::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(rgba(106, 79, 217, 0.05) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(106, 79, 217, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.5;
    z-index: 0;
}

.ecom-ppc .bg-tech-light-gradient::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: none;
    z-index: 0;
}

/* Enhanced styling for the technology-deep-dive section */
.technology-deep-dive {
    padding: 80px 0;
    position: relative;
}

.technology-deep-dive .nav-tabs {
    border: none;
    position: relative;
}

.technology-deep-dive .nav-tabs::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: -15px;
    width: 1px;
    background: linear-gradient(to bottom, transparent, rgba(233, 88, 161, 0.2), transparent);
}

.technology-deep-dive .nav-link {
    border: none;
    border-radius: 0;
    padding: 15px 20px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.technology-deep-dive .nav-link::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff8cc6, #df367d);
    transition: width 0.3s ease;
}

.technology-deep-dive .nav-link.active::before,
.technology-deep-dive .nav-link:hover::before {
    width: 10px;
}

.technology-deep-dive .nav-link.active,
.technology-deep-dive .nav-link:hover {
    background: rgba(233, 88, 161, 0.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.technology-deep-dive .tab-content {
    padding: 20px 0;
}
.modern-heading-gradient{
		-webkit-text-fill-color: transparent;


		background-image: linear-gradient(166deg, #2b2d3a 0%, #393b48 40%, #4b4d5b 60%, #f04b64 80%, #f35a77 100%);

		-webkit-background-clip: text;
		background-clip: text;
		color: transparent;
		letter-spacing: -0.0625rem;
		font-weight: 600;
		line-height: 1.2;
		position: relative;
		display: inline-block;
}

/* No background glow or hover effect */
