/**
 * Refined Comparison Section
 * Advanced animations and micro-interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Animate elements when they come into view with staggered timing
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.fade-up, .fade-left, .fade-right, .scale-up');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            // Check if element is in viewport
            if (elementTop < windowHeight * 0.85) {
                element.classList.add('active');
            }
        });
    };
    
    // Initial check for elements in viewport
    setTimeout(animateOnScroll, 100);
    
    // Listen for scroll events with throttling for performance
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        if (!scrollTimeout) {
            scrollTimeout = setTimeout(() => {
                animateOnScroll();
                scrollTimeout = null;
            }, 50);
        }
    });
    
    // Animate stats with counting effect and easing
    const animateStats = () => {
        const stats = document.querySelectorAll('.stat-value');
        
        stats.forEach(stat => {
            const targetValue = parseFloat(stat.getAttribute('data-value'));
            const suffix = stat.getAttribute('data-suffix') || '';
            const duration = 2000; // 2 seconds
            const startTime = Date.now();
            const startValue = 0;
            const decimalPlaces = (targetValue % 1 !== 0) ? 1 : 0;
            
            const updateValue = () => {
                const currentTime = Date.now();
                const elapsed = currentTime - startTime;
                
                if (elapsed < duration) {
                    const value = easeOutExpo(elapsed, startValue, targetValue, duration);
                    stat.textContent = value.toFixed(decimalPlaces) + suffix;
                    requestAnimationFrame(updateValue);
                } else {
                    stat.textContent = targetValue + suffix;
                }
            };
            
            // Easing function for smoother animation
            const easeOutExpo = (t, b, c, d) => {
                return c * (-Math.pow(2, -10 * t / d) + 1) + b;
            };
            
            // Start animation when stat comes into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateValue();
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(stat);
        });
    };
    
    // Add hover effects to cards
    const comparisonCards = document.querySelectorAll('.comparison-card');
    comparisonCards.forEach(card => {
        // Create subtle movement on mouse move
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left; // x position within the element
            const y = e.clientY - rect.top;  // y position within the element
            
            // Calculate rotation based on mouse position
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const rotateX = (y - centerY) / 20;
            const rotateY = (centerX - x) / 20;
            
            // Apply subtle transform
            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-5px)`;
        });
        
        // Reset transform on mouse leave
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
        });
        
        // Add pulse effect to badge on hover
        const badge = card.querySelector('.card-badge');
        if (badge) {
            card.addEventListener('mouseenter', function() {
                badge.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    badge.style.transform = 'scale(1)';
                }, 300);
            });
        }
    });
    
    // Add hover effect to CTA button
    const ctaButton = document.querySelector('.btn-cta');
    if (ctaButton) {
        ctaButton.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'translateX(5px)';
            }
        });
        
        ctaButton.addEventListener('mouseleave', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'translateX(0)';
            }
        });
    }
    
    // Initialize animations when comparison section is in view
    const comparisonSection = document.querySelector('.refined-comparison');
    if (comparisonSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateStats();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });
        
        observer.observe(comparisonSection);
    }
    
    // Add subtle parallax effect to background
    window.addEventListener('scroll', () => {
        const scrollY = window.scrollY;
        const bgPattern = document.querySelector('.bg-pattern');
        
        if (bgPattern) {
            bgPattern.style.transform = `translateY(${scrollY * 0.05}px)`;
        }
    });
});
