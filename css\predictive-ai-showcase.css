
.predictive-ai-showcase {
    padding: 100px 0 80px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(166deg,
        #F2F0EE 0%,
        #f6f4f9 25%,
        #f6f4f9 45%,
        #FFFFFF 70%,
        #FFFFFF 100%);
		
		
}

.showcase-header {
    margin-bottom: 60px;
    text-align: center;
}

.showcase-title {
    margin-bottom: 20px;
    font-size: 38px;
    line-height: 1.2;
    font-weight: 600;
}

.showcase-description {
    font-size: 18px;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
    color: rgba(44, 46, 60, 0.8);
}

.process-flow {
    position: relative;
    margin-bottom: 50px;
}

.process-nav {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
    position: relative;
    z-index: 2;
    background: white;
    padding: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
    border-radius: 8px 8px 0 0;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.process-step {
    position: relative;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 20px 30px;
    margin: 0;
    flex: 1;
    border-bottom: 3px solid transparent;
}

.step-label {
    font-size: 16px;
    font-weight: 500;
    color: #2c2e3c;
    transition: all 0.3s ease;
    position: relative;
    white-space: nowrap;
}

.step-label::after {
    content: none;
}

.process-step.active {
    border-bottom: 3px solid #e958a1;
    background-color: rgba(233, 88, 161, 0.05);
}

.process-step.active .step-label {
    color: #e958a1;
    font-weight: 600;
}

.process-step:hover:not(.active) {
    background-color: rgba(233, 88, 161, 0.02);
    border-bottom: 3px solid rgba(233, 88, 161, 0.3);
}

.process-step:hover:not(.active) .step-label {
    color: #e958a1;
}

.step-number {
    display: none;
}

.predictive-ai-showcase .accordion-header {
    display: none;
    width: 100%;
    padding: 15px 20px;
    background: white;
    border-radius: 8px;
    margin-bottom: 10px;
    margin-left: 0;
    margin-right: 0;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    box-sizing: border-box;
}

.predictive-ai-showcase .accordion-header.active {
    background: linear-gradient(to right, rgba(233, 88, 161, 0.1), rgba(255, 255, 255, 0.9));
    border-left: 3px solid #e958a1;
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
}

.predictive-ai-showcase .accordion-header.active .step-label-mobile {
    color: #e958a1;
    font-weight: 700;
}

.predictive-ai-showcase .accordion-header.active .step-icon-mobile {
    color: #e958a1;
    transform: rotate(180deg);
}

.predictive-ai-showcase .accordion-header .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.step-label-mobile {
    font-size: 15px;
    font-weight: 600;
    color: #2c2e3c;
}

.step-icon-mobile {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.process-content {
    position: relative;
    min-height: 550px; 
}

.process-panel {
    display: none;
    opacity: 0;
    transition: all 0.5s ease;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    padding-bottom: 40px; 
}

@media (min-width: 768px) {
    .process-panel.active {
        display: block !important;
        opacity: 1;
    }
}

.process-panel.active {
    display: block;
    opacity: 1;
    animation: fadeInUp 0.5s ease forwards;
}

.predictive-ai-showcase .accordion-panel {
    display: none;
    background: white;
    border-radius: 0 0 8px 8px;
    margin-bottom: 15px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.predictive-ai-showcase .accordion-header.active + .accordion-panel {
    display: block;
}

@media (max-width: 767px) {
    .predictive-ai-showcase .accordion-panel {
        display: none;
    }
}

@media (max-width: 767px) {
    .process-content > .process-panel {
        display: none !important;
    }
}

.panel-container {
    display: flex;
    align-items: stretch;
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.5s ease;
    max-width: 1200px;
    margin: 0 auto;
    min-height: 500px; 
}

.panel-visual {
    width: 45%;
    padding: 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.panel-visual::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.05) 0%, rgba(143, 118, 245, 0.05) 100%);
    z-index: 1;
}

.panel-visual::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(rgba(233, 88, 161, 0.1) 2px, transparent 2px),
                      radial-gradient(rgba(143, 118, 245, 0.1) 2px, transparent 2px);
    background-size: 30px 30px, 25px 25px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 1;
}

.panel-visual .svg-container {
    position: relative;
    z-index: 2;
    max-width: 100%;
    height: auto;
}

.panel-content {
    width: 55%;
    padding: 40px 50px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.panel-title {
    font-size: 32px;
    margin-bottom: 15px; 
    color: #2c2e3c;
    font-weight: 700;
    line-height: 1.3;
    position: relative;
    display: inline-block;
}



.panel-description {
    font-size: 16px;
    line-height: 1.7;
    color: rgba(44, 46, 60, 0.8);
    margin-bottom: 30px;
}

.panel-features {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px; /* Ensure space at bottom */
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 12px 18px;
    border-radius: 12px;
    background-color: rgba(248, 249, 250, 0.7);
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    margin-bottom: 0; /* Reset any default margins */
}

.feature-item:hover {
    transform: translateX(5px);
    background-color: rgba(248, 249, 250, 0.9);
    border-left: 3px solid #e958a1;
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.08);
}

.feature-icon {
    margin-right: 18px;
    color: #e958a1;
    font-size: 20px;
    flex-shrink: 0;
    width: 45px;
    height: 45px;
    background: rgba(233, 88, 161, 0.08);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(233, 88, 161, 0.12);
    border: 1px solid rgba(233, 88, 161, 0.1);
}

.feature-item:hover .feature-icon {
    background: linear-gradient(to right, #e958a1, #ff5d74);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(233, 88, 161, 0.25);
    transition: all 0.3s ease;
}

.feature-text {
    font-size: 16px;
    line-height: 1.6;
    width: 100%;
    word-wrap: break-word;
}

.feature-text strong {
    display: block;
    margin-bottom: 3px;
    color: #2c2e3c;
    font-weight: 600;
}


.svg-container {
    width: 100%;
    height: 100%;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.svg-animation {
    width: 100%;
    height: auto;
    max-width: 450px;
}


@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


@media (max-width: 1199px) {
    .panel-visual {
        width: 40%;
    }

    .panel-content {
        width: 60%;
    }
}

@media (max-width: 991px) {
    .predictive-ai-showcase {
        padding: 70px 0 50px;
    }

    .showcase-title {
        font-size: 32px;
    }

    .panel-container {
        flex-direction: column;
    }

    .panel-visual, .panel-content {
        width: 100%;
    }

    .panel-visual {
        min-height: 300px;
        order: 1;
        padding: 30px;
    }

    .panel-content {
        order: 2;
        padding: 35px;
    }

    .panel-title {
        font-size: 24px;
    }

    .feature-item {
        padding: 12px 15px;
    }
}

@media (max-width: 767px) {
 
    .predictive-ai-showcase {
        padding: 50px 0 30px;
    }

    .predictive-ai-showcase .container {
        padding-left: 15px;
        padding-right: 15px;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }


    .predictive-ai-showcase .accordion-header,
    .predictive-ai-showcase .accordion-panel {
        width: 100%;
        max-width: 100%;
        margin-left: auto;
        margin-right: auto;
        box-sizing: border-box;
    }

   
    .predictive-ai-showcase .panel-container {
        width: 100%;
        max-width: 100%;
        margin-left: 0;
        margin-right: 0;
        padding-left: 0;
        padding-right: 0;
        box-sizing: border-box;
    }

   
    .predictive-ai-showcase .panel-content,
    .predictive-ai-showcase .panel-visual {
        padding-left: 20px;
        padding-right: 20px;
        width: 100%;
        box-sizing: border-box;
    }

  
    .predictive-ai-showcase .svg-container {
        width: 100%;
        padding: 0;
        margin: 0;
    }

    .predictive-ai-showcase .svg-animation {
        width: 100%;
        max-width: 100%;
    }

    .process-nav {
        display: none !important; 
    }

    .process-content {
        position: static;
        min-height: auto;
        margin-top: 0;
        width: 100%;
    }

    .process-panel {
        position: static;
        opacity: 1;
        animation: none;
        padding-bottom: 20px;
    }


    .accordion-panel .process-panel {
        display: block !important;
    }

    .predictive-ai-showcase .accordion-header {
        display: block; 
        margin-bottom: 8px;
        width: 100%;
        max-width: 100%;
        border-radius: 8px;
        padding: 15px;
        left: 0;
        right: 0;
        box-sizing: border-box;
    }

    .predictive-ai-showcase .accordion-header .header-content {
        padding: 0 5px;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .predictive-ai-showcase .step-label-mobile {
        display: flex;
        align-items: center;
        flex: 1;
    }

    .predictive-ai-showcase .accordion-panel {
        margin-bottom: 8px;
        width: 100%;
        max-width: 100%;
        border-radius: 0;
    }

    /* Other mobile styles */
    .showcase-title {
        font-size: 24px;
        margin-bottom: 15px;
    }

    .showcase-description {
        font-size: 15px;
    }

    .showcase-header {
        margin-bottom: 30px;
    }

    .panel-title {
        font-size: 20px;
        margin-bottom: 15px;
    }

    .panel-description {
        font-size: 15px;
        margin-bottom: 20px;
    }

    .panel-content, .panel-visual {
        padding: 20px;
        width: 100%;
        box-sizing: border-box;
    }

    /* Ensure feature items don't overflow on mobile */
    .feature-item {
        width: 100%;
        box-sizing: border-box;
        flex-wrap: wrap;
    }

    .feature-text {
        font-size: 14px;
        width: calc(100% - 55px); /* Account for icon width */
    }

    .panel-visual {
        min-height: 220px;
    }

    .feature-item {
        padding: 10px 15px;
    }

    .feature-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
        margin-right: 12px;
    }

    .feature-text {
        font-size: 14px;
    }

    /* Adjust panel container for accordion */
    .panel-container {
        border-radius: 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        min-height: auto;
        overflow: hidden;
    }
}

/* SVG Animation Styles */
.svg-animation {
    width: 100%;
    height: 100%;
    min-height: 300px;
}

/* ValueBid Framework Section Styling - Matching Our Predictive AI mobile accordions */
.bg-very-light-gray .accordion-style-04 .accordion-item {
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.bg-very-light-gray .accordion-style-04 .accordion-header {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    text-align: left;
}

.bg-very-light-gray .accordion-style-04 .accordion-title {
    position: relative;
    margin-bottom: 0;
    padding-right: 20px;
    color: #2c2e3c;
    font-weight: 600;
    font-size: 18px;
    text-align: left;
}

.bg-very-light-gray .accordion-style-04 .accordion-item.active-accordion .accordion-header {
    background: linear-gradient(to right, rgba(233, 88, 161, 0.1), rgba(255, 255, 255, 0.9));
    border-left: 3px solid #e958a1;
    border-radius: 8px 8px 0 0;
}

.bg-very-light-gray .accordion-style-04 .accordion-item.active-accordion .accordion-title {
    color: #e958a1 !important;
    font-weight: 700;
}

/* Match the arrow styling with Our Predictive AI mobile accordions */
.bg-very-light-gray .accordion-style-04 .accordion-title i.fa-angle-down,
.bg-very-light-gray .accordion-style-04 .accordion-title i.fa-angle-right {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    color: #e958a1;
    font-size: 16px;
}

/* Rotate the down arrow when active */
.bg-very-light-gray .accordion-style-04 .accordion-item.active-accordion .accordion-title i.fa-angle-down {
    transform: translateY(-50%) rotate(180deg);
}

/* Hide the right-angle icon when active and show down-angle icon */
.bg-very-light-gray .accordion-style-04 .accordion-item.active-accordion .accordion-title i.fa-angle-right {
    display: none;
}

.valuebid-showcase .accordion-style-04 .accordion-item.active-accordion .accordion-title i.fa-angle-down {
    display: inline-block;
}

/* Hide the down-angle icon when not active and show right-angle icon */
.valuebid-showcase .accordion-style-04 .accordion-item:not(.active-accordion) .accordion-title i.fa-angle-down {
    display: none;
}

.valuebid-showcase .accordion-style-04 .accordion-item:not(.active-accordion) .accordion-title i.fa-angle-right {
    display: inline-block;
}

.valuebid-showcase .accordion-style-04 .accordion-body {
    background: white;
    border-radius: 0 0 8px 8px;
}

/* Match the SVG container styling for ValueBid section */
.valuebid-showcase .svg-container {
    width: 100%;
    height: 100%;
    min-height: 500px; /* Increased minimum height for the container */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0; /* Add some vertical padding */
}

.valuebid-showcase .svg-animation {
    width: 100%;
    max-width: 100%; /* Set to 100% to fill the container */
    height: auto;
}

/* Data Flow Animation */
.data-source {
    transition: all 0.5s ease;
}

.data-path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
}

.data-particle {
    opacity: 0;
}

/* Neural Network Animation */
.network-node {
    transform-origin: center;
    transition: all 0.5s ease;
}

.network-connection {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
}

/* Prediction Graph Animation */
.prediction-line {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
}

.confidence-area {
    opacity: 0;
}

/* Learning Cycle Animation */
.cycle-path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
}

.cycle-node {
    transform-origin: center;
    transition: all 0.5s ease;
}

/* ValueBid Showcase Section - Match padding with Predictive AI section */
.valuebid-showcase .container {
    padding-left: 15px;
    padding-right: 15px;
    width: 100%;
    max-width: 1140px;
    margin-left: auto;
    margin-right: auto;
}

.valuebid-showcase .row.align-items-center {
    padding-left: 0;
    padding-right: 0;
}

/* Match accordion styling with Predictive AI section */
.valuebid-showcase .accordion-style-01 {
    width: 100%;
    max-width: 100%;
    min-width: 320px; /* Ensure minimum width for accordion */
}

.valuebid-showcase .accordion-item {
    margin-bottom: 10px;
}

/* Match font sizes between both sections */
.valuebid-showcase .accordion-title,
.predictive-ai-showcase .step-label-mobile {
    font-size: 16px;
    font-weight: 600;
}

/* Icon styling for ValueBid accordions */
.valuebid-showcase .accordion-title {
    display: flex;
    align-items: center;
}

/* Feature icon wrapper to override global styles */
.valuebid-showcase .feature-icon-wrapper {
    display: inline-flex;
    align-items: center;
    position: static;
    transform: none;
}

.valuebid-showcase .feature-icon-wrapper i {
    color: #232323; /* Same color as title text */
    position: static !important;
    transform: none !important;
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
    margin-right: 12px; /* Add right margin after icons */
    font-size: 16px; /* Match the font size of the title */
}

/* Position the chevron icon on the right */
.valuebid-showcase .accordion-title i.icon-small {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #e958a1;
}

/* Active accordion styling */
.valuebid-showcase .accordion-item.active-accordion .accordion-title .feature-icon-wrapper i {
    color: #e958a1; /* Pink color for active state */
    font-weight: bold;
}

/* Rotate chevron when accordion is active */
.valuebid-showcase .accordion-item.active-accordion .accordion-title i.bi-chevron-down.icon-small {
    transform: translateY(-50%) rotate(180deg);
}

/* Ensure text is properly positioned */
.valuebid-showcase .accordion-title span:not(.feature-icon-wrapper) {
    flex: 1;
}

@media (max-width: 1024px) {
    .valuebid-showcase .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .valuebid-showcase .col-xl-5,
    .valuebid-showcase .col-lg-6,
    .valuebid-showcase .col-xl-7,
    .valuebid-showcase .col-lg-6 {
        padding-left: 15px;
        padding-right: 15px;
        width: 100%;
    }

    /* Adjust SVG container on tablet */
    .valuebid-showcase .svg-container {
        min-height: 400px;
        margin-top: 30px;
    }

    .valuebid-showcase .svg-animation {
        max-width: 100%;
    }

    .valuebid-showcase .accordion-style-01 {
        width: 100%;
        max-width: 100%;
        min-width: 0; /* Reset min-width on tablet */
    }

    .valuebid-showcase .accordion-header {
        width: 100%;
        max-width: 100%;
        margin-left: auto;
        margin-right: auto;
        box-sizing: border-box;
    }

    /* Responsive font sizes for tablet */
    .valuebid-showcase .accordion-title,
    .predictive-ai-showcase .step-label-mobile {
        font-size: 15px;
    }

    /* Adjust icon size on tablet */
    .valuebid-showcase .accordion-title .feature-icon-wrapper i {
        font-size: 15px;
    }
}

/* Mobile specific styles */
@media (max-width: 767px) {
    /* Responsive font sizes for mobile */
    .valuebid-showcase .accordion-title,
    .predictive-ai-showcase .step-label-mobile {
        font-size: 16px;
    }

    /* Adjust SVG container on mobile */
    .valuebid-showcase .svg-container {
        min-height: 350px;
    }

    .valuebid-showcase .svg-animation {
        max-width: 100%;
    }

    /* Adjust icon size on mobile */
    .valuebid-showcase .accordion-title .feature-icon-wrapper i,
    .predictive-ai-showcase .step-label-mobile i.bi {
        font-size: 14px;
    }

    /* Ensure proper spacing on mobile */
    .valuebid-showcase .accordion-title {
        padding-right: 25px;
    }

    /* Fix content overflow in ValueBid accordions */
    .valuebid-showcase .accordion-body {
        padding: 15px 20px;
        width: 100%;
        box-sizing: border-box;
    }

    .valuebid-showcase .accordion-body p {
        width: 100%;
        word-wrap: break-word;
        margin-bottom: 0;
    }
}
