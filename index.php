<?php include 'header.php'; ?>
        <!-- start hero section -->
        <section class="cover-background  top-space-padding pb-0 overflow-hidden position-relative lg-pb-30px">
            <div class="professional-gradient-container">
                <div class="corner-gradient top-left"></div>
                <div class="corner-gradient top-right"></div>
                <div class="corner-gradient bottom-left"></div>
                <div class="corner-gradient bottom-right"></div>
                <div class="diagonal-gradient"></div>
                <div class="mesh-overlay"></div>
                <div class="vignette-overlay"></div>
            </div>
            <div id="particles-style-03" class="h-100 position-absolute left-0px top-0 w-100" data-particle="true" data-particle-options='{"particles": {"number": {"value": 70,"density": {"enable": true,"value_area": 1800}},"color": {"value": ["#e958a1", "#d15ec7", "#ff7042", "#8f76f5", "#ff5a5a"]},"shape": {"type": ["circle"],"stroke":{"width":0,"color":"#000000"}},"opacity": {"value": 0.5,"random": true,"anim": {"enable": true,"speed": 0.8,"sync": false}},"size": {"value": 3,"random": true,"anim": {"enable": true,"speed": 0.8,"sync": false}},"line_linked":{"enable":true,"distance":180,"color":"#e958a1","opacity":0.2,"width":1},"move": {"enable": true,"speed":1.0,"direction": "none","random": true,"straight": false,"out_mode": "out","bounce": false,"attract": {"enable": true,"rotateX": 600,"rotateY": 1200}}},"interactivity": {"detect_on": "canvas","events": {"onhover": {"enable": true,"mode": "grab"},"onclick": {"enable": true,"mode": "repulse"},"resize": true},"modes":{"grab":{"distance":180,"line_linked":{"opacity":0.4}},"repulse":{"distance":200,"duration":0.4}}},"retina_detect": true}'></div>
            <div class="container h-100">
                <!-- Removed distracting background elements for a more professional look -->
                <div class="row align-items-center h-100 md-mt-50px md-mb-10px pt-5">
                    <div class="col-xl-6 col-lg-6 mb-9 position-relative z-index-1 ps-lg-5" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <div class="d-flex align-items-center mb-20px">
                            <span class="fs-12 fw-light text-white opacity-90 primary-font ls-wide">
                                 <span class="ai-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="pulse">
                                        <!-- Modern AI chip/processor shape -->
                                        <rect x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5"/>
                                        <!-- Circuit lines -->
                                        <path class="circuit1" d="M8 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit2" d="M12 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit3" d="M16 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit4" d="M8 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit5" d="M12 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit6" d="M16 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit7" d="M2 8H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit8" d="M2 12H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit9" d="M2 16H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit10" d="M20 8H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit11" d="M20 12H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit12" d="M20 16H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <!-- Inner processor grid -->
                                        <path class="grid" d="M8 8H16M8 12H16M8 16H16M8 8V16M12 8V16M16 8V16" stroke="url(#paint0_linear_ai_icon)" stroke-width="0.75" stroke-linecap="round" stroke-dasharray="1 2"/>
                                        <!-- Central core -->
                                        <rect class="core" x="10" y="10" width="4" height="4" fill="url(#paint0_linear_ai_icon)"/>
                                        <!-- Sparkle overlay -->
                                        <rect class="sparkle" x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint1_linear_ai_icon)" stroke-width="1" stroke-opacity="0.7"/>
                                        <defs>
                                            <linearGradient id="paint0_linear_ai_icon" x1="2" y1="2" x2="22" y2="22" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#e958a1"/>
                                                <stop offset="0.5" stop-color="#8f76f5"/>
                                                <stop offset="1" stop-color="#4a9eff"/>
                                            </linearGradient>
                                            <linearGradient id="paint1_linear_ai_icon" x1="20" y1="4" x2="4" y2="20" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#ffffff"/>
                                                <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                <span class="text-gradient-purple-blue ls-2px">AI-POWERED</span>
                                <span class="mx-2">|</span>
                                <span data-fancy-text='{ "effect": "rotate", "string": ["Value Bidding", "Customer Targeting", "ROAS Optimization", "Profit Forecasting"], "speed": 50, "duration": 3500 }'></span>
                            </span>
                        </div>
                        <h1 class="alt-font text-white fw-400 mb-30px lh-1-2 fs-45 md-fs-42 sm-fs-36 xs-fs-34" data-anime='{ "el": "words", "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 300, "staggervalue": 150, "easing": "easeOutQuad" }'>Predict Profit<span class="d-block">Bid Smarter<br><span class="text-gradient-purple-blue fw-700 ">Scale Faster<span class="text-gradient-purple">.</span></span></span></h1>
                        <div class="primary-font fw-light fs-16 w-90 sm-w-100 mb-40px xs-mb-30px text-white opacity-75 lh-1-7 ls-wide">AdZeta's smart bidding technology looks beyond quick wins to focus on customers who bring lasting value. Built for D2C and e-commerce teams who want ROAS today and LTV tomorrow.</div>
                        <div class="d-flex flex-wrap" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 800, "easing": "easeOutQuad" }'>
                            <a href="free-ad-audit.php" target="blank" class="btn btn-large btn-switch-text box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-gradient-pink-orange btn-rounded me-15px mt-20px fw-400 alt-font">
                            <span>
                            <span class="btn-double-text fw-400 alt-font fs-16 md-fs-16" data-text="Get Free Audit">Get Free Audit&nbsp;</span>
                            <span><i class="fa-solid fa-chart-line"></i></span>
                            </span>
                            </a>
                              <a href="https://www.youtube.com/watch?v=18rbBqm056U?autoplay=1" class="popup-youtube btn btn-large btn-switch-text box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-transparent-white-light btn-rounded border-1 mt-20px fw-semibold alt-font">
                                <span>
                                    <span class="btn-double-text fw-400 fs-16" data-text="Play Video">Adzeta in Action&nbsp;&nbsp;</span>
                                    <span><svg width="30" height="30" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
									  <path d="M10,9.35,15,12l-5,2.65ZM12,6a54.36,54.36,0,0,0-7.56.38A1.53,1.53,0,0,0,3.38,7.44,24.63,24.63,0,0,0,3,12a24.63,24.63,0,0,0,.38,4.56,1.53,1.53,0,0,0,1.06,1.06A54.36,54.36,0,0,0,12,18a54.36,54.36,0,0,0,7.56-.38,1.53,1.53,0,0,0,1.06-1.06A24.63,24.63,0,0,0,21,12a24.63,24.63,0,0,0-.38-4.56,1.53,1.53,0,0,0-1.06-1.06A54.36,54.36,0,0,0,12,6h0m0-1s6.25,0,7.81.42a2.51,2.51,0,0,1,1.77,1.77A25.87,25.87,0,0,1,22,12a25.87,25.87,0,0,1-.42,4.81,2.51,2.51,0,0,1-1.77,1.77C18.25,19,12,19,12,19s-6.25,0-7.81-.42a2.51,2.51,0,0,1-1.77-1.77A25.87,25.87,0,0,1,2,12a25.87,25.87,0,0,1,.42-4.81A2.51,2.51,0,0,1,4.19,5.42C5.75,5,12,5,12,5Z"/>
									</svg></span>
                                </span>
                                </a>
                        </div>
                    </div>
                    <div class="col-xl-6 col-lg-6 align-self-center">
                        <div class="position-relative z-index-9" style="min-height: 550px; height: 100%;">
                            <!-- Apple-inspired Minimalist Graph -->
                            <div class="animated-graph-container position-relative z-index-9" style="min-height: 550px; height: 100%;" aria-label="Elegant comparison graph showing how AdZeta's AI Value-Based Bidding delivers superior profit growth compared to traditional ROAS-focused advertising.">
                                <!-- Clean, focused visualization with sequential animation and subtle interactivity -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end hero section -->
        <!-- start section -->
        <section class="pt-30px pb-30px overflow-hidden">
            <div class="container">
                <div class="row position-relative clients-style-08" data-anime='{ "translateX": [0, 0], "opacity": [0,1], "duration": 800, "delay":100, "staggervalue": 150, "easing": "easeOutQuad" }'>
                    <div class="col swiper text-center feather-shadow" data-slider-options='{ "slidesPerView": 2, "spaceBetween":0, "speed": 3000, "loop": true, "pagination": { "el": ".slider-four-slide-pagination-2", "clickable": false }, "allowTouchMove": false, "autoplay": { "delay":0, "disableOnInteraction": false, "pauseOnMouseEnter": false}, "navigation": { "nextEl": ".slider-four-slide-next-2", "prevEl": ".slider-four-slide-prev-2" }, "keyboard": { "enabled": true, "onlyInViewport": true }, "breakpoints": { "1200": { "slidesPerView": 5 }, "768": { "slidesPerView": 3 }, "576": { "slidesPerView": 2 } }, "effect": "slide" }'>
                        <div class="swiper-wrapper marquee-slide">
                            <!-- start client item -->
                            <div class="swiper-slide">
                                <a href="#"><img src="images/logos/ELA_Logo-01-1536x535.png" class="h-25px xs-h-25px" alt="" /></a>
                            </div>
                            <!-- end client item -->
								<div class="swiper-slide">
                                <a href="#"><img src="images/logos/meta.png" class="h-30px xs-h-25px" alt="" /></a>
                            </div>
                       
                            <div class="swiper-slide">
                                <a href="#"><img src="images/logos/deco-de-mode-logo.png" class="h-35px xs-h-25px" alt="" /></a>
                            </div>
                          	
                            <div class="swiper-slide">
                                <a href="#"><img src="images/logos/Metascapes-Web-Logo-V2.png" class="h-35px xs-h-35px" alt="" /></a>
                            </div>
                          
                            <div class="swiper-slide">
                                <a href="#"><img src="images/logos/shopify.png" class="h-35px xs-h-25px" alt="" /></a>
                            </div>
                        
							<div class="swiper-slide">
                                <a href="#"><img src="images/logos/aws.png" class="h-30px xs-h-25px" alt="" /></a>
                            </div>
                       
                            <div class="swiper-slide">
                                <a href="#"><img src="images/logos/klaviyo.png" class="h-30px xs-h-25px" alt="" /></a>
                            </div>
							
							<div class="swiper-slide">
                                <a href="#"><img src="images/logos/woo.png" class="h-30px xs-h-25px" alt="" /></a>
                            </div>
						
                            <div class="swiper-slide">
                                <a href="#"><img src="images/logos/logo-addiction-rehab-toronto.png" class="h-35px xs-h-25px" alt="" /></a>
                            </div>
							 <div class="swiper-slide">
                                <a href="#"><img src="images/logos/gcloud.png" class="h-35px xs-h-35px" alt="" /></a>
                            </div>
                           
						
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
        <!-- SECTION 3: Minimal Futuristic Comparison - Start -->
        <section class="comparison-section-v2 pb-0 overflow-hidden">
            <div class="comparison-container container">
                <!-- Section Header -->
                <div class="row justify-content-center mb-4" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 150, "easing": "easeOutQuad" }'>
                    <div class="col-xl-8 col-lg-10 col-md-11 text-center">
                        <div class="mb-10px">
                            <!-- <span class="w-25px h-1px d-inline-block bg-base-color me-5px align-middle"></span> -->
                            <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">The Smarter Bidding Approach</span>
                        </div>
                        <h2 class="alt-font fw-600 text-dark-gray ls-minus-1px mb-3 fs-32 md-fs-31 sm-fs-30">Don't Treat All Clicks Equally<br > <span class="fw-700">AdZeta AI vs. The Old Way.</span></h2>
                        <p class="w-90 mx-auto fs-16 fw-400 lh-30"> Compare how conventional bidding overlooks value while AdZeta's Predictive AI targets and captures your most profitable customers.</p>
                    </div>
                </div>
                <!-- Mobile Toggle (Visible below md breakpoint: 768px) -->
                <div class="comparison-toggle-mobile d-md-none" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 300, "easing": "easeOutQuad" }'>
                    <!-- Modern Toggle Slider -->
                    <div class="modern-toggle-slider" id="comparisonToggle">
                        <div class="toggle-option traditional active" data-target="#traditional-content-mobile">
                            <i class="feather icon-feather-bar-chart-2"></i>
                            <span>Traditional</span>
                        </div>
                        <div class="toggle-option AdZeta" data-target="#AdZeta-content-mobile">
                            <i class="feather icon-feather-trending-up"></i>
                            <span>AdZeta AI</span>
                        </div>
                        <div class="slider-indicator"></div>
                    </div>
                </div>
                <!-- Comparison Content Area -->
                <div class="comparison-content-area">
                    <!-- Mobile Content (Tab Panes, hidden above md) -->
                    <div class="mobile-comparison-content d-md-none">
                        <!-- Tab content for mobile -->
                        <div class="tab-content" id="mobileTabContent">
                            <div class="tab-pane show active" id="traditional-content-mobile" role="tabpanel" aria-labelledby="traditional-tab-mobile">
                                <div class="comparison-card traditional box-shadow-small" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 500, "easing": "easeOutQuad" }'>
                                    <h3 class="alt-font fw-600 text-dark-gray fs-22 mb-2">Standard Bidding</h3>
                                    <span class="sub-tag-outline fs-16 fw-500">The Conventional Approach</span>
                                    <ul>
                                        <li><span class="icon-wrapper"><i class="feather icon-feather-x"></i></span>Treats all customers equally, regardless of potential value.</li>
                                        <li><span class="icon-wrapper"><i class="feather icon-feather-x"></i></span>Optimizes for short-term metrics like CPA, ignoring LTV.</li>
                                        <li><span class="icon-wrapper"><i class="feather icon-feather-x"></i></span>Wastes budget (up to 40%) on low-value acquisitions.</li>
                                        <li><span class="icon-wrapper"><i class="feather icon-feather-x"></i></span>Limited ability to scale profitably and sustainably.</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="tab-pane" id="AdZeta-content-mobile" role="tabpanel" aria-labelledby="AdZeta-tab-mobile">
                                <div class="comparison-card AdZeta recommended box-shadow-small" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 500, "easing": "easeOutQuad" }'>
                                    <h3 class="alt-font fw-600 text-dark-gray fs-22 mb-2">Value-Based Bidding</h3>
                                    <span class="sub-tag fs-16 fw-500">Our AI-Powered Advantage</span>
                                    <ul>
                                        <li><span class="icon-wrapper"><i class="feather icon-feather-check"></i></span>Identifies and targets high-LTV customers with precision.</li>
                                        <li><span class="icon-wrapper"><i class="feather icon-feather-check"></i></span>Optimizes bids for long-term profit and customer value.</li>
                                        <li><span class="icon-wrapper"><i class="feather icon-feather-check"></i></span>Reduces wasted ad spend significantly (up to 40%).</li>
                                        <li><span class="icon-wrapper"><i class="feather icon-feather-check"></i></span>Enables sustainable, profitable scaling for growth.</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Desktop Content (Side-by-Side, hidden below md) -->
                    <div class="desktop-comparison-content d-none d-md-flex row" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 500, "staggervalue": 200, "easing": "easeOutQuad" }'>
                        <!-- Traditional Card (Desktop) -->
                        <div class="col-md-6">
                            <div class="comparison-card traditional box-shadow-quadruple-large box-shadow-extra-large-hover  h-100">
                                <!-- Added h-100 for equal height -->
                                <h3 class="alt-font fw-600 text-dark-gray fs-22 mb-2">Standard Bidding</h3>
                                <span class="sub-tag-outline fs-16 fw-500">The Conventional Approach</span>
                                <ul>
                                    <li><span class="icon-wrapper"><i class="feather icon-feather-x"></i></span>Treats all customers equally, regardless of potential value.</li>
                                    <li><span class="icon-wrapper"><i class="feather icon-feather-x"></i></span>Optimizes for short-term metrics like CPA, ignoring LTV.</li>
                                    <li><span class="icon-wrapper"><i class="feather icon-feather-x"></i></span>Wastes budget (up to 40%) on low-value acquisitions.</li>
                                    <li><span class="icon-wrapper"><i class="feather icon-feather-x"></i></span>Limited ability to scale profitably and sustainably.</li>
                                </ul>
                            </div>
                        </div>
                        <!-- AdZeta Card (Desktop) -->
                        <div class="col-md-6">
                            <div class="comparison-card AdZeta box-shadow-quadruple-large box-shadow-extra-large-hover recommended h-100">
                                <!-- Added h-100 for equal height -->
                                <h3 class="alt-font fw-600 text-dark-gray fs-22 mb-2">Value-Based Bidding</h3>
                                <span class="sub-tag fs-16 fw-500">Our AI-Powered Advantage</span>
                                <ul>
                                    <li><span class="icon-wrapper"><i class="feather icon-feather-check"></i></span>Identifies and targets high-LTV customers with precision.</li>
                                    <li><span class="icon-wrapper"><i class="feather icon-feather-check"></i></span>Optimizes bids for long-term profit and customer value.</li>
                                    <li><span class="icon-wrapper"><i class="feather icon-feather-check"></i></span>Reduces wasted ad spend significantly (up to 40%).</li>
                                    <li><span class="icon-wrapper"><i class="feather icon-feather-check"></i></span>Enables sustainable, profitable scaling for growth.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End comparison-content-area -->
            </div>
        </section>
        <!-- SECTION 3: Minimal Futuristic Comparison - End -->
        <!-- start section -->
        <section class="metrics-section pt-0 mt-n1 overflow-hidden">
		
            <div class="container">
                <!-- Modern Heading -->
                <div class="row justify-content-center">
                    <div class="col-xl-7 col-lg-10 col-md-11 text-center pt-4">
                        <div class="metrics-heading">
                            <div class="mb-10px">
                                <!-- <span class="w-25px h-1px d-inline-block bg-base-color me-5px align-middle"></span> -->
                                <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">Proven Results</span>
                            </div>
                            <h2 class="alt-font fw-600 text-dark-gray ls-minus-1px fs-32 md-fs-31 sm-fs-30">
                                With Predictive LTV Insights, Your <br><span class="fw-700">Growth Metrics Will Soar.</span>
                            </h2>
                            <p>These are the typical results our clients achieve – and you can too.</p>
                        </div>
                    </div>
                </div>
                <!-- Metrics Cards Row -->
                <div class="row">
                    <!-- ROAS Improvement Metric -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="metric-card box-shadow-large box-shadow-extra-large-hover">
                            <span class="metric-title">LTV-Focused ROAS Lift</span>
                            <div class="counter-wrapper">
                                <span class="counter-number" data-target="4">4</span>
                                <span class="unit">x</span>
                            </div>
                            <span class="trend trend-up">
                            <i class="bi bi-arrow-up text-pink"></i><span class="text-pink">Improvement</span>
                            </span>
                            <p class="metric-description">Higher return by optimizing bids for predicted lifetime value, not just initial sales.</p>
                            <div class="metric-graph">
                                <svg viewBox="0 0 300 60" preserveAspectRatio="none">
                                <defs>
                                    <linearGradient id="positive-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" stop-color="rgba(244, 88, 136, 0.2)" />
                                        <stop offset="100%" stop-color="rgba(244, 88, 136, 0.01)" />
                                    </linearGradient>
                                </defs>
                                <path class="area" d="M0,50 C50,40 100,20 150,10 S250,5 300,15 L300,60 L0,60 Z" fill="url(#positive-gradient)" />
                                <path class="line positive-line" d="M0,50 C50,40 100,20 150,10 S250,5 300,15" />
                            </svg>
                            </div>
                        </div>
                    </div>
                    <!-- CAC Reduction Metric -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="metric-card box-shadow-large box-shadow-extra-large-hover">
                            <span class="metric-title">Average CAC Reduction</span>
                            <div class="counter-wrapper">
                                <span class="counter-number" data-target="36">36</span>
                                <span class="unit">%</span>
                            </div>
                            <span class="trend trend-down">
                            <i class="bi bi-arrow-down text-blue"></i><span class="text-blue">Savings</span>
                            </span>
                            <p class="metric-description">Lower acquisition costs through efficient, value-driven AI targeting.</p>
                            <div class="metric-graph">
                                <svg viewBox="0 0 300 60" preserveAspectRatio="none">
                                <defs>
                                    <linearGradient id="reduction-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" stop-color="rgba(106, 140, 175, 0.2)" />
                                        <stop offset="100%" stop-color="rgba(106, 140, 175, 0.01)" />
                                    </linearGradient>
                                </defs>
                                <path class="area" d="M0,10 C50,20 100,35 150,40 S250,50 300,45 L300,60 L0,60 Z" fill="url(#reduction-gradient)" />
                                <path class="line reduction-line" d="M0,10 C50,20 100,35 150,40 S250,50 300,45" />
                            </svg>
                            </div>
                        </div>
                    </div>
                    <!-- LTV Increase Metric -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="metric-card box-shadow-large box-shadow-extra-large-hover">
                            <span class="metric-title">Average LTV Increase</span>
                            <div class="counter-wrapper">
                                <span class="counter-number" data-target="37">37</span>
                                <span class="unit">%</span>
                            </div>
                            <span class="trend trend-up">
                            <i class="bi bi-arrow-up text-pink"></i><span class="text-pink">Value Added</span>
                            </span>
                            <p class="metric-description">Significant boost in Customer Lifetime Value via optimized acquisition.</p>
                            <div class="metric-graph">
                                <svg viewBox="0 0 300 60" preserveAspectRatio="none">
                                <defs>
                                    <linearGradient id="positive-gradient-2" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" stop-color="rgba(244, 88, 136, 0.2)" />
                                        <stop offset="100%" stop-color="rgba(244, 88, 136, 0.01)" />
                                    </linearGradient>
                                </defs>
                                <path class="area" d="M0,45 C50,40 100,25 150,15 S250,5 300,10 L300,60 L0,60 Z" fill="url(#positive-gradient-2)" />
                                <path class="line positive-line" d="M0,45 C50,40 100,25 150,15 S250,5 300,10" />
                            </svg>
                            </div>
                        </div>
                    </div>
                    <!-- Ad Waste Reduction Metric -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="metric-card box-shadow-large box-shadow-extra-large-hover">
                            <span class="metric-title">Ad Waste Reduction</span>
                            <div class="counter-wrapper">
                                <span class="counter-number" data-target="42">42</span>
                                <span class="unit">%</span>
                            </div>
                            <span class="trend trend-down">
                            <i class="bi bi-arrow-down text-blue"></i><span class="text-blue">Efficiency Gain</span>
                            </span>
                            <p class="metric-description">Less budget wasted by automatically avoiding low-value segments.</p>
                            <div class="metric-graph">
                               <svg viewBox="0 0 300 60" preserveAspectRatio="none">
                                <defs>
                                    <linearGradient id="reduction-gradient-2" x1="0%" y1="0%" x2="0%" y2="100%">
                                        <stop offset="0%" stop-color="rgba(106, 140, 175, 0.2)" />
                                        <stop offset="100%" stop-color="rgba(106, 140, 175, 0.01)" />
                                    </linearGradient>
                                </defs>
                                <path class="area" d="M0,15 C50,25 100,30 150,35 S250,45 300,40 L300,60 L0,60 Z" fill="url(#reduction-gradient-2)" />
                                <path class="line reduction-line" d="M0,15 C50,25 100,30 150,35 S250,45 300,40" />
                            </svg>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Data Source Information -->
                <div class="row justify-content-center">
                    <div class="col-12">
                        <div class="metrics-footer d-flex justify-content-center align-items-center flex-wrap text-center py-2">
                            <div class="d-flex align-items-center flex-wrap justify-content-center">
                                <span class="me-2 text-muted">Data from 50+ D2C brands across Fashion, Beauty, Home & Other categories</span>
                                <a href="#" class="text-nowrap">View methodology</a>
                                <span class="ms-3 text-muted text-nowrap">Updated May 2023</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
		  <section class="position-relative overflow-hidden py-0">
            <div class="skrollr-parallax mx-auto pt-7 pb-7 md-pt-12 md-pb-12" data-bottom-top="width: 63%" data-center-top="width: 100%;" data-parallax-background-ratio="0.5" style="background-image: url('images/demo-consulting-parallax.jpg')">
                <div class="opacity-extra-medium bg-gradient-black-dark-orange"></div>
                <div class="container">
                    <div class="row justify-content-center align-items-center mb-5">
                        <div class="col-xl-9 col-lg-10 text-center position-relative last-paragraph-no-margin parallax-scrolling-style-2">
                              <a href="https://www.youtube.com/watch?v=18rbBqm056U?autoplay=1" class="position-relative d-inline-block text-center rounded-circle border border-3 border-color-transparent-white-very-light video-icon-box video-icon-extra-large popup-youtube mb-5">
                            <span>
                            <span class="video-icon">
                            <i class="fa-solid fa-play text-white"></i>
                            </span>
                            </span>
                            </a>
                            <span class="opacity-6 ls-2px text-uppercase alt-font text-white d-block mb-2 fw-500">Powered by Adzeta AI</span>
                            <h2 class="text-white fw-600 alt-font ls-minus-2px text-shadow-double-large">See How We Drive E-commerce Growth</h2>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- Make sure to include Bootstrap Icons CSS if using bi-* classes -->
		
        <!-- start section -->
        <section class="homepage overflow-hidden">
            <div class="container position-relative">
                <div class="row align-items-center mb-7">
                    <div class="col-xxl-4 col-lg-5 md-mb-15 sm-mb-20 text-center text-lg-start">
                        <div class="mb-10px">
                           <!--  <span class="w-25px h-1px d-inline-block bg-base-color me-5px align-middle"></span> -->
                            <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">AI-POWERED PROFIT PROCESS</span>
                        </div>
                        <h2 class="alt-font text-dark-gray fw-700 ls-minus-1px mb-40px fs-32 md-fs-31 sm-fs-30">Transform Ad Spend Into Your Profit Engine</h2>
                        <div class="row row-cols-1" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                            <!-- start process step item -->
                            <div class="col-12 process-step-style-05 position-relative hover-box">
                                <div class="process-step-item d-flex">
                                    <div class="process-step-icon-wrap position-relative">
                                        <div class="process-step-icon d-flex justify-content-center align-items-center mx-auto rounded-circle h-60px w-60px fs-14 bg-light-red fw-700 position-relative">
                                            <span class="number position-relative z-index-1 text-dark-gray">01</span>
                                            <div class="box-overlay bg-base-color rounded-circle"></div>
                                        </div>
                                        <span class="progress-step-separator bg-dark-gray opacity-1"></span>
                                    </div>
                                    <div class="process-content ps-30px last-paragraph-no-margin mb-30px">
                                        <span class="d-block fw-700 text-dark-gray mb-5px fs-22 md-fs-19 sm-fs-18 xs-fs-18">Uncover High-Value Customers</span>
                                        <p class="w-90 lg-w-100 fs-16 md-fs-14 sm-fs-14">Our <span class="fw-600">Predictive AI</span> analyzes key customer signals—behavior, purchase patterns, engagement—to precisely identify the segments driving your real long-term profit (LTV)</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end process step item -->
                            <!-- start process step item -->
                            <div class="col-12 process-step-style-05 position-relative hover-box">
                                <div class="process-step-item d-flex">
                                    <div class="process-step-icon-wrap position-relative">
                                        <div class="process-step-icon d-flex justify-content-center align-items-center mx-auto rounded-circle h-60px w-60px fs-14 bg-light-red fw-700 position-relative">
                                            <span class="number position-relative z-index-1 text-dark-gray">02</span>
                                            <div class="box-overlay bg-base-color rounded-circle"></div>
                                        </div>
                                        <span class="progress-step-separator bg-dark-gray opacity-1"></span>
                                    </div>
                                    <div class="process-content ps-30px last-paragraph-no-margin mb-30px">
                                        <span class="d-block fw-700 text-dark-gray mb-5px fs-22 md-fs-19 sm-fs-18 xs-fs-18">Predict Future Profitability</span>
                                        <p class="w-90 lg-w-100 fs-16  md-fs-14 sm-fs-14">Know who's worth more, sooner. We accurately forecast future <span class="fw-600">Customer Lifetime Value (LTV)</span> before you bid, targeting users likely to deliver <span class="fw-600">3-5x higher returns</span>.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end process step item -->
                            <!-- start process step item -->
                            <div class="col-12 process-step-style-05 position-relative hover-box xs-mb-30px">
                                <div class="process-step-item d-flex">
                                    <div class="process-step-icon-wrap position-relative">
                                        <div class="process-step-icon d-flex justify-content-center align-items-center mx-auto rounded-circle h-60px w-60px fs-14 bg-light-red fw-700 position-relative">
                                            <span class="number position-relative z-index-1 text-dark-gray">03</span>
                                            <div class="box-overlay bg-base-color rounded-circle"></div>
                                        </div>
                                    </div>
                                    <div class="process-content ps-30px last-paragraph-no-margin">
                                        <span class="d-block fw-700 text-dark-gray mb-5px fs-22 md-fs-19 sm-fs-18 xs-fs-18">Optimize Bids Intelligently</span>
                                        <p class="w-90 lg-w-100 fs-16 md-fs-14 sm-fs-14">Activate smarter bidding. Our AI uses these predictions for <span class="fw-600">Value-Based Bidding (VBB)</span>, automatically focusing your <span class="fw-600">ad spend</span> on high-LTV customers on Google & Meta and reducing waste by up to <span class="fw-600">40%</span>.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end process step item -->
                        </div>
                    </div>
                    <div class="col-lg-7 offset-xxl-1 position-relative md-mb-30px sm-mb-15"
                        data-anime='{ "translateX": [0, 0], "opacity": [0,1], "duration": 1200, "delay": 0, "staggervalue": 150, "easing": "easeOutQuad" }'>
                        <div class="chip-image-container position-relative">
                            <img src="images/demo-corporate-10.png" class="position-relative z-index-9" alt="">
                            <img src="images/round-bg.webp" class="absolute-middle-center xs-w-95 " alt="">
                            <img src="images/demo-corporate-051.png" class="position-absolute profit-engine-facebook left-0px "
                                data-bottom-top="transform: translateY(-50px)" data-top-bottom="transform: translateY(50px)"
                                alt="">
                            <!--  <img src="images/demo-corporate-06.png" class="position-absolute top-150px left-30"
                                data-bottom-top="transform: translateY(30px)" data-top-bottom="transform: translateY(-30px)"
                                alt=""> -->
                            <!--    <img src="images/demo-corporate-07.png" class="position-absolute top-50px right-30"
                                data-bottom-top="transform: translateY(-50px)" data-top-bottom="transform: translateY(50px)"
                                alt=""> -->
                            <img src="images/demo-corporate-08.png"
                                class="position-absolute profit-engine-graph right-0px"
                                data-bottom-top="transform: translateY(30px)" data-top-bottom="transform: translateY(-30px)"
                                alt="">
                            <!-- Particles container overlay -->
                            <div class="particles-container position-absolute top-0 left-0 w-100 h-100 z-index-10"></div>
                        </div>
                    </div>
                </div>
                <div class="row justify-content-center align-items-center">
                    <div class="col-12 text-center align-items-center" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <div class="bg-base-color fw-600 text-white text-uppercase border-radius-30px ps-20px pe-20px fs-12 me-10px sm-m-5px d-inline-block align-middle">READY?</div>
                        <div class="fs-18 fw-500 text-dark-gray d-inline-block align-middle"><a href="/platform" class="text-dark-gray text-decoration-line-bottom fw-600">Dive Deeper into Our Technology →</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
        <!-- start section -->
		 <!--
     -->
        <!-- end section -->
		  <!-- start section: Live AI Decision Engine -->
        <section class="position-relative overflow-hidden py-0" style="background-image: url('images/demo-consulting-parallax.jpg'); background-size: cover; background-position: center; background-attachment: fixed;">
            <div class="position-absolute top-0 left-0 w-100 h-100 opacity-extra-medium bg-gradient-black-dark-orange"></div>
            <div class="position-relative z-index-9 pt-7 pb-7 md-pt-12 md-pb-12">
                <div class="container">
                    <div class="row justify-content-center align-items-center mb-6">
                        <div class="col-xl-10 col-lg-12 text-center position-relative last-paragraph-no-margin parallax-scrolling-style-2">
                            <span class="opacity-6 ls-2px text-uppercase alt-font text-white d-block mb-3 fw-500">ADZETA AI IN ACTION</span>
                            <h2 class="text-white fw-600 alt-font ls-minus-2px text-shadow-double-large mb-4">Live Performance Optimization</h2>
                            <p class="text-white opacity-7 fs-18 mb-5 w-80 mx-auto lg-w-90 md-w-100">Watch your campaigns optimize in real-time as our AI identifies high-value customers and maximizes your ROI.</p>
                        </div>
                    </div>

                    <!-- Live Performance Dashboard -->
                    <div class="row justify-content-center mb-6">
                        <div class="col-lg-12">
                            <div class="ai-dashboard bg-transparent-white-very-light border-radius-20px p-40px lg-p-35px md-p-30px position-relative overflow-hidden">

                                <!-- Dashboard Header -->
                                <div class="row align-items-center mb-4">
                                    <div class="col-lg-6">
                                        <div class="d-flex align-items-center">
                                            <div class="live-indicator me-15px">
                                                <div class="pulse-dot bg-green"></div>
                                                <span class="text-white fs-14 fw-600 ms-10px">LIVE</span>
                                            </div>
                                            <h4 class="text-white fw-600 mb-0">Campaign Performance</h4>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 text-lg-end">
                                        <div class="text-white opacity-7 fs-14">
                                            <span id="optimizations-count">47</span> optimizations active
                                        </div>
                                    </div>
                                </div>

                                <!-- Real-time Optimization Feed -->
                                <div class="optimization-stream mb-4">
                                    <div class="stream-header mb-3">
                                        <span class="text-white fs-16 fw-600">Recent Performance Improvements</span>
                                    </div>

                                    <!-- Optimization Items (will be populated by JavaScript) -->
                                    <div id="optimization-stream" class="optimization-items">
                                        <!-- Sample optimization item -->
                                        <div class="optimization-item border-radius-10px p-20px mb-15px d-flex align-items-center justify-content-between">
                                            <div class="optimization-info d-flex align-items-center">
                                                <div class="optimization-icon me-15px">
                                                    <i class="bi bi-graph-up-arrow text-base-color fs-20"></i>
                                                </div>
                                                <div>
                                                    <div class="text-white fs-15 fw-600">ROAS improved for Beauty Products campaign</div>
                                                    <div class="text-white opacity-6 fs-13">Female 25-34 • High-value segment identified</div>
                                                </div>
                                            </div>
                                            <div class="optimization-impact text-end">
                                                <div class="text-base-color fs-14 fw-600">+18% ROAS</div>
                                                <div class="text-white opacity-6 fs-12">3 min ago</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Live Performance Metrics -->
                                <div class="row">
                                    <div class="col-6 col-lg-3 col-md-6 mb-3">
                                        <div class="metric-box text-center">
                                            <div class="metric-value text-white fs-24 fw-700" id="current-roas">4.2x</div>
                                            <div class="metric-label text-white opacity-6 fs-12">Current ROAS</div>
                                            <div class="metric-change text-base-color fs-11" id="roas-change">↑ 0.3x vs yesterday</div>
                                        </div>
                                    </div>
                                    <div class="col-6 col-lg-3 col-md-6 mb-3">
                                        <div class="metric-box text-center">
                                            <div class="metric-value text-white fs-24 fw-700" id="cpa-reduction">-23%</div>
                                            <div class="metric-label text-white opacity-6 fs-12">CPA Reduction</div>
                                            <div class="metric-change text-base-color fs-11" id="cpa-change">↓ $8.50 this week</div>
                                        </div>
                                    </div>
                                    <div class="col-6 col-lg-3 col-md-6 mb-3">
                                        <div class="metric-box text-center">
                                            <div class="metric-value text-white fs-24 fw-700" id="high-ltv-traffic">67%</div>
                                            <div class="metric-label text-white opacity-6 fs-12">High-LTV Traffic</div>
                                            <div class="metric-change text-base-color fs-11" id="ltv-traffic-change">↑ 14% vs last month</div>
                                        </div>
                                    </div>
                                    <div class="col-6 col-lg-3 col-md-6 mb-3">
                                        <div class="metric-box text-center">
                                            <div class="metric-value text-white fs-24 fw-700" id="budget-efficiency">$2,847</div>
                                            <div class="metric-label text-white opacity-6 fs-12">Budget Saved Today</div>
                                            <div class="metric-change text-base-color fs-11" id="budget-change">↑ $247 vs yesterday</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- How Performance Optimization Works -->
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <div class="row" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 200, "staggervalue": 200, "easing": "easeOutQuad" }'>
                                <div class="col-lg-4 col-md-6 text-center mb-4">
                                    <div class="mb-20px">
                                        <i class="line-icon-Target icon-large text-white mb-15px"></i>
                                    </div>
                                    <h5 class="text-white fw-600 mb-15px">Identify High-Value Customers</h5>
                                    <p class="text-white opacity-7 fs-15 mb-0">AI analyzes customer behavior patterns to predict lifetime value and identify your most profitable segments.</p>
                                </div>

                                <div class="col-lg-4 col-md-6 text-center mb-4">
                                    <div class="mb-20px">
                                        <i class="line-icon-Gear-2 icon-large text-white mb-15px"></i>
                                    </div>
                                    <h5 class="text-white fw-600 mb-15px">Optimize Campaign Performance</h5>
                                    <p class="text-white opacity-7 fs-15 mb-0">Automatically adjusts targeting and bidding strategies to focus budget on customers with highest ROI potential.</p>
                                </div>

                                <div class="col-lg-4 col-md-6 text-center mb-4">
                                    <div class="mb-20px">
                                        <i class="line-icon-Bar-Chart icon-large text-white mb-15px"></i>
                                    </div>
                                    <h5 class="text-white fw-600 mb-15px">Deliver Measurable Results</h5>
                                    <p class="text-white opacity-7 fs-15 mb-0">Track real-time improvements in ROAS, CPA reduction, and budget efficiency across all your campaigns.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Section
                    <div class="row justify-content-center mt-5">
                        <div class="col-auto text-center">
                            <a href="#" class="btn btn-gradient-pink-orange btn-large btn-round-edge fw-600 ls-0px">
                                See Your Performance Dashboard
                                <i class="fa-solid fa-arrow-right ms-10px"></i>
                            </a>
                        </div>
                    </div> -->
                </div>
            </div>
        </section>

        <!-- Live Performance Dashboard JavaScript -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Realistic performance optimization simulation
            const campaignTypes = [
                { name: "Fashion & Apparel", segments: ["Women's clothing", "Men's accessories", "Luxury fashion", "Streetwear"] },
                { name: "Beauty Products", segments: ["Skincare", "Makeup", "Hair care", "Anti-aging"] },
                { name: "Home & Garden", segments: ["Home decor", "Kitchen appliances", "Furniture", "Smart home"] },
                { name: "Electronics", segments: ["Smartphones", "Laptops", "Gaming gear", "Audio equipment"] },
                { name: "Health & Fitness", segments: ["Supplements", "Fitness equipment", "Workout gear", "Nutrition"] }
            ];

            const optimizationTypes = [
                {
                    type: "roas_improvement",
                    weight: 40,
                    actions: ["ROAS improved", "Performance increased", "ROI optimized", "Revenue boosted"],
                    impacts: ["+12% ROAS", "+18% ROAS", "+24% ROAS", "+15% ROAS", "+21% ROAS"],
                    icon: "bi-graph-up-arrow",
                    color: "text-base-color"
                },
                {
                    type: "cpa_reduction",
                    weight: 30,
                    actions: ["CPA reduced", "Cost optimized", "Efficiency improved", "Spend optimized"],
                    impacts: ["-15% CPA", "-22% CPA", "-18% CPA", "-28% CPA", "-12% CPA"],
                    icon: "bi-arrow-down-circle",
                    color: "text-base-color"
                },
                {
                    type: "audience_discovery",
                    weight: 20,
                    actions: ["High-value segment identified", "New audience discovered", "Profitable segment found", "LTV segment optimized"],
                    impacts: ["New segment", "Audience expansion", "LTV +$89", "Segment refined"],
                    icon: "bi-people",
                    color: "text-base-color"
                },
                {
                    type: "budget_optimization",
                    weight: 10,
                    actions: ["Budget reallocated", "Spend optimized", "Budget efficiency improved", "Waste eliminated"],
                    impacts: ["$247 saved", "$189 reallocated", "$156 optimized", "$298 saved"],
                    icon: "bi-piggy-bank",
                    color: "text-base-color"
                }
            ];

            const audienceSegments = [
                "Female 25-34", "Male 18-29", "Female 35-45", "Male 30-45", "Mixed 25-55",
                "High-value customers", "Repeat buyers", "Cart abandoners", "Mobile users", "Desktop users"
            ];

            let optimizationsCount = 47;
            let usedOptimizations = new Set();

            // Current performance metrics
            let currentMetrics = {
                roas: 4.2,
                cpaReduction: -23,
                highLtvTraffic: 67,
                budgetSaved: 2847
            };

            // Weighted random selection for optimization types
            function selectWeightedOptimization() {
                const totalWeight = optimizationTypes.reduce((sum, opt) => sum + opt.weight, 0);
                let random = Math.random() * totalWeight;

                for (const optimization of optimizationTypes) {
                    random -= optimization.weight;
                    if (random <= 0) return optimization;
                }
                return optimizationTypes[0];
            }

            function generatePerformanceOptimization() {
                const optimization = selectWeightedOptimization();
                const campaign = campaignTypes[Math.floor(Math.random() * campaignTypes.length)];
                const segment = campaign.segments[Math.floor(Math.random() * campaign.segments.length)];
                const audience = audienceSegments[Math.floor(Math.random() * audienceSegments.length)];

                // Create unique combination key
                const combinationKey = `${optimization.type}_${campaign.name}_${segment}_${audience}`;

                // Skip if recently used
                if (usedOptimizations.has(combinationKey) && usedOptimizations.size < 50) {
                    return null;
                }

                usedOptimizations.add(combinationKey);
                if (usedOptimizations.size > 75) {
                    // Remove oldest combinations
                    const oldOptimizations = Array.from(usedOptimizations).slice(0, 25);
                    oldOptimizations.forEach(combo => usedOptimizations.delete(combo));
                }

                // Generate realistic optimization
                const action = optimization.actions[Math.floor(Math.random() * optimization.actions.length)];
                const impact = optimization.impacts[Math.floor(Math.random() * optimization.impacts.length)];

                return {
                    action: `${action} for ${campaign.name} campaign`,
                    segment: `${audience} • ${segment}`,
                    impact: impact,
                    icon: optimization.icon,
                    color: optimization.color,
                    timestamp: Date.now()
                };
            }

            function displayOptimization(optimization) {
                const streamContainer = document.getElementById('optimization-stream');

                const optimizationElement = document.createElement('div');
                optimizationElement.className = 'optimization-item bg-dark-slate-blue border-radius-10px p-20px mb-15px d-flex align-items-center justify-content-between';
                optimizationElement.style.opacity = '0';
                optimizationElement.style.transform = 'translateY(20px)';

                optimizationElement.innerHTML = `
                    <div class="optimization-info d-flex align-items-center">
                        <div class="optimization-icon me-15px">
                            <i class="${optimization.icon} ${optimization.color} fs-20"></i>
                        </div>
                        <div>
                            <div class="text-white fs-15 fw-600">${optimization.action}</div>
                            <div class="text-white opacity-6 fs-13">${optimization.segment}</div>
                        </div>
                    </div>
                    <div class="optimization-impact text-end">
                        <div class="${optimization.color} fs-14 fw-600">${optimization.impact}</div>
                        <div class="text-white opacity-6 fs-12">Just now</div>
                    </div>
                `;

                // Insert at the beginning
                streamContainer.insertBefore(optimizationElement, streamContainer.firstChild);

                // Animate in
                setTimeout(() => {
                    optimizationElement.style.transition = 'all 0.4s ease';
                    optimizationElement.style.opacity = '1';
                    optimizationElement.style.transform = 'translateY(0)';
                }, 150);

                // Remove old items (keep only 3)
                const items = streamContainer.children;
                if (items.length > 3) {
                    streamContainer.removeChild(items[items.length - 1]);
                }

                // Update timestamps
                updateTimestamps();

                // Update optimization count
                optimizationsCount++;
                document.getElementById('optimizations-count').textContent = optimizationsCount;
            }

            function generateAndDisplayOptimization() {
                let attempts = 0;
                let optimization = null;

                // Try to generate unique optimization
                while (!optimization && attempts < 5) {
                    optimization = generatePerformanceOptimization();
                    attempts++;
                }

                if (optimization) {
                    displayOptimization(optimization);
                }

                // Schedule next optimization (realistic timing: 45-120 seconds)
                const nextDelay = Math.random() * 75000 + 45000; // 45-120 seconds
                setTimeout(generateAndDisplayOptimization, nextDelay);
            }

            function updateTimestamps() {
                const items = document.querySelectorAll('.optimization-item');
                items.forEach((item, index) => {
                    const timeElement = item.querySelector('.optimization-impact .fs-12');
                    if (timeElement) {
                        if (index === 0) {
                            timeElement.textContent = 'Just now';
                        } else {
                            // Realistic timestamp progression for performance optimizations
                            const baseTime = index * 180; // 3 minutes base (optimizations are less frequent)
                            const variation = Math.floor(Math.random() * 60); // 0-60 seconds variation
                            const totalSeconds = baseTime + variation;

                            if (totalSeconds < 60) {
                                timeElement.textContent = `${totalSeconds} sec ago`;
                            } else {
                                const minutes = Math.floor(totalSeconds / 60);
                                timeElement.textContent = `${minutes}m ago`;
                            }
                        }
                    }
                });
            }

            function updatePerformanceMetrics() {
                // Realistic performance metric updates
                const roasElement = document.getElementById('current-roas');
                const cpaElement = document.getElementById('cpa-reduction');
                const ltvTrafficElement = document.getElementById('high-ltv-traffic');
                const budgetElement = document.getElementById('budget-efficiency');

                // Gradual improvements over time (realistic for AI optimization)
                currentMetrics.roas += (Math.random() - 0.4) * 0.02; // Slight upward bias
                currentMetrics.cpaReduction += (Math.random() - 0.6) * 0.5; // Gradual improvement
                currentMetrics.highLtvTraffic += (Math.random() - 0.3) * 0.3; // Slow increase
                currentMetrics.budgetSaved += Math.floor((Math.random() - 0.2) * 15); // Gradual savings

                // Keep within realistic bounds
                currentMetrics.roas = Math.max(3.5, Math.min(6.0, currentMetrics.roas));
                currentMetrics.cpaReduction = Math.max(-35, Math.min(-10, currentMetrics.cpaReduction));
                currentMetrics.highLtvTraffic = Math.max(55, Math.min(80, currentMetrics.highLtvTraffic));
                currentMetrics.budgetSaved = Math.max(2000, Math.min(4000, currentMetrics.budgetSaved));

                // Update display
                roasElement.textContent = currentMetrics.roas.toFixed(1) + 'x';
                cpaElement.textContent = Math.round(currentMetrics.cpaReduction) + '%';
                ltvTrafficElement.textContent = Math.round(currentMetrics.highLtvTraffic) + '%';
                budgetElement.textContent = '$' + Math.round(currentMetrics.budgetSaved).toLocaleString();

                // Update change indicators
                const roasChange = document.getElementById('roas-change');
                const cpaChange = document.getElementById('cpa-change');
                const ltvChange = document.getElementById('ltv-traffic-change');
                const budgetChange = document.getElementById('budget-change');

                // Show realistic improvements
                roasChange.textContent = `↑ ${(Math.random() * 0.5 + 0.1).toFixed(1)}x vs yesterday`;
                cpaChange.textContent = `↓ $${(Math.random() * 10 + 5).toFixed(0)} this week`;
                ltvChange.textContent = `↑ ${Math.floor(Math.random() * 8 + 10)}% vs last month`;
                budgetChange.textContent = `↑ $${Math.floor(Math.random() * 200 + 150)} vs yesterday`;
            }

            // Initialize the performance dashboard
            setTimeout(() => {
                generateAndDisplayOptimization(); // Start optimization feed
                setInterval(updatePerformanceMetrics, 8000); // Update metrics every 8 seconds
                setInterval(updateTimestamps, 30000); // Update timestamps every 30 seconds
            }, 1000);

            // Add initial optimizations with staggered timing
            setTimeout(() => {
                const initialOptimization = generatePerformanceOptimization();
                if (initialOptimization) {
                    displayOptimization(initialOptimization);
                }
            }, 3000);

            setTimeout(() => {
                const secondOptimization = generatePerformanceOptimization();
                if (secondOptimization) {
                    displayOptimization(secondOptimization);
                }
            }, 8000);
        });
        </script>

        <style>
		.optimization-item{background-color: #68275a7a;}
		
        .pulse-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
            background: var(--base-color, #de347f); /* Using your site's base color */
        }

        @keyframes pulse {
            0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(222, 52, 127, 0.7); }
            70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(222, 52, 127, 0); }
            100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(222, 52, 127, 0); }
        }

        .decision-item {
            transition: all 0.3s ease;
        }

        .decision-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .live-indicator {
            display: flex;
            align-items: center;
        }

        /* Using your site's existing color classes */
        .text-base-color { color: var(--base-color, #de347f) !important; }
        .text-dark-gray { color: var(--dark-gray, #232323) !important; }
        </style>
        <!-- end section -->
        <!-- start section -->
         <section class="bg-base-medium overflow-hidden position-relative">
            <div class="container">
                <div class="row align-items-center mb-5 sm-mb-30px text-center text-lg-start" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay":0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                    <div class="col-lg-6 md-mb-30px">
                        <h2 class="alt-font text-dark-gray fw-700 ls-minus-1px fs-32 md-fs-31 sm-fs-30 mb-0">Real Growth Stories from E-commerce Leaders</h2>
                    </div>
                    <div class="col-lg-6 text-lg-end d-flex justify-content-lg-end justify-content-center align-items-center">
                        <!-- start slider navigation -->
                        <div class="slider-one-slide-prev-1 icon-extra-small text-dark-gray swiper-button-prev slider-navigation-style-04 me-2"><i class="fa-solid fa-arrow-left"></i></div>
                        <div class="slider-one-slide-next-1 icon-extra-small text-dark-gray swiper-button-next slider-navigation-style-04"><i class="fa-solid fa-arrow-right"></i></div>
                        <!-- end slider navigation -->
                    </div>
                </div>
                <div class="row align-items-center" data-anime='{ "opacity": [0,1], "duration": 600, "delay":0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                    <div class="col-12">
                        <div class="outside-box-right-20 sm-outside-box-right-0">
                            <div class="swiper magic-cursor drag-cursor slider-one-slide" data-slider-options='{ "slidesPerView": 1, "spaceBetween": 30, "loop": true, "navigation": { "nextEl": ".slider-one-slide-next-1", "prevEl": ".slider-one-slide-prev-1" }, "autoplay": { "delay": 4000, "disableOnInteraction": false }, "keyboard": { "enabled": true, "onlyInViewport": true }, "breakpoints": { "1200": { "slidesPerView": 3 }, "992": { "slidesPerView": 3 }, "768": { "slidesPerView": 2 }, "320": { "slidesPerView": 1 } }, "effect": "slide" }'>
                                <div class="swiper-wrapper">
                                    <!-- start slider item -->
                                    <div class="swiper-slide">
                                        <!-- start services box style -->
                                        <div class="services-box-style-03 last-paragraph-no-margin border-radius-24px overflow-hidden case-study-card">
                                            <!-- Data visualization decorative elements -->
                                            <div class="data-viz-element circle-1"></div>
                                            <div class="data-viz-element circle-2"></div>
                                            <div class="data-point point-1"></div>
                                            <div class="data-point point-2"></div>
                                            <div class="data-point point-3"></div>
                                            <div class="data-point point-4"></div>
                                            <div class="data-point point-5"></div>
                                            <a href="oakbrew-coffee-gains-vip-customers-with-ltv-predictions.php" class="logo-link">
                                            <!-- <img src="images/logos/1.png" alt="Coffee Business Case Study"> -->
											<h3 class="text-dark-gray fs-26">Coffee Brand Gains VIP Customers with LTV Predictions</h3>
                                            <i class="bi bi-arrow-up-right arrow-icon" aria-hidden="true"></i>
                                            </a>
                                            <div class="bg-white">
                                                <div class="ps-30px pe-30px pt-30px pb-20px">
                                                    <div class="key-metric">
                                                        <div class="key-metric-badge">VIP Customers</div>
                                                        <div class="key-metric-value">+3.5x</div>
                                                    </div>
                                                    <p>"With AdZeta's AI platform, we've been able to scale our ad spend by 3.5x while significantly increasing the acquisition of our most valuable VIP subscribers. The predictive LTV insights gave us the confidence to expand into new channels we previously thought were too risky for our premium coffee brand."</p>
                                                </div>
                                            </div>
                                            <div class="client-info-container">
                                                <img src="images/case-studies/Sarah-Johnson.jpg" alt="Client Photo" class="client-photo">
                                                <div class="client-info">
                                                    <span class="client-name">Sarah Johnson</span>
                                                    <span class="client-title">Co-Founder @ Oakbrew Coffee</span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- end services box style -->
                                    </div>
                                    <!-- end slider item -->
                                    <!-- start slider item -->
                                    <div class="swiper-slide">
                                        <!-- start services box style -->
                                        <div class="services-box-style-03 last-paragraph-no-margin border-radius-24px overflow-hidden case-study-card">
                                            <!-- Data visualization decorative elements -->
                                            <div class="data-viz-element circle-1"></div>
                                            <div class="data-viz-element circle-2"></div>
                                            <div class="data-point point-1"></div>
                                            <div class="data-point point-2"></div>
                                            <div class="data-point point-3"></div>
                                            <div class="data-point point-4"></div>
                                            <div class="data-point point-5"></div>
                                            <a href="luminova-boosts-customer-lifetime-value-with-ai-personalization.php" class="logo-link">
                                            <!--<img src="images/logos/1.svg" alt="Home Decor Business Case Study"> -->
											<h3 class="text-dark-gray fs-26">Home Decor Brand Boosts Customer LTV with AI Personalization</h3>
                                            <i class="bi bi-arrow-up-right arrow-icon" aria-hidden="true"></i>
                                            </a>
                                            <div class="bg-white">
                                                <div class="ps-30px pe-30px pt-30px pb-20px">
                                                    <div class="key-metric">
                                                        <div class="key-metric-badge">CLV</div>
                                                        <div class="key-metric-value">+72%</div>
                                                    </div>
                                                    <p>"AdZeta's AI platform has been a game-changer. The ability to predict and intervene in cart abandonment, coupled with truly personalized recommendations, has not only recovered significant revenue but has also built a more loyal customer base."</p>
                                                </div>
                                            </div>
                                            <div class="client-info-container">
                                                <img src="images/case-studies/Sarah-Chen.jpg" alt="Client Photo" class="client-photo">
                                                <div class="client-info">
                                                    <span class="client-name">Sarah Chen</span>
                                                    <span class="client-title">Founder & CEO, LumiNova Home</span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- end services box style -->
                                    </div>
                                    <!-- end slider item -->
                                    <!-- start slider item -->
                                    <div class="swiper-slide">
                                        <!-- start services box style -->
                                        <div class="services-box-style-03 last-paragraph-no-margin border-radius-24px overflow-hidden case-study-card">
                                            <!-- Data visualization decorative elements -->
                                            <div class="data-viz-element circle-1"></div>
                                            <div class="data-viz-element circle-2"></div>
                                            <div class="data-point point-1"></div>
                                            <div class="data-point point-2"></div>
                                            <div class="data-point point-3"></div>
                                            <div class="data-point point-4"></div>
                                            <div class="data-point point-5"></div>
                                            <a href="horizon-properties-adzeta-predictive-ltv-targeting.php" class="logo-link">
                                           <!-- <img src="images/logos/2.svg" alt="Real Estate Business Case Study">-->
										   <h3 class="text-dark-gray fs-26">Real Estate Company Scales High-Value Leads with Predictive Targeting</h3>
                                            <i class="bi bi-arrow-up-right arrow-icon" aria-hidden="true"></i>
                                            </a>
                                            <div class="bg-white">
                                                <div class="ps-30px pe-30px pt-30px pb-20px">
                                                    <div class="key-metric">
                                                        <div class="key-metric-badge">ROAS</div>
                                                        <div class="key-metric-value">2.9x</div>
                                                    </div>
                                                    <p>"With AdZeta's AI platform, we've been able to scale our ad spend effectively while significantly increasing the acquisition of high-net-worth investors who make multiple property investments. The predictive LTV insights gave us the confidence to expand into luxury development markets."</p>
                                                </div>
                                            </div>
                                            <div class="client-info-container">
                                                <img src="images/case-studies/Alexander-Morgan.jpg" alt="Client Photo" class="client-photo">
                                                <div class="client-info">
                                                    <span class="client-name">Alexander Morgan</span>
                                                    <span class="client-title">Chief Investment Officer, Horizon Properties</span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- end services box style -->
                                    </div>
                                    <!-- end slider item -->
                                    <!-- start slider item -->
                                    <div class="swiper-slide">
                                        <!-- start services box style -->
                                        <div class="services-box-style-03 last-paragraph-no-margin border-radius-24px overflow-hidden case-study-card">
                                            <!-- Data visualization decorative elements -->
                                            <div class="data-viz-element circle-1"></div>
                                            <div class="data-viz-element circle-2"></div>
                                            <div class="data-point point-1"></div>
                                            <div class="data-point point-2"></div>
                                            <div class="data-point point-3"></div>
                                            <div class="data-point point-4"></div>
                                            <div class="data-point point-5"></div>
                                            <a href="luminous-skin-clinic-adzeta-predictive-ltv-targeting.php" class="logo-link">
                                           <!--  <img src="images/logos/3.svg" alt="Beauty Clinic Case Study"> -->
											<h3 class="text-dark-gray fs-26">Beauty Clinic Multiplies High-Value Clientele 3.2X Through Predictive LTV</h3>
                                            <i class="bi bi-arrow-up-right arrow-icon" aria-hidden="true"></i>
                                            </a>
                                            <div class="bg-white">
                                                <div class="ps-30px pe-30px pt-30px pb-20px">
                                                    <div class="key-metric">
                                                        <div class="key-metric-badge">ROI</div>
                                                        <div class="key-metric-value">3.2x</div>
                                                    </div>
                                                    <p>"With AdZeta's AI platform, we've been able to scale our ad spend by 3.2X while significantly increasing the acquisition of our most valuable long-term clients. The predictive LTV insights gave us the confidence to expand our premium service offerings."</p>
                                                </div>
                                            </div>
                                            <div class="client-info-container">
                                                <img src="images/case-studies/Dr-Sophia-Williams.jpg" alt="Client Photo" class="client-photo">
                                                <div class="client-info">
                                                    <span class="client-name">Dr. Sophia Williams</span>
                                                    <span class="client-title">Founder & Lead Esthetician, Luminous Skin Clinic</span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- end services box style -->
                                    </div>
                                    <!-- end slider item -->
                                    <!-- start slider item -->
                                    <div class="swiper-slide">
                                        <!-- start services box style -->
                                        <div class="services-box-style-03 last-paragraph-no-margin border-radius-24px overflow-hidden case-study-card">
                                            <!-- Data visualization decorative elements -->
                                            <div class="data-viz-element circle-1"></div>
                                            <div class="data-viz-element circle-2"></div>
                                            <div class="data-point point-1"></div>
                                            <div class="data-point point-2"></div>
                                            <div class="data-point point-3"></div>
                                            <div class="data-point point-4"></div>
                                            <div class="data-point point-5"></div>
                                            <a href="airpro-supply-contractor-retention-predictive-ltv.php" class="logo-link">
                                            <!--<img src="images/logos/4.svg" alt="B2B Supply Company Case Study">-->
											<h3 class="text-dark-gray fs-26">B2B Supply Company Improves Contractor Retention with Predictive LTV</h3>
                                            <i class="bi bi-arrow-up-right arrow-icon" aria-hidden="true"></i>
                                            </a>
                                            <div class="bg-white">
                                                <div class="ps-30px pe-30px pt-30px pb-20px">
                                                    <div class="key-metric">
                                                        <div class="key-metric-badge">ROI</div>
                                                        <div class="key-metric-value">3.8x</div>
                                                    </div>
                                                    <p>"With AdZeta's AI platform, we've been able to scale our ad spend by 3.8X while significantly increasing the acquisition of our most valuable contractor customers. The predictive LTV insights gave us the confidence to expand our product catalog and enter new regional markets."</p>
                                                </div>
                                            </div>
                                            <div class="client-info-container">
                                                <img src="images/case-studies/Jennifer-Rodriguez.jpg" alt="Client Photo" class="client-photo">
                                                <div class="client-info">
                                                    <span class="client-name">Jennifer Rodriguez</span>
                                                    <span class="client-title">VP of Marketing, AirPro Supply</span>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- end services box style -->
                                    </div>
                                    <!-- end slider item -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-4 xs-mb-8">
                    <!--  <div class="col-12 text-center">
                        <div class="alt-font fs-18 text-dark-gray">Ready to transform your marketing ROI? <a href="/case-studies" class="text-dark-gray fw-600 text-dark-gray-hover text-decoration-line-bottom">Explore more case studies</a></div>
                        </div> -->
                    <div class="col-12 text-center">
                        <div class="alt-font fs-18 text-dark-gray">
                            Trusted by <span class="fw-600">1000+ US brands</span> that value long-term growth and measurable ROI<br>
                            <a href="case-studies.php" class="text-dark-gray fw-600 text-base-color-hover text-decoration-line-bottom">Explore more case studies →</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
        <!-- start section: Why Choose Adzeta? -->
        <section class="high-tech-features-section bg-extra-dark-slate-blue big-section overflow-hidden">
            <div class="tech-bg-grid"></div>
            <div class="tech-grid-lines"></div>
            <div class="container high-tech-features-content">
                <div class="row align-items-end justify-content-center mb-5 md-mb-40px text-center text-md-start">
                    <div class="col-xl-5 col-lg-6 col-md-10 md-mb-20px text-center text-lg-start" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <span class="ps-20px pe-20px mb-20px text-uppercase text-white fs-12 lh-42px fw-600 border-radius-100px bg-dark-slate-blue d-inline-block">The Adzeta Advantage</span>
                        <h2 class="text-white fw-600 mb-0 ls-minus-1px fs-32 md-fs-31 sm-fs-30">Why Partner with Adzeta?</h2>
                    </div>
                    <div class="col-xl-6 col-lg-6 col-md-10 offset-xl-1 text-center text-lg-start last-paragraph-no-margin">
                        <p class="w-90 xl-w-100 text-white opacity-6">We go beyond basic ad optimization. Our Predictive AI and e-commerce focus deliver unique advantages for <span class="fw-500 text-white">sustainable profit growth.</span></p>
                    </div>
                </div>
                <div class="row row-cols-1 row-cols-lg-2 mb-60px justify-content-center" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-02 transition-inner-all mb-30px">
                        <div class="feature-box feature-box-left-icon-middle text-start hover-box dark-hover bg-dark-slate-blue border-radius-20px p-9 overflow-hidden last-paragraph-no-margin">
                            <div class="feature-box-icon">
                                <i class="line-icon-Target icon-double-large text-white"></i>
                            </div>
                            <div class="feature-box-content">
                                <span class="d-inline-block fs-18 text-white fw-500 mb-5px">True Predictive Insight</span>
                                <p class="text-light-opacity">Forecast customer lifetime value with precision before bidding, identifying valuable segments others miss.</p>
                            </div>
                            <div class="feature-box-overlay bg-cornflower-blue"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-02 transition-inner-all mb-30px">
                        <div class="feature-box feature-box-left-icon-middle text-start hover-box dark-hover bg-dark-slate-blue border-radius-20px p-9 overflow-hidden last-paragraph-no-margin">
                            <div class="feature-box-icon">
                                <i class="line-icon-Money-2 icon-double-large text-white"></i>
                            </div>
                            <div class="feature-box-content">
                                <span class="d-inline-block fs-18 text-white fw-500 mb-5px">Focus on Real Profit</span>
                                <p class="text-light-opacity">Optimize campaigns for sustainable growth and improved LTV:CAC ratios beyond surface-level metrics.</p>
                            </div>
                            <div class="feature-box-overlay bg-cornflower-blue"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-02 transition-inner-all md-mb-30px">
                        <div class="feature-box feature-box-left-icon-middle text-start hover-box dark-hover bg-dark-slate-blue border-radius-20px p-9 overflow-hidden last-paragraph-no-margin">
                            <div class="feature-box-icon">
                                <i class="line-icon-Shop-4 icon-double-large text-white"></i>
                            </div>
                            <div class="feature-box-content">
                                <span class="d-inline-block fs-18 text-white fw-500 mb-5px">E-commerce Specialization</span>
                                <p class="text-light-opacity">Tailored AI models designed specifically for digital retail environments and consumer behavior patterns.</p>
                            </div>
                            <div class="feature-box-overlay bg-cornflower-blue"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                    <!-- start features box item -->
                    <div class="col icon-with-text-style-02 transition-inner-all">
                        <div class="feature-box feature-box-left-icon-middle text-start hover-box dark-hover bg-dark-slate-blue border-radius-20px p-9 overflow-hidden last-paragraph-no-margin">
                            <div class="feature-box-icon">
                                <i class="line-icon-Security-Block icon-double-large text-white"></i>
                            </div>
                            <div class="feature-box-content">
                                <span class="d-inline-block fs-18 text-white fw-500 mb-5px">Seamless & Secure Tech</span>
                                <p class="text-light-opacity">Integration-ready ValueBid™ framework with enterprise-grade security and privacy-first architecture.</p>
                            </div>
                            <div class="feature-box-overlay bg-cornflower-blue"></div>
                        </div>
                    </div>
                    <!-- end features box item -->
                </div>
                <!--    <div class="row justify-content-center mt-5" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                    <div class="col-auto text-center last-paragraph-no-margin">
                        <div class="feature-box feature-box-left-icon-middle overflow-hidden">
                            <div class="feature-box-icon me-10px">
                                <i class="bi bi-chat-text icon-extra-medium text-white"></i>
                            </div>
                            <div class="feature-box-content last-paragraph-no-margin text-white text-uppercase fs-15 fw-500 ls-05px">
                                Let's make something great work together. <a href="#" class="text-white border-1">Got a project in mind?</a>
                            </div>
                        </div>
                    </div>
                    </div> -->
            </div>
        </section>
        <!-- end section -->
        <!-- start section -->
        <section class="pt-60px pb-60px bg-base-medium-single">
            <div class="container overlap-section z-index-9 mb-3">
                <div class="row align-items-center justify-content-center border bg-white border-color-extra-medium-gray border-radius-24px p-40px lg-p-40px m-0 box-shadow-quadruple-large" data-bottom-top="transform:scale(1.1, 1.1) translateY(30px);" data-top-bottom="transform:scale(1, 1) translateY(-30px);">
                    <!-- Theme-consistent minimal CTA content -->
                    <div class="col-12 ">
                        <div class="text-center mb-15px">
                            <h2 class="alt-font fw-700 text-dark-gray ls-minus-1px fs-32 md-fs-31 sm-fs-30 mb-0">Unlock your customer <span class="text-gradient-pink-blue">value</span></h2>
                            <p class="mb-30px">Join powerhouse brands driving growth with us.</p>
                        </div>
                        <!-- Responsive form with improved styling -->
                        <form action="email-templates/homepage-cta.php" method="post" class="mx-auto homepage-cta-form" style="max-width: 650px; width: 90%;" id="homepage-cta-form">
                            <div class="row justify-content-center g-2">
                                <div class="col-lg-4 col-md-6 col-sm-12 mb-md-0 mb-3">
                                    <input type="text" name="name" placeholder="Your name" class="form-control w-100" style="height: 45px; border: 1px solid #ccc; border-radius: 50px; padding-left: 20px;"/>
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-12 mb-md-0 mb-3">
                                    <input type="email" name="email" placeholder="Your email address*" class="form-control required w-100" style="height: 45px; border: 1px solid #ccc; border-radius: 50px; padding-left: 20px;" required/>
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <input type="hidden" name="cta_location" value="homepage_final_cta">
                                    <button type="submit" class="btn btn-gradient-pink-orange btn-medium btn-round-edge w-100" style="height: 45px; border-radius: 50px; padding: 0; display: flex; align-items: center; justify-content: center; font-size: 15px;">
                                    Talk to an Expert
                                    </button>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-results d-none text-center mt-15px"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
        <!-- start section: Apple-inspired FAQ -->
        <section class="overflow-hidden faq-section pt-0 pb-7 bg-base-medium-single">
            <!-- Added background and adjusted padding -->
            <div class="container">
                <div class="row justify-content-center mb-2">
                    <!-- Increased spacing for cleaner look -->
                    <div class="col-lg-8 text-center" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 0 }'>
                        <!-- Refined headline with Apple-inspired typography -->
                        <h2 class="alt-font fw-700 text-dark-gray ls-minus-1px fs-32 md-fs-31 sm-fs-30 mb-0">Have a question?</h2>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-lg-9">
                        <!-- Apple-inspired accordion with minimal design -->
                        <div class="apple-style-faq" id="faq-accordion" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 100, "staggervalue": 120, "easing": "easeOutQuad" }'>
                            <!-- FAQ Item 1 -->
                            <div class="faq-item">
                                <div id="faq-q1" class="collapse show" data-bs-parent="#faq-accordion">
                                    <div class="faq-body">
                                        <p>Value-Based Bidding (VBB) is an advanced advertising strategy that optimizes bids based on a user's predicted <strong>Lifetime Value (LTV)</strong> or potential profitability, rather than just their likelihood to make an initial conversion (like traditional CPA/ROAS bidding). Adzeta uses Predictive AI to enhance VBB, focusing your ad spend on acquiring customers who drive the most <strong>long-term profit</strong>.</p>
                                    </div>
                                </div>
                                <div class="faq-header">
                                    <a href="#" class="faq-toggle" data-bs-toggle="collapse" data-bs-target="#faq-q1" aria-expanded="true">
                                        <div class="faq-title">
                                            <span class="faq-number">01</span>
                                            <h3 class="fw-600">What is Value-Based Bidding (VBB)?</h3>
                                            <div class="faq-icon"><span></span><span></span></div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <!-- FAQ Item 2 -->
                            <div class="faq-item">
                                <div id="faq-q2" class="collapse" data-bs-parent="#faq-accordion">
                                    <div class="faq-body">
                                        <p>Our proprietary AI analyzes hundreds of signals from your first-party data in real-time. This includes historical <strong>purchase data</strong>, website/app <strong>user behavior</strong>, product interactions, campaign engagement, and key demographics. By identifying patterns correlated with high long-term value, our models accurately forecast the potential LTV of new visitors, often <strong>before their first purchase</strong>.</p>
                                    </div>
                                </div>
                                <div class="faq-header">
                                    <a href="#" class="faq-toggle" data-bs-toggle="collapse" data-bs-target="#faq-q2" aria-expanded="false">
                                        <div class="faq-title">
                                            <span class="faq-number">02</span>
                                            <h3 class="fw-600">How does Adzeta's AI predict LTV?</h3>
                                            <div class="faq-icon"><span></span><span></span></div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <!-- FAQ Item 3 -->
                            <div class="faq-item">
                                <div id="faq-q3" class="collapse" data-bs-parent="#faq-accordion">
                                    <div class="faq-body">
                                        <p>Adzeta specializes in applying Predictive AI and Value-Based Bidding across the key platforms crucial for e-commerce growth, including <strong>Google Ads</strong> (Search, Shopping, PMax, YouTube), <strong>Meta Ads</strong> (Facebook, Instagram), <strong>TikTok Ads</strong>, and leading <strong>Programmatic Advertising</strong> DSPs.</p>
                                    </div>
                                </div>
                                <div class="faq-header">
                                    <a href="#" class="faq-toggle" data-bs-toggle="collapse" data-bs-target="#faq-q3" aria-expanded="false">
                                        <div class="faq-title">
                                            <span class="faq-number">03</span>
                                            <h3 class="fw-600">What advertising platforms do you work with?</h3>
                                            <div class="faq-icon"><span></span><span></span></div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <!-- FAQ Item 4 -->
                            <div class="faq-item">
                                <div id="faq-q4" class="collapse" data-bs-parent="#faq-accordion">
                                    <div class="faq-body">
                                        <p>While results vary, clients typically see significant improvements after implementing Adzeta. Common outcomes include measurable <strong>reductions in Customer Acquisition Cost (CAC)</strong> (often 30%+), substantially higher <strong>LTV-focused ROAS</strong> (frequently 3-4x+ lifts compared to previous methods), and overall <strong>more profitable and sustainable scaling</strong> of their ad campaigns. Your free audit will provide a personalized potential analysis.</p>
                                    </div>
                                </div>
                                <div class="faq-header">
                                    <a href="#" class="faq-toggle" data-bs-toggle="collapse" data-bs-target="#faq-q4" aria-expanded="false">
                                        <div class="faq-title">
                                            <span class="faq-number">04</span>
                                            <h3 class="fw-600">What results can I realistically expect?</h3>
                                            <div class="faq-icon"><span></span><span></span></div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
		 <script src="<?= isset($base_url) ? $base_url : '' ?>js/ai-particles-effect.js" defer></script>
        <script src="<?= isset($base_url) ? $base_url : '' ?>js/chip-particles.js" defer></script>
        <script src="<?= isset($base_url) ? $base_url : '' ?>js/animated-graph.js" defer></script>
        <script src="<?= isset($base_url) ? $base_url : '' ?>js/enterprise-metrics.js" defer></script>
        <script src="<?= isset($base_url) ? $base_url : '' ?>js/modern-toggle-slider.js" defer></script>
       
       
       
<?php include 'footer.php'; ?>
