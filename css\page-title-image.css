/* Page Title Image CSS */

/* Page title section styling */
.page-title-section {
    padding: 80px 0;
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
}

/* Image styling */
.page-title-section img {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;
}

.page-title-section img:hover {
    transform: translateY(-5px);
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .page-title-section {
        padding: 60px 0;
    }
    
    .page-title-section img {
        margin-top: 30px;
    }
}

@media (max-width: 767px) {
    .page-title-section {
        padding: 40px 0;
    }
    
    .page-title-section .col-md-6 {
        text-align: center !important;
    }
    
    .page-title-section img {
        margin-top: 30px;
        max-width: 80%;
    }
}
