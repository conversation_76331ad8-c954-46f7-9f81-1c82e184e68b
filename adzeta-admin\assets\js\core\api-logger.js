/**
 * Centralized API Request Logger
 * Logs all API requests, responses, and errors automatically
 */

class APILogger {
    constructor() {
        this.logs = [];
        this.maxLogs = 100; // Keep last 100 requests
        this.enableConsoleLogging = true;
        this.enableServerLogging = true; // Auto-send all errors to server
        this.setupGlobalErrorCapture(); // Capture ALL browser errors
    }

    /**
     * Log an API request
     */
    logRequest(method, url, data = null, headers = {}) {
        const logEntry = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            type: 'REQUEST',
            method: method.toUpperCase(),
            url: url,
            data: data,
            headers: headers,
            userAgent: navigator.userAgent,
            page: window.location.pathname
        };

        this.addLog(logEntry);
        
        if (this.enableConsoleLogging) {
            console.group(`🚀 API Request: ${method.toUpperCase()} ${url}`);
            console.log('Data:', data);
            console.log('Headers:', headers);
            console.log('Timestamp:', logEntry.timestamp);
            console.groupEnd();
        }

        return logEntry.id;
    }

    /**
     * Log an API response
     */
    logResponse(requestId, status, data, responseTime = null) {
        const logEntry = {
            id: this.generateId(),
            requestId: requestId,
            timestamp: new Date().toISOString(),
            type: 'RESPONSE',
            status: status,
            data: data,
            responseTime: responseTime,
            success: status >= 200 && status < 300
        };

        this.addLog(logEntry);

        if (this.enableConsoleLogging) {
            const icon = logEntry.success ? '✅' : '❌';
            console.group(`${icon} API Response: ${status} (${responseTime}ms)`);
            console.log('Data:', data);
            console.log('Request ID:', requestId);
            console.groupEnd();
        }

        return logEntry.id;
    }

    /**
     * Log an API error
     */
    logError(requestId, error, context = {}) {
        const logEntry = {
            id: this.generateId(),
            requestId: requestId,
            timestamp: new Date().toISOString(),
            type: 'ERROR',
            error: {
                message: error.message,
                stack: error.stack,
                name: error.name
            },
            context: context,
            page: window.location.pathname,
            userAgent: navigator.userAgent
        };

        this.addLog(logEntry);

        if (this.enableConsoleLogging) {
            console.group('🔥 API Error');
            console.error('Error:', error);
            console.log('Request ID:', requestId);
            console.log('Context:', context);
            console.log('Page:', window.location.pathname);
            console.groupEnd();
        }

        // Auto-save all errors to database for admin panel
        this.saveErrorToDatabase(logEntry, context);

        return logEntry.id;
    }

    /**
     * Get all logs
     */
    getLogs(type = null) {
        if (type) {
            return this.logs.filter(log => log.type === type);
        }
        return this.logs;
    }

    /**
     * Get logs for a specific request
     */
    getRequestLogs(requestId) {
        return this.logs.filter(log => log.requestId === requestId || log.id === requestId);
    }

    /**
     * Clear all logs
     */
    clearLogs() {
        this.logs = [];
        console.log('📝 API logs cleared');
    }

    /**
     * Export logs as JSON
     */
    exportLogs() {
        const exportData = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            page: window.location.href,
            logs: this.logs
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `api-logs-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    /**
     * Display logs in console table
     */
    showLogs() {
        console.table(this.logs.map(log => ({
            Time: new Date(log.timestamp).toLocaleTimeString(),
            Type: log.type,
            Method: log.method || '-',
            URL: log.url || '-',
            Status: log.status || '-',
            Success: log.success !== undefined ? log.success : '-',
            Error: log.error ? log.error.message : '-'
        })));
    }

    /**
     * Setup global error capture for ALL browser errors
     */
    setupGlobalErrorCapture() {
        // Capture JavaScript errors
        window.addEventListener('error', (event) => {
            this.logJavaScriptError(event.error, {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                type: 'javascript_error'
            });
        });

        // Capture unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logJavaScriptError(event.reason, {
                type: 'unhandled_promise_rejection',
                promise: event.promise
            });
        });

        // Capture console errors (override console.error)
        const originalConsoleError = console.error;
        console.error = (...args) => {
            this.logConsoleError(args);
            originalConsoleError.apply(console, args);
        };

        // Capture console warnings (override console.warn)
        const originalConsoleWarn = console.warn;
        console.warn = (...args) => {
            this.logConsoleWarning(args);
            originalConsoleWarn.apply(console, args);
        };

        console.log('🔍 Global error capture initialized - ALL errors will be automatically logged');
    },

    /**
     * Log JavaScript errors
     */
    logJavaScriptError(error, context = {}) {
        const logEntry = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            type: 'JAVASCRIPT_ERROR',
            error: {
                message: error?.message || 'Unknown error',
                stack: error?.stack || 'No stack trace',
                name: error?.name || 'Error'
            },
            context: context,
            page: window.location.href,
            userAgent: navigator.userAgent
        };

        this.addLog(logEntry);

        if (this.enableConsoleLogging) {
            console.group('🔥 JavaScript Error Captured');
            console.error('Error:', error);
            console.log('Context:', context);
            console.log('Page:', window.location.href);
            console.groupEnd();
        }

        // Auto-save to database
        this.saveErrorToDatabase(logEntry, context);

        return logEntry.id;
    },

    /**
     * Log console errors
     */
    logConsoleError(args) {
        const logEntry = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            type: 'CONSOLE_ERROR',
            error: {
                message: args.join(' '),
                stack: new Error().stack,
                name: 'ConsoleError'
            },
            context: {
                type: 'console_error',
                arguments: args
            },
            page: window.location.href,
            userAgent: navigator.userAgent
        };

        this.addLog(logEntry);

        // Auto-save to database
        this.saveErrorToDatabase(logEntry, logEntry.context);
    },

    /**
     * Log console warnings
     */
    logConsoleWarning(args) {
        const logEntry = {
            id: this.generateId(),
            timestamp: new Date().toISOString(),
            type: 'CONSOLE_WARNING',
            error: {
                message: args.join(' '),
                stack: new Error().stack,
                name: 'ConsoleWarning'
            },
            context: {
                type: 'console_warning',
                arguments: args,
                severity: 'warning'
            },
            page: window.location.href,
            userAgent: navigator.userAgent
        };

        this.addLog(logEntry);

        // Auto-save warnings to database too
        this.saveErrorToDatabase(logEntry, logEntry.context);
    },

    /**
     * Private methods
     */
    addLog(logEntry) {
        this.logs.push(logEntry);

        // Keep only the last maxLogs entries
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }
    }

    generateId() {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Save error to database for admin panel
     */
    async saveErrorToDatabase(logEntry, context) {
        try {
            // Find the original request log
            const requestLog = this.logs.find(log => log.id === logEntry.requestId || log.requestId === logEntry.requestId);

            const errorData = {
                request_id: logEntry.requestId,
                method: requestLog?.method || context.method || 'UNKNOWN',
                endpoint: context.endpoint || requestLog?.url || 'unknown',
                full_url: requestLog?.url || window.location.href,
                request_data: requestLog?.data || null,
                request_headers: requestLog?.headers || null,
                status_code: context.status || requestLog?.status || 0,
                response_data: context.responseData || null,
                response_time: context.responseTime || null,
                error_type: logEntry.error.name || 'UnknownError',
                error_message: logEntry.error.message || 'No error message',
                error_stack: logEntry.error.stack || null,
                error_context: context,
                user_agent: navigator.userAgent,
                page_url: window.location.href,
                referrer: document.referrer || null,
                browser_info: this.getBrowserInfo(),
                device_info: this.getDeviceInfo(),
                admin_version: '2.0.0'
            };

            // Don't use the main API request method to avoid infinite loops
            await fetch('/adzeta-admin/api/error-logs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${window.AdZetaApp?.state?.token || ''}`
                },
                body: JSON.stringify(errorData)
            });

        } catch (e) {
            console.warn('Failed to save error to database:', e);
        }
    },

    /**
     * Get browser information
     */
    getBrowserInfo() {
        const ua = navigator.userAgent;
        return {
            userAgent: ua,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };
    },

    /**
     * Get device information
     */
    getDeviceInfo() {
        const ua = navigator.userAgent;
        if (/Mobile|Android|iPhone|iPad/.test(ua)) {
            return 'Mobile';
        } else if (/Tablet|iPad/.test(ua)) {
            return 'Tablet';
        } else {
            return 'Desktop';
        }
    },

    async sendErrorToServer(logEntry) {
        try {
            await fetch('/adzeta-admin/api/logs/error', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(logEntry)
            });
        } catch (e) {
            console.warn('Failed to send error log to server:', e);
        }
    }
}

// Create global instance
window.AdZetaAPILogger = new APILogger();

// Add global methods for easy access
window.showAPILogs = () => window.AdZetaAPILogger.showLogs();
window.clearAPILogs = () => window.AdZetaAPILogger.clearLogs();
window.exportAPILogs = () => window.AdZetaAPILogger.exportLogs();

console.log('📝 API Logger initialized. Use showAPILogs(), clearAPILogs(), or exportAPILogs() in console.');
