<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create PNG Placeholders</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        .canvas-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
        }
        button {
            background-color: #e958a1;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #d6478f;
        }
        .download-all {
            background-color: #6a4fd9;
            margin-top: 20px;
            font-weight: bold;
        }
        .instructions {
            background-color: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #e958a1;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PNG Placeholders for SVG Hybrid</h1>
        
        <div class="instructions">
            <p>This tool creates placeholder PNG images for the hybrid SVG/PNG approach. Click each button to generate and download the corresponding PNG image.</p>
            <p>After downloading, you can replace these placeholders with higher-quality PNG images if needed.</p>
        </div>
        
        <div class="canvas-container">
            <canvas id="canvas" width="200" height="200"></canvas>
        </div>
        
        <div>
            <button onclick="createServerStack1()">Create Server Stack 1</button>
            <button onclick="createServerStack2()">Create Server Stack 2</button>
            <button onclick="createAICore()">Create ADZETA AI Core</button>
            <button onclick="createGooglePlatform()">Create Google Platform</button>
            <button onclick="createMetaPlatform()">Create Meta Platform</button>
            <button onclick="createTikTokPlatform()">Create TikTok Platform</button>
            <button onclick="createOutcomesChart()">Create Outcomes Chart</button>
            <button onclick="createDataStack1()">Create Data Stack 1</button>
            <button onclick="createDataStack2()">Create Data Stack 2</button>
            <button class="download-all" onclick="downloadAll()">Download All PNGs</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        function downloadCanvas(filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        function createServerStack1() {
            clearCanvas();
            canvas.width = 70;
            canvas.height = 110;
            
            // Draw server stack
            ctx.fillStyle = '#6a4fd9';
            for (let i = 0; i < 5; i++) {
                ctx.fillRect(0, i * 20, 60, 15);
                
                // Server lights
                ctx.fillStyle = '#e958a1';
                ctx.fillRect(10, i * 20 + 5, 20, 5);
                ctx.fillStyle = '#6a4fd9';
            }
            
            // Base
            ctx.fillStyle = '#8f76f5';
            ctx.fillRect(0, 100, 70, 10);
            
            downloadCanvas('server-stack1.png');
        }
        
        function createServerStack2() {
            clearCanvas();
            canvas.width = 70;
            canvas.height = 90;
            
            // Draw server stack
            ctx.fillStyle = '#6a4fd9';
            for (let i = 0; i < 4; i++) {
                ctx.fillRect(0, i * 20, 60, 15);
                
                // Server lights
                ctx.fillStyle = '#e958a1';
                ctx.fillRect(10, i * 20 + 5, 20, 5);
                ctx.fillStyle = '#6a4fd9';
            }
            
            // Base
            ctx.fillStyle = '#8f76f5';
            ctx.fillRect(0, 80, 70, 10);
            
            downloadCanvas('server-stack2.png');
        }
        
        function createAICore() {
            clearCanvas();
            canvas.width = 160;
            canvas.height = 160;
            
            // Processor chip base
            ctx.fillStyle = '#6a4fd9';
            ctx.fillRect(20, 20, 120, 120);
            
            ctx.fillStyle = '#e958a1';
            ctx.fillRect(30, 30, 100, 100);
            
            // ADZETA text
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('ADZETA', 80, 80);
            
            ctx.font = 'bold 14px Arial';
            ctx.fillText('AI CORE', 80, 100);
            
            downloadCanvas('adzeta-ai-core.png');
        }
        
        function createGooglePlatform() {
            clearCanvas();
            canvas.width = 80;
            canvas.height = 50;
            
            // Platform card
            ctx.fillStyle = 'white';
            ctx.strokeStyle = '#8f76f5';
            ctx.lineWidth = 1.5;
            ctx.beginPath();
            ctx.roundRect(0, 0, 80, 50, 5);
            ctx.fill();
            ctx.stroke();
            
            // Google text
            ctx.fillStyle = '#333';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('ANS', 40, 20);
            
            const colors = ['#4285F4', '#EA4335', '#FBBC05', '#4285F4', '#34A853', '#EA4335'];
            const letters = ['G', 'o', 'o', 'g', 'l', 'e'];
            let x = 40 - 15;
            
            for (let i = 0; i < letters.length; i++) {
                ctx.fillStyle = colors[i];
                ctx.fillText(letters[i], x, 35);
                x += 8;
            }
            
            downloadCanvas('google-platform.png');
        }
        
        function createMetaPlatform() {
            clearCanvas();
            canvas.width = 80;
            canvas.height = 50;
            
            // Platform card
            ctx.fillStyle = 'white';
            ctx.strokeStyle = '#8f76f5';
            ctx.lineWidth = 1.5;
            ctx.beginPath();
            ctx.roundRect(0, 0, 80, 50, 5);
            ctx.fill();
            ctx.stroke();
            
            // Meta text
            ctx.fillStyle = '#333';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Meta', 40, 25);
            
            // Meta infinity symbol
            ctx.strokeStyle = '#0081fb';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(20, 35);
            ctx.bezierCurveTo(23, 31, 27, 31, 30, 35);
            ctx.bezierCurveTo(33, 39, 37, 39, 40, 35);
            ctx.bezierCurveTo(43, 31, 47, 31, 50, 35);
            ctx.bezierCurveTo(53, 39, 57, 39, 60, 35);
            ctx.stroke();
            
            downloadCanvas('meta-platform.png');
        }
        
        function createTikTokPlatform() {
            clearCanvas();
            canvas.width = 80;
            canvas.height = 50;
            
            // Platform card
            ctx.fillStyle = 'white';
            ctx.strokeStyle = '#8f76f5';
            ctx.lineWidth = 1.5;
            ctx.beginPath();
            ctx.roundRect(0, 0, 80, 50, 5);
            ctx.fill();
            ctx.stroke();
            
            // TikTok logo
            ctx.fillStyle = '#000';
            ctx.beginPath();
            ctx.roundRect(25, 10, 30, 30, 3);
            ctx.fill();
            
            // TikTok note symbol
            ctx.fillStyle = '#00f2ea';
            ctx.beginPath();
            ctx.moveTo(35, 15);
            ctx.lineTo(35, 30);
            ctx.lineTo(30, 30);
            ctx.lineTo(30, 25);
            ctx.lineTo(35, 25);
            ctx.fill();
            
            ctx.fillStyle = '#ff004f';
            ctx.beginPath();
            ctx.moveTo(45, 15);
            ctx.lineTo(45, 30);
            ctx.lineTo(40, 30);
            ctx.lineTo(40, 25);
            ctx.lineTo(45, 25);
            ctx.fill();
            
            downloadCanvas('tiktok-platform.png');
        }
        
        function createOutcomesChart() {
            clearCanvas();
            canvas.width = 110;
            canvas.height = 80;
            
            // Chart bars
            ctx.fillStyle = '#ff8cc6';
            const barWidths = [15, 15, 15, 15, 15];
            const barHeights = [30, 40, 20, 50, 70];
            const barSpacing = 20;
            
            for (let i = 0; i < 5; i++) {
                ctx.fillRect(i * barSpacing, 75 - barHeights[i], barWidths[i], barHeights[i]);
            }
            
            // Arrow
            ctx.strokeStyle = '#ff8cc6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 40);
            ctx.lineTo(100, 5);
            ctx.stroke();
            
            // Arrow head
            ctx.fillStyle = '#ff8cc6';
            ctx.beginPath();
            ctx.moveTo(100, 0);
            ctx.lineTo(110, 5);
            ctx.lineTo(100, 10);
            ctx.fill();
            
            // Base line
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(0, 75);
            ctx.lineTo(95, 75);
            ctx.stroke();
            
            downloadCanvas('outcomes-chart.png');
        }
        
        function createDataStack1() {
            clearCanvas();
            canvas.width = 70;
            canvas.height = 85;
            
            // Data stack
            ctx.fillStyle = '#6a4fd9';
            ctx.fillRect(0, 0, 60, 10);
            
            ctx.fillStyle = '#e958a1';
            ctx.fillRect(0, 15, 60, 10);
            
            ctx.fillStyle = '#6a4fd9';
            ctx.fillRect(0, 30, 60, 10);
            
            ctx.fillStyle = '#e958a1';
            ctx.fillRect(0, 45, 60, 10);
            
            ctx.fillStyle = '#6a4fd9';
            ctx.fillRect(0, 60, 60, 10);
            
            // Base
            ctx.fillStyle = '#8f76f5';
            ctx.fillRect(0, 75, 70, 10);
            
            downloadCanvas('data-stack1.png');
        }
        
        function createDataStack2() {
            clearCanvas();
            canvas.width = 70;
            canvas.height = 70;
            
            // Data stack
            ctx.fillStyle = '#6a4fd9';
            ctx.fillRect(0, 0, 60, 10);
            
            ctx.fillStyle = '#e958a1';
            ctx.fillRect(0, 15, 60, 10);
            
            ctx.fillStyle = '#6a4fd9';
            ctx.fillRect(0, 30, 60, 10);
            
            ctx.fillStyle = '#e958a1';
            ctx.fillRect(0, 45, 60, 10);
            
            // Base
            ctx.fillStyle = '#8f76f5';
            ctx.fillRect(0, 60, 70, 10);
            
            downloadCanvas('data-stack2.png');
        }
        
        function downloadAll() {
            createServerStack1();
            setTimeout(() => createServerStack2(), 500);
            setTimeout(() => createAICore(), 1000);
            setTimeout(() => createGooglePlatform(), 1500);
            setTimeout(() => createMetaPlatform(), 2000);
            setTimeout(() => createTikTokPlatform(), 2500);
            setTimeout(() => createOutcomesChart(), 3000);
            setTimeout(() => createDataStack1(), 3500);
            setTimeout(() => createDataStack2(), 4000);
        }
    </script>
</body>
</html>
