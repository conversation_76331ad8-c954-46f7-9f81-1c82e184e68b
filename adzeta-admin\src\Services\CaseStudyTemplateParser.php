<?php

namespace AdZetaAdmin\Services;

/**
 * Case Study Template <PERSON><PERSON><PERSON>
 * Parses the luminous-skin-clinic template and extracts editable sections
 */
class CaseStudyTemplateParser
{
    private $templatePath;
    private $templateSections;

    public function __construct($templatePath = null)
    {
        $this->templatePath = $templatePath ?: $_SERVER['DOCUMENT_ROOT'] . '/luminous-skin-clinic-adzeta-predictive-ltv-targeting.php';
        $this->templateSections = $this->defineTemplateSections();
    }

    /**
     * Define all editable sections in the template
     */
    private function defineTemplateSections()
    {
        return [
            'hero' => [
                'title' => 'Hero Section',
                'fields' => [
                    'hero_badge_text' => [
                        'label' => 'Badge Text',
                        'type' => 'text',
                        'placeholder' => 'AI-DRIVEN CLIENT ACQUISITION',
                        'max_length' => 50
                    ],
                    'hero_title' => [
                        'label' => 'Main Title',
                        'type' => 'textarea',
                        'placeholder' => 'Client Name Achieves X% Growth Through...',
                        'max_length' => 200
                    ],
                    'hero_description' => [
                        'label' => 'Description',
                        'type' => 'textarea',
                        'placeholder' => 'Brief overview of the case study...',
                        'max_length' => 500
                    ],
                    'hero_image' => [
                        'label' => 'Hero Image',
                        'type' => 'image',
                        'placeholder' => 'images/case-studies/client-name.png'
                    ]
                ]
            ],
            'highlights' => [
                'title' => 'Project Highlights',
                'description' => '6 key sections that tell the story',
                'fields' => []
            ],
            'challenge' => [
                'title' => 'Challenge Section',
                'fields' => [
                    'challenge_title' => [
                        'label' => 'Challenge Title',
                        'type' => 'text',
                        'placeholder' => 'The Challenge: Main Problem Statement',
                        'max_length' => 100
                    ],
                    'challenge_subtitle' => [
                        'label' => 'Challenge Subtitle',
                        'type' => 'text',
                        'placeholder' => 'Understanding the Problem',
                        'max_length' => 100
                    ],
                    'challenge_description' => [
                        'label' => 'Challenge Description',
                        'type' => 'textarea',
                        'placeholder' => 'Detailed explanation of the challenge...',
                        'max_length' => 1000
                    ]
                ]
            ],
            'results' => [
                'title' => 'Results & Metrics',
                'fields' => [
                    'results_data' => [
                        'label' => 'Key Metrics',
                        'type' => 'json',
                        'structure' => [
                            'primary_metric' => 'Main result (e.g., 320%)',
                            'secondary_metric' => 'Secondary result (e.g., 41%)',
                            'tertiary_metric' => 'Third result (e.g., 12.5%)',
                            'additional_metrics' => 'Other metrics as needed'
                        ]
                    ]
                ]
            ],
            'seo' => [
                'title' => 'SEO & Meta Data',
                'fields' => [
                    'meta_title' => [
                        'label' => 'Meta Title',
                        'type' => 'text',
                        'placeholder' => 'Case Study Title | Company Name',
                        'max_length' => 60
                    ],
                    'meta_description' => [
                        'label' => 'Meta Description',
                        'type' => 'textarea',
                        'placeholder' => 'Brief description for search engines...',
                        'max_length' => 160
                    ],
                    'focus_keyword' => [
                        'label' => 'Focus Keyword',
                        'type' => 'text',
                        'placeholder' => 'main keyword phrase',
                        'max_length' => 50
                    ]
                ]
            ]
        ];
    }

    /**
     * Generate highlight sections (6 sections)
     */
    private function generateHighlightSections()
    {
        $highlights = [];
        $defaultTitles = [
            'Client Profile',
            'The Core Challenge', 
            'Previous Approach',
            'Our Solution',
            'Methodology',
            'Key Outcomes'
        ];

        $defaultIcons = [
            'bi-heart-pulse',
            'bi-question-circle',
            'bi-compass',
            'bi-cpu',
            'bi-gear-wide-connected',
            'bi-trophy'
        ];

        for ($i = 1; $i <= 6; $i++) {
            $highlights["highlight_{$i}"] = [
                'title' => "Highlight {$i}: " . $defaultTitles[$i-1],
                'fields' => [
                    "highlight_{$i}_title" => [
                        'label' => 'Section Title',
                        'type' => 'text',
                        'placeholder' => $defaultTitles[$i-1],
                        'max_length' => 100
                    ],
                    "highlight_{$i}_content" => [
                        'label' => 'Content',
                        'type' => 'textarea',
                        'placeholder' => 'Detailed content for this section...',
                        'max_length' => 500
                    ],
                    "highlight_{$i}_icon" => [
                        'label' => 'Bootstrap Icon',
                        'type' => 'select',
                        'options' => [
                            'bi-heart-pulse' => 'Heart Pulse',
                            'bi-question-circle' => 'Question Circle',
                            'bi-compass' => 'Compass',
                            'bi-cpu' => 'CPU',
                            'bi-gear-wide-connected' => 'Gear Connected',
                            'bi-trophy' => 'Trophy',
                            'bi-chart-line' => 'Chart Line',
                            'bi-target' => 'Target',
                            'bi-lightbulb' => 'Lightbulb',
                            'bi-rocket' => 'Rocket'
                        ],
                        'default' => $defaultIcons[$i-1]
                    ]
                ]
            ];
        }

        return $highlights;
    }

    /**
     * Get all template sections with highlight sections included
     */
    public function getAllSections()
    {
        $sections = $this->templateSections;
        $sections['highlights']['fields'] = $this->generateHighlightSections();
        return $sections;
    }

    /**
     * Get form configuration for case study editor
     */
    public function getEditorConfig()
    {
        $sections = $this->getAllSections();
        $config = [
            'sections' => [],
            'field_groups' => []
        ];

        foreach ($sections as $sectionKey => $section) {
            $config['sections'][$sectionKey] = [
                'title' => $section['title'],
                'description' => $section['description'] ?? '',
                'fields' => []
            ];

            if ($sectionKey === 'highlights') {
                // Special handling for highlights
                foreach ($section['fields'] as $highlightKey => $highlight) {
                    $config['sections'][$sectionKey]['fields'][$highlightKey] = $highlight;
                }
            } else {
                // Regular sections
                foreach ($section['fields'] as $fieldKey => $field) {
                    $config['sections'][$sectionKey]['fields'][$fieldKey] = $field;
                }
            }
        }

        return $config;
    }

    /**
     * Validate case study data against template structure
     */
    public function validateData($data)
    {
        $errors = [];
        $sections = $this->getAllSections();

        // Required fields
        $requiredFields = ['title', 'client_name', 'hero_title'];
        
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[] = "Field '{$field}' is required";
            }
        }

        // Validate field lengths
        foreach ($sections as $sectionKey => $section) {
            if ($sectionKey === 'highlights') {
                foreach ($section['fields'] as $highlightKey => $highlight) {
                    foreach ($highlight['fields'] as $fieldKey => $fieldConfig) {
                        if (isset($data[$fieldKey]) && isset($fieldConfig['max_length'])) {
                            if (strlen($data[$fieldKey]) > $fieldConfig['max_length']) {
                                $errors[] = "Field '{$fieldKey}' exceeds maximum length of {$fieldConfig['max_length']} characters";
                            }
                        }
                    }
                }
            } else {
                foreach ($section['fields'] as $fieldKey => $fieldConfig) {
                    if (isset($data[$fieldKey]) && isset($fieldConfig['max_length'])) {
                        if (strlen($data[$fieldKey]) > $fieldConfig['max_length']) {
                            $errors[] = "Field '{$fieldKey}' exceeds maximum length of {$fieldConfig['max_length']} characters";
                        }
                    }
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get template preview data
     */
    public function getPreviewData($caseStudyData)
    {
        // Process the case study data for template preview
        $previewData = $caseStudyData;
        
        // Ensure results_data is properly formatted
        if (isset($previewData['results_data']) && is_string($previewData['results_data'])) {
            $previewData['results_data'] = json_decode($previewData['results_data'], true);
        }

        // Add default values for missing fields
        $defaults = [
            'hero_badge_text' => 'AI-DRIVEN CLIENT ACQUISITION',
            'hero_image' => 'images/case-studies/default.png',
            'template' => 'luminous-skin-clinic'
        ];

        foreach ($defaults as $key => $value) {
            if (empty($previewData[$key])) {
                $previewData[$key] = $value;
            }
        }

        return $previewData;
    }

    /**
     * Get available Bootstrap icons for highlights
     */
    public function getAvailableIcons()
    {
        return [
            'bi-heart-pulse' => 'Heart Pulse',
            'bi-question-circle' => 'Question Circle', 
            'bi-compass' => 'Compass',
            'bi-cpu' => 'CPU',
            'bi-gear-wide-connected' => 'Gear Connected',
            'bi-trophy' => 'Trophy',
            'bi-chart-line' => 'Chart Line',
            'bi-target' => 'Target',
            'bi-lightbulb' => 'Lightbulb',
            'bi-rocket' => 'Rocket',
            'bi-graph-up' => 'Graph Up',
            'bi-bullseye' => 'Bullseye',
            'bi-puzzle' => 'Puzzle',
            'bi-shield-check' => 'Shield Check',
            'bi-star' => 'Star',
            'bi-award' => 'Award'
        ];
    }

    /**
     * Generate AI context for section generation
     */
    public function generateAIContext($caseStudyData, $sectionType)
    {
        $context = "Case Study: {$caseStudyData['title']}\n";
        $context .= "Client: {$caseStudyData['client_name']}\n";
        $context .= "Industry: {$caseStudyData['industry']}\n";
        
        if (!empty($caseStudyData['challenge_description'])) {
            $context .= "Challenge: {$caseStudyData['challenge_description']}\n";
        }
        
        if (!empty($caseStudyData['solution_description'])) {
            $context .= "Solution: {$caseStudyData['solution_description']}\n";
        }

        if (!empty($caseStudyData['results_data'])) {
            $results = is_string($caseStudyData['results_data']) 
                ? json_decode($caseStudyData['results_data'], true) 
                : $caseStudyData['results_data'];
            $context .= "Results: " . json_encode($results) . "\n";
        }

        return $context;
    }
}
