<?php
    // WordPress-Inspired Centralized Frontend Cache Integration
    require_once __DIR__ . '/adzeta-admin/src/Cache/FrontendCacheManager.php';

    // Initialize cache manager
    $cacheManager = new \AdZetaAdmin\Cache\FrontendCacheManager();

    // Get pagination and filter parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $industry = $_GET['industry'] ?? null;
    $search = $_GET['search'] ?? null;

    // Try to serve cached content first (BLAZING FAST!)
    $cachedContent = $cacheManager->getCacheStudiesListCache($page, $industry, $search);
    if ($cachedContent !== false) {
        // Content served with optimized headers and gzip compression
        echo $cachedContent;
        exit;
    }

    // Cache miss - generate content dynamically
    header('X-Cache: MISS');
    header('X-Cache-Reason: Generating fresh content');

    // Start output buffering to capture generated content for caching
    ob_start();

    // Initialize the application
    require_once 'bootstrap.php';
    require_once 'includes/CaseStudiesDatabase.php';

    // Get pagination parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $industry = $_GET['industry'] ?? null;
    $search = $_GET['search'] ?? null;
    $studiesPerPage = 12;

    // Get case studies from database
    $caseStudies = getCaseStudies([
        'page' => $page,
        'limit' => $studiesPerPage,
        'industry' => $industry,
        'search' => $search
    ]);

    // Get total count for pagination
    $totalStudies = getCaseStudiesDatabase()->getCaseStudiesCount([
        'industry' => $industry,
        'search' => $search
    ]);

    $totalPages = ceil($totalStudies / $studiesPerPage);

    // Get industries for filter
    $industries = getCaseStudiesDatabase()->getIndustries();

    // Helper functions for template
    function extractMetrics($resultsData) {
        if (empty($resultsData)) {
            return [];
        }
        
        // Extract up to 3 key metrics
        $metrics = [];
        $count = 0;
        foreach ($resultsData as $key => $value) {
            if ($count >= 3) break;
            
            // Format the metric label
            $label = ucwords(str_replace('_', ' ', $key));
            $metrics[] = [
                'value' => $value,
                'label' => $label
            ];
            $count++;
        }
        
        return $metrics;
    }

    function getCategoryFromIndustry($industry) {
        $categoryMap = [
            'Healthcare' => 'Healthcare',
            'Technology' => 'E-commerce',
            'E-commerce' => 'E-commerce',
            'Finance' => 'E-commerce',
            'Real Estate' => 'Proptech',
            'B2B' => 'B2B E-commerce'
        ];
        
        return $categoryMap[$industry] ?? 'E-commerce';
    }

    function getFilterClasses($industry) {
        $filterMap = [
            'Healthcare' => 'ecommerce social ltv',
            'Technology' => 'ecommerce ppc ltv',
            'E-commerce' => 'ecommerce ppc ltv',
            'Finance' => 'ecommerce social ltv',
            'Real Estate' => 'ecommerce ppc ltv',
            'B2B' => 'ecommerce ppc ltv'
        ];
        
        return $filterMap[$industry] ?? 'ecommerce ppc ltv';
    }

    // Set page title and meta description
    $pageTitle = 'Case Studies - Proven Results for E-commerce Brands | AdZeta';
    $metaDescription = 'See how our AI-powered Value-Based Bidding approach has transformed profitability and sustainable growth for leading e-commerce businesses.';
    if ($industry) {
        $pageTitle = ucfirst($industry) . ' Case Studies | AdZeta';
        $metaDescription = "Discover how AdZeta's AI-powered solutions have helped " . strtolower($industry) . " businesses achieve remarkable growth and profitability.";
    }
    if ($search) {
        $pageTitle = 'Search Results: ' . htmlspecialchars($search) . ' | AdZeta Case Studies';
    }

    include 'header.php'; ?>
<!-- end header -->
  <link rel="stylesheet" href="css/case-studies-enhanced.css?v=1.0" />
<!-- start page title -->
<section class="page-title-big-typography ipad-top-space-margin case-studies-header" data-parallax-background-ratio="0.5" aria-label="Case studies header section">

    <div class="professional-gradient-container">
        <div class="corner-gradient top-left"></div>
        <div class="corner-gradient top-right"></div>
        <div class="corner-gradient bottom-left"></div>
        <div class="corner-gradient bottom-right"></div>
        <div class="diagonal-gradient"></div>
        <div class="mesh-overlay"></div>
        <div class="vignette-overlay"></div>
    </div>
    <!-- Particles container overlay -->
    <div id="particles-style-03" class="particles-container position-absolute top-0 left-0 w-100 h-100 z-index-1" data-particle="true" data-particle-options='{
        "particles": {
        "number": {
        "value": 40,
        "density": {
        "enable": true,
        "value_area": 800
        }
        },
        "color": {
        "value": ["#e958a1", "#ff7042", "#d15ec7", "#8f76f5"]
        },
        "shape": {
        "type": "circle"
        },
        "size": {
        "value": 4,
        "random": true,
        "anim": {
        "enable": true,
        "speed": 1
        }
        },
        "opacity": {
        "value": 0.5,
        "random": true,
        "anim": {
        "enable": true,
        "speed": 1,
        "opacity_min": 0.1,
        "sync": false
        }
        },
        "move": {
        "enable": true,
        "direction": "none",
        "speed": 1.5,
        "random": true,
        "straight": false,
        "out_mode": "out",
        "bounce": false,
        "attract": {
        "enable": true,
        "rotateX": 600,
        "rotateY": 1200
        }
        },
        "line_linked": {
        "enable": true,
        "distance": 150,
        "color": "#e958a1",
        "opacity": 0.2,
        "width": 1
        }
        },
        "interactivity": {
        "detect_on": "canvas",
        "events": {
        "onhover": {
        "enable": true,
        "mode": "grab"
        },
        "onclick": {
        "enable": true,
        "mode": "push"
        },
        "resize": true
        },
        "modes": {
        "grab": {
        "distance": 140,
        "line_linked": {
        "opacity": 1
        }
        },
        "push": {
        "particles_nb": 4
        }
        }
        },
        "retina_detect": true
        }'></div>
    <div class="container">
        <div class="row align-items-center justify-content-center small-screen">
            <div class="col-xl-7 col-lg-8 col-md-10 position-relative text-center">
                <!-- Small AI icon above the title -->
                <h1 class="mb-20px mt-5 text-white ls-minus-1px  text-shadow-double-large alt-font"><span class="fw-400">Proven Results for</span><span class="text-gradient-purple-blue fw-700">E-commerce Brands</span></h1>
                <p class="mb-30 alt-font text-white opacity-8 fw-400 ls-1px fs-20 sm-fs-16 xs-fs-14 mx-auto">See how our AI-powered Value-Based Bidding approach has transformed profitability and sustainable growth for these leading businesses.</p>
                <!-- Animated down arrow -->
                <div class=" animate-bounce">
                    <div class="down-section text-center">
                        <a href="#down-section" class="section-link">
                            <div class="d-flex justify-content-center align-items-center mx-auto rounded-circle h-55px w-55px fs-22 text-dark-gray bg-white">
                                <i class="feather icon-feather-arrow-down"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end page title -->
<!-- start section -->
<section class="pt-5 position-relative" id="down-section">
    <!-- Subtle background pattern -->
    <div class="position-absolute top-0 left-0 w-100 h-100" style="background-image: url('images/subtle-mesh-pattern.png'); opacity: 0.05; z-index: -1;"></div>
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <!-- filter navigation -->
                <div class="mb-5 w-100">
                    <div class="w-100 overflow-hidden">
                        <ul class="portfolio-filter nav nav-tabs justify-content-center border-0 fw-500 alt-font pb-3">
                            <li class="nav active"><a data-filter="*" href="#"><i class="bi bi-grid-3x3-gap-fill me-3"></i>All Case Studies</a></li>
                            <li class="nav"><a data-filter=".ecommerce" href="#"><i class="bi bi-cart-check me-3"></i>E-commerce</a></li>
                            <li class="nav"><a data-filter=".ppc" href="#"><i class="bi bi-google me-3"></i>Google Ads</a></li>
                            <li class="nav"><a data-filter=".social" href="#"><i class="bi bi-facebook me-3"></i>Paid Social</a></li>
                            <li class="nav"><a data-filter=".ltv" href="#"><i class="bi bi-graph-up-arrow me-3"></i>LTV Optimization</a></li>
                        </ul>
                    </div>
                    <div class="text-center mb-4">
                        <p class="mb-0 text-medium">Showing our most recent client success stories</p>
                    </div>
                </div>
                <!-- end filter navigation -->
            </div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-12 filter-content">
                <ul class="portfolio-wrapper grid grid-3col xxl-grid-3col xl-grid-3col lg-grid-3col md-grid-2col sm-grid-2col xs-grid-1col gutter-large">
                    <li class="grid-sizer"></li>

                    <?php if (!empty($caseStudies)): ?>
                        <?php foreach ($caseStudies as $caseStudy): ?>
                            <?php
                                $metrics = extractMetrics($caseStudy['results_data']);
                                $category = getCategoryFromIndustry($caseStudy['industry']);
                                $filterClasses = getFilterClasses($caseStudy['industry']);
                                $caseStudyUrl = "/case-studies/" . urlencode($caseStudy['slug']) . "/";
                                $featuredImage = $caseStudy['featured_image'] ?: $caseStudy['hero_image'] ?: 'images/demo-accounting-process-03.jpg';
                                $description = $caseStudy['hero_description'] ?: $caseStudy['excerpt'] ?: 'Discover how this client achieved remarkable results through our AI-powered approach.';
                            ?>
                            <!-- Case Study: <?= htmlspecialchars($caseStudy['client_name']) ?> -->
                            <li class="grid-item <?= $filterClasses ?>">
                                <div class="case-study-item">
                                    <a href="<?= $caseStudyUrl ?>" class="case-study-full-link" aria-label="<?= htmlspecialchars($caseStudy['title']) ?>"></a>
                                    <div class="case-study-image">
                                        <img src="<?= htmlspecialchars($featuredImage) ?>" alt="<?= htmlspecialchars($caseStudy['title']) ?>" />
                                        <div class="case-study-category"><?= htmlspecialchars($category) ?></div>
                                    </div>
                                    <div class="case-study-content">
                                        <h3 class="case-study-title"><?= htmlspecialchars($caseStudy['title']) ?></h3>
                                        <p class="case-study-description"><?= htmlspecialchars(substr($description, 0, 150)) ?><?= strlen($description) > 150 ? '...' : '' ?></p>
                                        <div class="case-study-metrics">
                                            <?php foreach ($metrics as $metric): ?>
                                                <div class="metric">
                                                    <div class="metric-value"><?= htmlspecialchars($metric['value']) ?></div>
                                                    <div class="metric-label"><?= htmlspecialchars($metric['label']) ?></div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- No case studies message -->
                        <li class="grid-item col-12">
                            <div class="text-center py-5">
                                <h4 class="mb-3">No Case Studies Available</h4>
                                <p class="text-muted">We're working on adding more client success stories. Check back soon!</p>
                            </div>
                        </li>
                    <?php endif; ?>
                </ul>

                <!-- No results message for filtering -->
                <div class="grid-no-results text-center py-5 d-none">
                    <div class="mb-3">
                        <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto opacity-50">
                            <path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="#6c6d80" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M21 21L16.65 16.65" stroke="#6c6d80" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 11H14" stroke="#6c6d80" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h4 class="mb-2">No Case Studies Found</h4>
                    <p class="mb-4">We couldn't find any case studies matching your selected filter.</p>
                    <button class="btn btn-medium btn-transparent-gradient-pink reset-filters">Show All Case Studies</button>
                </div>
            </div>
        </div>

        <?php if ($totalPages > 1): ?>
        <!-- Pagination -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="pagination-style-01 justify-content-center">
                    <ul class="pagination">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?><?= $industry ? '&industry=' . urlencode($industry) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                                    <i class="feather icon-feather-arrow-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?><?= $industry ? '&industry=' . urlencode($industry) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?><?= $industry ? '&industry=' . urlencode($industry) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>">
                                    <i class="feather icon-feather-arrow-right"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>
<!-- end section -->
<!-- start footer -->
<?php include 'footer.php'; ?>

<?php
    // Capture the generated content
    $content = ob_get_contents();
    ob_end_clean();

    // Cache the content for future requests (BLAZING FAST!)
    $cacheManager->setCacheStudiesListCache($page, $industry, $search, $content);

    // Output the content with optimized headers
    echo $content;
?>
