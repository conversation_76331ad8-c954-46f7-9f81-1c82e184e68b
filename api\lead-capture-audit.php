<?php
/**
 * Lead Capture Audit Form Handler
 * Processes free audit form submissions
 */

// Set proper headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed. Please use POST.'
    ]);
    exit;
}

try {
    // Get form data
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $work_email = trim($_POST['work_email'] ?? '');
    $company_name = trim($_POST['company_name'] ?? '');
    $website_url = trim($_POST['website_url'] ?? '');
    $monthly_ad_spend = trim($_POST['monthly_ad_spend'] ?? '');
    $primary_goal = trim($_POST['primary_goal'] ?? '');
    $current_challenges = trim($_POST['current_challenges'] ?? '');
    $preferred_contact = trim($_POST['preferred_contact'] ?? '');
    
    // Validate required fields
    $errors = [];
    
    if (empty($first_name)) {
        $errors[] = 'First name is required';
    }
    
    if (empty($last_name)) {
        $errors[] = 'Last name is required';
    }
    
    if (empty($work_email)) {
        $errors[] = 'Work email is required';
    } elseif (!filter_var($work_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address';
    }
    
    if (empty($company_name)) {
        $errors[] = 'Company name is required';
    }
    
    if (empty($website_url)) {
        $errors[] = 'Website URL is required';
    } elseif (!filter_var($website_url, FILTER_VALIDATE_URL) && !preg_match('/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', $website_url)) {
        // If not a valid URL, try to validate as domain
        $website_url = 'https://' . $website_url;
        if (!filter_var($website_url, FILTER_VALIDATE_URL)) {
            $errors[] = 'Please enter a valid website URL';
        }
    }
    
    if (empty($monthly_ad_spend)) {
        $errors[] = 'Monthly ad spend is required';
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please fix the following errors: ' . implode(', ', $errors)
        ]);
        exit;
    }
    
    // Prepare email content
    $full_name = $first_name . ' ' . $last_name;
    $submission_time = date('Y-m-d H:i:s');
    
    // Email to admin
    $admin_email = '<EMAIL>'; // Change this to your admin email
    $admin_subject = 'New Free Audit Request from ' . $full_name;
    
    $admin_message = "
    <html>
    <head>
        <title>New Free Audit Request</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .field { margin-bottom: 15px; }
            .field-label { font-weight: bold; color: #555; }
            .field-value { margin-left: 10px; }
            .footer { background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h2>🎯 New Free Audit Request</h2>
        </div>
        <div class='content'>
            <p><strong>A new free audit request has been submitted:</strong></p>
            
            <div class='field'>
                <span class='field-label'>Name:</span>
                <span class='field-value'>{$full_name}</span>
            </div>
            
            <div class='field'>
                <span class='field-label'>Email:</span>
                <span class='field-value'>{$work_email}</span>
            </div>
            
            <div class='field'>
                <span class='field-label'>Company:</span>
                <span class='field-value'>{$company_name}</span>
            </div>
            
            <div class='field'>
                <span class='field-label'>Website:</span>
                <span class='field-value'><a href='{$website_url}' target='_blank'>{$website_url}</a></span>
            </div>
            
            <div class='field'>
                <span class='field-label'>Monthly Ad Spend:</span>
                <span class='field-value'>{$monthly_ad_spend}</span>
            </div>
            
            <div class='field'>
                <span class='field-label'>Primary Goal:</span>
                <span class='field-value'>{$primary_goal}</span>
            </div>
            
            <div class='field'>
                <span class='field-label'>Current Challenges:</span>
                <span class='field-value'>{$current_challenges}</span>
            </div>
            
            <div class='field'>
                <span class='field-label'>Preferred Contact:</span>
                <span class='field-value'>{$preferred_contact}</span>
            </div>
            
            <div class='field'>
                <span class='field-label'>Submitted:</span>
                <span class='field-value'>{$submission_time}</span>
            </div>
        </div>
        <div class='footer'>
            <p>This email was sent from the AdZeta free audit form.</p>
        </div>
    </body>
    </html>
    ";
    
    // Email headers
    $admin_headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: AdZeta Website <<EMAIL>>',
        'Reply-To: ' . $work_email,
        'X-Mailer: PHP/' . phpversion()
    ];
    
    // Send admin email
    $admin_sent = mail($admin_email, $admin_subject, $admin_message, implode("\r\n", $admin_headers));
    
    // Email to user (confirmation)
    $user_subject = 'Thank you for your free audit request - AdZeta';
    
    $user_message = "
    <html>
    <head>
        <title>Thank you for your audit request</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .cta { background: #f8f9fa; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
            .button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold; }
            .footer { background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h2>🎉 Thank You, {$first_name}!</h2>
        </div>
        <div class='content'>
            <p>Thank you for requesting a free ValueGap audit from AdZeta!</p>
            
            <p>We've received your request and our team will analyze your information to prepare a customized audit report.</p>
            
            <h3>What happens next?</h3>
            <ol>
                <li><strong>Review:</strong> Our experts will analyze your current advertising setup</li>
                <li><strong>Prepare:</strong> We'll create a custom audit report with actionable insights</li>
                <li><strong>Contact:</strong> We'll reach out within 24 hours to schedule your results presentation</li>
            </ol>
            
            <div class='cta'>
                <p><strong>While you wait, explore our platform and case studies:</strong></p>
                <a href='https://adzeta.io/platform' class='button'>Learn About Our Platform</a>
                <a href='https://adzeta.io/case-studies' class='button' style='margin-left: 10px;'>View Case Studies</a>
            </div>
            
            <p>If you have any immediate questions, feel free to reply to this email or contact us directly.</p>
            
            <p>Best regards,<br>
            The AdZeta Team</p>
        </div>
        <div class='footer'>
            <p>AdZeta - Predictive Customer Lifetime Value Targeting<br>
            <a href='https://adzeta.io'>adzeta.io</a> | <a href='mailto:<EMAIL>'><EMAIL></a></p>
        </div>
    </body>
    </html>
    ";
    
    $user_headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: AdZeta Team <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'X-Mailer: PHP/' . phpversion()
    ];
    
    // Send user confirmation email
    $user_sent = mail($work_email, $user_subject, $user_message, implode("\r\n", $user_headers));
    
    // Log the submission (optional - create logs directory if needed)
    $log_entry = [
        'timestamp' => $submission_time,
        'name' => $full_name,
        'email' => $work_email,
        'company' => $company_name,
        'website' => $website_url,
        'ad_spend' => $monthly_ad_spend,
        'goal' => $primary_goal,
        'challenges' => $current_challenges,
        'contact_method' => $preferred_contact,
        'admin_email_sent' => $admin_sent,
        'user_email_sent' => $user_sent
    ];
    
    // Create logs directory if it doesn't exist
    $logs_dir = __DIR__ . '/logs';
    if (!file_exists($logs_dir)) {
        mkdir($logs_dir, 0755, true);
    }
    
    // Log to file
    $log_file = $logs_dir . '/audit-requests-' . date('Y-m') . '.log';
    file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => "Thank you, {$first_name}! Your audit request has been submitted successfully. We'll contact you within 24 hours with your personalized ValueGap audit results."
    ]);
    
} catch (Exception $e) {
    // Log error
    error_log('Audit form error: ' . $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while processing your request. Please try again or contact us directly.'
    ]);
}
?>
