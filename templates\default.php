<?php
/**
 * Default Template
 * Used when no specific template is defined for a page
 */

// Set page data if not already set
if (!isset($pageData)) {
    $pageData = [
        'title' => $page['title'] ?? 'AdZeta',
        'description' => $page['meta_description'] ?? DEFAULT_META_DESCRIPTION,
        'keywords' => $page['meta_keywords'] ?? DEFAULT_META_KEYWORDS,
        'template' => 'default'
    ];
}

// Include header
include __DIR__ . '/../header.php';
?>

<main class="main-content">
    <?php if (isset($page['content'])): ?>
        <section class="page-content">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <?php if (isset($page['title'])): ?>
                            <h1><?php echo htmlspecialchars($page['title']); ?></h1>
                        <?php endif; ?>
                        
                        <div class="content">
                            <?php echo $page['content']; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php else: ?>
        <section class="page-content">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h1>Welcome to AdZeta</h1>
                        <p>This is the default template. Content will be displayed here.</p>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
</main>

<?php
// Include footer
include __DIR__ . '/../footer.php';
?>
