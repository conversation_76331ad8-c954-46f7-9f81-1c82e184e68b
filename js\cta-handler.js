/**
 * Adzeta CTA Handler
 * 
 * This script handles all CTA interactions across the Adzeta website.
 * It manages the audit request modal and form submissions.
 */

(function() {
    'use strict';
    
    // Load the modal HTML via AJAX if it's not already in the page
    function loadModalContent() {
        // Check if modal already exists in the DOM
        if (document.getElementById('auditRequestModal')) {
            return Promise.resolve();
        }
        
        // Fetch the modal HTML
        return fetch('/cta-modal.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Failed to load modal content');
                }
                return response.text();
            })
            .then(html => {
                // Append the modal HTML to the body
                const modalContainer = document.createElement('div');
                modalContainer.innerHTML = html;
                document.body.appendChild(modalContainer);
            })
            .catch(error => {
                console.error('Error loading modal:', error);
            });
    }
    
    // Initialize CTA buttons
    function initCTAButtons() {
        // Find all CTA buttons with the appropriate class or data attribute
        const ctaButtons = document.querySelectorAll('.cta-audit-button, [data-cta="audit"], a[href="/free-audit"]');
        
        // Add click event listeners to each button
        ctaButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Load the modal content if needed, then show it
                loadModalContent().then(() => {
                    const auditModal = new bootstrap.Modal(document.getElementById('auditRequestModal'));
                    auditModal.show();
                });
            });
        });
    }
    
    // Handle form submission
    function initFormHandlers() {
        // Listen for dynamically added forms
        document.addEventListener('submit', function(e) {
            // Check if this is one of our audit request forms
            if (e.target.id === 'audit-request-form' || e.target.id === 'modal-audit-request-form') {
                e.preventDefault();
                
                // Basic form validation
                const form = e.target;
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('is-invalid');
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });
                
                // Email validation
                const emailField = form.querySelector('input[type="email"]');
                if (emailField && emailField.value) {
                    // Business email validation (basic check for non-common domains)
                    const commonPersonalDomains = [
                        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 
                        'aol.com', 'icloud.com', 'mail.com', 'protonmail.com'
                    ];
                    
                    const emailDomain = emailField.value.split('@')[1].toLowerCase();
                    
                    // First check basic email format
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(emailField.value)) {
                        isValid = false;
                        emailField.classList.add('is-invalid');
                        
                        // Add custom validation message
                        let feedbackEl = emailField.nextElementSibling;
                        if (!feedbackEl || !feedbackEl.classList.contains('invalid-feedback')) {
                            feedbackEl = document.createElement('div');
                            feedbackEl.classList.add('invalid-feedback');
                            emailField.parentNode.insertBefore(feedbackEl, emailField.nextSibling);
                        }
                        feedbackEl.textContent = 'Please enter a valid email address.';
                    } 
                    // Then check if it's a business email
                    else if (commonPersonalDomains.includes(emailDomain)) {
                        // We'll allow personal emails but show a warning
                        const warningEl = document.createElement('div');
                        warningEl.classList.add('text-warning', 'small', 'mt-1');
                        warningEl.textContent = 'Consider using your work email for faster processing.';
                        
                        // Remove any existing warning
                        const existingWarning = emailField.parentNode.querySelector('.text-warning');
                        if (existingWarning) {
                            existingWarning.remove();
                        }
                        
                        emailField.parentNode.insertBefore(warningEl, emailField.nextSibling);
                    }
                }
                
                // If form is valid, submit it
                if (isValid) {
                    // Get the submit button and show loading state
                    const submitButton = form.querySelector('button[type="submit"]');
                    const originalButtonText = submitButton.innerHTML;
                    submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';
                    submitButton.disabled = true;
                    
                    // Prepare form data
                    const formData = new FormData(form);
                    
                    // Send the form data via AJAX
                    fetch(form.action, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Handle successful submission
                        if (form.id === 'modal-audit-request-form') {
                            // Hide the modal
                            const auditModal = bootstrap.Modal.getInstance(document.getElementById('auditRequestModal'));
                            auditModal.hide();
                        }
                        
                        // Show success message
                        const successMessage = document.createElement('div');
                        successMessage.className = 'alert alert-success mt-3';
                        successMessage.innerHTML = '<strong>Thank you!</strong> Your audit request has been submitted. Our team will contact you shortly.';
                        
                        // Insert the success message
                        form.parentNode.insertBefore(successMessage, form);
                        
                        // Hide the form
                        form.style.display = 'none';
                        
                        // If we're on the dedicated page, scroll to the success message
                        if (form.id === 'audit-request-form') {
                            successMessage.scrollIntoView({ behavior: 'smooth' });
                        }
                    })
                    .catch(error => {
                        console.error('Error submitting form:', error);
                        
                        // Show error message
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'alert alert-danger mt-3';
                        errorMessage.innerHTML = '<strong>Submission Error</strong> There was a problem submitting your request. Please try again or contact us directly.';
                        
                        // Insert the error message
                        form.parentNode.insertBefore(errorMessage, form.nextSibling);
                        
                        // Reset button
                        submitButton.innerHTML = originalButtonText;
                        submitButton.disabled = false;
                    });
                }
            }
        });
    }
    
    // Initialize when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        initCTAButtons();
        initFormHandlers();
        
        // Preload the modal HTML for faster response
        if (document.querySelectorAll('.cta-audit-button, [data-cta="audit"], a[href="/free-audit"]').length > 0) {
            // Only preload if there are CTA buttons on the page
            setTimeout(() => {
                loadModalContent();
            }, 2000); // Delay preloading to prioritize page rendering
        }
    });
})();
