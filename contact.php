﻿<?php include 'header.php'; ?>
        <!-- end header -->
        <!-- end header --> 
        <!-- start page title -->
        <section class="pt-0 cover-background ipad-top-space-margin sm-pb-0" style="background-image:url('images/demo-it-business-contact-title-bg.jpg');">
		<div class="professional-gradient-container">
                <div class="corner-gradient top-left"></div>
                <div class="corner-gradient top-right"></div>
                <div class="corner-gradient bottom-left"></div>
                <div class="corner-gradient bottom-right"></div>
                <div class="diagonal-gradient"></div>
                <div class="mesh-overlay"></div>
                <div class="vignette-overlay"></div>
            </div>
            <div class="shape-image-animation bottom-0 p-0 w-100 d-none d-md-block"> 
                <svg xmlns="http://www.w3.org/2000/svg" widht="3000" height="400" viewBox="0 180 2500 200" fill="#ffffff"> 
                <path class="st1" d="M 0 250 C 1200 400 1200 50 3000 250 L 3000 550 L 0 550 L 0 250">
                <animate
                    attributeName="d"
                    dur="5s"
                    values="M 0 250 C 1200 400 1200 50 3000 250 L 3000 550 L 0 550 L 0 250;
                            M 0 250 C 400 50 400 400 3000 250 L 3000 550 L 0 550 L 0 250;
                            M 0 250 C 1200 400 1200 50 3000 250 L 3000 550 L 0 550 L 0 250"
                    repeatCount="indefinite"
                    />
                </path>
                </svg>
            </div>
            <div class="container">
                <div class="row align-items-center justify-content-center h-400px sm-h-300px">
                    <div class="col-12 col-md-6 position-relative text-center page-title-extra-large d-flex flex-wrap flex-column align-items-center justify-content-center" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 300, "easing": "easeOutQuad" }'>
                        <span class="ps-25px pe-25px pt-5px pb-5px mb-15px text-uppercase text-white fs-12 ls-1px fw-600 border-radius-100px bg-gradient-dark-gray-transparent d-flex"><i class="bi bi-megaphone text-white icon-small me-10px"></i>Grow your business with us</span>
                        <h1 class="mb-20px text-white fw-600 ls-minus-1px">Contact us</h1>
                    </div> 
                </div>
            </div>
        </section>
        <!-- end page title -->
        <!-- start section -->
        <section class="pt-3 sm-pt-50px">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-xl-5 col-lg-8 col-md-10 lg-mb-50px" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 250, "easing": "easeOutQuad" }'>
                        <div class="position-sticky top-120px lg-top-0px lg-position-relative text-center text-xl-start">
                            <span class="ps-25px pe-25px mb-20px text-uppercase text-base-color fs-12 lh-40 fw-700 border-radius-100px bg-gradient-very-light-gray-transparent d-inline-flex justify-content-center justify-content-xl-start"><i class="bi bi-chat-square-dots fs-16 me-5px"></i>Lets's work together</span>
                            <h3 class="text-dark-gray ls-minus-2px fw-700">Call or visit us at one of our different locations.</h3>
                            <p class="mb-35px w-90 lg-w-100 sm-mb-25px">Amet minim mollit non deserunt ullamco est aliqua dolor do amet sint. Velit officia consequat duis enim velit mollit amet minim mollit on.</p>
                            <a href="#" class="btn btn-extra-large btn-switch-text btn-gradient-pink-orange left-icon btn-rounded me-10px ls-0px">
                                <span>
                                    <span><i class="bi bi-telephone-outbound"></i></span>
                                    <span class="btn-double-text" data-text="Schedule a call">Schedule a call</span>
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="col-xl-6 col-lg-8 col-md-10 offset-xl-1">
                        <div class="row row-cols-1 justify-content-center" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 0, "staggervalue": 150, "easing": "easeOutQuad" }'>
                            <!-- start services box item -->
                            <div class="col services-box-style-02 mb-30px">
                                <div class="row g-0 box-shadow-quadruple-large border-radius-6px overflow-hidden">
                                    <div class="col-lg-6 col-sm-6">
                                        <div class="h-100 cover-background xs-h-300px" style="background-image: url(images/austin.jpg)"></div>
                                    </div>
                                    <div class="col-lg-6 col-sm-6 bg-white box-shadow-extra-large p-40px xl-p-30px">
                                        <div class="services-box-content last-paragraph-no-margin">
                                            <span class="d-block text-dark-gray primary-font fw-700 fs-19 mb-10px">Austin</span>
                                            <p>167 Hawthorne, Business central, New York, 11722</p>
                                            <a href="#" class="fs-16 lh-20 primary-font fw-500 text-dark-gray text-decoration-line-bottom d-inline-block mb-25px">View on map</a>
                                            <div class="text-dark-gray fw-600"><i class="feather icon-feather-phone-call icon-small me-10px text-dark-gray"></i><a href="tel:+****************">+****************</a></div>
                                        </div> 
                                    </div> 
                                </div>
                            </div>
                            <!-- end services box item --> 
                            <!-- start services box item -->
                            <div class="col services-box-style-02 mb-30px">
                                <div class="row g-0 box-shadow-quadruple-large border-radius-6px overflow-hidden">
                                    <div class="col-lg-6 col-sm-6">
                                        <div class="h-100 cover-background xs-h-300px" style="background-image: url(images/toronto.jpg)"></div>
                                    </div>
                                    <div class="col-lg-6 col-sm-6 bg-white box-shadow-extra-large p-40px xl-p-30px">
                                        <div class="services-box-content last-paragraph-no-margin">
                                            <span class="d-block text-dark-gray primary-font fw-700 fs-19 mb-10px">Toronto</span>
                                            <p>777 Magnolia farme ville, San Francisco, 93223</p>
                                            <a href="#" class="fs-16 lh-20 primary-font fw-500 text-dark-gray text-decoration-line-bottom d-inline-block mb-25px">View on map</a>
                                            <div class="text-dark-gray fw-600"><i class="feather icon-feather-phone-call icon-small me-10px text-dark-gray"></i><a href="tel:+****************">+****************</a></div>
                                        </div> 
                                    </div>
                                </div>
                            </div>
                            <!-- end services box item --> 
                            <!-- start services box item -->
                            <div class="col services-box-style-02 md-mb-30px">
                                <div class="row g-0 box-shadow-quadruple-large border-radius-6px overflow-hidden">
                                    <div class="col-lg-6 col-sm-6">
                                        <div class="h-100 cover-background xs-h-300px" style="background-image: url(images/munich.jpg)"></div>
                                    </div>
                                    <div class="col-lg-6 col-sm-6 bg-white box-shadow-extra-large p-40px xl-p-30px">
                                        <div class="services-box-content last-paragraph-no-margin">
                                            <span class="d-block text-dark-gray primary-font fw-700 fs-19 mb-10px">Munich</span>
                                            <p>959 Seven forks tower, Paris, France, 30557</p>
                                            <a href="#" class="fs-16 lh-20 primary-font fw-500 text-dark-gray text-decoration-line-bottom d-inline-block mb-25px">View on map</a>
                                            <div class="text-dark-gray fw-600"><i class="feather icon-feather-phone-call icon-small me-10px text-dark-gray"></i><a href="tel:+****************">+****************</a></div>
                                        </div> 
                                    </div>
                                </div>
                            </div>
                            <!-- end services box item --> 
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section -->
        <!-- start section --> 
        <section class="bg-very-light-gray position-relative">     
            <div class="container">
                <div class="row mb-8">
                    <div class="col-xl-5 col-lg-6 md-mb-50px" data-anime='{ "el": "childs", "translateX": [-50, 0], "opacity": [0,1], "duration": 1200, "delay": 0, "staggervalue": 150, "easing": "easeOutQuad" }'>  
                        <div class="bg-white border-radius-6px box-shadow-quadruple-large p-10 ps-12 pe-12 lg-ps-8 lg-pe-8 h-100 d-flex flex-wrap flex-column justify-content-center" data-anime='{ "el": "childs", "translateY": [0, 0], "opacity": [0,1], "duration": 1200, "delay": 0, "staggervalue": 150, "easing": "easeOutQuad" }'>
                            <span class="ps-25px pe-25px mb-20px text-uppercase text-base-color fs-12 lh-40 fw-700 border-radius-100px bg-gradient-very-light-gray-transparent d-inline-flex align-self-start"><i class="bi bi-chat-square-dots fs-16 me-5px"></i>Lets's work together</span>
                            <h4 class="text-dark-gray ls-minus-1px fw-700 mb-15px">Ready to help you!</h4>
                            <p class="w-85 sm-w-100">We're here to help and answer any question you might have.</p>
                            <div class="row row-cols-1 row-cols-sm-2">
                                <div class="col last-paragraph-no-margin mb-25px">
                                    <p>Call us directly?</p>
                                    <a href="tel:12345678910" class="text-dark-gray fw-600">+****************</a>
                                </div>
                                <div class="col last-paragraph-no-margin mb-25px">
                                    <p>Need live support?</p>
                                    <a href="/cdn-cgi/l/email-protection#bed7d0d8d1fedad1d3dfd7d090ddd1d3" class="text-dark-gray fw-600"><span class="__cf_email__" data-cfemail="066f6860694662696b676f682865696b"><EMAIL></span></a>
                                </div>
                                <div class="col last-paragraph-no-margin sm-mb-25px">
                                    <p>Join growing team?</p>
                                    <a href="/cdn-cgi/l/email-protection#52383d3b3c12363d3f333b3c7c313d3f" class="text-dark-gray fw-600"><span class="__cf_email__" data-cfemail="d5bfbabcbb95b1bab8b4bcbbfbb6bab8"><EMAIL></span></a>    
                                </div>
                                <div class="col last-paragraph-no-margin">
                                    <p>Visit headquarters?</p>
                                    <a href="https://maps.google.com/maps?ll=-37.805688,144.962312&amp;z=17&amp;t=m&amp;hl=en-US&amp;gl=IN&amp;mapclient=embed&amp;cid=13153204942596594449" target="_blank" class="text-dark-gray fw-600">View on google map</a>    
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 offset-xl-1 md-mb-50px sm-mb-0" data-anime='{ "el": "childs", "translateX": [50, 0], "opacity": [0,1], "duration": 1200, "delay": 0, "staggervalue": 150, "easing": "easeOutQuad" }'>
                        <h3 class="text-dark-gray ls-minus-2px fw-700">Looking for any help?</h3>
                        <form action="email-templates/contact-form.php" method="post" class="contact-form-style-03">
                            <label for="exampleInputEmail1" class="form-label fs-13 text-uppercase text-dark-gray fw-700 mb-0">Enter your name*</label>
                            <div class="position-relative form-group mb-20px">
                                <span class="form-icon"><i class="bi bi-emoji-smile text-dark-gray"></i></span>
                                <input class="fs-15 ps-0 border-radius-0px border-color-dark-gray bg-transparent form-control required" id="exampleInputEmail1" type="text" name="name" placeholder="What's your good name" />
                            </div>
                            <label for="exampleInputEmail1" class="form-label fs-13 text-uppercase text-dark-gray fw-700 mb-0">Email address*</label>
                            <div class="position-relative form-group mb-20px">
                                <span class="form-icon"><i class="bi bi-envelope text-dark-gray"></i></span>
                                <input class="fs-15 ps-0 border-radius-0px border-color-dark-gray bg-transparent form-control required" id="exampleInputEmail2" type="email" name="email" placeholder="Enter your email address" />
                            </div>
                            <label for="exampleInputEmail1" class="form-label fs-13 text-uppercase text-dark-gray fw-700 mb-0">Your message</label>
                            <div class="position-relative form-group form-textarea mb-0"> 
                                <textarea class="fs-15 ps-0 border-radius-0px border-color-dark-gray bg-transparent form-control" name="comment" placeholder="Describe about your project" rows="3"></textarea>
                                <span class="form-icon"><i class="bi bi-chat-square-dots text-dark-gray"></i></span>
                            </div>
                            <div class="row mt-25px align-items-center">
                                <div class="col-xl-7 col-lg-12 col-sm-7 lg-mb-30px md-mb-0">
                                    <p class="mb-0 fs-14 lh-22 text-center text-sm-start">We will never collect information about you without your explicit consent.</p>
                                </div>
                                <div class="col-xl-5 col-lg-12 col-sm-5 text-center text-sm-end text-lg-start text-xl-end xs-mt-25px">
                                    <input id="exampleInputEmail3" type="hidden" name="redirect" value="">
                                    <button class="btn btn-dark-gray btn-medium btn-round-edge btn-box-shadow submit" type="submit">Send message</button> 
                                </div>
                                <div class="col-12 mt-20px mb-0 text-center text-md-start">
                                    <div class="form-results d-none"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
         
        </section>
        <!-- end section -->
        <!-- start footer -->
  
     <!-- start enhanced footer -->
     <?php include 'footer.php'; ?>  