<?php include 'header.php'; ?>
        <!-- end header -->

<!-- start page title -->
<section class="page-title-parallax-background half-section ipad-top-space-margin" data-parallax-background-ratio="0.5" style="background-image: url('images/parallax-bg.jpg');">
   <div class="professional-gradient-container">
                <div class="corner-gradient top-left"></div>
                <div class="corner-gradient top-right"></div>
                <div class="corner-gradient bottom-left"></div>
                <div class="corner-gradient bottom-right"></div>
                <div class="diagonal-gradient"></div>
                <div class="vignette-overlay"></div>
            </div>
    <div class="container">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-6 col-lg-7 text-center position-relative page-title-extra-large">
                <div class="d-flex flex-column small-screen">
                   <div class="mt-auto">
                        <h1 class="text-white fw-500 ls-minus-1px mb-0">Profitability Boost Calculator</h1>
                    </div>
                    <div class="mt-auto justify-content-center breadcrumb breadcrumb-style-01 fs-15 text-white">
                        <ul>
                            <li><a href="#" class="text-white">Home</a></li>
                            <li><a href="#" class="text-white">Resources</a></li>
                            <li><span class="opacity-7">Profitability Boost Calculator</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end page title -->

<!-- start Profitability Boost Calculator section -->
<section class="profitability-calculator-section position-relative overflow-hidden py-100px">
    <!-- Background elements -->
 

 

    <div class="container position-relative">
        <div class="row justify-content-center mb-50px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <h2 class="alt-font fw-700 ls-minus-1px mb-20px text-dark-gray">Estimate Your Ad Spend Profitability Boost</h2>
                <p class="mb-0">Still focusing just on ROAS? See how optimizing for Customer Lifetime Value with Adzeta's Predictive AI can significantly increase the actual profit your ad spend generates.</p>
                <button class="btn btn-link-gradient btn-extra-large text-gradient-light-pink-light-purple thin d-table d-lg-inline-block xl-mb-15px md-mx-auto mt-20px" id="howToUseBtn">
                    <i class="bi bi-info-circle me-5px"></i>How to use this calculator
                </button>
            </div>
        </div>

        <!-- How to Use Modal -->
        <div class="modal fade" id="howToUseModal" tabindex="-1" aria-labelledby="howToUseModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content border-radius-6px">
                    <div class="modal-header">
                        <h5 class="modal-title" id="howToUseModalLabel">How to Use the Profitability Boost Calculator</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-30px">
                        <h6 class="fw-600 mb-15px text-dark-gray">What is Ad Spend Profitability Optimization?</h6>
                        <p>Ad Spend Profitability Optimization is the practice of focusing your advertising budget on acquiring customers with higher lifetime value (LTV) rather than just optimizing for immediate ROAS. By identifying and targeting customer segments with higher long-term value, businesses can improve the overall profitability of their advertising investments.</p>

                        <h6 class="fw-600 mb-15px mt-30px">How to Use This Calculator</h6>
                        <ol class="mb-30px ps-3">
                            <li class="mb-10px"><span class="fw-600">Enter your Current Monthly Ad Spend</span> - The total amount you spend on advertising each month.</li>
                            <li class="mb-10px"><span class="fw-600">Enter your Current Average ROAS</span> - Your Return On Ad Spend as a multiplier. Calculate this by dividing your total revenue from ads by your total ad spend. For example, if you spend $1,000 on ads and generate $3,000 in revenue, your ROAS is 3.</li>
                            <li class="mb-10px"><span class="fw-600">Enter your Current Profit Margin</span> - The percentage of revenue that becomes profit after accounting for costs.</li>
                            <li class="mb-10px"><span class="fw-600">Select an Optimization Level</span> - Choose between conservative, moderate, or aggressive improvement scenarios based on your business goals.</li>
                            <li class="mb-10px"><span class="fw-600">Select an Implementation Timeframe</span> - Results typically improve over time as optimization strategies are refined.</li>
                            <li class="mb-10px"><span class="fw-600">Adjust the Optimizable Portion</span> - Not all ad spend can be immediately optimized; this represents a realistic portion that can be effectively improved.</li>
                        </ol>

                        <h6 class="fw-600 mb-15px text-dark-gray">Why This Matters</h6>
                        <div class="row">
                            <div class="col-md-6 mb-20px">
                                <div class="p-20px border-radius-6px bg-very-light-gray h-100">
                                    <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-graph-up text-gradient-primary me-10px"></i>Beyond ROAS</h6>
                                    <p class="mb-0 fs-14">ROAS only tells part of the story. Optimizing for profit means focusing on customers who drive the most value over time, not just immediate revenue.</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-20px">
                                <div class="p-20px border-radius-6px bg-very-light-gray h-100">
                                    <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-bullseye text-gradient-primary me-10px"></i>Reduce Wasted Spend</h6>
                                    <p class="mb-0 fs-14">Identify and reduce spending on customer segments that have low lifetime value, reallocating budget to more profitable segments.</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-20px">
                                <div class="p-20px border-radius-6px bg-very-light-gray h-100">
                                    <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-cash-stack text-gradient-primary me-10px"></i>Sustainable Growth</h6>
                                    <p class="mb-0 fs-14">LTV-focused acquisition creates a more sustainable business model with higher customer equity and better long-term economics.</p>
                                </div>
                            </div>
                            <div class="col-md-6 mb-20px">
                                <div class="p-20px border-radius-6px bg-very-light-gray h-100">
                                    <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-people text-gradient-primary me-10px"></i>Better Customer Quality</h6>
                                    <p class="mb-0 fs-14">Attract customers who spend more, stay longer, and have higher lifetime value, creating a stronger customer base.</p>
                                </div>
                            </div>
                        </div>

                        <h6 class="fw-600 mb-15px mt-20px text-dark-gray">How Our Calculation Model Works</h6>
                        <p>This calculator uses a realistic model based on actual client results and industry benchmarks:</p>
                        <ul class="ps-3 mb-15px">
                            <li class="mb-5px"><span class="fw-600">Gradual Implementation:</span> Results improve over time as optimization strategies are refined and more data is collected.</li>
                            <li class="mb-5px"><span class="fw-600">Partial Optimization:</span> Not all ad spend can be immediately optimized; we account for a realistic portion that can be effectively improved.</li>
                            <li class="mb-5px"><span class="fw-600">Conservative Estimates:</span> Our default settings use conservative improvement factors based on actual client results.</li>
                            <li class="mb-5px"><span class="fw-600">Transparent Calculation:</span> You can view the exact formula and steps used by clicking "Show Calculation Details" in the results section.</li>
                        </ul>

                        <div class="p-15px border-radius-6px bg-very-light-gray mb-20px">
                            <h6 class="fw-600 fs-15 mb-10px"><i class="bi bi-info-circle text-gradient-primary me-10px"></i>Real-World Implementation</h6>
                            <p class="mb-0 fs-14">In practice, LTV optimization involves several steps: data collection and analysis, customer segmentation, predictive modeling, signal delivery to ad platforms, and continuous testing and refinement. Results typically improve over time as these processes are optimized.</p>
                        </div>

                        <p class="mb-0 mt-15px"><span class="fw-600">Note:</span> This calculator provides estimates based on aggregated client data. For a personalized analysis specific to your business, <a href="/free-audit" class="text-decoration-underline">request a free audit</a>.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-dark-gray" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-xl-10">
                <div class="profitability-calculator-container box-shadow-extra-large border-radius-24px overflow-hidden">
                    <div class="row g-0">
                        <!-- Calculator Inputs -->
                        <div class="col-lg-6 calculator-inputs-container">
                            <div class="p-40px md-p-30px sm-p-20px">
                                <h3 class="alt-font fw-700 fs-22 mb-25px text-dark">Enter Your Data</h3>

                                <form id="profitabilityCalculatorForm" class="calculator-form">
                                    <!-- Input 1: Current Monthly Ad Spend -->
                                    <div class="position-relative form-group mb-25px">
                                        <label for="adSpend" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Current Monthly Ad Spend ($)
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="The total amount you spend on advertising each month across all platforms.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-transparent border-end-0">$</span>
                                            <input type="number" id="adSpend" class="form-control border-start-0 border-radius-right-4px" placeholder="0.00" min="0" step="100" value="10000">
                                        </div>
                                    </div>

                                    <!-- Input 2: Current Average ROAS -->
                                    <div class="position-relative form-group mb-25px">
                                        <label for="currentRoas" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Current Average ROAS
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="Return On Ad Spend - the revenue generated for each dollar spent on ads. Enter as a multiplier: if you get $300 in revenue from $100 in ad spend, your ROAS is 3.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <div class="input-group">
                                            <input type="number" id="currentRoas" class="form-control border-radius-4px" placeholder="Enter as multiplier (e.g., 3)" min="0" step="0.1" value="3">
                                            <span class="input-group-text bg-transparent border-start-0">x</span>
                                        </div>
                                        <div class="fs-12 text-muted mt-5px">
                                            Example: If $100 in ad spend generates $300 in revenue, enter 3
                                        </div>
                                    </div>

                                    <!-- Input 3: Current Profit Margin -->
                                    <div class="position-relative form-group mb-25px">
                                        <label for="profitMargin" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Current Profit Margin
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="Your average profit margin as a percentage of revenue. This is the percentage of revenue that becomes profit after accounting for all costs.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <div class="range-slider-container">
                                            <input type="range" id="profitMarginSlider" class="form-range" min="0" max="100" step="1" value="30">
                                            <div class="input-group" style="width: 170px;">
                                                <input type="number" id="profitMargin" class="form-control border-radius-left-4px" min="0" max="100" value="30">
                                                <span class="input-group-text bg-transparent border-start-0">%</span>
                                            </div>
                                        </div>
                                        <div class="range-labels d-flex justify-content-between mt-5px">
                                            <span class="range-min fs-12 opacity-6">0%</span>
                                            <span class="range-max fs-12 opacity-6">100%</span>
                                        </div>
                                    </div>

                                    <!-- Input 4: Optimization Level (Optional) -->
                                    <div class="position-relative form-group mb-25px">
                                        <label for="optimizationLevel" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Optimization Level
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="Select the level of optimization you want to model. Conservative is based on lower-end results from actual client data, while Aggressive represents upper-end results achieved after full implementation.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <select id="optimizationLevel" class="form-select border-radius-4px">
                                            <option value="conservative" selected>Conservative (15-20% Improvement)</option>
                                            <option value="moderate">Moderate (20-30% Improvement)</option>
                                            <option value="aggressive">Aggressive (30-40% Improvement)</option>
                                        </select>
                                    </div>

                                    <!-- Input 5: Implementation Timeframe -->
                                    <div class="position-relative form-group mb-25px">
                                        <label for="timeframe" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Implementation Timeframe
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="Results from optimization are typically realized over time as the system learns and improves. Select a timeframe to see projected results.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <select id="timeframe" class="form-select border-radius-4px">
                                            <option value="3">3 Months (Initial Results)</option>
                                            <option value="6" selected>6 Months (Established Optimization)</option>
                                            <option value="12">12 Months (Full Implementation)</option>
                                        </select>
                                    </div>

                                    <!-- Input 6: Portion of Ad Spend Optimizable -->
                                    <div class="position-relative form-group mb-25px">
                                        <label for="optimizablePortion" class="form-label fw-600 text-dark mb-10px d-flex align-items-center">
                                            Portion of Ad Spend Optimizable
                                            <span class="tooltip-icon ms-10px" data-bs-toggle="tooltip" data-bs-placement="top" title="Not all ad spend can be immediately optimized. This represents the percentage of your ad spend that can be effectively optimized using LTV-based strategies.">
                                                <i class="bi bi-question-circle fs-14 opacity-6"></i>
                                            </span>
                                        </label>
                                        <div class="range-slider-container">
                                            <input type="range" id="optimizablePortionSlider" class="form-range" min="40" max="90" step="5" value="70">
                                            <div class="input-group" style="width: 180px;">
                                                <input type="number" id="optimizablePortion" class="form-control border-radius-left-4px" min="40" max="90" value="70">
                                                <span class="input-group-text bg-transparent border-start-0">%</span>
                                            </div>
                                        </div>
                                        <div class="range-labels d-flex justify-content-between mt-5px">
                                            <span class="range-min fs-12 opacity-6">40%</span>
                                            <span class="range-max fs-12 opacity-6">90%</span>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Calculator Results -->
                        <div class="col-lg-6 calculator-results-container custom-gradient-bg">
                            <div class="p-40px md-p-30px sm-p-20px h-100 d-flex flex-column">
                                <h3 class="alt-font fw-700 fs-22 mb-25px text-white">Your Results</h3>

                                <div class="results-content flex-grow-1 d-flex flex-column justify-content-center mt-10px">
                                    <!-- Result 1: Current Monthly Profit -->
                                    <div class="result-item mb-20px">
                                        <div class="result-label text-white opacity-9 mb-5px fw-500">Current Estimated Monthly Profit from Ads:</div>
                                        <div class="result-value d-flex align-items-start">
                                            <span class="currency text-white fs-24 fw-600 me-2" style="margin-top: 8px;">$</span>
                                            <span id="currentProfit" class="value fs-40 fw-700 text-white" data-anime='{ "el": "self", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 300 }'>9,000</span>
                                        </div>
                                    </div>

                                    <!-- Result 2: Potential Monthly Profit -->
                                    <div class="result-item mb-20px">
                                        <div class="result-label text-white opacity-9 mb-5px fw-500">Projected Monthly Profit After <span id="timeframeDisplay">6</span> Months:</div>
                                        <div class="result-value d-flex align-items-start">
                                            <span class="currency text-white fs-24 fw-600 me-2" style="margin-top: 8px;">$</span>
                                            <span id="potentialProfit" class="value fs-40 fw-700 text-white" data-anime='{ "el": "self", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 400 }'>10,350</span>
                                        </div>
                                    </div>

                                    <!-- Result 3: Monthly Profit Uplift -->
                                    <div class="result-item mb-20px">
                                        <div class="result-label text-white opacity-9 mb-5px fw-500">Projected Monthly Profit Increase:</div>
                                        <div class="result-value d-flex align-items-start">
                                            <span class="currency text-white fs-24 fw-600 me-2" style="margin-top: 8px;">$</span>
                                            <span id="profitUplift" class="value fs-40 fw-700 text-white" data-anime='{ "el": "self", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 500 }'>1,350</span>
                                            <span id="profitUpliftPercentage" class="fs-20 fw-500 text-white ms-10px" style="margin-top: 12px;">(+15%)</span>
                                        </div>
                                    </div>

                                    <!-- Result 4: Annual Impact -->
                                    <div class="result-item mb-20px">
                                        <div class="result-label text-white opacity-9 mb-5px fw-500">Projected Annual Profit Increase:</div>
                                        <div class="result-value d-flex align-items-start">
                                            <span class="currency text-white fs-24 fw-600 me-2" style="margin-top: 8px;">$</span>
                                            <span id="annualUplift" class="value fs-40 fw-700 text-white" data-anime='{ "el": "self", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 600 }'>16,200</span>
                                        </div>
                                    </div>

                                    <!-- Insight Message -->
                                    <div id="insightMessageContainer" class="insight-message p-20px border-radius-6px mb-20px level-good">
                                        <div class="insight-icon"><i class="bi bi-graph-up-arrow"></i></div>
                                        <p class="text-white mb-0 fw-500 line-height-1-5 opacity-95" id="insightMessage">Based on conservative estimates and actual client results, optimizing your ad spend for high-LTV customers could increase your monthly profit by $1,350 after 6 months of implementation.</p>
                                    </div>

                                    <!-- Calculation Transparency Button -->
                                    <button id="showCalculationBtn" class="btn btn-outline-light btn-small d-block mb-20px">
                                        <i class="bi bi-calculator me-5px"></i> Show Calculation Details
                                    </button>

                                    <!-- Calculation Details (Hidden by Default) -->
                                    <div id="calculationDetails" class="calculation-details p-15px border-radius-6px bg-white bg-opacity-10 mb-20px" style="display: none;">
                                        <h6 class="fs-14 fw-600 text-white mb-10px">How We Calculate This:</h6>
                                        <ol class="ps-3 mb-0 fs-13 text-white opacity-9">
                                            <li class="mb-5px">Current Revenue = Ad Spend × ROAS = <span id="calcCurrentRevenue">$30,000</span></li>
                                            <li class="mb-5px">Current Profit = Current Revenue × Profit Margin = <span id="calcCurrentProfit">$9,000</span></li>
                                            <li class="mb-5px">Optimizable Ad Spend = Ad Spend × Optimizable Portion = <span id="calcOptimizableSpend">$7,000</span></li>
                                            <li class="mb-5px">Optimization Factor = <span id="calcOptimizationFactor">15%</span> (based on selected level)</li>
                                            <li class="mb-5px">Implementation Factor = <span id="calcImplementationFactor">85%</span> (based on timeframe)</li>
                                            <li class="mb-5px">Profit Increase = Current Profit × Optimization Factor × Implementation Factor × Optimizable Portion = <span id="calcProfitIncrease">$1,350</span></li>
                                            <li class="mb-5px">Projected Profit = Current Profit + Profit Increase = <span id="calcProjectedProfit">$10,350</span></li>
                                        </ol>
                                        <p class="fs-12 text-white opacity-8 mt-10px mb-0">Note: This model is based on aggregated data from actual client results and accounts for realistic implementation timelines.</p>
                                    </div>

                                    <!-- Disclaimer -->
                                    <div class="disclaimer fs-12 text-white opacity-7 mb-20px">
                                        <p class="mb-0">* Results vary based on industry, current optimization level, and implementation quality. This calculator provides estimates based on typical client outcomes after proper implementation.</p>
                                    </div>
                                </div>

                                <!-- CTA Button -->
                                <div class="cta-container">
                                    <a href="free-ad-audit.php" class="btn btn-large btn-white btn-box-shadow btn-round-edge d-block">
                                        <span>See My Profit Potential →</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end Profitability Boost Calculator section -->

<!-- Profitability Boost Calculator CSS -->
<style>
    /* Calculator Container Styles */
    .profitability-calculator-container {
        background: #ffffff;
        overflow: hidden;
        position: relative;
        border-radius: 24px !important;
    }

    /* Input Styles */
    .calculator-inputs-container {
        position: relative;
        z-index: 1;
    }

    .calculator-inputs-container::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        width: 40px;
        background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
        z-index: 2;
    }

    .calculator-form .form-control {
        height: 50px;
        border-color: rgba(0, 0, 0, 0.1);
        background-color: rgba(248, 249, 250, 0.5);
        transition: all 0.3s ease;
    }

    .calculator-form .form-control:focus {
        border-color: #8f76f5;
        box-shadow: 0 0 10px rgba(143, 118, 245, 0.2);
    }

    .calculator-form .input-group-text {
        border-color: rgba(0, 0, 0, 0.1);
        color: #666;
    }

    /* Range Slider Styles */
    .range-slider-container {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .range-slider-container .form-range {
        flex-grow: 1;
    }

    .range-slider-container input[type="number"] {
        width: 60px;
        text-align: center;
        -webkit-appearance: textfield;
        -moz-appearance: textfield;
        appearance: textfield; /* Remove spinner for Firefox */
    }

    /* Remove spinner for Chrome, Safari, Edge, Opera */
    .range-slider-container input[type="number"]::-webkit-outer-spin-button,
    .range-slider-container input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        appearance: none;
        margin: 0;
    }

    .form-range {
        height: 8px;
        background: rgba(143, 118, 245, 0.2);
        border-radius: 4px;
        -webkit-appearance: none;
        appearance: none;
    }

    .form-range::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
        cursor: pointer;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
        margin-top: -7px;
    }

    .form-range::-moz-range-thumb {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
        cursor: pointer;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
        border: none;
    }

    /* Results Container Styles */
    .calculator-results-container {
        position: relative;
        overflow: hidden;
    }

    .custom-gradient-bg {
        background: linear-gradient(135deg, #1B0B24 0%, #2b1a3a 50%, #3a1a3a 100%);
    }

    .calculator-results-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url('images/mesh-overlay.svg');
        background-size: cover;
        opacity: 0.1;
        z-index: 0;
        pointer-events: none;
    }

    .results-content {
        position: relative;
        z-index: 1;
    }

    /* CTA Button Styles */
    .cta-container {
        position: relative;
        z-index: 100 !important;
        pointer-events: auto !important;
    }

    .cta-container .btn {
        cursor: pointer !important;
        position: relative;
        z-index: 100 !important;
        transition: all 0.3s ease;
        pointer-events: auto !important;
        display: block !important;
    }

    .cta-container .btn:hover {
        cursor: pointer !important;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .cta-container .btn span {
        pointer-events: none;
    }

    .result-value {
        position: relative;
    }

    .result-value .currency {
        line-height: 1;
        opacity: 0.9;
    }

    .result-value .value {
        background: linear-gradient(to right, #ffffff, #ff8cc6);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent !important;
        display: inline-block;
        line-height: 1;
    }

    /* Insight Message Styles */
    .insight-message {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        position: relative;
        transition: all 0.3s ease;
    }

    .insight-icon {
        font-size: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 30px;
    }

    #insightMessage {
        font-weight: 500;
        line-height: 1.5;
        opacity: 0.95;
    }

    /* Color-coded levels */
    .level-excellent {
        background: rgba(52, 191, 163, 0.2);
        border-left: 4px solid #34BFA3;
    }

    .level-excellent .insight-icon {
        color: #34BFA3;
    }

    .level-good {
        background: rgba(88, 103, 221, 0.2);
        border-left: 4px solid #5867DD;
    }

    .level-good .insight-icon {
        color: #5867DD;
    }

    .level-average {
        background: rgba(255, 184, 34, 0.2);
        border-left: 4px solid #FFB822;
    }

    .level-average .insight-icon {
        color: #FFB822;
    }

    .level-low {
        background: rgba(244, 88, 136, 0.2);
        border-left: 4px solid #F45888;
    }

    .level-low .insight-icon {
        color: #F45888;
    }

    /* Animated Particles */
    .particle-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
        pointer-events: none;
    }

    .particle {
        position: absolute;
        border-radius: 50%;
        opacity: 0.3;
        background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
        animation: float 15s infinite ease-in-out;
        pointer-events: none;
    }

    .particle-1 {
        width: 100px;
        height: 100px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
    }

    .particle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        left: 5%;
        animation-delay: -5s;
    }

    .particle-3 {
        width: 80px;
        height: 80px;
        top: 30%;
        right: 15%;
        animation-delay: -2s;
    }

    .particle-4 {
        width: 120px;
        height: 120px;
        bottom: 10%;
        right: 20%;
        animation-delay: -7s;
    }

    @keyframes float {
        0%, 100% {
            transform: translate(0, 0) scale(1);
        }
        25% {
            transform: translate(10px, 15px) scale(1.05);
        }
        50% {
            transform: translate(5px, -10px) scale(0.95);
        }
        75% {
            transform: translate(-10px, 5px) scale(1.02);
        }
    }
</style>

<!-- Profitability Boost Calculator JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get all the input elements
        const adSpendInput = document.getElementById('adSpend');
        const currentRoasInput = document.getElementById('currentRoas');
        const profitMarginInput = document.getElementById('profitMargin');
        const profitMarginSlider = document.getElementById('profitMarginSlider');
        const optimizationLevelSelect = document.getElementById('optimizationLevel');
        const timeframeSelect = document.getElementById('timeframe');
        const optimizablePortionInput = document.getElementById('optimizablePortion');
        const optimizablePortionSlider = document.getElementById('optimizablePortionSlider');

        // Get the result elements
        const currentProfitElement = document.getElementById('currentProfit');
        const potentialProfitElement = document.getElementById('potentialProfit');
        const profitUpliftElement = document.getElementById('profitUplift');
        const profitUpliftPercentageElement = document.getElementById('profitUpliftPercentage');
        const annualUpliftElement = document.getElementById('annualUplift');
        const timeframeDisplayElement = document.getElementById('timeframeDisplay');
        const insightMessageElement = document.getElementById('insightMessage');
        const insightMessageContainer = document.getElementById('insightMessageContainer');

        // Get calculation detail elements
        const calcCurrentRevenueElement = document.getElementById('calcCurrentRevenue');
        const calcCurrentProfitElement = document.getElementById('calcCurrentProfit');
        const calcOptimizableSpendElement = document.getElementById('calcOptimizableSpend');
        const calcOptimizationFactorElement = document.getElementById('calcOptimizationFactor');
        const calcImplementationFactorElement = document.getElementById('calcImplementationFactor');
        const calcProfitIncreaseElement = document.getElementById('calcProfitIncrease');
        const calcProjectedProfitElement = document.getElementById('calcProjectedProfit');

        // Optimization level parameters - more conservative and realistic
        const optimizationLevels = {
            conservative: { factor: 0.15 }, // 15% improvement
            moderate: { factor: 0.25 },     // 25% improvement
            aggressive: { factor: 0.35 }    // 35% improvement
        };

        // Implementation timeframe factors
        const implementationFactors = {
            '3': 0.6,  // 60% of potential realized after 3 months
            '6': 0.85, // 85% of potential realized after 6 months
            '12': 1.0  // 100% of potential realized after 12 months
        };

        // Sync the number input and range slider for profit margin
        profitMarginSlider.addEventListener('input', function() {
            profitMarginInput.value = this.value;
            calculateProfitability();
        });

        profitMarginInput.addEventListener('input', function() {
            // Ensure the value is within the allowed range
            if (this.value > 100) this.value = 100;
            if (this.value < 0) this.value = 0;

            profitMarginSlider.value = this.value;
            calculateProfitability();
        });

        // Sync the number input and range slider for optimizable portion
        optimizablePortionSlider.addEventListener('input', function() {
            optimizablePortionInput.value = this.value;
            calculateProfitability();
        });

        optimizablePortionInput.addEventListener('input', function() {
            // Ensure the value is within the allowed range
            if (this.value > 90) this.value = 90;
            if (this.value < 40) this.value = 40;

            optimizablePortionSlider.value = this.value;
            calculateProfitability();
        });

        // Add event listeners to all inputs
        adSpendInput.addEventListener('input', calculateProfitability);
        currentRoasInput.addEventListener('input', calculateProfitability);
        optimizationLevelSelect.addEventListener('change', calculateProfitability);
        timeframeSelect.addEventListener('change', calculateProfitability);

        // Toggle calculation details visibility
        const showCalculationBtn = document.getElementById('showCalculationBtn');
        const calculationDetails = document.getElementById('calculationDetails');

        showCalculationBtn.addEventListener('click', function() {
            if (calculationDetails.style.display === 'none') {
                calculationDetails.style.display = 'block';
                showCalculationBtn.innerHTML = '<i class="bi bi-x-circle me-5px"></i> Hide Calculation Details';
            } else {
                calculationDetails.style.display = 'none';
                showCalculationBtn.innerHTML = '<i class="bi bi-calculator me-5px"></i> Show Calculation Details';
            }
        });

        // Function to calculate profitability with a more realistic model
        function calculateProfitability() {
            // Get the values from inputs
            const adSpend = parseFloat(adSpendInput.value) || 0;
            const currentRoas = parseFloat(currentRoasInput.value) || 0;
            const profitMargin = parseFloat(profitMarginInput.value) || 0;
            const optimizationLevel = optimizationLevelSelect.value;
            const timeframe = timeframeSelect.value;
            const optimizablePortion = parseFloat(optimizablePortionInput.value) / 100 || 0.7; // Convert to decimal

            // Update timeframe display
            timeframeDisplayElement.textContent = timeframe;

            // Get optimization parameters
            const optimizationFactor = optimizationLevels[optimizationLevel].factor;
            const implementationFactor = implementationFactors[timeframe];

            // Calculate current scenario
            const currentRevenue = adSpend * currentRoas;
            const currentProfit = currentRevenue * (profitMargin / 100);

            // Calculate optimizable ad spend
            const optimizableAdSpend = adSpend * optimizablePortion;

            // Calculate profit increase using a more realistic model
            // Only apply optimization to the portion of ad spend that can be optimized
            // and factor in the implementation timeline
            const profitIncrease = currentProfit * optimizationFactor * implementationFactor * optimizablePortion;

            // Calculate projected profit
            const projectedProfit = currentProfit + profitIncrease;

            // Calculate annual impact
            const annualProfitIncrease = profitIncrease * 12;

            // Calculate profit uplift percentage
            const profitUpliftPercentage = currentProfit > 0 ? (profitIncrease / currentProfit) * 100 : 0;

            // Update the calculation details
            calcCurrentRevenueElement.textContent = '$' + formatNumber(currentRevenue);
            calcCurrentProfitElement.textContent = '$' + formatNumber(currentProfit);
            calcOptimizableSpendElement.textContent = '$' + formatNumber(optimizableAdSpend);
            calcOptimizationFactorElement.textContent = (optimizationFactor * 100).toFixed(0) + '%';
            calcImplementationFactorElement.textContent = (implementationFactor * 100).toFixed(0) + '%';
            calcProfitIncreaseElement.textContent = '$' + formatNumber(profitIncrease);
            calcProjectedProfitElement.textContent = '$' + formatNumber(projectedProfit);

            // Update the result elements with animation
            animateValue(currentProfitElement, currentProfit);
            animateValue(potentialProfitElement, projectedProfit);
            animateValue(profitUpliftElement, profitIncrease);
            animateValue(annualUpliftElement, annualProfitIncrease);

            // Update profit uplift percentage
            profitUpliftPercentageElement.textContent = `(+${profitUpliftPercentage.toFixed(0)}%)`;

            // Update insight message based on results
            updateInsightMessage(currentProfit, projectedProfit, profitIncrease, profitUpliftPercentage, optimizationLevel, timeframe);
        }

        // Function to animate value changes
        function animateValue(element, newValue) {
            const startValue = parseFloat(element.textContent.replace(/,/g, '')) || 0;
            const duration = 500; // Animation duration in milliseconds
            const startTime = performance.now();

            function updateValue(currentTime) {
                const elapsedTime = currentTime - startTime;
                const progress = Math.min(elapsedTime / duration, 1);
                const currentValue = startValue + (newValue - startValue) * progress;

                // Format the value with commas for thousands
                element.textContent = formatNumber(currentValue);

                if (progress < 1) {
                    requestAnimationFrame(updateValue);
                }
            }

            requestAnimationFrame(updateValue);
        }

        // Function to format numbers with commas
        function formatNumber(number) {
            return number.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,').replace(/\.00$/, '');
        }

        // Function to update the insight message with color coding
        function updateInsightMessage(currentProfit, projectedProfit, profitIncrease, profitUpliftPercentage, optimizationLevel, timeframe) {
            let message = '';
            let insightLevel = '';
            let insightIcon = '';

            // Determine insight level and message based on profit uplift percentage
            if (profitUpliftPercentage >= 30) {
                insightLevel = 'level-excellent';
                insightIcon = '<i class="bi bi-trophy"></i>';
                message = `Based on ${optimizationLevel} estimates and actual client results, optimizing your ad spend for high-LTV customers could increase your monthly profit by $${formatNumber(profitIncrease)} after ${timeframe} months of implementation.`;
            } else if (profitUpliftPercentage >= 20) {
                insightLevel = 'level-good';
                insightIcon = '<i class="bi bi-graph-up-arrow"></i>';
                message = `Based on ${optimizationLevel} estimates and actual client results, optimizing your ad spend for high-LTV customers could increase your monthly profit by $${formatNumber(profitIncrease)} after ${timeframe} months of implementation.`;
            } else if (profitUpliftPercentage >= 10) {
                insightLevel = 'level-average';
                insightIcon = '<i class="bi bi-lightbulb"></i>';
                message = `Based on ${optimizationLevel} estimates, optimizing your ad spend for high-LTV customers could increase your monthly profit by $${formatNumber(profitIncrease)} after ${timeframe} months of implementation.`;
            } else {
                insightLevel = 'level-low';
                insightIcon = '<i class="bi bi-arrow-up-circle"></i>';
                message = `Even with ${optimizationLevel} estimates, focusing on high-LTV customers could increase your monthly profit by $${formatNumber(profitIncrease)} after ${timeframe} months of implementation.`;
            }

            // Add context based on inputs
            if (parseFloat(currentRoasInput.value) < 2) {
                message += ' Your current ROAS is on the lower side, which means there may be significant room for improvement.';
            }

            if (parseFloat(optimizablePortionInput.value) < 60) {
                message += ' Consider increasing the portion of ad spend that can be optimized for even better results.';
            }

            if (timeframe === '3') {
                message += ' For more significant results, consider a longer implementation timeframe as optimization improves over time.';
            }

            if (currentProfit > 50000) {
                message += ' With your scale, even these modest percentage improvements translate to significant absolute profit increases.';
            }

            // Update the message and styling
            insightMessageElement.textContent = message;
            insightMessageContainer.className = `insight-message p-20px border-radius-6px mb-20px ${insightLevel}`;
            insightMessageContainer.querySelector('.insight-icon').innerHTML = insightIcon;
        }

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize How to Use modal
        const howToUseBtn = document.getElementById('howToUseBtn');
        howToUseBtn.addEventListener('click', function() {
            const howToUseModal = new bootstrap.Modal(document.getElementById('howToUseModal'));
            howToUseModal.show();
        });

        // Calculate initial values
        calculateProfitability();
    });
</script>

    <!-- start enhanced footer -->
 <?php include 'footer.php'; ?>     