/* Homepage Standard vs Value-Based Bidding Starts */
.comparison-section-v2{
    background:linear-gradient(to bottom,#fff 0,#f2f0ee 100%);
    overflow-x:hidden;
    position:relative;
    border-bottom:none;
    width:100%
}
.comparison-section-v2 .container{
    position:relative;
    z-index:2;
    padding-top:30px;
    padding-bottom:50px
}
.comparison-header{
    text-align:center;
    margin-bottom:40px
}
.sub-tag{
    display:inline-block;
    padding:5px 5px;
    background:linear-gradient(90deg,rgba(158,114,186,.08) 0,rgba(74,158,255,.08) 100%);
    color:#8f76f5;
    font-size:12px;
    text-align:center;
    position:relative;
    display:block;
    font-weight:500;
    letter-spacing:1.2px;
    text-transform:uppercase;
    border-radius:30px;
    margin-bottom:20px
}
.comparison-toggle-mobile{
    display:flex;
    justify-content:center;
    margin-bottom:30px
}
.modern-toggle-slider{
    position:relative;
    display:flex;
    width:85%;
    max-width:280px;
    height:46px;
    background-color:#f8f9fa;
    border-radius:23px;
    box-shadow:0 2px 10px rgba(0,0,0,.05);
    margin:0 auto;
    overflow:hidden;
    border:1px solid rgba(240,240,245,.5);
    padding:3px;
    z-index:1
}
.toggle-option{
    flex:1;
    display:flex;
    flex-direction:row;
    align-items:center;
    justify-content:center;
    padding:0 15px;
    cursor:pointer;
    position:relative;
    z-index:2;
    transition:color .3s ease;
    color:#777;
    font-size:14px;
    font-weight:500;
    border-radius:20px;
    user-select:none
}
.toggle-option i{
    font-size:14px;
    margin-right:6px
}
.toggle-option.active{
    color:#fff;
    font-weight:600
}
.slider-indicator{
    position:absolute;
    top:3px;
    left:3px;
    width:calc(50% - 3px);
    height:calc(100% - 6px);
    background:linear-gradient(135deg,#e958a1,#8f76f5);
    border-radius:20px;
    transition:all .4s cubic-bezier(.25,1,.5,1);
    z-index:1;
    box-shadow:0 2px 8px rgba(143,118,245,.25);
    will-change:left,right;
    pointer-events:none
}
.toggle-option:not(.active):hover{
    color:#444
}
@keyframes indicatorPulse{
    0%{
        box-shadow:0 2px 8px rgba(143,118,245,.25)
    }
    50%{
        box-shadow:0 2px 12px rgba(143,118,245,.35)
    }
    100%{
        box-shadow:0 2px 8px rgba(143,118,245,.25)
    }
}
.slider-indicator{
    animation:indicatorPulse 2s infinite
}
@media (max-width:360px){
    .modern-toggle-slider{
        width:95%;
        height:42px
    }
    .toggle-option{
        font-size:13px;
        padding:0 10px
    }
    .toggle-option i{
        font-size:12px;
        margin-right:4px
    }
}
.comparison-content-area{
    margin-top:30px
}
.mobile-comparison-content{
    margin-bottom:30px;
    padding:0 15px;
    max-width:100%;
    overflow-x:hidden
}
.tab-pane{
    opacity:0;
    transition:opacity .4s ease,transform .4s ease;
    display:none;
    transform:translateY(15px);
    pointer-events:none
}
.tab-pane.show{
    opacity:1;
    display:block;
    transform:translateY(0);
    pointer-events:auto
}
.tab-pane .comparison-card{
    transition:all .5s ease
}
.mobile-comparison-content .comparison-card{
    margin-top:15px;
    padding:30px 25px;
    border-radius:24px;
    border:none;
    background-color:#fff;
    position:relative;
    overflow:hidden
}
.mobile-comparison-content .comparison-card::before{
    content:'';
    position:absolute;
    top:0;
    right:0;
    width:100%;
    height:100%;
    background-image:radial-gradient(circle at top right,rgba(233,88,161,.03) 0,rgba(143,118,245,.02) 50%,transparent 70%);
    z-index:0
}
.mobile-comparison-content .comparison-card h3{
    font-size:24px;
    font-weight:700;
    margin-bottom:5px;
    text-align:center;
    position:relative;
    z-index:1
}
.mobile-comparison-content .comparison-card ul{
    position:relative;
    z-index:1
}
.mobile-comparison-content .comparison-card li{
    padding:12px 0;
    font-size:15px;
    display:flex;
    align-items:flex-start;
    border-bottom:1px solid rgba(240,240,245,.6)
}
.mobile-comparison-content .comparison-card li:last-child{
    border-bottom:none
}
.mobile-comparison-content .comparison-card .icon-wrapper{
    width:24px;
    height:24px;
    border-radius:50%;
    display:flex;
    align-items:center;
    justify-content:center;
    margin-right:12px;
    flex-shrink:0
}
.mobile-comparison-content .comparison-card.traditional .icon-wrapper{
    background-color:#ff7878
}
.mobile-comparison-content .comparison-card.AdZeta .icon-wrapper{
    background:linear-gradient(135deg,#e958a1,#8f76f5)
}
.mobile-comparison-content .comparison-card.recommended{
    position:relative
}
.desktop-comparison-content.row{
    margin-left:-15px;
    margin-right:-15px
}
.comparison-card{
    background-color:#fff;
    border-radius:32px;
    padding:40px 30px;
    transition:all .4s ease;
    height:100%;
    position:relative;
    overflow:hidden;
    margin-bottom:20px
}
.comparison-card.AdZeta{
    background-color:#fff;
    position:relative;
    z-index:1
}
.comparison-card h3,.comparison-card li,.comparison-card ul{
    position:relative;
    z-index:2
}
.comparison-card:hover{
    transform:translateY(-6px);
    border-color:rgba(240,240,245,.9)
}
.comparison-card.AdZeta:hover{
    border-color:rgba(143,118,245,.25)
}
.desktop-comparison-content .comparison-card.recommended{
    position:relative
}
.comparison-card h3{
    font-size:24px;
    font-weight:600;
    color:#1a1a1a;
    margin-bottom:8px;
    text-align:center;
    letter-spacing:-.3px;
    line-height:1.3
}
.comparison-card .sub-tag{
    display:inline-block;
    text-align:center;
    font-size:10px;
    color:#fff;
    margin:0 auto 35px;
    text-transform:uppercase;
    letter-spacing:.5px;
    font-weight:500;
    background:linear-gradient(135deg,#ff6a8d,#8f76f5);
    padding:3px 10px;
    border-radius:12px;
    position:relative;
    left:50%;
    transform:translateX(-50%);
    line-height:1.4
}
.comparison-card .sub-tag-outline{
    display:inline-block;
    text-align:center;
    font-size:10px;
    color:#777;
    margin:0 auto 35px;
    text-transform:uppercase;
    letter-spacing:.5px;
    font-weight:500;
    background:0 0;
    padding:2px 10px;
    border-radius:12px;
    border:1px solid rgba(120,120,120,.2);
    position:relative;
    left:50%;
    transform:translateX(-50%);
    line-height:1.4
}
.comparison-card ul{
    list-style:none;
    padding:0;
    margin:0
}
.comparison-card li{
    display:flex;
    align-items:flex-start;
    font-size:15px;
    color:#333;
    line-height:1.7;
    padding:15px 0;
    border-bottom:1px solid #f0f0f5;
    letter-spacing:.2px;
    font-weight:400
}
.comparison-card li:last-child{
    border-bottom:none;
    padding-bottom:0
}
.comparison-card li span.icon-wrapper{
    display:flex;
    align-items:center;
    justify-content:center;
    height:22px;
    width:22px;
    border-radius:50%;
    margin-right:14px;
    flex-shrink:0;
    margin-top:4px
}
.comparison-card li span.icon-wrapper i{
    font-size:.85rem;
    color:#fff;
    line-height:1
}
.comparison-card.traditional li span.icon-wrapper{
    background-color:#ff7878;
    box-shadow:0 2px 5px rgba(255,107,107,.3)
}
.comparison-card.AdZeta li span.icon-wrapper{
    background:linear-gradient(135deg,#e958a1,#8f76f5);
    box-shadow:0 2px 5px rgba(143,118,245,.3)
}
@keyframes fadeInItem{
    from{
        opacity:0;
        transform:translateY(8px)
    }
    to{
        opacity:1;
        transform:translateY(0)
    }
}
.mobile-comparison-content .tab-pane.active .comparison-card li{
    animation:fadeInItem .35s ease forwards;
    opacity:0
}
.mobile-comparison-content .tab-pane.active .comparison-card li:nth-child(1){
    animation-delay:50ms
}
.mobile-comparison-content .tab-pane.active .comparison-card li:nth-child(2){
    animation-delay:.1s
}
.mobile-comparison-content .tab-pane.active .comparison-card li:nth-child(3){
    animation-delay:.15s
}
.mobile-comparison-content .tab-pane.active .comparison-card li:nth-child(4){
    animation-delay:.2s
}
@media (min-width:768px){
    .desktop-comparison-content.row{
        gap:0
    }
    .desktop-comparison-content>.col-md-6{
        padding-left:15px;
        padding-right:15px
    }
    .desktop-comparison-content .comparison-card{
        padding:45px;
        opacity:1;
        transform:translateY(0) scale(1);
        background-color:#fff
    }
    .desktop-comparison-content .comparison-card:hover{
        transform:translateY(-6px)
    }
    .desktop-comparison-content .comparison-card.AdZeta.recommended{
        border-color:rgba(143,118,245,.2)
    }
    .desktop-comparison-content .comparison-card.AdZeta.recommended:hover{
        border-color:rgba(143,118,245,.5)
    }
    .desktop-comparison-content .comparison-card li{
        opacity:1
    }
}
@media (min-width:992px){
    .comparison-section-v2{
        padding:20px 0
    }
    .comparison-header{
        margin-bottom:70px
    }
    .desktop-comparison-content>.col-md-6{
        padding-left:20px;
        padding-right:20px
    }
    .desktop-comparison-content .comparison-card{
        padding:50px
    }
}
@media (min-width:1200px){
    .desktop-comparison-content>.col-md-6{
        padding-left:25px;
        padding-right:25px
    }
    .desktop-comparison-content .comparison-card{
        padding:55px
    }
}
/* Homepage Standard vs Value-Based Bidding Ends */