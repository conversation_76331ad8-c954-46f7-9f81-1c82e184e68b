/**
 * Equal Height Feature Boxes
 * This script ensures all feature boxes with the js-equal-height class
 * have the same height based on the tallest box in the group.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Set equal heights for feature boxes after a short delay to ensure all content is rendered
    setTimeout(function() {
        console.log('DOMContentLoaded - checking equal heights');
        setEqualHeights();
    }, 100);

    // Also set equal heights after window load to ensure images are loaded
    window.addEventListener('load', function() {
        console.log('Window load - checking equal heights');
        setEqualHeights();
    });

    // Re-apply on window resize
    window.addEventListener('resize', debounce(function() {
        console.log('Window resize - checking equal heights');
        setEqualHeights();
    }, 250));

    // Re-apply on orientation change for mobile devices
    window.addEventListener('orientationchange', function() {
        setTimeout(function() {
            console.log('Orientation change - checking equal heights');
            setEqualHeights();
        }, 250);
    });
});

/**
 * Check if device should use equal heights
 */
function shouldUseEqualHeights() {
    const windowWidth = window.innerWidth;
    const isMobile = windowWidth <= 768;
    const isTabletPortrait = windowWidth <= 991 && windowWidth > 768;

    // Skip equal heights on mobile and small tablets in portrait
    if (isMobile || isTabletPortrait) {
        return false;
    }

    return true;
}

/**
 * Sets equal heights for elements with js-equal-height class
 * Only applies on desktop/tablet to avoid excessive height on mobile
 */
function setEqualHeights() {
    console.log('Setting equal heights - Window width:', window.innerWidth);

    // Always reset heights first - this is crucial for mobile
    const elements = document.querySelectorAll('.js-equal-height');
    elements.forEach(function(el) {
        el.style.height = 'auto';
    });

    // Also reset all feature boxes to auto height
    const allFeatureBoxes = document.querySelectorAll('.hover-box.feature-box');
    allFeatureBoxes.forEach(function(box) {
        box.style.height = 'auto';
    });

    // Check if we should apply equal heights
    if (!shouldUseEqualHeights()) {
        console.log('Mobile/small tablet detected - keeping natural heights to prevent excessive spacing');

        // Force all feature boxes to auto height on mobile
        allFeatureBoxes.forEach(function(box) {
            box.style.height = 'auto !important';
            box.style.minHeight = 'auto';
        });

        return; // Don't apply equal heights on mobile/small tablets
    }

    console.log('Desktop/large tablet detected - applying equal heights');

    // Process each row separately
    const rows = document.querySelectorAll('.row-equal-height');
    rows.forEach(function(row) {
        // Get all feature boxes in this row
        const boxes = row.querySelectorAll('.hover-box.feature-box');
        if (boxes.length === 0) return;

        let maxHeight = 0;

        // Find the tallest box
        boxes.forEach(function(box) {
            // Force reflow to get accurate height
            box.style.height = 'auto';
            const height = box.scrollHeight;
            console.log('Box height:', height);
            if (height > maxHeight) {
                maxHeight = height;
            }
        });

        // Set all boxes to the max height
        if (maxHeight > 0) {
            console.log('Setting max height for row:', maxHeight);
            boxes.forEach(function(box) {
                box.style.height = maxHeight + 'px';
            });
        }
    });
}

/**
 * Debounce function to limit how often a function is called
 */
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(function() {
            func.apply(context, args);
        }, wait);
    };
}
