<?xml version="1.0" encoding="UTF-8"?>
<svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Profit Growth Potential</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#f45888" offset="0%"></stop>
            <stop stop-color="#ff8cc6" offset="100%"></stop>
        </linearGradient>
        <filter x="-25%" y="-25%" width="150%" height="150%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Subtle Glow -->
        <circle cx="18" cy="18" r="12" fill="url(#linearGradient-1)" opacity="0.1" filter="url(#filter-2)"></circle>
        
        <!-- Main Icon -->
        <g transform="translate(4.000000, 4.000000)" stroke="url(#linearGradient-1)" stroke-width="1.5">
            <!-- Chart Frame -->
            <rect x="1" y="1" width="26" height="26" rx="2"></rect>
            
            <!-- Growth Arrow -->
            <polyline points="22 10 27 5 27 12" stroke-linecap="round" stroke-linejoin="round"></polyline>
            <polyline points="27 5 20 12 15 7 7 15" stroke-linecap="round" stroke-linejoin="round"></polyline>
            
            <!-- Bar Charts -->
            <path d="M7,22 L7,18" stroke-linecap="round"></path>
            <path d="M12,22 L12,15" stroke-linecap="round"></path>
            <path d="M17,22 L17,12" stroke-linecap="round"></path>
            <path d="M22,22 L22,9" stroke-linecap="round"></path>
            
            <!-- Dollar Sign -->
            <path d="M4,5 C4,5 5,4 7,4 C9,4 10,5 10,6 C10,7 9,8 7,8.5 C5,9 4,10 4,11 C4,12 5,13 7,13 C9,13 10,12 10,12" stroke-linecap="round"></path>
            <path d="M7,3 L7,14" stroke-linecap="round"></path>
        </g>
    </g>
</svg>
