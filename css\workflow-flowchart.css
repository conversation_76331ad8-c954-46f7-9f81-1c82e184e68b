/* Workflow Flowchart - Apple-inspired Design */

.workflow-section {
    padding: 100px 0;
    background: linear-gradient(to bottom, #F7F7F9 0%, #FFFFFF 100%);
    position: relative;
    overflow: hidden;
}

.workflow-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
}

.workflow-svg-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 80%; /* Aspect ratio */
    margin: 40px auto;
    overflow: visible;
}

.workflow-svg-container svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Animation keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(-30px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes flowRight {
    0% { stroke-dashoffset: 1000; }
    100% { stroke-dashoffset: 0; }
}

@keyframes flowData {
    0% { transform: translateX(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateX(100px); opacity: 0; }
}

/* SVG element animations */
.workflow-node {
    animation: fadeIn 0.5s ease-out forwards;
    opacity: 0;
}

.workflow-connection {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: flowRight 1.5s ease-out forwards;
}

.workflow-data-particle {
    animation: flowData 3s infinite;
}

.workflow-icon {
    animation: pulse 2s infinite;
}

/* Animation delays for sequential appearance */
.delay-0 { animation-delay: 0s; }
.delay-1 { animation-delay: 0.2s; }
.delay-2 { animation-delay: 0.4s; }
.delay-3 { animation-delay: 0.6s; }
.delay-4 { animation-delay: 0.8s; }
.delay-5 { animation-delay: 1s; }
.delay-6 { animation-delay: 1.2s; }
.delay-7 { animation-delay: 1.4s; }
.delay-8 { animation-delay: 1.6s; }
.delay-9 { animation-delay: 1.8s; }
.delay-10 { animation-delay: 2s; }

/* Responsive adjustments */
@media (max-width: 991px) {
    .workflow-svg-container {
        padding-bottom: 120%; /* Taller aspect ratio for smaller screens */
    }
}

@media (max-width: 767px) {
    .workflow-svg-container {
        padding-bottom: 150%; /* Even taller for mobile */
    }
    
    .workflow-section {
        padding: 60px 0;
    }
}

/* Interactive elements */
.workflow-node:hover {
    filter: brightness(1.1);
    cursor: pointer;
}

/* Legend styles */
.workflow-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #555;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    margin-right: 8px;
}

.legend-client { background: linear-gradient(135deg, #e958a1, #df367d); }
.legend-adzeta { background: linear-gradient(135deg, #8f76f5, #6a4fd9); }
.legend-google { background: linear-gradient(135deg, #4285F4, #34A853); }
.legend-results { background: linear-gradient(135deg, #34A853, #FBBC05); }
