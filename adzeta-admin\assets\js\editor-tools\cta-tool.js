/**
 * CTA (Call-to-Action) Tool for Editor.js
 * Creates styled call-to-action blocks with buttons
 */

class CTATool {
    static get toolbox() {
        return {
            title: 'Call to Action',
            icon: '<svg width="17" height="15" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="#FF4081"/></svg>'
        };
    }

    static get isReadOnlySupported() {
        return true;
    }

    constructor({ data, config, api, readOnly }) {
        this.api = api;
        this.readOnly = readOnly;
        this.config = config || {};

        this.data = {
            title: data.title || 'Ready to Get Started?',
            description: data.description || 'Join thousands of businesses optimizing their performance with AdZeta.',
            buttonText: data.buttonText || 'Start Free Trial',
            buttonUrl: data.buttonUrl || '#',
            style: data.style || 'gradient',
            alignment: data.alignment || 'center',
            ...data
        };

        this.wrapper = undefined;
    }

    render() {
        this.wrapper = document.createElement('div');
        this.wrapper.classList.add('cta-tool');

        const ctaContainer = document.createElement('div');
        ctaContainer.classList.add('cta-block');

        // Apply styling based on style type
        this.applyContainerStyle(ctaContainer);

        // Title
        const titleElement = document.createElement('h3');
        titleElement.classList.add('cta-title');
        titleElement.textContent = this.data.title;
        titleElement.contentEditable = !this.readOnly;
        titleElement.style.cssText = `
            color: white;
            font-size: 2rem;
            margin-bottom: 16px;
            margin-top: 0;
            outline: none;
            text-align: ${this.data.alignment};
        `;

        // Description
        const descElement = document.createElement('p');
        descElement.classList.add('cta-description');
        descElement.textContent = this.data.description;
        descElement.contentEditable = !this.readOnly;
        descElement.style.cssText = `
            color: #E6D8F2;
            font-size: 1.2rem;
            margin-bottom: 24px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            outline: none;
            text-align: ${this.data.alignment};
        `;

        // Button
        const buttonElement = document.createElement('a');
        buttonElement.classList.add('cta-button');
        buttonElement.textContent = this.data.buttonText;
        buttonElement.href = this.data.buttonUrl;
        buttonElement.contentEditable = !this.readOnly;
        buttonElement.style.cssText = `
            display: inline-block;
            background: white;
            color: #2B0B3A;
            padding: 16px 32px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);
            outline: none;
        `;

        if (!this.readOnly) {
            // Add event listeners
            titleElement.addEventListener('input', () => {
                this.data.title = titleElement.textContent;
            });

            descElement.addEventListener('input', () => {
                this.data.description = descElement.textContent;
            });

            buttonElement.addEventListener('input', () => {
                this.data.buttonText = buttonElement.textContent;
            });

            // Add controls
            const controls = this.createControls();
            this.wrapper.appendChild(controls);
        }

        ctaContainer.appendChild(titleElement);
        ctaContainer.appendChild(descElement);
        ctaContainer.appendChild(buttonElement);
        this.wrapper.appendChild(ctaContainer);

        return this.wrapper;
    }

    applyContainerStyle(container) {
        const baseStyle = `
            border-radius: 16px;
            padding: 48px;
            margin: 32px 0;
            text-align: ${this.data.alignment};
            box-shadow: 0 8px 32px rgba(43, 11, 58, 0.2);
        `;

        switch (this.data.style) {
            case 'gradient':
                container.style.cssText = baseStyle + `
                    background: linear-gradient(135deg, #2B0B3A, #FF4081);
                `;
                break;
            case 'solid-purple':
                container.style.cssText = baseStyle + `
                    background: #2B0B3A;
                `;
                break;
            case 'solid-pink':
                container.style.cssText = baseStyle + `
                    background: #FF4081;
                `;
                break;
            case 'outline':
                container.style.cssText = baseStyle + `
                    background: white;
                    border: 2px solid #FF4081;
                `;
                // Adjust text colors for light background
                break;
        }
    }

    createControls() {
        const controls = document.createElement('div');
        controls.classList.add('cta-controls');
        controls.style.cssText = `
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 8px;
            font-size: 12px;
            flex-wrap: wrap;
        `;

        // Style options
        const styles = [
            { name: 'Gradient', value: 'gradient' },
            { name: 'Purple', value: 'solid-purple' },
            { name: 'Pink', value: 'solid-pink' },
            { name: 'Outline', value: 'outline' }
        ];

        styles.forEach(style => {
            const button = document.createElement('button');
            button.textContent = style.name;
            button.style.cssText = `
                padding: 4px 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
                cursor: pointer;
                font-size: 11px;
                ${this.data.style === style.value ? 'background: #FF4081; color: white;' : ''}
            `;

            button.addEventListener('click', () => {
                this.data.style = style.value;
                this.updateCTAStyle();
                this.updateControlsState();
            });

            controls.appendChild(button);
        });

        // Alignment options
        const alignments = [
            { name: 'Left', value: 'left' },
            { name: 'Center', value: 'center' },
            { name: 'Right', value: 'right' }
        ];

        const separator = document.createElement('div');
        separator.style.cssText = 'width: 1px; background: #ddd; margin: 0 4px;';
        controls.appendChild(separator);

        alignments.forEach(alignment => {
            const button = document.createElement('button');
            button.textContent = alignment.name;
            button.style.cssText = `
                padding: 4px 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
                cursor: pointer;
                font-size: 11px;
                ${this.data.alignment === alignment.value ? 'background: #2B0B3A; color: white;' : ''}
            `;

            button.addEventListener('click', () => {
                this.data.alignment = alignment.value;
                this.updateCTAStyle();
                this.updateControlsState();
            });

            controls.appendChild(button);
        });

        // URL input
        const urlInput = document.createElement('input');
        urlInput.type = 'url';
        urlInput.placeholder = 'Button URL';
        urlInput.value = this.data.buttonUrl;
        urlInput.style.cssText = `
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 11px;
            width: 120px;
        `;

        urlInput.addEventListener('input', () => {
            this.data.buttonUrl = urlInput.value;
            const button = this.wrapper.querySelector('.cta-button');
            if (button) {
                button.href = urlInput.value;
            }
        });

        controls.appendChild(urlInput);

        return controls;
    }

    updateCTAStyle() {
        const ctaBlock = this.wrapper.querySelector('.cta-block');
        const title = this.wrapper.querySelector('.cta-title');
        const description = this.wrapper.querySelector('.cta-description');

        if (ctaBlock) {
            this.applyContainerStyle(ctaBlock);
        }

        if (title) {
            title.style.textAlign = this.data.alignment;
            title.style.color = this.data.style === 'outline' ? '#2B0B3A' : 'white';
        }

        if (description) {
            description.style.textAlign = this.data.alignment;
            description.style.color = this.data.style === 'outline' ? '#666' : '#E6D8F2';
        }
    }

    updateControlsState() {
        const controls = this.wrapper.querySelector('.cta-controls');
        if (controls) {
            // Update button states
            const buttons = controls.querySelectorAll('button');
            buttons.forEach(button => {
                button.style.background = 'white';
                button.style.color = 'black';
            });
        }
    }

    save() {
        return {
            title: this.data.title,
            description: this.data.description,
            buttonText: this.data.buttonText,
            buttonUrl: this.data.buttonUrl,
            style: this.data.style,
            alignment: this.data.alignment
        };
    }

    static get sanitize() {
        return {
            title: false,
            description: false,
            buttonText: false
        };
    }

    // Paste config removed to avoid Editor.js conflicts
    // Custom paste handling is done through AI content processor
}

// Export for use in Editor.js
window.CTATool = CTATool;
