/* ===================================
    Crafto - Application
====================================== */
/* font */

/* Import Proxima Nova fonts */
@import url('../web-fonts/proxima/style.css');

/* variable */
:root {
    --alt-font: 'Proxima Nova', sans-serif; /* Heading Font */
    --primary-font: 'Proxima Nova', sans-serif; /* Body Font */
    --base-color:#de347f;
    --dark-gray: #2c2e3c;
    --medium-gray:#797a8c;
}
/* reset */


::-webkit-input-placeholder {
    color: var(--aluminium-grey) !important;
    text-overflow: ellipsis;
}
::-moz-placeholder {
    color: var(--aluminium-grey) !important;
    text-overflow: ellipsis;
    opacity: 1;
}
:-ms-input-placeholder {
    color: var(--aluminium-grey) !important;
    text-overflow: ellipsis;
    opacity: 1;
}

.bg-base-color{background-color:#ff6a8d}
.base-color-gradient {
    background: -webkit-linear-gradient(to right, #e958a1, #ff5d74); 
    background: linear-gradient(to right, #e958a1, #ff5d74);       

    -webkit-background-clip: text; 
    background-clip: text;         

    -webkit-text-fill-color: transparent; 
    color: transparent;                  

    display: inline-block; 
}

body {
   font-family: var(--primary-font);
   font-size: 1rem; /* 16px base font size */
   line-height: 1.6;
   font-weight: 400;
   color: #797a8c;
}



/* Paragraph styling for Proxima Nova */
p {
   font-family: var(--primary-font);
   font-weight: 400;
   margin-bottom: 1rem;
   font-size: 1rem; /* 16px */
   line-height: 1.6;
}

/* Responsive paragraph styling - following industry standards for readability */
/* Media queries are consolidated at the end of the file */

/* Proxima Nova has excellent readability at slightly smaller sizes */


/* Proxima Nova looks great with slightly tighter line height for small text */
.small, small {
   font-size: 0.8125rem; /* 13px */
   line-height: 1.4;
}
/* custom cursor */
@media (hover: hover) and (pointer: fine) {
    .custom-cursor .circle-cursor-outer {
        border: 1px solid transparent;
    }
    .custom-cursor .circle-cursor-inner {
        width: 25px;
        height: 25px;
        background: rgba(44,46,60,.15)
    }
}

.bg-base-medium{background: linear-gradient(to bottom, #f2f0ee, #f6f4f9);}
.bg-base-medium-single{background:#f2f0ee;}


/* --- Hero Section Dark Gradient Background --- */

/* Enhanced Typography for Hero Section with Proxima Nova Font */
.primary-font {
    font-family: var(--primary-font);
    letter-spacing: 0;
}

.alt-font {
    font-family: var(--alt-font);

}

/* Gradient text effects */
.text-gradient-blue-magenta-orange {
    background: linear-gradient(to right, #0036ff, #e958a1, #ff7042);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    display: inline-block;
}

/* Letter spacing utilities */
.ls-wide {
    letter-spacing: 0.02em;
}

/* Font weight classes for Proxima Nova */


/* Additional typography enhancements */
.ls-tight {
    letter-spacing: -0.02em;
}

.ls-normal {
    letter-spacing: 0;
}

.ls-wide {
    letter-spacing: 0.01em;
}

.text-highlight {
    position: relative;
    display: inline-block;
    color: #e958a1;
}

/* Modern UI Enhancements for Proxima Nova */
.text-highlight {
    position: relative;
}

.text-highlight::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background-image: linear-gradient(to right, rgba(233, 88, 161, 0.7), rgba(209, 94, 199, 0.7));
    opacity: 0.8;
    border-radius: 1px;
}
/* text gradient color */
.text-gradient-base-color {
    background-image: linear-gradient(to right, var(--base-color) 0%, #ff5d74 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}


/* Enhance button hover states */
.btn-gradient-purple-pink:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(233, 88, 161, 0.3);
}



/* Proxima Nova specific adjustments */
.alt-font.fw-bold {
    /* Proxima Nova Bold looks better with slightly tighter tracking */
    letter-spacing: -0.01em;
}

.alt-font.fw-semibold {
    /* Proxima Nova SemiBold looks better with slightly tighter tracking */
    letter-spacing: -0.01em;
}

.alt-font.fw-medium {
    /* Proxima Nova Medium looks better with normal tracking */
    letter-spacing: 0;
}

.primary-font.fw-regular {
    /* Proxima Nova Regular looks better with normal tracking */
    letter-spacing: 0;
}

/* Improve heading line heights for Proxima Nova */
h1.alt-font, h2.alt-font, h3.alt-font {
    line-height: 1.15;
}

h4.alt-font, h5.alt-font, h6.alt-font {
    line-height: 1.35;
}

/* Blog post styling for Proxima Nova */
.blog-content h3 {
    font-size: 1.125rem; /* 18px */
    line-height: 1.4;
    margin-bottom: 0.5rem; /* 8px */
}

.blog-content p {
    font-size: 0.9375rem; /* 15px */
    line-height: 1.6;
    color: #555;
}

.blog-content .blog-date {
    font-family: var(--primary-font);
    font-size: 0.875rem; /* 14px */
}

.blog-categories a {
    font-family: var(--alt-font);
    font-weight: 600;
    letter-spacing: 0.03125rem; /* 0.5px */
}

/* Author styling for blog posts */
.card-top .avtar {
    width: 2.5rem; /* 40px */
    height: 2.5rem; /* 40px */
    object-fit: cover;
}

.card-top span {
    font-family: var(--primary-font);
}

.card-top a {
    font-family: var(--alt-font);
}

.text-medium-gray {
    color: #777;
}

/* Comparison Cards Typography */
.comparison-card ul li {
    margin-bottom: 0.75rem;
}

/* Apple-inspired Minimalist Graph Styles */
.animated-graph-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 500px;
    margin-top: -20px;
    border-radius: 12px;
    overflow: hidden;


}

/* Hover effect for animated graph container */
.animated-graph-container:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

/* All animation keyframes moved to enhanced-particles.css */
@keyframes fadeInLegend {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInPoint {
    from {
        opacity: 0;
        transform: scale(0);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeInArea {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.15;
    }
}

@keyframes fadeInLabel {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInSocialIcon {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.graph-line {
    fill: none;
    stroke-width: 3;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-dasharray: 2000;
    stroke-dashoffset: 2000;
    animation: drawLine 3s ease-in-out forwards;
}

.graph-line-primary {
    stroke: #e958a1;
    stroke-width: 3.5;
}

.graph-line-secondary {
    stroke: #8f76f5;
    opacity: 0.8;
    stroke-width: 2.5;
}

.graph-point {
    fill: #e958a1;
    r: 6;
    opacity: 0;
    transform-origin: center;
    animation: fadeInPoint 0.3s ease-in-out forwards;
    filter: drop-shadow(0 0 3px rgba(233, 88, 161, 0.5));
}

.graph-point-secondary {
    fill: #8f76f5;
    filter: drop-shadow(0 0 3px rgba(143, 118, 245, 0.5));
}

.graph-area {
    fill: url(#graphGradient);
    opacity: 0;
    animation: fadeInArea 2s ease-in-out 1s forwards;
}

.graph-area-secondary {
    fill: url(#graphGradientSecondary);
}

.graph-label {
    font-size: 12px;
    font-weight: 600;
    opacity: 0;
    animation: fadeInLabel 0.5s ease-in-out forwards;
}

.graph-tooltip {
    position: absolute;
    background: rgba(28, 28, 30, 0.85);
    color: #fff;
    padding: 8px 12px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 500;
    pointer-events: none;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(8px);
    border: 0.5px solid rgba(255, 255, 255, 0.1);
}

.graph-tooltip.active {
    opacity: 1;
    transform: translateY(0);
}

.graph-tooltip-value {
    color: #e958a1;
    font-weight: 700;
}

/* Simplified for Apple-inspired minimalist design */

.social-icon-container {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: scale(0.5);
    animation: fadeInSocialIcon 0.5s ease-in-out forwards;
    z-index: 5;
}

.graph-data-label {
    position: absolute;
    font-size: 14px;
    font-weight: 700;
    color: #fff;
    background: rgba(233, 88, 161, 0.8);
    padding: 3px 8px;
    border-radius: 4px;
    transform: translate(-50%, -50%);
    opacity: 0;
    animation: fadeInLabel 0.5s ease-in-out forwards;
    z-index: 5;
}

.graph-legend {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    padding: 12px 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    animation: fadeInLegend 0.5s ease-in-out 3.5s forwards;
    border: 1px solid rgba(233, 88, 161, 0.2);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #fff;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* Single definition of animation keyframes */
@keyframes drawLine {
    to {
        stroke-dashoffset: 0;
    }
}

/* fadeInLegend keyframe moved to enhanced-particles.css */

.social-icon-container {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0);
    animation: popIcon 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    z-index: 10;
}

.social-icon-container:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.social-icon-container i {
    font-size: 20px;
}

.social-icon-facebook i {
    color: #3b5998;
}

.social-icon-twitter i {
    color: #1da1f2;
}

.social-icon-instagram i {
    color: #c32aa3;
}

.social-icon-linkedin i {
    color: #0077b5;
}

.social-icon-youtube i {
    color: #ff0000;
}

.social-icon-pinterest i {
    color: #bd081c;
}

.graph-tooltip {
    position: absolute;
    background: white;
    border-radius: 4px;
    padding: 8px 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-size: 12px;
    font-weight: 600;
    pointer-events: none;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    z-index: 100;
}

.graph-tooltip.active {
    opacity: 1;
    transform: translateY(0);
}

.graph-tooltip-value {
    color: #8f76f5;
    font-weight: 700;
}

/* Duplicate keyframes removed */

@keyframes popIcon {
    0% {
        transform: scale(0);
    }
    70% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* bg gradient color */
.bg-gradient-pink-orchid {
    background-image: linear-gradient(to right top, #e958a1, #e15aae, #d15ec7, #c561db, #bf63e5);
}
.bg-gradient-deep-zuccini-green {
    background-image: linear-gradient(to right bottom, #19493e, #164339, #143e34, #113830, #0f332b);
}
.bg-gradient-aztec-green {
    background-image: linear-gradient(to right bottom, #313e3b, #2e3b38, #2c3835, #293532, #27322f);
}
.bg-gradient-tranquil-white {
    background-image: linear-gradient(to bottom, #ffffff, #f7f9f9, #f3f6f6, #eff3f3, #ecf1f1);
}
.bg-yellow {
    background-color: var(--yellow);
}
.bg-tranquil {
    background-color: #f3f8f8;
}
.bg-gradient-dark-transparent {
    background: -webkit-gradient(linear, left bottom, left top, color-stop(7%, #36332e), to(transparent));
    background: linear-gradient(to top, #262a29 0%, transparent 100%);
}
.bg-gradient-flamingo-amethyst-green {
    background-image: linear-gradient(to right, #E3545C, #c84d9d, #885dd1, #2f86bc, #01a89e);
}
/* Adzeta button style */
.btn-gradient-purple-pink {
    background-image: linear-gradient(to right, #e958a1, #d15ec7) !important;
    border: none !important;
    transition: all 0.3s ease !important;
}

/* Text gradients */
.text-gradient-purple {
    background: linear-gradient(to right, var(--base-color) 0%, #ff5d74 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

.text-gradient-purple-blue {
    background: linear-gradient(90deg, #FF6A8D 20%, #C46CFA 60%, #7369f1 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}


/* Blue-Magenta-Orange gradient for hero text */
.text-gradient-blue-magenta-orange {
    background: linear-gradient(to right, #3366ff, #b14aed, #ff6347);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

/* Gradient line for section headers */
.gradient-line {
    background: linear-gradient(to right, #3366ff, #b14aed);
    height: 1px;
    width: 25px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
}

/* Gradient text styles for stats */
.gradient-text-pink {
    background: linear-gradient(to right, #e958a1, #ff5a8a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.gradient-text-purple {
    background: linear-gradient(to right, #8f76f5, #d15ec7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.gradient-text-orange {
    background: linear-gradient(to right, #ff7042, #ff5a5a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.gradient-text-mixed {
    background: linear-gradient(to right, #ff5a5a, #e958a1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

.btn-gradient-purple-pink:hover {
    background-image: linear-gradient(to right, #d15ec7, #e958a1) !important;
    box-shadow: 0 8px 20px rgba(233, 88, 161, 0.3) !important;
    transform: translateY(-2px) !important;
}



.btn-transparent-white-light:hover {
    background: #fff !important;
    border-color: #fff !important;

}

/* Professional high-tech gradient system */
.professional-gradient-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #14101e; /* Matching the footer base color */
    z-index: 0;
}

/* Corner gradients for professional look */
.corner-gradient {
    position: absolute;
    width: 60%;
    height: 60%;
    opacity: 0.7;
    mix-blend-mode: screen;
    filter: blur(60px);
    z-index: 1;
}

.top-left {
    top: -10%;
    left: -10%;
    background: radial-gradient(circle at 30% 30%, rgba(209, 94, 199, 0.35) 0%, rgba(0, 0, 0, 0) 70%);
    animation: subtlePulse 15s infinite alternate;
}

.top-right {
    top: -10%;
    right: -10%;
    background: radial-gradient(circle at 70% 30%, rgba(233, 88, 161, 0.4) 0%, rgba(0, 0, 0, 0) 70%);
    animation: subtlePulse 18s infinite alternate-reverse;
}

.bottom-left {
    bottom: -10%;
    left: -10%;
    background: radial-gradient(circle at 30% 70%, rgba(143, 118, 245, 0.3) 0%, rgba(0, 0, 0, 0) 70%);
    animation: subtlePulse 20s infinite alternate;
}

.bottom-right {
    bottom: -10%;
    right: -10%;
    background: radial-gradient(circle at 70% 70%, rgba(143, 118, 245, 0.25) 0%, rgba(0, 0, 0, 0) 70%);
    animation: subtlePulse 17s infinite alternate-reverse;
}

/* Diagonal gradient for professional look - Coordinated with footer */
.diagonal-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(0, 0, 0, 0.95) 0%,
        rgba(20, 10, 30, 0.9) 20%,
        rgba(40, 15, 50, 0.85) 40%,
        rgba(233, 88, 161, 0.25) 70%,
        rgba(143, 118, 245, 0.2) 100%);
    z-index: 1;
}

/* Minimal high-tech grid overlay with strategic fade-out */
body:not(.ecom-ppc) .mesh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(90deg, rgba(255, 255, 255, 0.015) 1px, transparent 1px),
        linear-gradient(0deg, rgba(255, 255, 255, 0.015) 1px, transparent 1px);
    background-size: 40px 40px; /* Much larger grid for minimalism */
    z-index: 2;
    pointer-events: none;
    /* Complex gradient mask for strategic fading */
    -webkit-mask-image:
        linear-gradient(to right,
            rgba(0,0,0,1) 20%, /* Solid on left side */
            rgba(0,0,0,0.7) 50%, /* Start fading in middle */
            rgba(0,0,0,0) 80% /* Completely transparent on right */
        ),
        linear-gradient(to bottom,
            rgba(0,0,0,1) 0%,
            rgba(0,0,0,1) 40%, /* Solid above graph area */
            rgba(0,0,0,0.3) 60%, /* Fade in graph area */
            rgba(0,0,0,0) 100% /* Completely transparent at bottom */
        );
    mask-image:
        linear-gradient(to right,
            rgba(0,0,0,1) 20%,
            rgba(0,0,0,0.7) 50%,
            rgba(0,0,0,0) 80%
        ),
        linear-gradient(to bottom,
            rgba(0,0,0,1) 0%,
            rgba(0,0,0,1) 40%,
            rgba(0,0,0,0.3) 60%,
            rgba(0,0,0,0) 100%
        );
    -webkit-mask-composite: source-in;
    mask-composite: intersect;
    opacity: 0.9; /* Slightly reduced overall opacity */
}

/* Empty mesh overlay for ecom-ppc page */
.ecom-ppc .mesh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: none;
    z-index: 2;
    pointer-events: none;
}

/* Professional vignette overlay */
.vignette-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0) 40%, rgba(0, 0, 0, 0.4) 100%);
    z-index: 2;
    pointer-events: none;
}

/* Light version of professional gradient container */
.light-professional-gradient-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #f8f9fc; /* Light background color */
    z-index: 0;
}

/* Light corner gradients */
.light-corner-gradient {
    position: absolute;
    width: 60%;
    height: 60%;
    opacity: 0.5;
    mix-blend-mode: multiply;
    filter: blur(60px);
    z-index: 1;
}

.light-top-left {
    top: -10%;
    left: -10%;
    background: radial-gradient(circle at 30% 30%, rgba(209, 94, 199, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
    animation: subtlePulse 15s infinite alternate;
}

.light-top-right {
    top: -10%;
    right: -10%;
    background: radial-gradient(circle at 70% 30%, rgba(233, 88, 161, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
    animation: subtlePulse 18s infinite alternate-reverse;
}

.light-bottom-left {
    bottom: -10%;
    left: -10%;
    background: radial-gradient(circle at 30% 70%, rgba(143, 118, 245, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
    animation: subtlePulse 20s infinite alternate;
}

.light-bottom-right {
    bottom: -10%;
    right: -10%;
    background: radial-gradient(circle at 70% 70%, rgba(143, 118, 245, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
    animation: subtlePulse 17s infinite alternate-reverse;
}

/* Light diagonal gradient */
.light-diagonal-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(245, 245, 250, 0.9) 20%,
        rgba(240, 240, 250, 0.85) 40%,
        rgba(233, 88, 161, 0.1) 70%,
        rgba(143, 118, 245, 0.08) 100%);
    z-index: 1;
}

/* Light mesh overlay */
body:not(.ecom-ppc) .light-mesh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px),
        linear-gradient(0deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
    background-size: 40px 40px;
    z-index: 2;
    pointer-events: none;
    -webkit-mask-image:
        linear-gradient(to right,
            rgba(0,0,0,1) 20%,
            rgba(0,0,0,0.7) 50%,
            rgba(0,0,0,0) 80%
        ),
        linear-gradient(to bottom,
            rgba(0,0,0,1) 0%,
            rgba(0,0,0,1) 40%,
            rgba(0,0,0,0.3) 60%,
            rgba(0,0,0,0) 100%
        );
    mask-image:
        linear-gradient(to right,
            rgba(0,0,0,1) 20%,
            rgba(0,0,0,0.7) 50%,
            rgba(0,0,0,0) 80%
        ),
        linear-gradient(to bottom,
            rgba(0,0,0,1) 0%,
            rgba(0,0,0,1) 40%,
            rgba(0,0,0,0.3) 60%,
            rgba(0,0,0,0) 100%
        );
    -webkit-mask-composite: source-in;
    mask-composite: intersect;
    opacity: 0.7;
}

/* Empty light mesh overlay for ecom-ppc page */
.ecom-ppc .light-mesh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: none;
    z-index: 2;
    pointer-events: none;
}

/* Light vignette overlay */
.light-vignette-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0) 40%, rgba(233, 88, 161, 0.1) 100%);
    z-index: 2;
    pointer-events: none;
}

/* Opacity control classes for light gradient container */
.light-professional-gradient-container.opacity-subtle {
    opacity: 0.5;
}

.light-professional-gradient-container.opacity-medium {
    opacity: 0.7;
}

.light-professional-gradient-container.opacity-strong {
    opacity: 0.9;
}

/* Light gradient variations */
.light-professional-gradient-container.blue-theme {
    background: #f5f8ff;
}

.light-professional-gradient-container.blue-theme .light-top-left {
    background: radial-gradient(circle at 30% 30%, rgba(100, 149, 237, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
}

.light-professional-gradient-container.blue-theme .light-top-right {
    background: radial-gradient(circle at 70% 30%, rgba(65, 105, 225, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
}

.light-professional-gradient-container.blue-theme .light-bottom-left {
    background: radial-gradient(circle at 30% 70%, rgba(30, 144, 255, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
}

.light-professional-gradient-container.blue-theme .light-bottom-right {
    background: radial-gradient(circle at 70% 70%, rgba(70, 130, 180, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
}

.light-professional-gradient-container.blue-theme .light-diagonal-gradient {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(240, 248, 255, 0.9) 20%,
        rgba(240, 248, 255, 0.85) 40%,
        rgba(65, 105, 225, 0.1) 70%,
        rgba(30, 144, 255, 0.08) 100%);
}

.light-professional-gradient-container.blue-theme .light-vignette-overlay {
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0) 40%, rgba(65, 105, 225, 0.1) 100%);
}

/* Soft purple theme */
.light-professional-gradient-container.purple-theme {
    background: #f8f5ff;
}

.light-professional-gradient-container.purple-theme .light-top-left {
    background: radial-gradient(circle at 30% 30%, rgba(147, 112, 219, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
}

.light-professional-gradient-container.purple-theme .light-top-right {
    background: radial-gradient(circle at 70% 30%, rgba(138, 43, 226, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
}

.light-professional-gradient-container.purple-theme .light-bottom-left {
    background: radial-gradient(circle at 30% 70%, rgba(123, 104, 238, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
}

.light-professional-gradient-container.purple-theme .light-bottom-right {
    background: radial-gradient(circle at 70% 70%, rgba(106, 90, 205, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
}

.light-professional-gradient-container.purple-theme .light-diagonal-gradient {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 240, 255, 0.9) 20%,
        rgba(245, 240, 255, 0.85) 40%,
        rgba(138, 43, 226, 0.1) 70%,
        rgba(123, 104, 238, 0.08) 100%);
}

.light-professional-gradient-container.purple-theme .light-vignette-overlay {
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0) 40%, rgba(138, 43, 226, 0.1) 100%);
}

/* Particle effects moved to enhanced-particles.css */
.bg-dark-haiti {
    background-color: #2e2b39;
}
.bg-floral-white {
    background-color: #fff8e7;
}
.bg-licorice-blue {
    background-color: #585d72;
}
.bg-black-pearl-blue {
    background-color: #232530;
}
.bg-black-pearl-blue-dark {
    background-color: #1f212a;
}
.bg-linen{
    background-color: #fdedea;
}
.bg-oxford-blue{
    background-color: #262D38;
}
/* bg color */
.bg-cabaret-red {
    background-color: #d94f6f;
}
/* text gradient color */
.text-gradient-pink-orchid {
    background-image: linear-gradient(to right, #e958a1, #e15aae, #d15ec7, #c561db, #bf63e5);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}
/* Heading styles for Proxima Nova */
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
    margin-bottom: 25px;
    font-family: var(--alt-font);
    font-weight: 700;
    letter-spacing: -0.01em;
}

h1, .h1 {
    font-size: 3.75rem; /* 60px */
    line-height: 1.05;
}

h2, .h2 {
    font-size: 2.5rem; /* 48px */
    line-height: 1.15;
}

h3, .h3 {
    font-size: 2.25rem; /* 36px */
    line-height: 1.25;
}

h4, .h4 {
    font-size: 1.875rem; /* 30px */
    line-height: 1.35;
}

h5, .h5 {
    font-size: 1.5rem; /* 24px */
    line-height: 1.45;
}

h6, .h6 {
    font-size: 1.125rem; /* 18px */
    line-height: 1.55;
}
/* Button styling for Proxima Nova */
.btn {
    font-family: var(--alt-font);
    font-weight: 600;
    text-transform: none;
    letter-spacing: 0;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-gradient-pink-orchid {
    background-image: linear-gradient(to right, #e958a1, #bf63e5, #bf63e5);
    background-size: 200% auto;
    color: var(--white);
}
.highlight-separator-small .highlight-separator[data-shadow-animation] span {
    height: 45px;
    bottom: -10px;
}

/* Form elements for Proxima Nova */
input, select, textarea {
    font-family: var(--primary-font);
    font-weight: 400;
    font-size: 0.9375rem; /* 15px */
}

/* Newsletter styling */
.newsletter-style-02 .btn {
    padding: 0.4375rem 1.125rem 0.5625rem 1.125rem; /* 7px 18px 9px 18px */
    font-family: var(--alt-font);
}
.newsletter-style-02 input {
    padding-right: 3.125rem; /* 50px */
    font-size: 0.9375rem; /* 15px */
    font-family: var(--primary-font);
}
/* border color gradient */
.border-gradient-pink-orchid {
    border-image: linear-gradient(to right top, #e958a1, #e15aae, #d15ec7, #c561db, #bf63e5);
    border-image-slice: 1;
}
/* Modern Header Styling */
header .navbar-brand img {
    max-height: 33px;
    transition: all 0.3s ease;
}

header .navbar-brand .default-logo{
	
	 max-height: 45px;
    transition: all 0.3s ease;
	
}

/* Navbar background with gradient and blur */
.navbar.header-light {
    background: linear-gradient(90deg, rgba(41, 41, 61, 0.85), rgba(61, 41, 81, 0.85)) !important;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(233, 88, 161, 0.15);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 15px 0;
}

/* Navbar links */
.navbar .navbar-nav .nav-link {
    font-size: 0.9375rem; /* 15px */
    font-weight: 600; /* Proxima Nova has 600 for semibold instead of 500 */
    color: rgba(255, 255, 255, 0.9) !important;
    padding: 0.625rem 1.125rem; /* 10px 18px */
    transition: all 0.3s ease;
    position: relative;
    letter-spacing: 0;
}

body.adzeta-case-study .navbar .navbar-nav .nav-link{
	color:#2a2b3f !important;
}
.adzeta-case-study .navbar .navbar-nav .nav-link:hover,
.adzeta-case-study .navbar .navbar-nav .nav-item.active .nav-link {
    color: #e958a1 !important;
}

/* Navbar link hover and active states */
/* .navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .nav-item.active .nav-link {
    color: #ffffff !important;
    opacity: 1 !important;
    transform: translateY(-1px);
} */

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Sticky menu text color */
header.sticky.sticky-active .navbar .navbar-nav .nav-link {
    color: var(--medium-gray) !important;
}

header.sticky.sticky-active .navbar .navbar-nav .nav-link:hover,
header.sticky.sticky-active .navbar .navbar-nav .nav-item.active .nav-link {
    color: var(--dark-gray) !important;
}

/* Elegant hover effect for nav links */
.navbar .navbar-nav .nav-link {
    position: relative;
    transition: color 0.3s ease;
}

.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .nav-item.active .nav-link {
    color: #e958a1 !important;
}

/* Subtle indicator for active/hover state */
.navbar .navbar-nav .nav-item.active .nav-link {
    font-weight: 500;
}

/* Dropdown styling - Corporate & Elegant */
.navbar .navbar-nav .dropdown-menu {
    width: 16.25rem; /* 260px */
    background: rgba(255, 255, 255, 0.97);
    backdrop-filter: blur(0.75rem); /* 12px */
    -webkit-backdrop-filter: blur(0.75rem); /* 12px */
    border: 1px solid rgba(233, 88, 161, 0.08);
    border-radius: 0.5rem; /* 8px */
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08); /* 0 8px 24px */
    padding: 0.75rem 0; /* 12px 0 */
    margin-top: 0.3125rem; /* 5px - Reduced space between menu and submenu */
    overflow: hidden;
    animation: dropdownFadeIn 0.25s ease-out;
}

/* dropdownFadeIn keyframe moved to enhanced-particles.css */

.navbar .navbar-nav .dropdown-menu li a {
    font-size: 0.875rem; /* 14px */
    line-height: 1.5rem; /* 24px */
    font-weight: 500;
    color: var(--dark-gray);
    padding: 0.625rem 1.5625rem; /* 10px 25px */
    transition: all 0.3s ease;
    position: relative;
}

.navbar .navbar-nav .dropdown-menu li a:hover {
    background: rgba(233, 88, 161, 0.05);
    color: #e958a1;
    transform: translateX(5px);
}

/* Dropdown with icon */
.navbar .navbar-nav .dropdown.dropdown-with-icon .dropdown-menu {
    width: 22.5rem; /* 360px */
}

.navbar .navbar-nav .dropdown.dropdown-with-icon .dropdown-menu li a i {
    color: #e958a1;
    margin-right: 0.625rem; /* 10px */
    font-size: 1.125rem; /* 18px */
    transition: all 0.3s ease;
}

.navbar .navbar-nav .dropdown.dropdown-with-icon .dropdown-menu li a:hover i {
    transform: translateX(0.125rem); /* 2px */
}

.navbar .navbar-nav .dropdown.dropdown-with-icon .dropdown-menu .submenu-icon-content {
    padding-left: 0.625rem; /* 10px */
}

.navbar .navbar-nav .dropdown.dropdown-with-icon .dropdown-menu .submenu-icon-content span {
    font-weight: 600;
    display: block;
    margin-bottom: 0.125rem; /* 2px */
    color: var(--dark-gray);
}

.navbar .navbar-nav .dropdown.dropdown-with-icon .dropdown-menu .submenu-icon-content p {
    font-size: 0.75rem; /* 12px */
    margin-bottom: 0;
    color: var(--medium-gray);
}

/* Header icons */
.header-icon .header-social-icon a {
    font-size: 1.0625rem; /* 17px */
    padding-left: 1.4375rem; /* 23px */
    color: rgba(255, 255, 255, 0.85);
    transition: all 0.3s ease;
}

.header-icon .header-social-icon a:hover {
    color: #ffffff;
    transform: translateY(-0.125rem); /* -2px */
}

/* Button in header */
header .btn i {
    top: -1px !important;
    transition: transform 0.3s ease;
}

/* Header CTA button */
header .btn.btn-gradient-purple-pink {
    background-image: linear-gradient(to right, #e958a1, #bf63e5);
    border: none;
    padding: 0.625rem 1.25rem; /* 10px 20px */
    font-weight: 600;
    letter-spacing: 0.03125rem; /* 0.5px */
    transition: all 0.3s ease;
    border-radius: 1.875rem; /* 30px */
}

header .btn.btn-gradient-purple-pink:hover {
    transform: translateY(-0.125rem); /* -2px */
    box-shadow: 0 0.5rem 0.9375rem rgba(191, 99, 229, 0.3); /* 0 8px 15px */
}

header .btn.btn-gradient-purple-pink:hover i {
    transform: translateX(0.1875rem); /* 3px */
}

/* Sticky header behavior */
header.sticky.sticky-active .navbar.header-light {
    background: rgba(255, 255, 255, 0.97) !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08) !important;
    padding: 10px 0;
    border-bottom: 1px solid rgba(233, 88, 161, 0.1);
}

header.sticky.sticky-active .navbar-brand img {
    max-height: 33px;
}

/* AI Icon Animation moved to enhanced-particles.css */

/* AI animation keyframes moved to enhanced-particles.css */

/* AI core pulse animation moved to enhanced-particles.css */

/* Mobile menu styles */

/* Make all navbar-toggler-line elements equal width */
.navbar-toggler .navbar-toggler-line:nth-child(1),
.navbar-toggler .navbar-toggler-line:nth-child(4) {
    width: 22px !important; /* Make first and fourth lines same width as middle lines */
}

/* Sticky header toggler */
header.sticky.sticky-active .navbar-toggler {

}

header.sticky.sticky-active .navbar-toggler .navbar-toggler-line {
    background: var(--dark-gray);
}

header.sticky.sticky-active .navbar-toggler:hover {
   
}

/* Mobile menu - no animation to preserve original shape */

/* Mobile menu expanded styles */
@media (max-width: 991px) {
    .navbar-collapse {
        background: rgba(255, 255, 255, 0.97);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border-radius: 0 0 15px 15px;
        padding: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        max-height: 80vh;
        overflow-y: auto;
        border: 1px solid rgba(233, 88, 161, 0.1);
        border-top: none;
    }

    .navbar-nav {
        padding: 10px 0;
    }

    .navbar .navbar-nav .nav-link {
        padding: 12px 20px;
        border-radius: 8px;
        color: var(--medium-gray) !important;
    }

 /*    .navbar .navbar-nav .nav-link:hover,
    .navbar .navbar-nav .nav-item.active .nav-link {
        background: rgba(233, 88, 161, 0.05);
        color: var(--dark-gray) !important;
    } */

    .navbar .navbar-nav .dropdown-menu {
        background: rgba(245, 245, 245, 0.7);
        border: none;
        box-shadow: none;
        padding: 5px 0 10px 0;
        margin-top: 5px;
        margin-bottom: 10px;
        border-radius: 8px;
        width: 100% !important;
    }

    .navbar .navbar-nav .dropdown-menu li a {
        padding-left: 30px;
    }

    .navbar .navbar-nav .nav-link::after {
        display: none;
    }

    .navbar-nav .dropdown-toggle {
        position: absolute;
        right: 20px;
        top: 12px;
        color: var(--medium-gray);
        transition: all 0.3s ease;
    }

    .navbar-nav .dropdown-toggle:hover {
        color: #e958a1;
    }

    /* Mobile menu toggler color for sticky header */
    header.sticky.sticky-active .navbar-toggler .navbar-toggler-line {
        background: var(--dark-gray);
    }

    header.sticky.sticky-active .navbar-toggler:hover .navbar-toggler-line {
        background: #e958a1;
    }
}

/* Sticky header button styles */
.sticky .header-transparent .header-button .btn {
    color: var(--dark-gray);
    border-color: rgba(233, 88, 161, 0.3);
    background: rgba(233, 88, 161, 0.05);
}

.sticky .header-transparent .header-button .btn:hover {
    border-color: var(--base-color) !important;
    color: #fff;
    background: var(--base-color) !important;
}

header.sticky.sticky-active [data-header-hover="light"] .widget-text i {
    color: var(--dark-gray);
}

/* Update CTA button in sticky header */
header.sticky.sticky-active .btn.btn-gradient-purple-pink {
    background-image: linear-gradient(to right, #e958a1, #bf63e5);
    color: var(--white) !important;
}

/* Dropdown toggle icon styling */
.navbar .navbar-nav .dropdown-toggle {
    font-size: 10px;
    margin-left: 2px;
    opacity: 0.6;
    transition: all 0.2s ease;
}

.navbar .navbar-nav .nav-item:hover .dropdown-toggle,
.navbar .navbar-nav .nav-item.active .dropdown-toggle {
    opacity: 0.9;
    color: #e958a1;
}

/* Dropdown with icon style 02 */
.navbar .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu {
    background: rgba(255, 255, 255, 0.97);
}

.navbar .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu li a {
    border-bottom: 1px solid rgba(233, 88, 161, 0.05);
    color: var(--dark-gray);
    transition: all 0.2s ease;
    padding: 10px 18px;
    font-size: 14px;
}

.navbar .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu li:last-child a {
    border-bottom: none;
}

.navbar .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu li:hover a,
.navbar .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu li.active a {
    background: rgba(233, 88, 161, 0.03);
    color: #e958a1;
    opacity: 1;
    transform: translateX(3px);
}

.navbar .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu li a i {
    margin-right: 8px;
    font-size: 13px;
    opacity: 0.7;
}
/* top right bottom left */
.bottom-130px {
    bottom: 130px;
}
.bottom-150px {
    bottom: 150px;
}
.top-25 {
    top: 25%!important;
}
/* height */
.h-64px {
    height: 64px !important;
}
/* left right top */
.top-20 {
    top: 20%;
}
.top-30 {
    top: 30%;
}
.top-minus-180px {
    top: -180px;
}
.left-40 {
    left: 40%;
}
.right-10 {
    right: 10%;
}
/* margin */
.margin-minus-95px-bottom {
    margin-bottom: -95px;
}
/* border radius */
.border-radius-top-left{
    border-radius: 10px 0 0 10px !important;
}
.border-color-transparent-dark {
    border-color: rgba(31, 36, 46, 0.5) !important;
}
/* page title */
.page-title-extra-large h1 {
    font-size: 4.063rem;
    line-height: 4.375rem;
}
.page-title-extra-large h2 {
    font-size: 16px;
    line-height: 20px;
}
/* scrolling text */
.marquees-text {
    word-break: normal;
}
/* review */
.review-star-icon i {
    color: #ffae00;
}
/*  process step style 05  */
.homepage .process-step-style-05 .progress-step-separator {
    bottom: inherit;
    height: 100%;
}
/*  Enhanced Professional Footer Styling  */
footer {
    padding: 70px 0 30px;
    position: relative;
    overflow: hidden;
}

/* Footer Background - Coordinated with Hero Section */
footer.bg-gradient-aztec-green {

	background-image: linear-gradient(to right bottom, #000000, #0A0A14, #12081E, #1A0A28, #220C32);
    position: relative;
    overflow: hidden;
}

footer .divider-style-03 {
    background: linear-gradient(to right, rgba(143, 118, 245, 0.3), rgba(233, 88, 161, 0.3));
    height: 1px;
}

footer .background-position-center-top {
    opacity: 0.08 !important;
}

/* Footer Logo */
footer .footer-logo img {
    max-height:50px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.3s ease;
}

footer .footer-logo:hover img {
    transform: translateY(-2px);
}

/* Footer Typography */
footer p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    line-height: 1.7;
    font-weight: 400;
    letter-spacing: 0.01em;
}

footer span.alt-font {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0.02em;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

/* Footer Links */
footer ul li {
    margin-bottom: 8px;
}

footer ul li a {
    color: rgba(255, 255, 255, 0.65);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: color 0.3s ease;
    display: inline-block;
    padding-left: 0;
}

footer ul li a:hover {
    color: #e958a1;
}

/* Footer Social Icons */
footer .elements-social .light li a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    transition: color 0.3s ease;
}

footer .elements-social .light li a:hover {
    color: #e958a1;
    opacity: 1;
}

footer .small-icon li {
    margin: 0 15px 0 0;
}

/* Partner Links */
.vy-footer-link-block {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    transition: background-color 0.3s ease;
}

.vy-footer-link-block:hover {
    background: rgba(255, 255, 255, 0.08);
}

.vy-footer-link-logo {
    margin-right: 12px;
    opacity: 0.9;
}

.vy-footer-link-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    font-weight: 500;
}

/* Footer Divider */


/* Footer Copyright & Menu */
footer .pt-25px {
    font-size: 13px !important;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
    letter-spacing: 0.01em;
}

footer .footer-navbar li {
    margin: 0 15px;
    display: inline-block;
}

footer .footer-navbar li a {
    color: rgba(255, 255, 255, 0.5);
    font-size: 13px;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: color 0.3s ease;
}

footer .footer-navbar li a:hover {
    color: #e958a1;
}

/* Responsive Adjustments */
/* Media queries are consolidated at the end of the file */
.footer-navbar li {
    line-height: normal;
}
footer ul li {
    margin-bottom: 2px;
}

.profit-engine-graph{top:100px;}
.profit-engine-facebook{top:50px;}

/* All media queries consolidated below */

/* Large Desktop - max-width: 1299px */
@media (max-width: 1299px) {
    /* Navbar */
    .navbar .navbar-nav .nav-link {
        font-size: 16px;
    }

    /* Header buttons */
    header .btn.btn-rounded.btn-large {
        padding: 10px 15px;
        font-size: 12px;
    }
}

/* Desktop - max-width: 1199px */
@media (max-width: 1199px) {
    /* Review stars */
    .review-star-icon i {
        letter-spacing: 2px;
    }

    /* Newsletter */
    .newsletter-style-02 input {
        padding-left: 15px;
    }

    /* Navbar */
    .navbar .navbar-nav .nav-link {
        font-size: 15px;
    }

    /* Header buttons */
    header .btn.btn-rounded.btn-large {
        padding: 10px 10px;
        font-size: 11px;
    }
}

/* Tablet - max-width: 991px */
@media (max-width: 991px) {
    /* Typography */
    p {
        font-size: 16px; /* Using px instead of rem to ensure consistent rendering */
        line-height: 1.6;
    }

    /* Navbar dropdown */
    .navbar .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu {
        width: calc(100% + 30px) !important;
        margin-left: -15px;
        padding: 10px 45px;
        margin-bottom: 0;
        margin-right: -15px;
    }

    .navbar .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu li:last-child a {
        padding-bottom: 17px;
    }

    [data-mobile-nav-style=full-screen-menu] .navbar-full-screen-menu-inner .navbar-nav .dropdown.dropdown-with-icon-style02 .dropdown-menu li a i {
        font-size: 19px;
    }

    /* Header buttons */
    header .btn.btn-rounded.btn-large {
        color: var(--dark-gray);
        border-color: var(--extra-medium-gray);
        padding: 10px 15px;
        font-size: 13px;
    }

    /* Footer */
    footer {
        padding: 60px 0 20px;
    }

    footer span.alt-font {
        margin-bottom: 12px;
        font-size: 15px;
    }

    footer .pt-25px {
        padding-top: 15px !important;
    }

    .vy-footer-link-block {
        padding: 8px 12px;
    }

    /* Buttons */
    .navbar .btn.btn-transparent-white-light {
        color: var(--dark-gray);
        border-color: rgba(233, 88, 161, 0.3);
        background: rgba(233, 88, 161, 0.05);
    }

    .navbar .btn.btn-transparent-white-light:hover {
        border-color: var(--base-color) !important;
        color: #fff;
        background: var(--base-color) !important;
    }
}

/* Mobile - max-width: 767px */
@media (max-width: 767px) {
    /* Typography */
    p {
        font-size: 15px; /* Minimum recommended size for body text */
        line-height: 1.5;
    }

    /* Footer */
    footer {
        padding: 40px 0 15px;
    }

    footer span.alt-font {
        font-size: 14px;
        margin-bottom: 10px;
    }

    footer p, footer ul li a {
        font-size: 13px;
    }

    footer .footer-navbar li {
        margin: 0 8px;
    }

    footer ul li {
        margin-bottom: 6px;
    }

    .vy-footer-link-block {
        margin-bottom: 10px;
    }

    /* Buttons */
    .navbar .btn.btn-transparent-white-light {
        color: var(--dark-gray);
        border-color: rgba(233, 88, 161, 0.3);
        background: rgba(233, 88, 161, 0.05);
    }

    .navbar .btn.btn-transparent-white-light:hover {
        border-color: var(--base-color);
        color: #fff;
        background: var(--base-color);
    }

	.profit-engine-graph{top:-10px;right:-40px;}
	.profit-engine-facebook{top:-20px;left:-10px;}
}

/* Small Mobile - max-width: 575px */
@media (max-width: 575px) {
    /* Typography - maintain same as 767px */
    p {
        font-size: 15px; /* Maintaining readability on small screens */
        line-height: 1.5;
    }

    /* Header */
    header .header-icon .btn.btn-rounded.btn-small {
        padding-left: 20px;
    }
}
