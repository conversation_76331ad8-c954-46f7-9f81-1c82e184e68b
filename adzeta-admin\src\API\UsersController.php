<?php

namespace AdZetaAdmin\API;

// BaseController already provides $db and $auth (JWTAuth)

class UsersController extends BaseController
{
    protected $db;
    protected $auth;

    public function __construct()
    {
        parent::__construct();
        // $db and $auth are already initialized in BaseController
    }

    /**
     * Get all users (for author dropdown in post editor)
     */
    public function index()
    {
        try {
            // Check authentication
            if (!$this->auth->isLoggedIn()) {
                return $this->error('Unauthorized', 401);
            }

            // Get query parameters
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 50);
            $search = $_GET['search'] ?? '';
            $role = $_GET['role'] ?? '';
            $status = $_GET['status'] ?? '';

            $offset = ($page - 1) * $limit;

            // Build WHERE conditions
            $whereConditions = [];
            $params = [];

            if (!empty($search)) {
                $whereConditions[] = '(name LIKE ? OR email LIKE ? OR username LIKE ?)';
                $searchTerm = "%{$search}%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            if (!empty($role) && $role !== 'all') {
                $whereConditions[] = 'role = ?';
                $params[] = $role;
            }

            if (!empty($status) && $status !== 'all') {
                $whereConditions[] = 'status = ?';
                $params[] = $status;
            }

            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // Get total count
            $countSQL = "SELECT COUNT(*) FROM users $whereClause";
            $totalItems = $this->db->fetchColumn($countSQL, $params);
            $totalPages = ceil($totalItems / $limit);

            // Get users
            $usersSQL = "
                SELECT id, name, email, username, role, status, avatar, bio,
                       created_at, updated_at, last_login,
                       (SELECT COUNT(*) FROM posts WHERE author_id = users.id) as posts_count
                FROM users
                $whereClause
                ORDER BY created_at DESC
                LIMIT $limit OFFSET $offset
            ";

            $users = $this->db->fetchAll($usersSQL, $params);

            // Format users data
            $formattedUsers = array_map(function($user) {
                return [
                    'id' => (int)$user['id'],
                    'name' => $user['name'] ?: $user['username'],
                    'email' => $user['email'],
                    'username' => $user['username'] ?? '',
                    'role' => $user['role'],
                    'status' => $user['status'],
                    'avatar' => $user['avatar'] ?: '/adzeta-admin/assets/images/default-avatar.svg',
                    'bio' => $user['bio'] ?? '',
                    'posts_count' => (int)($user['posts_count'] ?? 0),
                    'created_at' => $user['created_at'],
                    'updated_at' => $user['updated_at'],
                    'last_login' => $user['last_login']
                ];
            }, $users);

            return $this->success([
                'users' => $formattedUsers,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_items' => $totalItems,
                    'per_page' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Users API error: ' . $e->getMessage());
            return $this->error('Failed to fetch users: ' . $e->getMessage());
        }
    }

    /**
     * Get specific user by ID
     */
    public function show($id)
    {
        try {
            if (!$this->auth->isLoggedIn()) {
                return $this->error('Unauthorized', 401);
            }

            $user = $this->db->fetch(
                "SELECT id, name, email, username, role, status, avatar, bio,
                        created_at, updated_at, last_login,
                        (SELECT COUNT(*) FROM posts WHERE author_id = users.id) as posts_count
                 FROM users WHERE id = ?",
                [$id]
            );

            if (!$user) {
                return $this->error('User not found', 404);
            }

            $formattedUser = [
                'id' => (int)$user['id'],
                'name' => $user['name'] ?: $user['username'],
                'email' => $user['email'],
                'username' => $user['username'] ?? '',
                'role' => $user['role'],
                'status' => $user['status'],
                'avatar' => $user['avatar'] ?: '/adzeta-admin/assets/images/default-avatar.svg',
                'bio' => $user['bio'] ?? '',
                'posts_count' => (int)($user['posts_count'] ?? 0),
                'created_at' => $user['created_at'],
                'updated_at' => $user['updated_at'],
                'last_login' => $user['last_login']
            ];

            return $this->success(['user' => $formattedUser]);

        } catch (\Exception $e) {
            error_log('User show API error: ' . $e->getMessage());
            return $this->error('Failed to fetch user: ' . $e->getMessage());
        }
    }

    /**
     * Create new user
     */
    public function store()
    {
        try {
            if (!$this->auth->isLoggedIn()) {
                return $this->error('Unauthorized', 401);
            }

            $input = $this->getRequestData();

            // Validate required fields
            $requiredFields = ['name', 'email', 'password', 'role'];
            foreach ($requiredFields as $field) {
                if (empty($input[$field])) {
                    return $this->error("Field '$field' is required", 400);
                }
            }

            // Check if email already exists
            $existingUser = $this->db->fetch("SELECT id FROM users WHERE email = ?", [$input['email']]);
            if ($existingUser) {
                return $this->error('Email already exists', 400);
            }

            // Create user
            $userId = $this->db->insert('users', [
                'name' => $input['name'],
                'email' => $input['email'],
                'username' => $input['username'] ?? $input['email'],
                'password' => password_hash($input['password'], PASSWORD_DEFAULT),
                'password_hash' => password_hash($input['password'], PASSWORD_DEFAULT), // For compatibility
                'role' => $input['role'],
                'status' => $input['status'] ?? 'active',
                'bio' => $input['bio'] ?? '',
                'avatar' => $input['avatar'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return $this->success([
                'message' => 'User created successfully',
                'user_id' => $userId
            ]);

        } catch (\Exception $e) {
            error_log('User create API error: ' . $e->getMessage());
            return $this->error('Failed to create user: ' . $e->getMessage());
        }
    }

    /**
     * Update user
     */
    public function update($id)
    {
        try {
            if (!$this->auth->isLoggedIn()) {
                return $this->error('Unauthorized', 401);
            }

            $input = $this->getRequestData();

            // Check if user exists
            $user = $this->db->fetch("SELECT id FROM users WHERE id = ?", [$id]);
            if (!$user) {
                return $this->error('User not found', 404);
            }

            // Build update data
            $updateData = [];
            $allowedFields = ['name', 'email', 'username', 'role', 'status', 'bio', 'avatar'];

            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    $updateData[$field] = $input[$field];
                }
            }

            // Handle password update
            if (isset($input['password']) && !empty($input['password'])) {
                $updateData['password'] = password_hash($input['password'], PASSWORD_DEFAULT);
                $updateData['password_hash'] = password_hash($input['password'], PASSWORD_DEFAULT); // For compatibility
            }

            $updateData['updated_at'] = date('Y-m-d H:i:s');

            // Update user
            $this->db->update('users', $updateData, ['id' => $id]);

            return $this->success(['message' => 'User updated successfully']);

        } catch (\Exception $e) {
            error_log('User update API error: ' . $e->getMessage());
            return $this->error('Failed to update user: ' . $e->getMessage());
        }
    }

    /**
     * Delete user
     */
    public function destroy($id)
    {
        try {
            if (!$this->auth->isLoggedIn()) {
                return $this->error('Unauthorized', 401);
            }

            // Check if user exists
            $user = $this->db->fetch("SELECT id FROM users WHERE id = ?", [$id]);
            if (!$user) {
                return $this->error('User not found', 404);
            }

            // Don't allow deleting the current user
            $currentUser = $this->auth->getCurrentUser();
            if ($currentUser && $currentUser['id'] == $id) {
                return $this->error('Cannot delete your own account', 400);
            }

            // Delete user
            $this->db->delete('users', ['id' => $id]);

            return $this->success(['message' => 'User deleted successfully']);

        } catch (\Exception $e) {
            error_log('User delete API error: ' . $e->getMessage());
            return $this->error('Failed to delete user: ' . $e->getMessage());
        }
    }
}
