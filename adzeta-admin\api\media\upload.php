<?php
/**
 * AdZeta Admin Panel - Media Upload API
 * Handles file uploads for the media library
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => 0, 'message' => 'Method not allowed']);
    exit;
}

// Database configuration
$host = 'localhost';
$dbname = 'adzetadb';
$username = 'adzetauser';
$password = 'Crazy1395#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => 0, 'message' => 'Database connection failed']);
    exit;
}

// Configuration
$uploadDir = '../../uploads/media/';
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
$maxFileSize = 10 * 1024 * 1024; // 10MB

// Create upload directory if it doesn't exist
if (!file_exists($uploadDir)) {
    if (!mkdir($uploadDir, 0755, true)) {
        http_response_code(500);
        echo json_encode(['success' => 0, 'message' => 'Failed to create upload directory']);
        exit;
    }
}

// Check if file was uploaded
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    $error = $_FILES['image']['error'] ?? 'No file uploaded';
    echo json_encode(['success' => 0, 'message' => "Upload error: $error"]);
    exit;
}

$file = $_FILES['image'];

// Validate file type
if (!in_array($file['type'], $allowedTypes)) {
    echo json_encode(['success' => 0, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.']);
    exit;
}

// Validate file size
if ($file['size'] > $maxFileSize) {
    echo json_encode(['success' => 0, 'message' => 'File too large. Maximum size is 10MB.']);
    exit;
}

// Generate unique filename
$originalName = pathinfo($file['name'], PATHINFO_FILENAME);
$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
$filename = $originalName . '_' . time() . '_' . uniqid() . '.' . $extension;
$filepath = $uploadDir . $filename;

// Move uploaded file
if (!move_uploaded_file($file['tmp_name'], $filepath)) {
    echo json_encode(['success' => 0, 'message' => 'Failed to save uploaded file']);
    exit;
}

// Get image dimensions
$imageInfo = getimagesize($filepath);
$width = $imageInfo[0] ?? 0;
$height = $imageInfo[1] ?? 0;

// Generate file URL
$fileUrl = '/adzeta-admin/uploads/media/' . $filename;

try {
    // Create media table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS media (
            id INT AUTO_INCREMENT PRIMARY KEY,
            original_filename VARCHAR(255) NOT NULL,
            filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_url VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            width INT DEFAULT 0,
            height INT DEFAULT 0,
            alt_text TEXT,
            caption TEXT,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_filename (filename),
            INDEX idx_uploaded_at (uploaded_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createTableSQL);

    // Insert media record
    $stmt = $pdo->prepare("
        INSERT INTO media (original_filename, filename, file_path, file_url, file_size, mime_type, width, height)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        $file['name'],
        $filename,
        $filepath,
        $fileUrl,
        $file['size'],
        $file['type'],
        $width,
        $height
    ]);

    $mediaId = $pdo->lastInsertId();

    // Return success response in both Editor.js and media library format
    echo json_encode([
        'success' => 1,        // For Editor.js (expects 1)
        'file' => [
            'url' => $fileUrl,
            'size' => $file['size'],
            'name' => $file['name'],
            'extension' => $extension,
            'width' => $width,
            'height' => $height
        ],
        // Additional data for our media library (expects true)
        'media' => [
            'id' => $mediaId,
            'original_filename' => $file['name'],
            'filename' => $filename,
            'file_url' => $fileUrl,
            'file_size' => $file['size'],
            'mime_type' => $file['type'],
            'width' => $width,
            'height' => $height,
            'uploaded_at' => date('Y-m-d H:i:s')
        ]
    ]);

} catch (PDOException $e) {
    // Delete uploaded file if database insert fails
    if (file_exists($filepath)) {
        unlink($filepath);
    }

    http_response_code(500);
    echo json_encode(['success' => 0, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
