<?php
/**
 * AdZeta Admin Bootstrap
 * Initialize admin-specific configurations and autoloading
 */

// Load environment configuration first
require_once __DIR__ . '/config/environment.php';

// Define JWT constants
define('ADZETA_JWT_EXPIRY', 7200); // 2 hours
define('ADZETA_JWT_SECRET', 'adzeta_admin_jwt_secret_key_2025_' . md5('adzeta_admin_panel'));

// Define admin constants
define('ADZETA_ADMIN_PATH', __DIR__);
define('ADZETA_ADMIN_URL', isDevelopment() ? '/adzeta-admin' : '/admin');
define('ADZETA_ADMIN_VERSION', '2.0.0');

// Define base URL using environment config
if (!defined('BASE_URL')) {
    define('BASE_URL', getBaseUrl());
}

// Configure session settings for better compatibility
if (session_status() === PHP_SESSION_NONE) {
    // Set session configuration before starting session
    ini_set('session.cookie_httponly', 0); // Allow JavaScript access for debugging
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_lifetime', 0);
    ini_set('session.gc_maxlifetime', 7200); // 2 hours
    ini_set('session.cookie_path', '/');
    ini_set('session.cookie_domain', '');
    ini_set('session.cookie_secure', 0); // Allow non-HTTPS for localhost

    // Set session name
    session_name('ADZETA_ADMIN_SESSION');

    // Start session
    session_start();

    // Set session started flag if not set
    if (!isset($_SESSION['session_started'])) {
        $_SESSION['session_started'] = time();
    }
}

// Admin-specific configurations
define('ADMIN_UPLOAD_PATH', ADZETA_ADMIN_PATH . '/uploads');
define('ADMIN_CACHE_PATH', ADZETA_ADMIN_PATH . '/cache');
define('ADMIN_LOGS_PATH', ADZETA_ADMIN_PATH . '/logs');

// Create necessary directories
$directories = [
    ADMIN_UPLOAD_PATH,
    ADMIN_CACHE_PATH,
    ADMIN_LOGS_PATH
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Admin autoloader (if composer autoload is not available)
spl_autoload_register(function ($class) {
    if (strpos($class, 'AdZetaAdmin\\') === 0) {
        $file = ADZETA_ADMIN_PATH . '/src/' . str_replace(['AdZetaAdmin\\', '\\'], ['', '/'], $class) . '.php';
        if (file_exists($file)) {
            require_once $file;
        }
    }
});

// Load admin configuration
$adminConfig = [
    'upload_max_size' => '10M',
    'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf', 'doc', 'docx'],
    'image_quality' => 85,
    'enable_webp' => true,
    'cache_duration' => 3600,
    'session_timeout' => 7200,
    'max_login_attempts' => 5,
    'lockout_duration' => 900, // 15 minutes
];

// Store config globally
$GLOBALS['adzeta_admin_config'] = $adminConfig;

// Database configuration for admin panel (from environment)
$adminDbConfig = getDatabaseConfig();

// Initialize database connection for admin panel
try {
    $dsn = "mysql:host={$adminDbConfig['host']};dbname={$adminDbConfig['database']};charset={$adminDbConfig['charset']}";
    $pdo = new PDO($dsn, $adminDbConfig['username'], $adminDbConfig['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ]);

    // Create a simple database wrapper
    $GLOBALS['admin_db'] = new class($pdo) {
        private $pdo;

        public function __construct($pdo) {
            $this->pdo = $pdo;
        }

        public function fetchAll($sql, $params = []) {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        }

        public function fetch($sql, $params = []) {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        }

        public function fetchColumn($sql, $params = []) {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchColumn();
        }

        public function execute($sql, $params = []) {
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($params);
        }

        public function insert($table, $data) {
            $columns = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($data);
        }

        public function update($table, $data, $where, $whereParams = []) {
            $set = [];
            foreach ($data as $key => $value) {
                $set[] = "{$key} = :{$key}";
            }
            $setClause = implode(', ', $set);
            $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute(array_merge($data, $whereParams));
        }

        public function lastInsertId() {
            return $this->pdo->lastInsertId();
        }

        public function getPdo() {
            return $this->pdo;
        }
    };

} catch (PDOException $e) {
    // If database connection fails, create a mock database object
    error_log("Admin database connection failed: " . $e->getMessage());

    $GLOBALS['admin_db'] = new class {
        public function fetchAll($sql, $params = []) { return []; }
        public function fetch($sql, $params = []) { return false; }
        public function fetchColumn($sql, $params = []) { return 0; }
        public function execute($sql, $params = []) { return false; }
        public function insert($table, $data) { return false; }
        public function update($table, $data, $where, $whereParams = []) { return false; }
        public function lastInsertId() { return 0; }
    };
}
