<?php

namespace AdZetaAdmin\API;

/**
 * REST API Router
 * Handles routing for the admin API
 */
class Router
{
    private $routes = [];
    private $middleware = [];

    public function get($path, $handler)
    {
        $this->addRoute('GET', $path, $handler);
    }

    public function post($path, $handler)
    {
        $this->addRoute('POST', $path, $handler);
    }

    public function put($path, $handler)
    {
        $this->addRoute('PUT', $path, $handler);
    }

    public function delete($path, $handler)
    {
        $this->addRoute('DELETE', $path, $handler);
    }

    public function patch($path, $handler)
    {
        $this->addRoute('PATCH', $path, $handler);
    }

    private function addRoute($method, $path, $handler)
    {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler
        ];
    }

    public function addMiddleware($middleware)
    {
        $this->middleware[] = $middleware;
    }

    public function dispatch()
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $this->getPath();



        // Find matching route
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $path)) {
                $params = $this->extractParams($route['path'], $path);

                // Apply middleware
                foreach ($this->middleware as $middleware) {
                    $result = call_user_func($middleware);
                    if ($result !== true) {
                        return $this->sendResponse($result);
                    }
                }

                // Call the handler
                return $this->callHandler($route['handler'], $params);
            }
        }

        // Route not found
        return $this->sendResponse([
            'success' => false,
            'message' => 'Route not found'
        ], 404);
    }

    private function getPath()
    {
        $requestUri = $_SERVER['REQUEST_URI'];

        // Remove query string
        $path = parse_url($requestUri, PHP_URL_PATH);

        // Remove /adzeta-admin/api prefix if present
        if (strpos($path, '/adzeta-admin/api') === 0) {
            $path = substr($path, strlen('/adzeta-admin/api'));
        }
        // Also handle just /api prefix
        elseif (strpos($path, '/api') === 0) {
            $path = substr($path, 4);
        }

        // Ensure path starts with /
        if (!$path || $path[0] !== '/') {
            $path = '/' . ltrim($path, '/');
        }

        // Remove trailing slash (except for root)
        if ($path !== '/' && substr($path, -1) === '/') {
            $path = rtrim($path, '/');
        }

        return $path;
    }

    private function matchPath($routePath, $requestPath)
    {
        // Convert route path to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';

        return preg_match($pattern, $requestPath);
    }

    private function extractParams($routePath, $requestPath)
    {
        $params = [];

        // Extract parameter names from route path
        preg_match_all('/\{([^}]+)\}/', $routePath, $paramNames);

        // Extract parameter values from request path
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $requestPath, $matches)) {
            array_shift($matches); // Remove full match

            foreach ($paramNames[1] as $index => $name) {
                if (isset($matches[$index])) {
                    $params[$name] = $matches[$index];
                }
            }
        }

        return $params;
    }

    private function callHandler($handler, $params = [])
    {
        try {
            if (is_array($handler)) {
                [$class, $method] = $handler;

                // Special handling for controllers that need database dependency
                if ($class === 'AdZetaAdmin\API\ErrorLogsController') {
                    global $admin_db;
                    $instance = new $class($admin_db);
                } else {
                    $instance = new $class();
                }

                $result = call_user_func_array([$instance, $method], $params);
            } else {
                $result = call_user_func_array($handler, $params);
            }

            return $this->sendResponse($result);
        } catch (Exception $e) {
            return $this->sendResponse([
                'success' => false,
                'message' => 'Handler error: ' . $e->getMessage(),
                'error' => ADZETA_DEBUG ? $e->getTraceAsString() : null
            ], 500);
        }
    }

    private function sendResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);

        if (is_array($data) || is_object($data)) {
            echo json_encode($data, JSON_PRETTY_PRINT);
        } else {
            echo $data;
        }

        exit;
    }

    public function getRequestData()
    {
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';

        if (strpos($contentType, 'application/json') !== false) {
            $input = file_get_contents('php://input');
            return json_decode($input, true) ?: [];
        }

        return $_POST;
    }

    public function getQueryParams()
    {
        return $_GET;
    }

    public function getHeaders()
    {
        return getallheaders() ?: [];
    }
}
