/* Paid social page */ 
/* Paid social page */ 



/* Value-Based Bidding Section Styles - Apple-inspired design */
 .vbb-section {
     background: #f5f5f7;
     padding: 100px 0 30px;
     overflow: hidden;
     position: relative;
}
 .vbb-section::before {
     content: '';
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background-image: radial-gradient(circle at 10% 10%, rgba(233, 88, 161, 0.02) 0%, transparent 30%), radial-gradient(circle at 90% 20%, rgba(143, 118, 245, 0.02) 0%, transparent 30%), radial-gradient(circle at 50% 80%, rgba(74, 158, 255, 0.02) 0%, transparent 30%);
     z-index: 0;
}
 .unified-value-card,.vbb-content-container {
     position: relative;
     z-index: 2;
     background: #fffdfc;
     border-radius: 24px;
    /* box-shadow: 0 20px 40px rgba(0, 0, 0, 0.04);
     */
     padding: 50px;
     margin-top: 30px;
    /* border: 1px solid rgba(0, 0, 0, 0.03);
     */
     transition: transform 0.3s ease, box-shadow 0.3s ease;
     overflow: hidden;
}
 .vbb-content-container::before {
     content: '';
     position: absolute;
     top: -15%;
     left: 35%;
     width: 30%;
     height: 100%;
     background-image: url('../images/google-bg-modified.svg');
     background-repeat: no-repeat;
     background-position: top center;
     background-size: contain;
     z-index: 1;
     pointer-events: none;
}
 .vo-section .vbb-content-container::before {
     content: '';
     background-image: url('../images/facebook-bg-modified.svg');
}
 .vbb-content-container::after {
     content: '';
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 70%);
     z-index: 0;
     pointer-events: none;
}
 .vbb-content-container:hover,.unified-value-card:hover {
     transform: translateY(-5px);
    /* box-shadow: 0 25px 50px rgba(0, 0, 0, 0.06);
     */
}
 .vbb-content {
     display: flex;
     flex-direction: column;
     justify-content: center;
     height: 100%;
     padding-right: 30px;
     position: relative;
     z-index: 2;
}
 .vbb-content h2 {
     font-size: 32px;
     font-weight: 700;
     color: #1a1a1a;
     margin-bottom: 30px;
     line-height: 1.2;
     letter-spacing: -0.5px;
}
 .vbb-content p {
     font-size: 15px;
     line-height: 1.7;
     color: #555;
     margin-bottom: 24px;
     font-weight: 400;
     letter-spacing: -0.2px;
}
 .vbb-visual {
     height: 100%;
     position: relative;
     overflow: hidden;
     z-index: 2;
}
 @keyframes cardAppear {
     0% {
         opacity: 0;
         transform: translateY(20px) scale(0.98);
    }
     100% {
         opacity: 1;
         transform: translateY(0) scale(1);
    }
}
 .vbb-comparison {
     width: 100%;
     position: relative;
     z-index: 1;
}
/* Google logo - repositioned for Apple-style */
 .google-logo {
     position: absolute;
     top: 30px;
     left: 40px;
     width: 28px;
     height: 28px;
     opacity: 0.9;
}
/* Bidding strategies layout */
 .bidding-strategies {
     display: flex;
     flex-direction: column;
     width: 100%;
}
 .strategy-row {
     display: flex;
     margin-bottom: 20px;
     align-items: center;
}
 .strategy-label {
     width: 180px;
     font-size: 14px;
     color: #333;
     font-weight: 600;
     text-align: right;
     padding-right: 10px;
     letter-spacing: -0.3px;
     position: relative;
     transition: all 0.3s ease;
     display: flex;
     align-items: center;
     justify-content: flex-end;
     height: 40px;
}
 .strategy-label:after {
     content: '';
     position: absolute;
     top: 50%;
     right: 0;
     width: 6px;
     height: 6px;
     border-radius: 50%;
     transform: translateY(-50%);
     opacity: 1;
}
 .strategy-label.tcpa {
     color: #4a9eff;
     font-weight: 600;
}
 .strategy-label.tcpa:after {
     background: #4a9eff;
}
 .strategy-label.vbb {
     color: #e958a1;
     font-weight: 600;
}
 .strategy-label.vbb:after {
     background: #e958a1;
}
 .customers-row {
     display: flex;
     justify-content: space-between;
     width: calc(100% - 180px);
     margin-left: auto;
}
 .customer-column {
     flex: 1;
     display: flex;
     flex-direction: column;
     align-items: center;
     padding: 0 8px;
     position: relative;
}
 .customer-photo {
     width: 60px;
     height: 60px;
     border-radius: 50%;
     overflow: hidden;
     margin: 0 auto 8px;
     background: #fffdfc;
     display: flex;
     align-items: center;
     justify-content: center;
     position: relative;
     border: 2px solid #fff;
     box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}
 .customer-photo img {
     width: 100%;
     height: 100%;
     object-fit: cover;
     transition: transform 0.5s ease;
}
 .customer-info:hover .customer-photo img {
     transform: scale(1.05);
}
 .customer-info.highlight .customer-photo {
     border: 2px solid rgba(233, 88, 161, 0.3);
}
 .customer-photo i {
     font-size: 24px;
     color: #666;
}
 .customer-info {
     text-align: center;
     margin-bottom: 15px;
     background: #f2f0ee;
     border-radius: 12px;
     padding: 16px 10px 14px;
     transition: all 0.3s ease;
     border: 1px solid rgba(0, 0, 0, 0.03);
     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.02);
     width: 100%;
     position: relative;
     overflow: hidden;
}
 .customer-info:hover {
     transform: translateY(-3px);
     box-shadow: 0 8px 16px rgba(0, 0, 0, 0.04);
}
 .customer-info.highlight {
     background: #fffdfc;
     box-shadow: 0 6px 16px rgba(243, 90, 119, 0.08);
     border: 1px solid rgba(243, 90, 119, 0.1);
}
 .customer-info.highlight:hover {
     box-shadow: 0 10px 20px rgba(243, 90, 119, 0.12);
}
 .customer-column-highlight .bid-box {
     background: #fffdfc;
     box-shadow: 0 4px 12px rgba(243, 90, 119, 0.06);
     border: 1px solid rgba(243, 90, 119, 0.1);
}
 .customer-column-highlight:before {
     content: '';
     position: absolute;
     top: 20px;
     right: 15px;
     width: 24px;
     height: 24px;
     background: #e958a1;
     border-radius: 50%;
     box-shadow: 0 4px 8px rgba(233, 88, 161, 0.3);
     z-index: 2;
     background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'%3E%3C/path%3E%3C/svg%3E");
     background-repeat: no-repeat;
     background-position: center;
     background-size: 14px;
     transform: translateY(-50%);
}
 .customer-title {
     font-size: 15px;
     font-weight: 600;
     color: #1a1a1a;
     margin-bottom: 0;
     letter-spacing: -0.3px;
     line-height: 1.2;
}
 .customer-value {
     font-size: 14px;
     font-weight: 500;
     color: #666;
     letter-spacing: -0.2px;
     line-height: 1.2;
     margin-top: 1px;
}
/* Bid amounts */
 .bid-row {
     display: flex;
     justify-content: space-between;
     width: calc(100% - 180px);
     margin-left: auto;
}
 .bid-box {
     flex: 1;
     background: #f2f0ee;
     border-radius: 12px;
     padding: 16px 10px;
     text-align: center;
     margin: 0 8px;
     font-size: 15px;
     font-weight: 500;
     color: #1a1a1a;
     display: flex;
     align-items: center;
     justify-content: center;
     transition: all 0.3s ease;
     border: 1px solid rgba(0, 0, 0, 0.03);
     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.02);
     width: 100%;
     position: relative;
     overflow: hidden;
}
/* Add subtle gradient to bid boxes */
 .bid-box:before {
     content: '';
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: linear-gradient(180deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
     opacity: 0.8;
     z-index: 0;
     pointer-events: none;
}
/* Make bid label and amount appear above the gradient */
 .bid-label, .bid-amount {
     position: relative;
     z-index: 1;
}
 .bid-box:hover {
     transform: translateY(-3px);
     box-shadow: 0 8px 16px rgba(0, 0, 0, 0.04);
}
 .bid-label {
     font-size: 14px;
     color: #666;
     font-weight: 500;
     margin-right: 6px;
     letter-spacing: -0.2px;
}
 .bid-amount {
     font-weight: 600;
     letter-spacing: -0.3px;
     position: relative;
}
/* Strategy-specific bid box styles */
 .bid-box.lower {
     background: #f2f0ee;
     color: #e958a1;
}
 .bid-box.lower:hover {
     box-shadow: 0 8px 16px rgba(233, 88, 161, 0.08);
}
 .bid-box.lower .bid-amount:after {
     content: '↓';
     display: inline-block;
     margin-left: 3px;
     font-size: 14px;
     animation: pulse 2s infinite;
}
 .bid-box.same {
     background: #f2f0ee;
     color: #1a1a1a;
}
 .bid-box.higher {
     background: #f2f0ee;
     color: #4a9eff;
}
 .bid-box.higher:hover {
     box-shadow: 0 8px 16px rgba(74, 158, 255, 0.08);
}
 .bid-box.higher .bid-amount:after {
     content: '↑';
     display: inline-block;
     margin-left: 3px;
     font-size: 14px;
     animation: pulse 2s infinite;
}
 .bid-box.highlight {
     background: #fffdfc;
     box-shadow: 0 6px 16px rgba(243, 90, 119, 0.06);
     border: 1px solid rgba(243, 90, 119, 0.1);
}
 .bid-box.highlight:hover {
     box-shadow: 0 10px 20px rgba(243, 90, 119, 0.1);
}
 .bid-arrow {
     font-size: 10px;
     margin-left: 5px;
     display: inline-block;
     animation: pulse 2s infinite;
     position: relative;
}
 @keyframes pulse {
     0% {
         opacity: 0.6;
         transform: scale(1) translateY(0);
    }
     50% {
         opacity: 1;
         transform: scale(1.1) translateY(-2px);
    }
     100% {
         opacity: 0.6;
         transform: scale(1) translateY(0);
    }
}
/* Responsive styles */
 @media (max-width: 991px) {
     .vbb-section {
         padding: 80px 0 40px;
    }
     .vbb-content-container {
         padding: 30px;
    }
     .vbb-content-container::before {
         top: -5%;
         left: 25%;
         width: 45%;
    }
     .vbb-content-container::after {
         background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 60%);
    }
     .vbb-content {
         margin-bottom: 40px;
    }
     .vbb-content h2 {
         font-size: 28px;
    }
     .strategy-label {
         width: 140px;
         font-size: 13px;
    }
     .customers-row, .bid-row {
         width: calc(100% - 140px);
    }
}
 @media (max-width: 767px) {
     .vbb-section {
         padding: 50px 0 30px;
    }
     .vbb-content-container {
         padding: 25px;
         margin-top: 20px;
    }
     .vbb-content-container::before {
         top: -5%;
         left: 20%;
         width: 60%;
    }
     .vbb-content-container::after {
         background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 50%);
    }
     .vbb-content h2 {
         font-size: 22px;
    }
     .vbb-content p {
         font-size: 15px;
    }
     .strategy-row {
         flex-direction: column;
         align-items: flex-start;
    }
     .strategy-label {
         width: 100%;
         margin-bottom: 15px;
         text-align: left;
         justify-content: flex-start;
         padding-right: 0;
         padding-left: 15px;
    }
     .strategy-label:after {
         left: 0;
         right: auto;
    }
     .customers-row, .bid-row {
         width: 100%;
    }
     .customer-photo {
         width: 50px;
         height: 50px;
    }
     .customer-title {
         font-size: 13px;
    }
     .customer-value {
         font-size: 12px;
    }
     .bid-box {
         padding: 12px 10px;
         font-size: 13px;
         margin: 0 5px;
    }
     .bid-label {
         font-size: 12px;
    }
     .customer-column-highlight:before {
         top: 0;
         right: 5px;
         width: 20px;
         height: 20px;
         background-size: 12px;
    }
}
 @media (max-width: 575px) {
	 
	 
	 .data-decisions-img-bg.is-google{
		 
		 top:15% !important ;
		scale: 0.6 !important;
        translate: 30% !important;
        opacity: 0.8 !important;
	
	 }
	 
	 
	 .customer-profile-a{
		 
		     top: 4% !important;
			right: 10% !important;
	 }
	 
	  .customer-profile-b{
		 
		     top: 50% !important;
			right: 2% !important;
			
	 }
	 
	 .annotation-pink{top: 25% !important; left: 25% !important;display:none !important;}
.annotation-blue{top: 70% !important; left: 35% !important; display:none !important;}
.annotation-window{top: 30% !important; left: 25% !important;}
	 
     .unified-value-card,.vbb-content-container {
         padding: 20px;
         border-radius: 16px;
    }
     .customer-photo {
         width: 45px;
         height: 45px;
         margin-bottom: 6px;
    }
     .customer-photo i {
         font-size: 18px;
    }
     .customer-info {
         padding: 12px 8px 10px;
         border-radius: 10px;
    }
     .customer-title {
         font-size: 12px;
    }
     .customer-value {
         font-size: 11px;
    }
     .bid-box {
         padding: 12px 8px;
         font-size: 12px;
         margin: 0 3px;
         border-radius: 10px;
    }
     .bid-label {
         font-size: 10px;
         margin-right: 3px;
    }
     .customer-column {
         padding: 0 4px;
    }
}
/* Timeline Graph Styles */
.annotation-pink{top: 25%; left: 25%; position: absolute; z-index: 20;}
.annotation-blue{top: 60%; left: 25%; position: absolute; z-index: 20;}
.annotation-window{top: 40%; left: 10%; position: absolute; z-index: 20;}

 .ltv-timeline-container {
     font-family: 'Inter', sans-serif;
     position: relative;
     min-height: 380px;
     overflow: hidden;
     background-color: transparent;
}
 .timeline-header {
     display: flex;
     align-items: center;
     justify-content: space-between;
     margin-bottom: 25px;
     padding-bottom: 15px;
}
 .timeline-subtitle {
     font-size: 14px;
     color: #555;
     margin-top: 8px;
     line-height: 1.5;
     max-width: 90%;
}
 .timeline-legend {
     display: flex;
     align-items: center;
     gap: 20px;
}
 .legend-item {
     display: flex;
     align-items: center;
     font-size: 11px;
     color: #555;
     margin-bottom: 4px;
}
 .legend-color {
     width: 14px;
     height: 14px;
     border-radius: 4px;
     margin-right: 6px;
}
 .legend-label {
     font-weight: 500;
     white-space: nowrap;
}
 .timeline-legend {
     max-width: 450px;
     margin-left: auto;
}
 .legend-group {
     display: flex;
     flex-direction: column;
     gap: 4px;
}
 .timeline-graph {
     position: relative;
     height: 300px;
     margin-top: 15px;
     margin-bottom: 20px;
     border-bottom: 0;
     background-color: #f9f9fb;
     border-radius: 8px;
     overflow: hidden;
     box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
/* Y-axis */
 .y-axis-timeline {
     position: absolute;
     left: 0;
     top: 0;
     bottom: 40px;
     width: 40px;
     display: flex;
     flex-direction: column;
     justify-content: space-between;
     padding: 15px 0;
     z-index: 5;
     border-right: 1px solid rgba(0,0,0,0.03);
}
 .y-axis-label-timeline {
     font-size: 10px;
     color: #777;
     text-align: right;
     padding-right: 8px;
     font-weight: 500;
}
/* Timeline axis */
 .timeline-axis {
     position: absolute;
     bottom: 0;
     left: 40px;
    /* Account for y-axis */
     width: calc(100% - 40px);
     height: 40px;
     z-index: 5;
     border-top: 1px solid rgba(0,0,0,0.05);
     background-color: rgba(255,255,255,0.8);
     backdrop-filter: blur(5px);
     -webkit-backdrop-filter: blur(5px);
     padding-left: 10px;
    /* Add padding for Day 1 label */
}
 .timeline-marker {
     position: absolute;
     text-align: center;
     font-size: 11px;
     color: #555;
     font-weight: 500;
     width: 60px;
     margin-left: -25px;
    /* Adjusted to prevent cutoff */
     padding: 12px 0 5px;
     top: 0;
}
/* Timeline marker positions are now set inline */
/* Meaningful vertical grid lines */
 .timeline-graph::before {
     content: '';
     position: absolute;
     top: 0;
     left: 40px;
    /* Account for y-axis */
     width: calc(100% - 40px);
     height: calc(100% - 40px);
     pointer-events: none;
    /* These positions match the day markers */
     background-image: linear-gradient(90deg, transparent 5%, rgba(200,200,200,0.08) 5%, rgba(200,200,200,0.08) calc(5% + 1px), transparent calc(5% + 1px)), linear-gradient(90deg, transparent 20%, rgba(200,200,200,0.12) 20%, rgba(200,200,200,0.12) calc(20% + 1px), transparent calc(20% + 1px)), linear-gradient(90deg, transparent 40%, rgba(200,200,200,0.08) 40%, rgba(200,200,200,0.08) calc(40% + 1px), transparent calc(40% + 1px)), linear-gradient(90deg, transparent 65%, rgba(200,200,200,0.08) 65%, rgba(200,200,200,0.08) calc(65% + 1px), transparent calc(65% + 1px)), linear-gradient(90deg, transparent 90%, rgba(200,200,200,0.08) 90%, rgba(200,200,200,0.08) calc(90% + 1px), transparent calc(90% + 1px));
     z-index: 1;
}
/* Meaningful horizontal grid lines at customer profile positions */
 .timeline-graph::after {
     content: '';
     position: absolute;
     top: 0;
     left: 40px;
    /* Account for y-axis */
     width: calc(100% - 40px);
     height: calc(100% - 40px);
     pointer-events: none;
    /* These positions match approximately where the customer profiles are */
     background-image: linear-gradient(0deg, transparent 15%, rgba(200,200,200,0.08) 15%, rgba(200,200,200,0.08) calc(15% + 1px), transparent calc(15% + 1px)), linear-gradient(0deg, transparent 45%, rgba(200,200,200,0.08) 45%, rgba(200,200,200,0.08) calc(45% + 1px), transparent calc(45% + 1px)), linear-gradient(0deg, transparent 65%, rgba(200,200,200,0.08) 65%, rgba(200,200,200,0.08) calc(65% + 1px), transparent calc(65% + 1px));
     z-index: 1;
}
 .timeline-marker::before {
     content: '';
     position: absolute;
     top: -6px;
     left: 50%;
     transform: translateX(-50%);
     height: 6px;
     width: 2px;
     background-color: #999;
}
/* Google's window */
 .google-window {
     position: absolute;
     left: 40px;
    /* Account for y-axis */
     bottom: 40px;
     width: calc(20% - 40px);
    /* 7 days out of ~180, adjusted for y-axis */
     height: calc(100% - 40px);
     background-color: rgba(66, 133, 244, 0.05);
     border-right: 1px dashed rgba(66, 133, 244, 0.4);
     z-index: 1;
}
/* Add a more prominent vertical grid line at day 7 */
 .google-window::after {
     content: '';
     position: absolute;
     top: 0;
     right: -1px;
     width: 1px;
     height: 100%;
     background-color: rgba(66, 133, 244, 0.3);
     z-index: 2;
}
/* Value labels inside Google window */
 .value-label {
     position: absolute;
     left: 10px;
     font-size: 11px;
     font-weight: 500;
     color: #4285F4;
     background-color: rgba(255, 255, 255, 0.9);
     padding: 4px 8px;
     border-radius: 12px;
     z-index: 3;
     box-shadow: 0 1px 3px rgba(0,0,0,0.05);
     opacity: 0.9;
     transition: opacity 0.2s;
}
 .value-label:hover {
     opacity: 1;
}
/* True value labels */
 .true-value-label {
     position: absolute;
     right: 10px;
     font-size: 11px;
     font-weight: 500;
     color: #E83E8C;
     background-color: rgba(255, 255, 255, 0.9);
     padding: 4px 8px;
     border-radius: 12px;
     z-index: 3;
     box-shadow: 0 1px 3px rgba(0,0,0,0.05);
     opacity: 0.9;
     transition: opacity 0.2s;
}
 .true-value-label:hover {
     opacity: 1;
}
 .value-section-label {
     position: absolute;
     padding: 6px 12px;
     font-size: 11px;
     font-weight: 600;
     border-radius: 20px;
     box-shadow: 0 2px 6px rgba(0,0,0,0.08);
     white-space: nowrap;
     display: flex;
     align-items: center;
     z-index: 3;
     opacity: 0.9;
     transition: opacity 0.2s;
}
 .value-section-label:hover {
     opacity: 1;
}
 .window-label {
     position: absolute;
     top: 15px;
     left: 50%;
     transform: translateX(-50%);
     background-color: rgba(66, 133, 244, 0.12);
     color: #4285F4;
     padding: 6px 12px;
     border-radius: 20px;
     font-size: 11px;
     font-weight: 600;
     box-shadow: 0 1px 5px rgba(66, 133, 244, 0.1);
     white-space: nowrap;
     display: flex;
     align-items: center;
     opacity: 0.9;
     transition: opacity 0.2s;
}
 .window-label:hover {
     opacity: 1;
}
 .window-label svg {
     margin-right: 5px;
     min-width: 14px;
     width: 14px;
     height: 14px;
}
/* Customer profiles */
 .customer-profile-a {
     top: 8%;
     right: 8%;
     position: absolute;
     background-color: white;
     border-radius: 12px;
     box-shadow: 0 2px 8px rgba(0,0,0,0.1);
     padding: 8px 12px;
     display: flex;
     align-items: center;
     z-index: 20;
     border: none;
     min-width: 120px;
     transition: transform 0.2s, box-shadow 0.2s;
     cursor: pointer;
}
 .customer-profile-b{
     top: 55%;
     right: 8%;
     position: absolute;
     background-color: white;
     border-radius: 12px;
     box-shadow: 0 2px 8px rgba(0,0,0,0.1);
     padding: 8px 12px;
     display: flex;
     align-items: center;
     z-index: 20;
     border: none;
     min-width: 120px;
     transition: transform 0.2s, box-shadow 0.2s;
     cursor: pointer;
}
 .customer-profile-a:hover,.customer-profile-b:hover {
     transform: translateY(-2px);
     box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}
 .star-highlight {
     position: absolute;
     top: 0;
     right: 0;
     transform: translate(40%, -40%);
     width: 20px;
     height: 20px;
     background: #e958a1;
     border-radius: 50%;
     box-shadow: 0 2px 6px rgba(233, 88, 161, 0.3);
     z-index: 2;
     display: flex;
     align-items: center;
     justify-content: center;
}
 .profile-image {
     width: 32px;
     height: 32px;
     border-radius: 50%;
     overflow: hidden;
     margin-right: 8px;
     flex-shrink: 0;
}
 .profile-image img {
     width: 100%;
     height: 100%;
     object-fit: cover;
}
 .profile-content {
     flex-grow: 1;
}
/* Dollar sign indicators */
 .dollar-indicator {
     display: flex;
     margin-top: 2px;
     font-size: 12px;
     font-weight: 600;
}
 .dollar-sign {
     margin-right: 1px;
}
 .dollar-sign.highlight {
     opacity: 1;
}
 .dollar-sign.fade {
     opacity: 0.3;
}
 .profile-name {
     font-size: 12px;
     font-weight: 600;
     line-height: 1.2;
     color: #333;
     white-space: nowrap;
}
 .profile-type {
     font-size: 11px;
     font-weight: 500;
     line-height: 1.2;
     white-space: nowrap;
}
/* Profile tooltip */
 .profile-tooltip {
     position: absolute;
     background-color: white;
     border-radius: 8px;
     padding: 10px;
     box-shadow: 0 4px 15px rgba(0,0,0,0.1);
     font-size: 12px;
     color: #444;
     z-index: 10;
     opacity: 0;
     visibility: hidden;
     transition: opacity 0.2s, visibility 0.2s;
     pointer-events: none;
     width: 180px;
}
 .customer-profile:hover .profile-tooltip {
     opacity: 1;
     visibility: visible;
}
 .tooltip-title {
     font-weight: 600;
     margin-bottom: 5px;
     font-size: 13px;
}
 .tooltip-value {
     display: flex;
     justify-content: space-between;
     margin-bottom: 3px;
}
 .tooltip-label {
     color: #666;
}
 .tooltip-amount {
     font-weight: 500;
}
/* Value curves */
 .value-curve {
     position: absolute;
     bottom: 40px;
     left: 40px;
    /* Account for y-axis */
     width: calc(100% - 40px);
     height: calc(100% - 40px);
     z-index: 2;
}
/* Animation for path drawing */
 @keyframes drawLine {
     0% {
         stroke-dashoffset: 1000;
    }
     100% {
         stroke-dashoffset: 0;
    }
}
 .value-curve path {
     stroke-dasharray: 1000;
     stroke-dashoffset: 1000;
     animation: drawLine 1.5s ease-in-out forwards;
}
/* Stagger animations for different paths */
 .value-curve path:nth-child(2) {
     animation-delay: 0.2s;
}
 .value-curve path:nth-child(3) {
     animation-delay: 0.4s;
}
 .value-curve path:nth-child(4) {
     animation-delay: 0.6s;
}
 .value-curve path:nth-child(5) {
     animation-delay: 0.8s;
}
 .value-curve path:nth-child(6) {
     animation-delay: 1s;
}
/* Annotations */
 .annotation {
     position: absolute;
     z-index: 4;
     opacity: 0.85;
     transition: opacity 0.2s;
}
 .annotation:hover {
     opacity: 1;
}
 .annotation-text {
     display: flex;
     align-items: center;
     max-width: 180px;
     border-radius: 16px;
     padding: 6px 12px;
     font-size: 11px;
     font-weight: 500;
     border: none;
     z-index: 10;
     box-shadow: 0 1px 5px rgba(0,0,0,0.08);
}
 .annotation-text svg {
     min-width: 14px;
     width: 14px;
     height: 14px;
     margin-right: 5px;
}
 .annotation-blue {
     background-color: rgba(66, 133, 244, 0.12);
     color: #4285F4;
     box-shadow: 0 1px 5px rgba(66, 133, 244, 0.1);
}
 .annotation-pink {
     background-color: rgba(232, 62, 140, 0.12);
     color: #E83E8C;
     box-shadow: 0 1px 5px rgba(232, 62, 140, 0.1);
}
/* Insight box */
 .timeline-insights {
     display: flex;
     justify-content: space-between;
     margin-top: 30px;
     gap: 20px;
}
 .insight-box {
     flex: 1;
     background-color: white;
     border-radius: 16px;
     padding: 18px;
     box-shadow: 0 2px 10px rgba(0,0,0,0.03);
     border: 1px solid rgba(0,0,0,0.03);
}
 .insight-title {
     font-weight: 600;
     font-size: 13px;
     color: #333;
     margin-bottom: 8px;
     display: flex;
     align-items: center;
}
 .insight-desc {
     font-size: 12px;
     color: #666;
     line-height: 1.5;
}
 .insight-google {
     border-top: 2px solid #4285F4;
}
 .insight-adzeta {
     border-top: 2px solid #ff8cc6;
}
/* Adzeta branding */
 .adzeta-logo {
     position: absolute;
     bottom: 5px;
     right: 15px;
     opacity: 0.8;
     width: 60px;
}
/* Timeline axis styles */
 .timeline-axis {
     position: absolute;
     bottom: 0;
     left: 0;
     width: 100%;
     height: 40px;
     display: flex;
     align-items: center;
     border-top: 1px solid rgba(0,0,0,0.1);
     background-color: rgba(255,255,255,0.9);
     z-index: 2;
}
 .timeline-marker {
     position: absolute;
     text-align: center;
     font-size: 12px;
     font-weight: 500;
     color: #555;
     padding: 5px 0;
}
 .timeline-marker:not(:first-child) {
     transform: translateX(-50%);
}
 .timeline-marker::before {
     content: '';
     display: block;
     width: 1px;
     height: 5px;
     background-color: rgba(0,0,0,0.2);
     margin: 0 auto 4px;
}
 .vertical-grid-line {
     position: absolute;
     top: 0;
     width: 1px;
     height: calc(100% - 40px);
     background-color: rgba(0,0,0,0.03);
     z-index: 1;
}
/* Detailed Customer Journey Visualization */
 .detailed-visualization {
     margin-top: 40px;
     background-color: white;
     border-radius: 16px;
     box-shadow: 0 10px 30px rgba(0,0,0,0.06);
     padding: 30px;
     position: relative;
     overflow: hidden;
}
 .detailed-visualization-header {
     margin-bottom: 30px;
     text-align: center;
}
 .detailed-visualization-title {
     font-size: 22px;
     font-weight: 600;
     color: #333;
     margin-bottom: 10px;
     letter-spacing: -0.5px;
}
 .detailed-visualization-subtitle {
     font-size: 15px;
     color: #666;
     max-width: 600px;
     margin: 0 auto;
     line-height: 1.5;
}
 .journey-chart {
     height: 380px;
     position: relative;
     margin-top: 30px;
     margin-bottom: 20px;
}
 .journey-chart-container {
     position: relative;
     height: 100%;
     width: 100%;
}
 .y-axis {
     position: absolute;
     left: 0;
     top: 0;
     height: 100%;
     width: 60px;
     display: flex;
     flex-direction: column;
     justify-content: space-between;
     padding: 10px 0;
}
 .y-axis-label {
     font-size: 13px;
     color: #777;
     text-align: right;
     padding-right: 15px;
     font-weight: 500;
}
 .x-axis {
     position: absolute;
     bottom: 0;
     left: 60px;
     right: 0;
     height: 40px;
     display: flex;
}
 .x-axis-label {
     position: absolute;
     font-size: 13px;
     color: #777;
     text-align: center;
     font-weight: 500;
     transform: translateX(-50%);
}
 .x-axis-label::before {
     content: '';
     position: absolute;
     top: -10px;
     left: 50%;
     transform: translateX(-50%);
     height: 6px;
     width: 1px;
     background-color: #aaa;
}
 .grid-lines {
     position: absolute;
     top: 0;
     left: 60px;
     right: 0;
     bottom: 40px;
     z-index: 1;
}
 .horizontal-grid-line {
     position: absolute;
     left: 0;
     right: 0;
     height: 1px;
     background-color: rgba(0,0,0,0.04);
}
 .vertical-grid-line-detailed {
     position: absolute;
     top: 0;
     bottom: 0;
     width: 1px;
     background-color: rgba(0,0,0,0.04);
}
 .google-window-overlay {
     position: absolute;
     top: 0;
     bottom: 0;
     background-color: rgba(66, 133, 244, 0.06);
     border-right: 2px dashed rgba(66, 133, 244, 0.3);
     z-index: 2;
}
 .google-window-label {
     position: absolute;
     top: 15px;
     left: 50%;
     transform: translateX(-50%);
     background-color: rgba(66, 133, 244, 0.12);
     color: #4285F4;
     border-radius: 20px;
     padding: 6px 12px;
     font-size: 12px;
     font-weight: 500;
     box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
     white-space: nowrap;
     display: flex;
     align-items: center;
     z-index: 3;
}
 .customer-journey {
     position: absolute;
     top: 0;
     left: 60px;
     right: 0;
     bottom: 40px;
     z-index: 3;
}
 .journey-line {
     position: absolute;
     stroke-width: 4;
     fill: none;
}
 .journey-point {
     position: absolute;
     width: 14px;
     height: 14px;
     border-radius: 50%;
     transform: translate(-50%, -50%);
     z-index: 4;
     cursor: pointer;
     box-shadow: 0 2px 6px rgba(0,0,0,0.2);
     transition: transform 0.2s, box-shadow 0.2s;
}
 .journey-point:hover {
     transform: translate(-50%, -50%) scale(1.2);
     box-shadow: 0 3px 10px rgba(0,0,0,0.25);
}
 .journey-point-label {
     position: absolute;
     background-color: white;
     border-radius: 8px;
     padding: 8px 12px;
     font-size: 12px;
     box-shadow: 0 4px 12px rgba(0,0,0,0.15);
     pointer-events: none;
     opacity: 0;
     transition: opacity 0.2s, transform 0.2s;
     z-index: 5;
     white-space: nowrap;
     transform: translateY(5px);
}
 .journey-point:hover .journey-point-label {
     opacity: 1;
     transform: translateY(0);
}
 .customer-legend {
     display: flex;
     justify-content: center;
     margin-top: 30px;
     flex-wrap: wrap;
     gap: 30px;
}
 .legend-item {
     display: flex;
     align-items: center;
}
 .legend-color {
     width: 24px;
     height: 4px;
     margin-right: 10px;
     border-radius: 2px;
}
 .legend-label {
     font-size: 14px;
     color: #444;
     font-weight: 500;
}
 .key-insight-box {
     position: absolute;
     top: 50%;
     right: 25px;
     transform: translateY(-50%);
     max-width: 220px;
     background-color: rgba(232, 62, 140, 0.06);
     border-radius: 12px;
     padding: 18px;
     box-shadow: 0 4px 15px rgba(0,0,0,0.05);
     z-index: 10;
}
 .key-insight-title {
     font-weight: 600;
     font-size: 15px;
     color: #E83E8C;
     margin-bottom: 10px;
     display: flex;
     align-items: center;
}
 .key-insight-content {
     font-size: 13px;
     color: #555;
     line-height: 1.6;
}
 .challenge-card {
     background: white;
     border-radius: 16px;
     padding: 30px;
     box-shadow: 0 8px 30px rgba(0,0,0,0.08);
     border: 1px solid rgba(143, 118, 245, 0.1);
     transition: all 0.3s ease;
     position: relative;
     overflow: hidden;
}
 .challenge-card:hover {
     transform: translateY(-5px);
     box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}
 .challenge-icon {
     margin-bottom: 20px;
}
 .challenge-icon i {
     font-size: 32px;
     background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
     -webkit-background-clip: text;
     background-clip: text;
     -webkit-text-fill-color: transparent;
}
 .challenge-title {
     font-size: 22px;
     font-weight: 600;
     margin-bottom: 15px;
     color: #333;
}
 .challenge-description {
     font-size: 16px;
     line-height: 1.6;
     color: #555;
     margin-bottom: 20px;
}
 .challenge-highlight {
     background: rgba(233, 88, 161, 0.05);
     border-radius: 12px;
     padding: 15px;
     display: flex;
     align-items: flex-start;
     gap: 12px;
}
 .highlight-icon {
     flex-shrink: 0;
     color: #e958a1;
     font-size: 20px;
     margin-top: 2px;
}
 .challenge-highlight p {
     margin: 0;
     font-size: 15px;
     color: #666;
     font-style: italic;
}
 @media (max-width: 991px) {
     .challenge-card {
         margin-bottom: 30px;
    }
}
 .data-decisions-section {
     position: relative;
     overflow: hidden;
}
 .text-white-transparent {
     color: rgba(255, 255, 255, 0.7);
}
/* Animated card with gradient border */
 .animated-card {
     --border-width: 2px;
     --radius: 16px;
     --angle: 0turn;
     --bg-color: transparent;
     position: relative;
     border-radius: var(--radius);
     border: var(--border-width) solid transparent;
     background-color: var(--bg-color);
     isolation: isolate;
     padding: 0;
     width: 100%;
}
 .animated-card::before {
     content: " ";
     position: absolute;
     inset: calc(var(--border-width) * -1);
     z-index: -1;
     border: inherit;
     border-radius: inherit;
     background-image: conic-gradient(from var(--angle), #2D1E4A 80%, #f45888 88%, #8f76f5 92%, #2D1E4A 100%);
     background-origin: border-box;
     -webkit-mask: linear-gradient(black, black) content-box, linear-gradient(black, black);
     mask: linear-gradient(black, black) content-box, linear-gradient(black, black);
     -webkit-mask-composite: xor;
     mask-composite: exclude;
     animation: rotate-gradient 4s linear infinite;
}
 @property --angle {
     syntax: " <angle> ";
     inherits: true;
     initial-value: 0turn;
}
 @keyframes rotate-gradient {
     to {
         --angle: 1turn;
    }
}
/* Fallback for browsers that don't support @property */
 @supports not (background: conic-gradient(from var(--angle), #3a1e5e 80%, #e958a1 88%)) {
     .animated-card::before {
         background-image: conic-gradient(#2D1E4A 80%, #f45888 88%, #8f76f5 92%, #2D1E4A 100%);
         animation: none;
    }
}
 .data-decisions-items-wrapper {
     position: relative;
     z-index: 2;
     max-width: 1200px;
     margin: 0 auto;
}
 .data-decisions-item {
     background: rgba(255, 255, 255, 0.8);
     backdrop-filter: blur(10px);
     -webkit-backdrop-filter: blur(10px);
     border-radius: 16px;
     padding: 30px;
     position: relative;
     transition: all 0.3s ease;
     box-shadow: 0 4px 20px rgba(0,0,0,0.05), 0 0 15px rgba(143, 118, 245, 0.1);
     overflow: hidden;
     width: 100%;
     display: block;
     text-decoration: none;
     color: inherit;
     border: 1px solid rgba(143, 118, 245, 0.1);
}
 .data-decisions-item:hover {
     transform: translateY(-5px);
     box-shadow: 0 10px 30px rgba(0,0,0,0.1), 0 0 20px rgba(143, 118, 245, 0.2);
     text-decoration: none;
     color: inherit;
}
 .data-decisions-item.is-dark {
     background: linear-gradient(135deg, #1E1A33 0%, #2D1E4A 100%);
     border: 1px solid rgba(143, 118, 245, 0.2);
     box-shadow: 0 4px 20px rgba(0,0,0,0.15), 0 0 20px rgba(143, 118, 245, 0.15);
}
 .data-decisions-item.is-dark:hover {
     box-shadow: 0 10px 30px rgba(0,0,0,0.2), 0 0 25px rgba(143, 118, 245, 0.25);
}
 .data-decisions-item-header {
     display: flex;
     align-items: center;
     gap: 15px;
     margin-bottom: 15px;
}
 .data-decisions-item-icon {
     flex-shrink: 0;
}
 .data-decisions-item-h3 {
     font-size: 22px;
     font-weight: 600;
     color: #2e2e3c;
     margin: 0;
}
 .data-decisions-item-h3.is-white {
     color: #ffffff;
}
 .data-decisions-item-p {
     font-size: 16px;
     line-height: 1.6;
     color: #555;
     margin-bottom: 0;
}
 .data-decisions-item-p.is-white {
     color: rgba(255,255,255,0.8);
}
 .data-decisions-item-arrow {
     position: absolute;
     bottom: 20px;
     right: 20px;
     color: #2e2e3c;
     opacity: 0.5;
     transition: all 0.3s ease;
}
 .data-decisions-item.is-dark .data-decisions-item-arrow {
     color: #ffffff;
}
 .data-decisions-item:hover .data-decisions-item-arrow {
     opacity: 1;
     transform: translate(3px, -3px);
}
 .data-decisions-img-bg {
     position: absolute;
     z-index: 1;
     opacity: 0.9;
     pointer-events: none;
}
 .data-decisions-img-bg.is-google {
     top: 360px;
     right: 130px;
     width: 360px;
     height: auto;
}
 .data-decisions-img-bg.is-adzeta {
     bottom: -200px;
     left: -200px;
     width: 600px;
     height: auto;
}
/* Decorative dots */
 .data-decisions-dots {
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background-image: radial-gradient(circle at 10% 20%, #4af 1px, transparent 1px), radial-gradient(circle at 20% 40%, #4af 1px, transparent 1px), radial-gradient(circle at 30% 30%, #4af 1px, transparent 1px), radial-gradient(circle at 40% 70%, #4af 1px, transparent 1px), radial-gradient(circle at 60% 10%, #4af 1px, transparent 1px), radial-gradient(circle at 70% 30%, #4af 1px, transparent 1px), radial-gradient(circle at 80% 60%, #4af 1px, transparent 1px), radial-gradient(circle at 90% 40%, #4af 1px, transparent 1px), radial-gradient(circle at 95% 70%, #4af 1px, transparent 1px);
     background-size: 800px 800px;
     background-repeat: repeat;
     opacity: 0.3;
     z-index: 1;
     pointer-events: none;
}
 @media (min-width: 992px) {
     .data-decisions-item {
         display: flex;
         flex-direction: column;
    }
     .data-decisions-item-p {
         flex-grow: 1;
         margin-bottom: 30px;
    }
    /* Add more spacing between cards */
     .data-decisions-items-wrapper .row {
         margin-bottom: 40px;
    }
    /* Adjust positioning for staggered effect */
     .data-decisions-items-wrapper .row:nth-child(1) {
         margin-right: 15%;
    }
     .data-decisions-items-wrapper .row:nth-child(3) {
         margin-left: 15%;
    }
}
 @media (max-width: 991px) {
     .data-decisions-section {
         padding: 60px 0;
    }
     .data-decisions-item {
         padding: 25px;
    }
     .data-decisions-items-wrapper .row {
         margin-bottom: 30px;
    }
    /* Center all cards on mobile */
     .data-decisions-items-wrapper .row {
         justify-content: center !important;
    }
     .data-decisions-item-h3 {
         font-size: 20px;
    }
     .data-decisions-img-bg {
         opacity: 0.3;
    }
}
 @media (max-width: 767px) {
     .data-decisions-section {
         padding: 40px 0;
    }
     .data-decisions-items-wrapper .row {
         margin-bottom: 20px;
    }
     .data-decisions-item-h3 {
         font-size: 18px;
    }
     .data-decisions-item-p {
         font-size: 15px;
    }
     .data-decisions-img-bg {
         opacity: 0.2;
    }
}
 @keyframes floatAnimation{
     0%{
         transform:translateY(0) 
    }
     50%{
         transform:translateY(-15px) 
    }
     100%{
         transform:translateY(0) 
    }
}
 @keyframes fadeInAnimation{
     0%{
         opacity:0;
         transform:scale(.95) 
    }
     100%{
         opacity:1;
         transform:scale(1) 
    }
}
 @media (max-width:991px){
     .ad-networks-wrapper{
         height:auto!important;
         display:none 
    }
}
 @media (min-width:992px){
     .ad-platform-card{
         width:420px;
         z-index:3 
    }
}
 .card-inner{
     transition:transform .3s ease,border-color .3s ease;
     border:1px solid rgba(0,0,0,.05) 
}
 .card-inner:hover{
     transform:translateY(-5px);
     border-color:rgba(233,88,161,.1) 
}
 .border-radius-24px{
     border-radius:24px!important 
}
 .platform-circle{
     animation:gentlePulse 4s infinite ease-in-out;
     filter:drop-shadow(0 0 3px rgba(233, 88, 161, .3)) 
}
 @keyframes gentlePulse{
     0%{
         transform:scale(1);
         opacity:.9 
    }
     50%{
         transform:scale(1.02);
         opacity:1 
    }
     100%{
         transform:scale(1);
         opacity:.9 
    }
}
 @keyframes pulseFade{
     0%{
         opacity:0;
         transform:scale(.8) 
    }
     50%{
         opacity:.5;
         transform:scale(1.1) 
    }
     100%{
         opacity:0;
         transform:scale(1.2) 
    }
}
/** * Animated Gradient Border * Based on the Bejamas Experiments implementation */
/* High-tech animated border with masking technique */
 .high-tech-border {
     --border-width: 1px;
     --radius: 20px;
     --angle: 0turn;
     position: relative;
     border-radius: var(--radius);
     border: var(--border-width) solid transparent;
     isolation: isolate;
}
 .high-tech-border::before {
     content: "";
     position: absolute;
     inset: calc(var(--border-width) * -1);
     z-index: -1;
     border: inherit;
     border-radius: inherit;
     background-image: conic-gradient(from var(--angle), #e958a1, #ff5d74, #8f76f5, #e958a1);
     background-origin: border-box;
     -webkit-mask: linear-gradient(#000, #000), linear-gradient(#000, #000);
     mask: linear-gradient(#000, #000), linear-gradient(#000, #000);
     -webkit-mask-clip: content-box, border-box;
     mask-clip: content-box, border-box;
     -webkit-mask-composite: xor;
     mask-composite: exclude;
     animation: rotate-gradient 4s linear infinite;
}
/* Animation for rotating gradient */
 @keyframes rotate-gradient {
     to {
         --angle: 1turn;
    }
}
/* Fallback for browsers that don't support @property */
 @supports not (background: conic-gradient(from var(--angle), #e958a1, #ff5d74)) {
     .high-tech-border::before {
         background-image: conic-gradient(#e958a1, #ff5d74, #8f76f5, #e958a1);
         animation: rotate-fallback 4s linear infinite;
    }
     @keyframes rotate-fallback {
         0% {
             transform: rotate(0deg);
        }
         100% {
             transform: rotate(360deg);
        }
    }
}
/* Define the --angle property for browsers that support CSS Houdini */
 @property --angle {
     syntax: "<angle>";
     inherits: true;
     initial-value: 0turn;
}
/* Ensure content is positioned correctly */
 .high-tech-border > * {
     position: relative;
     z-index: 1;
}
/* Add subtle glow effect */
 .high-tech-border {
     box-shadow: 0 0 15px rgba(233, 88, 161, 0.2);
}
/* Solution card specific styles */
 .solution-card {
     background: rgba(255, 255, 255, 0.30);
     backdrop-filter: blur(20px);
     -webkit-backdrop-filter: blur(20px);
     transition: transform 0.3s ease, box-shadow 0.3s ease;
     position: relative;
     z-index: 1;
}
 .solution-card:hover {
     transform: translateY(-4px);
}
/* Proven Results Section Styles */
 .proven-results-section {
     position: relative;
     overflow: hidden;
}
/* Metric Cards */
 .compact-metric-card {
     background-color: #ffffff;
     border-radius: 16px;
     padding: 25px 15px;
     transition: all 0.3s ease;
     border: 1px solid rgba(143, 118, 245, 0.1);
     box-shadow: 0 4px 20px rgba(0,0,0,0.05);
}
 .compact-metric-card:hover {
     transform: translateY(-5px);
     box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}
 .metric-icon {
     margin-bottom: 15px;
}
 .metric-icon i {
     font-size: 32px;
}
 .counter {
     font-size: 42px;
     font-weight: 700;
     line-height: 1;
}
 .counter-suffix {
     font-size: 24px;
     font-weight: 600;
     vertical-align: top;
     margin-left: 2px;
     background: linear-gradient(to right, #e958a1, #ff5d74);
     -webkit-background-clip: text;
     background-clip: text;
     -webkit-text-fill-color: transparent;
}
 .metric-title {
     font-size: 18px;
     font-weight: 600;
     margin-bottom: 10px;
     color: #333;
}
 .metric-description {
     font-size: 14px;
     color: #666;
     line-height: 1.5;
     margin-bottom: 0;
}
/* Trust Indicators Section */
 .trust-indicators-container {
     display: flex;
     flex-wrap: wrap;
     justify-content: center;
     gap: 30px;
     background: rgba(255, 255, 255, 0.8);
     padding: 30px 20px;
     border-radius: 16px;
     box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
     border: 1px solid rgba(143, 118, 245, 0.1);
     backdrop-filter: blur(8px);
}
 .trust-item {
     display: flex;
     flex-direction: column;
     align-items: center;
     max-width: 260px;
     text-align: center;
}
 .partner-badge-wrapper {
     display: flex;
     flex-direction: column;
     align-items: center;
     position: relative;
}
 .partner-badge-container {
     position: relative;
     text-align: center;
     margin-bottom: 10px;
}
 .partner-badge {
     height: 70px;
     transition: all 0.3s ease;
}
 .partner-badge:hover {
     transform: scale(1.05);
}
 .badge-tooltip {
     position: absolute;
     bottom: 120%;
     left: 50%;
     transform: translateX(-50%);
     background: rgba(0, 0, 0, 0.85);
     color: #fff;
     padding: 8px 12px;
     border-radius: 6px;
     font-size: 12px;
     width: 220px;
     opacity: 0;
     visibility: hidden;
     transition: 0.3s ease;
     z-index: 10;
}
 .badge-tooltip::after {
     content: '';
     position: absolute;
     top: 100%;
     left: 50%;
     transform: translateX(-50%);
     border: 8px solid transparent;
     border-top-color: rgba(0, 0, 0, 0.85);
}
 .partner-badge-container:hover .badge-tooltip {
     opacity: 1;
     visibility: visible;
}
 .verification-text {
     display: flex;
     align-items: center;
     font-size: 14px;
     color: #333;
     font-weight: 500;
}
 .verification-icon {
     color: #27AE60;
     margin-right: 5px;
}
 .certification-badge, .verification-badge {
     background: #fff;
     padding: 12px 16px;
     border-radius: 12px;
     border: 1px solid rgba(143, 118, 245, 0.1);
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
     display: flex;
     align-items: center;
     gap: 12px;
}
 .certification-icon, .verification-icon {
     display: flex;
     align-items: center;
     justify-content: center;
     width: 40px;
     height: 40px;
     border-radius: 50%;
     font-size: 18px;
     background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
     color: #fff;
}
 .certification-text, .verification-text {
     display: flex;
     flex-direction: column;
}
 .certification-title, .verification-title {
     font-weight: 600;
     font-size: 14px;
     color: #333;
}
 .certification-detail, .verification-detail {
     font-size: 12px;
     color: #666;
}
 .results-disclaimer {
     font-size: 12px;
     color: #777;
     font-style: italic;
}
/* Section Tag */
 .section-tag {
     display: inline-block;
     margin-bottom: 10px;
}
/* Case Study Cards */
 .case-study-card {
     background-color: #ffffff;
     border-radius: 16px;
     overflow: hidden;
     transition: all 0.3s ease;
     border: 1px solid rgba(143, 118, 245, 0.1);
     height: 100%;
     display: flex;
     flex-direction: column;
     box-shadow: 0 4px 20px rgba(0,0,0,0.05);
}
 .case-study-card:hover {
     transform: translateY(-5px);
     box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}
 .case-study-header {
     padding: 20px;
     border-bottom: 1px solid #f0f0f0;
     text-align: center;
     position: relative;
}
 .industry-tag {
     position: absolute;
     top: 15px;
     left: 15px;
     background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
     color: white;
     font-size: 11px;
     font-weight: 600;
     padding: 4px 10px;
     border-radius: 20px;
     letter-spacing: 0.5px;
}
 .client-logo {
     height: 40px;
     max-width: 80%;
     margin-top: 10px;
}
 .case-study-content {
     padding: 25px;
     flex-grow: 1;
}
 .result-highlight {
     display: inline-block;
     margin-bottom: 15px;
     padding: 8px 15px;
     background-color: rgba(143, 118, 245, 0.1);
     border-radius: 8px;
     text-align: center;
}
 .highlight-number {
     display: block;
     font-size: 24px;
     font-weight: 700;
     background: linear-gradient(to right, #e958a1, #ff5d74);
     -webkit-background-clip: text;
     background-clip: text;
     -webkit-text-fill-color: transparent;
     line-height: 1.2;
}
 .highlight-text {
     display: block;
     font-size: 12px;
     color: #666;
     font-weight: 500;
}
 .case-study-title {
     font-size: 20px;
     font-weight: 600;
     margin-bottom: 15px;
     color: #333;
     line-height: 1.3;
}
 .case-study-text {
     font-size: 15px;
     color: #555;
     line-height: 1.6;
     margin-bottom: 20px;
     font-style: italic;
}
 .case-study-attribution {
     display: flex;
     align-items: center;
     margin-top: 20px;
}
 .attribution-photo {
     width: 40px;
     height: 40px;
     border-radius: 50%;
     overflow: hidden;
     margin-right: 12px;
     border: 2px solid rgba(143, 118, 245, 0.3);
}
 .attribution-photo img {
     width: 100%;
     height: 100%;
     object-fit: cover;
}
 .attribution-info {
     display: flex;
     flex-direction: column;
}
 .attribution-name {
     font-weight: 600;
     font-size: 14px;
     color: #333;
}
 .attribution-title {
     font-size: 12px;
     color: #666;
}
 .case-study-link {
     display: flex;
     justify-content: space-between;
     align-items: center;
     padding: 15px 25px;
     background-color: #f8f9fa;
     color: #333;
     font-weight: 600;
     text-decoration: none;
     border-top: 1px solid #f0f0f0;
     transition: all 0.3s ease;
     margin-top: auto;
}
 .case-study-link i {
     transition: transform 0.3s ease;
}
 .case-study-link:hover {
     background: linear-gradient(135deg, rgba(244, 88, 136, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
     color: #f45888;
}
 .case-study-link:hover i {
     transform: translateX(5px);
}
/* Responsive adjustments */
 @media (max-width: 991px) {
     .compact-metric-card, .case-study-card {
         margin-bottom: 30px;
    }
     .trust-indicators-container {
         padding: 15px;
         gap: 20px;
    }
     .certification-badge, .verification-badge {
         padding: 8px 12px;
    }
}
 @media (max-width: 767px) {
     .counter {
         font-size: 36px;
    }
     .counter-suffix {
         font-size: 20px;
    }
     .metric-title {
         font-size: 16px;
    }
     .case-study-title {
         font-size: 18px;
    }
     .trust-indicators-container {
         flex-direction: column;
         align-items: flex-start;
         gap: 15px;
    }
     .partner-badge-wrapper {
         align-items: flex-start;
         width: 100%;
    }
     .certification-badge, .verification-badge {
         width: 100%;
    }
     .result-highlight {
         padding: 6px 12px;
    }
     .highlight-number {
         font-size: 20px;
    }
     .highlight-text {
         font-size: 11px;
    }
}
 @media (max-width: 575px) {
     .trust-indicators-container {
         padding: 12px;
    }
     .partner-badge {
         height: 60px;
    }
     .certification-icon, .verification-icon {
         width: 30px;
         height: 30px;
         font-size: 16px;
    }
     .certification-title, .verification-title {
         font-size: 13px;
    }
     .certification-detail, .verification-detail {
         font-size: 11px;
    }
     .case-study-content {
         padding: 20px;
    }
     .case-study-text {
         font-size: 14px;
    }
     .case-study-link {
         padding: 12px 20px;
    }
}
 