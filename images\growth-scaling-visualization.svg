<?xml version="1.0" encoding="UTF-8"?>
<svg viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none">
  <defs>
    <!-- Apple-style Gradients -->
    <linearGradient id="pinkOrangeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#f45888" />
      <stop offset="100%" stop-color="#ee5c46" />
    </linearGradient>
    <linearGradient id="purpleBlueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#6c7ae0" />
      <stop offset="100%" stop-color="#4e5bd0" />
    </linearGradient>

    <!-- Subtle Glow Filter -->
    <filter id="subtle-glow" x="-10%" y="-10%" width="120%" height="120%">
      <feGaussianBlur stdDeviation="2" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>

  <!-- Clean Background -->
  <g id="background">
    <rect width="1200" height="600" fill="#ffffff" />
  </g>

  <!-- Minimal Grid -->
  <g id="grid">
    <!-- Horizontal grid lines - more subtle -->
    <line x1="50" y1="80" x2="1150" y2="80" stroke="#f0f0f0" stroke-width="1" />
    <line x1="50" y1="160" x2="1150" y2="160" stroke="#f0f0f0" stroke-width="1" />
    <line x1="50" y1="240" x2="1150" y2="240" stroke="#f0f0f0" stroke-width="1" />
    <line x1="50" y1="320" x2="1150" y2="320" stroke="#f0f0f0" stroke-width="1" />
    <line x1="50" y1="400" x2="1150" y2="400" stroke="#f0f0f0" stroke-width="1" />
    <line x1="50" y1="480" x2="1150" y2="480" stroke="#f0f0f0" stroke-width="1" />
    <line x1="50" y1="560" x2="1150" y2="560" stroke="#f0f0f0" stroke-width="1" />

    <!-- Vertical grid lines - more subtle -->
    <line x1="250" y1="40" x2="250" y2="560" stroke="#f0f0f0" stroke-width="1" />
    <line x1="450" y1="40" x2="450" y2="560" stroke="#f0f0f0" stroke-width="1" />
    <line x1="650" y1="40" x2="650" y2="560" stroke="#f0f0f0" stroke-width="1" />
    <line x1="850" y1="40" x2="850" y2="560" stroke="#f0f0f0" stroke-width="1" />
    <line x1="1050" y1="40" x2="1050" y2="560" stroke="#f0f0f0" stroke-width="1" />
  </g>

  <!-- Axes - Apple-style minimal -->
  <g id="axes">
    <line x1="50" y1="560" x2="1150" y2="560" stroke="#e0e0e0" stroke-width="1.5" />
    <line x1="50" y1="40" x2="50" y2="560" stroke="#e0e0e0" stroke-width="1.5" />

    <!-- X-axis labels - Apple-style typography -->
    <text x="50" y="580" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="middle" fill="#999">0</text>
    <text x="250" y="580" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="middle" fill="#999">Month 3</text>
    <text x="450" y="580" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="middle" fill="#999">Month 6</text>
    <text x="650" y="580" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="middle" fill="#999">Month 9</text>
    <text x="850" y="580" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="middle" fill="#999">Month 12</text>
    <text x="1050" y="580" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="middle" fill="#999">Month 15</text>

    <!-- Y-axis labels - Apple-style typography -->
    <text x="40" y="560" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="end" fill="#999">0%</text>
    <text x="40" y="480" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="end" fill="#999">50%</text>
    <text x="40" y="400" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="end" fill="#999">100%</text>
    <text x="40" y="320" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="end" fill="#999">150%</text>
    <text x="40" y="240" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="end" fill="#999">200%</text>
    <text x="40" y="160" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="end" fill="#999">250%</text>
    <text x="40" y="80" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="end" fill="#999">300%</text>
    <text x="40" y="40" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="axis-label" text-anchor="end" fill="#999">350%</text>
  </g>

  <!-- Traditional Growth Line - Apple-style minimal -->
  <g id="traditional-growth">
    <!-- Smoother curve -->
    <path d="M50,560 Q150,400 250,360 Q350,330 450,310 Q550,300 650,295 Q750,292 850,290 Q950,288 1050,287"
          fill="none" stroke="#aaaaaa" stroke-width="2" />

    <!-- Minimal data points -->
    <circle cx="50" cy="560" r="4" fill="#aaaaaa" />
    <circle cx="250" cy="360" r="4" fill="#aaaaaa" />
    <circle cx="450" cy="310" r="4" fill="#aaaaaa" />
    <circle cx="650" cy="295" r="4" fill="#aaaaaa" />
    <circle cx="850" cy="290" r="4" fill="#aaaaaa" />
    <circle cx="1050" cy="287" r="4" fill="#aaaaaa" />

    <!-- Apple-style label -->
    <text x="1070" y="287" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="line-label" fill="#aaaaaa">Traditional Scaling</text>
  </g>

  <!-- AI-Powered Growth Line - Apple-style -->
  <g id="ai-growth">
    <!-- Smoother curve with subtle glow -->
    <path d="M50,560 Q150,400 250,360 Q350,280 450,220 Q550,170 650,130 Q750,100 850,80 Q950,65 1050,50"
          fill="none" stroke="url(#pinkOrangeGradient)" stroke-width="3" filter="url(#subtle-glow)" />

    <!-- Minimal data points -->
    <circle cx="50" cy="560" r="5" fill="url(#pinkOrangeGradient)" />
    <circle cx="250" cy="360" r="5" fill="url(#pinkOrangeGradient)" />
    <circle cx="450" cy="220" r="5" fill="url(#pinkOrangeGradient)" />
    <circle cx="650" cy="130" r="5" fill="url(#pinkOrangeGradient)" />
    <circle cx="850" cy="80" r="5" fill="url(#pinkOrangeGradient)" />
    <circle cx="1050" cy="50" r="5" fill="url(#pinkOrangeGradient)" />

    <!-- Apple-style label -->
    <text x="1070" y="50" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="line-label" font-weight="600" fill="#f45888">Adzeta AI Scaling</text>
  </g>

  <!-- Apple-style Annotations -->
  <g id="annotations">
    <!-- Diminishing returns annotation - Apple-style -->
    <path d="M650,295 L700,240" stroke="#aaaaaa" stroke-width="1" stroke-dasharray="3,3" />
    <rect x="580" y="210" width="240" height="30" rx="15" fill="white" stroke="#f0f0f0" stroke-width="1" />
    <text x="700" y="230" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="annotation-text" text-anchor="middle" fill="#aaaaaa">Diminishing returns as ad spend increases</text>

    <!-- AI advantage annotation - Apple-style -->
    <path d="M850,80 L900,120" stroke="url(#pinkOrangeGradient)" stroke-width="1" />
    <rect x="780" y="120" width="240" height="30" rx="15" fill="white" stroke="#f0f0f0" stroke-width="1" />
    <text x="900" y="140" font-family="SF Pro Display, -apple-system, BlinkMacSystemFont, Arial" class="annotation-text" text-anchor="middle" fill="#f45888">Continuous improvement with AI-powered scaling</text>
  </g>

  <!-- Apple-style Animation -->
  <style>
    /* Responsive text sizing to maintain readability when SVG stretches */
    text {
      font-size: 1.5vw; /* Responsive font size based on viewport width */
      vector-effect: non-scaling-stroke;
    }

    .axis-label {
      font-size: 1.8vw; /* Slightly larger for axis labels */
    }

    .line-label {
      font-size: 1.6vw; /* Medium size for line labels */
    }

    .annotation-text {
      font-size: 1.3vw; /* Smaller for annotations */
    }

    @keyframes drawLine {
      to {
        stroke-dashoffset: 0;
      }
    }

    @keyframes fadeIn {
      to {
        opacity: 1;
      }
    }

    @keyframes subtlePulse {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.1);
        opacity: 0.9;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }

    #traditional-growth path {
      stroke-dasharray: 1000;
      stroke-dashoffset: 1000;
      animation: drawLine 1.5s ease-out forwards;
    }

    #ai-growth path {
      stroke-dasharray: 1000;
      stroke-dashoffset: 1000;
      animation: drawLine 2s ease-out 0.8s forwards;
    }

    #annotations {
      opacity: 0;
      animation: fadeIn 0.8s ease-out 2.8s forwards;
    }

    #ai-growth circle {
      animation: subtlePulse 3s infinite;
      animation-delay: 2.8s;
    }

    #traditional-growth circle, #ai-growth circle {
      opacity: 0;
      animation: fadeIn 0.3s ease-out forwards;
    }

    #traditional-growth circle:nth-child(2) { animation-delay: 0.3s; }
    #traditional-growth circle:nth-child(3) { animation-delay: 0.6s; }
    #traditional-growth circle:nth-child(4) { animation-delay: 0.9s; }
    #traditional-growth circle:nth-child(5) { animation-delay: 1.2s; }
    #traditional-growth circle:nth-child(6) { animation-delay: 1.5s; }

    #ai-growth circle:nth-child(2) { animation-delay: 1.1s; }
    #ai-growth circle:nth-child(3) { animation-delay: 1.4s; }
    #ai-growth circle:nth-child(4) { animation-delay: 1.7s; }
    #ai-growth circle:nth-child(5) { animation-delay: 2.0s; }
    #ai-growth circle:nth-child(6) { animation-delay: 2.3s; }
  </style>
</svg>
