/**
 * AdZeta Admin Panel - Media Manager Module
 * Full-page media library management interface
 */

window.AdZetaMediaManager = {
    // State
    state: {
        currentView: 'grid', // grid or list
        selectedItems: [],
        currentPage: 1,
        totalPages: 1,
        searchQuery: '',
        filterType: 'all',
        sortBy: 'date',
        sortOrder: 'desc',
        media: [],
        loading: false,
        bulkMode: false
    },

    // Initialize media manager
    init() {
        console.log('Media Manager module initialized');
    },

    // Load media manager view
    load() {
        console.log('Loading Media Manager view');
        this.renderMediaManager();
        this.bindEvents();
        this.loadMedia();
    },

    // Render media manager interface
    renderMediaManager() {
        const mediaView = document.getElementById('mediaView');
        if (!mediaView) return;

        mediaView.innerHTML = `
            <!-- Media Manager Header -->
            <div class="media-manager-header mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center gap-3">
                            <h2 class="h4 mb-0">Media Library</h2>
                            <span class="badge bg-secondary" id="mediaCount">0 items</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end gap-2">
                            <button class="btn btn-outline-secondary" id="bulkModeBtn">
                                <i class="fas fa-check-square me-1"></i>
                                Bulk Select
                            </button>
                            <button class="btn btn-primary" id="uploadMediaBtn">
                                <i class="fas fa-upload me-1"></i>
                                Upload Media
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media Manager Toolbar -->
            <div class="media-toolbar mb-4">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" id="mediaSearch" 
                                   placeholder="Search media files...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="mediaFilter">
                            <option value="all">All Types</option>
                            <option value="image">Images</option>
                            <option value="video">Videos</option>
                            <option value="audio">Audio</option>
                            <option value="document">Documents</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="mediaSortBy">
                            <option value="date">Date</option>
                            <option value="name">Name</option>
                            <option value="size">Size</option>
                            <option value="type">Type</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="mediaSortOrder">
                            <option value="desc">Newest First</option>
                            <option value="asc">Oldest First</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary active" id="gridViewBtn">
                                <i class="fas fa-th"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="listViewBtn">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bulk Actions Bar -->
            <div class="bulk-actions-bar mb-3" id="bulkActionsBar" style="display: none;">
                <div class="d-flex align-items-center justify-content-between bg-light p-3 rounded">
                    <div class="d-flex align-items-center gap-3">
                        <span id="selectedCount">0 items selected</span>
                        <button class="btn btn-sm btn-outline-primary" id="selectAllBtn">Select All</button>
                        <button class="btn btn-sm btn-outline-secondary" id="clearSelectionBtn">Clear Selection</button>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-danger" id="bulkDeleteBtn">
                            <i class="fas fa-trash me-1"></i>
                            Delete Selected
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" id="exitBulkModeBtn">
                            <i class="fas fa-times me-1"></i>
                            Exit Bulk Mode
                        </button>
                    </div>
                </div>
            </div>

            <!-- Media Grid/List Container -->
            <div class="media-container">
                <div class="media-grid" id="mediaGrid">
                    <!-- Media items will be loaded here -->
                </div>
                <div class="media-list" id="mediaList" style="display: none;">
                    <!-- List view will be loaded here -->
                </div>
                
                <!-- Loading State -->
                <div class="text-center py-5" id="mediaLoading" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading media...</p>
                </div>
                
                <!-- Empty State -->
                <div class="text-center py-5" id="mediaEmpty" style="display: none;">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h4>No media files found</h4>
                    <p class="text-muted">Upload your first media file to get started.</p>
                    <button class="btn btn-primary" onclick="AdZetaMediaManager.openUploadModal()">
                        <i class="fas fa-upload me-1"></i>
                        Upload Media
                    </button>
                </div>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4" id="mediaPagination" style="display: none;">
                <div class="text-muted">
                    Showing <span id="showingStart">1</span> to <span id="showingEnd">20</span> 
                    of <span id="totalItems">0</span> items
                </div>
                <nav>
                    <ul class="pagination mb-0" id="paginationList">
                        <!-- Pagination will be rendered here -->
                    </ul>
                </nav>
            </div>
        `;

        // Update page header
        this.updatePageHeader();
    },

    // Update page header
    updatePageHeader() {
        const pageTitle = document.getElementById('pageTitle');
        const pageSubtitle = document.getElementById('pageSubtitle');
        const pageActions = document.getElementById('pageActions');

        if (pageTitle) {
            pageTitle.innerHTML = '<i class="fas fa-images me-2"></i>Media Library';
        }
        if (pageSubtitle) {
            pageSubtitle.textContent = 'Manage your media files, images, and documents.';
        }
        if (pageActions) {
            pageActions.innerHTML = `
                <button class="btn btn-primary" onclick="AdZetaMediaManager.openUploadModal()">
                    <i class="fas fa-upload me-1"></i>
                    Upload Media
                </button>
            `;
        }
    },

    // Bind events
    bindEvents() {
        // Search
        const searchInput = document.getElementById('mediaSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce((e) => {
                this.state.searchQuery = e.target.value;
                this.state.currentPage = 1;
                this.loadMedia();
            }, 300));
        }

        // Filter
        const filterSelect = document.getElementById('mediaFilter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.state.filterType = e.target.value;
                this.state.currentPage = 1;
                this.loadMedia();
            });
        }

        // Sort
        const sortBySelect = document.getElementById('mediaSortBy');
        const sortOrderSelect = document.getElementById('mediaSortOrder');
        
        if (sortBySelect) {
            sortBySelect.addEventListener('change', (e) => {
                this.state.sortBy = e.target.value;
                this.loadMedia();
            });
        }
        
        if (sortOrderSelect) {
            sortOrderSelect.addEventListener('change', (e) => {
                this.state.sortOrder = e.target.value;
                this.loadMedia();
            });
        }

        // View toggle
        const gridViewBtn = document.getElementById('gridViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');
        
        if (gridViewBtn) {
            gridViewBtn.addEventListener('click', () => this.switchView('grid'));
        }
        if (listViewBtn) {
            listViewBtn.addEventListener('click', () => this.switchView('list'));
        }

        // Bulk mode
        const bulkModeBtn = document.getElementById('bulkModeBtn');
        const exitBulkModeBtn = document.getElementById('exitBulkModeBtn');
        
        if (bulkModeBtn) {
            bulkModeBtn.addEventListener('click', () => this.toggleBulkMode(true));
        }
        if (exitBulkModeBtn) {
            exitBulkModeBtn.addEventListener('click', () => this.toggleBulkMode(false));
        }

        // Upload button
        const uploadBtn = document.getElementById('uploadMediaBtn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => this.openUploadModal());
        }
    },

    // Load media with current filters
    async loadMedia() {
        this.state.loading = true;
        this.showLoading();

        try {
            const params = new URLSearchParams({
                page: this.state.currentPage,
                limit: 24,
                search: this.state.searchQuery,
                type: this.state.filterType,
                sort_by: this.state.sortBy,
                sort_order: this.state.sortOrder
            });

            const response = await fetch(`/adzeta-admin/api/media/index.php?${params}`);

            // Check if response is ok
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Get response text first to debug JSON issues
            const responseText = await response.text();

            // Try to parse JSON
            let result;
            try {
                result = JSON.parse(responseText);
            } catch (jsonError) {
                console.error('Media Manager JSON Parse Error:', jsonError);
                console.error('Response Text:', responseText.substring(0, 500));
                throw new Error(`Invalid JSON response from media API. Response: ${responseText.substring(0, 100)}...`);
            }

            if (result.success) {
                this.state.media = result.media || [];
                this.state.currentPage = result.pagination?.current_page || 1;
                this.state.totalPages = result.pagination?.total_pages || 1;

                this.renderMedia();
                this.renderPagination();
                this.updateMediaCount(result.pagination?.total_items || 0);
            } else {
                throw new Error(result.message || 'Failed to load media');
            }
        } catch (error) {
            console.error('Failed to load media:', error);
            this.showEmpty();
        } finally {
            this.state.loading = false;
            this.hideLoading();
        }
    },

    // Switch between grid and list view
    switchView(view) {
        this.state.currentView = view;
        
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');
        const gridContainer = document.getElementById('mediaGrid');
        const listContainer = document.getElementById('mediaList');
        
        if (view === 'grid') {
            gridBtn?.classList.add('active');
            listBtn?.classList.remove('active');
            gridContainer.style.display = 'grid';
            listContainer.style.display = 'none';
        } else {
            listBtn?.classList.add('active');
            gridBtn?.classList.remove('active');
            gridContainer.style.display = 'none';
            listContainer.style.display = 'block';
        }
        
        this.renderMedia();
    },

    // Toggle bulk selection mode
    toggleBulkMode(enabled) {
        this.state.bulkMode = enabled;
        this.state.selectedItems = [];
        
        const bulkActionsBar = document.getElementById('bulkActionsBar');
        const bulkModeBtn = document.getElementById('bulkModeBtn');
        
        if (enabled) {
            bulkActionsBar.style.display = 'block';
            bulkModeBtn.innerHTML = '<i class="fas fa-times me-1"></i>Exit Bulk Mode';
        } else {
            bulkActionsBar.style.display = 'none';
            bulkModeBtn.innerHTML = '<i class="fas fa-check-square me-1"></i>Bulk Select';
        }
        
        this.renderMedia();
        this.updateSelectedCount();
    },

    // Open upload modal
    openUploadModal() {
        if (window.AdZetaMediaLibrary) {
            window.AdZetaMediaLibrary.open((selectedImage) => {
                // Refresh media list after upload
                this.loadMedia();
            });
        }
    },

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    showLoading() {
        document.getElementById('mediaLoading').style.display = 'block';
        document.getElementById('mediaGrid').style.display = 'none';
        document.getElementById('mediaList').style.display = 'none';
    },

    hideLoading() {
        document.getElementById('mediaLoading').style.display = 'none';
        if (this.state.currentView === 'grid') {
            document.getElementById('mediaGrid').style.display = 'grid';
        } else {
            document.getElementById('mediaList').style.display = 'block';
        }
    },

    showEmpty() {
        document.getElementById('mediaEmpty').style.display = 'block';
        document.getElementById('mediaGrid').style.display = 'none';
        document.getElementById('mediaList').style.display = 'none';
    },

    updateMediaCount(count) {
        const mediaCount = document.getElementById('mediaCount');
        if (mediaCount) {
            mediaCount.textContent = `${count} item${count !== 1 ? 's' : ''}`;
        }
    },

    updateSelectedCount() {
        const selectedCount = document.getElementById('selectedCount');
        if (selectedCount) {
            const count = this.state.selectedItems.length;
            selectedCount.textContent = `${count} item${count !== 1 ? 's' : ''} selected`;
        }
    },

    // Render media items
    renderMedia() {
        if (this.state.currentView === 'grid') {
            this.renderGridView();
        } else {
            this.renderListView();
        }
    },

    // Render grid view
    renderGridView() {
        const grid = document.getElementById('mediaGrid');
        const emptyState = document.getElementById('mediaEmpty');

        if (this.state.media.length === 0) {
            grid.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';

        grid.innerHTML = this.state.media.map(item => `
            <div class="media-grid-item ${this.state.selectedItems.includes(item.id) ? 'selected' : ''}"
                 data-id="${item.id}">
                ${this.state.bulkMode ? `
                    <div class="media-checkbox">
                        <input type="checkbox" class="form-check-input"
                               ${this.state.selectedItems.includes(item.id) ? 'checked' : ''}
                               onchange="AdZetaMediaManager.toggleItemSelection(${item.id})">
                    </div>
                ` : ''}
                <div class="media-thumbnail" onclick="AdZetaMediaManager.openMediaDetails(${item.id})">
                    <img src="${item.file_url}" alt="${item.original_filename}" loading="lazy">
                    <div class="media-overlay">
                        <div class="media-actions">
                            <button class="btn btn-sm btn-light" onclick="event.stopPropagation(); AdZetaMediaManager.editMedia(${item.id})" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-light" onclick="event.stopPropagation(); AdZetaMediaManager.downloadMedia(${item.id})" title="Download">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); AdZetaMediaManager.deleteMedia(${item.id})" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="media-info">
                    <div class="media-name" title="${item.original_filename}">${item.original_filename}</div>
                    <div class="media-meta">
                        <span class="media-size">${this.formatFileSize(item.file_size)}</span>
                        <span class="media-date">${this.formatDate(item.uploaded_at)}</span>
                    </div>
                </div>
            </div>
        `).join('');
    },

    // Render list view
    renderListView() {
        const list = document.getElementById('mediaList');
        const emptyState = document.getElementById('mediaEmpty');

        if (this.state.media.length === 0) {
            list.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }

        emptyState.style.display = 'none';

        list.innerHTML = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            ${this.state.bulkMode ? '<th width="40"><input type="checkbox" class="form-check-input" id="selectAllCheckbox"></th>' : ''}
                            <th width="60">Preview</th>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Date</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.state.media.map(item => `
                            <tr class="${this.state.selectedItems.includes(item.id) ? 'table-active' : ''}" data-id="${item.id}">
                                ${this.state.bulkMode ? `
                                    <td>
                                        <input type="checkbox" class="form-check-input"
                                               ${this.state.selectedItems.includes(item.id) ? 'checked' : ''}
                                               onchange="AdZetaMediaManager.toggleItemSelection(${item.id})">
                                    </td>
                                ` : ''}
                                <td>
                                    <img src="${item.file_url}" alt="${item.original_filename}"
                                         class="media-list-thumbnail" onclick="AdZetaMediaManager.openMediaDetails(${item.id})">
                                </td>
                                <td>
                                    <div class="media-list-name" onclick="AdZetaMediaManager.openMediaDetails(${item.id})">
                                        ${item.original_filename}
                                    </div>
                                </td>
                                <td><span class="badge bg-secondary">${item.mime_type}</span></td>
                                <td>${this.formatFileSize(item.file_size)}</td>
                                <td>${this.formatDate(item.uploaded_at)}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="AdZetaMediaManager.editMedia(${item.id})" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="AdZetaMediaManager.downloadMedia(${item.id})" title="Download">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="AdZetaMediaManager.deleteMedia(${item.id})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    },

    // Render pagination
    renderPagination() {
        const pagination = document.getElementById('mediaPagination');
        const paginationList = document.getElementById('paginationList');

        if (this.state.totalPages <= 1) {
            pagination.style.display = 'none';
            return;
        }

        pagination.style.display = 'flex';

        let paginationHTML = '';

        // Previous button
        if (this.state.currentPage > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="AdZetaMediaManager.goToPage(${this.state.currentPage - 1})">Previous</a></li>`;
        }

        // Page numbers
        const startPage = Math.max(1, this.state.currentPage - 2);
        const endPage = Math.min(this.state.totalPages, this.state.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            if (i === this.state.currentPage) {
                paginationHTML += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="AdZetaMediaManager.goToPage(${i})">${i}</a></li>`;
            }
        }

        // Next button
        if (this.state.currentPage < this.state.totalPages) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="AdZetaMediaManager.goToPage(${this.state.currentPage + 1})">Next</a></li>`;
        }

        paginationList.innerHTML = paginationHTML;

        // Update showing info
        const itemsPerPage = 24;
        const start = (this.state.currentPage - 1) * itemsPerPage + 1;
        const end = Math.min(this.state.currentPage * itemsPerPage, this.state.media.length);

        document.getElementById('showingStart').textContent = start;
        document.getElementById('showingEnd').textContent = end;
    },

    // Utility functions
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    // Media actions
    goToPage(page) {
        this.state.currentPage = page;
        this.loadMedia();
    },

    toggleItemSelection(itemId) {
        const index = this.state.selectedItems.indexOf(itemId);
        if (index > -1) {
            this.state.selectedItems.splice(index, 1);
        } else {
            this.state.selectedItems.push(itemId);
        }
        this.updateSelectedCount();
        this.renderMedia();
    },

    openMediaDetails(itemId) {
        // TODO: Implement media details modal
        console.log('Opening media details for item:', itemId);
    },

    editMedia(itemId) {
        // TODO: Implement media editing
        console.log('Editing media item:', itemId);
    },

    downloadMedia(itemId) {
        const item = this.state.media.find(m => m.id === itemId);
        if (item) {
            const link = document.createElement('a');
            link.href = item.file_url;
            link.download = item.original_filename;
            link.click();
        }
    },

    deleteMedia(itemId) {
        if (confirm('Are you sure you want to delete this media file?')) {
            // TODO: Implement media deletion
            console.log('Deleting media item:', itemId);
        }
    }
};
