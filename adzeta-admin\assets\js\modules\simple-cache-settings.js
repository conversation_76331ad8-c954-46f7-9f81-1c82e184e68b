/**
 * Simplified Cache Settings Module
 * User-friendly cache management interface
 */

const AdZetaSimpleCacheSettings = {
    state: {
        enabled: false, // Default to false until loaded from server
        stats: {
            cached_pages: 0,
            cache_size: '0 MB',
            last_cleared: null,
            speed_improvement: '0%'
        },
        cachedUrls: []
    },

    // Initialize
    async init() {
        console.log('AdZetaSimpleCacheSettings: Initializing...');
        await this.loadSettings(); // Load settings first
        await this.loadStats();
        console.log('AdZetaSimpleCacheSettings: Initialized');
    },

    // <PERSON><PERSON> simplified cache settings (Apple-inspired design)
    renderSimpleCacheSettings() {
        return `
            <div class="cache-settings-container">
                <!-- Main Cache Toggle - Bootstrap Style -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary text-white rounded p-2 me-3">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 fw-semibold">Speed Optimization</h6>
                                    <small class="text-muted">Accelerate your website performance</small>
                                </div>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="simpleCacheToggle"
                                       ${this.state.enabled ? 'checked' : ''}
                                       onchange="AdZetaSimpleCacheSettings.toggleCache(this.checked)">
                                <label class="form-check-label" for="simpleCacheToggle"></label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bootstrap Stats Grid -->
                <div class="row mb-4" ${!this.state.enabled ? 'style="opacity: 0.6;"' : ''}>
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="card-title text-primary mb-1">${this.state.stats.total_cached_pages || this.state.stats.cached_pages || 0}</h4>
                                <p class="card-text text-muted small mb-0">PAGES</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="card-title text-info mb-1">${this.state.stats.cache_size || '0 KB'}</h4>
                                <p class="card-text text-muted small mb-0">SIZE</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="card-title text-success mb-1">${this.state.stats.performance_improvement || this.state.stats.speed_improvement || '0'}%</h4>
                                <p class="card-text text-muted small mb-0">FASTER</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="card-title ${this.state.enabled ? 'text-success' : 'text-secondary'} mb-1">${this.state.enabled ? 'Active' : 'Off'}</h4>
                                <p class="card-text text-muted small mb-0">STATUS</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cached URLs List - Bootstrap Style -->
                <div class="card mb-4" id="cachedUrlsList">
                    <div class="card-header bg-light">
                        <h6 class="mb-0 fw-semibold">Cached Pages</h6>
                        <small class="text-muted">Currently optimized URLs</small>
                    </div>
                    <div class="card-body">
                        <div id="urlsListContent">
                            ${this.renderCachedUrls()}
                        </div>
                    </div>
                </div>

                <!-- Bootstrap Action Buttons -->
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-primary w-100" onclick="AdZetaSimpleCacheSettings.preloadCache()"
                                ${!this.state.enabled ? 'disabled' : ''}>
                            <i class="fas fa-rocket me-2"></i>
                            Optimize Now
                        </button>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-secondary w-100" onclick="AdZetaSimpleCacheSettings.clearCache('blog')"
                                ${!this.state.enabled ? 'disabled' : ''}>
                            <i class="fas fa-blog me-2"></i>
                            Clear Blog
                        </button>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-danger w-100" onclick="AdZetaSimpleCacheSettings.clearCache('all')">
                            <i class="fas fa-trash me-2"></i>
                            Clear All
                        </button>
                    </div>
                </div>

                <!-- Optimization Settings -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0 fw-semibold">Optimization Settings</h6>
                        <small class="text-muted">Configure cache optimization features</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="gzipToggle"
                                           ${this.state.settings?.static_cache_gzip === true ? 'checked' : ''}
                                           onchange="AdZetaSimpleCacheSettings.updateSetting('static_cache_gzip', this.checked)">
                                    <label class="form-check-label" for="gzipToggle">
                                        <strong>Gzip Compression</strong><br>
                                        <small class="text-muted">Reduce file sizes by 60-80%</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="minifyToggle"
                                           ${this.state.settings?.static_cache_minify === true ? 'checked' : ''}
                                           onchange="AdZetaSimpleCacheSettings.updateSetting('static_cache_minify', this.checked)">
                                    <label class="form-check-label" for="minifyToggle">
                                        <strong>HTML Minification</strong><br>
                                        <small class="text-muted">Remove whitespace and comments</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="browserCacheToggle"
                                           ${this.state.settings?.browser_cache_enabled === true ? 'checked' : ''}
                                           onchange="AdZetaSimpleCacheSettings.updateSetting('browser_cache_enabled', this.checked)">
                                    <label class="form-check-label" for="browserCacheToggle">
                                        <strong>Browser Cache</strong><br>
                                        <small class="text-muted">Cache files in visitor browsers</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        `;
    },

    // Render cached URLs list
    renderCachedUrls() {
        if (!this.state.cachedUrls || this.state.cachedUrls.length === 0) {
            return `
                <div class="apple-empty-state">
                    <i class="fas fa-inbox text-muted mb-2"></i>
                    <p class="text-muted mb-0">No cached pages yet</p>
                    <small class="text-muted">Click "Optimize Now" to generate cache</small>
                </div>
            `;
        }

        return this.state.cachedUrls.map(url => `
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="flex-grow-1">
                    <div class="fw-semibold text-dark">${url.path}</div>
                    <div class="d-flex gap-3 mt-1">
                        <small class="text-muted">${url.size}</small>
                        <span class="badge bg-primary">${url.type}</span>
                    </div>
                </div>
                <div class="text-success">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        `).join('');
    },

    // Toggle cache on/off
    async toggleCache(enabled) {
        console.log('🔧 DEBUG: toggleCache called with enabled =', enabled);
        console.log('🔧 DEBUG: Current state.enabled =', this.state.enabled);

        try {
            console.log('🔧 DEBUG: Sending POST request to /adzeta-admin/api/cache/settings');
            console.log('🔧 DEBUG: Request body:', JSON.stringify({ cache_enabled: enabled }));

            const response = await fetch('/adzeta-admin/api/cache/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ cache_enabled: enabled })
            });

            console.log('🔧 DEBUG: Response status:', response.status);
            console.log('🔧 DEBUG: Response ok:', response.ok);

            const data = await response.json();
            console.log('🔧 DEBUG: Response data:', data);

            if (data.success) {
                console.log('🔧 DEBUG: API call successful, updating state');
                this.state.enabled = enabled;
                console.log('🔧 DEBUG: New state.enabled =', this.state.enabled);

                window.AdZetaApp.showNotification(
                    enabled ? '🚀 Speed boost activated!' : '⚠️ Speed boost disabled',
                    enabled ? 'success' : 'warning'
                );
                this.refreshInterface();
                console.log('🔧 DEBUG: Interface refreshed');
            } else {
                console.error('🔧 DEBUG: API call failed with message:', data.message);
                throw new Error(data.message || 'Failed to toggle cache');
            }
        } catch (error) {
            console.error('🔧 DEBUG: Exception in toggleCache:', error);
            console.error('Cache toggle error:', error);
            window.AdZetaApp.showNotification('Failed to change cache settings', 'danger');
            // Revert toggle
            const toggle = document.getElementById('simpleCacheToggle');
            if (toggle) {
                console.log('🔧 DEBUG: Reverting toggle to', !enabled);
                toggle.checked = !enabled;
            }
        }
    },

    // Clear cache
    async clearCache(type) {
        if (!confirm(`Clear ${type} cache? This will temporarily slow down your website until cache rebuilds.`)) {
            return;
        }

        try {
            // Show loading state
            const clearBtn = document.querySelector(`button[onclick*="clearCache('${type}')"]`);
            if (clearBtn) {
                clearBtn.disabled = true;
                clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Clearing...';
            }

            const response = await fetch('/adzeta-admin/api/cache/clear', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ type: type })
            });

            const data = await response.json();

            if (data.success) {
                const messages = {
                    'all': '🗑️ All cache cleared! Your website will rebuild cache automatically.',
                    'blog': '📝 Blog cache cleared! New posts will now show up immediately.'
                };

                window.AdZetaApp.showNotification(messages[type], 'success');

                // Immediately reset stats for better UX
                if (type === 'all') {
                    this.state.stats = {
                        total_cached_pages: 0,
                        cached_pages: 0,
                        cache_size: '0 KB',
                        last_cleared: 'Just now',
                        speed_improvement: '0%',
                        performance_improvement: 0
                    };
                    this.state.cachedUrls = [];
                }

                // Update UI immediately
                this.refreshInterface();

                // Then refresh from server with longer delay to ensure cache is cleared
                setTimeout(async () => {
                    await this.loadStats();
                    this.refreshInterface();
                }, 1000);
            } else {
                throw new Error(data.message || 'Failed to clear cache');
            }
        } catch (error) {
            console.error('Cache clear error:', error);
            window.AdZetaApp.showNotification('Failed to clear cache', 'danger');
        } finally {
            // Restore button state
            const clearBtn = document.querySelector(`button[onclick*="clearCache('${type}')"]`);
            if (clearBtn) {
                clearBtn.disabled = false;
                if (type === 'all') {
                    clearBtn.innerHTML = '<i class="fas fa-trash me-2"></i>Clear All Cache';
                } else {
                    clearBtn.innerHTML = '<i class="fas fa-blog me-2"></i>Clear Blog Cache';
                }
            }
        }
    },

    // Preload cache with progress display
    async preloadCache() {
        try {
            // Show loading notification
            window.AdZetaApp.showNotification('🚀 Boosting website speed...', 'info');

            // Disable the button during preload
            const preloadBtn = document.querySelector('button[onclick*="preloadCache"]');
            if (preloadBtn) {
                preloadBtn.disabled = true;
                preloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Boosting Speed...';
            }

            const response = await fetch('/adzeta-admin/api/cache/preload', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });

            const data = await response.json();

            if (data.success && data.data) {
                // Show detailed success message with URLs cached
                const urlsList = data.data.urls_cached ? data.data.urls_cached.join(', ') : 'blog pages';
                const message = `🎉 Speed boost complete! Generated cache for ${data.data.pages_generated} pages (${urlsList}) in ${data.data.total_time}. Cache size: ${data.data.cache_size}`;

                window.AdZetaApp.showNotification(message, 'success');

                // Log details to console for debugging
                console.log('Cache preload completed:', data.data);

                // Refresh stats to show new cache
                this.loadStats();
            } else {
                throw new Error(data.message || 'Failed to preload cache');
            }
        } catch (error) {
            console.error('Cache preload error:', error);
            window.AdZetaApp.showNotification('Failed to boost speed: ' + error.message, 'danger');
        } finally {
            // Re-enable the button
            const preloadBtn = document.querySelector('button[onclick*="preloadCache"]');
            if (preloadBtn) {
                preloadBtn.disabled = false;
                preloadBtn.innerHTML = '<i class="fas fa-rocket me-2"></i>Boost Speed Now';
            }
        }
    },

    // Load settings
    async loadSettings() {
        console.log('🔧 DEBUG: loadSettings() called');
        try {
            console.log('🔧 DEBUG: Fetching settings from /adzeta-admin/api/cache/settings');
            const response = await fetch('/adzeta-admin/api/cache/settings');
            console.log('🔧 DEBUG: Settings response status:', response.status);

            const data = await response.json();
            console.log('🔧 DEBUG: Settings response data:', data);

            if (data.success) {
                // Store all settings for individual toggles
                this.state.settings = {
                    cache_enabled: data.cache_enabled,
                    static_cache_gzip: data.static_cache_gzip,
                    static_cache_minify: data.static_cache_minify,
                    browser_cache_enabled: data.browser_cache_enabled
                };
                console.log('🔧 DEBUG: Stored settings:', this.state.settings);

                // Handle main cache toggle
                const cacheEnabled = data.cache_enabled ?? data.data?.cache_enabled ?? true;
                console.log('🔧 DEBUG: Extracted cache_enabled value:', cacheEnabled);
                console.log('🔧 DEBUG: Previous state.enabled:', this.state.enabled);

                this.state.enabled = cacheEnabled !== false;
                console.log('🔧 DEBUG: New state.enabled:', this.state.enabled);

                console.log('Cache settings loaded:', {
                    rawData: data,
                    cacheEnabled,
                    enabled: this.state.enabled,
                    allSettings: this.state.settings
                });

                // Update the UI to reflect the loaded state
                console.log('🔧 DEBUG: Calling refreshInterface()');
                this.refreshInterface();
            } else {
                console.error('🔧 DEBUG: Settings API returned success=false:', data);
            }
        } catch (error) {
            console.error('🔧 DEBUG: Exception in loadSettings:', error);
            console.error('Failed to load cache settings:', error);
        }
    },

    // Load stats
    async loadStats() {
        try {
            const response = await fetch('/adzeta-admin/api/cache/stats');
            const data = await response.json();

            if (data.success) {
                this.state.stats = {
                    cached_pages: data.total_cached_pages || 0,
                    cache_size: typeof data.cache_size === 'number' ? this.formatFileSize(data.cache_size) : (data.cache_size || '0 B'),
                    last_cleared: data.last_cleared ? this.timeAgo(data.last_cleared) : 'Never',
                    speed_improvement: data.performance_improvement ? data.performance_improvement + '%' : '0%'
                };

                // Use real cached URLs from API
                this.state.cachedUrls = data.cached_urls || [];

                // Log stats for debugging
                console.log('Cache stats loaded:', this.state.stats);

                // Refresh the interface if it's already rendered
                this.refreshInterface();
            }
        } catch (error) {
            console.error('Failed to load cache stats:', error);
        }
    },

    // Generate cached URLs list from stats data
    generateCachedUrlsList(data) {
        const urls = [];

        // Add blog listing if cached
        if (data.blog_lists_cached > 0) {
            urls.push({
                path: '/blog/',
                type: 'Blog List',
                size: '~40KB'
            });
        }

        // Add blog posts if cached
        if (data.blog_posts_cached > 0) {
            // Generate sample post URLs (in real implementation, get from API)
            for (let i = 0; i < Math.min(data.blog_posts_cached, 5); i++) {
                urls.push({
                    path: `/blog/post-${i + 1}`,
                    type: 'Blog Post',
                    size: '~40KB'
                });
            }

            // Show "and X more" if there are more posts
            if (data.blog_posts_cached > 5) {
                urls.push({
                    path: `... and ${data.blog_posts_cached - 5} more posts`,
                    type: 'Blog Posts',
                    size: ''
                });
            }
        }

        return urls;
    },

    // Refresh interface
    refreshInterface() {
        // Update the main cache toggle first
        const toggle = document.getElementById('simpleCacheToggle');
        if (toggle) {
            toggle.checked = this.state.enabled;
        }

        // Then re-render the entire container if needed
        const container = document.querySelector('.cache-settings-container');
        if (container && container.parentElement) {
            container.parentElement.innerHTML = this.renderSimpleCacheSettings();
        }
    },

    // Helper functions
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    timeAgo(date) {
        const now = new Date();
        const past = new Date(date);
        const diffInSeconds = Math.floor((now - past) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' min ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        return Math.floor(diffInSeconds / 86400) + ' days ago';
    },

    // Update individual cache setting
    async updateSetting(setting, value) {
        console.log('🔧 DEBUG: updateSetting called with:', setting, '=', value);

        try {
            console.log('🔧 DEBUG: Sending POST to /adzeta-admin/api/cache/settings');
            console.log('🔧 DEBUG: Request body:', JSON.stringify({ [setting]: value }));

            const response = await fetch('/adzeta-admin/api/cache/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ [setting]: value })
            });

            console.log('🔧 DEBUG: Response status:', response.status);
            console.log('🔧 DEBUG: Response ok:', response.ok);

            const data = await response.json();
            console.log('🔧 DEBUG: Response data:', data);

            if (data.success) {
                console.log('🔧 DEBUG: Setting update successful');
                // Update local state
                this.state.settings = this.state.settings || {};
                this.state.settings[setting] = value;

                const settingNames = {
                    'static_cache_gzip': 'Gzip Compression',
                    'static_cache_minify': 'HTML Minification',
                    'browser_cache_enabled': 'Browser Cache'
                };

                window.AdZetaApp.showNotification(
                    `${settingNames[setting]} ${value ? 'enabled' : 'disabled'}`,
                    'success'
                );
            } else {
                console.error('🔧 DEBUG: API returned success=false:', data.message);
                throw new Error(data.message || 'Failed to update setting');
            }
        } catch (error) {
            console.error('🔧 DEBUG: Exception in updateSetting:', error);
            console.error('Setting update error:', error);
            window.AdZetaApp.showNotification('Failed to update setting', 'danger');

            // Revert toggle
            const toggleId = setting.replace(/_/g, '') + 'Toggle';
            const toggle = document.getElementById(toggleId);
            if (toggle) {
                console.log('🔧 DEBUG: Reverting toggle', toggleId, 'to', !value);
                toggle.checked = !value;
            }
        }
    }
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => AdZetaSimpleCacheSettings.init());
} else {
    AdZetaSimpleCacheSettings.init();
}
