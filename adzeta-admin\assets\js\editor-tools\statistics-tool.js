/**
 * Statistics Tool for Editor.js
 * Creates styled statistics displays with numbers and descriptions
 */

class StatisticsTool {
    static get toolbox() {
        return {
            title: 'Statistics',
            icon: '<svg width="17" height="15" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-6h2v20h-2V1zm4 8h2v12h-2V9zm4-4h2v16h-2V5z" fill="#FF4081"/></svg>'
        };
    }

    static get isReadOnlySupported() {
        return true;
    }

    constructor({ data, config, api, readOnly }) {
        this.api = api;
        this.readOnly = readOnly;
        this.config = config || {};

        this.data = {
            value: data.value || '247',
            unit: data.unit || '%',
            label: data.label || 'Improvement',
            style: data.style || 'default',
            color: data.color || '#FF4081',
            ...data
        };

        this.wrapper = undefined;
    }

    render() {
        this.wrapper = document.createElement('div');
        this.wrapper.classList.add('statistics-tool');

        const statsContainer = document.createElement('div');
        statsContainer.classList.add('statistics-display');
        statsContainer.style.cssText = `
            text-align: center;
            padding: 24px;
            background: #F5F5F5;
            border-radius: 12px;
            margin: 16px 0;
            border: 2px solid ${this.data.color};
            position: relative;
        `;

        // Value display
        const valueElement = document.createElement('div');
        valueElement.classList.add('stat-value');
        valueElement.style.cssText = `
            font-size: 3rem;
            font-weight: 700;
            color: ${this.data.color};
            line-height: 1;
            margin-bottom: 8px;
        `;

        const valueSpan = document.createElement('span');
        valueSpan.textContent = this.data.value;
        valueSpan.contentEditable = !this.readOnly;
        valueSpan.style.outline = 'none';

        const unitSpan = document.createElement('span');
        unitSpan.textContent = this.data.unit;
        unitSpan.contentEditable = !this.readOnly;
        unitSpan.style.cssText = `
            font-size: 2rem;
            margin-left: 4px;
            outline: none;
        `;

        valueElement.appendChild(valueSpan);
        valueElement.appendChild(unitSpan);

        // Label display
        const labelElement = document.createElement('div');
        labelElement.classList.add('stat-label');
        labelElement.textContent = this.data.label;
        labelElement.contentEditable = !this.readOnly;
        labelElement.style.cssText = `
            font-size: 1rem;
            color: #2B0B3A;
            font-weight: 500;
            outline: none;
        `;

        if (!this.readOnly) {
            // Add event listeners for editing
            valueSpan.addEventListener('input', () => {
                this.data.value = valueSpan.textContent;
            });

            unitSpan.addEventListener('input', () => {
                this.data.unit = unitSpan.textContent;
            });

            labelElement.addEventListener('input', () => {
                this.data.label = labelElement.textContent;
            });

            // Add color controls
            const controls = this.createControls();
            this.wrapper.appendChild(controls);
        }

        statsContainer.appendChild(valueElement);
        statsContainer.appendChild(labelElement);
        this.wrapper.appendChild(statsContainer);

        return this.wrapper;
    }

    createControls() {
        const controls = document.createElement('div');
        controls.classList.add('statistics-controls');
        controls.style.cssText = `
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 8px;
            font-size: 12px;
        `;

        // Color presets
        const colors = [
            { name: 'Pink', value: '#FF4081' },
            { name: 'Purple', value: '#2B0B3A' },
            { name: 'Blue', value: '#4A7AB5' },
            { name: 'Green', value: '#28A745' },
            { name: 'Orange', value: '#FD7E14' }
        ];

        colors.forEach(color => {
            const button = document.createElement('button');
            button.textContent = color.name;
            button.style.cssText = `
                padding: 4px 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: ${color.value};
                color: white;
                cursor: pointer;
                font-size: 11px;
            `;

            button.addEventListener('click', () => {
                this.data.color = color.value;
                this.updateStatStyle();
            });

            controls.appendChild(button);
        });

        return controls;
    }

    updateStatStyle() {
        const statsDisplay = this.wrapper.querySelector('.statistics-display');
        const statValue = this.wrapper.querySelector('.stat-value');

        if (statsDisplay) {
            statsDisplay.style.borderColor = this.data.color;
        }

        if (statValue) {
            statValue.style.color = this.data.color;
        }
    }

    save() {
        return {
            value: this.data.value,
            unit: this.data.unit,
            label: this.data.label,
            color: this.data.color,
            style: this.data.style
        };
    }

    static get sanitize() {
        return {
            value: false,
            unit: false,
            label: false
        };
    }

    // Paste config removed to avoid Editor.js conflicts
    // Custom paste handling is done through AI content processor

    // onPaste method removed to avoid conflicts
    // Paste handling is done through AI content processor
}

// Export for use in Editor.js
window.StatisticsTool = StatisticsTool;
