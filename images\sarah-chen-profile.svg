<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="100" cy="100" r="100" fill="#F4F4F4"/>
  
  <!-- Gradient overlay -->
  <circle cx="100" cy="100" r="100" fill="url(#paint0_linear)"/>
  
  <!-- Head shape -->
  <path d="M100 120C122.091 120 140 102.091 140 80C140 57.9086 122.091 40 100 40C77.9086 40 60 57.9086 60 80C60 102.091 77.9086 120 100 120Z" fill="#E0E0E0"/>
  
  <!-- Body shape -->
  <path d="M155 200H45C45 166.863 69.8629 140 100 140C130.137 140 155 166.863 155 200Z" fill="#E0E0E0"/>
  
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="200" y2="200" gradientUnits="userSpaceOnUse">
      <stop stop-color="#f45888" stop-opacity="0.1"/>
      <stop offset="1" stop-color="#ee5c46" stop-opacity="0.1"/>
    </linearGradient>
  </defs>
</svg>
