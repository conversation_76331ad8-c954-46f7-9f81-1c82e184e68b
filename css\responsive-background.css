/* Responsive background adjustments */

/* Default styles for all devices */
.pgc-responsive {
    background-position: right center !important;
    background-repeat: no-repeat !important;
}

/* Large desktop screens */
@media (min-width: 1200px) {
    .pgc-responsive {
        background-position: 90% center !important;
    }
}

/* Medium desktop screens */
@media (max-width: 1199px) and (min-width: 992px) {
    .pgc-responsive {
        background-position: 85% center !important;
    }
}

/* Tablet screens */
@media (max-width: 991px) and (min-width: 768px) {
    .pgc-responsive {
        background-position: 80% center !important;
    }
}

/* Mobile landscape screens */
@media (max-width: 767px) and (min-width: 576px) {
    .pgc-responsive {
        background-position: 75% center !important;
        background-size: 50% auto !important;
    }
}

/* Mobile portrait screens */
@media (max-width: 575px) {
    .pgc-responsive {
        background-position: 120% center !important;
        background-size: 60% auto !important;
    }
}
