(function(){"use strict";try{if(typeof document<"u"){var e=document.createElement("style");e.appendChild(document.createTextNode(".ce-hint--align-start{text-align:left}.ce-hint--align-center{text-align:center}.ce-hint__description{opacity:.6;margin-top:3px}")),document.head.appendChild(e)}}catch(t){console.error("vite-plugin-css-injected-by-js",t)}})();
(function(J,ne){typeof exports=="object"&&typeof module<"u"?module.exports=ne():typeof define=="function"&&define.amd?define(ne):(J=typeof globalThis<"u"?globalThis:J||self,J.EditorJS=ne())})(this,function(){"use strict";var J=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ne(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}function Yn(n){if(n.__esModule)return n;var e=n.default;if(typeof e=="function"){var t=function o(){return this instanceof o?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};t.prototype=e.prototype}else t={};return Object.defineProperty(t,"__esModule",{value:!0}),Object.keys(n).forEach(function(o){var i=Object.getOwnPropertyDescriptor(n,o);Object.defineProperty(t,o,i.get?i:{enumerable:!0,get:function(){return n[o]}})}),t}function tt(){}Object.assign(tt,{default:tt,register:tt,revert:function(){},__esModule:!0}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(n){const e=(this.document||this.ownerDocument).querySelectorAll(n);let t=e.length;for(;--t>=0&&e.item(t)!==this;);return t>-1}),Element.prototype.closest||(Element.prototype.closest=function(n){let e=this;if(!document.documentElement.contains(e))return null;do{if(e.matches(n))return e;e=e.parentElement||e.parentNode}while(e!==null);return null}),Element.prototype.prepend||(Element.prototype.prepend=function(e){const t=document.createDocumentFragment();Array.isArray(e)||(e=[e]),e.forEach(o=>{const i=o instanceof Node;t.appendChild(i?o:document.createTextNode(o))}),this.insertBefore(t,this.firstChild)}),Element.prototype.scrollIntoViewIfNeeded||(Element.prototype.scrollIntoViewIfNeeded=function(n){n=arguments.length===0?!0:!!n;const e=this.parentNode,t=window.getComputedStyle(e,null),o=parseInt(t.getPropertyValue("border-top-width")),i=parseInt(t.getPropertyValue("border-left-width")),s=this.offsetTop-e.offsetTop<e.scrollTop,r=this.offsetTop-e.offsetTop+this.clientHeight-o>e.scrollTop+e.clientHeight,a=this.offsetLeft-e.offsetLeft<e.scrollLeft,l=this.offsetLeft-e.offsetLeft+this.clientWidth-i>e.scrollLeft+e.clientWidth,c=s&&!r;(s||r)&&n&&(e.scrollTop=this.offsetTop-e.offsetTop-e.clientHeight/2-o+this.clientHeight/2),(a||l)&&n&&(e.scrollLeft=this.offsetLeft-e.offsetLeft-e.clientWidth/2-i+this.clientWidth/2),(s||r||a||l)&&!n&&this.scrollIntoView(c)}),window.requestIdleCallback=window.requestIdleCallback||function(n){const e=Date.now();return setTimeout(function(){n({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},window.cancelIdleCallback=window.cancelIdleCallback||function(n){clearTimeout(n)};let Kn=(n=21)=>crypto.getRandomValues(new Uint8Array(n)).reduce((e,t)=>(t&=63,t<36?e+=t.toString(36):t<62?e+=(t-26).toString(36).toUpperCase():t>62?e+="-":e+="_",e),"");var ko=(n=>(n.VERBOSE="VERBOSE",n.INFO="INFO",n.WARN="WARN",n.ERROR="ERROR",n))(ko||{});const y={BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,LEFT:37,UP:38,DOWN:40,RIGHT:39,DELETE:46,META:91,SLASH:191},Xn={LEFT:0,WHEEL:1,RIGHT:2,BACKWARD:3,FORWARD:4};function ye(n,e,t="log",o,i="color: inherit"){if(!("console"in window)||!window.console[t])return;const s=["info","log","warn","error"].includes(t),r=[];switch(ye.logLevel){case"ERROR":if(t!=="error")return;break;case"WARN":if(!["error","warn"].includes(t))return;break;case"INFO":if(!s||n)return;break}o&&r.push(o);const a="Editor.js 2.31.0-rc.7",l=`line-height: 1em;
            color: #006FEA;
            display: inline-block;
            font-size: 11px;
            line-height: 1em;
            background-color: #fff;
            padding: 4px 9px;
            border-radius: 30px;
            border: 1px solid rgba(56, 138, 229, 0.16);
            margin: 4px 5px 4px 0;`;n&&(s?(r.unshift(l,i),e=`%c${a}%c ${e}`):e=`( ${a} )${e}`);try{s?o?console[t](`${e} %o`,...r):console[t](e,...r):console[t](e)}catch{}}ye.logLevel="VERBOSE";function Vn(n){ye.logLevel=n}const T=ye.bind(window,!1),X=ye.bind(window,!0);function re(n){return Object.prototype.toString.call(n).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function M(n){return re(n)==="function"||re(n)==="asyncfunction"}function N(n){return re(n)==="object"}function Q(n){return re(n)==="string"}function qn(n){return re(n)==="boolean"}function yo(n){return re(n)==="number"}function wo(n){return re(n)==="undefined"}function V(n){return n?Object.keys(n).length===0&&n.constructor===Object:!0}function Eo(n){return n>47&&n<58||n===32||n===13||n===229||n>64&&n<91||n>95&&n<112||n>185&&n<193||n>218&&n<223}async function Zn(n,e=()=>{},t=()=>{}){async function o(i,s,r){try{await i.function(i.data),await s(wo(i.data)?{}:i.data)}catch{r(wo(i.data)?{}:i.data)}}return n.reduce(async(i,s)=>(await i,o(s,e,t)),Promise.resolve())}function xo(n){return Array.prototype.slice.call(n)}function Ae(n,e){return function(){const t=this,o=arguments;window.setTimeout(()=>n.apply(t,o),e)}}function Gn(n){return n.name.split(".").pop()}function Jn(n){return/^[-\w]+\/([-+\w]+|\*)$/.test(n)}function Bo(n,e,t){let o;return(...i)=>{const s=this,r=()=>{o=null,t||n.apply(s,i)},a=t&&!o;window.clearTimeout(o),o=window.setTimeout(r,e),a&&n.apply(s,i)}}function ot(n,e,t=void 0){let o,i,s,r=null,a=0;t||(t={});const l=function(){a=t.leading===!1?0:Date.now(),r=null,s=n.apply(o,i),r||(o=i=null)};return function(){const c=Date.now();!a&&t.leading===!1&&(a=c);const u=e-(c-a);return o=this,i=arguments,u<=0||u>e?(r&&(clearTimeout(r),r=null),a=c,s=n.apply(o,i),r||(o=i=null)):!r&&t.trailing!==!1&&(r=setTimeout(l,u)),s}}function Qn(){const n={win:!1,mac:!1,x11:!1,linux:!1},e=Object.keys(n).find(t=>window.navigator.appVersion.toLowerCase().indexOf(t)!==-1);return e&&(n[e]=!0),n}function Le(n){return n[0].toUpperCase()+n.slice(1)}function nt(n,...e){if(!e.length)return n;const t=e.shift();if(N(n)&&N(t))for(const o in t)N(t[o])?(n[o]||Object.assign(n,{[o]:{}}),nt(n[o],t[o])):Object.assign(n,{[o]:t[o]});return nt(n,...e)}function it(n){const e=Qn();return n=n.replace(/shift/gi,"⇧").replace(/backspace/gi,"⌫").replace(/enter/gi,"⏎").replace(/up/gi,"↑").replace(/left/gi,"→").replace(/down/gi,"↓").replace(/right/gi,"←").replace(/escape/gi,"⎋").replace(/insert/gi,"Ins").replace(/delete/gi,"␡").replace(/\+/gi," + "),e.mac?n=n.replace(/ctrl|cmd/gi,"⌘").replace(/alt/gi,"⌥"):n=n.replace(/cmd/gi,"Ctrl").replace(/windows/gi,"WIN"),n}function ei(n){try{return new URL(n).href}catch{}return n.substring(0,2)==="//"?window.location.protocol+n:window.location.origin+n}function ti(){return Kn(10)}function oi(n){window.open(n,"_blank")}function ni(n=""){return`${n}${Math.floor(Math.random()*1e8).toString(16)}`}function st(n,e,t){const o=`«${e}» is deprecated and will be removed in the next major release. Please use the «${t}» instead.`;n&&X(o,"warn")}function fe(n,e,t){const o=t.value?"value":"get",i=t[o],s=`#${e}Cache`;if(t[o]=function(...r){return this[s]===void 0&&(this[s]=i.apply(this,...r)),this[s]},o==="get"&&t.set){const r=t.set;t.set=function(a){delete n[s],r.apply(this,a)}}return t}const Co=650;function ge(){return window.matchMedia(`(max-width: ${Co}px)`).matches}const rt=typeof window<"u"&&window.navigator&&window.navigator.platform&&(/iP(ad|hone|od)/.test(window.navigator.platform)||window.navigator.platform==="MacIntel"&&window.navigator.maxTouchPoints>1);function ii(n,e){const t=Array.isArray(n)||N(n),o=Array.isArray(e)||N(e);return t||o?JSON.stringify(n)===JSON.stringify(e):n===e}class d{static isSingleTag(e){return e.tagName&&["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"].includes(e.tagName)}static isLineBreakTag(e){return e&&e.tagName&&["BR","WBR"].includes(e.tagName)}static make(e,t=null,o={}){const i=document.createElement(e);if(Array.isArray(t)){const s=t.filter(r=>r!==void 0);i.classList.add(...s)}else t&&i.classList.add(t);for(const s in o)Object.prototype.hasOwnProperty.call(o,s)&&(i[s]=o[s]);return i}static text(e){return document.createTextNode(e)}static append(e,t){Array.isArray(t)?t.forEach(o=>e.appendChild(o)):e.appendChild(t)}static prepend(e,t){Array.isArray(t)?(t=t.reverse(),t.forEach(o=>e.prepend(o))):e.prepend(t)}static swap(e,t){const o=document.createElement("div"),i=e.parentNode;i.insertBefore(o,e),i.insertBefore(e,t),i.insertBefore(t,o),i.removeChild(o)}static find(e=document,t){return e.querySelector(t)}static get(e){return document.getElementById(e)}static findAll(e=document,t){return e.querySelectorAll(t)}static get allInputsSelector(){return"[contenteditable=true], textarea, input:not([type]), "+["text","password","email","number","search","tel","url"].map(t=>`input[type="${t}"]`).join(", ")}static findAllInputs(e){return xo(e.querySelectorAll(d.allInputsSelector)).reduce((t,o)=>d.isNativeInput(o)||d.containsOnlyInlineElements(o)?[...t,o]:[...t,...d.getDeepestBlockElements(o)],[])}static getDeepestNode(e,t=!1){const o=t?"lastChild":"firstChild",i=t?"previousSibling":"nextSibling";if(e&&e.nodeType===Node.ELEMENT_NODE&&e[o]){let s=e[o];if(d.isSingleTag(s)&&!d.isNativeInput(s)&&!d.isLineBreakTag(s))if(s[i])s=s[i];else if(s.parentNode[i])s=s.parentNode[i];else return s.parentNode;return this.getDeepestNode(s,t)}return e}static isElement(e){return yo(e)?!1:e&&e.nodeType&&e.nodeType===Node.ELEMENT_NODE}static isFragment(e){return yo(e)?!1:e&&e.nodeType&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE}static isContentEditable(e){return e.contentEditable==="true"}static isNativeInput(e){const t=["INPUT","TEXTAREA"];return e&&e.tagName?t.includes(e.tagName):!1}static canSetCaret(e){let t=!0;if(d.isNativeInput(e))switch(e.type){case"file":case"checkbox":case"radio":case"hidden":case"submit":case"button":case"image":case"reset":t=!1;break}else t=d.isContentEditable(e);return t}static isNodeEmpty(e,t){let o;return this.isSingleTag(e)&&!this.isLineBreakTag(e)?!1:(this.isElement(e)&&this.isNativeInput(e)?o=e.value:o=e.textContent.replace("​",""),t&&(o=o.replace(new RegExp(t,"g"),"")),o.length===0)}static isLeaf(e){return e?e.childNodes.length===0:!1}static isEmpty(e,t){const o=[e];for(;o.length>0;)if(e=o.shift(),!!e){if(this.isLeaf(e)&&!this.isNodeEmpty(e,t))return!1;e.childNodes&&o.push(...Array.from(e.childNodes))}return!0}static isHTMLString(e){const t=d.make("div");return t.innerHTML=e,t.childElementCount>0}static getContentLength(e){return d.isNativeInput(e)?e.value.length:e.nodeType===Node.TEXT_NODE?e.length:e.textContent.length}static get blockElements(){return["address","article","aside","blockquote","canvas","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","ruby","section","table","tbody","thead","tr","tfoot","ul","video"]}static containsOnlyInlineElements(e){let t;Q(e)?(t=document.createElement("div"),t.innerHTML=e):t=e;const o=i=>!d.blockElements.includes(i.tagName.toLowerCase())&&Array.from(i.children).every(o);return Array.from(t.children).every(o)}static getDeepestBlockElements(e){return d.containsOnlyInlineElements(e)?[e]:Array.from(e.children).reduce((t,o)=>[...t,...d.getDeepestBlockElements(o)],[])}static getHolder(e){return Q(e)?document.getElementById(e):e}static isAnchor(e){return e.tagName.toLowerCase()==="a"}static offset(e){const t=e.getBoundingClientRect(),o=window.pageXOffset||document.documentElement.scrollLeft,i=window.pageYOffset||document.documentElement.scrollTop,s=t.top+i,r=t.left+o;return{top:s,left:r,bottom:s+t.height,right:r+t.width}}}function si(n){return!/[^\t\n\r ]/.test(n)}function ri(n){const e=window.getComputedStyle(n),t=parseFloat(e.fontSize),o=parseFloat(e.lineHeight)||t*1.2,i=parseFloat(e.paddingTop),s=parseFloat(e.borderTopWidth),r=parseFloat(e.marginTop),a=t*.8,l=(o-t)/2;return r+s+i+l+a}function To(n){n.dataset.empty=d.isEmpty(n)?"true":"false"}const So={ui:{blockTunes:{toggler:{"Click to tune":"","or drag to move":""}},inlineToolbar:{converter:{"Convert to":""}},toolbar:{toolbox:{Add:""}},popover:{Filter:"","Nothing found":"","Convert to":""}},toolNames:{Text:"",Link:"",Bold:"",Italic:""},tools:{link:{"Add a link":""},stub:{"The block can not be displayed correctly.":""}},blockTunes:{delete:{Delete:"","Click to delete":""},moveUp:{"Move up":""},moveDown:{"Move down":""}}},Io=class ke{static ui(e,t){return ke._t(e,t)}static t(e,t){return ke._t(e,t)}static setDictionary(e){ke.currentDictionary=e}static _t(e,t){const o=ke.getNamespace(e);return!o||!o[t]?t:o[t]}static getNamespace(e){return e.split(".").reduce((o,i)=>!o||!Object.keys(o).length?{}:o[i],ke.currentDictionary)}};Io.currentDictionary=So;let H=Io;class Oo extends Error{}class we{constructor(){this.subscribers={}}on(e,t){e in this.subscribers||(this.subscribers[e]=[]),this.subscribers[e].push(t)}once(e,t){e in this.subscribers||(this.subscribers[e]=[]);const o=i=>{const s=t(i),r=this.subscribers[e].indexOf(o);return r!==-1&&this.subscribers[e].splice(r,1),s};this.subscribers[e].push(o)}emit(e,t){V(this.subscribers)||!this.subscribers[e]||this.subscribers[e].reduce((o,i)=>{const s=i(o);return s!==void 0?s:o},t)}off(e,t){if(this.subscribers[e]===void 0){console.warn(`EventDispatcher .off(): there is no subscribers for event "${e.toString()}". Probably, .off() called before .on()`);return}for(let o=0;o<this.subscribers[e].length;o++)if(this.subscribers[e][o]===t){delete this.subscribers[e][o];break}}destroy(){this.subscribers={}}}function ee(n){Object.setPrototypeOf(this,{get id(){return n.id},get name(){return n.name},get config(){return n.config},get holder(){return n.holder},get isEmpty(){return n.isEmpty},get selected(){return n.selected},set stretched(t){n.stretched=t},get stretched(){return n.stretched},get focusable(){return n.focusable},call(t,o){return n.call(t,o)},save(){return n.save()},validate(t){return n.validate(t)},dispatchChange(){n.dispatchChange()},getActiveToolboxEntry(){return n.getActiveToolboxEntry()}})}class Ee{constructor(){this.allListeners=[]}on(e,t,o,i=!1){const s=ni("l"),r={id:s,element:e,eventType:t,handler:o,options:i};if(!this.findOne(e,t,o))return this.allListeners.push(r),e.addEventListener(t,o,i),s}off(e,t,o,i){const s=this.findAll(e,t,o);s.forEach((r,a)=>{const l=this.allListeners.indexOf(s[a]);l>-1&&(this.allListeners.splice(l,1),r.element.removeEventListener(r.eventType,r.handler,r.options))})}offById(e){const t=this.findById(e);t&&t.element.removeEventListener(t.eventType,t.handler,t.options)}findOne(e,t,o){const i=this.findAll(e,t,o);return i.length>0?i[0]:null}findAll(e,t,o){let i;const s=e?this.findByEventTarget(e):[];return e&&t&&o?i=s.filter(r=>r.eventType===t&&r.handler===o):e&&t?i=s.filter(r=>r.eventType===t):i=s,i}removeAll(){this.allListeners.map(e=>{e.element.removeEventListener(e.eventType,e.handler,e.options)}),this.allListeners=[]}destroy(){this.removeAll()}findByEventTarget(e){return this.allListeners.filter(t=>{if(t.element===e)return t})}findByType(e){return this.allListeners.filter(t=>{if(t.eventType===e)return t})}findByHandler(e){return this.allListeners.filter(t=>{if(t.handler===e)return t})}findById(e){return this.allListeners.find(t=>t.id===e)}}class E{constructor({config:e,eventsDispatcher:t}){if(this.nodes={},this.listeners=new Ee,this.readOnlyMutableListeners={on:(o,i,s,r=!1)=>{this.mutableListenerIds.push(this.listeners.on(o,i,s,r))},clearAll:()=>{for(const o of this.mutableListenerIds)this.listeners.offById(o);this.mutableListenerIds=[]}},this.mutableListenerIds=[],new.target===E)throw new TypeError("Constructors for abstract class Module are not allowed.");this.config=e,this.eventsDispatcher=t}set state(e){this.Editor=e}removeAllNodes(){for(const e in this.nodes){const t=this.nodes[e];t instanceof HTMLElement&&t.remove()}}get isRtl(){return this.config.i18n.direction==="rtl"}}class b{constructor(){this.instance=null,this.selection=null,this.savedSelectionRange=null,this.isFakeBackgroundEnabled=!1,this.commandBackground="backColor",this.commandRemoveFormat="removeFormat"}static get CSS(){return{editorWrapper:"codex-editor",editorZone:"codex-editor__redactor"}}static get anchorNode(){const e=window.getSelection();return e?e.anchorNode:null}static get anchorElement(){const e=window.getSelection();if(!e)return null;const t=e.anchorNode;return t?d.isElement(t)?t:t.parentElement:null}static get anchorOffset(){const e=window.getSelection();return e?e.anchorOffset:null}static get isCollapsed(){const e=window.getSelection();return e?e.isCollapsed:null}static get isAtEditor(){return this.isSelectionAtEditor(b.get())}static isSelectionAtEditor(e){if(!e)return!1;let t=e.anchorNode||e.focusNode;t&&t.nodeType===Node.TEXT_NODE&&(t=t.parentNode);let o=null;return t&&t instanceof Element&&(o=t.closest(`.${b.CSS.editorZone}`)),o?o.nodeType===Node.ELEMENT_NODE:!1}static isRangeAtEditor(e){if(!e)return;let t=e.startContainer;t&&t.nodeType===Node.TEXT_NODE&&(t=t.parentNode);let o=null;return t&&t instanceof Element&&(o=t.closest(`.${b.CSS.editorZone}`)),o?o.nodeType===Node.ELEMENT_NODE:!1}static get isSelectionExists(){return!!b.get().anchorNode}static get range(){return this.getRangeFromSelection(this.get())}static getRangeFromSelection(e){return e&&e.rangeCount?e.getRangeAt(0):null}static get rect(){let e=document.selection,t,o={x:0,y:0,width:0,height:0};if(e&&e.type!=="Control")return e=e,t=e.createRange(),o.x=t.boundingLeft,o.y=t.boundingTop,o.width=t.boundingWidth,o.height=t.boundingHeight,o;if(!window.getSelection)return T("Method window.getSelection is not supported","warn"),o;if(e=window.getSelection(),e.rangeCount===null||isNaN(e.rangeCount))return T("Method SelectionUtils.rangeCount is not supported","warn"),o;if(e.rangeCount===0)return o;if(t=e.getRangeAt(0).cloneRange(),t.getBoundingClientRect&&(o=t.getBoundingClientRect()),o.x===0&&o.y===0){const i=document.createElement("span");if(i.getBoundingClientRect){i.appendChild(document.createTextNode("​")),t.insertNode(i),o=i.getBoundingClientRect();const s=i.parentNode;s.removeChild(i),s.normalize()}}return o}static get text(){return window.getSelection?window.getSelection().toString():""}static get(){return window.getSelection()}static setCursor(e,t=0){const o=document.createRange(),i=window.getSelection();return d.isNativeInput(e)?d.canSetCaret(e)?(e.focus(),e.selectionStart=e.selectionEnd=t,e.getBoundingClientRect()):void 0:(o.setStart(e,t),o.setEnd(e,t),i.removeAllRanges(),i.addRange(o),o.getBoundingClientRect())}static isRangeInsideContainer(e){const t=b.range;return t===null?!1:e.contains(t.startContainer)}static addFakeCursor(){const e=b.range;if(e===null)return;const t=d.make("span","codex-editor__fake-cursor");t.dataset.mutationFree="true",e.collapse(),e.insertNode(t)}static isFakeCursorInsideContainer(e){return d.find(e,".codex-editor__fake-cursor")!==null}static removeFakeCursor(e=document.body){const t=d.find(e,".codex-editor__fake-cursor");t&&t.remove()}removeFakeBackground(){this.isFakeBackgroundEnabled&&(this.isFakeBackgroundEnabled=!1,document.execCommand(this.commandRemoveFormat))}setFakeBackground(){document.execCommand(this.commandBackground,!1,"#a8d6ff"),this.isFakeBackgroundEnabled=!0}save(){this.savedSelectionRange=b.range}restore(){if(!this.savedSelectionRange)return;const e=window.getSelection();e.removeAllRanges(),e.addRange(this.savedSelectionRange)}clearSaved(){this.savedSelectionRange=null}collapseToEnd(){const e=window.getSelection(),t=document.createRange();t.selectNodeContents(e.focusNode),t.collapse(!1),e.removeAllRanges(),e.addRange(t)}findParentTag(e,t,o=10){const i=window.getSelection();let s=null;return!i||!i.anchorNode||!i.focusNode?null:([i.anchorNode,i.focusNode].forEach(a=>{let l=o;for(;l>0&&a.parentNode&&!(a.tagName===e&&(s=a,t&&a.classList&&!a.classList.contains(t)&&(s=null),s));)a=a.parentNode,l--}),s)}expandToTag(e){const t=window.getSelection();t.removeAllRanges();const o=document.createRange();o.selectNodeContents(e),t.addRange(o)}}function ai(n,e){const{type:t,target:o,addedNodes:i,removedNodes:s}=n;return n.type==="attributes"&&n.attributeName==="data-empty"?!1:!!(e.contains(o)||t==="childList"&&(Array.from(i).some(l=>l===e)||Array.from(s).some(l=>l===e)))}const at="redactor dom changed",_o="block changed",Mo="fake cursor is about to be toggled",Ao="fake cursor have been set",xe="editor mobile layout toggled";function lt(n,e){if(!n.conversionConfig)return!1;const t=n.conversionConfig[e];return M(t)||Q(t)}function Pe(n,e){return lt(n.tool,e)}function Lo(n,e){return Object.entries(n).some(([t,o])=>e[t]&&ii(e[t],o))}async function Po(n,e){const o=(await n.save()).data,i=e.find(s=>s.name===n.name);return i!==void 0&&!lt(i,"export")?[]:e.reduce((s,r)=>{if(!lt(r,"import")||r.toolbox===void 0)return s;const a=r.toolbox.filter(l=>{if(V(l)||l.icon===void 0)return!1;if(l.data!==void 0){if(Lo(l.data,o))return!1}else if(r.name===n.name)return!1;return!0});return s.push({...r,toolbox:a}),s},[])}function No(n,e){return n.mergeable?n.name===e.name?!0:Pe(e,"export")&&Pe(n,"import"):!1}function li(n,e){const t=e==null?void 0:e.export;return M(t)?t(n):Q(t)?n[t]:(t!==void 0&&T("Conversion «export» property must be a string or function. String means key of saved data object to export. Function should export processed string to export."),"")}function Ro(n,e,t){const o=e==null?void 0:e.import;return M(o)?o(n,t):Q(o)?{[o]:n}:(o!==void 0&&T("Conversion «import» property must be a string or function. String means key of tool data to import. Function accepts a imported string and return composed tool data."),{})}var _=(n=>(n.Default="default",n.Separator="separator",n.Html="html",n))(_||{}),te=(n=>(n.APPEND_CALLBACK="appendCallback",n.RENDERED="rendered",n.MOVED="moved",n.UPDATED="updated",n.REMOVED="removed",n.ON_PASTE="onPaste",n))(te||{});class R extends we{constructor({id:e=ti(),data:t,tool:o,readOnly:i,tunesData:s},r){super(),this.cachedInputs=[],this.toolRenderedElement=null,this.tunesInstances=new Map,this.defaultTunesInstances=new Map,this.unavailableTunesData={},this.inputIndex=0,this.editorEventBus=null,this.handleFocus=()=>{this.dropInputsCache(),this.updateCurrentInput()},this.didMutated=(a=void 0)=>{const l=a===void 0,c=a instanceof InputEvent;!l&&!c&&this.detectToolRootChange(a);let u;l||c?u=!0:u=!(a.length>0&&a.every(p=>{const{addedNodes:g,removedNodes:f,target:v}=p;return[...Array.from(g),...Array.from(f),v].some(S=>(d.isElement(S)||(S=S.parentElement),S&&S.closest('[data-mutation-free="true"]')!==null))})),u&&(this.dropInputsCache(),this.updateCurrentInput(),this.toggleInputsEmptyMark(),this.call("updated"),this.emit("didMutated",this))},this.name=o.name,this.id=e,this.settings=o.settings,this.config=o.settings.config||{},this.editorEventBus=r||null,this.blockAPI=new ee(this),this.tool=o,this.toolInstance=o.create(t,this.blockAPI,i),this.tunes=o.tunes,this.composeTunes(s),this.holder=this.compose(),window.requestIdleCallback(()=>{this.watchBlockMutations(),this.addInputEvents(),this.toggleInputsEmptyMark()})}static get CSS(){return{wrapper:"ce-block",wrapperStretched:"ce-block--stretched",content:"ce-block__content",selected:"ce-block--selected",dropTarget:"ce-block--drop-target"}}get inputs(){if(this.cachedInputs.length!==0)return this.cachedInputs;const e=d.findAllInputs(this.holder);return this.inputIndex>e.length-1&&(this.inputIndex=e.length-1),this.cachedInputs=e,e}get currentInput(){return this.inputs[this.inputIndex]}set currentInput(e){const t=this.inputs.findIndex(o=>o===e||o.contains(e));t!==-1&&(this.inputIndex=t)}get firstInput(){return this.inputs[0]}get lastInput(){const e=this.inputs;return e[e.length-1]}get nextInput(){return this.inputs[this.inputIndex+1]}get previousInput(){return this.inputs[this.inputIndex-1]}get data(){return this.save().then(e=>e&&!V(e.data)?e.data:{})}get sanitize(){return this.tool.sanitizeConfig}get mergeable(){return M(this.toolInstance.merge)}get focusable(){return this.inputs.length!==0}get isEmpty(){const e=d.isEmpty(this.pluginsContent,"/"),t=!this.hasMedia;return e&&t}get hasMedia(){const e=["img","iframe","video","audio","source","input","textarea","twitterwidget"];return!!this.holder.querySelector(e.join(","))}set selected(e){var i,s;this.holder.classList.toggle(R.CSS.selected,e);const t=e===!0&&b.isRangeInsideContainer(this.holder),o=e===!1&&b.isFakeCursorInsideContainer(this.holder);(t||o)&&((i=this.editorEventBus)==null||i.emit(Mo,{state:e}),t?b.addFakeCursor():b.removeFakeCursor(this.holder),(s=this.editorEventBus)==null||s.emit(Ao,{state:e}))}get selected(){return this.holder.classList.contains(R.CSS.selected)}set stretched(e){this.holder.classList.toggle(R.CSS.wrapperStretched,e)}get stretched(){return this.holder.classList.contains(R.CSS.wrapperStretched)}set dropTarget(e){this.holder.classList.toggle(R.CSS.dropTarget,e)}get pluginsContent(){return this.toolRenderedElement}call(e,t){if(M(this.toolInstance[e])){e==="appendCallback"&&T("`appendCallback` hook is deprecated and will be removed in the next major release. Use `rendered` hook instead","warn");try{this.toolInstance[e].call(this.toolInstance,t)}catch(o){T(`Error during '${e}' call: ${o.message}`,"error")}}}async mergeWith(e){await this.toolInstance.merge(e)}async save(){const e=await this.toolInstance.save(this.pluginsContent),t=this.unavailableTunesData;[...this.tunesInstances.entries(),...this.defaultTunesInstances.entries()].forEach(([s,r])=>{if(M(r.save))try{t[s]=r.save()}catch(a){T(`Tune ${r.constructor.name} save method throws an Error %o`,"warn",a)}});const o=window.performance.now();let i;return Promise.resolve(e).then(s=>(i=window.performance.now(),{id:this.id,tool:this.name,data:s,tunes:t,time:i-o})).catch(s=>{T(`Saving process for ${this.name} tool failed due to the ${s}`,"log","red")})}async validate(e){let t=!0;return this.toolInstance.validate instanceof Function&&(t=await this.toolInstance.validate(e)),t}getTunes(){const e=[],t=[],o=typeof this.toolInstance.renderSettings=="function"?this.toolInstance.renderSettings():[];return d.isElement(o)?e.push({type:_.Html,element:o}):Array.isArray(o)?e.push(...o):e.push(o),[...this.tunesInstances.values(),...this.defaultTunesInstances.values()].map(s=>s.render()).forEach(s=>{d.isElement(s)?t.push({type:_.Html,element:s}):Array.isArray(s)?t.push(...s):t.push(s)}),{toolTunes:e,commonTunes:t}}updateCurrentInput(){this.currentInput=d.isNativeInput(document.activeElement)||!b.anchorNode?document.activeElement:b.anchorNode}dispatchChange(){this.didMutated()}destroy(){this.unwatchBlockMutations(),this.removeInputEvents(),super.destroy(),M(this.toolInstance.destroy)&&this.toolInstance.destroy()}async getActiveToolboxEntry(){const e=this.tool.toolbox;if(e.length===1)return Promise.resolve(this.tool.toolbox[0]);const t=await this.data,o=e;return o==null?void 0:o.find(i=>Lo(i.data,t))}async exportDataAsString(){const e=await this.data;return li(e,this.tool.conversionConfig)}compose(){const e=d.make("div",R.CSS.wrapper),t=d.make("div",R.CSS.content),o=this.toolInstance.render();e.setAttribute("data-cy","block-wrapper"),e.dataset.id=this.id,this.toolRenderedElement=o,t.appendChild(this.toolRenderedElement);let i=t;return[...this.tunesInstances.values(),...this.defaultTunesInstances.values()].forEach(s=>{if(M(s.wrap))try{i=s.wrap(i)}catch(r){T(`Tune ${s.constructor.name} wrap method throws an Error %o`,"warn",r)}}),e.appendChild(i),e}composeTunes(e){Array.from(this.tunes.values()).forEach(t=>{(t.isInternal?this.defaultTunesInstances:this.tunesInstances).set(t.name,t.create(e[t.name],this.blockAPI))}),Object.entries(e).forEach(([t,o])=>{this.tunesInstances.has(t)||(this.unavailableTunesData[t]=o)})}addInputEvents(){this.inputs.forEach(e=>{e.addEventListener("focus",this.handleFocus),d.isNativeInput(e)&&e.addEventListener("input",this.didMutated)})}removeInputEvents(){this.inputs.forEach(e=>{e.removeEventListener("focus",this.handleFocus),d.isNativeInput(e)&&e.removeEventListener("input",this.didMutated)})}watchBlockMutations(){var e;this.redactorDomChangedCallback=t=>{const{mutations:o}=t;o.some(s=>ai(s,this.toolRenderedElement))&&this.didMutated(o)},(e=this.editorEventBus)==null||e.on(at,this.redactorDomChangedCallback)}unwatchBlockMutations(){var e;(e=this.editorEventBus)==null||e.off(at,this.redactorDomChangedCallback)}detectToolRootChange(e){e.forEach(t=>{if(Array.from(t.removedNodes).includes(this.toolRenderedElement)){const i=t.addedNodes[t.addedNodes.length-1];this.toolRenderedElement=i}})}dropInputsCache(){this.cachedInputs=[]}toggleInputsEmptyMark(){this.inputs.forEach(To)}}class ci extends E{constructor(){super(...arguments),this.insert=(e=this.config.defaultBlock,t={},o={},i,s,r,a)=>{const l=this.Editor.BlockManager.insert({id:a,tool:e,data:t,index:i,needToFocus:s,replace:r});return new ee(l)},this.composeBlockData=async e=>{const t=this.Editor.Tools.blockTools.get(e);return new R({tool:t,api:this.Editor.API,readOnly:!0,data:{},tunesData:{}}).data},this.update=async(e,t,o)=>{const{BlockManager:i}=this.Editor,s=i.getBlockById(e);if(s===void 0)throw new Error(`Block with id "${e}" not found`);const r=await i.update(s,t,o);return new ee(r)},this.convert=async(e,t,o)=>{var h,p;const{BlockManager:i,Tools:s}=this.Editor,r=i.getBlockById(e);if(!r)throw new Error(`Block with id "${e}" not found`);const a=s.blockTools.get(r.name),l=s.blockTools.get(t);if(!l)throw new Error(`Block Tool with type "${t}" not found`);const c=((h=a==null?void 0:a.conversionConfig)==null?void 0:h.export)!==void 0,u=((p=l.conversionConfig)==null?void 0:p.import)!==void 0;if(c&&u){const g=await i.convert(r,t,o);return new ee(g)}else{const g=[c?!1:Le(r.name),u?!1:Le(t)].filter(Boolean).join(" and ");throw new Error(`Conversion from "${r.name}" to "${t}" is not possible. ${g} tool(s) should provide a "conversionConfig"`)}},this.insertMany=(e,t=this.Editor.BlockManager.blocks.length-1)=>{this.validateIndex(t);const o=e.map(({id:i,type:s,data:r})=>this.Editor.BlockManager.composeBlock({id:i,tool:s||this.config.defaultBlock,data:r}));return this.Editor.BlockManager.insertMany(o,t),o.map(i=>new ee(i))}}get methods(){return{clear:()=>this.clear(),render:e=>this.render(e),renderFromHTML:e=>this.renderFromHTML(e),delete:e=>this.delete(e),swap:(e,t)=>this.swap(e,t),move:(e,t)=>this.move(e,t),getBlockByIndex:e=>this.getBlockByIndex(e),getById:e=>this.getById(e),getCurrentBlockIndex:()=>this.getCurrentBlockIndex(),getBlockIndex:e=>this.getBlockIndex(e),getBlocksCount:()=>this.getBlocksCount(),getBlockByElement:e=>this.getBlockByElement(e),stretchBlock:(e,t=!0)=>this.stretchBlock(e,t),insertNewBlock:()=>this.insertNewBlock(),insert:this.insert,insertMany:this.insertMany,update:this.update,composeBlockData:this.composeBlockData,convert:this.convert}}getBlocksCount(){return this.Editor.BlockManager.blocks.length}getCurrentBlockIndex(){return this.Editor.BlockManager.currentBlockIndex}getBlockIndex(e){const t=this.Editor.BlockManager.getBlockById(e);if(!t){X("There is no block with id `"+e+"`","warn");return}return this.Editor.BlockManager.getBlockIndex(t)}getBlockByIndex(e){const t=this.Editor.BlockManager.getBlockByIndex(e);if(t===void 0){X("There is no block at index `"+e+"`","warn");return}return new ee(t)}getById(e){const t=this.Editor.BlockManager.getBlockById(e);return t===void 0?(X("There is no block with id `"+e+"`","warn"),null):new ee(t)}getBlockByElement(e){const t=this.Editor.BlockManager.getBlock(e);if(t===void 0){X("There is no block corresponding to element `"+e+"`","warn");return}return new ee(t)}swap(e,t){T("`blocks.swap()` method is deprecated and will be removed in the next major release. Use `block.move()` method instead","info"),this.Editor.BlockManager.swap(e,t)}move(e,t){this.Editor.BlockManager.move(e,t)}delete(e=this.Editor.BlockManager.currentBlockIndex){try{const t=this.Editor.BlockManager.getBlockByIndex(e);this.Editor.BlockManager.removeBlock(t)}catch(t){X(t,"warn");return}this.Editor.BlockManager.blocks.length===0&&this.Editor.BlockManager.insert(),this.Editor.BlockManager.currentBlock&&this.Editor.Caret.setToBlock(this.Editor.BlockManager.currentBlock,this.Editor.Caret.positions.END),this.Editor.Toolbar.close()}async clear(){await this.Editor.BlockManager.clear(!0),this.Editor.InlineToolbar.close()}async render(e){if(e===void 0||e.blocks===void 0)throw new Error("Incorrect data passed to the render() method");this.Editor.ModificationsObserver.disable(),await this.Editor.BlockManager.clear(),await this.Editor.Renderer.render(e.blocks),this.Editor.ModificationsObserver.enable()}renderFromHTML(e){return this.Editor.BlockManager.clear(),this.Editor.Paste.processText(e,!0)}stretchBlock(e,t=!0){st(!0,"blocks.stretchBlock()","BlockAPI");const o=this.Editor.BlockManager.getBlockByIndex(e);o&&(o.stretched=t)}insertNewBlock(){T("Method blocks.insertNewBlock() is deprecated and it will be removed in the next major release. Use blocks.insert() instead.","warn"),this.insert()}validateIndex(e){if(typeof e!="number")throw new Error("Index should be a number");if(e<0)throw new Error("Index should be greater than or equal to 0");if(e===null)throw new Error("Index should be greater than or equal to 0")}}function di(n,e){return typeof n=="number"?e.BlockManager.getBlockByIndex(n):typeof n=="string"?e.BlockManager.getBlockById(n):e.BlockManager.getBlockById(n.id)}class ui extends E{constructor(){super(...arguments),this.setToFirstBlock=(e=this.Editor.Caret.positions.DEFAULT,t=0)=>this.Editor.BlockManager.firstBlock?(this.Editor.Caret.setToBlock(this.Editor.BlockManager.firstBlock,e,t),!0):!1,this.setToLastBlock=(e=this.Editor.Caret.positions.DEFAULT,t=0)=>this.Editor.BlockManager.lastBlock?(this.Editor.Caret.setToBlock(this.Editor.BlockManager.lastBlock,e,t),!0):!1,this.setToPreviousBlock=(e=this.Editor.Caret.positions.DEFAULT,t=0)=>this.Editor.BlockManager.previousBlock?(this.Editor.Caret.setToBlock(this.Editor.BlockManager.previousBlock,e,t),!0):!1,this.setToNextBlock=(e=this.Editor.Caret.positions.DEFAULT,t=0)=>this.Editor.BlockManager.nextBlock?(this.Editor.Caret.setToBlock(this.Editor.BlockManager.nextBlock,e,t),!0):!1,this.setToBlock=(e,t=this.Editor.Caret.positions.DEFAULT,o=0)=>{const i=di(e,this.Editor);return i===void 0?!1:(this.Editor.Caret.setToBlock(i,t,o),!0)},this.focus=(e=!1)=>e?this.setToLastBlock(this.Editor.Caret.positions.END):this.setToFirstBlock(this.Editor.Caret.positions.START)}get methods(){return{setToFirstBlock:this.setToFirstBlock,setToLastBlock:this.setToLastBlock,setToPreviousBlock:this.setToPreviousBlock,setToNextBlock:this.setToNextBlock,setToBlock:this.setToBlock,focus:this.focus}}}class hi extends E{get methods(){return{emit:(e,t)=>this.emit(e,t),off:(e,t)=>this.off(e,t),on:(e,t)=>this.on(e,t)}}on(e,t){this.eventsDispatcher.on(e,t)}emit(e,t){this.eventsDispatcher.emit(e,t)}off(e,t){this.eventsDispatcher.off(e,t)}}class ct extends E{static getNamespace(e,t){return t?`blockTunes.${e}`:`tools.${e}`}get methods(){return{t:()=>{X("I18n.t() method can be accessed only from Tools","warn")}}}getMethodsForTool(e,t){return Object.assign(this.methods,{t:o=>H.t(ct.getNamespace(e,t),o)})}}class pi extends E{get methods(){return{blocks:this.Editor.BlocksAPI.methods,caret:this.Editor.CaretAPI.methods,tools:this.Editor.ToolsAPI.methods,events:this.Editor.EventsAPI.methods,listeners:this.Editor.ListenersAPI.methods,notifier:this.Editor.NotifierAPI.methods,sanitizer:this.Editor.SanitizerAPI.methods,saver:this.Editor.SaverAPI.methods,selection:this.Editor.SelectionAPI.methods,styles:this.Editor.StylesAPI.classes,toolbar:this.Editor.ToolbarAPI.methods,inlineToolbar:this.Editor.InlineToolbarAPI.methods,tooltip:this.Editor.TooltipAPI.methods,i18n:this.Editor.I18nAPI.methods,readOnly:this.Editor.ReadOnlyAPI.methods,ui:this.Editor.UiAPI.methods}}getMethodsForTool(e,t){return Object.assign(this.methods,{i18n:this.Editor.I18nAPI.getMethodsForTool(e,t)})}}class fi extends E{get methods(){return{close:()=>this.close(),open:()=>this.open()}}open(){this.Editor.InlineToolbar.tryToShow()}close(){this.Editor.InlineToolbar.close()}}class gi extends E{get methods(){return{on:(e,t,o,i)=>this.on(e,t,o,i),off:(e,t,o,i)=>this.off(e,t,o,i),offById:e=>this.offById(e)}}on(e,t,o,i){return this.listeners.on(e,t,o,i)}off(e,t,o,i){this.listeners.off(e,t,o,i)}offById(e){this.listeners.offById(e)}}var Do={exports:{}};(function(n,e){(function(t,o){n.exports=o()})(window,function(){return function(t){var o={};function i(s){if(o[s])return o[s].exports;var r=o[s]={i:s,l:!1,exports:{}};return t[s].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=o,i.d=function(s,r,a){i.o(s,r)||Object.defineProperty(s,r,{enumerable:!0,get:a})},i.r=function(s){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})},i.t=function(s,r){if(1&r&&(s=i(s)),8&r||4&r&&typeof s=="object"&&s&&s.__esModule)return s;var a=Object.create(null);if(i.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:s}),2&r&&typeof s!="string")for(var l in s)i.d(a,l,(function(c){return s[c]}).bind(null,l));return a},i.n=function(s){var r=s&&s.__esModule?function(){return s.default}:function(){return s};return i.d(r,"a",r),r},i.o=function(s,r){return Object.prototype.hasOwnProperty.call(s,r)},i.p="/",i(i.s=0)}([function(t,o,i){i(1),t.exports=function(){var s=i(6),r="cdx-notify--bounce-in",a=null;return{show:function(l){if(l.message){(function(){if(a)return!0;a=s.getWrapper(),document.body.appendChild(a)})();var c=null,u=l.time||8e3;switch(l.type){case"confirm":c=s.confirm(l);break;case"prompt":c=s.prompt(l);break;default:c=s.alert(l),window.setTimeout(function(){c.remove()},u)}a.appendChild(c),c.classList.add(r)}}}}()},function(t,o,i){var s=i(2);typeof s=="string"&&(s=[[t.i,s,""]]);var r={hmr:!0,transform:void 0,insertInto:void 0};i(4)(s,r),s.locals&&(t.exports=s.locals)},function(t,o,i){(t.exports=i(3)(!1)).push([t.i,`.cdx-notify--error{background:#fffbfb!important}.cdx-notify--error::before{background:#fb5d5d!important}.cdx-notify__input{max-width:130px;padding:5px 10px;background:#f7f7f7;border:0;border-radius:3px;font-size:13px;color:#656b7c;outline:0}.cdx-notify__input:-ms-input-placeholder{color:#656b7c}.cdx-notify__input::placeholder{color:#656b7c}.cdx-notify__input:focus:-ms-input-placeholder{color:rgba(101,107,124,.3)}.cdx-notify__input:focus::placeholder{color:rgba(101,107,124,.3)}.cdx-notify__button{border:none;border-radius:3px;font-size:13px;padding:5px 10px;cursor:pointer}.cdx-notify__button:last-child{margin-left:10px}.cdx-notify__button--cancel{background:#f2f5f7;box-shadow:0 2px 1px 0 rgba(16,19,29,0);color:#656b7c}.cdx-notify__button--cancel:hover{background:#eee}.cdx-notify__button--confirm{background:#34c992;box-shadow:0 1px 1px 0 rgba(18,49,35,.05);color:#fff}.cdx-notify__button--confirm:hover{background:#33b082}.cdx-notify__btns-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;margin-top:5px}.cdx-notify__cross{position:absolute;top:5px;right:5px;width:10px;height:10px;padding:5px;opacity:.54;cursor:pointer}.cdx-notify__cross::after,.cdx-notify__cross::before{content:'';position:absolute;left:9px;top:5px;height:12px;width:2px;background:#575d67}.cdx-notify__cross::before{transform:rotate(-45deg)}.cdx-notify__cross::after{transform:rotate(45deg)}.cdx-notify__cross:hover{opacity:1}.cdx-notifies{position:fixed;z-index:2;bottom:20px;left:20px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Fira Sans","Droid Sans","Helvetica Neue",sans-serif}.cdx-notify{position:relative;width:220px;margin-top:15px;padding:13px 16px;background:#fff;box-shadow:0 11px 17px 0 rgba(23,32,61,.13);border-radius:5px;font-size:14px;line-height:1.4em;word-wrap:break-word}.cdx-notify::before{content:'';position:absolute;display:block;top:0;left:0;width:3px;height:calc(100% - 6px);margin:3px;border-radius:5px;background:0 0}@keyframes bounceIn{0%{opacity:0;transform:scale(.3)}50%{opacity:1;transform:scale(1.05)}70%{transform:scale(.9)}100%{transform:scale(1)}}.cdx-notify--bounce-in{animation-name:bounceIn;animation-duration:.6s;animation-iteration-count:1}.cdx-notify--success{background:#fafffe!important}.cdx-notify--success::before{background:#41ffb1!important}`,""])},function(t,o){t.exports=function(i){var s=[];return s.toString=function(){return this.map(function(r){var a=function(l,c){var u=l[1]||"",h=l[3];if(!h)return u;if(c&&typeof btoa=="function"){var p=(f=h,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(f))))+" */"),g=h.sources.map(function(v){return"/*# sourceURL="+h.sourceRoot+v+" */"});return[u].concat(g).concat([p]).join(`
`)}var f;return[u].join(`
`)}(r,i);return r[2]?"@media "+r[2]+"{"+a+"}":a}).join("")},s.i=function(r,a){typeof r=="string"&&(r=[[null,r,""]]);for(var l={},c=0;c<this.length;c++){var u=this[c][0];typeof u=="number"&&(l[u]=!0)}for(c=0;c<r.length;c++){var h=r[c];typeof h[0]=="number"&&l[h[0]]||(a&&!h[2]?h[2]=a:a&&(h[2]="("+h[2]+") and ("+a+")"),s.push(h))}},s}},function(t,o,i){var s,r,a={},l=(s=function(){return window&&document&&document.all&&!window.atob},function(){return r===void 0&&(r=s.apply(this,arguments)),r}),c=function(k){var m={};return function(w){if(typeof w=="function")return w();if(m[w]===void 0){var x=(function(I){return document.querySelector(I)}).call(this,w);if(window.HTMLIFrameElement&&x instanceof window.HTMLIFrameElement)try{x=x.contentDocument.head}catch{x=null}m[w]=x}return m[w]}}(),u=null,h=0,p=[],g=i(5);function f(k,m){for(var w=0;w<k.length;w++){var x=k[w],I=a[x.id];if(I){I.refs++;for(var C=0;C<I.parts.length;C++)I.parts[C](x.parts[C]);for(;C<x.parts.length;C++)I.parts.push(j(x.parts[C],m))}else{var D=[];for(C=0;C<x.parts.length;C++)D.push(j(x.parts[C],m));a[x.id]={id:x.id,refs:1,parts:D}}}}function v(k,m){for(var w=[],x={},I=0;I<k.length;I++){var C=k[I],D=m.base?C[0]+m.base:C[0],B={css:C[1],media:C[2],sourceMap:C[3]};x[D]?x[D].parts.push(B):w.push(x[D]={id:D,parts:[B]})}return w}function O(k,m){var w=c(k.insertInto);if(!w)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var x=p[p.length-1];if(k.insertAt==="top")x?x.nextSibling?w.insertBefore(m,x.nextSibling):w.appendChild(m):w.insertBefore(m,w.firstChild),p.push(m);else if(k.insertAt==="bottom")w.appendChild(m);else{if(typeof k.insertAt!="object"||!k.insertAt.before)throw new Error(`[Style Loader]

 Invalid value for parameter 'insertAt' ('options.insertAt') found.
 Must be 'top', 'bottom', or Object.
 (https://github.com/webpack-contrib/style-loader#insertat)
`);var I=c(k.insertInto+" "+k.insertAt.before);w.insertBefore(m,I)}}function S(k){if(k.parentNode===null)return!1;k.parentNode.removeChild(k);var m=p.indexOf(k);m>=0&&p.splice(m,1)}function A(k){var m=document.createElement("style");return k.attrs.type===void 0&&(k.attrs.type="text/css"),G(m,k.attrs),O(k,m),m}function G(k,m){Object.keys(m).forEach(function(w){k.setAttribute(w,m[w])})}function j(k,m){var w,x,I,C;if(m.transform&&k.css){if(!(C=m.transform(k.css)))return function(){};k.css=C}if(m.singleton){var D=h++;w=u||(u=A(m)),x=he.bind(null,w,D,!1),I=he.bind(null,w,D,!0)}else k.sourceMap&&typeof URL=="function"&&typeof URL.createObjectURL=="function"&&typeof URL.revokeObjectURL=="function"&&typeof Blob=="function"&&typeof btoa=="function"?(w=function(B){var K=document.createElement("link");return B.attrs.type===void 0&&(B.attrs.type="text/css"),B.attrs.rel="stylesheet",G(K,B.attrs),O(B,K),K}(m),x=(function(B,K,Oe){var pe=Oe.css,vo=Oe.sourceMap,Ca=K.convertToAbsoluteUrls===void 0&&vo;(K.convertToAbsoluteUrls||Ca)&&(pe=g(pe)),vo&&(pe+=`
/*# sourceMappingURL=data:application/json;base64,`+btoa(unescape(encodeURIComponent(JSON.stringify(vo))))+" */");var Ta=new Blob([pe],{type:"text/css"}),$n=B.href;B.href=URL.createObjectURL(Ta),$n&&URL.revokeObjectURL($n)}).bind(null,w,m),I=function(){S(w),w.href&&URL.revokeObjectURL(w.href)}):(w=A(m),x=(function(B,K){var Oe=K.css,pe=K.media;if(pe&&B.setAttribute("media",pe),B.styleSheet)B.styleSheet.cssText=Oe;else{for(;B.firstChild;)B.removeChild(B.firstChild);B.appendChild(document.createTextNode(Oe))}}).bind(null,w),I=function(){S(w)});return x(k),function(B){if(B){if(B.css===k.css&&B.media===k.media&&B.sourceMap===k.sourceMap)return;x(k=B)}else I()}}t.exports=function(k,m){if(typeof DEBUG<"u"&&DEBUG&&typeof document!="object")throw new Error("The style-loader cannot be used in a non-browser environment");(m=m||{}).attrs=typeof m.attrs=="object"?m.attrs:{},m.singleton||typeof m.singleton=="boolean"||(m.singleton=l()),m.insertInto||(m.insertInto="head"),m.insertAt||(m.insertAt="bottom");var w=v(k,m);return f(w,m),function(x){for(var I=[],C=0;C<w.length;C++){var D=w[C];(B=a[D.id]).refs--,I.push(B)}for(x&&f(v(x,m),m),C=0;C<I.length;C++){var B;if((B=I[C]).refs===0){for(var K=0;K<B.parts.length;K++)B.parts[K]();delete a[B.id]}}}};var U,oe=(U=[],function(k,m){return U[k]=m,U.filter(Boolean).join(`
`)});function he(k,m,w,x){var I=w?"":x.css;if(k.styleSheet)k.styleSheet.cssText=oe(m,I);else{var C=document.createTextNode(I),D=k.childNodes;D[m]&&k.removeChild(D[m]),D.length?k.insertBefore(C,D[m]):k.appendChild(C)}}},function(t,o){t.exports=function(i){var s=typeof window<"u"&&window.location;if(!s)throw new Error("fixUrls requires window.location");if(!i||typeof i!="string")return i;var r=s.protocol+"//"+s.host,a=r+s.pathname.replace(/\/[^\/]*$/,"/");return i.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(l,c){var u,h=c.trim().replace(/^"(.*)"$/,function(p,g){return g}).replace(/^'(.*)'$/,function(p,g){return g});return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(h)?l:(u=h.indexOf("//")===0?h:h.indexOf("/")===0?r+h:a+h.replace(/^\.\//,""),"url("+JSON.stringify(u)+")")})}},function(t,o,i){var s,r,a,l,c,u,h,p,g;t.exports=(s="cdx-notifies",r="cdx-notify",a="cdx-notify__cross",l="cdx-notify__button--confirm",c="cdx-notify__button--cancel",u="cdx-notify__input",h="cdx-notify__button",p="cdx-notify__btns-wrapper",{alert:g=function(f){var v=document.createElement("DIV"),O=document.createElement("DIV"),S=f.message,A=f.style;return v.classList.add(r),A&&v.classList.add(r+"--"+A),v.innerHTML=S,O.classList.add(a),O.addEventListener("click",v.remove.bind(v)),v.appendChild(O),v},confirm:function(f){var v=g(f),O=document.createElement("div"),S=document.createElement("button"),A=document.createElement("button"),G=v.querySelector("."+a),j=f.cancelHandler,U=f.okHandler;return O.classList.add(p),S.innerHTML=f.okText||"Confirm",A.innerHTML=f.cancelText||"Cancel",S.classList.add(h),A.classList.add(h),S.classList.add(l),A.classList.add(c),j&&typeof j=="function"&&(A.addEventListener("click",j),G.addEventListener("click",j)),U&&typeof U=="function"&&S.addEventListener("click",U),S.addEventListener("click",v.remove.bind(v)),A.addEventListener("click",v.remove.bind(v)),O.appendChild(S),O.appendChild(A),v.appendChild(O),v},prompt:function(f){var v=g(f),O=document.createElement("div"),S=document.createElement("button"),A=document.createElement("input"),G=v.querySelector("."+a),j=f.cancelHandler,U=f.okHandler;return O.classList.add(p),S.innerHTML=f.okText||"Ok",S.classList.add(h),S.classList.add(l),A.classList.add(u),f.placeholder&&A.setAttribute("placeholder",f.placeholder),f.default&&(A.value=f.default),f.inputType&&(A.type=f.inputType),j&&typeof j=="function"&&G.addEventListener("click",j),U&&typeof U=="function"&&S.addEventListener("click",function(){U(A.value)}),S.addEventListener("click",v.remove.bind(v)),O.appendChild(A),O.appendChild(S),v.appendChild(O),v},getWrapper:function(){var f=document.createElement("DIV");return f.classList.add(s),f}})}])})})(Do);var mi=Do.exports;const bi=ne(mi);class vi{show(e){bi.show(e)}}class ki extends E{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t}),this.notifier=new vi}get methods(){return{show:e=>this.show(e)}}show(e){return this.notifier.show(e)}}class yi extends E{get methods(){const e=()=>this.isEnabled;return{toggle:t=>this.toggle(t),get isEnabled(){return e()}}}toggle(e){return this.Editor.ReadOnly.toggle(e)}get isEnabled(){return this.Editor.ReadOnly.isEnabled}}var Fo={exports:{}};(function(n,e){(function(t,o){n.exports=o()})(J,function(){function t(h){var p=h.tags,g=Object.keys(p),f=g.map(function(v){return typeof p[v]}).every(function(v){return v==="object"||v==="boolean"||v==="function"});if(!f)throw new Error("The configuration was invalid");this.config=h}var o=["P","LI","TD","TH","DIV","H1","H2","H3","H4","H5","H6","PRE"];function i(h){return o.indexOf(h.nodeName)!==-1}var s=["A","B","STRONG","I","EM","SUB","SUP","U","STRIKE"];function r(h){return s.indexOf(h.nodeName)!==-1}t.prototype.clean=function(h){const p=document.implementation.createHTMLDocument(),g=p.createElement("div");return g.innerHTML=h,this._sanitize(p,g),g.innerHTML},t.prototype._sanitize=function(h,p){var g=a(h,p),f=g.firstChild();if(f)do{if(f.nodeType===Node.TEXT_NODE)if(f.data.trim()===""&&(f.previousElementSibling&&i(f.previousElementSibling)||f.nextElementSibling&&i(f.nextElementSibling))){p.removeChild(f),this._sanitize(h,p);break}else continue;if(f.nodeType===Node.COMMENT_NODE){p.removeChild(f),this._sanitize(h,p);break}var v=r(f),O;v&&(O=Array.prototype.some.call(f.childNodes,i));var S=!!p.parentNode,A=i(p)&&i(f)&&S,G=f.nodeName.toLowerCase(),j=l(this.config,G,f),U=v&&O;if(U||c(f,j)||!this.config.keepNestedBlockElements&&A){if(!(f.nodeName==="SCRIPT"||f.nodeName==="STYLE"))for(;f.childNodes.length>0;)p.insertBefore(f.childNodes[0],f);p.removeChild(f),this._sanitize(h,p);break}for(var oe=0;oe<f.attributes.length;oe+=1){var he=f.attributes[oe];u(he,j,f)&&(f.removeAttribute(he.name),oe=oe-1)}this._sanitize(h,f)}while(f=g.nextSibling())};function a(h,p){return h.createTreeWalker(p,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_COMMENT,null,!1)}function l(h,p,g){return typeof h.tags[p]=="function"?h.tags[p](g):h.tags[p]}function c(h,p){return typeof p>"u"?!0:typeof p=="boolean"?!p:!1}function u(h,p,g){var f=h.name.toLowerCase();return p===!0?!1:typeof p[f]=="function"?!p[f](h.value,g):typeof p[f]>"u"||p[f]===!1?!0:typeof p[f]=="string"?p[f]!==h.value:!1}return t})})(Fo);var wi=Fo.exports;const Ei=ne(wi);function dt(n,e){return n.map(t=>{const o=M(e)?e(t.tool):e;return V(o)||(t.data=ut(t.data,o)),t})}function q(n,e={}){const t={tags:e};return new Ei(t).clean(n)}function ut(n,e){return Array.isArray(n)?xi(n,e):N(n)?Bi(n,e):Q(n)?Ci(n,e):n}function xi(n,e){return n.map(t=>ut(t,e))}function Bi(n,e){const t={};for(const o in n){if(!Object.prototype.hasOwnProperty.call(n,o))continue;const i=n[o],s=Ti(e[o])?e[o]:e;t[o]=ut(i,s)}return t}function Ci(n,e){return N(e)?q(n,e):e===!1?q(n,{}):n}function Ti(n){return N(n)||qn(n)||M(n)}class Si extends E{get methods(){return{clean:(e,t)=>this.clean(e,t)}}clean(e,t){return q(e,t)}}class Ii extends E{get methods(){return{save:()=>this.save()}}save(){const e="Editor's content can not be saved in read-only mode";return this.Editor.ReadOnly.isEnabled?(X(e,"warn"),Promise.reject(new Error(e))):this.Editor.Saver.save()}}class Oi extends E{constructor(){super(...arguments),this.selectionUtils=new b}get methods(){return{findParentTag:(e,t)=>this.findParentTag(e,t),expandToTag:e=>this.expandToTag(e),save:()=>this.selectionUtils.save(),restore:()=>this.selectionUtils.restore(),setFakeBackground:()=>this.selectionUtils.setFakeBackground(),removeFakeBackground:()=>this.selectionUtils.removeFakeBackground()}}findParentTag(e,t){return this.selectionUtils.findParentTag(e,t)}expandToTag(e){this.selectionUtils.expandToTag(e)}}class _i extends E{get methods(){return{getBlockTools:()=>Array.from(this.Editor.Tools.blockTools.values())}}}class Mi extends E{get classes(){return{block:"cdx-block",inlineToolButton:"ce-inline-tool",inlineToolButtonActive:"ce-inline-tool--active",input:"cdx-input",loader:"cdx-loader",button:"cdx-button",settingsButton:"cdx-settings-button",settingsButtonActive:"cdx-settings-button--active"}}}class Ai extends E{get methods(){return{close:()=>this.close(),open:()=>this.open(),toggleBlockSettings:e=>this.toggleBlockSettings(e),toggleToolbox:e=>this.toggleToolbox(e)}}open(){this.Editor.Toolbar.moveAndOpen()}close(){this.Editor.Toolbar.close()}toggleBlockSettings(e){if(this.Editor.BlockManager.currentBlockIndex===-1){X("Could't toggle the Toolbar because there is no block selected ","warn");return}e??!this.Editor.BlockSettings.opened?(this.Editor.Toolbar.moveAndOpen(),this.Editor.BlockSettings.open()):this.Editor.BlockSettings.close()}toggleToolbox(e){if(this.Editor.BlockManager.currentBlockIndex===-1){X("Could't toggle the Toolbox because there is no block selected ","warn");return}e??!this.Editor.Toolbar.toolbox.opened?(this.Editor.Toolbar.moveAndOpen(),this.Editor.Toolbar.toolbox.open()):this.Editor.Toolbar.toolbox.close()}}var jo={exports:{}};/*!
 * CodeX.Tooltips
 * 
 * @version 1.0.5
 * 
 * @licence MIT
 * <AUTHOR> <https://codex.so>
 * 
 * 
 */(function(n,e){(function(t,o){n.exports=o()})(window,function(){return function(t){var o={};function i(s){if(o[s])return o[s].exports;var r=o[s]={i:s,l:!1,exports:{}};return t[s].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=o,i.d=function(s,r,a){i.o(s,r)||Object.defineProperty(s,r,{enumerable:!0,get:a})},i.r=function(s){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})},i.t=function(s,r){if(1&r&&(s=i(s)),8&r||4&r&&typeof s=="object"&&s&&s.__esModule)return s;var a=Object.create(null);if(i.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:s}),2&r&&typeof s!="string")for(var l in s)i.d(a,l,(function(c){return s[c]}).bind(null,l));return a},i.n=function(s){var r=s&&s.__esModule?function(){return s.default}:function(){return s};return i.d(r,"a",r),r},i.o=function(s,r){return Object.prototype.hasOwnProperty.call(s,r)},i.p="",i(i.s=0)}([function(t,o,i){t.exports=i(1)},function(t,o,i){i.r(o),i.d(o,"default",function(){return s});class s{constructor(){this.nodes={wrapper:null,content:null},this.showed=!1,this.offsetTop=10,this.offsetLeft=10,this.offsetRight=10,this.hidingDelay=0,this.handleWindowScroll=()=>{this.showed&&this.hide(!0)},this.loadStyles(),this.prepare(),window.addEventListener("scroll",this.handleWindowScroll,{passive:!0})}get CSS(){return{tooltip:"ct",tooltipContent:"ct__content",tooltipShown:"ct--shown",placement:{left:"ct--left",bottom:"ct--bottom",right:"ct--right",top:"ct--top"}}}show(a,l,c){this.nodes.wrapper||this.prepare(),this.hidingTimeout&&clearTimeout(this.hidingTimeout);const u=Object.assign({placement:"bottom",marginTop:0,marginLeft:0,marginRight:0,marginBottom:0,delay:70,hidingDelay:0},c);if(u.hidingDelay&&(this.hidingDelay=u.hidingDelay),this.nodes.content.innerHTML="",typeof l=="string")this.nodes.content.appendChild(document.createTextNode(l));else{if(!(l instanceof Node))throw Error("[CodeX Tooltip] Wrong type of «content» passed. It should be an instance of Node or String. But "+typeof l+" given.");this.nodes.content.appendChild(l)}switch(this.nodes.wrapper.classList.remove(...Object.values(this.CSS.placement)),u.placement){case"top":this.placeTop(a,u);break;case"left":this.placeLeft(a,u);break;case"right":this.placeRight(a,u);break;case"bottom":default:this.placeBottom(a,u)}u&&u.delay?this.showingTimeout=setTimeout(()=>{this.nodes.wrapper.classList.add(this.CSS.tooltipShown),this.showed=!0},u.delay):(this.nodes.wrapper.classList.add(this.CSS.tooltipShown),this.showed=!0)}hide(a=!1){if(this.hidingDelay&&!a)return this.hidingTimeout&&clearTimeout(this.hidingTimeout),void(this.hidingTimeout=setTimeout(()=>{this.hide(!0)},this.hidingDelay));this.nodes.wrapper.classList.remove(this.CSS.tooltipShown),this.showed=!1,this.showingTimeout&&clearTimeout(this.showingTimeout)}onHover(a,l,c){a.addEventListener("mouseenter",()=>{this.show(a,l,c)}),a.addEventListener("mouseleave",()=>{this.hide()})}destroy(){this.nodes.wrapper.remove(),window.removeEventListener("scroll",this.handleWindowScroll)}prepare(){this.nodes.wrapper=this.make("div",this.CSS.tooltip),this.nodes.content=this.make("div",this.CSS.tooltipContent),this.append(this.nodes.wrapper,this.nodes.content),this.append(document.body,this.nodes.wrapper)}loadStyles(){const a="codex-tooltips-style";if(document.getElementById(a))return;const l=i(2),c=this.make("style",null,{textContent:l.toString(),id:a});this.prepend(document.head,c)}placeBottom(a,l){const c=a.getBoundingClientRect(),u=c.left+a.clientWidth/2-this.nodes.wrapper.offsetWidth/2,h=c.bottom+window.pageYOffset+this.offsetTop+l.marginTop;this.applyPlacement("bottom",u,h)}placeTop(a,l){const c=a.getBoundingClientRect(),u=c.left+a.clientWidth/2-this.nodes.wrapper.offsetWidth/2,h=c.top+window.pageYOffset-this.nodes.wrapper.clientHeight-this.offsetTop;this.applyPlacement("top",u,h)}placeLeft(a,l){const c=a.getBoundingClientRect(),u=c.left-this.nodes.wrapper.offsetWidth-this.offsetLeft-l.marginLeft,h=c.top+window.pageYOffset+a.clientHeight/2-this.nodes.wrapper.offsetHeight/2;this.applyPlacement("left",u,h)}placeRight(a,l){const c=a.getBoundingClientRect(),u=c.right+this.offsetRight+l.marginRight,h=c.top+window.pageYOffset+a.clientHeight/2-this.nodes.wrapper.offsetHeight/2;this.applyPlacement("right",u,h)}applyPlacement(a,l,c){this.nodes.wrapper.classList.add(this.CSS.placement[a]),this.nodes.wrapper.style.left=l+"px",this.nodes.wrapper.style.top=c+"px"}make(a,l=null,c={}){const u=document.createElement(a);Array.isArray(l)?u.classList.add(...l):l&&u.classList.add(l);for(const h in c)c.hasOwnProperty(h)&&(u[h]=c[h]);return u}append(a,l){Array.isArray(l)?l.forEach(c=>a.appendChild(c)):a.appendChild(l)}prepend(a,l){Array.isArray(l)?(l=l.reverse()).forEach(c=>a.prepend(c)):a.prepend(l)}}},function(t,o){t.exports=`.ct{z-index:999;opacity:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none;-webkit-transition:opacity 50ms ease-in,-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,transform 70ms cubic-bezier(.215,.61,.355,1),-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);will-change:opacity,top,left;-webkit-box-shadow:0 8px 12px 0 rgba(29,32,43,.17),0 4px 5px -3px rgba(5,6,12,.49);box-shadow:0 8px 12px 0 rgba(29,32,43,.17),0 4px 5px -3px rgba(5,6,12,.49);border-radius:9px}.ct,.ct:before{position:absolute;top:0;left:0}.ct:before{content:"";bottom:0;right:0;background-color:#1d202b;z-index:-1;border-radius:4px}@supports(-webkit-mask-box-image:url("")){.ct:before{border-radius:0;-webkit-mask-box-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M10.71 0h2.58c3.02 0 4.64.42 6.1 1.2a8.18 8.18 0 013.4 3.4C23.6 6.07 24 7.7 24 10.71v2.58c0 3.02-.42 4.64-1.2 6.1a8.18 8.18 0 01-3.4 3.4c-1.47.8-3.1 1.21-6.11 1.21H10.7c-3.02 0-4.64-.42-6.1-1.2a8.18 8.18 0 01-3.4-3.4C.4 17.93 0 16.3 0 13.29V10.7c0-3.02.42-4.64 1.2-6.1a8.18 8.18 0 013.4-3.4C6.07.4 7.7 0 10.71 0z"/></svg>') 48% 41% 37.9% 53.3%}}@media (--mobile){.ct{display:none}}.ct__content{padding:6px 10px;color:#cdd1e0;font-size:12px;text-align:center;letter-spacing:.02em;line-height:1em}.ct:after{content:"";width:8px;height:8px;position:absolute;background-color:#1d202b;z-index:-1}.ct--bottom{-webkit-transform:translateY(5px);transform:translateY(5px)}.ct--bottom:after{top:-3px;left:50%;-webkit-transform:translateX(-50%) rotate(-45deg);transform:translateX(-50%) rotate(-45deg)}.ct--top{-webkit-transform:translateY(-5px);transform:translateY(-5px)}.ct--top:after{top:auto;bottom:-3px;left:50%;-webkit-transform:translateX(-50%) rotate(-45deg);transform:translateX(-50%) rotate(-45deg)}.ct--left{-webkit-transform:translateX(-5px);transform:translateX(-5px)}.ct--left:after{top:50%;left:auto;right:0;-webkit-transform:translate(41.6%,-50%) rotate(-45deg);transform:translate(41.6%,-50%) rotate(-45deg)}.ct--right{-webkit-transform:translateX(5px);transform:translateX(5px)}.ct--right:after{top:50%;left:0;-webkit-transform:translate(-41.6%,-50%) rotate(-45deg);transform:translate(-41.6%,-50%) rotate(-45deg)}.ct--shown{opacity:1;-webkit-transform:none;transform:none}`}]).default})})(jo);var Li=jo.exports;const Pi=ne(Li);let $=null;function ht(){$||($=new Pi)}function Ni(n,e,t){ht(),$==null||$.show(n,e,t)}function Ne(n=!1){ht(),$==null||$.hide(n)}function Re(n,e,t){ht(),$==null||$.onHover(n,e,t)}function Ri(){$==null||$.destroy(),$=null}class Di extends E{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t})}get methods(){return{show:(e,t,o)=>this.show(e,t,o),hide:()=>this.hide(),onHover:(e,t,o)=>this.onHover(e,t,o)}}show(e,t,o){Ni(e,t,o)}hide(){Ne()}onHover(e,t,o){Re(e,t,o)}}class Fi extends E{get methods(){return{nodes:this.editorNodes}}get editorNodes(){return{wrapper:this.Editor.UI.nodes.wrapper,redactor:this.Editor.UI.nodes.redactor}}}function Ho(n,e){const t={};return Object.entries(n).forEach(([o,i])=>{if(N(i)){const s=e?`${e}.${o}`:o;Object.values(i).every(a=>Q(a))?t[o]=s:t[o]=Ho(i,s);return}t[o]=i}),t}const W=Ho(So);function ji(n,e){const t={};return Object.keys(n).forEach(o=>{const i=e[o];i!==void 0?t[i]=n[o]:t[o]=n[o]}),t}const $o=class _e{constructor(e,t){this.cursor=-1,this.items=[],this.items=e||[],this.focusedCssClass=t}get currentItem(){return this.cursor===-1?null:this.items[this.cursor]}setCursor(e){e<this.items.length&&e>=-1&&(this.dropCursor(),this.cursor=e,this.items[this.cursor].classList.add(this.focusedCssClass))}setItems(e){this.items=e}next(){this.cursor=this.leafNodesAndReturnIndex(_e.directions.RIGHT)}previous(){this.cursor=this.leafNodesAndReturnIndex(_e.directions.LEFT)}dropCursor(){this.cursor!==-1&&(this.items[this.cursor].classList.remove(this.focusedCssClass),this.cursor=-1)}leafNodesAndReturnIndex(e){if(this.items.length===0)return this.cursor;let t=this.cursor;return t===-1?t=e===_e.directions.RIGHT?-1:0:this.items[t].classList.remove(this.focusedCssClass),e===_e.directions.RIGHT?t=(t+1)%this.items.length:t=(this.items.length+t-1)%this.items.length,d.canSetCaret(this.items[t])&&Ae(()=>b.setCursor(this.items[t]),50)(),this.items[t].classList.add(this.focusedCssClass),t}};$o.directions={RIGHT:"right",LEFT:"left"};let Be=$o;class ae{constructor(e){this.iterator=null,this.activated=!1,this.flipCallbacks=[],this.onKeyDown=t=>{if(this.isEventReadyForHandling(t))switch(ae.usedKeys.includes(t.keyCode)&&t.preventDefault(),t.keyCode){case y.TAB:this.handleTabPress(t);break;case y.LEFT:case y.UP:this.flipLeft();break;case y.RIGHT:case y.DOWN:this.flipRight();break;case y.ENTER:this.handleEnterPress(t);break}},this.iterator=new Be(e.items,e.focusedItemClass),this.activateCallback=e.activateCallback,this.allowedKeys=e.allowedKeys||ae.usedKeys}get isActivated(){return this.activated}static get usedKeys(){return[y.TAB,y.LEFT,y.RIGHT,y.ENTER,y.UP,y.DOWN]}activate(e,t){this.activated=!0,e&&this.iterator.setItems(e),t!==void 0&&this.iterator.setCursor(t),document.addEventListener("keydown",this.onKeyDown,!0)}deactivate(){this.activated=!1,this.dropCursor(),document.removeEventListener("keydown",this.onKeyDown)}focusFirst(){this.dropCursor(),this.flipRight()}flipLeft(){this.iterator.previous(),this.flipCallback()}flipRight(){this.iterator.next(),this.flipCallback()}hasFocus(){return!!this.iterator.currentItem}onFlip(e){this.flipCallbacks.push(e)}removeOnFlip(e){this.flipCallbacks=this.flipCallbacks.filter(t=>t!==e)}dropCursor(){this.iterator.dropCursor()}isEventReadyForHandling(e){return this.activated&&this.allowedKeys.includes(e.keyCode)}handleTabPress(e){switch(e.shiftKey?Be.directions.LEFT:Be.directions.RIGHT){case Be.directions.RIGHT:this.flipRight();break;case Be.directions.LEFT:this.flipLeft();break}}handleEnterPress(e){this.activated&&(this.iterator.currentItem&&(e.stopPropagation(),e.preventDefault(),this.iterator.currentItem.click()),M(this.activateCallback)&&this.activateCallback(this.iterator.currentItem))}flipCallback(){this.iterator.currentItem&&this.iterator.currentItem.scrollIntoViewIfNeeded(),this.flipCallbacks.forEach(e=>e())}}const Hi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9 12L9 7.1C9 7.04477 9.04477 7 9.1 7H10.4C11.5 7 14 7.1 14 9.5C14 9.5 14 12 11 12M9 12V16.8C9 16.9105 9.08954 17 9.2 17H12.5C14 17 15 16 15 14.5C15 11.7046 11 12 11 12M9 12H11"/></svg>',$i='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 10L11.8586 14.8586C11.9367 14.9367 12.0633 14.9367 12.1414 14.8586L17 10"/></svg>',zi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M14.5 17.5L9.64142 12.6414C9.56331 12.5633 9.56331 12.4367 9.64142 12.3586L14.5 7.5"/></svg>',Ui='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9.58284 17.5L14.4414 12.6414C14.5195 12.5633 14.5195 12.4367 14.4414 12.3586L9.58284 7.5"/></svg>',Wi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 15L11.8586 10.1414C11.9367 10.0633 12.0633 10.0633 12.1414 10.1414L17 15"/></svg>',Yi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8 8L12 12M12 12L16 16M12 12L16 8M12 12L8 16"/></svg>',Ki='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="4" stroke="currentColor" stroke-width="2"/></svg>',Xi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M13.34 10C12.4223 12.7337 11 17 11 17"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M14.21 7H14.2"/></svg>',zo='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7.69998 12.6L7.67896 12.62C6.53993 13.7048 6.52012 15.5155 7.63516 16.625V16.625C8.72293 17.7073 10.4799 17.7102 11.5712 16.6314L13.0263 15.193C14.0703 14.1609 14.2141 12.525 13.3662 11.3266L13.22 11.12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16.22 11.12L16.3564 10.9805C17.2895 10.0265 17.3478 8.5207 16.4914 7.49733V7.49733C15.5691 6.39509 13.9269 6.25143 12.8271 7.17675L11.3901 8.38588C10.0935 9.47674 9.95706 11.4241 11.0888 12.6852L11.12 12.72"/></svg>',Vi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.40999 7.29999H9.4"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 7.29999H14.59"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.30999 12H9.3"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 12H14.59"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.40999 16.7H9.4"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 16.7H14.59"/></svg>',qi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12 7V12M12 17V12M17 12H12M12 12H7"/></svg>',Uo='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M11.5 17.5L5 11M5 11V15.5M5 11H9.5"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12.5 6.5L19 13M19 13V8.5M19 13H14.5"/></svg>',Zi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><circle cx="10.5" cy="10.5" r="5.5" stroke="currentColor" stroke-width="2"/><line x1="15.4142" x2="19" y1="15" y2="18.5858" stroke="currentColor" stroke-linecap="round" stroke-width="2"/></svg>',Gi='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M15.7795 11.5C15.7795 11.5 16.053 11.1962 16.5497 10.6722C17.4442 9.72856 17.4701 8.2475 16.5781 7.30145V7.30145C15.6482 6.31522 14.0873 6.29227 13.1288 7.25073L11.8796 8.49999"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8.24517 12.3883C8.24517 12.3883 7.97171 12.6922 7.47504 13.2161C6.58051 14.1598 6.55467 15.6408 7.44666 16.5869V16.5869C8.37653 17.5731 9.93744 17.5961 10.8959 16.6376L12.1452 15.3883"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M17.7802 15.1032L16.597 14.9422C16.0109 14.8624 15.4841 15.3059 15.4627 15.8969L15.4199 17.0818"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6.39064 9.03238L7.58432 9.06668C8.17551 9.08366 8.6522 8.58665 8.61056 7.99669L8.5271 6.81397"/><line x1="12.1142" x2="11.7" y1="12.2" y2="11.7858" stroke="currentColor" stroke-linecap="round" stroke-width="2"/></svg>',Ji='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><rect width="14" height="14" x="5" y="5" stroke="currentColor" stroke-width="2" rx="4"/><line x1="12" x2="12" y1="9" y2="12" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12 15.02V15.01"/></svg>',Qi="__",es="--";function ie(n){return(e,t)=>[[n,e].filter(i=>!!i).join(Qi),t].filter(i=>!!i).join(es)}const Ce=ie("ce-hint"),Te={root:Ce(),alignedStart:Ce(null,"align-left"),alignedCenter:Ce(null,"align-center"),title:Ce("title"),description:Ce("description")},Ma="";class ts{constructor(e){this.nodes={root:d.make("div",[Te.root,e.alignment==="center"?Te.alignedCenter:Te.alignedStart]),title:d.make("div",Te.title,{textContent:e.title})},this.nodes.root.appendChild(this.nodes.title),e.description!==void 0&&(this.nodes.description=d.make("div",Te.description,{textContent:e.description}),this.nodes.root.appendChild(this.nodes.description))}getElement(){return this.nodes.root}}class pt{constructor(e){this.params=e}get name(){if(this.params!==void 0&&"name"in this.params)return this.params.name}destroy(){Ne()}onChildrenOpen(){var e;this.params!==void 0&&"children"in this.params&&typeof((e=this.params.children)==null?void 0:e.onOpen)=="function"&&this.params.children.onOpen()}onChildrenClose(){var e;this.params!==void 0&&"children"in this.params&&typeof((e=this.params.children)==null?void 0:e.onClose)=="function"&&this.params.children.onClose()}handleClick(){var e,t;this.params!==void 0&&"onActivate"in this.params&&((t=(e=this.params).onActivate)==null||t.call(e,this.params))}addHint(e,t){const o=new ts(t);Re(e,o.getElement(),{placement:t.position,hidingDelay:100})}get children(){var e;return this.params!==void 0&&"children"in this.params&&((e=this.params.children)==null?void 0:e.items)!==void 0?this.params.children.items:[]}get hasChildren(){return this.children.length>0}get isChildrenOpen(){var e;return this.params!==void 0&&"children"in this.params&&((e=this.params.children)==null?void 0:e.isOpen)===!0}get isChildrenFlippable(){var e;return!(this.params===void 0||!("children"in this.params)||((e=this.params.children)==null?void 0:e.isFlippable)===!1)}get isChildrenSearchable(){var e;return this.params!==void 0&&"children"in this.params&&((e=this.params.children)==null?void 0:e.searchable)===!0}get closeOnActivate(){return this.params!==void 0&&"closeOnActivate"in this.params&&this.params.closeOnActivate}get isActive(){return this.params===void 0||!("isActive"in this.params)?!1:typeof this.params.isActive=="function"?this.params.isActive():this.params.isActive===!0}}const Y=ie("ce-popover-item"),L={container:Y(),active:Y(null,"active"),disabled:Y(null,"disabled"),focused:Y(null,"focused"),hidden:Y(null,"hidden"),confirmationState:Y(null,"confirmation"),noHover:Y(null,"no-hover"),noFocus:Y(null,"no-focus"),title:Y("title"),secondaryTitle:Y("secondary-title"),icon:Y("icon"),iconTool:Y("icon","tool"),iconChevronRight:Y("icon","chevron-right"),wobbleAnimation:ie("wobble")()};class le extends pt{constructor(e,t){super(e),this.params=e,this.nodes={root:null,icon:null},this.confirmationState=null,this.removeSpecialFocusBehavior=()=>{var o;(o=this.nodes.root)==null||o.classList.remove(L.noFocus)},this.removeSpecialHoverBehavior=()=>{var o;(o=this.nodes.root)==null||o.classList.remove(L.noHover)},this.onErrorAnimationEnd=()=>{var o,i;(o=this.nodes.icon)==null||o.classList.remove(L.wobbleAnimation),(i=this.nodes.icon)==null||i.removeEventListener("animationend",this.onErrorAnimationEnd)},this.nodes.root=this.make(e,t)}get isDisabled(){return this.params.isDisabled===!0}get toggle(){return this.params.toggle}get title(){return this.params.title}get isConfirmationStateEnabled(){return this.confirmationState!==null}get isFocused(){return this.nodes.root===null?!1:this.nodes.root.classList.contains(L.focused)}getElement(){return this.nodes.root}handleClick(){if(this.isConfirmationStateEnabled&&this.confirmationState!==null){this.activateOrEnableConfirmationMode(this.confirmationState);return}this.activateOrEnableConfirmationMode(this.params)}toggleActive(e){var t;(t=this.nodes.root)==null||t.classList.toggle(L.active,e)}toggleHidden(e){var t;(t=this.nodes.root)==null||t.classList.toggle(L.hidden,e)}reset(){this.isConfirmationStateEnabled&&this.disableConfirmationMode()}onFocus(){this.disableSpecialHoverAndFocusBehavior()}make(e,t){var s,r;const o=(t==null?void 0:t.wrapperTag)||"div",i=d.make(o,L.container,{type:o==="button"?"button":void 0});return e.name&&(i.dataset.itemName=e.name),this.nodes.icon=d.make("div",[L.icon,L.iconTool],{innerHTML:e.icon||Ki}),i.appendChild(this.nodes.icon),e.title!==void 0&&i.appendChild(d.make("div",L.title,{innerHTML:e.title||""})),e.secondaryLabel&&i.appendChild(d.make("div",L.secondaryTitle,{textContent:e.secondaryLabel})),this.hasChildren&&i.appendChild(d.make("div",[L.icon,L.iconChevronRight],{innerHTML:Ui})),this.isActive&&i.classList.add(L.active),e.isDisabled&&i.classList.add(L.disabled),e.hint!==void 0&&((s=t==null?void 0:t.hint)==null?void 0:s.enabled)!==!1&&this.addHint(i,{...e.hint,position:((r=t==null?void 0:t.hint)==null?void 0:r.position)||"right"}),i}enableConfirmationMode(e){if(this.nodes.root===null)return;const t={...this.params,...e,confirmation:"confirmation"in e?e.confirmation:void 0},o=this.make(t);this.nodes.root.innerHTML=o.innerHTML,this.nodes.root.classList.add(L.confirmationState),this.confirmationState=e,this.enableSpecialHoverAndFocusBehavior()}disableConfirmationMode(){if(this.nodes.root===null)return;const e=this.make(this.params);this.nodes.root.innerHTML=e.innerHTML,this.nodes.root.classList.remove(L.confirmationState),this.confirmationState=null,this.disableSpecialHoverAndFocusBehavior()}enableSpecialHoverAndFocusBehavior(){var e,t,o;(e=this.nodes.root)==null||e.classList.add(L.noHover),(t=this.nodes.root)==null||t.classList.add(L.noFocus),(o=this.nodes.root)==null||o.addEventListener("mouseleave",this.removeSpecialHoverBehavior,{once:!0})}disableSpecialHoverAndFocusBehavior(){var e;this.removeSpecialFocusBehavior(),this.removeSpecialHoverBehavior(),(e=this.nodes.root)==null||e.removeEventListener("mouseleave",this.removeSpecialHoverBehavior)}activateOrEnableConfirmationMode(e){var t;if(!("confirmation"in e)||e.confirmation===void 0)try{(t=e.onActivate)==null||t.call(e,e),this.disableConfirmationMode()}catch{this.animateError()}else this.enableConfirmationMode(e.confirmation)}animateError(){var e,t,o;(e=this.nodes.icon)!=null&&e.classList.contains(L.wobbleAnimation)||((t=this.nodes.icon)==null||t.classList.add(L.wobbleAnimation),(o=this.nodes.icon)==null||o.addEventListener("animationend",this.onErrorAnimationEnd))}}const ft=ie("ce-popover-item-separator"),gt={container:ft(),line:ft("line"),hidden:ft(null,"hidden")};class Wo extends pt{constructor(){super(),this.nodes={root:d.make("div",gt.container),line:d.make("div",gt.line)},this.nodes.root.appendChild(this.nodes.line)}getElement(){return this.nodes.root}toggleHidden(e){var t;(t=this.nodes.root)==null||t.classList.toggle(gt.hidden,e)}}var Z=(n=>(n.Closed="closed",n.ClosedOnActivate="closed-on-activate",n))(Z||{});const z=ie("ce-popover"),P={popover:z(),popoverContainer:z("container"),popoverOpenTop:z(null,"open-top"),popoverOpenLeft:z(null,"open-left"),popoverOpened:z(null,"opened"),search:z("search"),nothingFoundMessage:z("nothing-found-message"),nothingFoundMessageDisplayed:z("nothing-found-message","displayed"),items:z("items"),overlay:z("overlay"),overlayHidden:z("overlay","hidden"),popoverNested:z(null,"nested"),getPopoverNestedClass:n=>z(null,`nested-level-${n.toString()}`),popoverInline:z(null,"inline"),popoverHeader:z("header")};var me=(n=>(n.NestingLevel="--nesting-level",n.PopoverHeight="--popover-height",n.InlinePopoverWidth="--inline-popover-width",n.TriggerItemLeft="--trigger-item-left",n.TriggerItemTop="--trigger-item-top",n))(me||{});const Yo=ie("ce-popover-item-html"),Ko={root:Yo(),hidden:Yo(null,"hidden")};class Se extends pt{constructor(e,t){var o,i;super(e),this.nodes={root:d.make("div",Ko.root)},this.nodes.root.appendChild(e.element),e.name&&(this.nodes.root.dataset.itemName=e.name),e.hint!==void 0&&((o=t==null?void 0:t.hint)==null?void 0:o.enabled)!==!1&&this.addHint(this.nodes.root,{...e.hint,position:((i=t==null?void 0:t.hint)==null?void 0:i.position)||"right"})}getElement(){return this.nodes.root}toggleHidden(e){var t;(t=this.nodes.root)==null||t.classList.toggle(Ko.hidden,e)}getControls(){const e=this.nodes.root.querySelectorAll(`button, ${d.allInputsSelector}`);return Array.from(e)}}class Xo extends we{constructor(e,t={}){super(),this.params=e,this.itemsRenderParams=t,this.listeners=new Ee,this.messages={nothingFound:"Nothing found",search:"Search"},this.items=this.buildItems(e.items),e.messages&&(this.messages={...this.messages,...e.messages}),this.nodes={},this.nodes.popoverContainer=d.make("div",[P.popoverContainer]),this.nodes.nothingFoundMessage=d.make("div",[P.nothingFoundMessage],{textContent:this.messages.nothingFound}),this.nodes.popoverContainer.appendChild(this.nodes.nothingFoundMessage),this.nodes.items=d.make("div",[P.items]),this.items.forEach(o=>{const i=o.getElement();i!==null&&this.nodes.items.appendChild(i)}),this.nodes.popoverContainer.appendChild(this.nodes.items),this.listeners.on(this.nodes.popoverContainer,"click",o=>this.handleClick(o)),this.nodes.popover=d.make("div",[P.popover,this.params.class]),this.nodes.popover.appendChild(this.nodes.popoverContainer)}get itemsDefault(){return this.items.filter(e=>e instanceof le)}getElement(){return this.nodes.popover}show(){this.nodes.popover.classList.add(P.popoverOpened),this.search!==void 0&&this.search.focus()}hide(){this.nodes.popover.classList.remove(P.popoverOpened),this.nodes.popover.classList.remove(P.popoverOpenTop),this.itemsDefault.forEach(e=>e.reset()),this.search!==void 0&&this.search.clear(),this.emit(Z.Closed)}destroy(){var e;this.items.forEach(t=>t.destroy()),this.nodes.popover.remove(),this.listeners.removeAll(),(e=this.search)==null||e.destroy()}activateItemByName(e){const t=this.items.find(o=>o.name===e);this.handleItemClick(t)}buildItems(e){return e.map(t=>{switch(t.type){case _.Separator:return new Wo;case _.Html:return new Se(t,this.itemsRenderParams[_.Html]);default:return new le(t,this.itemsRenderParams[_.Default])}})}getTargetItem(e){return this.items.filter(t=>t instanceof le||t instanceof Se).find(t=>{const o=t.getElement();return o===null?!1:e.composedPath().includes(o)})}handleItemClick(e){if(!("isDisabled"in e&&e.isDisabled)){if(e.hasChildren){this.showNestedItems(e),"handleClick"in e&&typeof e.handleClick=="function"&&e.handleClick();return}this.itemsDefault.filter(t=>t!==e).forEach(t=>t.reset()),"handleClick"in e&&typeof e.handleClick=="function"&&e.handleClick(),this.toggleItemActivenessIfNeeded(e),e.closeOnActivate&&(this.hide(),this.emit(Z.ClosedOnActivate))}}handleClick(e){const t=this.getTargetItem(e);t!==void 0&&this.handleItemClick(t)}toggleItemActivenessIfNeeded(e){if(e instanceof le&&(e.toggle===!0&&e.toggleActive(),typeof e.toggle=="string")){const t=this.itemsDefault.filter(o=>o.toggle===e.toggle);if(t.length===1){e.toggleActive();return}t.forEach(o=>{o.toggleActive(o===e)})}}}var De=(n=>(n.Search="search",n))(De||{});const mt=ie("cdx-search-field"),bt={wrapper:mt(),icon:mt("icon"),input:mt("input")};class os extends we{constructor({items:e,placeholder:t}){super(),this.listeners=new Ee,this.items=e,this.wrapper=d.make("div",bt.wrapper);const o=d.make("div",bt.icon,{innerHTML:Zi});this.input=d.make("input",bt.input,{placeholder:t,tabIndex:-1}),this.wrapper.appendChild(o),this.wrapper.appendChild(this.input),this.listeners.on(this.input,"input",()=>{this.searchQuery=this.input.value,this.emit(De.Search,{query:this.searchQuery,items:this.foundItems})})}getElement(){return this.wrapper}focus(){this.input.focus()}clear(){this.input.value="",this.searchQuery="",this.emit(De.Search,{query:"",items:this.foundItems})}destroy(){this.listeners.removeAll()}get foundItems(){return this.items.filter(e=>this.checkItem(e))}checkItem(e){var i,s;const t=((i=e.title)==null?void 0:i.toLowerCase())||"",o=(s=this.searchQuery)==null?void 0:s.toLowerCase();return o!==void 0?t.includes(o):!1}}var ns=Object.defineProperty,is=Object.getOwnPropertyDescriptor,ss=(n,e,t,o)=>{for(var i=o>1?void 0:o?is(e,t):e,s=n.length-1,r;s>=0;s--)(r=n[s])&&(i=(o?r(e,t,i):r(i))||i);return o&&i&&ns(e,t,i),i};const Vo=class zn extends Xo{constructor(e,t){super(e,t),this.nestingLevel=0,this.nestedPopoverTriggerItem=null,this.previouslyHoveredItem=null,this.scopeElement=document.body,this.hide=()=>{var o;super.hide(),this.destroyNestedPopoverIfExists(),(o=this.flipper)==null||o.deactivate(),this.previouslyHoveredItem=null},this.onFlip=()=>{const o=this.itemsDefault.find(i=>i.isFocused);o==null||o.onFocus()},this.onSearch=o=>{var a;const i=o.query==="",s=o.items.length===0;this.items.forEach(l=>{let c=!1;l instanceof le?c=!o.items.includes(l):(l instanceof Wo||l instanceof Se)&&(c=s||!i),l.toggleHidden(c)}),this.toggleNothingFoundMessage(s);const r=o.query===""?this.flippableElements:o.items.map(l=>l.getElement());(a=this.flipper)!=null&&a.isActivated&&(this.flipper.deactivate(),this.flipper.activate(r))},e.nestingLevel!==void 0&&(this.nestingLevel=e.nestingLevel),this.nestingLevel>0&&this.nodes.popover.classList.add(P.popoverNested),e.scopeElement!==void 0&&(this.scopeElement=e.scopeElement),this.nodes.popoverContainer!==null&&this.listeners.on(this.nodes.popoverContainer,"mouseover",o=>this.handleHover(o)),e.searchable&&this.addSearch(),e.flippable!==!1&&(this.flipper=new ae({items:this.flippableElements,focusedItemClass:L.focused,allowedKeys:[y.TAB,y.UP,y.DOWN,y.ENTER]}),this.flipper.onFlip(this.onFlip))}hasFocus(){return this.flipper===void 0?!1:this.flipper.hasFocus()}get scrollTop(){return this.nodes.items===null?0:this.nodes.items.scrollTop}get offsetTop(){return this.nodes.popoverContainer===null?0:this.nodes.popoverContainer.offsetTop}show(){var e;this.nodes.popover.style.setProperty(me.PopoverHeight,this.size.height+"px"),this.shouldOpenBottom||this.nodes.popover.classList.add(P.popoverOpenTop),this.shouldOpenRight||this.nodes.popover.classList.add(P.popoverOpenLeft),super.show(),(e=this.flipper)==null||e.activate(this.flippableElements)}destroy(){this.hide(),super.destroy()}showNestedItems(e){this.nestedPopover!==null&&this.nestedPopover!==void 0||(this.nestedPopoverTriggerItem=e,this.showNestedPopoverForItem(e))}handleHover(e){const t=this.getTargetItem(e);t!==void 0&&this.previouslyHoveredItem!==t&&(this.destroyNestedPopoverIfExists(),this.previouslyHoveredItem=t,t.hasChildren&&this.showNestedPopoverForItem(t))}setTriggerItemPosition(e,t){const o=t.getElement(),i=(o?o.offsetTop:0)-this.scrollTop,s=this.offsetTop+i;e.style.setProperty(me.TriggerItemTop,s+"px")}destroyNestedPopoverIfExists(){var e,t;this.nestedPopover===void 0||this.nestedPopover===null||(this.nestedPopover.off(Z.ClosedOnActivate,this.hide),this.nestedPopover.hide(),this.nestedPopover.destroy(),this.nestedPopover.getElement().remove(),this.nestedPopover=null,(e=this.flipper)==null||e.activate(this.flippableElements),(t=this.nestedPopoverTriggerItem)==null||t.onChildrenClose())}showNestedPopoverForItem(e){var o;this.nestedPopover=new zn({searchable:e.isChildrenSearchable,items:e.children,nestingLevel:this.nestingLevel+1,flippable:e.isChildrenFlippable,messages:this.messages}),e.onChildrenOpen(),this.nestedPopover.on(Z.ClosedOnActivate,this.hide);const t=this.nestedPopover.getElement();return this.nodes.popover.appendChild(t),this.setTriggerItemPosition(t,e),t.style.setProperty(me.NestingLevel,this.nestedPopover.nestingLevel.toString()),this.nestedPopover.show(),(o=this.flipper)==null||o.deactivate(),this.nestedPopover}get shouldOpenBottom(){if(this.nodes.popover===void 0||this.nodes.popover===null)return!1;const e=this.nodes.popoverContainer.getBoundingClientRect(),t=this.scopeElement.getBoundingClientRect(),o=this.size.height,i=e.top+o,s=e.top-o,r=Math.min(window.innerHeight,t.bottom);return s<t.top||i<=r}get shouldOpenRight(){if(this.nodes.popover===void 0||this.nodes.popover===null)return!1;const e=this.nodes.popover.getBoundingClientRect(),t=this.scopeElement.getBoundingClientRect(),o=this.size.width,i=e.right+o,s=e.left-o,r=Math.min(window.innerWidth,t.right);return s<t.left||i<=r}get size(){var i;const e={height:0,width:0};if(this.nodes.popover===null)return e;const t=this.nodes.popover.cloneNode(!0);t.style.visibility="hidden",t.style.position="absolute",t.style.top="-1000px",t.classList.add(P.popoverOpened),(i=t.querySelector("."+P.popoverNested))==null||i.remove(),document.body.appendChild(t);const o=t.querySelector("."+P.popoverContainer);return e.height=o.offsetHeight,e.width=o.offsetWidth,t.remove(),e}get flippableElements(){return this.items.map(t=>{if(t instanceof le)return t.getElement();if(t instanceof Se)return t.getControls()}).flat().filter(t=>t!=null)}addSearch(){this.search=new os({items:this.itemsDefault,placeholder:this.messages.search}),this.search.on(De.Search,this.onSearch);const e=this.search.getElement();e.classList.add(P.search),this.nodes.popoverContainer.insertBefore(e,this.nodes.popoverContainer.firstChild)}toggleNothingFoundMessage(e){this.nodes.nothingFoundMessage.classList.toggle(P.nothingFoundMessageDisplayed,e)}};ss([fe],Vo.prototype,"size",1);let vt=Vo;class rs extends vt{constructor(e){const t=!ge();super({...e,class:P.popoverInline},{[_.Default]:{wrapperTag:"button",hint:{position:"top",alignment:"center",enabled:t}},[_.Html]:{hint:{position:"top",alignment:"center",enabled:t}}}),this.items.forEach(o=>{!(o instanceof le)&&!(o instanceof Se)||o.hasChildren&&o.isChildrenOpen&&this.showNestedItems(o)})}get offsetLeft(){return this.nodes.popoverContainer===null?0:this.nodes.popoverContainer.offsetLeft}show(){this.nestingLevel===0&&this.nodes.popover.style.setProperty(me.InlinePopoverWidth,this.size.width+"px"),super.show()}handleHover(){}setTriggerItemPosition(e,t){const o=t.getElement(),i=o?o.offsetLeft:0,s=this.offsetLeft+i;e.style.setProperty(me.TriggerItemLeft,s+"px")}showNestedItems(e){if(this.nestedPopoverTriggerItem===e){this.destroyNestedPopoverIfExists(),this.nestedPopoverTriggerItem=null;return}super.showNestedItems(e)}showNestedPopoverForItem(e){const t=super.showNestedPopoverForItem(e);return t.getElement().classList.add(P.getPopoverNestedClass(t.nestingLevel)),t}handleItemClick(e){var t;e!==this.nestedPopoverTriggerItem&&((t=this.nestedPopoverTriggerItem)==null||t.handleClick(),super.destroyNestedPopoverIfExists()),super.handleItemClick(e)}}const qo=class Me{constructor(){this.scrollPosition=null}lock(){rt?this.lockHard():document.body.classList.add(Me.CSS.scrollLocked)}unlock(){rt?this.unlockHard():document.body.classList.remove(Me.CSS.scrollLocked)}lockHard(){this.scrollPosition=window.pageYOffset,document.documentElement.style.setProperty("--window-scroll-offset",`${this.scrollPosition}px`),document.body.classList.add(Me.CSS.scrollLockedHard)}unlockHard(){document.body.classList.remove(Me.CSS.scrollLockedHard),this.scrollPosition!==null&&window.scrollTo(0,this.scrollPosition),this.scrollPosition=null}};qo.CSS={scrollLocked:"ce-scroll-locked",scrollLockedHard:"ce-scroll-locked--hard"};let as=qo;const kt=ie("ce-popover-header"),yt={root:kt(),text:kt("text"),backButton:kt("back-button")};class ls{constructor({text:e,onBackButtonClick:t}){this.listeners=new Ee,this.text=e,this.onBackButtonClick=t,this.nodes={root:d.make("div",[yt.root]),backButton:d.make("button",[yt.backButton]),text:d.make("div",[yt.text])},this.nodes.backButton.innerHTML=zi,this.nodes.root.appendChild(this.nodes.backButton),this.listeners.on(this.nodes.backButton,"click",this.onBackButtonClick),this.nodes.text.innerText=this.text,this.nodes.root.appendChild(this.nodes.text)}getElement(){return this.nodes.root}destroy(){this.nodes.root.remove(),this.listeners.destroy()}}class cs{constructor(){this.history=[]}push(e){this.history.push(e)}pop(){return this.history.pop()}get currentTitle(){return this.history.length===0?"":this.history[this.history.length-1].title}get currentItems(){return this.history.length===0?[]:this.history[this.history.length-1].items}reset(){for(;this.history.length>1;)this.pop()}}class Zo extends Xo{constructor(e){super(e,{[_.Default]:{hint:{enabled:!1}},[_.Html]:{hint:{enabled:!1}}}),this.scrollLocker=new as,this.history=new cs,this.isHidden=!0,this.nodes.overlay=d.make("div",[P.overlay,P.overlayHidden]),this.nodes.popover.insertBefore(this.nodes.overlay,this.nodes.popover.firstChild),this.listeners.on(this.nodes.overlay,"click",()=>{this.hide()}),this.history.push({items:e.items})}show(){this.nodes.overlay.classList.remove(P.overlayHidden),super.show(),this.scrollLocker.lock(),this.isHidden=!1}hide(){this.isHidden||(super.hide(),this.nodes.overlay.classList.add(P.overlayHidden),this.scrollLocker.unlock(),this.history.reset(),this.isHidden=!0)}destroy(){super.destroy(),this.scrollLocker.unlock()}showNestedItems(e){this.updateItemsAndHeader(e.children,e.title),this.history.push({title:e.title,items:e.children})}updateItemsAndHeader(e,t){if(this.header!==null&&this.header!==void 0&&(this.header.destroy(),this.header=null),t!==void 0){this.header=new ls({text:t,onBackButtonClick:()=>{this.history.pop(),this.updateItemsAndHeader(this.history.currentItems,this.history.currentTitle)}});const o=this.header.getElement();o!==null&&this.nodes.popoverContainer.insertBefore(o,this.nodes.popoverContainer.firstChild)}this.items.forEach(o=>{var i;return(i=o.getElement())==null?void 0:i.remove()}),this.items=this.buildItems(e),this.items.forEach(o=>{var s;const i=o.getElement();i!==null&&((s=this.nodes.items)==null||s.appendChild(i))})}}class ds extends E{constructor(){super(...arguments),this.opened=!1,this.selection=new b,this.popover=null,this.close=()=>{this.opened&&(this.opened=!1,b.isAtEditor||this.selection.restore(),this.selection.clearSaved(),!this.Editor.CrossBlockSelection.isCrossBlockSelectionStarted&&this.Editor.BlockManager.currentBlock&&this.Editor.BlockSelection.unselectBlock(this.Editor.BlockManager.currentBlock),this.eventsDispatcher.emit(this.events.closed),this.popover&&(this.popover.off(Z.Closed,this.onPopoverClose),this.popover.destroy(),this.popover.getElement().remove(),this.popover=null))},this.onPopoverClose=()=>{this.close()}}get events(){return{opened:"block-settings-opened",closed:"block-settings-closed"}}get CSS(){return{settings:"ce-settings"}}get flipper(){var e;if(this.popover!==null)return"flipper"in this.popover?(e=this.popover)==null?void 0:e.flipper:void 0}make(){this.nodes.wrapper=d.make("div",[this.CSS.settings]),this.nodes.wrapper.setAttribute("data-cy","block-tunes"),this.eventsDispatcher.on(xe,this.close)}destroy(){this.removeAllNodes(),this.listeners.destroy(),this.eventsDispatcher.off(xe,this.close)}async open(e=this.Editor.BlockManager.currentBlock){var s;this.opened=!0,this.selection.save(),this.Editor.BlockSelection.selectBlock(e),this.Editor.BlockSelection.clearCache();const{toolTunes:t,commonTunes:o}=e.getTunes();this.eventsDispatcher.emit(this.events.opened);const i=ge()?Zo:vt;this.popover=new i({searchable:!0,items:await this.getTunesItems(e,o,t),scopeElement:this.Editor.API.methods.ui.nodes.redactor,messages:{nothingFound:H.ui(W.ui.popover,"Nothing found"),search:H.ui(W.ui.popover,"Filter")}}),this.popover.on(Z.Closed,this.onPopoverClose),(s=this.nodes.wrapper)==null||s.append(this.popover.getElement()),this.popover.show()}getElement(){return this.nodes.wrapper}async getTunesItems(e,t,o){const i=[];o!==void 0&&o.length>0&&(i.push(...o),i.push({type:_.Separator}));const s=Array.from(this.Editor.Tools.blockTools.values()),a=(await Po(e,s)).reduce((l,c)=>(c.toolbox.forEach(u=>{l.push({icon:u.icon,title:H.t(W.toolNames,u.title),name:c.name,closeOnActivate:!0,onActivate:async()=>{const{BlockManager:h,Caret:p,Toolbar:g}=this.Editor,f=await h.convert(e,c.name,u.data);g.close(),p.setToBlock(f,p.positions.END)}})}),l),[]);return a.length>0&&(i.push({icon:Uo,name:"convert-to",title:H.ui(W.ui.popover,"Convert to"),children:{searchable:!0,items:a}}),i.push({type:_.Separator})),i.push(...t),i.map(l=>this.resolveTuneAliases(l))}resolveTuneAliases(e){if(e.type===_.Separator||e.type===_.Html)return e;const t=ji(e,{label:"title"});return e.confirmation&&(t.confirmation=this.resolveTuneAliases(e.confirmation)),t}}var Go={exports:{}};/*!
 * Library for handling keyboard shortcuts
 * @copyright CodeX (https://codex.so)
 * @license MIT
 * <AUTHOR> (https://codex.so)
 * @version 1.2.0
 */(function(n,e){(function(t,o){n.exports=o()})(window,function(){return function(t){var o={};function i(s){if(o[s])return o[s].exports;var r=o[s]={i:s,l:!1,exports:{}};return t[s].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=o,i.d=function(s,r,a){i.o(s,r)||Object.defineProperty(s,r,{enumerable:!0,get:a})},i.r=function(s){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(s,"__esModule",{value:!0})},i.t=function(s,r){if(1&r&&(s=i(s)),8&r||4&r&&typeof s=="object"&&s&&s.__esModule)return s;var a=Object.create(null);if(i.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:s}),2&r&&typeof s!="string")for(var l in s)i.d(a,l,(function(c){return s[c]}).bind(null,l));return a},i.n=function(s){var r=s&&s.__esModule?function(){return s.default}:function(){return s};return i.d(r,"a",r),r},i.o=function(s,r){return Object.prototype.hasOwnProperty.call(s,r)},i.p="",i(i.s=0)}([function(t,o,i){function s(l,c){for(var u=0;u<c.length;u++){var h=c[u];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(l,h.key,h)}}function r(l,c,u){return c&&s(l.prototype,c),u&&s(l,u),l}i.r(o);var a=function(){function l(c){var u=this;(function(h,p){if(!(h instanceof p))throw new TypeError("Cannot call a class as a function")})(this,l),this.commands={},this.keys={},this.name=c.name,this.parseShortcutName(c.name),this.element=c.on,this.callback=c.callback,this.executeShortcut=function(h){u.execute(h)},this.element.addEventListener("keydown",this.executeShortcut,!1)}return r(l,null,[{key:"supportedCommands",get:function(){return{SHIFT:["SHIFT"],CMD:["CMD","CONTROL","COMMAND","WINDOWS","CTRL"],ALT:["ALT","OPTION"]}}},{key:"keyCodes",get:function(){return{0:48,1:49,2:50,3:51,4:52,5:53,6:54,7:55,8:56,9:57,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,BACKSPACE:8,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,INSERT:45,DELETE:46,".":190}}}]),r(l,[{key:"parseShortcutName",value:function(c){c=c.split("+");for(var u=0;u<c.length;u++){c[u]=c[u].toUpperCase();var h=!1;for(var p in l.supportedCommands)if(l.supportedCommands[p].includes(c[u])){h=this.commands[p]=!0;break}h||(this.keys[c[u]]=!0)}for(var g in l.supportedCommands)this.commands[g]||(this.commands[g]=!1)}},{key:"execute",value:function(c){var u,h={CMD:c.ctrlKey||c.metaKey,SHIFT:c.shiftKey,ALT:c.altKey},p=!0;for(u in this.commands)this.commands[u]!==h[u]&&(p=!1);var g,f=!0;for(g in this.keys)f=f&&c.keyCode===l.keyCodes[g];p&&f&&this.callback(c)}},{key:"remove",value:function(){this.element.removeEventListener("keydown",this.executeShortcut)}}]),l}();o.default=a}]).default})})(Go);var us=Go.exports;const hs=ne(us);class ps{constructor(){this.registeredShortcuts=new Map}add(e){if(this.findShortcut(e.on,e.name))throw Error(`Shortcut ${e.name} is already registered for ${e.on}. Please remove it before add a new handler.`);const o=new hs({name:e.name,on:e.on,callback:e.handler}),i=this.registeredShortcuts.get(e.on)||[];this.registeredShortcuts.set(e.on,[...i,o])}remove(e,t){const o=this.findShortcut(e,t);if(!o)return;o.remove();const i=this.registeredShortcuts.get(e);this.registeredShortcuts.set(e,i.filter(s=>s!==o))}findShortcut(e,t){return(this.registeredShortcuts.get(e)||[]).find(({name:i})=>i===t)}}const be=new ps;var fs=Object.defineProperty,gs=Object.getOwnPropertyDescriptor,Jo=(n,e,t,o)=>{for(var i=o>1?void 0:o?gs(e,t):e,s=n.length-1,r;s>=0;s--)(r=n[s])&&(i=(o?r(e,t,i):r(i))||i);return o&&i&&fs(e,t,i),i},Fe=(n=>(n.Opened="toolbox-opened",n.Closed="toolbox-closed",n.BlockAdded="toolbox-block-added",n))(Fe||{});const wt=class Un extends we{constructor({api:e,tools:t,i18nLabels:o}){super(),this.opened=!1,this.listeners=new Ee,this.popover=null,this.handleMobileLayoutToggle=()=>{this.destroyPopover(),this.initPopover()},this.onPopoverClose=()=>{this.opened=!1,this.emit("toolbox-closed")},this.api=e,this.tools=t,this.i18nLabels=o,this.enableShortcuts(),this.nodes={toolbox:d.make("div",Un.CSS.toolbox)},this.initPopover(),this.nodes.toolbox.setAttribute("data-cy","toolbox"),this.api.events.on(xe,this.handleMobileLayoutToggle)}get isEmpty(){return this.toolsToBeDisplayed.length===0}static get CSS(){return{toolbox:"ce-toolbox"}}getElement(){return this.nodes.toolbox}hasFocus(){if(this.popover!==null)return"hasFocus"in this.popover?this.popover.hasFocus():void 0}destroy(){var e;super.destroy(),this.nodes&&this.nodes.toolbox&&this.nodes.toolbox.remove(),this.removeAllShortcuts(),(e=this.popover)==null||e.off(Z.Closed,this.onPopoverClose),this.listeners.destroy(),this.api.events.off(xe,this.handleMobileLayoutToggle)}toolButtonActivated(e,t){this.insertNewBlock(e,t)}open(){var e;this.isEmpty||((e=this.popover)==null||e.show(),this.opened=!0,this.emit("toolbox-opened"))}close(){var e;(e=this.popover)==null||e.hide(),this.opened=!1,this.emit("toolbox-closed")}toggle(){this.opened?this.close():this.open()}initPopover(){var t;const e=ge()?Zo:vt;this.popover=new e({scopeElement:this.api.ui.nodes.redactor,searchable:!0,messages:{nothingFound:this.i18nLabels.nothingFound,search:this.i18nLabels.filter},items:this.toolboxItemsToBeDisplayed}),this.popover.on(Z.Closed,this.onPopoverClose),(t=this.nodes.toolbox)==null||t.append(this.popover.getElement())}destroyPopover(){this.popover!==null&&(this.popover.hide(),this.popover.off(Z.Closed,this.onPopoverClose),this.popover.destroy(),this.popover=null),this.nodes.toolbox!==null&&(this.nodes.toolbox.innerHTML="")}get toolsToBeDisplayed(){const e=[];return this.tools.forEach(t=>{t.toolbox&&e.push(t)}),e}get toolboxItemsToBeDisplayed(){const e=(t,o,i=!0)=>({icon:t.icon,title:H.t(W.toolNames,t.title||Le(o.name)),name:o.name,onActivate:()=>{this.toolButtonActivated(o.name,t.data)},secondaryLabel:o.shortcut&&i?it(o.shortcut):""});return this.toolsToBeDisplayed.reduce((t,o)=>(Array.isArray(o.toolbox)?o.toolbox.forEach((i,s)=>{t.push(e(i,o,s===0))}):o.toolbox!==void 0&&t.push(e(o.toolbox,o)),t),[])}enableShortcuts(){this.toolsToBeDisplayed.forEach(e=>{const t=e.shortcut;t&&this.enableShortcutForTool(e.name,t)})}enableShortcutForTool(e,t){be.add({name:t,on:this.api.ui.nodes.redactor,handler:async o=>{o.preventDefault();const i=this.api.blocks.getCurrentBlockIndex(),s=this.api.blocks.getBlockByIndex(i);if(s)try{const r=await this.api.blocks.convert(s.id,e);this.api.caret.setToBlock(r,"end");return}catch{}this.insertNewBlock(e)}})}removeAllShortcuts(){this.toolsToBeDisplayed.forEach(e=>{const t=e.shortcut;t&&be.remove(this.api.ui.nodes.redactor,t)})}async insertNewBlock(e,t){const o=this.api.blocks.getCurrentBlockIndex(),i=this.api.blocks.getBlockByIndex(o);if(!i)return;const s=i.isEmpty?o:o+1;let r;if(t){const l=await this.api.blocks.composeBlockData(e);r=Object.assign(l,t)}const a=this.api.blocks.insert(e,r,void 0,s,void 0,i.isEmpty);a.call(te.APPEND_CALLBACK),this.api.caret.setToBlock(s),this.emit("toolbox-block-added",{block:a}),this.api.toolbar.close()}};Jo([fe],wt.prototype,"toolsToBeDisplayed",1),Jo([fe],wt.prototype,"toolboxItemsToBeDisplayed",1);let ms=wt;const Qo="block hovered";async function bs(n,e){const t=navigator.keyboard;if(!t)return e;try{return(await t.getLayoutMap()).get(n)||e}catch(o){return console.error(o),e}}class vs extends E{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t}),this.toolboxInstance=null}get CSS(){return{toolbar:"ce-toolbar",content:"ce-toolbar__content",actions:"ce-toolbar__actions",actionsOpened:"ce-toolbar__actions--opened",toolbarOpened:"ce-toolbar--opened",openedToolboxHolderModifier:"codex-editor--toolbox-opened",plusButton:"ce-toolbar__plus",plusButtonShortcut:"ce-toolbar__plus-shortcut",settingsToggler:"ce-toolbar__settings-btn",settingsTogglerHidden:"ce-toolbar__settings-btn--hidden"}}get opened(){return this.nodes.wrapper.classList.contains(this.CSS.toolbarOpened)}get toolbox(){var e;return{opened:(e=this.toolboxInstance)==null?void 0:e.opened,close:()=>{var t;(t=this.toolboxInstance)==null||t.close()},open:()=>{if(this.toolboxInstance===null){T("toolbox.open() called before initialization is finished","warn");return}this.Editor.BlockManager.currentBlock=this.hoveredBlock,this.toolboxInstance.open()},toggle:()=>{if(this.toolboxInstance===null){T("toolbox.toggle() called before initialization is finished","warn");return}this.toolboxInstance.toggle()},hasFocus:()=>{var t;return(t=this.toolboxInstance)==null?void 0:t.hasFocus()}}}get blockActions(){return{hide:()=>{this.nodes.actions.classList.remove(this.CSS.actionsOpened)},show:()=>{this.nodes.actions.classList.add(this.CSS.actionsOpened)}}}get blockTunesToggler(){return{hide:()=>this.nodes.settingsToggler.classList.add(this.CSS.settingsTogglerHidden),show:()=>this.nodes.settingsToggler.classList.remove(this.CSS.settingsTogglerHidden)}}toggleReadOnly(e){e?(this.destroy(),this.Editor.BlockSettings.destroy(),this.disableModuleBindings()):window.requestIdleCallback(()=>{this.drawUI(),this.enableModuleBindings()},{timeout:2e3})}moveAndOpen(e=this.Editor.BlockManager.currentBlock){if(this.toolboxInstance===null){T("Can't open Toolbar since Editor initialization is not finished yet","warn");return}if(this.toolboxInstance.opened&&this.toolboxInstance.close(),this.Editor.BlockSettings.opened&&this.Editor.BlockSettings.close(),!e)return;this.hoveredBlock=e;const t=e.holder,{isMobile:o}=this.Editor.UI;let i;const s=20,r=e.firstInput,a=t.getBoundingClientRect(),l=r!==void 0?r.getBoundingClientRect():null,c=l!==null?l.top-a.top:null,u=c!==null?c>s:void 0;if(o)i=t.offsetTop+t.offsetHeight;else if(r===void 0||u){const h=parseInt(window.getComputedStyle(e.pluginsContent).paddingTop);i=t.offsetTop+h}else{const h=ri(r),p=parseInt(window.getComputedStyle(this.nodes.plusButton).height,10),g=8;i=t.offsetTop+h-p+g+c}this.nodes.wrapper.style.top=`${Math.floor(i)}px`,this.Editor.BlockManager.blocks.length===1&&e.isEmpty?this.blockTunesToggler.hide():this.blockTunesToggler.show(),this.open()}close(){var e,t;this.Editor.ReadOnly.isEnabled||((e=this.nodes.wrapper)==null||e.classList.remove(this.CSS.toolbarOpened),this.blockActions.hide(),(t=this.toolboxInstance)==null||t.close(),this.Editor.BlockSettings.close(),this.reset())}reset(){this.nodes.wrapper.style.top="unset"}open(e=!0){this.nodes.wrapper.classList.add(this.CSS.toolbarOpened),e?this.blockActions.show():this.blockActions.hide()}async make(){this.nodes.wrapper=d.make("div",this.CSS.toolbar),["content","actions"].forEach(s=>{this.nodes[s]=d.make("div",this.CSS[s])}),d.append(this.nodes.wrapper,this.nodes.content),d.append(this.nodes.content,this.nodes.actions),this.nodes.plusButton=d.make("div",this.CSS.plusButton,{innerHTML:qi}),d.append(this.nodes.actions,this.nodes.plusButton),this.readOnlyMutableListeners.on(this.nodes.plusButton,"click",()=>{Ne(!0),this.plusButtonClicked()},!1);const e=d.make("div");e.appendChild(document.createTextNode(H.ui(W.ui.toolbar.toolbox,"Add"))),e.appendChild(d.make("div",this.CSS.plusButtonShortcut,{textContent:"/"})),Re(this.nodes.plusButton,e,{hidingDelay:400}),this.nodes.settingsToggler=d.make("span",this.CSS.settingsToggler,{innerHTML:Vi}),d.append(this.nodes.actions,this.nodes.settingsToggler);const t=d.make("div"),o=d.text(H.ui(W.ui.blockTunes.toggler,"Click to tune")),i=await bs("Slash","/");t.appendChild(o),t.appendChild(d.make("div",this.CSS.plusButtonShortcut,{textContent:it(`CMD + ${i}`)})),Re(this.nodes.settingsToggler,t,{hidingDelay:400}),d.append(this.nodes.actions,this.makeToolbox()),d.append(this.nodes.actions,this.Editor.BlockSettings.getElement()),d.append(this.Editor.UI.nodes.wrapper,this.nodes.wrapper)}makeToolbox(){return this.toolboxInstance=new ms({api:this.Editor.API.methods,tools:this.Editor.Tools.blockTools,i18nLabels:{filter:H.ui(W.ui.popover,"Filter"),nothingFound:H.ui(W.ui.popover,"Nothing found")}}),this.toolboxInstance.on(Fe.Opened,()=>{this.Editor.UI.nodes.wrapper.classList.add(this.CSS.openedToolboxHolderModifier)}),this.toolboxInstance.on(Fe.Closed,()=>{this.Editor.UI.nodes.wrapper.classList.remove(this.CSS.openedToolboxHolderModifier)}),this.toolboxInstance.on(Fe.BlockAdded,({block:e})=>{const{BlockManager:t,Caret:o}=this.Editor,i=t.getBlockById(e.id);i.inputs.length===0&&(i===t.lastBlock?(t.insertAtEnd(),o.setToBlock(t.lastBlock)):o.setToBlock(t.nextBlock))}),this.toolboxInstance.getElement()}plusButtonClicked(){var e;this.Editor.BlockManager.currentBlock=this.hoveredBlock,(e=this.toolboxInstance)==null||e.toggle()}enableModuleBindings(){this.readOnlyMutableListeners.on(this.nodes.settingsToggler,"mousedown",e=>{var t;e.stopPropagation(),this.settingsTogglerClicked(),(t=this.toolboxInstance)!=null&&t.opened&&this.toolboxInstance.close(),Ne(!0)},!0),ge()||this.eventsDispatcher.on(Qo,e=>{var t;this.Editor.BlockSettings.opened||(t=this.toolboxInstance)!=null&&t.opened||this.moveAndOpen(e.block)})}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}settingsTogglerClicked(){this.Editor.BlockManager.currentBlock=this.hoveredBlock,this.Editor.BlockSettings.opened?this.Editor.BlockSettings.close():this.Editor.BlockSettings.open(this.hoveredBlock)}drawUI(){this.Editor.BlockSettings.make(),this.make()}destroy(){this.removeAllNodes(),this.toolboxInstance&&this.toolboxInstance.destroy()}}var ce=(n=>(n[n.Block=0]="Block",n[n.Inline=1]="Inline",n[n.Tune=2]="Tune",n))(ce||{}),je=(n=>(n.Shortcut="shortcut",n.Toolbox="toolbox",n.EnabledInlineTools="inlineToolbar",n.EnabledBlockTunes="tunes",n.Config="config",n))(je||{}),en=(n=>(n.Shortcut="shortcut",n.SanitizeConfig="sanitize",n))(en||{}),ve=(n=>(n.IsEnabledLineBreaks="enableLineBreaks",n.Toolbox="toolbox",n.ConversionConfig="conversionConfig",n.IsReadOnlySupported="isReadOnlySupported",n.PasteConfig="pasteConfig",n))(ve||{}),He=(n=>(n.IsInline="isInline",n.Title="title",n.IsReadOnlySupported="isReadOnlySupported",n))(He||{}),Et=(n=>(n.IsTune="isTune",n))(Et||{});class xt{constructor({name:e,constructable:t,config:o,api:i,isDefault:s,isInternal:r=!1,defaultPlaceholder:a}){this.api=i,this.name=e,this.constructable=t,this.config=o,this.isDefault=s,this.isInternal=r,this.defaultPlaceholder=a}get settings(){const e=this.config.config||{};return this.isDefault&&!("placeholder"in e)&&this.defaultPlaceholder&&(e.placeholder=this.defaultPlaceholder),e}reset(){if(M(this.constructable.reset))return this.constructable.reset()}prepare(){if(M(this.constructable.prepare))return this.constructable.prepare({toolName:this.name,config:this.settings})}get shortcut(){const e=this.constructable.shortcut;return this.config.shortcut||e}get sanitizeConfig(){return this.constructable.sanitize||{}}isInline(){return this.type===ce.Inline}isBlock(){return this.type===ce.Block}isTune(){return this.type===ce.Tune}}class ks extends E{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t}),this.CSS={inlineToolbar:"ce-inline-toolbar"},this.opened=!1,this.popover=null,this.toolbarVerticalMargin=ge()?20:6,this.tools=new Map,window.requestIdleCallback(()=>{this.make()},{timeout:2e3})}async tryToShow(e=!1){e&&this.close(),this.allowedToShow()&&(await this.open(),this.Editor.Toolbar.close())}close(){var e,t;if(this.opened){for(const[o,i]of this.tools){const s=this.getToolShortcut(o.name);s!==void 0&&be.remove(this.Editor.UI.nodes.redactor,s),M(i.clear)&&i.clear()}this.tools=new Map,this.reset(),this.opened=!1,(e=this.popover)==null||e.hide(),(t=this.popover)==null||t.destroy(),this.popover=null}}containsNode(e){return this.nodes.wrapper===void 0?!1:this.nodes.wrapper.contains(e)}destroy(){var e;this.removeAllNodes(),(e=this.popover)==null||e.destroy(),this.popover=null}make(){this.nodes.wrapper=d.make("div",[this.CSS.inlineToolbar,...this.isRtl?[this.Editor.UI.CSS.editorRtlFix]:[]]),this.nodes.wrapper.setAttribute("data-cy","inline-toolbar"),d.append(this.Editor.UI.nodes.wrapper,this.nodes.wrapper)}async open(){var t;if(this.opened)return;this.opened=!0,this.popover!==null&&this.popover.destroy(),this.createToolsInstances();const e=await this.getPopoverItems();this.popover=new rs({items:e,scopeElement:this.Editor.API.methods.ui.nodes.redactor,messages:{nothingFound:H.ui(W.ui.popover,"Nothing found"),search:H.ui(W.ui.popover,"Filter")}}),this.move(this.popover.size.width),(t=this.nodes.wrapper)==null||t.append(this.popover.getElement()),this.popover.show()}move(e){const t=b.rect,o=this.Editor.UI.nodes.wrapper.getBoundingClientRect(),i={x:t.x-o.x,y:t.y+t.height-o.top+this.toolbarVerticalMargin};i.x+e+o.x>this.Editor.UI.contentRect.right&&(i.x=this.Editor.UI.contentRect.right-e-o.x),this.nodes.wrapper.style.left=Math.floor(i.x)+"px",this.nodes.wrapper.style.top=Math.floor(i.y)+"px"}reset(){this.nodes.wrapper.style.left="0",this.nodes.wrapper.style.top="0"}allowedToShow(){const e=["IMG","INPUT"],t=b.get(),o=b.text;if(!t||!t.anchorNode||t.isCollapsed||o.length<1)return!1;const i=d.isElement(t.anchorNode)?t.anchorNode:t.anchorNode.parentElement;if(i===null||t!==null&&e.includes(i.tagName))return!1;const s=this.Editor.BlockManager.getBlock(t.anchorNode);return!s||this.getTools().some(c=>s.tool.inlineTools.has(c.name))===!1?!1:i.closest("[contenteditable]")!==null}getTools(){const e=this.Editor.BlockManager.currentBlock;return e?Array.from(e.tool.inlineTools.values()).filter(o=>!(this.Editor.ReadOnly.isEnabled&&o.isReadOnlySupported!==!0)):[]}createToolsInstances(){this.tools=new Map,this.getTools().forEach(t=>{const o=t.create();this.tools.set(t,o)})}async getPopoverItems(){const e=[];let t=0;for(const[o,i]of this.tools){const s=await i.render(),r=this.getToolShortcut(o.name);if(r!==void 0)try{this.enableShortcuts(o.name,r)}catch{}const a=r!==void 0?it(r):void 0,l=H.t(W.toolNames,o.title||Le(o.name));[s].flat().forEach(c=>{var h,p;const u={name:o.name,onActivate:()=>{this.toolClicked(i)},hint:{title:l,description:a}};if(d.isElement(c)){const g={...u,element:c,type:_.Html};if(M(i.renderActions)){const f=i.renderActions();g.children={isOpen:(h=i.checkState)==null?void 0:h.call(i,b.get()),isFlippable:!1,items:[{type:_.Html,element:f}]}}else(p=i.checkState)==null||p.call(i,b.get());e.push(g)}else if(c.type===_.Html)e.push({...u,...c,type:_.Html});else if(c.type===_.Separator)e.push({type:_.Separator});else{const g={...u,...c,type:_.Default};"children"in g&&t!==0&&e.push({type:_.Separator}),e.push(g),"children"in g&&t<this.tools.size-1&&e.push({type:_.Separator})}}),t++}return e}getToolShortcut(e){const{Tools:t}=this.Editor,o=t.inlineTools.get(e),i=t.internal.inlineTools;return Array.from(i.keys()).includes(e)?this.inlineTools[e][en.Shortcut]:o==null?void 0:o.shortcut}enableShortcuts(e,t){be.add({name:t,handler:o=>{var s;const{currentBlock:i}=this.Editor.BlockManager;i&&i.tool.enabledInlineTools&&(o.preventDefault(),(s=this.popover)==null||s.activateItemByName(e))},on:document})}toolClicked(e){var o;const t=b.range;(o=e.surround)==null||o.call(e,t),this.checkToolsState()}checkToolsState(){var e;(e=this.tools)==null||e.forEach(t=>{var o;(o=t.checkState)==null||o.call(t,b.get())})}get inlineTools(){const e={};return Array.from(this.Editor.Tools.inlineTools.entries()).forEach(([t,o])=>{e[t]=o.create()}),e}}function tn(){const n=window.getSelection();if(n===null)return[null,0];let e=n.focusNode,t=n.focusOffset;return e===null?[null,0]:(e.nodeType!==Node.TEXT_NODE&&e.childNodes.length>0&&(e.childNodes[t]?(e=e.childNodes[t],t=0):(e=e.childNodes[t-1],t=e.textContent.length)),[e,t])}function on(n,e,t,o){const i=document.createRange();o==="left"?(i.setStart(n,0),i.setEnd(e,t)):(i.setStart(e,t),i.setEnd(n,n.childNodes.length));const s=i.cloneContents(),r=document.createElement("div");r.appendChild(s);const a=r.textContent||"";return si(a)}function $e(n){const e=d.getDeepestNode(n);if(e===null||d.isEmpty(n))return!0;if(d.isNativeInput(e))return e.selectionEnd===0;if(d.isEmpty(n))return!0;const[t,o]=tn();return t===null?!1:on(n,t,o,"left")}function ze(n){const e=d.getDeepestNode(n,!0);if(e===null)return!0;if(d.isNativeInput(e))return e.selectionEnd===e.value.length;const[t,o]=tn();return t===null?!1:on(n,t,o,"right")}var nn={},Bt={},Ue={},de={},Ct={},Tt={};Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.allInputsSelector=ys;function ys(){var n=["text","password","email","number","search","tel","url"];return"[contenteditable=true], textarea, input:not([type]), "+n.map(function(e){return'input[type="'.concat(e,'"]')}).join(", ")}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.allInputsSelector=void 0;var e=Tt;Object.defineProperty(n,"allInputsSelector",{enumerable:!0,get:function(){return e.allInputsSelector}})})(Ct);var ue={},St={};Object.defineProperty(St,"__esModule",{value:!0}),St.isNativeInput=ws;function ws(n){var e=["INPUT","TEXTAREA"];return n&&n.tagName?e.includes(n.tagName):!1}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isNativeInput=void 0;var e=St;Object.defineProperty(n,"isNativeInput",{enumerable:!0,get:function(){return e.isNativeInput}})})(ue);var sn={},It={};Object.defineProperty(It,"__esModule",{value:!0}),It.append=Es;function Es(n,e){Array.isArray(e)?e.forEach(function(t){n.appendChild(t)}):n.appendChild(e)}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.append=void 0;var e=It;Object.defineProperty(n,"append",{enumerable:!0,get:function(){return e.append}})})(sn);var Ot={},_t={};Object.defineProperty(_t,"__esModule",{value:!0}),_t.blockElements=xs;function xs(){return["address","article","aside","blockquote","canvas","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","ruby","section","table","tbody","thead","tr","tfoot","ul","video"]}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.blockElements=void 0;var e=_t;Object.defineProperty(n,"blockElements",{enumerable:!0,get:function(){return e.blockElements}})})(Ot);var rn={},Mt={};Object.defineProperty(Mt,"__esModule",{value:!0}),Mt.calculateBaseline=Bs;function Bs(n){var e=window.getComputedStyle(n),t=parseFloat(e.fontSize),o=parseFloat(e.lineHeight)||t*1.2,i=parseFloat(e.paddingTop),s=parseFloat(e.borderTopWidth),r=parseFloat(e.marginTop),a=t*.8,l=(o-t)/2,c=r+s+i+l+a;return c}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.calculateBaseline=void 0;var e=Mt;Object.defineProperty(n,"calculateBaseline",{enumerable:!0,get:function(){return e.calculateBaseline}})})(rn);var an={},At={},Lt={},Pt={};Object.defineProperty(Pt,"__esModule",{value:!0}),Pt.isContentEditable=Cs;function Cs(n){return n.contentEditable==="true"}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isContentEditable=void 0;var e=Pt;Object.defineProperty(n,"isContentEditable",{enumerable:!0,get:function(){return e.isContentEditable}})})(Lt),Object.defineProperty(At,"__esModule",{value:!0}),At.canSetCaret=Is;var Ts=ue,Ss=Lt;function Is(n){var e=!0;if((0,Ts.isNativeInput)(n))switch(n.type){case"file":case"checkbox":case"radio":case"hidden":case"submit":case"button":case"image":case"reset":e=!1;break}else e=(0,Ss.isContentEditable)(n);return e}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.canSetCaret=void 0;var e=At;Object.defineProperty(n,"canSetCaret",{enumerable:!0,get:function(){return e.canSetCaret}})})(an);var We={},Nt={};function Os(n,e,t){const o=t.value!==void 0?"value":"get",i=t[o],s=`#${e}Cache`;if(t[o]=function(...r){return this[s]===void 0&&(this[s]=i.apply(this,r)),this[s]},o==="get"&&t.set){const r=t.set;t.set=function(a){delete n[s],r.apply(this,a)}}return t}function ln(){const n={win:!1,mac:!1,x11:!1,linux:!1},e=Object.keys(n).find(t=>window.navigator.appVersion.toLowerCase().indexOf(t)!==-1);return e!==void 0&&(n[e]=!0),n}function Rt(n){return n!=null&&n!==""&&(typeof n!="object"||Object.keys(n).length>0)}function _s(n){return!Rt(n)}const Ms=()=>typeof window<"u"&&window.navigator!==null&&Rt(window.navigator.platform)&&(/iP(ad|hone|od)/.test(window.navigator.platform)||window.navigator.platform==="MacIntel"&&window.navigator.maxTouchPoints>1);function As(n){const e=ln();return n=n.replace(/shift/gi,"⇧").replace(/backspace/gi,"⌫").replace(/enter/gi,"⏎").replace(/up/gi,"↑").replace(/left/gi,"→").replace(/down/gi,"↓").replace(/right/gi,"←").replace(/escape/gi,"⎋").replace(/insert/gi,"Ins").replace(/delete/gi,"␡").replace(/\+/gi,"+"),e.mac?n=n.replace(/ctrl|cmd/gi,"⌘").replace(/alt/gi,"⌥"):n=n.replace(/cmd/gi,"Ctrl").replace(/windows/gi,"WIN"),n}function Ls(n){return n[0].toUpperCase()+n.slice(1)}function Ps(n){const e=document.createElement("div");e.style.position="absolute",e.style.left="-999px",e.style.bottom="-999px",e.innerHTML=n,document.body.appendChild(e);const t=window.getSelection(),o=document.createRange();if(o.selectNode(e),t===null)throw new Error("Cannot copy text to clipboard");t.removeAllRanges(),t.addRange(o),document.execCommand("copy"),document.body.removeChild(e)}function Ns(n,e,t){let o;return(...i)=>{const s=this,r=()=>{o=void 0,t!==!0&&n.apply(s,i)},a=t===!0&&o!==void 0;window.clearTimeout(o),o=window.setTimeout(r,e),a&&n.apply(s,i)}}function se(n){return Object.prototype.toString.call(n).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function Rs(n){return se(n)==="boolean"}function cn(n){return se(n)==="function"||se(n)==="asyncfunction"}function Ds(n){return cn(n)&&/^\s*class\s+/.test(n.toString())}function Fs(n){return se(n)==="number"}function Ye(n){return se(n)==="object"}function js(n){return Promise.resolve(n)===n}function Hs(n){return se(n)==="string"}function $s(n){return se(n)==="undefined"}function Dt(n,...e){if(!e.length)return n;const t=e.shift();if(Ye(n)&&Ye(t))for(const o in t)Ye(t[o])?(n[o]===void 0&&Object.assign(n,{[o]:{}}),Dt(n[o],t[o])):Object.assign(n,{[o]:t[o]});return Dt(n,...e)}function zs(n,e,t){const o=`«${e}» is deprecated and will be removed in the next major release. Please use the «${t}» instead.`;n&&console.warn(o)}function Us(n){try{return new URL(n).href}catch{}return n.substring(0,2)==="//"?window.location.protocol+n:window.location.origin+n}function Ws(n){return n>47&&n<58||n===32||n===13||n===229||n>64&&n<91||n>95&&n<112||n>185&&n<193||n>218&&n<223}const Ys={BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,LEFT:37,UP:38,DOWN:40,RIGHT:39,DELETE:46,META:91,SLASH:191},Ks={LEFT:0,WHEEL:1,RIGHT:2,BACKWARD:3,FORWARD:4};let Xs=class{constructor(){this.completed=Promise.resolve()}add(e){return new Promise((t,o)=>{this.completed=this.completed.then(e).then(t).catch(o)})}};function Vs(n,e,t=void 0){let o,i,s,r=null,a=0;t||(t={});const l=function(){a=t.leading===!1?0:Date.now(),r=null,s=n.apply(o,i),r===null&&(o=i=null)};return function(){const c=Date.now();!a&&t.leading===!1&&(a=c);const u=e-(c-a);return o=this,i=arguments,u<=0||u>e?(r&&(clearTimeout(r),r=null),a=c,s=n.apply(o,i),r===null&&(o=i=null)):!r&&t.trailing!==!1&&(r=setTimeout(l,u)),s}}const Ft=Yn(Object.freeze(Object.defineProperty({__proto__:null,PromiseQueue:Xs,beautifyShortcut:As,cacheable:Os,capitalize:Ls,copyTextToClipboard:Ps,debounce:Ns,deepMerge:Dt,deprecationAssert:zs,getUserOS:ln,getValidUrl:Us,isBoolean:Rs,isClass:Ds,isEmpty:_s,isFunction:cn,isIosDevice:Ms,isNumber:Fs,isObject:Ye,isPrintableKey:Ws,isPromise:js,isString:Hs,isUndefined:$s,keyCodes:Ys,mouseButtons:Ks,notEmpty:Rt,throttle:Vs,typeOf:se},Symbol.toStringTag,{value:"Module"})));Object.defineProperty(Nt,"__esModule",{value:!0}),Nt.containsOnlyInlineElements=Gs;var qs=Ft,Zs=Ot;function Gs(n){var e;(0,qs.isString)(n)?(e=document.createElement("div"),e.innerHTML=n):e=n;var t=function(o){return!(0,Zs.blockElements)().includes(o.tagName.toLowerCase())&&Array.from(o.children).every(t)};return Array.from(e.children).every(t)}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.containsOnlyInlineElements=void 0;var e=Nt;Object.defineProperty(n,"containsOnlyInlineElements",{enumerable:!0,get:function(){return e.containsOnlyInlineElements}})})(We);var dn={},jt={},Ke={},Ht={};Object.defineProperty(Ht,"__esModule",{value:!0}),Ht.make=Js;function Js(n,e,t){var o;e===void 0&&(e=null),t===void 0&&(t={});var i=document.createElement(n);if(Array.isArray(e)){var s=e.filter(function(a){return a!==void 0});(o=i.classList).add.apply(o,s)}else e!==null&&i.classList.add(e);for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(i[r]=t[r]);return i}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.make=void 0;var e=Ht;Object.defineProperty(n,"make",{enumerable:!0,get:function(){return e.make}})})(Ke),Object.defineProperty(jt,"__esModule",{value:!0}),jt.fragmentToString=er;var Qs=Ke;function er(n){var e=(0,Qs.make)("div");return e.appendChild(n),e.innerHTML}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.fragmentToString=void 0;var e=jt;Object.defineProperty(n,"fragmentToString",{enumerable:!0,get:function(){return e.fragmentToString}})})(dn);var un={},$t={};Object.defineProperty($t,"__esModule",{value:!0}),$t.getContentLength=or;var tr=ue;function or(n){var e,t;return(0,tr.isNativeInput)(n)?n.value.length:n.nodeType===Node.TEXT_NODE?n.length:(t=(e=n.textContent)===null||e===void 0?void 0:e.length)!==null&&t!==void 0?t:0}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.getContentLength=void 0;var e=$t;Object.defineProperty(n,"getContentLength",{enumerable:!0,get:function(){return e.getContentLength}})})(un);var zt={},Ut={},hn=J&&J.__spreadArray||function(n,e,t){if(t||arguments.length===2)for(var o=0,i=e.length,s;o<i;o++)(s||!(o in e))&&(s||(s=Array.prototype.slice.call(e,0,o)),s[o]=e[o]);return n.concat(s||Array.prototype.slice.call(e))};Object.defineProperty(Ut,"__esModule",{value:!0}),Ut.getDeepestBlockElements=pn;var nr=We;function pn(n){return(0,nr.containsOnlyInlineElements)(n)?[n]:Array.from(n.children).reduce(function(e,t){return hn(hn([],e,!0),pn(t),!0)},[])}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.getDeepestBlockElements=void 0;var e=Ut;Object.defineProperty(n,"getDeepestBlockElements",{enumerable:!0,get:function(){return e.getDeepestBlockElements}})})(zt);var fn={},Wt={},Xe={},Yt={};Object.defineProperty(Yt,"__esModule",{value:!0}),Yt.isLineBreakTag=ir;function ir(n){return["BR","WBR"].includes(n.tagName)}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isLineBreakTag=void 0;var e=Yt;Object.defineProperty(n,"isLineBreakTag",{enumerable:!0,get:function(){return e.isLineBreakTag}})})(Xe);var Ve={},Kt={};Object.defineProperty(Kt,"__esModule",{value:!0}),Kt.isSingleTag=sr;function sr(n){return["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"].includes(n.tagName)}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isSingleTag=void 0;var e=Kt;Object.defineProperty(n,"isSingleTag",{enumerable:!0,get:function(){return e.isSingleTag}})})(Ve),Object.defineProperty(Wt,"__esModule",{value:!0}),Wt.getDeepestNode=gn;var rr=ue,ar=Xe,lr=Ve;function gn(n,e){e===void 0&&(e=!1);var t=e?"lastChild":"firstChild",o=e?"previousSibling":"nextSibling";if(n.nodeType===Node.ELEMENT_NODE&&n[t]){var i=n[t];if((0,lr.isSingleTag)(i)&&!(0,rr.isNativeInput)(i)&&!(0,ar.isLineBreakTag)(i))if(i[o])i=i[o];else if(i.parentNode!==null&&i.parentNode[o])i=i.parentNode[o];else return i.parentNode;return gn(i,e)}return n}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.getDeepestNode=void 0;var e=Wt;Object.defineProperty(n,"getDeepestNode",{enumerable:!0,get:function(){return e.getDeepestNode}})})(fn);var mn={},Xt={},qe=J&&J.__spreadArray||function(n,e,t){if(t||arguments.length===2)for(var o=0,i=e.length,s;o<i;o++)(s||!(o in e))&&(s||(s=Array.prototype.slice.call(e,0,o)),s[o]=e[o]);return n.concat(s||Array.prototype.slice.call(e))};Object.defineProperty(Xt,"__esModule",{value:!0}),Xt.findAllInputs=pr;var cr=We,dr=zt,ur=Ct,hr=ue;function pr(n){return Array.from(n.querySelectorAll((0,ur.allInputsSelector)())).reduce(function(e,t){return(0,hr.isNativeInput)(t)||(0,cr.containsOnlyInlineElements)(t)?qe(qe([],e,!0),[t],!1):qe(qe([],e,!0),(0,dr.getDeepestBlockElements)(t),!0)},[])}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.findAllInputs=void 0;var e=Xt;Object.defineProperty(n,"findAllInputs",{enumerable:!0,get:function(){return e.findAllInputs}})})(mn);var bn={},Vt={};Object.defineProperty(Vt,"__esModule",{value:!0}),Vt.isCollapsedWhitespaces=fr;function fr(n){return!/[^\t\n\r ]/.test(n)}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isCollapsedWhitespaces=void 0;var e=Vt;Object.defineProperty(n,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return e.isCollapsedWhitespaces}})})(bn);var qt={},Zt={};Object.defineProperty(Zt,"__esModule",{value:!0}),Zt.isElement=mr;var gr=Ft;function mr(n){return(0,gr.isNumber)(n)?!1:!!n&&!!n.nodeType&&n.nodeType===Node.ELEMENT_NODE}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isElement=void 0;var e=Zt;Object.defineProperty(n,"isElement",{enumerable:!0,get:function(){return e.isElement}})})(qt);var vn={},Gt={},Jt={},Qt={};Object.defineProperty(Qt,"__esModule",{value:!0}),Qt.isLeaf=br;function br(n){return n===null?!1:n.childNodes.length===0}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isLeaf=void 0;var e=Qt;Object.defineProperty(n,"isLeaf",{enumerable:!0,get:function(){return e.isLeaf}})})(Jt);var eo={},to={};Object.defineProperty(to,"__esModule",{value:!0}),to.isNodeEmpty=Er;var vr=Xe,kr=qt,yr=ue,wr=Ve;function Er(n,e){var t="";return(0,wr.isSingleTag)(n)&&!(0,vr.isLineBreakTag)(n)?!1:((0,kr.isElement)(n)&&(0,yr.isNativeInput)(n)?t=n.value:n.textContent!==null&&(t=n.textContent.replace("​","")),e!==void 0&&(t=t.replace(new RegExp(e,"g"),"")),t.trim().length===0)}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isNodeEmpty=void 0;var e=to;Object.defineProperty(n,"isNodeEmpty",{enumerable:!0,get:function(){return e.isNodeEmpty}})})(eo),Object.defineProperty(Gt,"__esModule",{value:!0}),Gt.isEmpty=Cr;var xr=Jt,Br=eo;function Cr(n,e){n.normalize();for(var t=[n];t.length>0;){var o=t.shift();if(o){if(n=o,(0,xr.isLeaf)(n)&&!(0,Br.isNodeEmpty)(n,e))return!1;t.push.apply(t,Array.from(n.childNodes))}}return!0}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isEmpty=void 0;var e=Gt;Object.defineProperty(n,"isEmpty",{enumerable:!0,get:function(){return e.isEmpty}})})(vn);var kn={},oo={};Object.defineProperty(oo,"__esModule",{value:!0}),oo.isFragment=Sr;var Tr=Ft;function Sr(n){return(0,Tr.isNumber)(n)?!1:!!n&&!!n.nodeType&&n.nodeType===Node.DOCUMENT_FRAGMENT_NODE}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isFragment=void 0;var e=oo;Object.defineProperty(n,"isFragment",{enumerable:!0,get:function(){return e.isFragment}})})(kn);var yn={},no={};Object.defineProperty(no,"__esModule",{value:!0}),no.isHTMLString=Or;var Ir=Ke;function Or(n){var e=(0,Ir.make)("div");return e.innerHTML=n,e.childElementCount>0}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isHTMLString=void 0;var e=no;Object.defineProperty(n,"isHTMLString",{enumerable:!0,get:function(){return e.isHTMLString}})})(yn);var wn={},io={};Object.defineProperty(io,"__esModule",{value:!0}),io.offset=_r;function _r(n){var e=n.getBoundingClientRect(),t=window.pageXOffset||document.documentElement.scrollLeft,o=window.pageYOffset||document.documentElement.scrollTop,i=e.top+o,s=e.left+t;return{top:i,left:s,bottom:i+e.height,right:s+e.width}}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.offset=void 0;var e=io;Object.defineProperty(n,"offset",{enumerable:!0,get:function(){return e.offset}})})(wn);var En={},so={};Object.defineProperty(so,"__esModule",{value:!0}),so.prepend=Mr;function Mr(n,e){Array.isArray(e)?(e=e.reverse(),e.forEach(function(t){return n.prepend(t)})):n.prepend(e)}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.prepend=void 0;var e=so;Object.defineProperty(n,"prepend",{enumerable:!0,get:function(){return e.prepend}})})(En),function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.prepend=n.offset=n.make=n.isLineBreakTag=n.isSingleTag=n.isNodeEmpty=n.isLeaf=n.isHTMLString=n.isFragment=n.isEmpty=n.isElement=n.isContentEditable=n.isCollapsedWhitespaces=n.findAllInputs=n.isNativeInput=n.allInputsSelector=n.getDeepestNode=n.getDeepestBlockElements=n.getContentLength=n.fragmentToString=n.containsOnlyInlineElements=n.canSetCaret=n.calculateBaseline=n.blockElements=n.append=void 0;var e=Ct;Object.defineProperty(n,"allInputsSelector",{enumerable:!0,get:function(){return e.allInputsSelector}});var t=ue;Object.defineProperty(n,"isNativeInput",{enumerable:!0,get:function(){return t.isNativeInput}});var o=sn;Object.defineProperty(n,"append",{enumerable:!0,get:function(){return o.append}});var i=Ot;Object.defineProperty(n,"blockElements",{enumerable:!0,get:function(){return i.blockElements}});var s=rn;Object.defineProperty(n,"calculateBaseline",{enumerable:!0,get:function(){return s.calculateBaseline}});var r=an;Object.defineProperty(n,"canSetCaret",{enumerable:!0,get:function(){return r.canSetCaret}});var a=We;Object.defineProperty(n,"containsOnlyInlineElements",{enumerable:!0,get:function(){return a.containsOnlyInlineElements}});var l=dn;Object.defineProperty(n,"fragmentToString",{enumerable:!0,get:function(){return l.fragmentToString}});var c=un;Object.defineProperty(n,"getContentLength",{enumerable:!0,get:function(){return c.getContentLength}});var u=zt;Object.defineProperty(n,"getDeepestBlockElements",{enumerable:!0,get:function(){return u.getDeepestBlockElements}});var h=fn;Object.defineProperty(n,"getDeepestNode",{enumerable:!0,get:function(){return h.getDeepestNode}});var p=mn;Object.defineProperty(n,"findAllInputs",{enumerable:!0,get:function(){return p.findAllInputs}});var g=bn;Object.defineProperty(n,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return g.isCollapsedWhitespaces}});var f=Lt;Object.defineProperty(n,"isContentEditable",{enumerable:!0,get:function(){return f.isContentEditable}});var v=qt;Object.defineProperty(n,"isElement",{enumerable:!0,get:function(){return v.isElement}});var O=vn;Object.defineProperty(n,"isEmpty",{enumerable:!0,get:function(){return O.isEmpty}});var S=kn;Object.defineProperty(n,"isFragment",{enumerable:!0,get:function(){return S.isFragment}});var A=yn;Object.defineProperty(n,"isHTMLString",{enumerable:!0,get:function(){return A.isHTMLString}});var G=Jt;Object.defineProperty(n,"isLeaf",{enumerable:!0,get:function(){return G.isLeaf}});var j=eo;Object.defineProperty(n,"isNodeEmpty",{enumerable:!0,get:function(){return j.isNodeEmpty}});var U=Xe;Object.defineProperty(n,"isLineBreakTag",{enumerable:!0,get:function(){return U.isLineBreakTag}});var oe=Ve;Object.defineProperty(n,"isSingleTag",{enumerable:!0,get:function(){return oe.isSingleTag}});var he=Ke;Object.defineProperty(n,"make",{enumerable:!0,get:function(){return he.make}});var k=wn;Object.defineProperty(n,"offset",{enumerable:!0,get:function(){return k.offset}});var m=En;Object.defineProperty(n,"prepend",{enumerable:!0,get:function(){return m.prepend}})}(de);var Ze={};Object.defineProperty(Ze,"__esModule",{value:!0}),Ze.getContenteditableSlice=Lr;var Ar=de;function Lr(n,e,t,o,i){var s;i===void 0&&(i=!1);var r=document.createRange();if(o==="left"?(r.setStart(n,0),r.setEnd(e,t)):(r.setStart(e,t),r.setEnd(n,n.childNodes.length)),i===!0){var a=r.extractContents();return(0,Ar.fragmentToString)(a)}var l=r.cloneContents(),c=document.createElement("div");c.appendChild(l);var u=(s=c.textContent)!==null&&s!==void 0?s:"";return u}Object.defineProperty(Ue,"__esModule",{value:!0}),Ue.checkContenteditableSliceForEmptiness=Rr;var Pr=de,Nr=Ze;function Rr(n,e,t,o){var i=(0,Nr.getContenteditableSlice)(n,e,t,o);return(0,Pr.isCollapsedWhitespaces)(i)}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.checkContenteditableSliceForEmptiness=void 0;var e=Ue;Object.defineProperty(n,"checkContenteditableSliceForEmptiness",{enumerable:!0,get:function(){return e.checkContenteditableSliceForEmptiness}})})(Bt);var xn={};(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.getContenteditableSlice=void 0;var e=Ze;Object.defineProperty(n,"getContenteditableSlice",{enumerable:!0,get:function(){return e.getContenteditableSlice}})})(xn);var Bn={},ro={};Object.defineProperty(ro,"__esModule",{value:!0}),ro.focus=Fr;var Dr=de;function Fr(n,e){var t,o;if(e===void 0&&(e=!0),(0,Dr.isNativeInput)(n)){n.focus();var i=e?0:n.value.length;n.setSelectionRange(i,i)}else{var s=document.createRange(),r=window.getSelection();if(!r)return;var a=function(p){var g=document.createTextNode("");p.appendChild(g),s.setStart(g,0),s.setEnd(g,0)},l=function(p){return p!=null},c=n.childNodes,u=e?c[0]:c[c.length-1];if(l(u)){for(;l(u)&&u.nodeType!==Node.TEXT_NODE;)u=e?u.firstChild:u.lastChild;if(l(u)&&u.nodeType===Node.TEXT_NODE){var h=(o=(t=u.textContent)===null||t===void 0?void 0:t.length)!==null&&o!==void 0?o:0,i=e?0:h;s.setStart(u,i),s.setEnd(u,i)}else a(n)}else a(n);r.removeAllRanges(),r.addRange(s)}}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.focus=void 0;var e=ro;Object.defineProperty(n,"focus",{enumerable:!0,get:function(){return e.focus}})})(Bn);var ao={},Ge={};Object.defineProperty(Ge,"__esModule",{value:!0}),Ge.getCaretNodeAndOffset=jr;function jr(){var n=window.getSelection();if(n===null)return[null,0];var e=n.focusNode,t=n.focusOffset;return e===null?[null,0]:(e.nodeType!==Node.TEXT_NODE&&e.childNodes.length>0&&(e.childNodes[t]!==void 0?(e=e.childNodes[t],t=0):(e=e.childNodes[t-1],e.textContent!==null&&(t=e.textContent.length))),[e,t])}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.getCaretNodeAndOffset=void 0;var e=Ge;Object.defineProperty(n,"getCaretNodeAndOffset",{enumerable:!0,get:function(){return e.getCaretNodeAndOffset}})})(ao);var Cn={},Je={};Object.defineProperty(Je,"__esModule",{value:!0}),Je.getRange=Hr;function Hr(){var n=window.getSelection();return n&&n.rangeCount?n.getRangeAt(0):null}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.getRange=void 0;var e=Je;Object.defineProperty(n,"getRange",{enumerable:!0,get:function(){return e.getRange}})})(Cn);var Tn={},lo={};Object.defineProperty(lo,"__esModule",{value:!0}),lo.isCaretAtEndOfInput=Ur;var Sn=de,$r=ao,zr=Bt;function Ur(n){var e=(0,Sn.getDeepestNode)(n,!0);if(e===null)return!0;if((0,Sn.isNativeInput)(e))return e.selectionEnd===e.value.length;var t=(0,$r.getCaretNodeAndOffset)(),o=t[0],i=t[1];return o===null?!1:(0,zr.checkContenteditableSliceForEmptiness)(n,o,i,"right")}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isCaretAtEndOfInput=void 0;var e=lo;Object.defineProperty(n,"isCaretAtEndOfInput",{enumerable:!0,get:function(){return e.isCaretAtEndOfInput}})})(Tn);var In={},co={};Object.defineProperty(co,"__esModule",{value:!0}),co.isCaretAtStartOfInput=Kr;var Qe=de,Wr=Ge,Yr=Ue;function Kr(n){var e=(0,Qe.getDeepestNode)(n);if(e===null||(0,Qe.isEmpty)(n))return!0;if((0,Qe.isNativeInput)(e))return e.selectionEnd===0;if((0,Qe.isEmpty)(n))return!0;var t=(0,Wr.getCaretNodeAndOffset)(),o=t[0],i=t[1];return o===null?!1:(0,Yr.checkContenteditableSliceForEmptiness)(n,o,i,"left")}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.isCaretAtStartOfInput=void 0;var e=co;Object.defineProperty(n,"isCaretAtStartOfInput",{enumerable:!0,get:function(){return e.isCaretAtStartOfInput}})})(In);var On={},uo={};Object.defineProperty(uo,"__esModule",{value:!0}),uo.save=qr;var Xr=de,Vr=Je;function qr(){var n=(0,Vr.getRange)(),e=(0,Xr.make)("span");if(e.id="cursor",e.hidden=!0,!!n)return n.insertNode(e),function(){var o=window.getSelection();o&&(n.setStartAfter(e),n.setEndAfter(e),o.removeAllRanges(),o.addRange(n),setTimeout(function(){e.remove()},150))}}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.save=void 0;var e=uo;Object.defineProperty(n,"save",{enumerable:!0,get:function(){return e.save}})})(On),function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.save=n.isCaretAtStartOfInput=n.isCaretAtEndOfInput=n.getRange=n.getCaretNodeAndOffset=n.focus=n.getContenteditableSlice=n.checkContenteditableSliceForEmptiness=void 0;var e=Bt;Object.defineProperty(n,"checkContenteditableSliceForEmptiness",{enumerable:!0,get:function(){return e.checkContenteditableSliceForEmptiness}});var t=xn;Object.defineProperty(n,"getContenteditableSlice",{enumerable:!0,get:function(){return t.getContenteditableSlice}});var o=Bn;Object.defineProperty(n,"focus",{enumerable:!0,get:function(){return o.focus}});var i=ao;Object.defineProperty(n,"getCaretNodeAndOffset",{enumerable:!0,get:function(){return i.getCaretNodeAndOffset}});var s=Cn;Object.defineProperty(n,"getRange",{enumerable:!0,get:function(){return s.getRange}});var r=Tn;Object.defineProperty(n,"isCaretAtEndOfInput",{enumerable:!0,get:function(){return r.isCaretAtEndOfInput}});var a=In;Object.defineProperty(n,"isCaretAtStartOfInput",{enumerable:!0,get:function(){return a.isCaretAtStartOfInput}});var l=On;Object.defineProperty(n,"save",{enumerable:!0,get:function(){return l.save}})}(nn);class Zr extends E{keydown(e){switch(this.beforeKeydownProcessing(e),e.keyCode){case y.BACKSPACE:this.backspace(e);break;case y.DELETE:this.delete(e);break;case y.ENTER:this.enter(e);break;case y.DOWN:case y.RIGHT:this.arrowRightAndDown(e);break;case y.UP:case y.LEFT:this.arrowLeftAndUp(e);break;case y.TAB:this.tabPressed(e);break}e.key==="/"&&!e.ctrlKey&&!e.metaKey&&this.slashPressed(e),e.code==="Slash"&&(e.ctrlKey||e.metaKey)&&(e.preventDefault(),this.commandSlashPressed())}beforeKeydownProcessing(e){this.needToolbarClosing(e)&&Eo(e.keyCode)&&(this.Editor.Toolbar.close(),e.ctrlKey||e.metaKey||e.altKey||e.shiftKey||this.Editor.BlockSelection.clearSelection(e))}keyup(e){e.shiftKey||this.Editor.UI.checkEmptiness()}dragOver(e){const t=this.Editor.BlockManager.getBlockByChildNode(e.target);t.dropTarget=!0}dragLeave(e){const t=this.Editor.BlockManager.getBlockByChildNode(e.target);t.dropTarget=!1}handleCommandC(e){const{BlockSelection:t}=this.Editor;t.anyBlockSelected&&t.copySelectedBlocks(e)}handleCommandX(e){const{BlockSelection:t,BlockManager:o,Caret:i}=this.Editor;t.anyBlockSelected&&t.copySelectedBlocks(e).then(()=>{const s=o.removeSelectedBlocks(),r=o.insertDefaultBlockAtIndex(s,!0);i.setToBlock(r,i.positions.START),t.clearSelection(e)})}tabPressed(e){const{InlineToolbar:t,Caret:o}=this.Editor;if(t.opened)return;(e.shiftKey?o.navigatePrevious(!0):o.navigateNext(!0))&&e.preventDefault()}commandSlashPressed(){this.Editor.BlockSelection.selectedBlocks.length>1||this.activateBlockSettings()}slashPressed(e){this.Editor.BlockManager.currentBlock.isEmpty&&(e.preventDefault(),this.Editor.Caret.insertContentAtCaretPosition("/"),this.activateToolbox())}enter(e){const{BlockManager:t,UI:o}=this.Editor,i=t.currentBlock;if(i===void 0||i.tool.isLineBreaksEnabled||o.someToolbarOpened&&o.someFlipperButtonFocused||e.shiftKey&&!rt)return;let s=i;i.currentInput!==void 0&&$e(i.currentInput)&&!i.hasMedia?this.Editor.BlockManager.insertDefaultBlockAtIndex(this.Editor.BlockManager.currentBlockIndex):i.currentInput&&ze(i.currentInput)?s=this.Editor.BlockManager.insertDefaultBlockAtIndex(this.Editor.BlockManager.currentBlockIndex+1):s=this.Editor.BlockManager.split(),this.Editor.Caret.setToBlock(s),this.Editor.Toolbar.moveAndOpen(s),e.preventDefault()}backspace(e){const{BlockManager:t,Caret:o}=this.Editor,{currentBlock:i,previousBlock:s}=t;if(i===void 0||!b.isCollapsed||!i.currentInput||!$e(i.currentInput))return;if(e.preventDefault(),this.Editor.Toolbar.close(),!(i.currentInput===i.firstInput)){o.navigatePrevious();return}if(s===null)return;if(s.isEmpty){t.removeBlock(s);return}if(i.isEmpty){t.removeBlock(i);const l=t.currentBlock;o.setToBlock(l,o.positions.END);return}No(s,i)?this.mergeBlocks(s,i):o.setToBlock(s,o.positions.END)}delete(e){const{BlockManager:t,Caret:o}=this.Editor,{currentBlock:i,nextBlock:s}=t;if(!b.isCollapsed||!ze(i.currentInput))return;if(e.preventDefault(),this.Editor.Toolbar.close(),!(i.currentInput===i.lastInput)){o.navigateNext();return}if(s===null)return;if(s.isEmpty){t.removeBlock(s);return}if(i.isEmpty){t.removeBlock(i),o.setToBlock(s,o.positions.START);return}No(i,s)?this.mergeBlocks(i,s):o.setToBlock(s,o.positions.START)}mergeBlocks(e,t){const{BlockManager:o,Toolbar:i}=this.Editor;e.lastInput!==void 0&&(nn.focus(e.lastInput,!1),o.mergeBlocks(e,t).then(()=>{i.close()}))}arrowRightAndDown(e){const t=ae.usedKeys.includes(e.keyCode)&&(!e.shiftKey||e.keyCode===y.TAB);if(this.Editor.UI.someToolbarOpened&&t)return;this.Editor.Toolbar.close();const{currentBlock:o}=this.Editor.BlockManager,s=((o==null?void 0:o.currentInput)!==void 0?ze(o.currentInput):void 0)||this.Editor.BlockSelection.anyBlockSelected;if(e.shiftKey&&e.keyCode===y.DOWN&&s){this.Editor.CrossBlockSelection.toggleBlockSelectedState();return}if(e.keyCode===y.DOWN||e.keyCode===y.RIGHT&&!this.isRtl?this.Editor.Caret.navigateNext():this.Editor.Caret.navigatePrevious()){e.preventDefault();return}Ae(()=>{this.Editor.BlockManager.currentBlock&&this.Editor.BlockManager.currentBlock.updateCurrentInput()},20)(),this.Editor.BlockSelection.clearSelection(e)}arrowLeftAndUp(e){if(this.Editor.UI.someToolbarOpened){if(ae.usedKeys.includes(e.keyCode)&&(!e.shiftKey||e.keyCode===y.TAB))return;this.Editor.UI.closeAllToolbars()}this.Editor.Toolbar.close();const{currentBlock:t}=this.Editor.BlockManager,i=((t==null?void 0:t.currentInput)!==void 0?$e(t.currentInput):void 0)||this.Editor.BlockSelection.anyBlockSelected;if(e.shiftKey&&e.keyCode===y.UP&&i){this.Editor.CrossBlockSelection.toggleBlockSelectedState(!1);return}if(e.keyCode===y.UP||e.keyCode===y.LEFT&&!this.isRtl?this.Editor.Caret.navigatePrevious():this.Editor.Caret.navigateNext()){e.preventDefault();return}Ae(()=>{this.Editor.BlockManager.currentBlock&&this.Editor.BlockManager.currentBlock.updateCurrentInput()},20)(),this.Editor.BlockSelection.clearSelection(e)}needToolbarClosing(e){const t=e.keyCode===y.ENTER&&this.Editor.Toolbar.toolbox.opened,o=e.keyCode===y.ENTER&&this.Editor.BlockSettings.opened,i=e.keyCode===y.ENTER&&this.Editor.InlineToolbar.opened,s=e.keyCode===y.TAB;return!(e.shiftKey||s||t||o||i)}activateToolbox(){this.Editor.Toolbar.opened||this.Editor.Toolbar.moveAndOpen(),this.Editor.Toolbar.toolbox.open()}activateBlockSettings(){this.Editor.Toolbar.opened||this.Editor.Toolbar.moveAndOpen(),this.Editor.BlockSettings.opened||this.Editor.BlockSettings.open()}}class ho{constructor(e){this.blocks=[],this.workingArea=e}get length(){return this.blocks.length}get array(){return this.blocks}get nodes(){return xo(this.workingArea.children)}static set(e,t,o){return isNaN(Number(t))?(Reflect.set(e,t,o),!0):(e.insert(+t,o),!0)}static get(e,t){return isNaN(Number(t))?Reflect.get(e,t):e.get(+t)}push(e){this.blocks.push(e),this.insertToDOM(e)}swap(e,t){const o=this.blocks[t];d.swap(this.blocks[e].holder,o.holder),this.blocks[t]=this.blocks[e],this.blocks[e]=o}move(e,t){const o=this.blocks.splice(t,1)[0],i=e-1,s=Math.max(0,i),r=this.blocks[s];e>0?this.insertToDOM(o,"afterend",r):this.insertToDOM(o,"beforebegin",r),this.blocks.splice(e,0,o);const a=this.composeBlockEvent("move",{fromIndex:t,toIndex:e});o.call(te.MOVED,a)}insert(e,t,o=!1){if(!this.length){this.push(t);return}e>this.length&&(e=this.length),o&&(this.blocks[e].holder.remove(),this.blocks[e].call(te.REMOVED));const i=o?1:0;if(this.blocks.splice(e,i,t),e>0){const s=this.blocks[e-1];this.insertToDOM(t,"afterend",s)}else{const s=this.blocks[e+1];s?this.insertToDOM(t,"beforebegin",s):this.insertToDOM(t)}}replace(e,t){if(this.blocks[e]===void 0)throw Error("Incorrect index");this.blocks[e].holder.replaceWith(t.holder),this.blocks[e]=t}insertMany(e,t){const o=new DocumentFragment;for(const i of e)o.appendChild(i.holder);if(this.length>0){if(t>0){const i=Math.min(t-1,this.length-1);this.blocks[i].holder.after(o)}else t===0&&this.workingArea.prepend(o);this.blocks.splice(t,0,...e)}else this.blocks.push(...e),this.workingArea.appendChild(o);e.forEach(i=>i.call(te.RENDERED))}remove(e){isNaN(e)&&(e=this.length-1),this.blocks[e].holder.remove(),this.blocks[e].call(te.REMOVED),this.blocks.splice(e,1)}removeAll(){this.workingArea.innerHTML="",this.blocks.forEach(e=>e.call(te.REMOVED)),this.blocks.length=0}insertAfter(e,t){const o=this.blocks.indexOf(e);this.insert(o+1,t)}get(e){return this.blocks[e]}indexOf(e){return this.blocks.indexOf(e)}insertToDOM(e,t,o){t?o.holder.insertAdjacentElement(t,e.holder):this.workingArea.appendChild(e.holder),e.call(te.RENDERED)}composeBlockEvent(e,t){return new CustomEvent(e,{detail:t})}}const _n="block-removed",Mn="block-added",Gr="block-moved",An="block-changed";class Jr{constructor(){this.completed=Promise.resolve()}add(e){return new Promise((t,o)=>{this.completed=this.completed.then(e).then(t).catch(o)})}}class Qr extends E{constructor(){super(...arguments),this._currentBlockIndex=-1,this._blocks=null}get currentBlockIndex(){return this._currentBlockIndex}set currentBlockIndex(e){this._currentBlockIndex=e}get firstBlock(){return this._blocks[0]}get lastBlock(){return this._blocks[this._blocks.length-1]}get currentBlock(){return this._blocks[this.currentBlockIndex]}set currentBlock(e){this.currentBlockIndex=this.getBlockIndex(e)}get nextBlock(){return this.currentBlockIndex===this._blocks.length-1?null:this._blocks[this.currentBlockIndex+1]}get nextContentfulBlock(){return this.blocks.slice(this.currentBlockIndex+1).find(t=>!!t.inputs.length)}get previousContentfulBlock(){return this.blocks.slice(0,this.currentBlockIndex).reverse().find(t=>!!t.inputs.length)}get previousBlock(){return this.currentBlockIndex===0?null:this._blocks[this.currentBlockIndex-1]}get blocks(){return this._blocks.array}get isEditorEmpty(){return this.blocks.every(e=>e.isEmpty)}prepare(){const e=new ho(this.Editor.UI.nodes.redactor);this._blocks=new Proxy(e,{set:ho.set,get:ho.get}),this.listeners.on(document,"copy",t=>this.Editor.BlockEvents.handleCommandC(t))}toggleReadOnly(e){e?this.disableModuleBindings():this.enableModuleBindings()}composeBlock({tool:e,data:t={},id:o=void 0,tunes:i={}}){const s=this.Editor.ReadOnly.isEnabled,r=this.Editor.Tools.blockTools.get(e),a=new R({id:o,data:t,tool:r,api:this.Editor.API,readOnly:s,tunesData:i},this.eventsDispatcher);return s||window.requestIdleCallback(()=>{this.bindBlockEvents(a)},{timeout:2e3}),a}insert({id:e=void 0,tool:t=this.config.defaultBlock,data:o={},index:i,needToFocus:s=!0,replace:r=!1,tunes:a={}}={}){let l=i;l===void 0&&(l=this.currentBlockIndex+(r?0:1));const c=this.composeBlock({id:e,tool:t,data:o,tunes:a});return r&&this.blockDidMutated(_n,this.getBlockByIndex(l),{index:l}),this._blocks.insert(l,c,r),this.blockDidMutated(Mn,c,{index:l}),s?this.currentBlockIndex=l:l<=this.currentBlockIndex&&this.currentBlockIndex++,c}insertMany(e,t=0){this._blocks.insertMany(e,t)}async update(e,t,o){if(!t&&!o)return e;const i=await e.data,s=this.composeBlock({id:e.id,tool:e.name,data:Object.assign({},i,t??{}),tunes:o??e.tunes}),r=this.getBlockIndex(e);return this._blocks.replace(r,s),this.blockDidMutated(An,s,{index:r}),s}replace(e,t,o){const i=this.getBlockIndex(e);return this.insert({tool:t,data:o,index:i,replace:!0})}paste(e,t,o=!1){const i=this.insert({tool:e,replace:o});try{window.requestIdleCallback(()=>{i.call(te.ON_PASTE,t)})}catch(s){T(`${e}: onPaste callback call is failed`,"error",s)}return i}insertDefaultBlockAtIndex(e,t=!1){const o=this.composeBlock({tool:this.config.defaultBlock});return this._blocks[e]=o,this.blockDidMutated(Mn,o,{index:e}),t?this.currentBlockIndex=e:e<=this.currentBlockIndex&&this.currentBlockIndex++,o}insertAtEnd(){return this.currentBlockIndex=this.blocks.length-1,this.insert()}async mergeBlocks(e,t){let o;if(e.name===t.name&&e.mergeable){const i=await t.data;if(V(i)){console.error("Could not merge Block. Failed to extract original Block data.");return}const[s]=dt([i],e.tool.sanitizeConfig);o=s}else if(e.mergeable&&Pe(t,"export")&&Pe(e,"import")){const i=await t.exportDataAsString(),s=q(i,e.tool.sanitizeConfig);o=Ro(s,e.tool.conversionConfig)}o!==void 0&&(await e.mergeWith(o),this.removeBlock(t),this.currentBlockIndex=this._blocks.indexOf(e))}removeBlock(e,t=!0){return new Promise(o=>{const i=this._blocks.indexOf(e);if(!this.validateIndex(i))throw new Error("Can't find a Block to remove");e.destroy(),this._blocks.remove(i),this.blockDidMutated(_n,e,{index:i}),this.currentBlockIndex>=i&&this.currentBlockIndex--,this.blocks.length?i===0&&(this.currentBlockIndex=0):(this.unsetCurrentBlock(),t&&this.insert()),o()})}removeSelectedBlocks(){let e;for(let t=this.blocks.length-1;t>=0;t--)this.blocks[t].selected&&(this.removeBlock(this.blocks[t]),e=t);return e}removeAllBlocks(){for(let e=this.blocks.length-1;e>=0;e--)this._blocks.remove(e);this.unsetCurrentBlock(),this.insert(),this.currentBlock.firstInput.focus()}split(){const e=this.Editor.Caret.extractFragmentFromCaretPosition(),t=d.make("div");t.appendChild(e);const o={text:d.isEmpty(t)?"":t.innerHTML};return this.insert({data:o})}getBlockByIndex(e){return e===-1&&(e=this._blocks.length-1),this._blocks[e]}getBlockIndex(e){return this._blocks.indexOf(e)}getBlockById(e){return this._blocks.array.find(t=>t.id===e)}getBlock(e){d.isElement(e)||(e=e.parentNode);const t=this._blocks.nodes,o=e.closest(`.${R.CSS.wrapper}`),i=t.indexOf(o);if(i>=0)return this._blocks[i]}setCurrentBlockByChildNode(e){d.isElement(e)||(e=e.parentNode);const t=e.closest(`.${R.CSS.wrapper}`);if(!t)return;const o=t.closest(`.${this.Editor.UI.CSS.editorWrapper}`);if(o!=null&&o.isEqualNode(this.Editor.UI.nodes.wrapper))return this.currentBlockIndex=this._blocks.nodes.indexOf(t),this.currentBlock.updateCurrentInput(),this.currentBlock}getBlockByChildNode(e){if(!e||!(e instanceof Node))return;d.isElement(e)||(e=e.parentNode);const t=e.closest(`.${R.CSS.wrapper}`);return this.blocks.find(o=>o.holder===t)}swap(e,t){this._blocks.swap(e,t),this.currentBlockIndex=t}move(e,t=this.currentBlockIndex){if(isNaN(e)||isNaN(t)){T("Warning during 'move' call: incorrect indices provided.","warn");return}if(!this.validateIndex(e)||!this.validateIndex(t)){T("Warning during 'move' call: indices cannot be lower than 0 or greater than the amount of blocks.","warn");return}this._blocks.move(e,t),this.currentBlockIndex=e,this.blockDidMutated(Gr,this.currentBlock,{fromIndex:t,toIndex:e})}async convert(e,t,o){if(!await e.save())throw new Error("Could not convert Block. Failed to extract original Block data.");const s=this.Editor.Tools.blockTools.get(t);if(!s)throw new Error(`Could not convert Block. Tool «${t}» not found.`);const r=await e.exportDataAsString(),a=q(r,s.sanitizeConfig);let l=Ro(a,s.conversionConfig,s.settings);return o&&(l=Object.assign(l,o)),this.replace(e,s.name,l)}unsetCurrentBlock(){this.currentBlockIndex=-1}async clear(e=!1){const t=new Jr;this.blocks.forEach(o=>{t.add(async()=>{await this.removeBlock(o,!1)})}),await t.completed,this.unsetCurrentBlock(),e&&this.insert(),this.Editor.UI.checkEmptiness()}async destroy(){await Promise.all(this.blocks.map(e=>e.destroy()))}bindBlockEvents(e){const{BlockEvents:t}=this.Editor;this.readOnlyMutableListeners.on(e.holder,"keydown",o=>{t.keydown(o)}),this.readOnlyMutableListeners.on(e.holder,"keyup",o=>{t.keyup(o)}),this.readOnlyMutableListeners.on(e.holder,"dragover",o=>{t.dragOver(o)}),this.readOnlyMutableListeners.on(e.holder,"dragleave",o=>{t.dragLeave(o)}),e.on("didMutated",o=>this.blockDidMutated(An,o,{index:this.getBlockIndex(o)}))}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}enableModuleBindings(){this.readOnlyMutableListeners.on(document,"cut",e=>this.Editor.BlockEvents.handleCommandX(e)),this.blocks.forEach(e=>{this.bindBlockEvents(e)})}validateIndex(e){return!(e<0||e>=this._blocks.length)}blockDidMutated(e,t,o){const i=new CustomEvent(e,{detail:{target:new ee(t),...o}});return this.eventsDispatcher.emit(_o,{event:i}),t}}class ea extends E{constructor(){super(...arguments),this.anyBlockSelectedCache=null,this.needToSelectAll=!1,this.nativeInputSelected=!1,this.readyToBlockSelection=!1}get sanitizerConfig(){return{p:{},h1:{},h2:{},h3:{},h4:{},h5:{},h6:{},ol:{},ul:{},li:{},br:!0,img:{src:!0,width:!0,height:!0},a:{href:!0},b:{},i:{},u:{}}}get allBlocksSelected(){const{BlockManager:e}=this.Editor;return e.blocks.every(t=>t.selected===!0)}set allBlocksSelected(e){const{BlockManager:t}=this.Editor;t.blocks.forEach(o=>{o.selected=e}),this.clearCache()}get anyBlockSelected(){const{BlockManager:e}=this.Editor;return this.anyBlockSelectedCache===null&&(this.anyBlockSelectedCache=e.blocks.some(t=>t.selected===!0)),this.anyBlockSelectedCache}get selectedBlocks(){return this.Editor.BlockManager.blocks.filter(e=>e.selected)}prepare(){this.selection=new b,be.add({name:"CMD+A",handler:e=>{const{BlockManager:t,ReadOnly:o}=this.Editor;if(o.isEnabled){e.preventDefault(),this.selectAllBlocks();return}t.currentBlock&&this.handleCommandA(e)},on:this.Editor.UI.nodes.redactor})}toggleReadOnly(){b.get().removeAllRanges(),this.allBlocksSelected=!1}unSelectBlockByIndex(e){const{BlockManager:t}=this.Editor;let o;isNaN(e)?o=t.currentBlock:o=t.getBlockByIndex(e),o.selected=!1,this.clearCache()}clearSelection(e,t=!1){const{BlockManager:o,Caret:i,RectangleSelection:s}=this.Editor;this.needToSelectAll=!1,this.nativeInputSelected=!1,this.readyToBlockSelection=!1;const r=e&&e instanceof KeyboardEvent,a=r&&Eo(e.keyCode);if(this.anyBlockSelected&&r&&a&&!b.isSelectionExists){const l=o.removeSelectedBlocks();o.insertDefaultBlockAtIndex(l,!0),i.setToBlock(o.currentBlock),Ae(()=>{const c=e.key;i.insertContentAtCaretPosition(c.length>1?"":c)},20)()}if(this.Editor.CrossBlockSelection.clear(e),!this.anyBlockSelected||s.isRectActivated()){this.Editor.RectangleSelection.clearSelection();return}t&&this.selection.restore(),this.allBlocksSelected=!1}copySelectedBlocks(e){e.preventDefault();const t=d.make("div");this.selectedBlocks.forEach(s=>{const r=q(s.holder.innerHTML,this.sanitizerConfig),a=d.make("p");a.innerHTML=r,t.appendChild(a)});const o=Array.from(t.childNodes).map(s=>s.textContent).join(`

`),i=t.innerHTML;return e.clipboardData.setData("text/plain",o),e.clipboardData.setData("text/html",i),Promise.all(this.selectedBlocks.map(s=>s.save())).then(s=>{try{e.clipboardData.setData(this.Editor.Paste.MIME_TYPE,JSON.stringify(s))}catch{}})}selectBlockByIndex(e){const{BlockManager:t}=this.Editor,o=t.getBlockByIndex(e);o!==void 0&&this.selectBlock(o)}selectBlock(e){this.selection.save(),b.get().removeAllRanges(),e.selected=!0,this.clearCache(),this.Editor.InlineToolbar.close()}unselectBlock(e){e.selected=!1,this.clearCache()}clearCache(){this.anyBlockSelectedCache=null}destroy(){be.remove(this.Editor.UI.nodes.redactor,"CMD+A")}handleCommandA(e){if(this.Editor.RectangleSelection.clearSelection(),d.isNativeInput(e.target)&&!this.readyToBlockSelection){this.readyToBlockSelection=!0;return}const t=this.Editor.BlockManager.getBlock(e.target),o=t.inputs;if(o.length>1&&!this.readyToBlockSelection){this.readyToBlockSelection=!0;return}if(o.length===1&&!this.needToSelectAll){this.needToSelectAll=!0;return}this.needToSelectAll?(e.preventDefault(),this.selectAllBlocks(),this.needToSelectAll=!1,this.readyToBlockSelection=!1):this.readyToBlockSelection&&(e.preventDefault(),this.selectBlock(t),this.needToSelectAll=!0)}selectAllBlocks(){this.selection.save(),b.get().removeAllRanges(),this.allBlocksSelected=!0,this.Editor.InlineToolbar.close()}}class et extends E{get positions(){return{START:"start",END:"end",DEFAULT:"default"}}static get CSS(){return{shadowCaret:"cdx-shadow-caret"}}setToBlock(e,t=this.positions.DEFAULT,o=0){var c;const{BlockManager:i,BlockSelection:s}=this.Editor;if(s.clearSelection(),!e.focusable){(c=window.getSelection())==null||c.removeAllRanges(),s.selectBlock(e),i.currentBlock=e;return}let r;switch(t){case this.positions.START:r=e.firstInput;break;case this.positions.END:r=e.lastInput;break;default:r=e.currentInput}if(!r)return;const a=d.getDeepestNode(r,t===this.positions.END),l=d.getContentLength(a);switch(!0){case t===this.positions.START:o=0;break;case t===this.positions.END:case o>l:o=l;break}this.set(a,o),i.setCurrentBlockByChildNode(e.holder),i.currentBlock.currentInput=r}setToInput(e,t=this.positions.DEFAULT,o=0){const{currentBlock:i}=this.Editor.BlockManager,s=d.getDeepestNode(e);switch(t){case this.positions.START:this.set(s,0);break;case this.positions.END:this.set(s,d.getContentLength(s));break;default:o&&this.set(s,o)}i.currentInput=e}set(e,t=0){const{top:i,bottom:s}=b.setCursor(e,t),{innerHeight:r}=window;i<0?window.scrollBy(0,i-30):s>r&&window.scrollBy(0,s-r+30)}setToTheLastBlock(){const e=this.Editor.BlockManager.lastBlock;if(e)if(e.tool.isDefault&&e.isEmpty)this.setToBlock(e);else{const t=this.Editor.BlockManager.insertAtEnd();this.setToBlock(t)}}extractFragmentFromCaretPosition(){const e=b.get();if(e.rangeCount){const t=e.getRangeAt(0),o=this.Editor.BlockManager.currentBlock.currentInput;if(t.deleteContents(),o)if(d.isNativeInput(o)){const i=o,s=document.createDocumentFragment(),r=i.value.substring(0,i.selectionStart),a=i.value.substring(i.selectionStart);return s.textContent=a,i.value=r,s}else{const i=t.cloneRange();return i.selectNodeContents(o),i.setStart(t.endContainer,t.endOffset),i.extractContents()}}}navigateNext(e=!1){const{BlockManager:t}=this.Editor,{currentBlock:o,nextBlock:i}=t;if(o===void 0)return!1;const{nextInput:s,currentInput:r}=o,a=r!==void 0?ze(r):void 0;let l=i;const c=e||a||!o.focusable;if(s&&c)return this.setToInput(s,this.positions.START),!0;if(l===null){if(o.tool.isDefault||!c)return!1;l=t.insertAtEnd()}return c?(this.setToBlock(l,this.positions.START),!0):!1}navigatePrevious(e=!1){const{currentBlock:t,previousBlock:o}=this.Editor.BlockManager;if(!t)return!1;const{previousInput:i,currentInput:s}=t,r=s!==void 0?$e(s):void 0,a=e||r||!t.focusable;return i&&a?(this.setToInput(i,this.positions.END),!0):o!==null&&a?(this.setToBlock(o,this.positions.END),!0):!1}createShadow(e){const t=document.createElement("span");t.classList.add(et.CSS.shadowCaret),e.insertAdjacentElement("beforeend",t)}restoreCaret(e){const t=e.querySelector(`.${et.CSS.shadowCaret}`);if(!t)return;new b().expandToTag(t);const i=document.createRange();i.selectNode(t),i.extractContents()}insertContentAtCaretPosition(e){const t=document.createDocumentFragment(),o=document.createElement("div"),i=b.get(),s=b.range;o.innerHTML=e,Array.from(o.childNodes).forEach(c=>t.appendChild(c)),t.childNodes.length===0&&t.appendChild(new Text);const r=t.lastChild;s.deleteContents(),s.insertNode(t);const a=document.createRange(),l=r.nodeType===Node.TEXT_NODE?r:r.firstChild;l!==null&&l.textContent!==null&&a.setStart(l,l.textContent.length),i.removeAllRanges(),i.addRange(a)}}class ta extends E{constructor(){super(...arguments),this.onMouseUp=()=>{this.listeners.off(document,"mouseover",this.onMouseOver),this.listeners.off(document,"mouseup",this.onMouseUp)},this.onMouseOver=e=>{const{BlockManager:t,BlockSelection:o}=this.Editor;if(e.relatedTarget===null&&e.target===null)return;const i=t.getBlockByChildNode(e.relatedTarget)||this.lastSelectedBlock,s=t.getBlockByChildNode(e.target);if(!(!i||!s)&&s!==i){if(i===this.firstSelectedBlock){b.get().removeAllRanges(),i.selected=!0,s.selected=!0,o.clearCache();return}if(s===this.firstSelectedBlock){i.selected=!1,s.selected=!1,o.clearCache();return}this.Editor.InlineToolbar.close(),this.toggleBlocksSelectedState(i,s),this.lastSelectedBlock=s}}}async prepare(){this.listeners.on(document,"mousedown",e=>{this.enableCrossBlockSelection(e)})}watchSelection(e){if(e.button!==Xn.LEFT)return;const{BlockManager:t}=this.Editor;this.firstSelectedBlock=t.getBlock(e.target),this.lastSelectedBlock=this.firstSelectedBlock,this.listeners.on(document,"mouseover",this.onMouseOver),this.listeners.on(document,"mouseup",this.onMouseUp)}get isCrossBlockSelectionStarted(){return!!this.firstSelectedBlock&&!!this.lastSelectedBlock&&this.firstSelectedBlock!==this.lastSelectedBlock}toggleBlockSelectedState(e=!0){const{BlockManager:t,BlockSelection:o}=this.Editor;this.lastSelectedBlock||(this.lastSelectedBlock=this.firstSelectedBlock=t.currentBlock),this.firstSelectedBlock===this.lastSelectedBlock&&(this.firstSelectedBlock.selected=!0,o.clearCache(),b.get().removeAllRanges());const i=t.blocks.indexOf(this.lastSelectedBlock)+(e?1:-1),s=t.blocks[i];s&&(this.lastSelectedBlock.selected!==s.selected?(s.selected=!0,o.clearCache()):(this.lastSelectedBlock.selected=!1,o.clearCache()),this.lastSelectedBlock=s,this.Editor.InlineToolbar.close(),s.holder.scrollIntoView({block:"nearest"}))}clear(e){const{BlockManager:t,BlockSelection:o,Caret:i}=this.Editor,s=t.blocks.indexOf(this.firstSelectedBlock),r=t.blocks.indexOf(this.lastSelectedBlock);if(o.anyBlockSelected&&s>-1&&r>-1&&e&&e instanceof KeyboardEvent)switch(e.keyCode){case y.DOWN:case y.RIGHT:i.setToBlock(t.blocks[Math.max(s,r)],i.positions.END);break;case y.UP:case y.LEFT:i.setToBlock(t.blocks[Math.min(s,r)],i.positions.START);break;default:i.setToBlock(t.blocks[Math.max(s,r)],i.positions.END)}this.firstSelectedBlock=this.lastSelectedBlock=null}enableCrossBlockSelection(e){const{UI:t}=this.Editor;b.isCollapsed||this.Editor.BlockSelection.clearSelection(e),t.nodes.redactor.contains(e.target)?this.watchSelection(e):this.Editor.BlockSelection.clearSelection(e)}toggleBlocksSelectedState(e,t){const{BlockManager:o,BlockSelection:i}=this.Editor,s=o.blocks.indexOf(e),r=o.blocks.indexOf(t),a=e.selected!==t.selected;for(let l=Math.min(s,r);l<=Math.max(s,r);l++){const c=o.blocks[l];c!==this.firstSelectedBlock&&c!==(a?e:t)&&(o.blocks[l].selected=!o.blocks[l].selected,i.clearCache())}}}class oa extends E{constructor(){super(...arguments),this.isStartedAtEditor=!1}toggleReadOnly(e){e?this.disableModuleBindings():this.enableModuleBindings()}enableModuleBindings(){const{UI:e}=this.Editor;this.readOnlyMutableListeners.on(e.nodes.holder,"drop",async t=>{await this.processDrop(t)},!0),this.readOnlyMutableListeners.on(e.nodes.holder,"dragstart",()=>{this.processDragStart()}),this.readOnlyMutableListeners.on(e.nodes.holder,"dragover",t=>{this.processDragOver(t)},!0)}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}async processDrop(e){const{BlockManager:t,Paste:o,Caret:i}=this.Editor;e.preventDefault(),t.blocks.forEach(r=>{r.dropTarget=!1}),b.isAtEditor&&!b.isCollapsed&&this.isStartedAtEditor&&document.execCommand("delete"),this.isStartedAtEditor=!1;const s=t.setCurrentBlockByChildNode(e.target);if(s)this.Editor.Caret.setToBlock(s,i.positions.END);else{const r=t.setCurrentBlockByChildNode(t.lastBlock.holder);this.Editor.Caret.setToBlock(r,i.positions.END)}await o.processDataTransfer(e.dataTransfer,!0)}processDragStart(){b.isAtEditor&&!b.isCollapsed&&(this.isStartedAtEditor=!0),this.Editor.InlineToolbar.close()}processDragOver(e){e.preventDefault()}}const na=180,ia=400;class sa extends E{constructor({config:e,eventsDispatcher:t}){super({config:e,eventsDispatcher:t}),this.disabled=!1,this.batchingTimeout=null,this.batchingOnChangeQueue=new Map,this.batchTime=ia,this.mutationObserver=new MutationObserver(o=>{this.redactorChanged(o)}),this.eventsDispatcher.on(_o,o=>{this.particularBlockChanged(o.event)}),this.eventsDispatcher.on(Mo,()=>{this.disable()}),this.eventsDispatcher.on(Ao,()=>{this.enable()})}enable(){this.mutationObserver.observe(this.Editor.UI.nodes.redactor,{childList:!0,subtree:!0,characterData:!0,attributes:!0}),this.disabled=!1}disable(){this.mutationObserver.disconnect(),this.disabled=!0}particularBlockChanged(e){this.disabled||!M(this.config.onChange)||(this.batchingOnChangeQueue.set(`block:${e.detail.target.id}:event:${e.type}`,e),this.batchingTimeout&&clearTimeout(this.batchingTimeout),this.batchingTimeout=setTimeout(()=>{let t;this.batchingOnChangeQueue.size===1?t=this.batchingOnChangeQueue.values().next().value:t=Array.from(this.batchingOnChangeQueue.values()),this.config.onChange&&this.config.onChange(this.Editor.API.methods,t),this.batchingOnChangeQueue.clear()},this.batchTime))}redactorChanged(e){this.eventsDispatcher.emit(at,{mutations:e})}}const Ln=class Wn extends E{constructor(){super(...arguments),this.MIME_TYPE="application/x-editor-js",this.toolsTags={},this.tagsByTool={},this.toolsPatterns=[],this.toolsFiles={},this.exceptionList=[],this.processTool=e=>{try{const t=e.create({},{},!1);if(e.pasteConfig===!1){this.exceptionList.push(e.name);return}if(!M(t.onPaste))return;this.getTagsConfig(e),this.getFilesConfig(e),this.getPatternsConfig(e)}catch(t){T(`Paste handling for «${e.name}» Tool hasn't been set up because of the error`,"warn",t)}},this.handlePasteEvent=async e=>{const{BlockManager:t,Toolbar:o}=this.Editor,i=t.setCurrentBlockByChildNode(e.target);!i||this.isNativeBehaviour(e.target)&&!e.clipboardData.types.includes("Files")||i&&this.exceptionList.includes(i.name)||(e.preventDefault(),this.processDataTransfer(e.clipboardData),o.close())}}async prepare(){this.processTools()}toggleReadOnly(e){e?this.unsetCallback():this.setCallback()}async processDataTransfer(e,t=!1){const{Tools:o}=this.Editor,i=e.types;if((i.includes?i.includes("Files"):i.contains("Files"))&&!V(this.toolsFiles)){await this.processFiles(e.files);return}const r=e.getData(this.MIME_TYPE),a=e.getData("text/plain");let l=e.getData("text/html");if(r)try{this.insertEditorJSData(JSON.parse(r));return}catch{}t&&a.trim()&&l.trim()&&(l="<p>"+(l.trim()?l:a)+"</p>");const c=Object.keys(this.toolsTags).reduce((p,g)=>(p[g.toLowerCase()]=this.toolsTags[g].sanitizationConfig??{},p),{}),u=Object.assign({},c,o.getAllInlineToolsSanitizeConfig(),{br:{}}),h=q(l,u);!h.trim()||h.trim()===a||!d.isHTMLString(h)?await this.processText(a):await this.processText(h,!0)}async processText(e,t=!1){const{Caret:o,BlockManager:i}=this.Editor,s=t?this.processHTML(e):this.processPlain(e);if(!s.length)return;if(s.length===1){s[0].isBlock?this.processSingleBlock(s.pop()):this.processInlinePaste(s.pop());return}const a=i.currentBlock&&i.currentBlock.tool.isDefault&&i.currentBlock.isEmpty;s.map(async(l,c)=>this.insertBlock(l,c===0&&a)),i.currentBlock&&o.setToBlock(i.currentBlock,o.positions.END)}setCallback(){this.listeners.on(this.Editor.UI.nodes.holder,"paste",this.handlePasteEvent)}unsetCallback(){this.listeners.off(this.Editor.UI.nodes.holder,"paste",this.handlePasteEvent)}processTools(){const e=this.Editor.Tools.blockTools;Array.from(e.values()).forEach(this.processTool)}collectTagNames(e){return Q(e)?[e]:N(e)?Object.keys(e):[]}getTagsConfig(e){if(e.pasteConfig===!1)return;const t=e.pasteConfig.tags||[],o=[];t.forEach(i=>{const s=this.collectTagNames(i);o.push(...s),s.forEach(r=>{if(Object.prototype.hasOwnProperty.call(this.toolsTags,r)){T(`Paste handler for «${e.name}» Tool on «${r}» tag is skipped because it is already used by «${this.toolsTags[r].tool.name}» Tool.`,"warn");return}const a=N(i)?i[r]:null;this.toolsTags[r.toUpperCase()]={tool:e,sanitizationConfig:a}})}),this.tagsByTool[e.name]=o.map(i=>i.toUpperCase())}getFilesConfig(e){if(e.pasteConfig===!1)return;const{files:t={}}=e.pasteConfig;let{extensions:o,mimeTypes:i}=t;!o&&!i||(o&&!Array.isArray(o)&&(T(`«extensions» property of the onDrop config for «${e.name}» Tool should be an array`),o=[]),i&&!Array.isArray(i)&&(T(`«mimeTypes» property of the onDrop config for «${e.name}» Tool should be an array`),i=[]),i&&(i=i.filter(s=>Jn(s)?!0:(T(`MIME type value «${s}» for the «${e.name}» Tool is not a valid MIME type`,"warn"),!1))),this.toolsFiles[e.name]={extensions:o||[],mimeTypes:i||[]})}getPatternsConfig(e){e.pasteConfig===!1||!e.pasteConfig.patterns||V(e.pasteConfig.patterns)||Object.entries(e.pasteConfig.patterns).forEach(([t,o])=>{o instanceof RegExp||T(`Pattern ${o} for «${e.name}» Tool is skipped because it should be a Regexp instance.`,"warn"),this.toolsPatterns.push({key:t,pattern:o,tool:e})})}isNativeBehaviour(e){return d.isNativeInput(e)}async processFiles(e){const{BlockManager:t}=this.Editor;let o;o=await Promise.all(Array.from(e).map(r=>this.processFile(r))),o=o.filter(r=>!!r);const s=t.currentBlock.tool.isDefault&&t.currentBlock.isEmpty;o.forEach((r,a)=>{t.paste(r.type,r.event,a===0&&s)})}async processFile(e){const t=Gn(e),o=Object.entries(this.toolsFiles).find(([r,{mimeTypes:a,extensions:l}])=>{const[c,u]=e.type.split("/"),h=l.find(g=>g.toLowerCase()===t.toLowerCase()),p=a.find(g=>{const[f,v]=g.split("/");return f===c&&(v===u||v==="*")});return!!h||!!p});if(!o)return;const[i]=o;return{event:this.composePasteEvent("file",{file:e}),type:i}}processHTML(e){const{Tools:t}=this.Editor,o=d.make("DIV");return o.innerHTML=e,this.getNodes(o).map(s=>{let r,a=t.defaultTool,l=!1;switch(s.nodeType){case Node.DOCUMENT_FRAGMENT_NODE:r=d.make("div"),r.appendChild(s);break;case Node.ELEMENT_NODE:r=s,l=!0,this.toolsTags[r.tagName]&&(a=this.toolsTags[r.tagName].tool);break}const{tags:c}=a.pasteConfig||{tags:[]},u=c.reduce((g,f)=>(this.collectTagNames(f).forEach(O=>{const S=N(f)?f[O]:null;g[O.toLowerCase()]=S||{}}),g),{}),h=Object.assign({},u,a.baseSanitizeConfig);if(r.tagName.toLowerCase()==="table"){const g=q(r.outerHTML,h);r=d.make("div",void 0,{innerHTML:g}).firstChild}else r.innerHTML=q(r.innerHTML,h);const p=this.composePasteEvent("tag",{data:r});return{content:r,isBlock:l,tool:a.name,event:p}}).filter(s=>{const r=d.isEmpty(s.content),a=d.isSingleTag(s.content);return!r||a})}processPlain(e){const{defaultBlock:t}=this.config;if(!e)return[];const o=t;return e.split(/\r?\n/).filter(i=>i.trim()).map(i=>{const s=d.make("div");s.textContent=i;const r=this.composePasteEvent("tag",{data:s});return{content:s,tool:o,isBlock:!1,event:r}})}async processSingleBlock(e){const{Caret:t,BlockManager:o}=this.Editor,{currentBlock:i}=o;if(!i||e.tool!==i.name||!d.containsOnlyInlineElements(e.content.innerHTML)){this.insertBlock(e,(i==null?void 0:i.tool.isDefault)&&i.isEmpty);return}t.insertContentAtCaretPosition(e.content.innerHTML)}async processInlinePaste(e){const{BlockManager:t,Caret:o}=this.Editor,{content:i}=e;if(t.currentBlock&&t.currentBlock.tool.isDefault&&i.textContent.length<Wn.PATTERN_PROCESSING_MAX_LENGTH){const r=await this.processPattern(i.textContent);if(r){const a=t.currentBlock&&t.currentBlock.tool.isDefault&&t.currentBlock.isEmpty,l=t.paste(r.tool,r.event,a);o.setToBlock(l,o.positions.END);return}}if(t.currentBlock&&t.currentBlock.currentInput){const r=t.currentBlock.tool.baseSanitizeConfig;document.execCommand("insertHTML",!1,q(i.innerHTML,r))}else this.insertBlock(e)}async processPattern(e){const t=this.toolsPatterns.find(i=>{const s=i.pattern.exec(e);return s?e===s.shift():!1});return t?{event:this.composePasteEvent("pattern",{key:t.key,data:e}),tool:t.tool.name}:void 0}insertBlock(e,t=!1){const{BlockManager:o,Caret:i}=this.Editor,{currentBlock:s}=o;let r;if(t&&s&&s.isEmpty){r=o.paste(e.tool,e.event,!0),i.setToBlock(r,i.positions.END);return}r=o.paste(e.tool,e.event),i.setToBlock(r,i.positions.END)}insertEditorJSData(e){const{BlockManager:t,Caret:o,Tools:i}=this.Editor;dt(e,r=>i.blockTools.get(r).sanitizeConfig).forEach(({tool:r,data:a},l)=>{let c=!1;l===0&&(c=t.currentBlock&&t.currentBlock.tool.isDefault&&t.currentBlock.isEmpty);const u=t.insert({tool:r,data:a,replace:c});o.setToBlock(u,o.positions.END)})}processElementNode(e,t,o){const i=Object.keys(this.toolsTags),s=e,{tool:r}=this.toolsTags[s.tagName]||{},a=this.tagsByTool[r==null?void 0:r.name]||[],l=i.includes(s.tagName),c=d.blockElements.includes(s.tagName.toLowerCase()),u=Array.from(s.children).some(({tagName:p})=>i.includes(p)&&!a.includes(p)),h=Array.from(s.children).some(({tagName:p})=>d.blockElements.includes(p.toLowerCase()));if(!c&&!l&&!u)return o.appendChild(s),[...t,o];if(l&&!u||c&&!h&&!u)return[...t,o,s]}getNodes(e){const t=Array.from(e.childNodes);let o;const i=(s,r)=>{if(d.isEmpty(r)&&!d.isSingleTag(r))return s;const a=s[s.length-1];let l=new DocumentFragment;switch(a&&d.isFragment(a)&&(l=s.pop()),r.nodeType){case Node.ELEMENT_NODE:if(o=this.processElementNode(r,s,l),o)return o;break;case Node.TEXT_NODE:return l.appendChild(r),[...s,l];default:return[...s,l]}return[...s,...Array.from(r.childNodes).reduce(i,[])]};return t.reduce(i,[])}composePasteEvent(e,t){return new CustomEvent(e,{detail:t})}};Ln.PATTERN_PROCESSING_MAX_LENGTH=450;let ra=Ln;class aa extends E{constructor(){super(...arguments),this.toolsDontSupportReadOnly=[],this.readOnlyEnabled=!1}get isEnabled(){return this.readOnlyEnabled}async prepare(){const{Tools:e}=this.Editor,{blockTools:t}=e,o=[];Array.from(t.entries()).forEach(([i,s])=>{s.isReadOnlySupported||o.push(i)}),this.toolsDontSupportReadOnly=o,this.config.readOnly&&o.length>0&&this.throwCriticalError(),this.toggle(this.config.readOnly,!0)}async toggle(e=!this.readOnlyEnabled,t=!1){e&&this.toolsDontSupportReadOnly.length>0&&this.throwCriticalError();const o=this.readOnlyEnabled;this.readOnlyEnabled=e;for(const s in this.Editor)this.Editor[s].toggleReadOnly&&this.Editor[s].toggleReadOnly(e);if(o===e)return this.readOnlyEnabled;if(t)return this.readOnlyEnabled;this.Editor.ModificationsObserver.disable();const i=await this.Editor.Saver.save();return await this.Editor.BlockManager.clear(),await this.Editor.Renderer.render(i.blocks),this.Editor.ModificationsObserver.enable(),this.readOnlyEnabled}throwCriticalError(){throw new Oo(`To enable read-only mode all connected tools should support it. Tools ${this.toolsDontSupportReadOnly.join(", ")} don't support read-only mode.`)}}class Ie extends E{constructor(){super(...arguments),this.isRectSelectionActivated=!1,this.SCROLL_SPEED=3,this.HEIGHT_OF_SCROLL_ZONE=40,this.BOTTOM_SCROLL_ZONE=1,this.TOP_SCROLL_ZONE=2,this.MAIN_MOUSE_BUTTON=0,this.mousedown=!1,this.isScrolling=!1,this.inScrollZone=null,this.startX=0,this.startY=0,this.mouseX=0,this.mouseY=0,this.stackOfSelected=[],this.listenerIds=[]}static get CSS(){return{overlay:"codex-editor-overlay",overlayContainer:"codex-editor-overlay__container",rect:"codex-editor-overlay__rectangle",topScrollZone:"codex-editor-overlay__scroll-zone--top",bottomScrollZone:"codex-editor-overlay__scroll-zone--bottom"}}prepare(){this.enableModuleBindings()}startSelection(e,t){const o=document.elementFromPoint(e-window.pageXOffset,t-window.pageYOffset);o.closest(`.${this.Editor.Toolbar.CSS.toolbar}`)||(this.Editor.BlockSelection.allBlocksSelected=!1,this.clearSelection(),this.stackOfSelected=[]);const s=[`.${R.CSS.content}`,`.${this.Editor.Toolbar.CSS.toolbar}`,`.${this.Editor.InlineToolbar.CSS.inlineToolbar}`],r=o.closest("."+this.Editor.UI.CSS.editorWrapper),a=s.some(l=>!!o.closest(l));!r||a||(this.mousedown=!0,this.startX=e,this.startY=t)}endSelection(){this.mousedown=!1,this.startX=0,this.startY=0,this.overlayRectangle.style.display="none"}isRectActivated(){return this.isRectSelectionActivated}clearSelection(){this.isRectSelectionActivated=!1}enableModuleBindings(){const{container:e}=this.genHTML();this.listeners.on(e,"mousedown",t=>{this.processMouseDown(t)},!1),this.listeners.on(document.body,"mousemove",ot(t=>{this.processMouseMove(t)},10),{passive:!0}),this.listeners.on(document.body,"mouseleave",()=>{this.processMouseLeave()}),this.listeners.on(window,"scroll",ot(t=>{this.processScroll(t)},10),{passive:!0}),this.listeners.on(document.body,"mouseup",()=>{this.processMouseUp()},!1)}processMouseDown(e){if(e.button!==this.MAIN_MOUSE_BUTTON)return;e.target.closest(d.allInputsSelector)!==null||this.startSelection(e.pageX,e.pageY)}processMouseMove(e){this.changingRectangle(e),this.scrollByZones(e.clientY)}processMouseLeave(){this.clearSelection(),this.endSelection()}processScroll(e){this.changingRectangle(e)}processMouseUp(){this.clearSelection(),this.endSelection()}scrollByZones(e){if(this.inScrollZone=null,e<=this.HEIGHT_OF_SCROLL_ZONE&&(this.inScrollZone=this.TOP_SCROLL_ZONE),document.documentElement.clientHeight-e<=this.HEIGHT_OF_SCROLL_ZONE&&(this.inScrollZone=this.BOTTOM_SCROLL_ZONE),!this.inScrollZone){this.isScrolling=!1;return}this.isScrolling||(this.scrollVertical(this.inScrollZone===this.TOP_SCROLL_ZONE?-this.SCROLL_SPEED:this.SCROLL_SPEED),this.isScrolling=!0)}genHTML(){const{UI:e}=this.Editor,t=e.nodes.holder.querySelector("."+e.CSS.editorWrapper),o=d.make("div",Ie.CSS.overlay,{}),i=d.make("div",Ie.CSS.overlayContainer,{}),s=d.make("div",Ie.CSS.rect,{});return i.appendChild(s),o.appendChild(i),t.appendChild(o),this.overlayRectangle=s,{container:t,overlay:o}}scrollVertical(e){if(!(this.inScrollZone&&this.mousedown))return;const t=window.pageYOffset;window.scrollBy(0,e),this.mouseY+=window.pageYOffset-t,setTimeout(()=>{this.scrollVertical(e)},0)}changingRectangle(e){if(!this.mousedown)return;e.pageY!==void 0&&(this.mouseX=e.pageX,this.mouseY=e.pageY);const{rightPos:t,leftPos:o,index:i}=this.genInfoForMouseSelection(),s=this.startX>t&&this.mouseX>t,r=this.startX<o&&this.mouseX<o;this.rectCrossesBlocks=!(s||r),this.isRectSelectionActivated||(this.rectCrossesBlocks=!1,this.isRectSelectionActivated=!0,this.shrinkRectangleToPoint(),this.overlayRectangle.style.display="block"),this.updateRectangleSize(),this.Editor.Toolbar.close(),i!==void 0&&(this.trySelectNextBlock(i),this.inverseSelection(),b.get().removeAllRanges())}shrinkRectangleToPoint(){this.overlayRectangle.style.left=`${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.top=`${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.bottom=`calc(100% - ${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.right=`calc(100% - ${this.startX-window.pageXOffset}px`}inverseSelection(){const t=this.Editor.BlockManager.getBlockByIndex(this.stackOfSelected[0]).selected;if(this.rectCrossesBlocks&&!t)for(const o of this.stackOfSelected)this.Editor.BlockSelection.selectBlockByIndex(o);if(!this.rectCrossesBlocks&&t)for(const o of this.stackOfSelected)this.Editor.BlockSelection.unSelectBlockByIndex(o)}updateRectangleSize(){this.mouseY>=this.startY?(this.overlayRectangle.style.top=`${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.bottom=`calc(100% - ${this.mouseY-window.pageYOffset}px`):(this.overlayRectangle.style.bottom=`calc(100% - ${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.top=`${this.mouseY-window.pageYOffset}px`),this.mouseX>=this.startX?(this.overlayRectangle.style.left=`${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.right=`calc(100% - ${this.mouseX-window.pageXOffset}px`):(this.overlayRectangle.style.right=`calc(100% - ${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.left=`${this.mouseX-window.pageXOffset}px`)}genInfoForMouseSelection(){const t=document.body.offsetWidth/2,o=this.mouseY-window.pageYOffset,i=document.elementFromPoint(t,o),s=this.Editor.BlockManager.getBlockByChildNode(i);let r;s!==void 0&&(r=this.Editor.BlockManager.blocks.findIndex(h=>h.holder===s.holder));const a=this.Editor.BlockManager.lastBlock.holder.querySelector("."+R.CSS.content),l=Number.parseInt(window.getComputedStyle(a).width,10)/2,c=t-l,u=t+l;return{index:r,leftPos:c,rightPos:u}}addBlockInSelection(e){this.rectCrossesBlocks&&this.Editor.BlockSelection.selectBlockByIndex(e),this.stackOfSelected.push(e)}trySelectNextBlock(e){const t=this.stackOfSelected[this.stackOfSelected.length-1]===e,o=this.stackOfSelected.length,i=1,s=-1,r=0;if(t)return;const a=this.stackOfSelected[o-1]-this.stackOfSelected[o-2]>0;let l=r;o>1&&(l=a?i:s);const c=e>this.stackOfSelected[o-1]&&l===i,u=e<this.stackOfSelected[o-1]&&l===s,p=!(c||u||l===r);if(!p&&(e>this.stackOfSelected[o-1]||this.stackOfSelected[o-1]===void 0)){let v=this.stackOfSelected[o-1]+1||e;for(v;v<=e;v++)this.addBlockInSelection(v);return}if(!p&&e<this.stackOfSelected[o-1]){for(let v=this.stackOfSelected[o-1]-1;v>=e;v--)this.addBlockInSelection(v);return}if(!p)return;let g=o-1,f;for(e>this.stackOfSelected[o-1]?f=()=>e>this.stackOfSelected[g]:f=()=>e<this.stackOfSelected[g];f();)this.rectCrossesBlocks&&this.Editor.BlockSelection.unSelectBlockByIndex(this.stackOfSelected[g]),this.stackOfSelected.pop(),g--}}class la extends E{async render(e){return new Promise(t=>{const{Tools:o,BlockManager:i}=this.Editor;if(e.length===0)i.insert();else{const s=e.map(({type:r,data:a,tunes:l,id:c})=>{o.available.has(r)===!1&&(X(`Tool «${r}» is not found. Check 'tools' property at the Editor.js config.`,"warn"),a=this.composeStubDataForTool(r,a,c),r=o.stubTool);let u;try{u=i.composeBlock({id:c,tool:r,data:a,tunes:l})}catch(h){T(`Block «${r}» skipped because of plugins error`,"error",{data:a,error:h}),a=this.composeStubDataForTool(r,a,c),r=o.stubTool,u=i.composeBlock({id:c,tool:r,data:a,tunes:l})}return u});i.insertMany(s)}window.requestIdleCallback(()=>{t()},{timeout:2e3})})}composeStubDataForTool(e,t,o){const{Tools:i}=this.Editor;let s=e;if(i.unavailable.has(e)){const r=i.unavailable.get(e).toolbox;r!==void 0&&r[0].title!==void 0&&(s=r[0].title)}return{savedData:{id:o,type:e,data:t},title:s}}}class ca extends E{async save(){const{BlockManager:e,Tools:t}=this.Editor,o=e.blocks,i=[];try{o.forEach(a=>{i.push(this.getSavedData(a))});const s=await Promise.all(i),r=await dt(s,a=>t.blockTools.get(a).sanitizeConfig);return this.makeOutput(r)}catch(s){X("Saving failed due to the Error %o","error",s)}}async getSavedData(e){const t=await e.save(),o=t&&await e.validate(t.data);return{...t,isValid:o}}makeOutput(e){const t=[];return e.forEach(({id:o,tool:i,data:s,tunes:r,isValid:a})=>{if(!a){T(`Block «${i}» skipped because saved data is invalid`);return}if(i===this.Editor.Tools.stubTool){t.push(s);return}const l={id:o,type:i,data:s,...!V(r)&&{tunes:r}};t.push(l)}),{time:+new Date,blocks:t,version:"2.31.0-rc.7"}}}(function(){try{if(typeof document<"u"){var n=document.createElement("style");n.appendChild(document.createTextNode(".ce-paragraph{line-height:1.6em;outline:none}.ce-block:only-of-type .ce-paragraph[data-placeholder-active]:empty:before,.ce-block:only-of-type .ce-paragraph[data-placeholder-active][data-empty=true]:before{content:attr(data-placeholder-active)}.ce-paragraph p:first-of-type{margin-top:0}.ce-paragraph p:last-of-type{margin-bottom:0}")),document.head.appendChild(n)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}})();const da='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8 9V7.2C8 7.08954 8.08954 7 8.2 7L12 7M16 9V7.2C16 7.08954 15.9105 7 15.8 7L12 7M12 7L12 17M12 17H10M12 17H14"/></svg>';function ua(n){const e=document.createElement("div");e.innerHTML=n.trim();const t=document.createDocumentFragment();return t.append(...Array.from(e.childNodes)),t}/**
 * Base Paragraph Block for the Editor.js.
 * Represents a regular text block
 *
 * <AUTHOR> (<EMAIL>)
 * @copyright CodeX 2018
 * @license The MIT License (MIT)
 */class po{static get DEFAULT_PLACEHOLDER(){return""}constructor({data:e,config:t,api:o,readOnly:i}){this.api=o,this.readOnly=i,this._CSS={block:this.api.styles.block,wrapper:"ce-paragraph"},this.readOnly||(this.onKeyUp=this.onKeyUp.bind(this)),this._placeholder=t.placeholder?t.placeholder:po.DEFAULT_PLACEHOLDER,this._data=e??{},this._element=null,this._preserveBlank=t.preserveBlank??!1}onKeyUp(e){if(e.code!=="Backspace"&&e.code!=="Delete"||!this._element)return;const{textContent:t}=this._element;t===""&&(this._element.innerHTML="")}drawView(){const e=document.createElement("DIV");return e.classList.add(this._CSS.wrapper,this._CSS.block),e.contentEditable="false",e.dataset.placeholderActive=this.api.i18n.t(this._placeholder),this._data.text&&(e.innerHTML=this._data.text),this.readOnly||(e.contentEditable="true",e.addEventListener("keyup",this.onKeyUp)),e}render(){return this._element=this.drawView(),this._element}merge(e){if(!this._element)return;this._data.text+=e.text;const t=ua(e.text);this._element.appendChild(t),this._element.normalize()}validate(e){return!(e.text.trim()===""&&!this._preserveBlank)}save(e){return{text:e.innerHTML}}onPaste(e){const t={text:e.detail.data.innerHTML};this._data=t,window.requestAnimationFrame(()=>{this._element&&(this._element.innerHTML=this._data.text||"")})}static get conversionConfig(){return{export:"text",import:"text"}}static get sanitize(){return{text:{br:!0}}}static get isReadOnlySupported(){return!0}static get pasteConfig(){return{tags:["P"]}}static get toolbox(){return{icon:da,title:"Text"}}}class fo{constructor(){this.commandName="bold"}static get sanitize(){return{b:{}}}render(){return{icon:Hi,name:"bold",onActivate:()=>{document.execCommand(this.commandName)},isActive:()=>document.queryCommandState(this.commandName)}}get shortcut(){return"CMD+B"}}fo.isInline=!0,fo.title="Bold";class go{constructor(){this.commandName="italic",this.CSS={button:"ce-inline-tool",buttonActive:"ce-inline-tool--active",buttonModifier:"ce-inline-tool--italic"},this.nodes={button:null}}static get sanitize(){return{i:{}}}render(){return this.nodes.button=document.createElement("button"),this.nodes.button.type="button",this.nodes.button.classList.add(this.CSS.button,this.CSS.buttonModifier),this.nodes.button.innerHTML=Xi,this.nodes.button}surround(){document.execCommand(this.commandName)}checkState(){const e=document.queryCommandState(this.commandName);return this.nodes.button.classList.toggle(this.CSS.buttonActive,e),e}get shortcut(){return"CMD+I"}}go.isInline=!0,go.title="Italic";class mo{constructor({api:e}){this.commandLink="createLink",this.commandUnlink="unlink",this.ENTER_KEY=13,this.CSS={button:"ce-inline-tool",buttonActive:"ce-inline-tool--active",buttonModifier:"ce-inline-tool--link",buttonUnlink:"ce-inline-tool--unlink",input:"ce-inline-tool-input",inputShowed:"ce-inline-tool-input--showed"},this.nodes={button:null,input:null},this.inputOpened=!1,this.toolbar=e.toolbar,this.inlineToolbar=e.inlineToolbar,this.notifier=e.notifier,this.i18n=e.i18n,this.selection=new b}static get sanitize(){return{a:{href:!0,target:"_blank",rel:"nofollow"}}}render(){return this.nodes.button=document.createElement("button"),this.nodes.button.type="button",this.nodes.button.classList.add(this.CSS.button,this.CSS.buttonModifier),this.nodes.button.innerHTML=zo,this.nodes.button}renderActions(){return this.nodes.input=document.createElement("input"),this.nodes.input.placeholder=this.i18n.t("Add a link"),this.nodes.input.enterKeyHint="done",this.nodes.input.classList.add(this.CSS.input),this.nodes.input.addEventListener("keydown",e=>{e.keyCode===this.ENTER_KEY&&this.enterPressed(e)}),this.nodes.input}surround(e){if(e){this.inputOpened?(this.selection.restore(),this.selection.removeFakeBackground()):(this.selection.setFakeBackground(),this.selection.save());const t=this.selection.findParentTag("A");if(t){this.selection.expandToTag(t),this.unlink(),this.closeActions(),this.checkState(),this.toolbar.close();return}}this.toggleActions()}checkState(){const e=this.selection.findParentTag("A");if(e){this.nodes.button.innerHTML=Gi,this.nodes.button.classList.add(this.CSS.buttonUnlink),this.nodes.button.classList.add(this.CSS.buttonActive),this.openActions();const t=e.getAttribute("href");this.nodes.input.value=t!=="null"?t:"",this.selection.save()}else this.nodes.button.innerHTML=zo,this.nodes.button.classList.remove(this.CSS.buttonUnlink),this.nodes.button.classList.remove(this.CSS.buttonActive);return!!e}clear(){this.closeActions()}get shortcut(){return"CMD+K"}toggleActions(){this.inputOpened?this.closeActions(!1):this.openActions(!0)}openActions(e=!1){this.nodes.input.classList.add(this.CSS.inputShowed),e&&this.nodes.input.focus(),this.inputOpened=!0}closeActions(e=!0){if(this.selection.isFakeBackgroundEnabled){const t=new b;t.save(),this.selection.restore(),this.selection.removeFakeBackground(),t.restore()}this.nodes.input.classList.remove(this.CSS.inputShowed),this.nodes.input.value="",e&&this.selection.clearSaved(),this.inputOpened=!1}enterPressed(e){let t=this.nodes.input.value||"";if(!t.trim()){this.selection.restore(),this.unlink(),e.preventDefault(),this.closeActions();return}if(!this.validateURL(t)){this.notifier.show({message:"Pasted link is not valid.",style:"error"}),T("Incorrect Link pasted","warn",t);return}t=this.prepareLink(t),this.selection.restore(),this.selection.removeFakeBackground(),this.insertLink(t),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),this.selection.collapseToEnd(),this.inlineToolbar.close()}validateURL(e){return!/\s/.test(e)}prepareLink(e){return e=e.trim(),e=this.addProtocol(e),e}addProtocol(e){if(/^(\w+):(\/\/)?/.test(e))return e;const t=/^\/[^/\s]/.test(e),o=e.substring(0,1)==="#",i=/^\/\/[^/\s]/.test(e);return!t&&!o&&!i&&(e="http://"+e),e}insertLink(e){const t=this.selection.findParentTag("A");t&&this.selection.expandToTag(t),document.execCommand(this.commandLink,!1,e)}unlink(){document.execCommand(this.commandUnlink)}}mo.isInline=!0,mo.title="Link";class Pn{constructor({api:e}){this.i18nAPI=e.i18n,this.blocksAPI=e.blocks,this.selectionAPI=e.selection,this.toolsAPI=e.tools,this.caretAPI=e.caret}async render(){const e=b.get(),t=this.blocksAPI.getBlockByElement(e.anchorNode);if(t===void 0)return[];const o=this.toolsAPI.getBlockTools(),i=await Po(t,o);if(i.length===0)return[];const s=i.reduce((c,u)=>{var h;return(h=u.toolbox)==null||h.forEach(p=>{c.push({icon:p.icon,title:H.t(W.toolNames,p.title),name:u.name,closeOnActivate:!0,onActivate:async()=>{const g=await this.blocksAPI.convert(t.id,u.name,p.data);this.caretAPI.setToBlock(g,"end")}})}),c},[]),r=await t.getActiveToolboxEntry(),a=r!==void 0?r.icon:Uo,l=!ge();return{icon:a,name:"convert-to",hint:{title:this.i18nAPI.t("Convert to")},children:{searchable:l,items:s,onOpen:()=>{l&&(this.selectionAPI.setFakeBackground(),this.selectionAPI.save())},onClose:()=>{l&&(this.selectionAPI.restore(),this.selectionAPI.removeFakeBackground())}}}}}Pn.isInline=!0;class Nn{constructor({data:e,api:t}){this.CSS={wrapper:"ce-stub",info:"ce-stub__info",title:"ce-stub__title",subtitle:"ce-stub__subtitle"},this.api=t,this.title=e.title||this.api.i18n.t("Error"),this.subtitle=this.api.i18n.t("The block can not be displayed correctly."),this.savedData=e.savedData,this.wrapper=this.make()}render(){return this.wrapper}save(){return this.savedData}make(){const e=d.make("div",this.CSS.wrapper),t=Ji,o=d.make("div",this.CSS.info),i=d.make("div",this.CSS.title,{textContent:this.title}),s=d.make("div",this.CSS.subtitle,{textContent:this.subtitle});return e.innerHTML=t,o.appendChild(i),o.appendChild(s),e.appendChild(o),e}}Nn.isReadOnlySupported=!0;class ha extends xt{constructor(){super(...arguments),this.type=ce.Inline}get title(){return this.constructable[He.Title]}create(){return new this.constructable({api:this.api,config:this.settings})}get isReadOnlySupported(){return this.constructable[He.IsReadOnlySupported]??!1}}class pa extends xt{constructor(){super(...arguments),this.type=ce.Tune}create(e,t){return new this.constructable({api:this.api,config:this.settings,block:t,data:e})}}class F extends Map{get blockTools(){const e=Array.from(this.entries()).filter(([,t])=>t.isBlock());return new F(e)}get inlineTools(){const e=Array.from(this.entries()).filter(([,t])=>t.isInline());return new F(e)}get blockTunes(){const e=Array.from(this.entries()).filter(([,t])=>t.isTune());return new F(e)}get internalTools(){const e=Array.from(this.entries()).filter(([,t])=>t.isInternal);return new F(e)}get externalTools(){const e=Array.from(this.entries()).filter(([,t])=>!t.isInternal);return new F(e)}}var fa=Object.defineProperty,ga=Object.getOwnPropertyDescriptor,Rn=(n,e,t,o)=>{for(var i=o>1?void 0:o?ga(e,t):e,s=n.length-1,r;s>=0;s--)(r=n[s])&&(i=(o?r(e,t,i):r(i))||i);return o&&i&&fa(e,t,i),i};class bo extends xt{constructor(){super(...arguments),this.type=ce.Block,this.inlineTools=new F,this.tunes=new F}create(e,t,o){return new this.constructable({data:e,block:t,readOnly:o,api:this.api,config:this.settings})}get isReadOnlySupported(){return this.constructable[ve.IsReadOnlySupported]===!0}get isLineBreaksEnabled(){return this.constructable[ve.IsEnabledLineBreaks]}get toolbox(){const e=this.constructable[ve.Toolbox],t=this.config[je.Toolbox];if(!V(e)&&t!==!1)return t?Array.isArray(e)?Array.isArray(t)?t.map((o,i)=>{const s=e[i];return s?{...s,...o}:o}):[t]:Array.isArray(t)?t:[{...e,...t}]:Array.isArray(e)?e:[e]}get conversionConfig(){return this.constructable[ve.ConversionConfig]}get enabledInlineTools(){return this.config[je.EnabledInlineTools]||!1}get enabledBlockTunes(){return this.config[je.EnabledBlockTunes]}get pasteConfig(){return this.constructable[ve.PasteConfig]??{}}get sanitizeConfig(){const e=super.sanitizeConfig,t=this.baseSanitizeConfig;if(V(e))return t;const o={};for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)){const s=e[i];N(s)?o[i]=Object.assign({},t,s):o[i]=s}return o}get baseSanitizeConfig(){const e={};return Array.from(this.inlineTools.values()).forEach(t=>Object.assign(e,t.sanitizeConfig)),Array.from(this.tunes.values()).forEach(t=>Object.assign(e,t.sanitizeConfig)),e}}Rn([fe],bo.prototype,"sanitizeConfig",1),Rn([fe],bo.prototype,"baseSanitizeConfig",1);class ma{constructor(e,t,o){this.api=o,this.config=e,this.editorConfig=t}get(e){const{class:t,isInternal:o=!1,...i}=this.config[e],s=this.getConstructor(t),r=t[Et.IsTune];return new s({name:e,constructable:t,config:i,api:this.api.getMethodsForTool(e,r),isDefault:e===this.editorConfig.defaultBlock,defaultPlaceholder:this.editorConfig.placeholder,isInternal:o})}getConstructor(e){switch(!0){case e[He.IsInline]:return ha;case e[Et.IsTune]:return pa;default:return bo}}}class Dn{constructor({api:e}){this.CSS={animation:"wobble"},this.api=e}render(){return{icon:$i,title:this.api.i18n.t("Move down"),onActivate:()=>this.handleClick(),name:"move-down"}}handleClick(){const e=this.api.blocks.getCurrentBlockIndex(),t=this.api.blocks.getBlockByIndex(e+1);if(!t)throw new Error("Unable to move Block down since it is already the last");const o=t.holder,i=o.getBoundingClientRect();let s=Math.abs(window.innerHeight-o.offsetHeight);i.top<window.innerHeight&&(s=window.scrollY+o.offsetHeight),window.scrollTo(0,s),this.api.blocks.move(e+1),this.api.toolbar.toggleBlockSettings(!0)}}Dn.isTune=!0;class Fn{constructor({api:e}){this.api=e}render(){return{icon:Yi,title:this.api.i18n.t("Delete"),name:"delete",confirmation:{title:this.api.i18n.t("Click to delete"),onActivate:()=>this.handleClick()}}}handleClick(){this.api.blocks.delete()}}Fn.isTune=!0;class jn{constructor({api:e}){this.CSS={animation:"wobble"},this.api=e}render(){return{icon:Wi,title:this.api.i18n.t("Move up"),onActivate:()=>this.handleClick(),name:"move-up"}}handleClick(){const e=this.api.blocks.getCurrentBlockIndex(),t=this.api.blocks.getBlockByIndex(e),o=this.api.blocks.getBlockByIndex(e-1);if(e===0||!t||!o)throw new Error("Unable to move Block up since it is already the first");const i=t.holder,s=o.holder,r=i.getBoundingClientRect(),a=s.getBoundingClientRect();let l;a.top>0?l=Math.abs(r.top)-Math.abs(a.top):l=Math.abs(r.top)+a.height,window.scrollBy(0,-1*l),this.api.blocks.move(e-1),this.api.toolbar.toggleBlockSettings(!0)}}jn.isTune=!0;var ba=Object.defineProperty,va=Object.getOwnPropertyDescriptor,ka=(n,e,t,o)=>{for(var i=o>1?void 0:o?va(e,t):e,s=n.length-1,r;s>=0;s--)(r=n[s])&&(i=(o?r(e,t,i):r(i))||i);return o&&i&&ba(e,t,i),i};class Hn extends E{constructor(){super(...arguments),this.stubTool="stub",this.toolsAvailable=new F,this.toolsUnavailable=new F}get available(){return this.toolsAvailable}get unavailable(){return this.toolsUnavailable}get inlineTools(){return this.available.inlineTools}get blockTools(){return this.available.blockTools}get blockTunes(){return this.available.blockTunes}get defaultTool(){return this.blockTools.get(this.config.defaultBlock)}get internal(){return this.available.internalTools}async prepare(){if(this.validateTools(),this.config.tools=nt({},this.internalTools,this.config.tools),!Object.prototype.hasOwnProperty.call(this.config,"tools")||Object.keys(this.config.tools).length===0)throw Error("Can't start without tools");const e=this.prepareConfig();this.factory=new ma(e,this.config,this.Editor.API);const t=this.getListOfPrepareFunctions(e);if(t.length===0)return Promise.resolve();await Zn(t,o=>{this.toolPrepareMethodSuccess(o)},o=>{this.toolPrepareMethodFallback(o)}),this.prepareBlockTools()}getAllInlineToolsSanitizeConfig(){const e={};return Array.from(this.inlineTools.values()).forEach(t=>{Object.assign(e,t.sanitizeConfig)}),e}destroy(){Object.values(this.available).forEach(async e=>{M(e.reset)&&await e.reset()})}get internalTools(){return{convertTo:{class:Pn,isInternal:!0},link:{class:mo,isInternal:!0},bold:{class:fo,isInternal:!0},italic:{class:go,isInternal:!0},paragraph:{class:po,inlineToolbar:!0,isInternal:!0},stub:{class:Nn,isInternal:!0},moveUp:{class:jn,isInternal:!0},delete:{class:Fn,isInternal:!0},moveDown:{class:Dn,isInternal:!0}}}toolPrepareMethodSuccess(e){const t=this.factory.get(e.toolName);if(t.isInline()){const i=["render"].filter(s=>!t.create()[s]);if(i.length){T(`Incorrect Inline Tool: ${t.name}. Some of required methods is not implemented %o`,"warn",i),this.toolsUnavailable.set(t.name,t);return}}this.toolsAvailable.set(t.name,t)}toolPrepareMethodFallback(e){this.toolsUnavailable.set(e.toolName,this.factory.get(e.toolName))}getListOfPrepareFunctions(e){const t=[];return Object.entries(e).forEach(([o,i])=>{t.push({function:M(i.class.prepare)?i.class.prepare:()=>{},data:{toolName:o,config:i.config}})}),t}prepareBlockTools(){Array.from(this.blockTools.values()).forEach(e=>{this.assignInlineToolsToBlockTool(e),this.assignBlockTunesToBlockTool(e)})}assignInlineToolsToBlockTool(e){if(this.config.inlineToolbar!==!1){if(e.enabledInlineTools===!0){e.inlineTools=new F(Array.isArray(this.config.inlineToolbar)?this.config.inlineToolbar.map(t=>[t,this.inlineTools.get(t)]):Array.from(this.inlineTools.entries()));return}Array.isArray(e.enabledInlineTools)&&(e.inlineTools=new F(["convertTo",...e.enabledInlineTools].map(t=>[t,this.inlineTools.get(t)])))}}assignBlockTunesToBlockTool(e){if(e.enabledBlockTunes!==!1){if(Array.isArray(e.enabledBlockTunes)){const t=new F(e.enabledBlockTunes.map(o=>[o,this.blockTunes.get(o)]));e.tunes=new F([...t,...this.blockTunes.internalTools]);return}if(Array.isArray(this.config.tunes)){const t=new F(this.config.tunes.map(o=>[o,this.blockTunes.get(o)]));e.tunes=new F([...t,...this.blockTunes.internalTools]);return}e.tunes=this.blockTunes.internalTools}}validateTools(){for(const e in this.config.tools)if(Object.prototype.hasOwnProperty.call(this.config.tools,e)){if(e in this.internalTools)return;const t=this.config.tools[e];if(!M(t)&&!M(t.class))throw Error(`Tool «${e}» must be a constructor function or an object with function in the «class» property`)}}prepareConfig(){const e={};for(const t in this.config.tools)N(this.config.tools[t])?e[t]=this.config.tools[t]:e[t]={class:this.config.tools[t]};return e}}ka([fe],Hn.prototype,"getAllInlineToolsSanitizeConfig",1);const ya=`:root{--selectionColor: #e1f2ff;--inlineSelectionColor: #d4ecff;--bg-light: #eff2f5;--grayText: #707684;--color-dark: #1D202B;--color-active-icon: #388AE5;--color-gray-border: rgba(201, 201, 204, .48);--content-width: 650px;--narrow-mode-right-padding: 50px;--toolbox-buttons-size: 26px;--toolbox-buttons-size--mobile: 36px;--icon-size: 20px;--icon-size--mobile: 28px;--block-padding-vertical: .4em;--color-line-gray: #EFF0F1 }.codex-editor{position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;z-index:1}.codex-editor .hide{display:none}.codex-editor__redactor [contenteditable]:empty:after{content:"\\feff"}@media (min-width: 651px){.codex-editor--narrow .codex-editor__redactor{margin-right:50px}}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .codex-editor__redactor{margin-left:50px;margin-right:0}}@media (min-width: 651px){.codex-editor--narrow .ce-toolbar__actions{right:-5px}}.codex-editor-copyable{position:absolute;height:1px;width:1px;top:-400%;opacity:.001}.codex-editor-overlay{position:fixed;top:0;left:0;right:0;bottom:0;z-index:999;pointer-events:none;overflow:hidden}.codex-editor-overlay__container{position:relative;pointer-events:auto;z-index:0}.codex-editor-overlay__rectangle{position:absolute;pointer-events:none;background-color:#2eaadc33;border:1px solid transparent}.codex-editor svg{max-height:100%}.codex-editor path{stroke:currentColor}.codex-editor ::-moz-selection{background-color:#d4ecff}.codex-editor ::selection{background-color:#d4ecff}.codex-editor--toolbox-opened [contentEditable=true][data-placeholder]:focus:before{opacity:0!important}.ce-scroll-locked{overflow:hidden}.ce-scroll-locked--hard{overflow:hidden;top:calc(-1 * var(--window-scroll-offset));position:fixed;width:100%}.ce-toolbar{position:absolute;left:0;right:0;top:0;-webkit-transition:opacity .1s ease;transition:opacity .1s ease;will-change:opacity,top;display:none}.ce-toolbar--opened{display:block}.ce-toolbar__content{max-width:650px;margin:0 auto;position:relative}.ce-toolbar__plus{color:#1d202b;cursor:pointer;width:26px;height:26px;border-radius:7px;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-ms-flex-negative:0;flex-shrink:0}@media (max-width: 650px){.ce-toolbar__plus{width:36px;height:36px}}@media (hover: hover){.ce-toolbar__plus:hover{background-color:#eff2f5}}.ce-toolbar__plus--active{background-color:#eff2f5;-webkit-animation:bounceIn .75s 1;animation:bounceIn .75s 1;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards}.ce-toolbar__plus-shortcut{opacity:.6;word-spacing:-2px;margin-top:5px}@media (max-width: 650px){.ce-toolbar__plus{position:absolute;background-color:#fff;border:1px solid #E8E8EB;-webkit-box-shadow:0 3px 15px -3px rgba(13,20,33,.13);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;z-index:2;position:static}.ce-toolbar__plus--left-oriented:before{left:15px;margin-left:0}.ce-toolbar__plus--right-oriented:before{left:auto;right:15px;margin-left:0}}.ce-toolbar__actions{position:absolute;right:100%;opacity:0;display:-webkit-box;display:-ms-flexbox;display:flex;padding-right:5px}.ce-toolbar__actions--opened{opacity:1}@media (max-width: 650px){.ce-toolbar__actions{right:auto}}.ce-toolbar__settings-btn{color:#1d202b;width:26px;height:26px;border-radius:7px;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;margin-left:3px;cursor:pointer;user-select:none}@media (max-width: 650px){.ce-toolbar__settings-btn{width:36px;height:36px}}@media (hover: hover){.ce-toolbar__settings-btn:hover{background-color:#eff2f5}}.ce-toolbar__settings-btn--active{background-color:#eff2f5;-webkit-animation:bounceIn .75s 1;animation:bounceIn .75s 1;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards}@media (min-width: 651px){.ce-toolbar__settings-btn{width:24px}}.ce-toolbar__settings-btn--hidden{display:none}@media (max-width: 650px){.ce-toolbar__settings-btn{position:absolute;background-color:#fff;border:1px solid #E8E8EB;-webkit-box-shadow:0 3px 15px -3px rgba(13,20,33,.13);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;z-index:2;position:static}.ce-toolbar__settings-btn--left-oriented:before{left:15px;margin-left:0}.ce-toolbar__settings-btn--right-oriented:before{left:auto;right:15px;margin-left:0}}.ce-toolbar__plus svg,.ce-toolbar__settings-btn svg{width:24px;height:24px}@media (min-width: 651px){.codex-editor--narrow .ce-toolbar__plus{left:5px}}@media (min-width: 651px){.codex-editor--narrow .ce-toolbox .ce-popover{right:0;left:auto;left:initial}}.ce-inline-toolbar{--y-offset: 8px;--color-background-icon-active: rgba(56, 138, 229, .1);--color-text-icon-active: #388AE5;--color-text-primary: black;position:absolute;visibility:hidden;-webkit-transition:opacity .25s ease;transition:opacity .25s ease;will-change:opacity,left,top;top:0;left:0;z-index:3;opacity:1;visibility:visible}.ce-inline-toolbar [hidden]{display:none!important}.ce-inline-toolbar__toggler-and-button-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;width:100%;padding:0 6px}.ce-inline-toolbar__buttons{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-inline-toolbar__dropdown{display:-webkit-box;display:-ms-flexbox;display:flex;padding:6px;margin:0 6px 0 -6px;-webkit-box-align:center;-ms-flex-align:center;align-items:center;cursor:pointer;border-right:1px solid rgba(201,201,204,.48);-webkit-box-sizing:border-box;box-sizing:border-box}@media (hover: hover){.ce-inline-toolbar__dropdown:hover{background:#eff2f5}}.ce-inline-toolbar__dropdown--hidden{display:none}.ce-inline-toolbar__dropdown-content,.ce-inline-toolbar__dropdown-arrow{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-inline-toolbar__dropdown-content svg,.ce-inline-toolbar__dropdown-arrow svg{width:20px;height:20px}.ce-inline-toolbar__shortcut{opacity:.6;word-spacing:-3px;margin-top:3px}.ce-inline-tool{color:var(--color-text-primary);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border:0;border-radius:4px;line-height:normal;height:100%;padding:0;width:28px;background-color:transparent;cursor:pointer}@media (max-width: 650px){.ce-inline-tool{width:36px;height:36px}}@media (hover: hover){.ce-inline-tool:hover{background-color:#f8f8f8}}.ce-inline-tool svg{display:block;width:20px;height:20px}@media (max-width: 650px){.ce-inline-tool svg{width:28px;height:28px}}.ce-inline-tool--link .icon--unlink,.ce-inline-tool--unlink .icon--link{display:none}.ce-inline-tool--unlink .icon--unlink{display:inline-block;margin-bottom:-1px}.ce-inline-tool-input{background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:4px 8px;font-size:14px;line-height:22px;outline:none;margin:0;width:100%;-webkit-box-sizing:border-box;box-sizing:border-box;display:none;font-weight:500;-webkit-appearance:none;font-family:inherit}@media (max-width: 650px){.ce-inline-tool-input{font-size:15px;font-weight:500}}.ce-inline-tool-input::-webkit-input-placeholder{color:#707684}.ce-inline-tool-input::-moz-placeholder{color:#707684}.ce-inline-tool-input:-ms-input-placeholder{color:#707684}.ce-inline-tool-input::-ms-input-placeholder{color:#707684}.ce-inline-tool-input::placeholder{color:#707684}.ce-inline-tool-input--showed{display:block}.ce-inline-tool--active{background:var(--color-background-icon-active);color:var(--color-text-icon-active)}@-webkit-keyframes fade-in{0%{opacity:0}to{opacity:1}}@keyframes fade-in{0%{opacity:0}to{opacity:1}}.ce-block{-webkit-animation:fade-in .3s ease;animation:fade-in .3s ease;-webkit-animation-fill-mode:none;animation-fill-mode:none;-webkit-animation-fill-mode:initial;animation-fill-mode:initial}.ce-block:first-of-type{margin-top:0}.ce-block--selected .ce-block__content{background:#e1f2ff}.ce-block--selected .ce-block__content [contenteditable]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.ce-block--selected .ce-block__content img,.ce-block--selected .ce-block__content .ce-stub{opacity:.55}.ce-block--stretched .ce-block__content{max-width:none}.ce-block__content{position:relative;max-width:650px;margin:0 auto;-webkit-transition:background-color .15s ease;transition:background-color .15s ease}.ce-block--drop-target .ce-block__content:before{content:"";position:absolute;top:100%;left:-20px;margin-top:-1px;height:8px;width:8px;border:solid #388AE5;border-width:1px 1px 0 0;-webkit-transform-origin:right;transform-origin:right;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.ce-block--drop-target .ce-block__content:after{content:"";position:absolute;top:100%;height:1px;width:100%;color:#388ae5;background:repeating-linear-gradient(90deg,#388AE5,#388AE5 1px,#fff 1px,#fff 6px)}.ce-block a{cursor:pointer;-webkit-text-decoration:underline;text-decoration:underline}.ce-block b{font-weight:700}.ce-block i{font-style:italic}@-webkit-keyframes bounceIn{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}20%{-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}60%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes bounceIn{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}20%{-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}60%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@-webkit-keyframes selectionBounce{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}50%{-webkit-transform:scale3d(1.01,1.01,1.01);transform:scale3d(1.01,1.01,1.01)}70%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes selectionBounce{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}50%{-webkit-transform:scale3d(1.01,1.01,1.01);transform:scale3d(1.01,1.01,1.01)}70%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@-webkit-keyframes buttonClicked{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.95,.95,.95);transform:scale3d(.95,.95,.95)}60%{-webkit-transform:scale3d(1.02,1.02,1.02);transform:scale3d(1.02,1.02,1.02)}80%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes buttonClicked{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.95,.95,.95);transform:scale3d(.95,.95,.95)}60%{-webkit-transform:scale3d(1.02,1.02,1.02);transform:scale3d(1.02,1.02,1.02)}80%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}.cdx-block{padding:.4em 0}.cdx-block::-webkit-input-placeholder{line-height:normal!important}.cdx-input{border:1px solid rgba(201,201,204,.48);-webkit-box-shadow:inset 0 1px 2px 0 rgba(35,44,72,.06);box-shadow:inset 0 1px 2px #232c480f;border-radius:3px;padding:10px 12px;outline:none;width:100%;-webkit-box-sizing:border-box;box-sizing:border-box}.cdx-input[data-placeholder]:before{position:static!important}.cdx-input[data-placeholder]:before{display:inline-block;width:0;white-space:nowrap;pointer-events:none}.cdx-settings-button{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;border-radius:3px;cursor:pointer;border:0;outline:none;background-color:transparent;vertical-align:bottom;color:inherit;margin:0;min-width:26px;min-height:26px}.cdx-settings-button--focused{background:rgba(34,186,255,.08)!important}.cdx-settings-button--focused{-webkit-box-shadow:inset 0 0 0px 1px rgba(7,161,227,.08);box-shadow:inset 0 0 0 1px #07a1e314}.cdx-settings-button--focused-animated{-webkit-animation-name:buttonClicked;animation-name:buttonClicked;-webkit-animation-duration:.25s;animation-duration:.25s}.cdx-settings-button--active{color:#388ae5}.cdx-settings-button svg{width:auto;height:auto}@media (max-width: 650px){.cdx-settings-button svg{width:28px;height:28px}}@media (max-width: 650px){.cdx-settings-button{width:36px;height:36px;border-radius:8px}}@media (hover: hover){.cdx-settings-button:hover{background-color:#eff2f5}}.cdx-loader{position:relative;border:1px solid rgba(201,201,204,.48)}.cdx-loader:before{content:"";position:absolute;left:50%;top:50%;width:18px;height:18px;margin:-11px 0 0 -11px;border:2px solid rgba(201,201,204,.48);border-left-color:#388ae5;border-radius:50%;-webkit-animation:cdxRotation 1.2s infinite linear;animation:cdxRotation 1.2s infinite linear}@-webkit-keyframes cdxRotation{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes cdxRotation{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.cdx-button{padding:13px;border-radius:3px;border:1px solid rgba(201,201,204,.48);font-size:14.9px;background:#fff;-webkit-box-shadow:0 2px 2px 0 rgba(18,30,57,.04);box-shadow:0 2px 2px #121e390a;color:#707684;text-align:center;cursor:pointer}@media (hover: hover){.cdx-button:hover{background:#FBFCFE;-webkit-box-shadow:0 1px 3px 0 rgba(18,30,57,.08);box-shadow:0 1px 3px #121e3914}}.cdx-button svg{height:20px;margin-right:.2em;margin-top:-2px}.ce-stub{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:12px 18px;margin:10px 0;border-radius:10px;background:#eff2f5;border:1px solid #EFF0F1;color:#707684;font-size:14px}.ce-stub svg{width:20px;height:20px}.ce-stub__info{margin-left:14px}.ce-stub__title{font-weight:500;text-transform:capitalize}.codex-editor.codex-editor--rtl{direction:rtl}.codex-editor.codex-editor--rtl .cdx-list{padding-left:0;padding-right:40px}.codex-editor.codex-editor--rtl .ce-toolbar__plus{right:-26px;left:auto}.codex-editor.codex-editor--rtl .ce-toolbar__actions{right:auto;left:-26px}@media (max-width: 650px){.codex-editor.codex-editor--rtl .ce-toolbar__actions{margin-left:0;margin-right:auto;padding-right:0;padding-left:10px}}.codex-editor.codex-editor--rtl .ce-settings{left:5px;right:auto}.codex-editor.codex-editor--rtl .ce-settings:before{right:auto;left:25px}.codex-editor.codex-editor--rtl .ce-settings__button:not(:nth-child(3n+3)){margin-left:3px;margin-right:0}.codex-editor.codex-editor--rtl .ce-conversion-tool__icon{margin-right:0;margin-left:10px}.codex-editor.codex-editor--rtl .ce-inline-toolbar__dropdown{border-right:0px solid transparent;border-left:1px solid rgba(201,201,204,.48);margin:0 -6px 0 6px}.codex-editor.codex-editor--rtl .ce-inline-toolbar__dropdown .icon--toggler-down{margin-left:0;margin-right:4px}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .ce-toolbar__plus{left:0;right:5px}}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .ce-toolbar__actions{left:-5px}}.cdx-search-field{--icon-margin-right: 10px;background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:2px;display:grid;grid-template-columns:auto auto 1fr;grid-template-rows:auto}.cdx-search-field__icon{width:26px;height:26px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;margin-right:var(--icon-margin-right)}.cdx-search-field__icon svg{width:20px;height:20px;color:#707684}.cdx-search-field__input{font-size:14px;outline:none;font-weight:500;font-family:inherit;border:0;background:transparent;margin:0;padding:0;line-height:22px;min-width:calc(100% - 26px - var(--icon-margin-right))}.cdx-search-field__input::-webkit-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::-moz-placeholder{color:#707684;font-weight:500}.cdx-search-field__input:-ms-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::-ms-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::placeholder{color:#707684;font-weight:500}.ce-popover{--border-radius: 6px;--width: 200px;--max-height: 270px;--padding: 6px;--offset-from-target: 8px;--color-border: #EFF0F1;--color-shadow: rgba(13, 20, 33, .1);--color-background: white;--color-text-primary: black;--color-text-secondary: #707684;--color-border-icon: rgba(201, 201, 204, .48);--color-border-icon-disabled: #EFF0F1;--color-text-icon-active: #388AE5;--color-background-icon-active: rgba(56, 138, 229, .1);--color-background-item-focus: rgba(34, 186, 255, .08);--color-shadow-item-focus: rgba(7, 161, 227, .08);--color-background-item-hover: #F8F8F8;--color-background-item-confirm: #E24A4A;--color-background-item-confirm-hover: #CE4343;--popover-top: calc(100% + var(--offset-from-target));--popover-left: 0;--nested-popover-overlap: 4px;--icon-size: 20px;--item-padding: 3px;--item-height: calc(var(--icon-size) + 2 * var(--item-padding))}.ce-popover__container{min-width:var(--width);width:var(--width);max-height:var(--max-height);border-radius:var(--border-radius);overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-box-shadow:0px 3px 15px -3px var(--color-shadow);box-shadow:0 3px 15px -3px var(--color-shadow);position:absolute;left:var(--popover-left);top:var(--popover-top);background:var(--color-background);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;z-index:4;opacity:0;max-height:0;pointer-events:none;padding:0;border:none}.ce-popover--opened>.ce-popover__container{opacity:1;padding:var(--padding);max-height:var(--max-height);pointer-events:auto;-webkit-animation:panelShowing .1s ease;animation:panelShowing .1s ease;border:1px solid var(--color-border)}@media (max-width: 650px){.ce-popover--opened>.ce-popover__container{-webkit-animation:panelShowingMobile .25s ease;animation:panelShowingMobile .25s ease}}.ce-popover--open-top .ce-popover__container{--popover-top: calc(-1 * (var(--offset-from-target) + var(--popover-height)))}.ce-popover--open-left .ce-popover__container{--popover-left: calc(-1 * var(--width) + 100%)}.ce-popover__items{overflow-y:auto;-ms-scroll-chaining:none;overscroll-behavior:contain}@media (max-width: 650px){.ce-popover__overlay{position:fixed;top:0;bottom:0;left:0;right:0;background:#1D202B;z-index:3;opacity:.5;-webkit-transition:opacity .12s ease-in;transition:opacity .12s ease-in;will-change:opacity;visibility:visible}}.ce-popover__overlay--hidden{display:none}@media (max-width: 650px){.ce-popover .ce-popover__container{--offset: 5px;position:fixed;max-width:none;min-width:calc(100% - var(--offset) * 2);left:var(--offset);right:var(--offset);bottom:calc(var(--offset) + env(safe-area-inset-bottom));top:auto;border-radius:10px}}.ce-popover__search{margin-bottom:5px}.ce-popover__nothing-found-message{color:#707684;display:none;cursor:default;padding:3px;font-size:14px;line-height:20px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ce-popover__nothing-found-message--displayed{display:block}.ce-popover--nested .ce-popover__container{--popover-left: calc(var(--nesting-level) * (var(--width) - var(--nested-popover-overlap)));top:calc(var(--trigger-item-top) - var(--nested-popover-overlap));position:absolute}.ce-popover--open-top.ce-popover--nested .ce-popover__container{top:calc(var(--trigger-item-top) - var(--popover-height) + var(--item-height) + var(--offset-from-target) + var(--nested-popover-overlap))}.ce-popover--open-left .ce-popover--nested .ce-popover__container{--popover-left: calc(-1 * (var(--nesting-level) + 1) * var(--width) + 100%)}.ce-popover-item-separator{padding:4px 3px}.ce-popover-item-separator--hidden{display:none}.ce-popover-item-separator__line{height:1px;background:var(--color-border);width:100%}.ce-popover-item-html--hidden{display:none}.ce-popover-item{--border-radius: 6px;border-radius:var(--border-radius);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:var(--item-padding);color:var(--color-text-primary);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border:none;background:transparent}@media (max-width: 650px){.ce-popover-item{padding:4px}}.ce-popover-item:not(:last-of-type){margin-bottom:1px}.ce-popover-item__icon{width:26px;height:26px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.ce-popover-item__icon svg{width:20px;height:20px}@media (max-width: 650px){.ce-popover-item__icon{width:36px;height:36px;border-radius:8px}.ce-popover-item__icon svg{width:28px;height:28px}}.ce-popover-item__icon--tool{margin-right:4px}.ce-popover-item__title{font-size:14px;line-height:20px;font-weight:500;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;margin-right:auto}@media (max-width: 650px){.ce-popover-item__title{font-size:16px}}.ce-popover-item__secondary-title{color:var(--color-text-secondary);font-size:12px;white-space:nowrap;letter-spacing:-.1em;padding-right:5px;opacity:.6}@media (max-width: 650px){.ce-popover-item__secondary-title{display:none}}.ce-popover-item--active{background:var(--color-background-icon-active);color:var(--color-text-icon-active)}.ce-popover-item--disabled{color:var(--color-text-secondary);cursor:default;pointer-events:none}.ce-popover-item--focused:not(.ce-popover-item--no-focus){background:var(--color-background-item-focus)!important}.ce-popover-item--hidden{display:none}@media (hover: hover){.ce-popover-item:hover{cursor:pointer}.ce-popover-item:hover:not(.ce-popover-item--no-hover){background-color:var(--color-background-item-hover)}}.ce-popover-item--confirmation{background:var(--color-background-item-confirm)}.ce-popover-item--confirmation .ce-popover-item__title,.ce-popover-item--confirmation .ce-popover-item__icon{color:#fff}@media (hover: hover){.ce-popover-item--confirmation:not(.ce-popover-item--no-hover):hover{background:var(--color-background-item-confirm-hover)}}.ce-popover-item--confirmation:not(.ce-popover-item--no-focus).ce-popover-item--focused{background:var(--color-background-item-confirm-hover)!important}@-webkit-keyframes panelShowing{0%{opacity:0;-webkit-transform:translateY(-8px) scale(.9);transform:translateY(-8px) scale(.9)}70%{opacity:1;-webkit-transform:translateY(2px);transform:translateY(2px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes panelShowing{0%{opacity:0;-webkit-transform:translateY(-8px) scale(.9);transform:translateY(-8px) scale(.9)}70%{opacity:1;-webkit-transform:translateY(2px);transform:translateY(2px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes panelShowingMobile{0%{opacity:0;-webkit-transform:translateY(14px) scale(.98);transform:translateY(14px) scale(.98)}70%{opacity:1;-webkit-transform:translateY(-4px);transform:translateY(-4px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes panelShowingMobile{0%{opacity:0;-webkit-transform:translateY(14px) scale(.98);transform:translateY(14px) scale(.98)}70%{opacity:1;-webkit-transform:translateY(-4px);transform:translateY(-4px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}.wobble{-webkit-animation-name:wobble;animation-name:wobble;-webkit-animation-duration:.4s;animation-duration:.4s}@-webkit-keyframes wobble{0%{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}15%{-webkit-transform:translate3d(-9%,0,0);transform:translate3d(-9%,0,0)}30%{-webkit-transform:translate3d(9%,0,0);transform:translate3d(9%,0,0)}45%{-webkit-transform:translate3d(-4%,0,0);transform:translate3d(-4%,0,0)}60%{-webkit-transform:translate3d(4%,0,0);transform:translate3d(4%,0,0)}75%{-webkit-transform:translate3d(-1%,0,0);transform:translate3d(-1%,0,0)}to{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes wobble{0%{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}15%{-webkit-transform:translate3d(-9%,0,0);transform:translate3d(-9%,0,0)}30%{-webkit-transform:translate3d(9%,0,0);transform:translate3d(9%,0,0)}45%{-webkit-transform:translate3d(-4%,0,0);transform:translate3d(-4%,0,0)}60%{-webkit-transform:translate3d(4%,0,0);transform:translate3d(4%,0,0)}75%{-webkit-transform:translate3d(-1%,0,0);transform:translate3d(-1%,0,0)}to{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}.ce-popover-header{margin-bottom:8px;margin-top:4px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.ce-popover-header__text{font-size:18px;font-weight:600}.ce-popover-header__back-button{border:0;background:transparent;width:36px;height:36px;color:var(--color-text-primary)}.ce-popover-header__back-button svg{display:block;width:28px;height:28px}.ce-popover--inline{--height: 38px;--height-mobile: 46px;--container-padding: 4px;position:relative}.ce-popover--inline .ce-popover__custom-content{margin-bottom:0}.ce-popover--inline .ce-popover__items{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-popover--inline .ce-popover__container{-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;padding:var(--container-padding);height:var(--height);top:0;min-width:-webkit-max-content;min-width:-moz-max-content;min-width:max-content;width:-webkit-max-content;width:-moz-max-content;width:max-content;-webkit-animation:none;animation:none}@media (max-width: 650px){.ce-popover--inline .ce-popover__container{height:var(--height-mobile);position:absolute}}.ce-popover--inline .ce-popover-item-separator{padding:0 4px}.ce-popover--inline .ce-popover-item-separator__line{height:100%;width:1px}.ce-popover--inline .ce-popover-item{border-radius:4px;padding:4px}.ce-popover--inline .ce-popover-item__icon--tool{-webkit-box-shadow:none;box-shadow:none;background:transparent;margin-right:0}.ce-popover--inline .ce-popover-item__icon{width:auto;width:initial;height:auto;height:initial}.ce-popover--inline .ce-popover-item__icon svg{width:20px;height:20px}@media (max-width: 650px){.ce-popover--inline .ce-popover-item__icon svg{width:28px;height:28px}}.ce-popover--inline .ce-popover-item:not(:last-of-type){margin-bottom:0;margin-bottom:initial}.ce-popover--inline .ce-popover-item-html{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.ce-popover--inline .ce-popover-item__icon--chevron-right{-webkit-transform:rotate(90deg);transform:rotate(90deg)}.ce-popover--inline .ce-popover--nested-level-1 .ce-popover__container{--offset: 3px;left:0;top:calc(var(--height) + var(--offset))}@media (max-width: 650px){.ce-popover--inline .ce-popover--nested-level-1 .ce-popover__container{top:calc(var(--height-mobile) + var(--offset))}}.ce-popover--inline .ce-popover--nested .ce-popover__container{min-width:var(--width);width:var(--width);height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;padding:6px;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.ce-popover--inline .ce-popover--nested .ce-popover__items{display:block;width:100%}.ce-popover--inline .ce-popover--nested .ce-popover-item{border-radius:6px;padding:3px}@media (max-width: 650px){.ce-popover--inline .ce-popover--nested .ce-popover-item{padding:4px}}.ce-popover--inline .ce-popover--nested .ce-popover-item__icon--tool{margin-right:4px}.ce-popover--inline .ce-popover--nested .ce-popover-item__icon{width:26px;height:26px}.ce-popover--inline .ce-popover--nested .ce-popover-item-separator{padding:4px 3px}.ce-popover--inline .ce-popover--nested .ce-popover-item-separator__line{width:100%;height:1px}.codex-editor [data-placeholder]:empty:before,.codex-editor [data-placeholder][data-empty=true]:before{pointer-events:none;color:#707684;cursor:text;content:attr(data-placeholder)}.codex-editor [data-placeholder-active]:empty:before,.codex-editor [data-placeholder-active][data-empty=true]:before{pointer-events:none;color:#707684;cursor:text}.codex-editor [data-placeholder-active]:empty:focus:before,.codex-editor [data-placeholder-active][data-empty=true]:focus:before{content:attr(data-placeholder-active)}
`;class wa extends E{constructor(){super(...arguments),this.isMobile=!1,this.contentRectCache=null,this.resizeDebouncer=Bo(()=>{this.windowResize()},200),this.selectionChangeDebounced=Bo(()=>{this.selectionChanged()},na),this.documentTouchedListener=e=>{this.documentTouched(e)}}get CSS(){return{editorWrapper:"codex-editor",editorWrapperNarrow:"codex-editor--narrow",editorZone:"codex-editor__redactor",editorZoneHidden:"codex-editor__redactor--hidden",editorEmpty:"codex-editor--empty",editorRtlFix:"codex-editor--rtl"}}get contentRect(){if(this.contentRectCache!==null)return this.contentRectCache;const e=this.nodes.wrapper.querySelector(`.${R.CSS.content}`);return e?(this.contentRectCache=e.getBoundingClientRect(),this.contentRectCache):{width:650,left:0,right:0}}async prepare(){this.setIsMobile(),this.make(),this.loadStyles()}toggleReadOnly(e){e?this.unbindReadOnlySensitiveListeners():window.requestIdleCallback(()=>{this.bindReadOnlySensitiveListeners()},{timeout:2e3})}checkEmptiness(){const{BlockManager:e}=this.Editor;this.nodes.wrapper.classList.toggle(this.CSS.editorEmpty,e.isEditorEmpty)}get someToolbarOpened(){const{Toolbar:e,BlockSettings:t,InlineToolbar:o}=this.Editor;return!!(t.opened||o.opened||e.toolbox.opened)}get someFlipperButtonFocused(){return this.Editor.Toolbar.toolbox.hasFocus()?!0:Object.entries(this.Editor).filter(([e,t])=>t.flipper instanceof ae).some(([e,t])=>t.flipper.hasFocus())}destroy(){this.nodes.holder.innerHTML="",this.unbindReadOnlyInsensitiveListeners()}closeAllToolbars(){const{Toolbar:e,BlockSettings:t,InlineToolbar:o}=this.Editor;t.close(),o.close(),e.toolbox.close()}setIsMobile(){const e=window.innerWidth<Co;e!==this.isMobile&&this.eventsDispatcher.emit(xe,{isEnabled:this.isMobile}),this.isMobile=e}make(){this.nodes.holder=d.getHolder(this.config.holder),this.nodes.wrapper=d.make("div",[this.CSS.editorWrapper,...this.isRtl?[this.CSS.editorRtlFix]:[]]),this.nodes.redactor=d.make("div",this.CSS.editorZone),this.nodes.holder.offsetWidth<this.contentRect.width&&this.nodes.wrapper.classList.add(this.CSS.editorWrapperNarrow),this.nodes.redactor.style.paddingBottom=this.config.minHeight+"px",this.nodes.wrapper.appendChild(this.nodes.redactor),this.nodes.holder.appendChild(this.nodes.wrapper),this.bindReadOnlyInsensitiveListeners()}loadStyles(){const e="editor-js-styles";if(d.get(e))return;const t=d.make("style",null,{id:e,textContent:ya.toString()});this.config.style&&!V(this.config.style)&&this.config.style.nonce&&t.setAttribute("nonce",this.config.style.nonce),d.prepend(document.head,t)}bindReadOnlyInsensitiveListeners(){this.listeners.on(document,"selectionchange",this.selectionChangeDebounced),this.listeners.on(window,"resize",this.resizeDebouncer,{passive:!0}),this.listeners.on(this.nodes.redactor,"mousedown",this.documentTouchedListener,{capture:!0,passive:!0}),this.listeners.on(this.nodes.redactor,"touchstart",this.documentTouchedListener,{capture:!0,passive:!0})}unbindReadOnlyInsensitiveListeners(){this.listeners.off(document,"selectionchange",this.selectionChangeDebounced),this.listeners.off(window,"resize",this.resizeDebouncer),this.listeners.off(this.nodes.redactor,"mousedown",this.documentTouchedListener),this.listeners.off(this.nodes.redactor,"touchstart",this.documentTouchedListener)}bindReadOnlySensitiveListeners(){this.readOnlyMutableListeners.on(this.nodes.redactor,"click",e=>{this.redactorClicked(e)},!1),this.readOnlyMutableListeners.on(document,"keydown",e=>{this.documentKeydown(e)},!0),this.readOnlyMutableListeners.on(document,"mousedown",e=>{this.documentClicked(e)},!0),this.watchBlockHoveredEvents(),this.enableInputsEmptyMark()}watchBlockHoveredEvents(){let e;this.readOnlyMutableListeners.on(this.nodes.redactor,"mousemove",ot(t=>{const o=t.target.closest(".ce-block");this.Editor.BlockSelection.anyBlockSelected||o&&e!==o&&(e=o,this.eventsDispatcher.emit(Qo,{block:this.Editor.BlockManager.getBlockByChildNode(o)}))},20),{passive:!0})}unbindReadOnlySensitiveListeners(){this.readOnlyMutableListeners.clearAll()}windowResize(){this.contentRectCache=null,this.setIsMobile()}documentKeydown(e){switch(e.keyCode){case y.ENTER:this.enterPressed(e);break;case y.BACKSPACE:case y.DELETE:this.backspacePressed(e);break;case y.ESC:this.escapePressed(e);break;default:this.defaultBehaviour(e);break}}defaultBehaviour(e){const{currentBlock:t}=this.Editor.BlockManager,o=e.target.closest(`.${this.CSS.editorWrapper}`),i=e.altKey||e.ctrlKey||e.metaKey||e.shiftKey;if(t!==void 0&&o===null){this.Editor.BlockEvents.keydown(e);return}o||t&&i||(this.Editor.BlockManager.unsetCurrentBlock(),this.Editor.Toolbar.close())}backspacePressed(e){const{BlockManager:t,BlockSelection:o,Caret:i}=this.Editor;if(o.anyBlockSelected&&!b.isSelectionExists){const s=t.removeSelectedBlocks(),r=t.insertDefaultBlockAtIndex(s,!0);i.setToBlock(r,i.positions.START),o.clearSelection(e),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation()}}escapePressed(e){this.Editor.BlockSelection.clearSelection(e),this.Editor.Toolbar.toolbox.opened?(this.Editor.Toolbar.toolbox.close(),this.Editor.Caret.setToBlock(this.Editor.BlockManager.currentBlock,this.Editor.Caret.positions.END)):this.Editor.BlockSettings.opened?this.Editor.BlockSettings.close():this.Editor.InlineToolbar.opened?this.Editor.InlineToolbar.close():this.Editor.Toolbar.close()}enterPressed(e){const{BlockManager:t,BlockSelection:o}=this.Editor;if(this.someToolbarOpened)return;const i=t.currentBlockIndex>=0;if(o.anyBlockSelected&&!b.isSelectionExists){o.clearSelection(e),e.preventDefault(),e.stopImmediatePropagation(),e.stopPropagation();return}if(!this.someToolbarOpened&&i&&e.target.tagName==="BODY"){const s=this.Editor.BlockManager.insert();e.preventDefault(),this.Editor.Caret.setToBlock(s),this.Editor.Toolbar.moveAndOpen(s)}this.Editor.BlockSelection.clearSelection(e)}documentClicked(e){var a,l;if(!e.isTrusted)return;const t=e.target;this.nodes.holder.contains(t)||b.isAtEditor||(this.Editor.BlockManager.unsetCurrentBlock(),this.Editor.Toolbar.close());const i=(a=this.Editor.BlockSettings.nodes.wrapper)==null?void 0:a.contains(t),s=(l=this.Editor.Toolbar.nodes.settingsToggler)==null?void 0:l.contains(t),r=i||s;if(this.Editor.BlockSettings.opened&&!r){this.Editor.BlockSettings.close();const c=this.Editor.BlockManager.getBlockByChildNode(t);this.Editor.Toolbar.moveAndOpen(c)}this.Editor.BlockSelection.clearSelection(e)}documentTouched(e){let t=e.target;if(t===this.nodes.redactor){const o=e instanceof MouseEvent?e.clientX:e.touches[0].clientX,i=e instanceof MouseEvent?e.clientY:e.touches[0].clientY;t=document.elementFromPoint(o,i)}try{this.Editor.BlockManager.setCurrentBlockByChildNode(t)}catch{this.Editor.RectangleSelection.isRectActivated()||this.Editor.Caret.setToTheLastBlock()}this.Editor.ReadOnly.isEnabled||this.Editor.Toolbar.moveAndOpen()}redactorClicked(e){if(!b.isCollapsed)return;const t=e.target,o=e.metaKey||e.ctrlKey;if(d.isAnchor(t)&&o){e.stopImmediatePropagation(),e.stopPropagation();const i=t.getAttribute("href"),s=ei(i);oi(s);return}this.processBottomZoneClick(e)}processBottomZoneClick(e){const t=this.Editor.BlockManager.getBlockByIndex(-1),o=d.offset(t.holder).bottom,i=e.pageY,{BlockSelection:s}=this.Editor;if(e.target instanceof Element&&e.target.isEqualNode(this.nodes.redactor)&&!s.anyBlockSelected&&o<i){e.stopImmediatePropagation(),e.stopPropagation();const{BlockManager:a,Caret:l,Toolbar:c}=this.Editor;(!a.lastBlock.tool.isDefault||!a.lastBlock.isEmpty)&&a.insertAtEnd(),l.setToTheLastBlock(),c.moveAndOpen(a.lastBlock)}}selectionChanged(){const{CrossBlockSelection:e,BlockSelection:t}=this.Editor,o=b.anchorElement;if(e.isCrossBlockSelectionStarted&&t.anyBlockSelected&&b.get().removeAllRanges(),!o){b.range||this.Editor.InlineToolbar.close();return}const i=o.closest(`.${R.CSS.content}`);(i===null||i.closest(`.${b.CSS.editorWrapper}`)!==this.nodes.wrapper)&&(this.Editor.InlineToolbar.containsNode(o)||this.Editor.InlineToolbar.close(),!(o.dataset.inlineToolbar==="true"))||(this.Editor.BlockManager.currentBlock||this.Editor.BlockManager.setCurrentBlockByChildNode(o),this.Editor.InlineToolbar.tryToShow(!0))}enableInputsEmptyMark(){function e(t){const o=t.target;To(o)}this.readOnlyMutableListeners.on(this.nodes.wrapper,"input",e),this.readOnlyMutableListeners.on(this.nodes.wrapper,"focusin",e),this.readOnlyMutableListeners.on(this.nodes.wrapper,"focusout",e)}}const Ea={BlocksAPI:ci,CaretAPI:ui,EventsAPI:hi,I18nAPI:ct,API:pi,InlineToolbarAPI:fi,ListenersAPI:gi,NotifierAPI:ki,ReadOnlyAPI:yi,SanitizerAPI:Si,SaverAPI:Ii,SelectionAPI:Oi,ToolsAPI:_i,StylesAPI:Mi,ToolbarAPI:Ai,TooltipAPI:Di,UiAPI:Fi,BlockSettings:ds,Toolbar:vs,InlineToolbar:ks,BlockEvents:Zr,BlockManager:Qr,BlockSelection:ea,Caret:et,CrossBlockSelection:ta,DragNDrop:oa,ModificationsObserver:sa,Paste:ra,ReadOnly:aa,RectangleSelection:Ie,Renderer:la,Saver:ca,Tools:Hn,UI:wa};class xa{constructor(e){this.moduleInstances={},this.eventsDispatcher=new we;let t,o;this.isReady=new Promise((i,s)=>{t=i,o=s}),Promise.resolve().then(async()=>{this.configuration=e,this.validate(),this.init(),await this.start(),await this.render();const{BlockManager:i,Caret:s,UI:r,ModificationsObserver:a}=this.moduleInstances;r.checkEmptiness(),a.enable(),this.configuration.autofocus===!0&&this.configuration.readOnly!==!0&&s.setToBlock(i.blocks[0],s.positions.START),t()}).catch(i=>{T(`Editor.js is not ready because of ${i}`,"error"),o(i)})}set configuration(e){var o,i;N(e)?this.config={...e}:this.config={holder:e},st(!!this.config.holderId,"config.holderId","config.holder"),this.config.holderId&&!this.config.holder&&(this.config.holder=this.config.holderId,this.config.holderId=null),this.config.holder==null&&(this.config.holder="editorjs"),this.config.logLevel||(this.config.logLevel=ko.VERBOSE),Vn(this.config.logLevel),st(!!this.config.initialBlock,"config.initialBlock","config.defaultBlock"),this.config.defaultBlock=this.config.defaultBlock||this.config.initialBlock||"paragraph",this.config.minHeight=this.config.minHeight!==void 0?this.config.minHeight:300;const t={type:this.config.defaultBlock,data:{}};this.config.placeholder=this.config.placeholder||!1,this.config.sanitizer=this.config.sanitizer||{p:!0,b:!0,a:!0},this.config.hideToolbar=this.config.hideToolbar?this.config.hideToolbar:!1,this.config.tools=this.config.tools||{},this.config.i18n=this.config.i18n||{},this.config.data=this.config.data||{blocks:[]},this.config.onReady=this.config.onReady||(()=>{}),this.config.onChange=this.config.onChange||(()=>{}),this.config.inlineToolbar=this.config.inlineToolbar!==void 0?this.config.inlineToolbar:!0,(V(this.config.data)||!this.config.data.blocks||this.config.data.blocks.length===0)&&(this.config.data={blocks:[t]}),this.config.readOnly=this.config.readOnly||!1,(o=this.config.i18n)!=null&&o.messages&&H.setDictionary(this.config.i18n.messages),this.config.i18n.direction=((i=this.config.i18n)==null?void 0:i.direction)||"ltr"}get configuration(){return this.config}validate(){const{holderId:e,holder:t}=this.config;if(e&&t)throw Error("«holderId» and «holder» param can't assign at the same time.");if(Q(t)&&!d.get(t))throw Error(`element with ID «${t}» is missing. Pass correct holder's ID.`);if(t&&N(t)&&!d.isElement(t))throw Error("«holder» value must be an Element node")}init(){this.constructModules(),this.configureModules()}async start(){await["Tools","UI","BlockManager","Paste","BlockSelection","RectangleSelection","CrossBlockSelection","ReadOnly"].reduce((t,o)=>t.then(async()=>{try{await this.moduleInstances[o].prepare()}catch(i){if(i instanceof Oo)throw new Error(i.message);T(`Module ${o} was skipped because of %o`,"warn",i)}}),Promise.resolve())}render(){return this.moduleInstances.Renderer.render(this.config.data.blocks)}constructModules(){Object.entries(Ea).forEach(([e,t])=>{try{this.moduleInstances[e]=new t({config:this.configuration,eventsDispatcher:this.eventsDispatcher})}catch(o){T("[constructModules]",`Module ${e} skipped because`,"error",o)}})}configureModules(){for(const e in this.moduleInstances)Object.prototype.hasOwnProperty.call(this.moduleInstances,e)&&(this.moduleInstances[e].state=this.getModulesDiff(e))}getModulesDiff(e){const t={};for(const o in this.moduleInstances)o!==e&&(t[o]=this.moduleInstances[o]);return t}}/**
 * Editor.js
 *
 * @license Apache-2.0
 * @see Editor.js <https://editorjs.io>
 * <AUTHOR> Team <https://codex.so>
 */class Ba{static get version(){return"2.31.0-rc.7"}constructor(e){let t=()=>{};N(e)&&M(e.onReady)&&(t=e.onReady);const o=new xa(e);this.isReady=o.isReady.then(()=>{this.exportAPI(o),t()})}exportAPI(e){const t=["configuration"],o=()=>{Object.values(e.moduleInstances).forEach(s=>{M(s.destroy)&&s.destroy(),s.listeners.removeAll()}),Ri(),e=null;for(const s in this)Object.prototype.hasOwnProperty.call(this,s)&&delete this[s];Object.setPrototypeOf(this,null)};t.forEach(s=>{this[s]=e[s]}),this.destroy=o,Object.setPrototypeOf(this,e.moduleInstances.API.methods),delete this.exportAPI,Object.entries({blocks:{clear:"clear",render:"render"},caret:{focus:"focus"},events:{on:"on",off:"off",emit:"emit"},saver:{save:"save"}}).forEach(([s,r])=>{Object.entries(r).forEach(([a,l])=>{this[l]=e.moduleInstances.API.methods[s][a]})})}}return Ba});
