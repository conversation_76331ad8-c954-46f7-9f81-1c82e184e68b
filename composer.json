{"name": "adzeta/website", "description": "AdZeta AI-Powered E-commerce Growth Platform Website", "type": "project", "license": "proprietary", "require": {"php": ">=7.4", "twig/twig": "^3.0", "symfony/http-foundation": "^5.0|^6.0", "vlucas/phpdotenv": "^5.0", "monolog/monolog": "^2.0|^3.0"}, "require-dev": {"phpunit/phpunit": "^9.0|^10.0", "symfony/var-dumper": "^5.0|^6.0"}, "autoload": {"psr-4": {"AdZeta\\": "src/", "AdZeta\\Admin\\": "admin/src/", "AdZetaAdmin\\": "adzeta-admin/src/"}, "files": ["includes/helpers.php"]}, "autoload-dev": {"psr-4": {"AdZeta\\Tests\\": "tests/"}}, "scripts": {"post-install-cmd": ["php -r \"if (!file_exists('.env')) { copy('.env.example', '.env'); }\""], "test": "phpunit", "serve": "php -S localhost:8000 -t public"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}