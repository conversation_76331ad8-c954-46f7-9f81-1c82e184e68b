/**
 * AdZeta AI Assistant Module
 * Global AI-powered content generation and optimization
 */

window.AdZetaAI = {
    // State management
    state: {
        isEnabled: false,
        isLoading: false,
        apiKeys: [],
        settings: {},
        currentRequest: null
    },

    // Initialize AI Assistant
    init() {
        console.log('AI Assistant module initialized');

        // Only load settings if user is authenticated
        if (this.isUserAuthenticated()) {
            this.loadSettings();
        } else {
            console.log('User not authenticated, skipping AI settings load');
        }

        this.bindGlobalEvents();
    },

    // Load AI settings
    async loadSettings() {
        // Double-check authentication before making API call
        if (!this.isUserAuthenticated()) {
            console.log('User not authenticated, cannot load AI settings');
            return;
        }

        console.log('AI: Loading settings from API...');

        try {
            const response = await window.AdZetaApp.apiRequest('/ai/settings');
            console.log('AI: Settings API response:', response);

            if (response.success) {
                this.state.settings = response.settings;
                this.state.apiKeys = response.settings.api_keys || [];
                this.state.isEnabled = this.state.apiKeys.length > 0;
                console.log('AI settings loaded successfully:', {
                    apiKeysCount: this.state.apiKeys.length,
                    isEnabled: this.state.isEnabled,
                    settings: this.state.settings
                });
                console.log('AI Assistant state after loading:', this.state);

                // Show a success notification to confirm AI is enabled
                if (this.state.isEnabled) {
                    console.log('✅ AI Assistant is now ENABLED with', this.state.apiKeys.length, 'API keys');
                } else {
                    console.log('❌ AI Assistant is DISABLED - no API keys found');
                }
            } else {
                console.error('AI settings API returned error:', response.error);
                if (this.isUserAuthenticated()) {
                    window.AdZetaApp.showNotification('Failed to load AI settings: ' + (response.error || 'Unknown error'), 'warning');
                }
            }
        } catch (error) {
            console.error('Failed to load AI settings:', error);
            // Only show notification if user is authenticated (to avoid login page errors)
            if (this.isUserAuthenticated()) {
                window.AdZetaApp.showNotification('Failed to load AI settings: ' + error.message, 'warning');
            }
        }
    },

    // Bind global events
    bindGlobalEvents() {
        // Listen for AI button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-ai-action]')) {
                e.preventDefault();
                this.handleAIAction(e.target);
            }
        });

        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'A') {
                e.preventDefault();
                this.showAIAssistant();
            }
        });
    },

    // Handle AI action buttons
    async handleAIAction(button) {
        console.log('🤖 AI Action triggered:', button.dataset.aiAction);
        console.log('🤖 Current AI state:', {
            isEnabled: this.state.isEnabled,
            apiKeysCount: this.state.apiKeys.length,
            isAuthenticated: this.isUserAuthenticated()
        });

        // Check authentication first
        if (!this.isUserAuthenticated()) {
            console.log('❌ User not authenticated, AI actions disabled');
            return;
        }

        if (!this.state.isEnabled) {
            console.log('❌ AI Assistant is disabled. State:', this.state);
            window.AdZetaApp.showNotification('AI Assistant is not configured. Please add API keys in settings.', 'warning');
            return;
        }

        console.log('✅ AI Assistant is enabled, proceeding with action:', button.dataset.aiAction);

        const action = button.dataset.aiAction;
        const target = button.dataset.aiTarget;

        this.showLoading(button);

        try {
            switch (action) {
                case 'generate-title':
                    await this.generateTitles(target);
                    break;
                case 'generate-meta-title':
                    await this.generateMetaTitle(target);
                    break;
                case 'generate-meta':
                    await this.generateMetaDescription(target);
                    break;
                case 'generate-tags':
                    await this.generateTags(target);
                    break;
                case 'suggest-keywords':
                    await this.suggestKeywords(target);
                    break;
                case 'analyze-seo':
                    await this.analyzeSEO(target);
                    break;
                case 'generate-all':
                    await this.generateAllSEO(target);
                    break;
                case 'improve-content':
                    await this.improveContent(target);
                    break;
                default:
                    console.warn('Unknown AI action:', action);
            }
        } catch (error) {
            console.error('AI action failed:', error);
            window.AdZetaApp.showNotification('AI request failed: ' + error.message, 'danger');
        } finally {
            this.hideLoading(button);
        }
    },

    // Generate title suggestions
    async generateTitles(target) {
        const topic = await this.getTopicFromContext(target);
        if (!topic) {
            window.AdZetaApp.showNotification('Please enter a topic or some content first', 'warning');
            return;
        }

        const keywords = this.getKeywordsFromContext(target);

        const response = await window.AdZetaApp.apiRequest('/ai/generate-titles', {
            method: 'POST',
            body: JSON.stringify({
                topic: topic,
                keywords: keywords,
                count: 5
            })
        });

        if (response.success) {
            this.showTitleSuggestions(response.titles, target);
        } else {
            throw new Error(response.error || 'Failed to generate titles');
        }
    },

    // Generate meta description
    async generateMetaDescription(target) {
        const title = this.getTitleFromContext(target);
        const content = await this.getContentFromContext(target);

        console.log('Meta generation - Title:', title);
        console.log('Meta generation - Content length:', content ? content.length : 0);

        if (!title || !content) {
            window.AdZetaApp.showNotification('Please enter a title and content first', 'warning');
            return;
        }

        const keywords = this.getKeywordsFromContext(target);

        console.log('Sending meta description request with:', {
            title: title,
            content: content.substring(0, 100) + '...',
            keywords: keywords
        });

        const response = await window.AdZetaApp.apiRequest('/ai/generate-meta-description', {
            method: 'POST',
            body: JSON.stringify({
                title: title,
                content: content,
                keywords: keywords
            })
        });

        if (response.success) {
            this.applyMetaDescription(response.meta_description, target);
            window.AdZetaApp.showNotification('Meta description generated successfully!', 'success');
        } else {
            throw new Error(response.error || 'Failed to generate meta description');
        }
    },

    // Generate content tags
    async generateTags(target) {
        const title = this.getTitleFromContext(target);
        const content = await this.getContentFromContext(target);

        if (!title || !content) {
            window.AdZetaApp.showNotification('Please enter a title and content first', 'warning');
            return;
        }

        const response = await window.AdZetaApp.apiRequest('/ai/generate-tags', {
            method: 'POST',
            body: JSON.stringify({
                title: title,
                content: content,
                maxTags: 10
            })
        });

        if (response.success) {
            this.showTagSuggestions(response.tags, target);
        } else {
            throw new Error(response.error || 'Failed to generate tags');
        }
    },

    // Analyze SEO - AI-powered analysis using Google Gemini
    async analyzeSEO(target) {
        try {
            // Show analyzing state
            const seoScoreBadge = document.getElementById('seoScore');
            if (seoScoreBadge) {
                seoScoreBadge.textContent = 'Analyzing...';
                seoScoreBadge.className = 'badge ms-2 badge-secondary';
            }

            // Get real form data
            const seoData = await this.collectRealSEOData(target);
            console.log('🔍 Collected SEO data for AI analysis:', seoData);

            // Use AI for comprehensive SEO analysis
            const aiAnalysis = await this.performAISEOAnalysis(seoData);

            // Fallback to local analysis if AI fails
            const analysis = aiAnalysis || this.performLocalSEOAnalysis(seoData);

            // Show the analysis
            this.showSEOAnalysis(analysis, target);

        } catch (error) {
            console.error('SEO analysis failed:', error);

            // Fallback to local analysis
            const seoData = await this.collectRealSEOData(target);
            const analysis = this.performLocalSEOAnalysis(seoData);
            this.showSEOAnalysis(analysis, target);
        }
    },

    // Collect real SEO data from form fields
    async collectRealSEOData(target) {
        const data = {
            // Basic content
            title: this.getTitleFromContext(target),
            content: await this.getContentFromContext(target),

            // SEO fields
            meta_title: this.getMetaTitleFromContext(target),
            meta_description: this.getMetaDescriptionFromContext(target),
            meta_keywords: this.getMetaKeywordsFromContext(target),
            focus_keyword: this.getFocusKeywordFromContext(target),

            // Additional data
            slug: this.getSlugFromContext(target),
            excerpt: this.getExcerptFromContext(target)
        };

        console.log('🔍 Collected SEO Data:', data);
        return data;
    },

    // Get meta title from form
    getMetaTitleFromContext(target) {
        const metaTitleField = document.querySelector('#metaTitle, [name="meta_title"], .meta-title-input');
        return metaTitleField ? metaTitleField.value.trim() : '';
    },

    // Get meta keywords from form
    getMetaKeywordsFromContext(target) {
        const metaKeywordsField = document.querySelector('#metaKeywords, [name="meta_keywords"], .meta-keywords-input');
        return metaKeywordsField ? metaKeywordsField.value.trim() : '';
    },

    // Get slug from form
    getSlugFromContext(target) {
        const slugField = document.querySelector('#postSlug, [name="slug"], .post-slug-input');
        return slugField ? slugField.value.trim() : '';
    },

    // Get excerpt from form
    getExcerptFromContext(target) {
        const excerptField = document.querySelector('#excerpt, [name="excerpt"], .excerpt-input');
        return excerptField ? excerptField.value.trim() : '';
    },

    // Perform AI-powered SEO analysis using Google Gemini
    async performAISEOAnalysis(data) {
        try {
            console.log('🤖 Starting AI-powered SEO analysis...');

            const prompt = `Analyze this blog post content for SEO optimization and provide a comprehensive analysis:

**Post Details:**
- Title: "${data.title || 'No title'}"
- Meta Title: "${data.meta_title || 'Not set'}"
- Meta Description: "${data.meta_description || 'Not set'}"
- Focus Keyword: "${data.focus_keyword || 'Not set'}"
- Content: ${data.content ? data.content.substring(0, 2000) + '...' : 'No content'}
- URL Slug: "${data.slug || 'Not set'}"

**Analysis Requirements:**
Provide a detailed SEO analysis with:

1. **Overall SEO Score** (0-100)
2. **Individual Metric Scores:**
   - Meta Title (0-100): Length, keyword usage, click-worthiness
   - Meta Description (0-100): Length, keyword usage, call-to-action
   - Focus Keyword (0-100): Density, placement, relevance
   - Content Quality (0-100): Structure, readability, depth
   - Technical SEO (0-100): URL structure, keyword optimization

3. **Top 3 Priority Issues** with specific actionable fixes
4. **Detailed Recommendations** for each metric

**Response Format (JSON):**
\`\`\`json
{
  "overall_score": 75,
  "metrics": [
    {
      "name": "Meta Title",
      "icon": "🏷️",
      "score": 85,
      "status": "good",
      "statusText": "Good",
      "value": "53 chars - Well optimized",
      "tip": "Consider including focus keyword at the beginning"
    }
  ],
  "quickWins": [
    {
      "icon": "🏷️",
      "title": "Optimize Meta Title",
      "description": "Include focus keyword at the beginning for better ranking",
      "priority": "high",
      "action": "AdZetaAI.generateMetaTitle(\"post-editor\")",
      "actionText": "Generate"
    }
  ],
  "recommendations": [
    "Include focus keyword in meta title",
    "Add more internal links",
    "Improve content structure with more headings"
  ]
}
\`\`\`

**Focus on:**
- Content relevance and user intent
- Keyword optimization without stuffing
- Technical SEO best practices
- User experience factors
- Search engine ranking factors

Provide specific, actionable insights based on current SEO best practices.`;

            const response = await window.AdZetaApp.apiRequest('/ai/generate-content', {
                method: 'POST',
                body: JSON.stringify({
                    prompt: prompt,
                    temperature: 0.3, // Lower temperature for more consistent analysis
                    maxOutputTokens: 2000
                })
            });

            if (response.success && response.content) {
                try {
                    // Extract JSON from response
                    const jsonMatch = response.content.match(/```json\s*([\s\S]*?)\s*```/);
                    if (jsonMatch) {
                        const analysisData = JSON.parse(jsonMatch[1]);
                        console.log('✅ AI SEO analysis successful:', analysisData);
                        return {
                            score: analysisData.overall_score || 65,
                            metrics: analysisData.metrics || [],
                            quickWins: analysisData.quickWins || [],
                            recommendations: analysisData.recommendations || [],
                            isAIGenerated: true
                        };
                    } else {
                        // Try to parse the entire response as JSON
                        const analysisData = JSON.parse(response.content);
                        console.log('✅ AI SEO analysis successful (direct JSON):', analysisData);
                        return {
                            score: analysisData.overall_score || 65,
                            metrics: analysisData.metrics || [],
                            quickWins: analysisData.quickWins || [],
                            recommendations: analysisData.recommendations || [],
                            isAIGenerated: true
                        };
                    }
                } catch (parseError) {
                    console.warn('⚠️ Failed to parse AI analysis JSON:', parseError);
                    // Fallback: parse text response for insights
                    return this.parseAITextResponse(response.content, data);
                }
            } else {
                console.warn('⚠️ AI SEO analysis failed:', response.error);
                return null;
            }

        } catch (error) {
            console.error('❌ AI SEO analysis error:', error);
            return null;
        }
    },

    // Parse AI text response when JSON parsing fails
    parseAITextResponse(content, data) {
        console.log('📝 Parsing AI text response for SEO insights...');

        // Extract score from text
        const scoreMatch = content.match(/(?:score|rating).*?(\d{1,3})/i);
        const score = scoreMatch ? parseInt(scoreMatch[1]) : 65;

        // Extract recommendations
        const recommendations = [];
        const lines = content.split('\n');
        lines.forEach(line => {
            if (line.includes('recommend') || line.includes('improve') || line.includes('optimize')) {
                const clean = line.replace(/^[\d\-\*\s]+/, '').trim();
                if (clean.length > 10) {
                    recommendations.push(clean);
                }
            }
        });

        // Generate basic metrics based on current data
        const metrics = this.generateAIBasedMetrics(data, content);

        // Generate quick wins from recommendations
        const quickWins = recommendations.slice(0, 3).map((rec, index) => ({
            icon: ['🏷️', '📝', '🎯'][index] || '💡',
            title: `Improve ${['Meta Tags', 'Content', 'Keywords'][index] || 'SEO'}`,
            description: rec,
            priority: 'medium',
            action: null,
            actionText: 'Review'
        }));

        return {
            score: Math.min(Math.max(score, 0), 100),
            metrics: metrics,
            quickWins: quickWins,
            recommendations: recommendations.slice(0, 5),
            isAIGenerated: true,
            isParsedText: true
        };
    },

    // Generate AI-based metrics when JSON parsing fails
    generateAIBasedMetrics(data, aiContent) {
        const metrics = [];

        // Meta Title Analysis
        const metaTitle = data.meta_title || data.title || '';
        const titleScore = this.calculateMetricScore(metaTitle, 'title', aiContent);
        metrics.push({
            name: 'Meta Title',
            icon: '🏷️',
            score: titleScore,
            status: this.getStatusFromScore(titleScore),
            statusText: this.getStatusText(titleScore),
            value: `${metaTitle.length} chars - ${this.getStatusText(titleScore)}`,
            tip: this.getAITip('title', aiContent) || 'Optimize title for better SEO'
        });

        // Meta Description Analysis
        const metaDesc = data.meta_description || '';
        const descScore = this.calculateMetricScore(metaDesc, 'description', aiContent);
        metrics.push({
            name: 'Meta Description',
            icon: '📝',
            score: descScore,
            status: this.getStatusFromScore(descScore),
            statusText: this.getStatusText(descScore),
            value: `${metaDesc.length} chars - ${this.getStatusText(descScore)}`,
            tip: this.getAITip('description', aiContent) || 'Improve meta description'
        });

        // Focus Keyword Analysis
        const keyword = data.focus_keyword || '';
        const keywordScore = this.calculateMetricScore(keyword, 'keyword', aiContent);
        metrics.push({
            name: 'Focus Keyword',
            icon: '🎯',
            score: keywordScore,
            status: this.getStatusFromScore(keywordScore),
            statusText: this.getStatusText(keywordScore),
            value: keyword ? `"${keyword}" - ${this.getStatusText(keywordScore)}` : 'Not set',
            tip: this.getAITip('keyword', aiContent) || 'Set and optimize focus keyword'
        });

        return metrics;
    },

    // Calculate metric score based on AI content analysis
    calculateMetricScore(value, type, aiContent) {
        if (!value) return 0;

        let score = 50; // Base score

        // Check if AI content mentions this metric positively
        const lowerContent = aiContent.toLowerCase();
        const lowerValue = value.toLowerCase();

        if (type === 'title') {
            if (value.length >= 30 && value.length <= 60) score += 20;
            if (lowerContent.includes('title') && lowerContent.includes('good')) score += 15;
            if (lowerContent.includes('title') && lowerContent.includes('optimize')) score -= 10;
        } else if (type === 'description') {
            if (value.length >= 120 && value.length <= 160) score += 20;
            if (lowerContent.includes('description') && lowerContent.includes('good')) score += 15;
            if (lowerContent.includes('description') && lowerContent.includes('improve')) score -= 10;
        } else if (type === 'keyword') {
            if (lowerContent.includes('keyword') && lowerContent.includes('good')) score += 20;
            if (lowerContent.includes('keyword') && lowerContent.includes('density')) score += 10;
            if (lowerContent.includes('keyword') && lowerContent.includes('missing')) score -= 20;
        }

        return Math.min(Math.max(score, 0), 100);
    },

    // Get status from score
    getStatusFromScore(score) {
        if (score >= 80) return 'excellent';
        if (score >= 60) return 'good';
        if (score >= 40) return 'fair';
        return 'poor';
    },

    // Get status text from score
    getStatusText(score) {
        if (score >= 80) return 'Excellent';
        if (score >= 60) return 'Good';
        if (score >= 40) return 'Needs Work';
        return 'Poor';
    },

    // Extract AI tip for specific metric
    getAITip(type, aiContent) {
        const lines = aiContent.split('\n');
        for (const line of lines) {
            const lowerLine = line.toLowerCase();
            if (lowerLine.includes(type) && (lowerLine.includes('tip') || lowerLine.includes('suggest') || lowerLine.includes('recommend'))) {
                return line.replace(/^[\d\-\*\s]+/, '').trim();
            }
        }
        return null;
    },

    // Perform local SEO analysis using Google standards
    performLocalSEOAnalysis(data) {
        let totalScore = 0;
        let maxScore = 0;
        const quickWins = [];
        const metrics = [];

        // 1. Meta Title Analysis (Weight: 25%)
        const titleAnalysis = this.analyzeTitleSEO(data);
        totalScore += titleAnalysis.score * 0.25;
        maxScore += 25;
        metrics.push(titleAnalysis.metric);
        if (titleAnalysis.quickWin) quickWins.push(titleAnalysis.quickWin);

        // 2. Meta Description Analysis (Weight: 20%)
        const descAnalysis = this.analyzeDescriptionSEO(data);
        totalScore += descAnalysis.score * 0.20;
        maxScore += 20;
        metrics.push(descAnalysis.metric);
        if (descAnalysis.quickWin) quickWins.push(descAnalysis.quickWin);

        // 3. Focus Keyword Analysis (Weight: 20%)
        const keywordAnalysis = this.analyzeKeywordSEO(data);
        totalScore += keywordAnalysis.score * 0.20;
        maxScore += 20;
        metrics.push(keywordAnalysis.metric);
        if (keywordAnalysis.quickWin) quickWins.push(keywordAnalysis.quickWin);

        // 4. Content Analysis (Weight: 20%)
        const contentAnalysis = this.analyzeContentSEO(data);
        totalScore += contentAnalysis.score * 0.20;
        maxScore += 20;
        metrics.push(contentAnalysis.metric);
        if (contentAnalysis.quickWin) quickWins.push(contentAnalysis.quickWin);

        // 5. Technical SEO (Weight: 15%)
        const technicalAnalysis = this.analyzeTechnicalSEO(data);
        totalScore += technicalAnalysis.score * 0.15;
        maxScore += 15;
        metrics.push(technicalAnalysis.metric);
        if (technicalAnalysis.quickWin) quickWins.push(technicalAnalysis.quickWin);

        const finalScore = Math.round((totalScore / maxScore) * 100);

        return {
            score: finalScore,
            quickWins: quickWins.slice(0, 3), // Top 3 priority issues
            metrics: metrics,
            raw_content: null // We're doing local analysis
        };
    },

    // Analyze Meta Title SEO (Google Standards)
    analyzeTitleSEO(data) {
        const title = data.title || '';
        const metaTitle = data.meta_title || title;
        const focusKeyword = data.focus_keyword || '';

        let score = 0;
        let issues = [];

        // Check if meta title exists
        if (!metaTitle) {
            return {
                score: 0,
                metric: {
                    icon: '🏷️',
                    name: 'Meta Title',
                    value: 'Missing',
                    status: 'poor',
                    statusText: 'Missing',
                    tip: 'Add a meta title for better search visibility'
                },
                quickWin: {
                    icon: '🏷️',
                    title: 'Add Meta Title',
                    description: 'Meta title is missing. This is critical for SEO.',
                    priority: 'high',
                    action: 'AdZetaAI.generateMetaTitle("post-editor")',
                    actionText: 'Generate'
                }
            };
        }

        // Length analysis (Google: 50-60 characters optimal)
        const length = metaTitle.length;
        if (length >= 50 && length <= 60) {
            score += 40; // Perfect length
        } else if (length >= 30 && length < 50) {
            score += 25; // Too short but acceptable
            issues.push('too short');
        } else if (length > 60 && length <= 70) {
            score += 25; // Too long but acceptable
            issues.push('too long');
        } else {
            score += 10; // Very poor length
            issues.push(length < 30 ? 'way too short' : 'way too long');
        }

        // Focus keyword inclusion
        if (focusKeyword && metaTitle.toLowerCase().includes(focusKeyword.toLowerCase())) {
            score += 40; // Keyword included
        } else if (focusKeyword) {
            score += 0; // Keyword missing
            issues.push('missing focus keyword');
        } else {
            score += 20; // No focus keyword set, so partial credit
        }

        // Uniqueness (different from main title)
        if (metaTitle !== title && metaTitle.length > 0) {
            score += 20; // Good - optimized separately
        } else if (metaTitle === title) {
            score += 10; // Using main title, not optimized
            issues.push('same as main title');
        }

        // Determine status
        let status, statusText, value;
        if (score >= 80) {
            status = 'excellent';
            statusText = 'Excellent';
            value = `${length} chars - Optimized`;
        } else if (score >= 60) {
            status = 'good';
            statusText = 'Good';
            value = `${length} chars - Good`;
        } else if (score >= 40) {
            status = 'fair';
            statusText = 'Needs Work';
            value = `${length} chars - Issues`;
        } else {
            status = 'poor';
            statusText = 'Poor';
            value = `${length} chars - Poor`;
        }

        const result = {
            score: Math.min(score, 100),
            metric: {
                icon: '🏷️',
                name: 'Meta Title',
                value: value,
                status: status,
                statusText: statusText,
                tip: this.getMetaTitleTip(length, issues)
            }
        };

        // Add quick win if score is low
        if (score < 60) {
            result.quickWin = {
                icon: '🏷️',
                title: 'Optimize Meta Title',
                description: `Issues: ${issues.join(', ')}. Optimal length: 50-60 characters.`,
                priority: score < 40 ? 'high' : 'medium',
                action: 'AdZetaAI.generateMetaTitle("post-editor")',
                actionText: 'Optimize'
            };
        }

        return result;
    },

    // Get meta title tip based on issues
    getMetaTitleTip(length, issues) {
        if (length === 0) return 'Add a meta title (50-60 characters optimal)';
        if (length < 30) return 'Too short - aim for 50-60 characters';
        if (length > 70) return 'Too long - Google may truncate it';
        if (issues.includes('missing focus keyword')) return 'Include your focus keyword naturally';
        if (issues.includes('same as main title')) return 'Create a unique, SEO-optimized version';
        return 'Looks good! Keep it compelling and keyword-focused';
    },

    // Analyze Meta Description SEO
    analyzeDescriptionSEO(data) {
        const metaDescription = data.meta_description || '';
        const focusKeyword = data.focus_keyword || '';

        let score = 0;
        let issues = [];

        // Check if meta description exists
        if (!metaDescription) {
            return {
                score: 0,
                metric: {
                    icon: '📝',
                    name: 'Meta Description',
                    value: 'Missing',
                    status: 'poor',
                    statusText: 'Missing',
                    tip: 'Add a meta description to improve click-through rates'
                },
                quickWin: {
                    icon: '📝',
                    title: 'Add Meta Description',
                    description: 'Meta description is missing. This affects search result snippets.',
                    priority: 'high',
                    action: 'AdZetaAI.generateMetaDescription("post-editor")',
                    actionText: 'Generate'
                }
            };
        }

        // Length analysis (Google: 150-160 characters optimal)
        const length = metaDescription.length;
        if (length >= 150 && length <= 160) {
            score += 40; // Perfect length
        } else if (length >= 120 && length < 150) {
            score += 30; // Good length
        } else if (length > 160 && length <= 180) {
            score += 25; // Too long but acceptable
            issues.push('too long');
        } else {
            score += 10; // Poor length
            issues.push(length < 120 ? 'too short' : 'way too long');
        }

        // Focus keyword inclusion
        if (focusKeyword && metaDescription.toLowerCase().includes(focusKeyword.toLowerCase())) {
            score += 30; // Keyword included
        } else if (focusKeyword) {
            issues.push('missing focus keyword');
        } else {
            score += 15; // No focus keyword set
        }

        // Call-to-action words
        const ctaWords = ['learn', 'discover', 'find out', 'get', 'download', 'read', 'explore', 'see how', 'start'];
        const hasCTA = ctaWords.some(word => metaDescription.toLowerCase().includes(word));
        if (hasCTA) {
            score += 30; // Has compelling language
        } else {
            score += 10;
            issues.push('lacks call-to-action');
        }

        // Determine status
        let status, statusText, value;
        if (score >= 80) {
            status = 'excellent';
            statusText = 'Excellent';
            value = `${length} chars - Optimized`;
        } else if (score >= 60) {
            status = 'good';
            statusText = 'Good';
            value = `${length} chars - Good`;
        } else if (score >= 40) {
            status = 'fair';
            statusText = 'Needs Work';
            value = `${length} chars - Issues`;
        } else {
            status = 'poor';
            statusText = 'Poor';
            value = `${length} chars - Poor`;
        }

        const result = {
            score: Math.min(score, 100),
            metric: {
                icon: '📝',
                name: 'Meta Description',
                value: value,
                status: status,
                statusText: statusText,
                tip: this.getMetaDescriptionTip(length, issues)
            }
        };

        if (score < 60) {
            result.quickWin = {
                icon: '📝',
                title: 'Optimize Meta Description',
                description: `Issues: ${issues.join(', ')}. Optimal: 150-160 characters with CTA.`,
                priority: score < 40 ? 'high' : 'medium',
                action: 'AdZetaAI.generateMetaDescription("post-editor")',
                actionText: 'Optimize'
            };
        }

        return result;
    },

    // Get meta description tip
    getMetaDescriptionTip(length, issues) {
        if (length === 0) return 'Add a compelling meta description (150-160 characters)';
        if (length < 120) return 'Too short - expand to 150-160 characters';
        if (length > 180) return 'Too long - Google will truncate it';
        if (issues.includes('missing focus keyword')) return 'Include your focus keyword naturally';
        if (issues.includes('lacks call-to-action')) return 'Add compelling words like "learn", "discover", "get"';
        return 'Great! Compelling and well-optimized';
    },

    // Analyze Focus Keyword SEO
    analyzeKeywordSEO(data) {
        const focusKeyword = data.focus_keyword || '';
        const title = data.title || '';
        const metaTitle = data.meta_title || title;
        const metaDescription = data.meta_description || '';
        const content = data.content || '';

        let score = 0;
        let issues = [];

        // Check if focus keyword exists
        if (!focusKeyword) {
            return {
                score: 0,
                metric: {
                    icon: '🎯',
                    name: 'Focus Keyword',
                    value: 'Not Set',
                    status: 'poor',
                    statusText: 'Missing',
                    tip: 'Set a focus keyword to optimize your content'
                },
                quickWin: {
                    icon: '🎯',
                    title: 'Set Focus Keyword',
                    description: 'Choose a primary keyword to optimize your content for search engines.',
                    priority: 'high',
                    action: 'AdZetaAI.suggestKeywords("post-editor")',
                    actionText: 'Suggest'
                }
            };
        }

        // Keyword in meta title
        if (metaTitle.toLowerCase().includes(focusKeyword.toLowerCase())) {
            score += 30;
        } else {
            issues.push('not in meta title');
        }

        // Keyword in meta description
        if (metaDescription.toLowerCase().includes(focusKeyword.toLowerCase())) {
            score += 25;
        } else {
            issues.push('not in meta description');
        }

        // Keyword density in content (1-3% is optimal)
        const contentLower = content.toLowerCase();
        const keywordLower = focusKeyword.toLowerCase();
        const keywordCount = (contentLower.match(new RegExp(keywordLower, 'g')) || []).length;
        const wordCount = content.split(/\s+/).length;
        const density = wordCount > 0 ? (keywordCount / wordCount) * 100 : 0;

        if (density >= 1 && density <= 3) {
            score += 25; // Optimal density
        } else if (density > 0 && density < 1) {
            score += 15; // Too low
            issues.push('low density in content');
        } else if (density > 3) {
            score += 10; // Too high (keyword stuffing)
            issues.push('keyword stuffing');
        } else {
            issues.push('not in content');
        }

        // Keyword placement (early in title/content is better)
        const titleWords = metaTitle.toLowerCase().split(/\s+/);
        const keywordPosition = titleWords.findIndex(word => word.includes(keywordLower));
        if (keywordPosition >= 0 && keywordPosition < 3) {
            score += 20; // Good position
        } else if (keywordPosition >= 0) {
            score += 10; // Present but not optimal position
        }

        // Determine status
        let status, statusText, value;
        if (score >= 80) {
            status = 'excellent';
            statusText = 'Excellent';
            value = `"${focusKeyword}" - Well Optimized`;
        } else if (score >= 60) {
            status = 'good';
            statusText = 'Good';
            value = `"${focusKeyword}" - Good Usage`;
        } else if (score >= 40) {
            status = 'fair';
            statusText = 'Needs Work';
            value = `"${focusKeyword}" - Issues`;
        } else {
            status = 'poor';
            statusText = 'Poor';
            value = `"${focusKeyword}" - Poor Usage`;
        }

        const result = {
            score: Math.min(score, 100),
            metric: {
                icon: '🎯',
                name: 'Focus Keyword',
                value: value,
                status: status,
                statusText: statusText,
                tip: this.getFocusKeywordTip(density, issues)
            }
        };

        if (score < 60) {
            result.quickWin = {
                icon: '🎯',
                title: 'Optimize Keyword Usage',
                description: `Issues: ${issues.join(', ')}. Include in title, description, and content naturally.`,
                priority: score < 40 ? 'high' : 'medium'
            };
        }

        return result;
    },

    // Get focus keyword tip
    getFocusKeywordTip(density, issues) {
        if (issues.includes('not in meta title')) return 'Include your focus keyword in the meta title';
        if (issues.includes('not in meta description')) return 'Include your focus keyword in the meta description';
        if (issues.includes('not in content')) return 'Use your focus keyword naturally in the content';
        if (issues.includes('keyword stuffing')) return 'Reduce keyword usage - aim for 1-3% density';
        if (issues.includes('low density in content')) return 'Use your keyword more frequently (1-3% density)';
        return 'Great keyword optimization!';
    },

    // Analyze Content SEO
    analyzeContentSEO(data) {
        const content = data.content || '';
        const title = data.title || '';

        let score = 0;
        let issues = [];

        // Word count analysis
        const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
        if (wordCount >= 1000) {
            score += 40; // Excellent length
        } else if (wordCount >= 500) {
            score += 30; // Good length
        } else if (wordCount >= 300) {
            score += 20; // Acceptable
            issues.push('could be longer');
        } else {
            score += 10; // Too short
            issues.push('too short');
        }

        // Readability (simple check for sentence length)
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const avgWordsPerSentence = sentences.length > 0 ? wordCount / sentences.length : 0;
        if (avgWordsPerSentence <= 20) {
            score += 30; // Good readability
        } else if (avgWordsPerSentence <= 25) {
            score += 20; // Acceptable
        } else {
            score += 10; // Poor readability
            issues.push('long sentences');
        }

        // Paragraph structure (check for line breaks)
        const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
        if (paragraphs.length >= 3) {
            score += 30; // Good structure
        } else {
            score += 15;
            issues.push('needs more paragraphs');
        }

        // Determine status
        let status, statusText, value;
        if (score >= 80) {
            status = 'excellent';
            statusText = 'Excellent';
            value = `${wordCount} words - Well Structured`;
        } else if (score >= 60) {
            status = 'good';
            statusText = 'Good';
            value = `${wordCount} words - Good`;
        } else if (score >= 40) {
            status = 'fair';
            statusText = 'Needs Work';
            value = `${wordCount} words - Issues`;
        } else {
            status = 'poor';
            statusText = 'Poor';
            value = `${wordCount} words - Poor`;
        }

        const result = {
            score: Math.min(score, 100),
            metric: {
                icon: '📊',
                name: 'Content Quality',
                value: value,
                status: status,
                statusText: statusText,
                tip: this.getContentTip(wordCount, issues)
            }
        };

        if (score < 60) {
            result.quickWin = {
                icon: '📊',
                title: 'Improve Content',
                description: `Issues: ${issues.join(', ')}. Aim for 1000+ words with good structure.`,
                priority: score < 40 ? 'high' : 'medium'
            };
        }

        return result;
    },

    // Get content tip
    getContentTip(wordCount, issues) {
        if (wordCount < 300) return 'Add more content - aim for at least 1000 words';
        if (issues.includes('long sentences')) return 'Break up long sentences for better readability';
        if (issues.includes('needs more paragraphs')) return 'Break content into more paragraphs';
        if (issues.includes('could be longer')) return 'Consider expanding to 1000+ words for better SEO';
        return 'Great content structure and length!';
    },

    // Analyze Technical SEO
    analyzeTechnicalSEO(data) {
        const slug = data.slug || '';
        const focusKeyword = data.focus_keyword || '';

        let score = 0;
        let issues = [];

        // URL/Slug analysis
        if (slug) {
            // Check slug length (optimal: 3-5 words)
            const slugWords = slug.split('-').filter(word => word.length > 0);
            if (slugWords.length >= 3 && slugWords.length <= 5) {
                score += 30;
            } else {
                score += 15;
                issues.push(slugWords.length < 3 ? 'slug too short' : 'slug too long');
            }

            // Check if focus keyword is in slug
            if (focusKeyword && slug.toLowerCase().includes(focusKeyword.toLowerCase().replace(/\s+/g, '-'))) {
                score += 40;
            } else if (focusKeyword) {
                score += 10;
                issues.push('keyword not in URL');
            } else {
                score += 20;
            }

            // Check for SEO-friendly characters
            if (/^[a-z0-9-]+$/.test(slug)) {
                score += 30;
            } else {
                score += 10;
                issues.push('non-SEO friendly characters');
            }
        } else {
            issues.push('no URL slug');
        }

        // Determine status
        let status, statusText, value;
        if (score >= 80) {
            status = 'excellent';
            statusText = 'Excellent';
            value = 'SEO-Friendly URL';
        } else if (score >= 60) {
            status = 'good';
            statusText = 'Good';
            value = 'Good URL Structure';
        } else if (score >= 40) {
            status = 'fair';
            statusText = 'Needs Work';
            value = 'URL Issues';
        } else {
            status = 'poor';
            statusText = 'Poor';
            value = 'Poor URL Structure';
        }

        const result = {
            score: Math.min(score, 100),
            metric: {
                icon: '🔧',
                name: 'Technical SEO',
                value: value,
                status: status,
                statusText: statusText,
                tip: this.getTechnicalSEOTip(issues)
            }
        };

        if (score < 60) {
            result.quickWin = {
                icon: '🔧',
                title: 'Fix Technical Issues',
                description: `Issues: ${issues.join(', ')}. Optimize URL structure and keywords.`,
                priority: 'medium'
            };
        }

        return result;
    },

    // Get technical SEO tip
    getTechnicalSEOTip(issues) {
        if (issues.includes('no URL slug')) return 'Add a URL slug for better SEO';
        if (issues.includes('keyword not in URL')) return 'Include your focus keyword in the URL';
        if (issues.includes('slug too long')) return 'Shorten URL to 3-5 words';
        if (issues.includes('slug too short')) return 'Expand URL to 3-5 descriptive words';
        if (issues.includes('non-SEO friendly characters')) return 'Use only lowercase letters, numbers, and hyphens';
        return 'Great technical SEO setup!';
    },

    // Show title suggestions modal
    showTitleSuggestions(titles, target) {
        const modal = this.createModal('AI Title Suggestions', `
            <div class="ai-suggestions">
                <p class="text-muted mb-3">Select a title to use or get inspiration:</p>
                <div class="title-suggestions">
                    ${titles.map((title, index) => `
                        <div class="suggestion-item" data-title="${this.escapeHtml(title)}">
                            <div class="suggestion-content">
                                <strong>${index + 1}.</strong> ${this.escapeHtml(title)}
                            </div>
                            <button class="btn btn-sm btn-outline-primary" onclick="AdZetaAI.selectTitle('${this.escapeHtml(title)}', '${target}')">
                                Use This
                            </button>
                        </div>
                    `).join('')}
                </div>
                <div class="mt-3">
                    <button class="btn btn-secondary" onclick="AdZetaAI.generateTitles('${target}')">
                        <i class="fas fa-refresh me-1"></i> Generate More
                    </button>
                </div>
            </div>
        `);

        modal.show();
    },

    // Show tag suggestions
    showTagSuggestions(tags, target) {
        // Store tags in a global variable to avoid JSON parsing issues
        window.currentAITags = tags;
        window.currentAITarget = target;

        const modal = this.createModal('AI Tag Suggestions', `
            <div class="ai-suggestions">
                <p class="text-muted mb-3">Click tags to add them to your post:</p>
                <div class="tag-suggestions">
                    ${tags.map((tag, index) => `
                        <span class="badge bg-light text-dark me-2 mb-2 tag-suggestion"
                              style="cursor: pointer; border: 1px solid #dee2e6;"
                              onclick="AdZetaAI.selectTag('${this.escapeHtml(tag)}', '${target}')">
                            ${this.escapeHtml(tag)}
                        </span>
                    `).join('')}
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="AdZetaAI.selectAllTags()">
                        Add All Tags
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="AdZetaAI.generateTags('${target}')">
                        Generate More
                    </button>
                </div>
            </div>
        `);

        modal.show();
    },

    // Show SEO analysis with Apple-inspired clean design
    showSEOAnalysis(analysis, target) {
        // Parse and structure the analysis data
        const seoData = this.parseSEOAnalysis(analysis);

        // Update post editor UI if we're in post editor context
        if (target === 'post-editor') {
            this.updatePostEditorSEOUI(seoData);
        }

        const content = `
            <div class="seo-analysis-modern">
                <!-- Overall Score Section -->
                <div class="seo-score-card mb-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="mb-1 text-muted">SEO Score</h6>
                            <div class="score-display">
                                <span class="score-number ${this.getScoreColorClass(seoData.score)}">${seoData.score}</span>
                                <span class="score-total text-muted">/100</span>
                            </div>
                        </div>
                        <div class="score-ring">
                            <svg width="60" height="60" viewBox="0 0 60 60">
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#f0f0f0" stroke-width="4"/>
                                <circle cx="30" cy="30" r="25" fill="none" stroke="${this.getScoreColor(seoData.score)}"
                                        stroke-width="4" stroke-linecap="round"
                                        stroke-dasharray="${(seoData.score / 100) * 157} 157"
                                        transform="rotate(-90 30 30)"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Quick Wins Section -->
                ${seoData.quickWins.length > 0 ? `
                    <div class="quick-wins-section mb-4">
                        <h6 class="section-title">🚀 Quick Wins</h6>
                        <div class="wins-grid">
                            ${seoData.quickWins.map(win => `
                                <div class="win-item ${win.priority}">
                                    <div class="win-icon">${win.icon}</div>
                                    <div class="win-content">
                                        <div class="win-title">${win.title}</div>
                                        <div class="win-description">${win.description}</div>
                                    </div>
                                    ${win.action ? `<button class="btn btn-sm btn-outline-primary win-action" onclick="${win.action}">${win.actionText}</button>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <!-- Detailed Analysis -->
                <div class="analysis-details">
                    <h6 class="section-title">📊 Analysis Details</h6>
                    <div class="metrics-grid">
                        ${seoData.metrics.map(metric => `
                            <div class="metric-card ${metric.status}">
                                <div class="metric-header">
                                    <span class="metric-icon">${metric.icon}</span>
                                    <span class="metric-name">${metric.name}</span>
                                    <span class="metric-status-badge ${metric.status}">${metric.statusText}</span>
                                </div>
                                <div class="metric-value">${metric.value}</div>
                                ${metric.tip ? `<div class="metric-tip">${metric.tip}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="analysis-actions mt-4">
                    <button class="btn btn-primary" onclick="AdZetaAI.generateAllSEO('${target}')">
                        🚀 Generate Complete SEO
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="AdZetaAI.analyzeSEO('${target}')">
                        🔄 Re-analyze
                    </button>
                </div>
            </div>

            <style>
                .seo-analysis-modern {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                }
                .seo-score-card {
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-radius: 12px;
                    padding: 20px;
                    border: 1px solid #dee2e6;
                }
                .score-display {
                    display: flex;
                    align-items: baseline;
                    gap: 4px;
                }
                .score-number {
                    font-size: 2.5rem;
                    font-weight: 600;
                    line-height: 1;
                }
                .score-total {
                    font-size: 1.2rem;
                }
                .score-excellent { color: #28a745; }
                .score-good { color: #17a2b8; }
                .score-fair { color: #ffc107; }
                .score-poor { color: #dc3545; }

                .section-title {
                    font-weight: 600;
                    color: #495057;
                    margin-bottom: 12px;
                    font-size: 0.95rem;
                }

                .wins-grid {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                }
                .win-item {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    padding: 12px;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;
                    background: #fff;
                }
                .win-item.high {
                    border-left: 4px solid #dc3545;
                }
                .win-item.medium {
                    border-left: 4px solid #ffc107;
                }
                .win-item.low {
                    border-left: 4px solid #28a745;
                }
                .win-icon {
                    font-size: 1.2rem;
                    width: 24px;
                    text-align: center;
                }
                .win-title {
                    font-weight: 600;
                    font-size: 0.9rem;
                    color: #495057;
                }
                .win-description {
                    font-size: 0.85rem;
                    color: #6c757d;
                    margin-top: 2px;
                }
                .win-action {
                    margin-left: auto;
                    font-size: 0.8rem;
                }

                .metrics-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 12px;
                }
                .metric-card {
                    padding: 16px;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;
                    background: #fff;
                }
                .metric-header {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 8px;
                }
                .metric-icon {
                    font-size: 1.1rem;
                }
                .metric-name {
                    font-weight: 500;
                    font-size: 0.9rem;
                    color: #495057;
                    flex: 1;
                }
                .metric-status-badge {
                    font-size: 0.75rem;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-weight: 500;
                }
                .metric-status-badge.excellent {
                    background: #d4edda;
                    color: #155724;
                }
                .metric-status-badge.good {
                    background: #d1ecf1;
                    color: #0c5460;
                }
                .metric-status-badge.fair {
                    background: #fff3cd;
                    color: #856404;
                }
                .metric-status-badge.poor {
                    background: #f8d7da;
                    color: #721c24;
                }
                .metric-value {
                    font-size: 1.1rem;
                    font-weight: 600;
                    color: #212529;
                    margin-bottom: 4px;
                }
                .metric-tip {
                    font-size: 0.8rem;
                    color: #6c757d;
                    font-style: italic;
                }

                .analysis-actions {
                    display: flex;
                    gap: 8px;
                    padding-top: 16px;
                    border-top: 1px solid #e9ecef;
                }
            </style>
        `;

        const modal = this.createModal('SEO Analysis', content, 'modal-lg');
        modal.show();
    },

    // Parse SEO analysis data into structured format
    parseSEOAnalysis(analysis) {
        // If we have structured analysis data (from performLocalSEOAnalysis)
        if (analysis.score !== undefined && analysis.metrics && analysis.quickWins !== undefined) {
            console.log('📊 Using structured SEO analysis data:', analysis);
            return {
                score: analysis.score,
                quickWins: analysis.quickWins || [],
                metrics: analysis.metrics || []
            };
        }

        // Default structure for fallback
        let seoData = {
            score: 65,
            quickWins: [],
            metrics: []
        };

        if (analysis.raw_content) {
            // Parse raw content and extract meaningful insights
            seoData = this.parseRawSEOContent(analysis.raw_content);
        } else if (analysis.score || analysis.recommendations) {
            // Structured analysis
            seoData.score = analysis.score || 65;

            if (analysis.recommendations) {
                seoData.quickWins = this.convertRecommendationsToQuickWins(analysis.recommendations);
            }
        }

        // Add default metrics if none exist
        if (seoData.metrics.length === 0) {
            seoData.metrics = this.generateDefaultMetrics();
        }

        return seoData;
    },

    // Parse raw SEO content into actionable insights
    parseRawSEOContent(rawContent) {
        const content = rawContent.toLowerCase();
        let score = 65;
        let quickWins = [];
        let metrics = [];

        // Analyze content for common SEO issues
        if (content.includes('meta title') || content.includes('title tag')) {
            if (content.includes('missing') || content.includes('too long') || content.includes('too short')) {
                quickWins.push({
                    icon: '🏷️',
                    title: 'Optimize Meta Title',
                    description: 'Your meta title needs attention for better search visibility',
                    priority: 'high',
                    action: 'AdZetaAI.generateMetaTitle("post-editor")',
                    actionText: 'Generate'
                });
                score -= 15;
            }
        }

        if (content.includes('meta description')) {
            if (content.includes('missing') || content.includes('too long') || content.includes('too short')) {
                quickWins.push({
                    icon: '📝',
                    title: 'Add Meta Description',
                    description: 'Meta description helps search engines understand your content',
                    priority: 'high',
                    action: 'AdZetaAI.generateMetaDescription("post-editor")',
                    actionText: 'Generate'
                });
                score -= 15;
            }
        }

        if (content.includes('keyword') && (content.includes('missing') || content.includes('density'))) {
            quickWins.push({
                icon: '🎯',
                title: 'Focus Keyword',
                description: 'Add a focus keyword to improve search ranking',
                priority: 'medium',
                action: 'AdZetaAI.suggestKeywords("post-editor")',
                actionText: 'Suggest'
            });
            score -= 10;
        }

        if (content.includes('heading') || content.includes('h1') || content.includes('h2')) {
            if (content.includes('missing') || content.includes('improve')) {
                quickWins.push({
                    icon: '📋',
                    title: 'Improve Headings',
                    description: 'Better heading structure improves readability and SEO',
                    priority: 'medium'
                });
                score -= 8;
            }
        }

        // Generate metrics based on analysis
        metrics = [
            {
                icon: '🏷️',
                name: 'Meta Title',
                value: content.includes('meta title') && !content.includes('missing') ? 'Optimized' : 'Needs Work',
                status: content.includes('meta title') && !content.includes('missing') ? 'good' : 'poor',
                statusText: content.includes('meta title') && !content.includes('missing') ? 'Good' : 'Fix',
                tip: 'Keep between 50-60 characters for best results'
            },
            {
                icon: '📝',
                name: 'Meta Description',
                value: content.includes('meta description') && !content.includes('missing') ? 'Present' : 'Missing',
                status: content.includes('meta description') && !content.includes('missing') ? 'good' : 'poor',
                statusText: content.includes('meta description') && !content.includes('missing') ? 'Good' : 'Add',
                tip: 'Aim for 150-160 characters to avoid truncation'
            },
            {
                icon: '🎯',
                name: 'Focus Keyword',
                value: content.includes('keyword') && !content.includes('missing') ? 'Set' : 'Not Set',
                status: content.includes('keyword') && !content.includes('missing') ? 'good' : 'fair',
                statusText: content.includes('keyword') && !content.includes('missing') ? 'Good' : 'Add',
                tip: 'Choose a primary keyword for this content'
            },
            {
                icon: '📊',
                name: 'Content Length',
                value: content.includes('content') ? 'Adequate' : 'Unknown',
                status: 'good',
                statusText: 'Good',
                tip: 'Longer content often ranks better'
            }
        ];

        return {
            score: Math.max(score, 0),
            quickWins,
            metrics
        };
    },

    // Convert recommendations to quick wins format
    convertRecommendationsToQuickWins(recommendations) {
        return recommendations.slice(0, 3).map((rec, index) => {
            const priority = index === 0 ? 'high' : index === 1 ? 'medium' : 'low';
            const icons = ['⚡', '🎯', '📈', '🔧', '💡'];

            return {
                icon: icons[index % icons.length],
                title: this.extractTitleFromRecommendation(rec),
                description: rec,
                priority: priority
            };
        });
    },

    // Extract title from recommendation text
    extractTitleFromRecommendation(rec) {
        if (rec.toLowerCase().includes('meta title')) return 'Meta Title';
        if (rec.toLowerCase().includes('meta description')) return 'Meta Description';
        if (rec.toLowerCase().includes('keyword')) return 'Keywords';
        if (rec.toLowerCase().includes('heading')) return 'Headings';
        if (rec.toLowerCase().includes('content')) return 'Content';
        return 'SEO Improvement';
    },

    // Generate default metrics when none exist
    generateDefaultMetrics() {
        return [
            {
                icon: '🏷️',
                name: 'Meta Title',
                value: 'Ready to optimize',
                status: 'fair',
                statusText: 'Pending',
                tip: 'Generate an SEO-optimized title'
            },
            {
                icon: '📝',
                name: 'Meta Description',
                value: 'Ready to create',
                status: 'fair',
                statusText: 'Pending',
                tip: 'Create compelling description'
            },
            {
                icon: '🎯',
                name: 'Focus Keyword',
                value: 'Not set',
                status: 'fair',
                statusText: 'Add',
                tip: 'Choose your target keyword'
            },
            {
                icon: '🏷️',
                name: 'Tags',
                value: 'Ready to generate',
                status: 'fair',
                statusText: 'Generate',
                tip: 'Add relevant content tags'
            }
        ];
    },

    // Get score color class
    getScoreColorClass(score) {
        if (score >= 85) return 'score-excellent';
        if (score >= 70) return 'score-good';
        if (score >= 50) return 'score-fair';
        return 'score-poor';
    },

    // Get score color for SVG
    getScoreColor(score) {
        if (score >= 85) return '#28a745';
        if (score >= 70) return '#17a2b8';
        if (score >= 50) return '#ffc107';
        return '#dc3545';
    },

    // Update post editor SEO UI elements
    updatePostEditorSEOUI(seoData) {
        console.log('📊 Updating post editor SEO UI with score:', seoData.score);

        // Update the SEO score badge
        const seoScoreBadge = document.getElementById('seoScore');
        if (seoScoreBadge) {
            seoScoreBadge.textContent = `${seoData.score}%`;

            // Set badge color based on score
            let badgeClass = 'badge ms-2 ';
            if (seoData.score >= 80) {
                badgeClass += 'badge-success';
            } else if (seoData.score >= 60) {
                badgeClass += 'badge-info';
            } else if (seoData.score >= 40) {
                badgeClass += 'badge-warning';
            } else {
                badgeClass += 'badge-danger';
            }

            seoScoreBadge.className = badgeClass;
            console.log('✅ Updated SEO score badge:', seoData.score + '%');
        }

        // Update SEO analysis results if container exists
        const resultsContainer = document.getElementById('seoAnalysisResults');
        if (resultsContainer) {
            resultsContainer.style.display = 'block';

            // Create a simplified breakdown for the post editor
            const scoreBreakdown = document.getElementById('seoScoreBreakdown');
            if (scoreBreakdown && seoData.metrics) {
                scoreBreakdown.innerHTML = this.renderPostEditorMetrics(seoData.metrics);
            }

            // Show quick wins as recommendations
            const recommendations = document.getElementById('seoRecommendations');
            if (recommendations && seoData.quickWins) {
                recommendations.innerHTML = this.renderPostEditorQuickWins(seoData.quickWins);
            }
        }
    },

    // Render metrics for post editor
    renderPostEditorMetrics(metrics) {
        if (!metrics || metrics.length === 0) {
            return '<div class="text-muted small">No metrics available</div>';
        }

        return `
            <div class="seo-metrics-compact">
                ${metrics.map(metric => `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="d-flex align-items-center">
                            <span class="me-2">${metric.icon}</span>
                            <small class="text-muted">${metric.name}</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge badge-sm ${this.getMetricBadgeClass(metric.status)} me-1">
                                ${metric.statusText}
                            </span>
                        </div>
                    </div>
                    <div class="small text-muted mb-2" style="margin-left: 1.5rem;">
                        ${metric.value}
                    </div>
                `).join('')}
            </div>
        `;
    },

    // Render quick wins for post editor
    renderPostEditorQuickWins(quickWins) {
        if (!quickWins || quickWins.length === 0) {
            return '<div class="alert alert-success alert-sm">Great! No major SEO issues found.</div>';
        }

        return `
            <div class="seo-quickwins-compact">
                <h6 class="mb-2 text-warning">
                    <i class="fas fa-lightbulb me-1"></i>
                    Quick Wins
                </h6>
                ${quickWins.slice(0, 3).map(win => `
                    <div class="quick-win-item mb-2 p-2 bg-light rounded">
                        <div class="d-flex align-items-start">
                            <span class="me-2">${win.icon}</span>
                            <div class="flex-grow-1">
                                <div class="fw-bold small">${win.title}</div>
                                <div class="text-muted small">${win.description}</div>
                                ${win.action ? `
                                    <button class="btn btn-sm btn-outline-primary mt-1"
                                            onclick="${win.action}"
                                            title="${win.title}">
                                        <i class="fas fa-magic me-1"></i>
                                        ${win.actionText || 'Fix'}
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    // Get metric badge class based on status
    getMetricBadgeClass(status) {
        switch (status) {
            case 'excellent': return 'badge-success';
            case 'good': return 'badge-info';
            case 'fair': return 'badge-warning';
            case 'poor': return 'badge-danger';
            default: return 'badge-secondary';
        }
    },

    // Apply title selection
    selectTitle(title, target) {
        // Use the post editor's updateField method if available (preferred)
        if (window.AdZetaPostEditor && window.AdZetaPostEditor.updateField) {
            window.AdZetaPostEditor.updateField('title', title);

            // Force slug generation for AI-generated titles
            if (window.AdZetaPostEditor.generateSlug) {
                console.log('Triggering slug generation...');
                window.AdZetaPostEditor.generateSlug(true); // Force generation even for existing posts
            }

            window.AdZetaApp.showNotification('Title applied successfully!', 'success');
            this.closeModal();
            return;
        }

        // Fallback method
        const titleField = document.querySelector('#postTitle, [name="title"], .post-title-input');
        if (titleField) {
            titleField.value = title;

            // Trigger multiple events to ensure compatibility
            titleField.dispatchEvent(new Event('input', { bubbles: true }));
            titleField.dispatchEvent(new Event('change', { bubbles: true }));

            // Specifically trigger slug generation if the post editor has this method
            if (window.AdZetaPostEditor && window.AdZetaPostEditor.generateSlug) {
                console.log('Triggering slug generation...');
                window.AdZetaPostEditor.generateSlug(true); // Force generation
            }

            window.AdZetaApp.showNotification('Title applied successfully!', 'success');
        }
        this.closeModal();
    },

    // Apply meta description
    applyMetaDescription(metaDescription, target) {
        const metaField = document.querySelector('#metaDescription, [name="meta_description"], .meta-description-input');
        if (metaField) {
            metaField.value = metaDescription;
            metaField.dispatchEvent(new Event('input', { bubbles: true }));
        }
    },

    // Select individual tag
    selectTag(tag, target) {
        console.log('Selecting tag:', tag);

        // Use the post editor's addTagFromAI method if available
        if (window.AdZetaPostEditor && window.AdZetaPostEditor.addTagFromAI) {
            window.AdZetaPostEditor.addTagFromAI(tag);
            window.AdZetaApp.showNotification(`Tag "${tag}" added!`, 'success');
            // Don't close modal - allow user to select more tags
            return;
        }

        // Fallback: Try to find tags input field with multiple selectors
        const tagsField = document.querySelector('#postTags, #tags, [name="tags"], .tags-input, [data-field="tags"]');

        if (tagsField) {
            console.log('Found tags field:', tagsField);
            const currentTags = tagsField.value ? tagsField.value.split(',').map(t => t.trim()).filter(t => t) : [];

            if (!currentTags.includes(tag)) {
                currentTags.push(tag);
                tagsField.value = currentTags.join(', ');

                // Trigger multiple events to ensure compatibility
                tagsField.dispatchEvent(new Event('input', { bubbles: true }));
                tagsField.dispatchEvent(new Event('change', { bubbles: true }));

                window.AdZetaApp.showNotification(`Tag "${tag}" added!`, 'success');
                // Don't close modal - allow user to select more tags
            } else {
                window.AdZetaApp.showNotification(`Tag "${tag}" already exists`, 'info');
            }
        } else {
            console.warn('Tags field not found. Available fields:', document.querySelectorAll('input, textarea'));
            window.AdZetaApp.showNotification('Tags field not found', 'warning');
        }
    },

    // Select all tags
    selectAllTags() {
        if (!window.currentAITags || !window.currentAITarget) {
            window.AdZetaApp.showNotification('No tags available', 'warning');
            return;
        }

        const tags = window.currentAITags;
        const target = window.currentAITarget;

        console.log('Adding all tags:', tags);

        // Use the post editor's addTagFromAI method if available
        if (window.AdZetaPostEditor && window.AdZetaPostEditor.addTagFromAI) {
            let addedCount = 0;
            tags.forEach(tag => {
                // Check if tag already exists in the post state
                if (!window.AdZetaPostEditor.state.currentPost.tags.includes(tag)) {
                    window.AdZetaPostEditor.addTagFromAI(tag);
                    addedCount++;
                }
            });

            if (addedCount > 0) {
                window.AdZetaApp.showNotification(`Added ${addedCount} new tags!`, 'success');
            } else {
                window.AdZetaApp.showNotification('All tags already exist', 'info');
            }
            this.closeModal();
            return;
        }

        // Fallback method
        const tagsField = document.querySelector('#postTags, #tags, [name="tags"], .tags-input, [data-field="tags"]');

        if (tagsField) {
            const currentTags = tagsField.value ? tagsField.value.split(',').map(t => t.trim()).filter(t => t) : [];
            const newTags = tags.filter(tag => !currentTags.includes(tag));

            if (newTags.length > 0) {
                const allTags = [...currentTags, ...newTags];
                tagsField.value = allTags.join(', ');

                // Trigger events
                tagsField.dispatchEvent(new Event('input', { bubbles: true }));
                tagsField.dispatchEvent(new Event('change', { bubbles: true }));

                window.AdZetaApp.showNotification(`Added ${newTags.length} new tags!`, 'success');
                this.closeModal();
            } else {
                window.AdZetaApp.showNotification('All tags already exist', 'info');
            }
        } else {
            window.AdZetaApp.showNotification('Tags field not found', 'warning');
        }
    },

    // Context extraction helpers
    getTitleFromContext(target) {
        const titleField = document.querySelector('#postTitle, [name="title"], .post-title-input');
        return titleField ? titleField.value.trim() : '';
    },

    async getContentFromContext(target) {
        // For now, let's use a simple fallback approach
        // Try to get content from Editor.js first
        if (window.AdZetaPostEditor && window.AdZetaPostEditor.state.editor) {
            try {
                const content = await window.AdZetaPostEditor.getPlainTextContent();
                if (content && content.trim()) {
                    console.log('Got content from Editor.js:', content.substring(0, 100) + '...');
                    return content.trim();
                }
            } catch (e) {
                console.warn('Could not get content from Editor.js:', e);
            }
        }

        // Fallback to textarea
        const contentField = document.querySelector('#content, [name="content"], .content-input');
        const fallbackContent = contentField ? contentField.value.trim() : '';

        // If no content found, use a placeholder for testing
        if (!fallbackContent) {
            console.log('No content found, using placeholder');
            return 'This is a sample blog post content for AI processing. Please add some actual content to get better results.';
        }

        console.log('Got fallback content:', fallbackContent.substring(0, 100) + '...');
        return fallbackContent;
    },

    async getTopicFromContext(target) {
        const title = this.getTitleFromContext(target);
        if (title) return title;

        const content = await this.getContentFromContext(target);
        return content.substring(0, 100);
    },

    getKeywordsFromContext(target) {
        const keywordField = document.querySelector('#focusKeyword, [name="focus_keyword"], .focus-keyword-input');
        if (keywordField && keywordField.value) {
            return [keywordField.value.trim()];
        }
        return [];
    },

    getMetaDescriptionFromContext(target) {
        const metaField = document.querySelector('#metaDescription, [name="meta_description"], .meta-description-input');
        return metaField ? metaField.value.trim() : '';
    },

    getFocusKeywordFromContext(target) {
        const keywordField = document.querySelector('#focusKeyword, [name="focus_keyword"], .focus-keyword-input');
        return keywordField ? keywordField.value.trim() : '';
    },

    // UI helpers
    showLoading(button) {
        this.state.isLoading = true;
        button.disabled = true;
        const originalText = button.innerHTML;
        button.dataset.originalText = originalText;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';
    },

    hideLoading(button) {
        this.state.isLoading = false;
        button.disabled = false;
        if (button.dataset.originalText) {
            button.innerHTML = button.dataset.originalText;
            delete button.dataset.originalText;
        }
    },

    // Create modal
    createModal(title, content) {
        const modalId = 'aiModal' + Date.now();
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-robot me-2 text-primary"></i>
                                ${title}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modalElement = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalElement);

        // Clean up modal after it's hidden
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });

        return modal;
    },

    closeModal() {
        const openModal = document.querySelector('.modal.show');
        if (openModal) {
            const modal = bootstrap.Modal.getInstance(openModal);
            if (modal) modal.hide();
        }
    },

    // Check if user is authenticated
    isUserAuthenticated() {
        // Primary check: AdZetaApp authentication state
        if (window.AdZetaApp && window.AdZetaApp.state && window.AdZetaApp.state.isAuthenticated) {
            console.log('AI: User authenticated via AdZetaApp state');
            return true;
        }

        // Secondary check: Check if we're on the login page
        if (window.location.pathname.includes('login') || window.location.search.includes('login')) {
            console.log('AI: On login page, user not authenticated');
            return false;
        }

        // Tertiary check: JWT token exists and admin dashboard is visible
        const token = localStorage.getItem('adzeta_token') || sessionStorage.getItem('adzeta_token');
        const adminDashboard = document.getElementById('adminDashboard');
        const loginPage = document.getElementById('loginPage');

        if (token && adminDashboard && adminDashboard.style.display !== 'none' &&
            loginPage && loginPage.style.display === 'none') {
            console.log('AI: User authenticated via token and UI state');
            return true;
        }

        console.log('AI: User not authenticated - no valid auth state found');
        return false;
    },

    // Utility functions
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    // Test AI connection
    async testConnection() {
        try {
            const response = await window.AdZetaApp.apiRequest('/ai/test-connection', {
                method: 'POST'
            });

            if (response.success) {
                window.AdZetaApp.showNotification('AI connection successful!', 'success');
                return true;
            } else {
                window.AdZetaApp.showNotification('AI connection failed: ' + response.error, 'danger');
                return false;
            }
        } catch (error) {
            window.AdZetaApp.showNotification('AI connection test failed: ' + error.message, 'danger');
            return false;
        }
    },

    // Generate meta title
    async generateMetaTitle(target) {
        const title = this.getTitleFromContext(target);
        const focusKeyword = this.getFocusKeywordFromContext(target);

        if (!title) {
            window.AdZetaApp.showNotification('Please enter a blog title first', 'warning');
            return;
        }

        const response = await window.AdZetaApp.apiRequest('/ai/generate-content', {
            method: 'POST',
            body: JSON.stringify({
                prompt: `Create an SEO-optimized meta title (50-60 characters) based on this blog title: "${title}"${focusKeyword ? ` and focus keyword: "${focusKeyword}"` : ''}.

Requirements:
- 50-60 characters maximum
- Include the focus keyword naturally
- Compelling and click-worthy
- Optimized for search engines
- Different from the blog title but related

Return only the meta title, no additional text.`,
                temperature: 0.6,
                maxOutputTokens: 100
            })
        });

        if (response.success) {
            this.applyMetaTitle(response.content.trim(), target);
            window.AdZetaApp.showNotification('Meta title generated successfully!', 'success');
        } else {
            throw new Error(response.error || 'Failed to generate meta title');
        }
    },

    // Suggest keywords
    async suggestKeywords(target) {
        const title = this.getTitleFromContext(target);
        const content = await this.getContentFromContext(target);

        if (!title && !content) {
            window.AdZetaApp.showNotification('Please enter a title or content first', 'warning');
            return;
        }

        const response = await window.AdZetaApp.apiRequest('/ai/generate-content', {
            method: 'POST',
            body: JSON.stringify({
                prompt: `Suggest 5-10 SEO keywords for this content:

Title: ${title}
Content: ${content.substring(0, 500)}...

Requirements:
- Focus on high-value, searchable keywords
- Mix of short-tail and long-tail keywords
- Relevant to the content topic
- Good search volume potential

Return as a comma-separated list only.`,
                temperature: 0.5,
                maxOutputTokens: 200
            })
        });

        if (response.success) {
            const keywords = response.content.split(',').map(k => k.trim());
            this.showKeywordSuggestions(keywords, target);
        } else {
            throw new Error(response.error || 'Failed to suggest keywords');
        }
    },

    // Generate all SEO elements
    async generateAllSEO(target) {
        const title = this.getTitleFromContext(target);
        const content = await this.getContentFromContext(target);

        if (!title || !content) {
            window.AdZetaApp.showNotification('Please enter a title and content first', 'warning');
            return;
        }

        window.AdZetaApp.showNotification('Generating all SEO elements...', 'info');

        try {
            // Generate meta title
            await this.generateMetaTitle(target);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay

            // Generate meta description
            await this.generateMetaDescription(target);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay

            // Generate tags
            await this.generateTags(target);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay

            // Suggest keywords
            await this.suggestKeywords(target);

            window.AdZetaApp.showNotification('All SEO elements generated successfully!', 'success');
        } catch (error) {
            console.error('Error generating all SEO elements:', error);
            window.AdZetaApp.showNotification('Some SEO elements could not be generated', 'warning');
        }
    },

    // Show keyword suggestions
    showKeywordSuggestions(keywords, target) {
        // Store keywords globally for selectAllKeywords function
        window.currentAIKeywords = keywords;
        window.currentAIKeywordTarget = target;

        const modal = this.createModal('Keyword Suggestions', `
            <div class="ai-suggestions">
                <p class="text-muted mb-3">Click keywords to add them to Meta Keywords:</p>
                <div class="keyword-suggestions">
                    ${keywords.map(keyword => `
                        <span class="badge bg-light text-dark me-2 mb-2 keyword-suggestion"
                              style="cursor: pointer; border: 1px solid #dee2e6;"
                              onclick="AdZetaAI.selectKeyword('${this.escapeHtml(keyword)}', '${target}')">
                            ${this.escapeHtml(keyword)}
                        </span>
                    `).join('')}
                </div>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="AdZetaAI.selectAllKeywords()">
                        Add All Keywords
                    </button>
                    <button class="btn btn-outline-secondary ms-2" onclick="AdZetaAI.suggestKeywords('${target}')">
                        Generate More
                    </button>
                </div>
            </div>
        `);

        modal.show();
    },

    // Select keyword - append to Meta Keywords field
    selectKeyword(keyword, target) {
        const metaKeywordsField = document.querySelector('#metaKeywords, [name="meta_keywords"], .meta-keywords-input');
        if (metaKeywordsField) {
            // Get current value and clean it
            let currentValue = metaKeywordsField.value.trim();

            // Check if keyword already exists
            const existingKeywords = currentValue ? currentValue.split(',').map(k => k.trim().toLowerCase()) : [];
            if (existingKeywords.includes(keyword.toLowerCase())) {
                window.AdZetaApp.showNotification(`Keyword "${keyword}" already exists`, 'info');
                return;
            }

            // Append the new keyword
            if (currentValue) {
                metaKeywordsField.value = currentValue + ', ' + keyword;
            } else {
                metaKeywordsField.value = keyword;
            }

            // Trigger events to update the post editor state
            metaKeywordsField.dispatchEvent(new Event('input', { bubbles: true }));
            metaKeywordsField.dispatchEvent(new Event('change', { bubbles: true }));

            window.AdZetaApp.showNotification(`Keyword "${keyword}" added to Meta Keywords`, 'success');
        } else {
            console.error('Meta Keywords field not found');
            window.AdZetaApp.showNotification('Meta Keywords field not found', 'error');
        }
        // Don't close modal - allow user to select more keywords
    },

    // Select all keywords
    selectAllKeywords() {
        // Get keywords from global storage
        const keywords = window.currentAIKeywords;

        if (!keywords || keywords.length === 0) {
            window.AdZetaApp.showNotification('No keywords available', 'error');
            return;
        }

        const metaKeywordsField = document.querySelector('#metaKeywords, [name="meta_keywords"], .meta-keywords-input');
        if (metaKeywordsField) {
            // Get current value and clean it
            let currentValue = metaKeywordsField.value.trim();
            const existingKeywords = currentValue ? currentValue.split(',').map(k => k.trim().toLowerCase()) : [];

            // Filter out keywords that already exist
            const newKeywords = keywords.filter(keyword =>
                !existingKeywords.includes(keyword.toLowerCase())
            );

            if (newKeywords.length === 0) {
                window.AdZetaApp.showNotification('All keywords already exist', 'info');
                return;
            }

            // Append new keywords
            if (currentValue) {
                metaKeywordsField.value = currentValue + ', ' + newKeywords.join(', ');
            } else {
                metaKeywordsField.value = newKeywords.join(', ');
            }

            // Trigger events to update the post editor state
            metaKeywordsField.dispatchEvent(new Event('input', { bubbles: true }));
            metaKeywordsField.dispatchEvent(new Event('change', { bubbles: true }));

            window.AdZetaApp.showNotification(`Added ${newKeywords.length} keywords to Meta Keywords`, 'success');
            this.closeModal();
        } else {
            console.error('Meta Keywords field not found');
            window.AdZetaApp.showNotification('Meta Keywords field not found', 'error');
        }
    },

    // Apply meta title
    applyMetaTitle(metaTitle, target) {
        const metaTitleField = document.querySelector('#metaTitle, [name="meta_title"], .meta-title-input');
        if (metaTitleField) {
            metaTitleField.value = metaTitle;
            metaTitleField.dispatchEvent(new Event('input', { bubbles: true }));
            metaTitleField.dispatchEvent(new Event('change', { bubbles: true }));
        }
    },

    // Show AI assistant panel
    showAIAssistant() {
        if (!this.state.isEnabled) {
            window.AdZetaApp.showNotification('AI Assistant is not configured. Please add API keys in settings.', 'warning');
            return;
        }

        const modal = this.createModal('AI Assistant', `
            <div class="ai-assistant-modern">
                <!-- Header Section -->
                <div class="assistant-header">
                    <div class="header-icon">🤖</div>
                    <div class="header-content">
                        <h5 class="header-title">AI Assistant</h5>
                        <p class="header-subtitle">Intelligent content optimization powered by AI</p>
                    </div>
                </div>

                <!-- Recommendation Banner -->
                <div class="recommendation-banner">
                    <div class="banner-icon">💡</div>
                    <div class="banner-content">
                        <div class="banner-title">Recommended Workflow</div>
                        <div class="banner-text">Use "Generate Complete SEO" after writing your content for best results</div>
                    </div>
                </div>

                <!-- Main Actions -->
                <div class="main-actions">
                    <button class="action-button primary" data-ai-action="analyze-seo">
                        <div class="action-icon">📊</div>
                        <div class="action-content">
                            <div class="action-title">Analyze SEO</div>
                            <div class="action-description">Review your content and get actionable SEO insights</div>
                        </div>
                        <div class="action-arrow">›</div>
                    </button>

                    <button class="action-button secondary" data-ai-action="generate-all">
                        <div class="action-icon">🚀</div>
                        <div class="action-content">
                            <div class="action-title">Generate Complete SEO</div>
                            <div class="action-description">Create meta title, description, tags, and keywords automatically</div>
                        </div>
                        <div class="action-arrow">›</div>
                    </button>
                </div>

                <!-- Quick Tools -->
                <div class="quick-tools">
                    <div class="tools-title">Quick Tools</div>
                    <div class="tools-grid">
                        <button class="tool-button" data-ai-action="generate-meta-title" title="Generate Meta Title">
                            <span class="tool-icon">🏷️</span>
                            <span class="tool-label">Meta Title</span>
                        </button>
                        <button class="tool-button" data-ai-action="generate-meta" title="Generate Meta Description">
                            <span class="tool-icon">📝</span>
                            <span class="tool-label">Description</span>
                        </button>
                        <button class="tool-button" data-ai-action="generate-tags" title="Generate Tags">
                            <span class="tool-icon">🏷️</span>
                            <span class="tool-label">Tags</span>
                        </button>
                        <button class="tool-button" data-ai-action="suggest-keywords" title="Suggest Keywords">
                            <span class="tool-icon">🎯</span>
                            <span class="tool-label">Keywords</span>
                        </button>
                    </div>
                </div>
            </div>

            <style>
                .ai-assistant-modern {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    max-width: 500px;
                    margin: 0 auto;
                }

                .assistant-header {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    margin-bottom: 24px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid #f0f0f0;
                }
                .header-icon {
                    font-size: 2.5rem;
                    width: 60px;
                    height: 60px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 16px;
                    color: white;
                }
                .header-title {
                    font-size: 1.5rem;
                    font-weight: 600;
                    color: #1d1d1f;
                    margin: 0;
                }
                .header-subtitle {
                    color: #86868b;
                    margin: 4px 0 0 0;
                    font-size: 0.9rem;
                }

                .recommendation-banner {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border: 1px solid #dee2e6;
                    border-radius: 12px;
                    padding: 16px;
                    margin-bottom: 24px;
                }
                .banner-icon {
                    font-size: 1.5rem;
                    width: 40px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #fff;
                    border-radius: 10px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }
                .banner-title {
                    font-weight: 600;
                    color: #1d1d1f;
                    font-size: 0.9rem;
                }
                .banner-text {
                    color: #86868b;
                    font-size: 0.85rem;
                    margin-top: 2px;
                }

                .main-actions {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                    margin-bottom: 32px;
                }
                .action-button {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    padding: 20px;
                    border: 1px solid #d2d2d7;
                    border-radius: 12px;
                    background: #fff;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    text-align: left;
                    width: 100%;
                }
                .action-button:hover {
                    border-color: #007aff;
                    box-shadow: 0 4px 16px rgba(0,122,255,0.15);
                    transform: translateY(-1px);
                }
                .action-button.primary {
                    background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
                    color: white;
                    border-color: #007aff;
                }
                .action-button.primary:hover {
                    box-shadow: 0 6px 20px rgba(0,122,255,0.3);
                }
                .action-button.secondary {
                    background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
                    color: white;
                    border-color: #34c759;
                }
                .action-button.secondary:hover {
                    box-shadow: 0 6px 20px rgba(52,199,89,0.3);
                }
                .action-icon {
                    font-size: 1.8rem;
                    width: 50px;
                    height: 50px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: rgba(255,255,255,0.2);
                    border-radius: 12px;
                    flex-shrink: 0;
                }
                .action-content {
                    flex: 1;
                }
                .action-title {
                    font-weight: 600;
                    font-size: 1.1rem;
                    margin-bottom: 4px;
                }
                .action-description {
                    opacity: 0.8;
                    font-size: 0.9rem;
                    line-height: 1.4;
                }
                .action-arrow {
                    font-size: 1.5rem;
                    opacity: 0.6;
                    flex-shrink: 0;
                }

                .quick-tools {
                    border-top: 1px solid #f0f0f0;
                    padding-top: 24px;
                }
                .tools-title {
                    font-weight: 600;
                    color: #1d1d1f;
                    margin-bottom: 16px;
                    font-size: 1rem;
                }
                .tools-grid {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 12px;
                }
                .tool-button {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 8px;
                    padding: 16px 8px;
                    border: 1px solid #e5e5e7;
                    border-radius: 10px;
                    background: #fff;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .tool-button:hover {
                    border-color: #007aff;
                    background: #f0f8ff;
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,122,255,0.15);
                }
                .tool-icon {
                    font-size: 1.5rem;
                }
                .tool-label {
                    font-size: 0.8rem;
                    font-weight: 500;
                    color: #1d1d1f;
                    text-align: center;
                }
            </style>
        `);

        modal.show();
    }
};

// Initialize AI Assistant after authentication
window.AdZetaAI.initializeAfterAuth = function() {
    console.log('AI: initializeAfterAuth called');
    console.log('AI: Current state:', window.AdZetaAI.state);
    console.log('AI: AdZetaApp state:', window.AdZetaApp ? window.AdZetaApp.state : 'AdZetaApp not available');

    // Always reload settings after authentication to ensure fresh data
    console.log('AI: Loading settings after authentication...');
    window.AdZetaAI.loadSettings();
};

// Auto-initialize when DOM is ready (but don't load settings until authenticated)
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => window.AdZetaAI.init());
} else {
    window.AdZetaAI.init();
}
