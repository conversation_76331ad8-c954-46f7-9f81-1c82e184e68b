/* platform Scale Section starts*/

/* Apple-style Visualization */
.apple-style-visualization {
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    background-color: #ffffff;
}

/* Scale Section Accordion - Matching ValueBid Section Style */
.accordion-style-01 {
    margin-bottom: 20px;
}

.accordion-style-01 .accordion-item {
    border: none;
    margin-bottom: 16px;
    border-radius: 16px !important;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    background-color: #ffffff;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.accordion-style-01 .accordion-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.accordion-style-01 .accordion-header {
    padding: 0;
    background-color: #ffffff;
}

.accordion-style-01 .accordion-title {
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    position: relative;
}

.accordion-style-01 .accordion-title .feature-icon-wrapper {
    margin-right: 12px;
    display: inline-flex;
}

.accordion-style-01 .accordion-title .feature-icon-wrapper i {
    font-size: 18px;
    background: linear-gradient(45deg, #f45888 0%, #ee5c46 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.accordion-style-01 .accordion-title i.icon-small {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #999;
    transition: all 0.3s ease;
    margin-left: auto;
}

.accordion-style-01 .accordion-body {
    padding: 0 20px 20px 20px;
    color: #666;
    font-size: 14px;
    line-height: 1.6;
}

.accordion-style-01 .active-accordion .accordion-title i.icon-small {
    transform: translateY(-50%) rotate(180deg);
}

/* Apple-style Metric Cards */
.apple-metric-card {
    padding: 28px;
    border-radius: 16px;
    background-color: #ffffff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 16px;
}

.apple-metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.apple-metric-card .metric-icon {
    margin-bottom: 16px;
}

.apple-metric-card .metric-icon i {
    font-size: 22px;
    background: linear-gradient(45deg, #f45888 0%, #ee5c46 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.apple-metric-card.reduction .metric-icon i {
    background: linear-gradient(45deg, #6c7ae0 0%, #4e5bd0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.apple-metric-card .metric-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    letter-spacing: -0.2px;
}

.apple-metric-card .metric-value {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
}

.apple-metric-card.positive .metric-value {
    color: #f45888;
}

.apple-metric-card.reduction .metric-value {
    color: #6c7ae0;
}

.apple-metric-card .metric-description {
    font-size: 16px;
    line-height: 1.5;
    color: #666;
    flex-grow: 1;
}


/* platform Scale Section ends*/