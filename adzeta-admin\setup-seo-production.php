<?php
/**
 * SEO System Production Setup
 * Complete setup and verification for production deployment
 */

header('Content-Type: text/html');

echo "<h1>🚀 SEO System Production Setup</h1>";

$errors = [];
$warnings = [];
$success = [];

// Step 1: Database Setup
echo "<h2>1. Database Setup</h2>";

try {
    $pdo = new PDO(
        'mysql:host=localhost;dbname=adzetadb;charset=utf8mb4',
        'adzetauser',
        'Crazy1395#',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $success[] = "Database connection successful";
    
    // Create tables
    $tables = [
        'page_seo' => "
            CREATE TABLE IF NOT EXISTS page_seo (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_url VARCHAR(255) NOT NULL UNIQUE,
                page_title VARCHAR(255) NOT NULL,
                meta_description TEXT,
                meta_keywords TEXT,
                og_title VARCHAR(255),
                og_description TEXT,
                og_image VARCHAR(500),
                og_type VARCHAR(50) DEFAULT 'website',
                twitter_card VARCHAR(50) DEFAULT 'summary_large_image',
                twitter_title VARCHAR(255),
                twitter_description TEXT,
                twitter_image VARCHAR(500),
                canonical_url VARCHAR(500),
                robots VARCHAR(100) DEFAULT 'index,follow',
                schema_markup JSON,
                custom_head_tags TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_page_url (page_url),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        'seo_settings' => "
            CREATE TABLE IF NOT EXISTS seo_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                setting_description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];
    
    foreach ($tables as $tableName => $sql) {
        try {
            $pdo->exec($sql);
            $success[] = "Table '$tableName' created/verified";
        } catch (PDOException $e) {
            $errors[] = "Failed to create table '$tableName': " . $e->getMessage();
        }
    }
    
    // Insert default data
    $defaultSettings = [
        ['default_title_suffix', ' - AdZeta', 'Default suffix added to page titles'],
        ['default_meta_description', 'AdZeta provides AI-powered performance marketing solutions for e-commerce businesses. Boost ROI with advanced algorithms and real-time optimization.', 'Default meta description when none is specified'],
        ['default_og_image', '/images/adzeta-og-default.jpg', 'Default Open Graph image'],
        ['default_twitter_handle', '@AdZetaAI', 'Default Twitter handle for Twitter cards'],
        ['google_analytics_id', 'GTM-KSC58XGP', 'Google Analytics/Tag Manager ID'],
        ['site_name', 'AdZeta', 'Site name for Open Graph'],
        ['default_robots', 'index,follow', 'Default robots meta tag']
    ];
    
    $insertSettingStmt = $pdo->prepare("
        INSERT INTO seo_settings (setting_key, setting_value, setting_description) 
        VALUES (?, ?, ?) 
        ON DUPLICATE KEY UPDATE 
        setting_value = VALUES(setting_value), 
        setting_description = VALUES(setting_description)
    ");
    
    foreach ($defaultSettings as $setting) {
        $insertSettingStmt->execute($setting);
    }
    $success[] = "Default SEO settings inserted/updated";
    
    // Insert default page data
    $defaultPages = [
        ['/platform.php', 'AdZeta Platform - AI-Powered Performance Marketing Technology', 'Discover AdZeta\'s cutting-edge AI platform for performance marketing. Advanced algorithms, real-time optimization, and data-driven insights for maximum ROI.'],
        ['/integrations.php', 'Third-Party Integrations - AdZeta Marketing Platform', 'Seamlessly integrate AdZeta with your existing marketing stack. Connect with Google Ads, Facebook, Shopify, and 100+ marketing tools and platforms.'],
        ['/blog-list-dynamic.php', 'Performance Marketing Blog - AdZeta Insights & Strategies', 'Stay ahead with the latest performance marketing insights, strategies, and industry trends. Expert tips for e-commerce growth and marketing optimization.'],
        ['/', 'AdZeta - AI-Powered Performance Marketing for E-commerce', 'Boost your e-commerce performance with AdZeta\'s AI-powered marketing platform. Advanced algorithms, real-time optimization, and measurable results.']
    ];
    
    $insertPageStmt = $pdo->prepare("
        INSERT INTO page_seo (page_url, page_title, meta_description) 
        VALUES (?, ?, ?) 
        ON DUPLICATE KEY UPDATE 
        page_title = VALUES(page_title),
        meta_description = VALUES(meta_description)
    ");
    
    foreach ($defaultPages as $page) {
        $insertPageStmt->execute($page);
    }
    $success[] = "Default page SEO data inserted/updated";
    
} catch (PDOException $e) {
    $errors[] = "Database error: " . $e->getMessage();
}

// Step 2: File Verification
echo "<h2>2. File Verification</h2>";

$requiredFiles = [
    'includes/seo-functions.php' => 'SEO functions library',
    'src/API/PageSEOController.php' => 'Page SEO API controller',
    'header.php' => 'Main header file',
    'header-light.php' => 'Light header file'
];

foreach ($requiredFiles as $file => $description) {
    // Handle different file paths correctly
    if ($file === 'src/API/PageSEOController.php') {
        $fullPath = __DIR__ . '/' . $file;  // This file is in adzeta-admin directory
    } else {
        $fullPath = __DIR__ . '/../' . $file;  // Other files are in parent directory
    }

    if (file_exists($fullPath)) {
        $success[] = "$description exists";

        // Check for specific content
        if ($file === 'header.php' || $file === 'header-light.php') {
            $content = file_get_contents($fullPath);
            if (strpos($content, 'blog-list-dynamic.php') !== false) {
                $success[] = "$description has updated blog links";
            } else {
                $warnings[] = "$description may not have updated blog links";
            }
        }

        // Verify PageSEOController can be loaded
        if ($file === 'src/API/PageSEOController.php') {
            try {
                require_once __DIR__ . '/bootstrap.php';
                require_once $fullPath;
                $controller = new \AdZetaAdmin\API\PageSEOController();
                $success[] = "$description loads successfully";
            } catch (Exception $e) {
                $warnings[] = "$description exists but has loading errors: " . $e->getMessage();
            }
        }
    } else {
        $errors[] = "$description missing: $file (checked: $fullPath)";
    }
}

// Step 3: API Endpoint Testing
echo "<h2>3. API Endpoint Testing</h2>";

$apiEndpoints = [
    '/adzeta-admin/api/page-seo' => 'Page SEO listing',
    '/adzeta-admin/api/seo-settings' => 'SEO settings'
];

foreach ($apiEndpoints as $endpoint => $description) {
    try {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 5,
                'ignore_errors' => true
            ]
        ]);
        
        $response = @file_get_contents("http://localhost$endpoint", false, $context);
        
        if ($response !== false) {
            $json = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE && isset($json['success'])) {
                $success[] = "$description API endpoint working";
            } else {
                $warnings[] = "$description API endpoint returns invalid JSON";
            }
        } else {
            $warnings[] = "$description API endpoint not responding";
        }
    } catch (Exception $e) {
        $warnings[] = "$description API endpoint error: " . $e->getMessage();
    }
}

// Step 4: SEO Functions Testing
echo "<h2>4. SEO Functions Testing</h2>";

if (file_exists(__DIR__ . '/../includes/seo-functions.php')) {
    try {
        require_once __DIR__ . '/../includes/seo-functions.php';
        $success[] = "SEO functions library loaded";
        
        // Test function
        $_SERVER['REQUEST_URI'] = '/platform.php';
        $seoData = getCurrentPageSEO();
        if ($seoData && !empty($seoData['page_title'])) {
            $success[] = "SEO data retrieval working";
        } else {
            $warnings[] = "SEO data retrieval returns empty results";
        }
        
        // Test SEO output
        $seoOutput = renderSEOTags($seoData);
        if (!empty($seoOutput)) {
            $success[] = "SEO tags generation working";
        } else {
            $warnings[] = "SEO tags generation returns empty output";
        }
        
    } catch (Exception $e) {
        $errors[] = "SEO functions error: " . $e->getMessage();
    }
} else {
    $errors[] = "SEO functions library not found";
}

// Display Results
echo "<hr>";
echo "<h2>📊 Setup Results</h2>";

if (!empty($success)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
    echo "<h3>✅ Successful Operations (" . count($success) . ")</h3>";
    echo "<ul>";
    foreach ($success as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($warnings)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; color: #856404; margin: 10px 0;'>";
    echo "<h3>⚠️ Warnings (" . count($warnings) . ")</h3>";
    echo "<ul>";
    foreach ($warnings as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
    echo "<h3>❌ Errors (" . count($errors) . ")</h3>";
    echo "<ul>";
    foreach ($errors as $item) {
        echo "<li>$item</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// Final Status
$totalIssues = count($errors);
$isProduction = $totalIssues === 0;

echo "<hr>";
if ($isProduction) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px; color: #155724;'>";
    echo "<h2>🎉 PRODUCTION READY!</h2>";
    echo "<p><strong>The SEO management system is fully operational and ready for production use.</strong></p>";
    echo "<h3>🚀 Next Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Access Admin Panel:</strong> <a href='/adzeta-admin/?view=seo' target='_blank'>SEO Management</a></li>";
    echo "<li><strong>Test Frontend:</strong> Check that SEO data appears on your pages</li>";
    echo "<li><strong>Add Header Integration:</strong> Include SEO functions in your templates</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 5px; color: #721c24;'>";
    echo "<h2>❌ NOT PRODUCTION READY</h2>";
    echo "<p><strong>Please fix the errors above before deploying to production.</strong></p>";
    echo "<p><strong>Critical Issues:</strong> $totalIssues</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>📝 Integration Instructions</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460;'>";
echo "<p><strong>To integrate SEO into your pages, add this to your header files:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars("<?php
// Add to your header.php file
require_once 'includes/seo-functions.php';
echo renderSEOTags();
?>");
echo "</pre>";
echo "<p><strong>This will automatically:</strong></p>";
echo "<ul>";
echo "<li>Detect if the page is a blog post or static page</li>";
echo "<li>Load appropriate SEO data from the database</li>";
echo "<li>Generate complete meta tags, Open Graph, and schema markup</li>";
echo "<li>Provide fallback values for pages without specific SEO data</li>";
echo "</ul>";
echo "</div>";
?>
