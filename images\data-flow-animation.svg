<?xml version="1.0" encoding="UTF-8"?>
<svg id="data-flow-svg" width="100%" height="100%" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <style>
    text {
      font-family: 'Proxima Nova', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	  font-size:1.5rem;
    }
  </style>
  <!-- Definitions for gradients and filters -->
  <defs>
    <!-- Gradient for AI Core -->
    <linearGradient id="core-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8f76f5">
        <animate attributeName="stop-color" values="#8f76f5;#9f86ff;#8f76f5" dur="8s" repeatCount="indefinite"/>
      </stop>
      <stop offset="50%" stop-color="#e958a1">
        <animate attributeName="stop-color" values="#e958a1;#ff68b1;#e958a1" dur="8s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#4a9eff">
        <animate attributeName="stop-color" values="#4a9eff;#5aaeFF;#4a9eff" dur="8s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>

    <!-- Glow filter for active elements -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>

    <!-- Particle filter -->
    <filter id="particle-blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1.5" result="blur"/>
    </filter>
  </defs>

  <!-- Data sources (left side) -->
  <g class="data-sources">
    <!-- Website data -->
    <rect x="15" y="100" width="185" height="80" rx="10" fill="#f5f5f7" stroke="#8f76f5" stroke-width="2" class="data-source website-data">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="0.5s"/>
      <animate attributeName="fill" values="#f5f5f7;#f0f4ff;#f5f5f7" dur="3s" repeatCount="indefinite" begin="0.5s"/>
    </rect>
    <text x="110" y="145" text-anchor="middle" fill="#2c2e3c" font-size="18" font-weight="600">Website Data</text>

    <!-- CRM data -->
    <rect x="15" y="250" width="185" height="80" rx="10" fill="#f5f5f7" stroke="#e958a1" stroke-width="2" class="data-source crm-data">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="1s"/>
      <animate attributeName="fill" values="#f5f5f7;#fff0f5;#f5f5f7" dur="3s" repeatCount="indefinite" begin="1s"/>
    </rect>
    <text x="110" y="295" text-anchor="middle" fill="#2c2e3c" font-size="18" font-weight="600">CRM Data</text>

    <!-- Transaction data -->
    <rect x="15" y="400" width="185" height="80" rx="10" fill="#f5f5f7" stroke="#4a9eff" stroke-width="2" class="data-source transaction-data">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="1.5s"/>
      <animate attributeName="fill" values="#f5f5f7;#f0faff;#f5f5f7" dur="3s" repeatCount="indefinite" begin="1.5s"/>
    </rect>
    <text x="110" y="445" text-anchor="middle" fill="#2c2e3c" font-size="18" font-weight="600">Transactions</text>
  </g>

  <!-- Flow paths with animated dash patterns - Starting from edges of boxes -->
  <path d="M200 140 C 300 140, 350 300, 400 300" fill="none" stroke="#8f76f5" stroke-width="3" class="data-path website-path" stroke-dasharray="1000" stroke-dashoffset="1000">
    <animate attributeName="stroke-dashoffset" from="1000" to="0" dur="5s" begin="0.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0.7;1;0.7" dur="4s" repeatCount="indefinite" begin="5.5s"/>
  </path>

  <path d="M200 290 C 250 290, 350 300, 400 300" fill="none" stroke="#e958a1" stroke-width="3" class="data-path crm-path" stroke-dasharray="1000" stroke-dashoffset="1000">
    <animate attributeName="stroke-dashoffset" from="1000" to="0" dur="4s" begin="1s" fill="freeze"/>
    <animate attributeName="opacity" values="0.7;1;0.7" dur="4s" repeatCount="indefinite" begin="5s"/>
  </path>

  <path d="M200 440 C 300 440, 350 300, 400 300" fill="none" stroke="#4a9eff" stroke-width="3" class="data-path transaction-path" stroke-dasharray="1000" stroke-dashoffset="1000">
    <animate attributeName="stroke-dashoffset" from="1000" to="0" dur="5s" begin="1.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0.7;1;0.7" dur="4s" repeatCount="indefinite" begin="6.5s"/>
  </path>

  <!-- AI Core with pulsing animation - Increased size -->
  <circle cx="400" cy="300" r="100" fill="url(#core-gradient)" class="ai-core">
    <animate attributeName="r" values="95;105;95" dur="8s" repeatCount="indefinite"/>
  </circle>

  <!-- Pulse effect around AI Core - Increased size -->
  <circle cx="400" cy="300" r="105" fill="none" stroke="url(#core-gradient)" stroke-width="2" opacity="0.5">
    <animate attributeName="r" values="105;115;105" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.5;0.2;0.5" dur="4s" repeatCount="indefinite"/>
  </circle>

  <text x="400" y="290" text-anchor="middle" fill="white" font-size="22" font-weight="700">AI Core</text>
  <text x="400" y="320" text-anchor="middle" fill="white" font-size="16" font-weight="500">Data Processing</text>

  <!-- Output path with animated dash pattern -->
  <path d="M500 300 C 550 300, 600 300, 620 300" fill="none" stroke="#2c2e3c" stroke-width="3" class="data-path output-path" stroke-dasharray="1000" stroke-dashoffset="1000">
    <animate attributeName="stroke-dashoffset" from="1000" to="0" dur="3s" begin="6s" fill="freeze"/>
    <animate attributeName="opacity" values="0.7;1;0.7" dur="4s" repeatCount="indefinite" begin="9s"/>
  </path>

  <!-- Output with fade-in animation - Increased size -->
  <rect x="620" y="240" width="150" height="120" rx="10" fill="#2c2e3c" class="data-output" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="2s" begin="7s" fill="freeze"/>
    <animate attributeName="opacity" values="0.8;1;0.8" dur="4s" repeatCount="indefinite" begin="9s"/>
  </rect>
  <text x="695" y="300" text-anchor="middle" fill="white" font-size="18" font-weight="600" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="2s" begin="7.5s" fill="freeze"/>
    Insights
  </text>

  <!-- Data particles with motion animations - Updated to start from edges -->
  <!-- Website data particles -->
  <circle cx="0" cy="0" r="5" fill="#8f76f5" class="data-particle website-particle-1" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 140 C 300 140, 350 300, 400 300" dur="3s" begin="1s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="3s" begin="1s"/>
  </circle>

  <circle cx="0" cy="0" r="5" fill="#8f76f5" class="data-particle website-particle-2" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 140 C 300 140, 350 300, 400 300" dur="3s" begin="2.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="3s" begin="2.5s"/>
  </circle>

  <circle cx="0" cy="0" r="5" fill="#8f76f5" class="data-particle website-particle-3" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 140 C 300 140, 350 300, 400 300" dur="3s" begin="4s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="3s" begin="4s"/>
  </circle>

  <!-- CRM data particles -->
  <circle cx="0" cy="0" r="5" fill="#e958a1" class="data-particle crm-particle-1" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 290 C 250 290, 350 300, 400 300" dur="2.5s" begin="1.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="2.5s" begin="1.5s"/>
  </circle>

  <circle cx="0" cy="0" r="5" fill="#e958a1" class="data-particle crm-particle-2" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 290 C 250 290, 350 300, 400 300" dur="2.5s" begin="3s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="2.5s" begin="3s"/>
  </circle>

  <circle cx="0" cy="0" r="5" fill="#e958a1" class="data-particle crm-particle-3" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 290 C 250 290, 350 300, 400 300" dur="2.5s" begin="4.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="2.5s" begin="4.5s"/>
  </circle>

  <!-- Transaction data particles -->
  <circle cx="0" cy="0" r="5" fill="#4a9eff" class="data-particle transaction-particle-1" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 440 C 300 440, 350 300, 400 300" dur="3s" begin="2s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="3s" begin="2s"/>
  </circle>

  <circle cx="0" cy="0" r="5" fill="#4a9eff" class="data-particle transaction-particle-2" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 440 C 300 440, 350 300, 400 300" dur="3s" begin="3.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="3s" begin="3.5s"/>
  </circle>

  <circle cx="0" cy="0" r="5" fill="#4a9eff" class="data-particle transaction-particle-3" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M200 440 C 300 440, 350 300, 400 300" dur="3s" begin="5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="3s" begin="5s"/>
  </circle>

  <!-- Output particles -->
  <circle cx="0" cy="0" r="4" fill="white" class="data-particle output-particle-1" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M500 300 C 550 300, 600 300, 620 300" dur="2s" begin="6.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="6.5s"/>
  </circle>

  <circle cx="0" cy="0" r="4" fill="white" class="data-particle output-particle-2" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M500 300 C 550 300, 600 300, 620 300" dur="2s" begin="7.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="7.5s"/>
  </circle>

  <!-- Processing Animation in AI Core -->
  <g class="processing-animation">
    <circle cx="400" cy="300" r="50" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="1" stroke-dasharray="10,5">
      <animateTransform attributeName="transform" type="rotate" from="0 400 300" to="360 400 300" dur="20s" repeatCount="indefinite"/>
    </circle>

    <circle cx="400" cy="300" r="65" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" stroke-dasharray="15,10">
      <animateTransform attributeName="transform" type="rotate" from="360 400 300" to="0 400 300" dur="30s" repeatCount="indefinite"/>
    </circle>

    <circle cx="400" cy="300" r="35" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="1" stroke-dasharray="5,3">
      <animateTransform attributeName="transform" type="rotate" from="0 400 300" to="360 400 300" dur="10s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Continuous animation for data flow -->
  <g class="continuous-flow" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="1s" begin="9s" fill="freeze"/>

    <!-- Website data continuous flow -->
    <circle cx="0" cy="0" r="4" fill="#8f76f5" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M200 140 C 300 140, 350 300, 400 300" dur="3s" begin="10s;13s;16s;19s;22s;25s;28s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" begin="10s;13s;16s;19s;22s;25s;28s" repeatCount="indefinite"/>
    </circle>

    <!-- CRM data continuous flow -->
    <circle cx="0" cy="0" r="4" fill="#e958a1" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M200 290 C 250 290, 350 300, 400 300" dur="2.5s" begin="11s;13.5s;16s;18.5s;21s;23.5s;26s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2.5s" begin="11s;13.5s;16s;18.5s;21s;23.5s;26s" repeatCount="indefinite"/>
    </circle>

    <!-- Transaction data continuous flow -->
    <circle cx="0" cy="0" r="4" fill="#4a9eff" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M200 440 C 300 440, 350 300, 400 300" dur="3s" begin="12s;15s;18s;21s;24s;27s;30s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" begin="12s;15s;18s;21s;24s;27s;30s" repeatCount="indefinite"/>
    </circle>

    <!-- Output continuous flow -->
    <circle cx="0" cy="0" r="3" fill="white" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M500 300 C 550 300, 600 300, 620 300" dur="2s" begin="12s;14s;16s;18s;20s;22s;24s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="2s" begin="12s;14s;16s;18s;20s;22s;24s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>