<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1900" height="574" viewBox="0 0 1900 574">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_1" data-name="Rectangle 1" width="1900" height="574" fill="#353642"/>
    </clipPath>
    <clipPath id="clip-logo-amazon">
      <rect width="1900" height="574"/>
    </clipPath>
  </defs>
  <g id="logo-amazon" clip-path="url(#clip-logo-amazon)">
    <g id="logo-amazon-2" data-name="logo-amazon" clip-path="url(#clip-path)">
      <g id="Group_1" data-name="Group 1" transform="translate(0 -133.261)">
        <g id="Amazon_logo" transform="translate(2 135.261)">
          <path id="path8" d="M1050.18,274.555C940,355.767,780.3,399.1,642.8,399.1c-192.795,0-366.359-71.307-497.666-189.9-10.316-9.326-1.073-22.036,11.307-14.773C298.152,276.866,473.368,326.468,654.36,326.468c122.065,0,256.343-25.255,379.811-77.662,18.652-7.923,34.251,12.215,16.011,25.75" transform="translate(126.059 172.271)" fill="#353642" fill-rule="evenodd"/>
          <path id="path10" d="M709.631,231.513c-14.03-17.992-93.1-8.5-128.584-4.292-10.812,1.321-12.462-8.088-2.723-14.856,62.972-44.32,166.3-31.527,178.351-16.671,12.05,14.938-3.136,118.516-62.311,167.952-9.078,7.593-17.744,3.549-13.7-6.52,13.288-33.178,43.082-107.539,28.969-125.613" transform="translate(512.415 162.906)" fill="#353642" fill-rule="evenodd"/>
          <path id="path12" d="M583.213,58.416V15.334A10.629,10.629,0,0,1,594.107,4.44H786.984c6.19,0,11.142,4.457,11.142,10.894V52.226c-.083,6.19-5.282,14.278-14.526,27.07L683.654,222c37.139-.908,76.342,4.622,110.015,23.6,7.593,4.292,9.656,10.564,10.234,16.754v45.97c0,6.272-6.933,13.618-14.2,9.821-59.34-31.115-138.158-34.5-203.771.33-6.685,3.631-13.7-3.631-13.7-9.9V264.91c0-7.015.083-18.982,7.1-29.629L695.126,69.228H594.355c-6.19,0-11.142-4.374-11.142-10.812" transform="translate(512.724 3.978)" fill="#353642" fill-rule="evenodd"/>
          <path id="path14" d="M239.623,329.836h-58.68a11.084,11.084,0,0,1-10.482-9.986V18.691A11.016,11.016,0,0,1,181.768,7.879h54.719a11.051,11.051,0,0,1,10.647,10.069V57.315h1.073c14.277-38.047,41.1-55.792,77.249-55.792,36.727,0,59.67,17.744,76.177,55.792,14.2-38.047,46.465-55.792,81.046-55.792,24.6,0,51.5,10.151,67.924,32.93,18.57,25.337,14.773,62.146,14.773,94.416l-.082,190.07a11.084,11.084,0,0,1-11.307,10.894h-58.6a11.154,11.154,0,0,1-10.564-10.894V159.324c0-12.71,1.155-44.4-1.651-56.452-4.374-20.22-17.5-25.915-34.5-25.915-14.2,0-29.051,9.491-35.076,24.677s-5.447,40.605-5.447,57.69V318.94a11.083,11.083,0,0,1-11.307,10.894h-58.6a11.093,11.093,0,0,1-10.564-10.894L327.6,159.324c0-33.59,5.53-83.027-36.149-83.027-42.174,0-40.523,48.2-40.523,83.027V318.94a11.083,11.083,0,0,1-11.307,10.894" transform="translate(152.733 1.365)" fill="#353642" fill-rule="evenodd"/>
          <path id="path16" d="M841.362,1.524c87.071,0,134.2,74.774,134.2,169.85,0,91.858-52.078,164.733-134.2,164.733-85.5,0-132.051-74.774-132.051-167.952,0-93.756,47.126-166.632,132.051-166.632m.5,61.486c-43.247,0-45.97,58.928-45.97,95.654,0,36.809-.578,115.379,45.475,115.379,45.475,0,47.621-63.384,47.621-102.009,0-25.42-1.073-55.791-8.748-79.891-6.6-20.963-19.725-29.134-38.377-29.134" transform="translate(635.543 1.365)" fill="#353642" fill-rule="evenodd"/>
          <path id="path18" d="M941.93,329.836H883.5a11.154,11.154,0,0,1-10.564-10.894L872.851,17.7a11.1,11.1,0,0,1,11.307-9.82h54.388a11.23,11.23,0,0,1,10.482,8.42V62.351H950.1c16.424-41.185,39.449-60.827,79.972-60.827,26.328,0,52,9.491,68.5,35.489,15.351,24.1,15.351,64.622,15.351,93.756V320.344a11.273,11.273,0,0,1-11.307,9.491h-58.845c-5.364-.413-9.821-4.374-10.4-9.491V156.766c0-32.93,3.8-81.129-36.726-81.129-14.278,0-27.4,9.574-33.921,24.1-8.253,18.4-9.326,36.727-9.326,57.029V318.941a11.283,11.283,0,0,1-11.472,10.894" transform="translate(782.074 1.365)" fill="#353642" fill-rule="evenodd"/>
          <path id="path28" d="M578.641,187.347c0,22.861.578,41.926-10.977,62.229-9.326,16.506-24.182,26.658-40.6,26.658-22.531,0-35.736-17.167-35.736-42.5,0-50.014,44.815-59.093,87.318-59.093v12.71m59.175,143.027a12.253,12.253,0,0,1-13.865,1.4c-19.478-16.176-23.026-23.687-33.673-39.12-32.188,32.848-55.049,42.669-96.727,42.669-49.436,0-87.814-30.454-87.814-91.445,0-47.621,25.75-80.056,62.559-95.9,31.857-14.03,76.342-16.506,110.345-20.385V120c0-13.948,1.073-30.454-7.18-42.5-7.1-10.812-20.8-15.268-32.93-15.268-22.366,0-42.256,11.472-47.126,35.241-.99,5.282-4.869,10.482-10.234,10.729l-56.865-6.108c-4.785-1.073-10.151-4.952-8.748-12.3C428.6,20.8,490.993,0,546.784,0c28.556,0,65.86,7.593,88.391,29.216,28.556,26.658,25.832,62.229,25.832,100.936V221.6c0,27.483,11.39,39.532,22.119,54.388,3.714,5.282,4.539,11.637-.248,15.6-11.967,9.986-33.26,28.556-44.98,38.955l-.082-.165" transform="translate(363.54)" fill="#353642" fill-rule="evenodd"/>
          <path id="path30" d="M172.9,187.347c0,22.861.578,41.926-10.977,62.229-9.326,16.506-24.1,26.658-40.606,26.658-22.531,0-35.654-17.167-35.654-42.5,0-50.014,44.815-59.093,87.236-59.093v12.71m59.175,143.027a12.253,12.253,0,0,1-13.865,1.4c-19.477-16.176-22.944-23.687-33.673-39.12-32.187,32.848-54.966,42.669-96.727,42.669C38.46,335.326,0,304.873,0,243.882c0-47.622,25.832-80.057,62.559-95.9C94.416,133.952,138.9,131.476,172.9,127.6V120c0-13.948,1.073-30.454-7.1-42.5-7.18-10.812-20.88-15.268-32.93-15.268-22.366,0-42.339,11.472-47.208,35.241-.99,5.282-4.869,10.482-10.151,10.729l-56.945-6.11c-4.787-1.073-10.069-4.952-8.748-12.3C22.944,20.8,85.255,0,141.047,0c28.556,0,65.86,7.593,88.391,29.216,28.556,26.658,25.832,62.229,25.832,100.936V221.6c0,27.483,11.389,39.532,22.118,54.388,3.8,5.282,4.622,11.637-.165,15.6-11.967,9.986-33.26,28.556-44.98,38.955l-.165-.165" fill="#353642" fill-rule="evenodd"/>
        </g>
      </g>
    </g>
  </g>
</svg>
