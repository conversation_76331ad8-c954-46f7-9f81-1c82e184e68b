<?php

namespace AdZetaAdmin\Services;

class SEOAnalyzer
{
    private array $suggestions = [];
    private float $score = 0.0;

    /**
     * Analyze content for SEO and return score with suggestions
     */
    public function analyze(array $data): array
    {
        $this->suggestions = [];
        $this->score = 0.0;

        $title = $data['title'] ?? '';
        $content = $data['content'] ?? '';
        $metaDescription = $data['meta_description'] ?? '';
        $focusKeyword = $data['focus_keyword'] ?? '';
        $slug = $data['slug'] ?? '';

        // Clean content for analysis
        $cleanContent = strip_tags($content);
        $wordCount = str_word_count($cleanContent);

        // Analyze different SEO factors
        $this->analyzeTitleSEO($title, $focusKeyword);
        $this->analyzeMetaDescription($metaDescription, $focusKeyword);
        $this->analyzeContentLength($wordCount);
        $this->analyzeKeywordDensity($cleanContent, $focusKeyword);
        $this->analyzeSlugSEO($slug, $focusKeyword);
        $this->analyzeHeadingStructure($content);
        $this->analyzeImageAltText($content);
        $this->analyzeReadability($cleanContent);

        return [
            'score' => round($this->score, 1),
            'suggestions' => $this->suggestions,
            'word_count' => $wordCount,
            'reading_time' => max(1, ceil($wordCount / 200))
        ];
    }

    /**
     * Analyze title SEO
     */
    private function analyzeTitleSEO(string $title, string $focusKeyword): void
    {
        $titleLength = strlen($title);

        // Title length check
        if ($titleLength === 0) {
            $this->addSuggestion('error', 'Title is required for SEO');
        } elseif ($titleLength < 30) {
            $this->addSuggestion('warning', 'Title is too short. Aim for 30-60 characters.');
            $this->score += 5;
        } elseif ($titleLength > 60) {
            $this->addSuggestion('warning', 'Title is too long. Keep it under 60 characters to avoid truncation in search results.');
            $this->score += 5;
        } else {
            $this->addSuggestion('success', 'Title length is optimal (30-60 characters)');
            $this->score += 15;
        }

        // Focus keyword in title
        if (!empty($focusKeyword)) {
            if (stripos($title, $focusKeyword) !== false) {
                $this->addSuggestion('success', 'Great! Your focus keyword appears in the title');
                $this->score += 15;
            } else {
                $this->addSuggestion('warning', 'Consider including your focus keyword in the title');
            }
        }
    }

    /**
     * Analyze meta description
     */
    private function analyzeMetaDescription(string $metaDescription, string $focusKeyword): void
    {
        $descLength = strlen($metaDescription);

        if ($descLength === 0) {
            $this->addSuggestion('warning', 'Meta description is missing. Add one to improve click-through rates.');
        } elseif ($descLength < 120) {
            $this->addSuggestion('warning', 'Meta description is too short. Aim for 120-160 characters.');
            $this->score += 5;
        } elseif ($descLength > 160) {
            $this->addSuggestion('warning', 'Meta description is too long. Keep it under 160 characters.');
            $this->score += 5;
        } else {
            $this->addSuggestion('success', 'Meta description length is optimal (120-160 characters)');
            $this->score += 10;
        }

        // Focus keyword in meta description
        if (!empty($focusKeyword) && !empty($metaDescription)) {
            if (stripos($metaDescription, $focusKeyword) !== false) {
                $this->addSuggestion('success', 'Focus keyword appears in meta description');
                $this->score += 10;
            } else {
                $this->addSuggestion('warning', 'Consider including your focus keyword in the meta description');
            }
        }
    }

    /**
     * Analyze content length
     */
    private function analyzeContentLength(int $wordCount): void
    {
        if ($wordCount < 300) {
            $this->addSuggestion('warning', "Content is too short ({$wordCount} words). Aim for at least 300 words for better SEO.");
        } elseif ($wordCount < 600) {
            $this->addSuggestion('info', "Content length is okay ({$wordCount} words). Consider expanding for better depth.");
            $this->score += 10;
        } else {
            $this->addSuggestion('success', "Excellent content length ({$wordCount} words)");
            $this->score += 15;
        }
    }

    /**
     * Analyze keyword density
     */
    private function analyzeKeywordDensity(string $content, string $focusKeyword): void
    {
        if (empty($focusKeyword) || empty($content)) {
            return;
        }

        $wordCount = str_word_count($content);
        $keywordCount = substr_count(strtolower($content), strtolower($focusKeyword));
        $density = $wordCount > 0 ? ($keywordCount / $wordCount) * 100 : 0;

        if ($keywordCount === 0) {
            $this->addSuggestion('warning', 'Focus keyword not found in content. Include it naturally in your text.');
        } elseif ($density < 0.5) {
            $this->addSuggestion('info', 'Focus keyword density is low. Consider using it a few more times naturally.');
            $this->score += 5;
        } elseif ($density > 3) {
            $this->addSuggestion('warning', 'Focus keyword density is too high. Avoid keyword stuffing.');
            $this->score += 5;
        } else {
            $this->addSuggestion('success', 'Focus keyword density is optimal (0.5-3%)');
            $this->score += 10;
        }

        // Check if keyword appears in first paragraph
        $firstParagraph = $this->getFirstParagraph($content);
        if (stripos($firstParagraph, $focusKeyword) !== false) {
            $this->addSuggestion('success', 'Focus keyword appears in the first paragraph');
            $this->score += 10;
        } else {
            $this->addSuggestion('warning', 'Consider including your focus keyword in the first paragraph');
        }
    }

    /**
     * Analyze slug SEO
     */
    private function analyzeSlugSEO(string $slug, string $focusKeyword): void
    {
        if (empty($slug)) {
            $this->addSuggestion('info', 'URL slug will be auto-generated from title');
            return;
        }

        // Check slug length
        if (strlen($slug) > 75) {
            $this->addSuggestion('warning', 'URL slug is too long. Keep it concise and descriptive.');
        } else {
            $this->score += 5;
        }

        // Check for focus keyword in slug
        if (!empty($focusKeyword) && stripos($slug, $focusKeyword) !== false) {
            $this->addSuggestion('success', 'Focus keyword appears in URL slug');
            $this->score += 10;
        }
    }

    /**
     * Analyze heading structure
     */
    private function analyzeHeadingStructure(string $content): void
    {
        // Count headings
        $h1Count = preg_match_all('/<h1[^>]*>/i', $content);
        $h2Count = preg_match_all('/<h2[^>]*>/i', $content);
        $h3Count = preg_match_all('/<h3[^>]*>/i', $content);

        if ($h1Count > 1) {
            $this->addSuggestion('warning', 'Multiple H1 tags found. Use only one H1 per page.');
        } elseif ($h1Count === 1) {
            $this->score += 5;
        }

        if ($h2Count > 0) {
            $this->addSuggestion('success', 'Good use of H2 headings for content structure');
            $this->score += 5;
        } else {
            $this->addSuggestion('info', 'Consider using H2 headings to structure your content');
        }
    }

    /**
     * Analyze image alt text
     */
    private function analyzeImageAltText(string $content): void
    {
        // Find all images
        preg_match_all('/<img[^>]*>/i', $content, $images);
        
        if (empty($images[0])) {
            return; // No images found
        }

        $totalImages = count($images[0]);
        $imagesWithAlt = 0;

        foreach ($images[0] as $img) {
            if (preg_match('/alt\s*=\s*["\'][^"\']*["\']/', $img)) {
                $imagesWithAlt++;
            }
        }

        if ($imagesWithAlt === $totalImages) {
            $this->addSuggestion('success', 'All images have alt text');
            $this->score += 10;
        } elseif ($imagesWithAlt > 0) {
            $missing = $totalImages - $imagesWithAlt;
            $this->addSuggestion('warning', "{$missing} image(s) missing alt text");
            $this->score += 5;
        } else {
            $this->addSuggestion('warning', 'Images are missing alt text. Add descriptive alt text for accessibility and SEO.');
        }
    }

    /**
     * Analyze readability
     */
    private function analyzeReadability(string $content): void
    {
        if (empty($content)) {
            return;
        }

        $sentences = preg_split('/[.!?]+/', $content);
        $sentences = array_filter($sentences, 'trim');
        $sentenceCount = count($sentences);
        
        if ($sentenceCount === 0) {
            return;
        }

        $wordCount = str_word_count($content);
        $avgWordsPerSentence = $wordCount / $sentenceCount;

        if ($avgWordsPerSentence > 25) {
            $this->addSuggestion('warning', 'Sentences are too long on average. Break them down for better readability.');
        } elseif ($avgWordsPerSentence > 20) {
            $this->addSuggestion('info', 'Consider shortening some sentences for better readability.');
            $this->score += 5;
        } else {
            $this->addSuggestion('success', 'Good sentence length for readability');
            $this->score += 5;
        }

        // Check paragraph length
        $paragraphs = explode("\n\n", $content);
        $longParagraphs = 0;
        
        foreach ($paragraphs as $paragraph) {
            if (str_word_count($paragraph) > 150) {
                $longParagraphs++;
            }
        }

        if ($longParagraphs > 0) {
            $this->addSuggestion('info', 'Some paragraphs are quite long. Consider breaking them up for better readability.');
        } else {
            $this->score += 5;
        }
    }

    /**
     * Add a suggestion
     */
    private function addSuggestion(string $type, string $message): void
    {
        $this->suggestions[] = [
            'type' => $type, // success, warning, error, info
            'message' => $message
        ];
    }

    /**
     * Get first paragraph from content
     */
    private function getFirstParagraph(string $content): string
    {
        // Remove HTML tags and get first 200 characters
        $clean = strip_tags($content);
        $paragraphs = explode("\n", $clean);
        
        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (!empty($paragraph)) {
                return $paragraph;
            }
        }
        
        return substr($clean, 0, 200);
    }

    /**
     * Get SEO score color class
     */
    public static function getScoreColor(float $score): string
    {
        if ($score >= 80) {
            return 'success'; // Green
        } elseif ($score >= 60) {
            return 'warning'; // Yellow
        } else {
            return 'danger'; // Red
        }
    }

    /**
     * Get SEO score description
     */
    public static function getScoreDescription(float $score): string
    {
        if ($score >= 90) {
            return 'Excellent SEO';
        } elseif ($score >= 80) {
            return 'Good SEO';
        } elseif ($score >= 60) {
            return 'Needs Improvement';
        } elseif ($score >= 40) {
            return 'Poor SEO';
        } else {
            return 'Very Poor SEO';
        }
    }
}
