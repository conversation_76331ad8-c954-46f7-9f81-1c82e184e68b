/**
 * Real-Time Error Monitor
 * Shows live errors as they happen with notifications
 */

class ErrorMonitor {
    constructor() {
        this.isEnabled = false;
        this.errorCount = 0;
        this.recentErrors = [];
        this.maxRecentErrors = 10;
        this.notificationContainer = null;
        this.init();
    }

    init() {
        this.createNotificationContainer();
        this.createErrorMonitorWidget();
        this.bindEvents();
        console.log('🔍 Real-time error monitor initialized');
    }

    /**
     * Create floating notification container
     */
    createNotificationContainer() {
        this.notificationContainer = document.createElement('div');
        this.notificationContainer.id = 'errorNotifications';
        this.notificationContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            pointer-events: none;
        `;
        document.body.appendChild(this.notificationContainer);
    }

    /**
     * Create error monitor widget
     */
    createErrorMonitorWidget() {
        const widget = document.createElement('div');
        widget.id = 'errorMonitorWidget';
        widget.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            z-index: 9998;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            display: none;
            pointer-events: auto;
        `;
        widget.innerHTML = `
            <i class="fas fa-bug"></i>
            <span id="errorCount">0</span> errors
        `;
        
        widget.addEventListener('click', () => {
            this.showErrorSummary();
        });

        document.body.appendChild(widget);
        this.errorWidget = widget;
    }

    /**
     * Enable/disable error monitoring
     */
    toggle(enabled) {
        this.isEnabled = enabled;
        
        if (enabled) {
            this.startMonitoring();
        } else {
            this.stopMonitoring();
        }
    }

    /**
     * Start monitoring errors
     */
    startMonitoring() {
        this.isEnabled = true;
        
        // Hook into the API logger
        if (window.AdZetaAPILogger) {
            const originalLogError = window.AdZetaAPILogger.logError.bind(window.AdZetaAPILogger);
            window.AdZetaAPILogger.logError = (requestId, error, context) => {
                const result = originalLogError(requestId, error, context);
                this.onErrorCaptured('API Error', error, context);
                return result;
            };

            const originalLogJavaScriptError = window.AdZetaAPILogger.logJavaScriptError.bind(window.AdZetaAPILogger);
            window.AdZetaAPILogger.logJavaScriptError = (error, context) => {
                const result = originalLogJavaScriptError(error, context);
                this.onErrorCaptured('JavaScript Error', error, context);
                return result;
            };

            const originalLogConsoleError = window.AdZetaAPILogger.logConsoleError.bind(window.AdZetaAPILogger);
            window.AdZetaAPILogger.logConsoleError = (args) => {
                const result = originalLogConsoleError(args);
                this.onErrorCaptured('Console Error', { message: args.join(' ') }, { type: 'console' });
                return result;
            };
        }

        this.showNotification('🔍 Error monitoring enabled', 'info');
        console.log('🔍 Real-time error monitoring started');
    }

    /**
     * Stop monitoring errors
     */
    stopMonitoring() {
        this.isEnabled = false;
        this.showNotification('🔍 Error monitoring disabled', 'info');
        console.log('🔍 Real-time error monitoring stopped');
    }

    /**
     * Handle captured errors
     */
    onErrorCaptured(type, error, context) {
        if (!this.isEnabled) return;

        this.errorCount++;
        
        const errorInfo = {
            type: type,
            message: error.message || error.toString(),
            timestamp: new Date().toISOString(),
            context: context,
            page: window.location.href
        };

        this.recentErrors.unshift(errorInfo);
        if (this.recentErrors.length > this.maxRecentErrors) {
            this.recentErrors = this.recentErrors.slice(0, this.maxRecentErrors);
        }

        this.updateErrorWidget();
        this.showErrorNotification(errorInfo);
    }

    /**
     * Update error widget
     */
    updateErrorWidget() {
        if (this.errorCount > 0) {
            this.errorWidget.style.display = 'block';
            document.getElementById('errorCount').textContent = this.errorCount;
            
            // Pulse animation for new errors
            this.errorWidget.style.transform = 'scale(1.1)';
            setTimeout(() => {
                this.errorWidget.style.transform = 'scale(1)';
            }, 200);
        }
    }

    /**
     * Show error notification
     */
    showErrorNotification(errorInfo) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            background: #dc3545;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideInRight 0.3s ease;
            pointer-events: auto;
            cursor: pointer;
            max-width: 100%;
            word-wrap: break-word;
        `;

        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 4px;">
                <i class="fas fa-exclamation-triangle"></i>
                ${errorInfo.type}
            </div>
            <div style="font-size: 12px; opacity: 0.9;">
                ${this.truncateText(errorInfo.message, 100)}
            </div>
            <div style="font-size: 10px; opacity: 0.7; margin-top: 4px;">
                ${new Date(errorInfo.timestamp).toLocaleTimeString()}
            </div>
        `;

        notification.addEventListener('click', () => {
            this.showErrorDetails(errorInfo);
        });

        this.notificationContainer.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 5000);
    }

    /**
     * Show error summary modal
     */
    showErrorSummary() {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        modal.innerHTML = `
            <div style="background: white; border-radius: 8px; padding: 20px; max-width: 600px; max-height: 80vh; overflow-y: auto;">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px;">
                    <h3 style="margin: 0; color: #dc3545;">
                        <i class="fas fa-bug"></i>
                        Recent Errors (${this.errorCount} total)
                    </h3>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                            style="background: none; border: none; font-size: 20px; cursor: pointer; margin-left: auto;">×</button>
                </div>
                <div>
                    ${this.recentErrors.map(error => `
                        <div style="border: 1px solid #ddd; border-radius: 4px; padding: 10px; margin-bottom: 10px;">
                            <div style="font-weight: bold; color: #dc3545;">${error.type}</div>
                            <div style="margin: 5px 0; font-family: monospace; font-size: 12px;">${error.message}</div>
                            <div style="font-size: 10px; color: #666;">
                                ${new Date(error.timestamp).toLocaleString()} - ${error.page}
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div style="margin-top: 20px; text-align: center;">
                    <button onclick="window.AdZetaNavigation.showView('error-logs')" 
                            style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                        View All Errors in Admin Panel
                    </button>
                    <button onclick="window.AdZetaErrorMonitor.clearErrors()" 
                            style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                        Clear Error Count
                    </button>
                </div>
            </div>
        `;

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        document.body.appendChild(modal);
    }

    /**
     * Show detailed error information
     */
    showErrorDetails(errorInfo) {
        console.group('🔍 Error Details');
        console.log('Type:', errorInfo.type);
        console.log('Message:', errorInfo.message);
        console.log('Timestamp:', errorInfo.timestamp);
        console.log('Context:', errorInfo.context);
        console.log('Page:', errorInfo.page);
        console.groupEnd();

        // Also show in admin panel if available
        if (window.AdZetaNavigation) {
            window.AdZetaNavigation.showView('error-logs');
        }
    }

    /**
     * Clear error count
     */
    clearErrors() {
        this.errorCount = 0;
        this.recentErrors = [];
        this.errorWidget.style.display = 'none';
        this.showNotification('✅ Error count cleared', 'success');
    }

    /**
     * Show general notification
     */
    showNotification(message, type = 'info') {
        const colors = {
            info: '#17a2b8',
            success: '#28a745',
            warning: '#ffc107',
            error: '#dc3545'
        };

        const notification = document.createElement('div');
        notification.style.cssText = `
            background: ${colors[type]};
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 12px;
            pointer-events: auto;
        `;
        notification.textContent = message;

        this.notificationContainer.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    /**
     * Bind events
     */
    bindEvents() {
        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Utility methods
     */
    truncateText(text, length) {
        return text.length > length ? text.substring(0, length) + '...' : text;
    }
}

// Create global instance
window.AdZetaErrorMonitor = new ErrorMonitor();

// Auto-enable monitoring
window.AdZetaErrorMonitor.toggle(true);

console.log('🔍 Real-time error monitor ready. All errors will be captured automatically!');
