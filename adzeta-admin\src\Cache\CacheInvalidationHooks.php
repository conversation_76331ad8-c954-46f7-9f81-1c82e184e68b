<?php

namespace AdZetaAdmin\Cache;

/**
 * Cache Invalidation Hooks
 * Automatically clears cache when content is updated in admin panel
 */
class CacheInvalidationHooks {
    private $frontendCacheManager;
    
    public function __construct() {
        $this->frontendCacheManager = new FrontendCacheManager();
    }
    
    /**
     * Clear cache when blog post is created/updated
     */
    public function onBlogPostSaved($postId, $postData) {
        try {
            // Clear specific post cache
            if (isset($postData['slug'])) {
                $this->frontendCacheManager->clearBlogPostCache($postData['slug']);
            }
            
            // Clear blog list cache (all variations)
            $this->frontendCacheManager->clearBlogListCache();
            
            // Log cache invalidation
            error_log("Cache invalidated for blog post: " . ($postData['slug'] ?? $postId));
            
            return true;
        } catch (\Exception $e) {
            error_log("Cache invalidation failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clear cache when blog post is deleted
     */
    public function onBlogPostDeleted($postId, $slug) {
        try {
            // Clear specific post cache
            $this->frontendCacheManager->clearBlogPostCache($slug);
            
            // Clear blog list cache
            $this->frontendCacheManager->clearBlogListCache();
            
            // Log cache invalidation
            error_log("Cache invalidated for deleted blog post: " . $slug);
            
            return true;
        } catch (\Exception $e) {
            error_log("Cache invalidation failed for deleted post: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clear cache when blog post status changes
     */
    public function onBlogPostStatusChanged($postId, $oldStatus, $newStatus, $slug) {
        try {
            // Clear specific post cache
            $this->frontendCacheManager->clearBlogPostCache($slug);
            
            // Clear blog list cache (affects listing)
            $this->frontendCacheManager->clearBlogListCache();
            
            // Log cache invalidation
            error_log("Cache invalidated for post status change: {$slug} ({$oldStatus} -> {$newStatus})");
            
            return true;
        } catch (\Exception $e) {
            error_log("Cache invalidation failed for status change: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clear cache when category is updated
     */
    public function onCategoryUpdated($categoryId, $categorySlug) {
        try {
            // Clear all blog list cache (categories affect listings)
            $this->frontendCacheManager->clearBlogListCache();
            
            // Log cache invalidation
            error_log("Cache invalidated for category update: " . $categorySlug);
            
            return true;
        } catch (\Exception $e) {
            error_log("Cache invalidation failed for category update: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clear all cache (manual trigger)
     */
    public function clearAllCache() {
        try {
            $this->frontendCacheManager->clearAllCache();
            
            // Log cache invalidation
            error_log("All frontend cache cleared manually");
            
            return true;
        } catch (\Exception $e) {
            error_log("Manual cache clear failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get cache statistics for admin display
     */
    public function getCacheStats() {
        try {
            return $this->frontendCacheManager->getStats();
        } catch (\Exception $e) {
            error_log("Failed to get cache stats: " . $e->getMessage());
            return [
                'total_files' => 0,
                'total_size' => 0,
                'blog_posts_cached' => 0,
                'blog_lists_cached' => 0,
                'last_generated' => null,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Warm cache for critical pages
     */
    public function warmCache($urls = null) {
        if (!$urls) {
            $urls = [
                '/blog/',
                '/blog/category/digital-marketing/',
                '/blog/category/ai-technology/',
                '/blog/category/performance-marketing/'
            ];
        }
        
        $warmed = 0;
        $errors = [];
        
        foreach ($urls as $url) {
            try {
                // Make internal request to generate cache
                $baseUrl = 'http://' . ($_SERVER['HTTP_HOST'] ?? 'localhost');
                $fullUrl = $baseUrl . $url;
                
                $context = stream_context_create([
                    'http' => [
                        'method' => 'GET',
                        'header' => [
                            'User-Agent: AdZeta-CacheWarmer/1.0',
                            'Accept: text/html'
                        ],
                        'timeout' => 30
                    ]
                ]);
                
                $content = file_get_contents($fullUrl, false, $context);
                
                if ($content !== false) {
                    $warmed++;
                } else {
                    $errors[] = "Failed to warm cache for: " . $url;
                }
                
            } catch (\Exception $e) {
                $errors[] = "Error warming cache for {$url}: " . $e->getMessage();
            }
        }
        
        // Log cache warming
        error_log("Cache warming completed: {$warmed} URLs warmed, " . count($errors) . " errors");
        
        return [
            'warmed' => $warmed,
            'total' => count($urls),
            'errors' => $errors
        ];
    }
}
