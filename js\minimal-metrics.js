document.addEventListener('DOMContentLoaded', function() {
    // Initialize counters
    initCounters();
    
    // Initialize mini charts
    initMiniCharts();
    
    // Initialize comparison charts
    initComparisonCharts();
});

// Initialize counters with smooth animation
function initCounters() {
    const counters = document.querySelectorAll('.counter-number');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const unit = counter.getAttribute('data-unit');
        const duration = 2000; // 2 seconds
        const frameRate = 60;
        const frames = duration / (1000 / frameRate);
        const increment = target / frames;
        
        let count = 0;
        
        const updateCount = () => {
            if (count < target) {
                count += increment;
                counter.textContent = Math.floor(count);
                requestAnimationFrame(updateCount);
            } else {
                counter.textContent = target;
            }
        };
        
        // Start animation when element is in viewport
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateCount();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(counter);
    });
}

// Initialize mini charts in metric cards
function initMiniCharts() {
    // ROAS Chart - Upward trend (purple gradient)
    const roasCtx = document.getElementById('roas-chart').getContext('2d');
    const roasGradient = roasCtx.createLinearGradient(0, 0, 0, 100);
    roasGradient.addColorStop(0, 'rgba(143, 118, 245, 0.3)');
    roasGradient.addColorStop(1, 'rgba(143, 118, 245, 0.05)');
    
    new Chart(roasCtx, {
        type: 'line',
        data: {
            labels: ['', '', '', '', '', ''],
            datasets: [{
                data: [10, 15, 25, 30, 45, 60],
                borderColor: '#8f76f5',
                backgroundColor: roasGradient,
                borderWidth: 2,
                pointRadius: 0,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: false },
                y: { display: false }
            },
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            }
        }
    });

    // CAC Chart - Downward trend (pink gradient)
    const cacCtx = document.getElementById('cac-chart').getContext('2d');
    const cacGradient = cacCtx.createLinearGradient(0, 0, 0, 100);
    cacGradient.addColorStop(0, 'rgba(233, 88, 161, 0.3)');
    cacGradient.addColorStop(1, 'rgba(233, 88, 161, 0.05)');
    
    new Chart(cacCtx, {
        type: 'line',
        data: {
            labels: ['', '', '', '', '', ''],
            datasets: [{
                data: [60, 50, 40, 35, 25, 20],
                borderColor: '#e958a1',
                backgroundColor: cacGradient,
                borderWidth: 2,
                pointRadius: 0,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: false },
                y: { display: false }
            },
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            }
        }
    });

    // Revenue Chart - Upward trend (purple gradient)
    const revenueCtx = document.getElementById('revenue-chart').getContext('2d');
    const revenueGradient = revenueCtx.createLinearGradient(0, 0, 0, 100);
    revenueGradient.addColorStop(0, 'rgba(143, 118, 245, 0.3)');
    revenueGradient.addColorStop(1, 'rgba(143, 118, 245, 0.05)');
    
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: ['', '', '', '', '', ''],
            datasets: [{
                data: [15, 25, 30, 40, 50, 65],
                borderColor: '#8f76f5',
                backgroundColor: revenueGradient,
                borderWidth: 2,
                pointRadius: 0,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: false },
                y: { display: false }
            },
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            }
        }
    });

    // Ad Waste Chart - Downward trend (pink gradient)
    const adWasteCtx = document.getElementById('ad-waste-chart').getContext('2d');
    const adWasteGradient = adWasteCtx.createLinearGradient(0, 0, 0, 100);
    adWasteGradient.addColorStop(0, 'rgba(233, 88, 161, 0.3)');
    adWasteGradient.addColorStop(1, 'rgba(233, 88, 161, 0.05)');
    
    new Chart(adWasteCtx, {
        type: 'line',
        data: {
            labels: ['', '', '', '', '', ''],
            datasets: [{
                data: [70, 60, 45, 35, 25, 15],
                borderColor: '#e958a1',
                backgroundColor: adWasteGradient,
                borderWidth: 2,
                pointRadius: 0,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: false },
                y: { display: false }
            },
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            }
        }
    });
}

// Initialize comparison charts
function initComparisonCharts() {
    // ROAS Comparison Chart
    const comparisonCtx = document.getElementById('comparison-chart').getContext('2d');
    new Chart(comparisonCtx, {
        type: 'bar',
        data: {
            labels: ['Traditional', 'Adzeta AI'],
            datasets: [{
                label: 'ROAS Comparison',
                data: [1, 4],
                backgroundColor: [
                    'rgba(149, 165, 166, 0.5)',
                    'rgba(143, 118, 245, 0.5)'
                ],
                borderColor: [
                    'rgba(149, 165, 166, 0.8)',
                    'rgba(143, 118, 245, 0.8)'
                ],
                borderWidth: 1,
                borderRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `ROAS: ${context.raw}x`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        drawBorder: false,
                        color: 'rgba(200, 200, 200, 0.1)'
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                },
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            }
        }
    });

    // Ad Waste Donut Chart
    const donutCtx = document.getElementById('donut-chart').getContext('2d');
    new Chart(donutCtx, {
        type: 'doughnut',
        data: {
            labels: ['Wasted Spend', 'Effective Spend'],
            datasets: [{
                data: [42, 58],
                backgroundColor: [
                    'rgba(233, 88, 161, 0.5)',
                    'rgba(143, 118, 245, 0.5)'
                ],
                borderColor: [
                    'rgba(233, 88, 161, 0.8)',
                    'rgba(143, 118, 245, 0.8)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '75%',
            plugins: {
                legend: { display: false },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.raw}%`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeOutQuart'
            }
        }
    });
}
