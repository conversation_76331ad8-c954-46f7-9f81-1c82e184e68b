<?php

namespace AdZetaAdmin\Frontend;

/**
 * WordPress-Inspired Frontend Router
 * Handles URL routing with template selection and caching
 */
class Router {
    private $cacheManager;
    private $templateEngine;
    private $routes = [];

    public function __construct() {
        require_once __DIR__ . '/../Cache/FrontendCacheManager.php';
        require_once __DIR__ . '/TemplateEngine.php';

        $this->cacheManager = new \AdZetaAdmin\Cache\FrontendCacheManager();
        $this->templateEngine = new TemplateEngine();

        $this->registerRoutes();
    }

    /**
     * Register all frontend routes
     */
    private function registerRoutes() {
        // Blog routes
        $this->routes = [
            // Blog listing
            [
                'pattern' => '/^\/blog\/?$/',
                'handler' => 'handleBlogList',
                'cache_key' => 'blog-list'
            ],

            // Blog listing with pagination
            [
                'pattern' => '/^\/blog\/page\/(\d+)\/?$/',
                'handler' => 'handleBlogList',
                'cache_key' => 'blog-list-page-{1}'
            ],

            // Category listing
            [
                'pattern' => '/^\/blog\/category\/([^\/]+)\/?$/',
                'handler' => 'handleCategoryList',
                'cache_key' => 'blog-category-{1}'
            ],

            // Category with pagination
            [
                'pattern' => '/^\/blog\/category\/([^\/]+)\/page\/(\d+)\/?$/',
                'handler' => 'handleCategoryList',
                'cache_key' => 'blog-category-{1}-page-{2}'
            ],

            // Tag listing
            [
                'pattern' => '/^\/blog\/tag\/([^\/]+)\/?$/',
                'handler' => 'handleTagList',
                'cache_key' => 'blog-tag-{1}'
            ],

            // Individual blog post
            [
                'pattern' => '/^\/blog\/([^\/]+)\/?$/',
                'handler' => 'handleBlogPost',
                'cache_key' => 'blog-post-{1}'
            ]
        ];
    }

    /**
     * Handle incoming request
     */
    public function handleRequest($uri = null) {
        if ($uri === null) {
            $uri = $_SERVER['REQUEST_URI'] ?? '/';
        }

        // Remove query string
        $uri = strtok($uri, '?');

        // Find matching route
        foreach ($this->routes as $route) {
            if (preg_match($route['pattern'], $uri, $matches)) {
                return $this->executeRoute($route, $matches, $uri);
            }
        }

        // No route found
        return $this->handle404();
    }

    /**
     * Execute matched route
     */
    private function executeRoute($route, $matches, $uri) {
        // Generate cache key
        $cacheKey = $route['cache_key'];
        for ($i = 1; $i < count($matches); $i++) {
            $cacheKey = str_replace('{' . $i . '}', $matches[$i], $cacheKey);
        }

        // Try to serve from cache first
        if ($this->cacheManager->isEnabled()) {
            $cachedContent = $this->getCachedContent($cacheKey);
            if ($cachedContent !== false) {
                $this->serveCachedContent($cachedContent, $cacheKey);
                return true;
            }
        }

        // Cache miss - generate content
        header('X-Cache: MISS');
        header('X-Cache-Key: ' . $cacheKey);

        // Start output buffering
        ob_start();

        // Execute route handler
        $handler = $route['handler'];
        $result = $this->$handler($matches, $uri);

        if ($result) {
            // Cache the generated content
            $content = ob_get_contents();
            if ($content && $this->cacheManager->isEnabled()) {
                $this->cacheContent($cacheKey, $content);
            }

            ob_end_flush();
            return true;
        }

        ob_end_clean();
        return false;
    }

    /**
     * Handle blog listing
     */
    private function handleBlogList($matches, $uri) {
        $page = 1;
        $category = null;

        // Extract page number if present
        if (count($matches) > 1 && is_numeric($matches[1])) {
            $page = (int)$matches[1];
        }

        // Get query parameters
        $search = $_GET['search'] ?? null;

        return $this->templateEngine->renderBlogList([
            'page' => $page,
            'category' => $category,
            'search' => $search,
            'template' => $this->getSelectedTemplate('blog-list')
        ]);
    }

    /**
     * Handle category listing
     */
    private function handleCategoryList($matches, $uri) {
        $category = $matches[1] ?? null;
        $page = $matches[2] ?? 1;

        if (!$category) {
            return false;
        }

        return $this->templateEngine->renderBlogList([
            'page' => (int)$page,
            'category' => $category,
            'search' => null,
            'template' => $this->getSelectedTemplate('blog-category')
        ]);
    }

    /**
     * Handle tag listing
     */
    private function handleTagList($matches, $uri) {
        $tag = $matches[1] ?? null;

        if (!$tag) {
            return false;
        }

        return $this->templateEngine->renderBlogList([
            'page' => 1,
            'category' => null,
            'tag' => $tag,
            'search' => null,
            'template' => $this->getSelectedTemplate('blog-tag')
        ]);
    }

    /**
     * Handle individual blog post
     */
    private function handleBlogPost($matches, $uri) {
        $slug = $matches[1] ?? null;

        if (!$slug) {
            return false;
        }

        // Get post data using BlogDatabase (simpler approach)
        require_once __DIR__ . '/../../../includes/BlogDatabase.php';
        $post = getBlogPostBySlug($slug);

        if (!$post) {
            return $this->handle404();
        }

        return $this->templateEngine->renderBlogPost([
            'post' => $post,
            'template' => $this->getSelectedTemplate('blog-post', $post['template'] ?? null)
        ]);
    }

    /**
     * Get selected template for content type
     */
    private function getSelectedTemplate($type, $postTemplate = null) {
        // For blog posts, check if specific template is set
        if ($type === 'blog-post' && $postTemplate) {
            return $postTemplate;
        }

        // Load template settings
        $templateSettings = $this->loadTemplateSettings();

        return $templateSettings[$type] ?? $this->getDefaultTemplate($type);
    }

    /**
     * Load template settings from admin
     */
    private function loadTemplateSettings() {
        $settingsFile = __DIR__ . '/../../cache/template-settings.json';

        if (file_exists($settingsFile)) {
            $settings = json_decode(file_get_contents($settingsFile), true);
            return $settings ?: [];
        }

        return [];
    }

    /**
     * Get default template for content type
     */
    private function getDefaultTemplate($type) {
        $defaults = [
            'blog-list' => 'modern-grid',
            'blog-category' => 'modern-grid',
            'blog-tag' => 'modern-grid',
            'blog-post' => 'professional-enhanced'
        ];

        return $defaults[$type] ?? 'default';
    }

    /**
     * Get cached content
     */
    private function getCachedContent($cacheKey) {
        $cacheFile = $this->getCacheFilePath($cacheKey);

        if (!file_exists($cacheFile)) {
            return false;
        }

        // Check cache validity
        $maxAge = $this->getCacheMaxAge();
        $cacheAge = time() - filemtime($cacheFile);

        if ($cacheAge > $maxAge) {
            // Cache expired
            unlink($cacheFile);
            if (file_exists($cacheFile . '.gz')) {
                unlink($cacheFile . '.gz');
            }
            return false;
        }

        return file_get_contents($cacheFile);
    }

    /**
     * Serve cached content with proper headers
     */
    private function serveCachedContent($content, $cacheKey) {
        // Set cache headers
        header('Content-Type: text/html; charset=UTF-8');
        header('X-Cache: HIT');
        header('X-Cache-Key: ' . $cacheKey);
        header('X-Served-By: AdZeta-Router-Cache');

        // Check if gzip version exists and client accepts it
        $cacheFile = $this->getCacheFilePath($cacheKey);
        if ($this->clientAcceptsGzip() && file_exists($cacheFile . '.gz')) {
            header('Content-Encoding: gzip');
            $content = file_get_contents($cacheFile . '.gz');
        }

        // Set browser cache headers
        $maxAge = $this->getBrowserCacheMaxAge();
        header('Cache-Control: public, max-age=' . $maxAge);
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT');

        echo $content;
    }

    /**
     * Cache generated content
     */
    private function cacheContent($cacheKey, $content) {
        $cacheFile = $this->getCacheFilePath($cacheKey);
        $cacheDir = dirname($cacheFile);

        // Create cache directory if needed
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }

        // Add cache generation comment
        $content = $this->addCacheComment($content, $cacheKey);

        // Save regular file
        file_put_contents($cacheFile, $content);

        // Save gzipped version
        if ($this->isGzipEnabled()) {
            file_put_contents($cacheFile . '.gz', gzencode($content, 9));
        }
    }

    /**
     * Get cache file path
     */
    private function getCacheFilePath($cacheKey) {
        return __DIR__ . '/../../cache/static/router/' . $cacheKey . '.html';
    }

    /**
     * Add cache generation comment
     */
    private function addCacheComment($content, $cacheKey) {
        $comment = sprintf(
            "<!-- Cached by AdZeta Router | Key: %s | Generated: %s -->",
            $cacheKey,
            date('Y-m-d H:i:s')
        );

        // Insert after <html> tag if it exists
        if (strpos($content, '<html') !== false) {
            $content = preg_replace('/(<html[^>]*>)/', '$1' . "\n" . $comment, $content, 1);
        } else {
            $content = $comment . "\n" . $content;
        }

        return $content;
    }

    /**
     * Handle 404 errors
     */
    private function handle404() {
        header('HTTP/1.1 404 Not Found');

        if (file_exists(__DIR__ . '/../../404.php')) {
            include __DIR__ . '/../../404.php';
        } else {
            echo '<h1>404 - Page Not Found</h1>';
        }

        return false;
    }

    /**
     * Helper methods
     */
    private function getCacheMaxAge() {
        $settings = $this->cacheManager->getSettings();
        return $settings['static_cache_duration'] ?? 14400; // 4 hours
    }

    private function getBrowserCacheMaxAge() {
        $settings = $this->cacheManager->getSettings();
        return $settings['browser_cache_duration'] ?? 86400; // 1 day
    }

    private function isGzipEnabled() {
        $settings = $this->cacheManager->getSettings();
        return $settings['static_cache_gzip'] ?? true;
    }

    private function clientAcceptsGzip() {
        return isset($_SERVER['HTTP_ACCEPT_ENCODING']) &&
               strpos($_SERVER['HTTP_ACCEPT_ENCODING'], 'gzip') !== false;
    }

    /**
     * Clear cache for specific pattern
     */
    public function clearCache($pattern = '*') {
        $cacheDir = __DIR__ . '/../../cache/static/router/';

        if ($pattern === '*') {
            // Clear all cache
            $files = glob($cacheDir . '*.html');
        } else {
            // Clear specific pattern
            $files = glob($cacheDir . $pattern . '.html');
        }

        $cleared = 0;
        foreach ($files as $file) {
            if (unlink($file)) {
                $cleared++;
            }
            // Also remove gzip version
            if (file_exists($file . '.gz')) {
                unlink($file . '.gz');
            }
        }

        return $cleared;
    }
}
