/**
 * Professional Enterprise Metrics - Consolidated JS
 * Combines functionality from integrated-metrics.js and clean-card-metrics.js
 */
document.addEventListener('DOMContentLoaded', function() {
    // Check if metrics section exists
    if (!document.querySelector('.metrics-section')) return;

    // Initialize on scroll
    window.addEventListener('scroll', checkAndInitialize);

    // Also check on initial load
    setTimeout(checkAndInitialize, 500);

    /**
     * Animate counter numbers with smooth animation
     */
    function animateCounters() {
        const counters = document.querySelectorAll('.counter-number');
        const speed = 1500; // Animation duration in ms

        counters.forEach(counter => {
            const target = parseFloat(counter.getAttribute('data-target'));
            const unit = counter.getAttribute('data-unit') || '';
            let count = 0;
            const increment = target / (speed / 16); // 60fps

            const updateCount = () => {
                if (count < target) {
                    count += increment;
                    // Don't exceed the target
                    if (count > target) count = target;

                    // Format the display based on unit
                    counter.textContent = Math.floor(count);

                    requestAnimationFrame(updateCount);
                } else {
                    counter.textContent = target;
                }
            };

            updateCount();
        });
    }

    /**
     * Initialize SVG-based mini charts
     * This uses simple SVG paths instead of Chart.js for better performance
     */
    function initSvgCharts() {
        // The SVG paths are already defined in the HTML
        // This function can be used to add any dynamic behavior to the charts
        
        // Add animation classes to make the charts appear with a nice animation
        const charts = document.querySelectorAll('.metric-graph');
        charts.forEach(chart => {
            chart.classList.add('animate-in');
        });
    }

    /**
     * Check if element is in viewport
     */
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
        );
    }

    /**
     * Check if metrics section is in viewport and initialize
     */
    function checkAndInitialize() {
        const metricsSection = document.querySelector('.metrics-section');

        if (metricsSection && isInViewport(metricsSection)) {
            // Initialize SVG charts
            initSvgCharts();

            // Animate counters
            animateCounters();

            // Add animation to metric cards
            animateMetricCards();

            // Remove scroll listener once initialized
            window.removeEventListener('scroll', checkAndInitialize);
        }
    }

    /**
     * Animate metric cards with a staggered entrance
     */
    function animateMetricCards() {
        const cards = document.querySelectorAll('.metric-card');
        
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('animate-in');
            }, 100 * index); // Stagger the animations
        });
    }

    // Add hover effects for metric cards
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('hover');
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });
    });
});
