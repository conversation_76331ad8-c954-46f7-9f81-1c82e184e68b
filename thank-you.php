<?php include 'header-light.php'; ?>
        <!-- Thank You Page Styles - Inline for better performance -->
        <style>
        /* Enhanced animations and hover effects */
        @keyframes pulse-ring {
            0% {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 0;
            }
            10% {
                transform: translate(-50%, -50%) scale(0.9);
                opacity: 0.6;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0.3;
            }
            100% {
                transform: translate(-50%, -50%) scale(1.8);
                opacity: 0;
            }
        }
        @keyframes pulse-step {
            0% { transform: scale(1); opacity: 0.6; }
            50% { transform: scale(1.15); opacity: 0.3; }
            100% { transform: scale(1.3); opacity: 0; }
        }
        .modern-step-card:hover {
            transform: translateY(-8px) scale(1.02) !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
            border-color: rgba(233, 88, 161, 0.3) !important;
        }
        .modern-step-card:hover .modern-step-number { transform: scale(1.1); }
        .modern-step-card:hover .step-icon i { transform: scale(1.1); }
        .modern-btn-primary:hover {
            transform: translateY(-4px) scale(1.02) !important;
            box-shadow: 0 15px 40px rgba(233, 88, 161, 0.4) !important;
        }
        .modern-btn-primary:hover .position-absolute { opacity: 1 !important; }
        .modern-btn-secondary:hover {
            transform: translateY(-4px) scale(1.02) !important;
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.08) !important;
            border-color: rgba(233, 88, 161, 0.3) !important;
            background: rgba(255, 255, 255, 1) !important;
            color: #e958a1 !important;
        }
        .contact-card:hover {
            transform: translateY(-6px) scale(1.02) !important;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
            border-color: rgba(233, 88, 161, 0.2) !important;
        }
        .contact-card:hover .contact-icon-large {
            transform: scale(1.1);
            box-shadow: 0 12px 30px rgba(233, 88, 161, 0.4);
        }
        .contact-card:hover h6 { color: #e958a1 !important; }
        </style>
        <!-- end header -->
   <!-- start page title -->
     
		
		<!-- start page title -->

<!-- end page title -->

        <!-- end page title -->

<!-- start section: Thank You Content -->
<section class="big-section position-relative overflow-hidden top-space-margin" style="background: linear-gradient(135deg, #fafbff 0%, #f5f7ff 50%, #f0f4fd 100%);">
    <!-- Modern background elements -->
    <div class="position-absolute w-100 h-100" style="top: 0; left: 0; z-index: 0;">
        <div class="position-absolute" style="top: -10%; right: -10%; width: 400px; height: 400px; background: radial-gradient(circle, rgba(233, 88, 161, 0.06) 0%, transparent 70%); border-radius: 50%; filter: blur(60px);"></div>
        <div class="position-absolute" style="bottom: -10%; left: -10%; width: 500px; height: 500px; background: radial-gradient(circle, rgba(143, 118, 245, 0.04) 0%, transparent 70%); border-radius: 50%; filter: blur(80px);"></div>
        <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%); width: 600px; height: 600px; background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%); border-radius: 50%; filter: blur(100px);"></div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <div class="row justify-content-center">
            <div class="col-lg-11 col-xl-10">
                <!-- Modern Success Card -->
                <div class="modern-success-card position-relative" style="background: rgba(255, 255, 255, 0.85); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border-radius: 24px; padding: 60px 40px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08), 0 8px 25px rgba(0, 0, 0, 0.04); border: 1px solid rgba(255, 255, 255, 0.3); margin-bottom: 40px;" data-anime='{ "opacity": [0, 1], "translateY": [30, 0], "duration": 400, "delay": 100, "easing": "easeOutQuad" }'>

                    <!-- Enhanced Success Icon -->
                    <div class="text-center mb-5" data-anime='{ "scale": [0, 1], "opacity": [0, 1], "duration": 300, "delay": 300, "easing": "easeOutBack" }'>
                        <div class="modern-success-icon position-relative d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
                            <!-- Enhanced animated background rings - stable positioning -->
                            <div class="position-absolute rounded-circle" style="top: 50%; left: 50%; width: 120px; height: 120px; background: radial-gradient(circle, rgba(46, 204, 113, 0.2) 0%, rgba(46, 204, 113, 0.1) 50%, transparent 70%); animation: pulse-ring 2.5s infinite;"></div>
                            <div class="position-absolute rounded-circle" style="top: 50%; left: 50%; width: 120px; height: 120px; background: radial-gradient(circle, rgba(46, 204, 113, 0.15) 0%, rgba(46, 204, 113, 0.05) 50%, transparent 70%); animation: pulse-ring 2.5s infinite 0.8s;"></div>
                            <div class="position-absolute rounded-circle" style="top: 50%; left: 50%; width: 120px; height: 120px; background: radial-gradient(circle, rgba(46, 204, 113, 0.1) 0%, rgba(46, 204, 113, 0.03) 50%, transparent 70%); animation: pulse-ring 2.5s infinite 1.6s;"></div>

                            <!-- Main success checkmark - stays perfectly centered -->
                            <div class="success-checkmark-modern position-relative" style="width: 70px; height: 70px; z-index: 10;" data-anime='{ "scale": [0, 1], "duration": 300, "delay": 500, "easing": "easeOutBack" }'>
                                <!-- Background shadow - subtle and centered -->
                                <div class="position-absolute rounded-circle" style="top: 3px; left: 3px; width: 70px; height: 70px; background: rgba(46, 204, 113, 0.15); filter: blur(6px); z-index: 1;"></div>
                                <!-- Main icon circle -->
                                <div class="check-icon-modern d-flex align-items-center justify-content-center position-relative" style="width: 70px; height: 70px; border-radius: 50%; background: linear-gradient(135deg, #2ecc71, #27ae60); box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3); z-index: 2;">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" style="color: white;">
                                        <path stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" d="M20 6L9 17l-5-5"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Main Message -->
                    <div class="text-center mb-5" data-anime='{ "el": "childs", "opacity": [0, 1], "translateY": [20, 0], "duration": 300, "delay": 600, "staggervalue": 100, "easing": "easeOutQuad" }'>
                        <h1 class="alt-font fw-700 mb-4" style="font-size: 42px; line-height: 1.2; background: linear-gradient(135deg, #2c2e3c, #4a4d5c); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; letter-spacing: -0.02em;">Request Received Successfully!</h1>
                        <h2 class="alt-font fw-600 text-dark-gray mb-4" style="font-size: 28px; letter-spacing: -0.01em;">We'll Review Your Information Shortly</h2>
                        <p class="fs-18 mb-0" style="color: #6c757d; line-height: 1.6; max-width: 650px; margin: 0 auto;">Thank you for your interest in AdZeta's AI-powered ValueGap Audit. Our expert team will review your request and reach out to discuss how we can help optimize your advertising performance.</p>
                    </div>

                </div>

                <!-- Modern What Happens Next Section -->
                <div class="modern-process-section position-relative" style="background: rgba(255, 255, 255, 0.6); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 20px; padding: 50px 40px; border: 1px solid rgba(255, 255, 255, 0.4); box-shadow: 0 15px 40px rgba(0, 0, 0, 0.06);" data-anime='{ "opacity": [0, 1], "translateY": [30, 0], "duration": 400, "delay": 900, "easing": "easeOutQuad" }'>

                    <!-- Section Header -->
                    <div class="text-center mb-5" data-anime='{ "opacity": [0, 1], "translateY": [20, 0], "duration": 300, "delay": 1100, "easing": "easeOutQuad" }'>
                        <h3 class="alt-font fw-700 text-dark-gray mb-4" style="font-size: 32px; letter-spacing: -0.01em;">What Happens Next?</h3>
                    </div>

                    <!-- Modern Process Steps -->
                    <div class="row g-4" data-anime='{ "el": "childs", "opacity": [0, 1], "translateY": [30, 0], "duration": 300, "delay": 1300, "staggervalue": 100, "easing": "easeOutQuad" }'>
                        <!-- Step 1: Data Review -->
                        <div class="col-lg-4 col-md-6">
                            <div class="modern-step-card position-relative h-100" style="background: rgba(255, 255, 255, 0.8); border-radius: 16px; padding: 35px 25px; text-align: center; transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1); border: 1px solid rgba(233, 88, 161, 0.1); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.04);">

                                <!-- Modern Step Number -->
                                <div class="modern-step-number position-relative mb-4" style="width: 70px; height: 70px; margin: 0 auto;">
                                    <div class="position-absolute w-100 h-100 rounded-circle" style="background: linear-gradient(135deg, #e958a1, #f45888); opacity: 0.1; animation: pulse-step 3s infinite;"></div>
                                    <div class="position-absolute w-100 h-100 rounded-circle d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #e958a1, #f45888); box-shadow: 0 8px 20px rgba(233, 88, 161, 0.3);">
                                        <span class="fw-700 text-white" style="font-size: 24px;">1</span>
                                    </div>
                                </div>

                                <!-- Step Icon -->
                                <div class="step-icon mb-3">
                                    <i class="bi bi-clipboard-check" style="font-size: 28px; color: #e958a1;"></i>
                                </div>

                                <!-- Step Content -->
                                <h5 class="alt-font fw-700 text-dark-gray mb-3" style="font-size: 20px;">Request Review</h5>
                                <p class="fs-15 mb-0" style="color: #6c757d; line-height: 1.6;">Our team reviews your audit request and prepares for the initial consultation</p>

                                <!-- Hover Effect Border -->
                                <div class="position-absolute w-100 h-100 rounded-circle" style="top: 0; left: 0; border: 2px solid transparent; transition: border-color 0.3s ease; pointer-events: none;"></div>
                            </div>
                        </div>

                        <!-- Step 2: Custom Report -->
                        <div class="col-lg-4 col-md-6">
                            <div class="modern-step-card position-relative h-100" style="background: rgba(255, 255, 255, 0.8); border-radius: 16px; padding: 35px 25px; text-align: center; transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1); border: 1px solid rgba(143, 118, 245, 0.1); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.04);">

                                <!-- Modern Step Number -->
                                <div class="modern-step-number position-relative mb-4" style="width: 70px; height: 70px; margin: 0 auto;">
                                    <div class="position-absolute w-100 h-100 rounded-circle" style="background: linear-gradient(135deg, #8f76f5, #a084f7); opacity: 0.1; animation: pulse-step 3s infinite 1s;"></div>
                                    <div class="position-absolute w-100 h-100 rounded-circle d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #8f76f5, #a084f7); box-shadow: 0 8px 20px rgba(143, 118, 245, 0.3);">
                                        <span class="fw-700 text-white" style="font-size: 24px;">2</span>
                                    </div>
                                </div>

                                <!-- Step Icon -->
                                <div class="step-icon mb-3">
                                    <i class="bi bi-chat-dots" style="font-size: 28px; color: #8f76f5;"></i>
                                </div>

                                <!-- Step Content -->
                                <h5 class="alt-font fw-700 text-dark-gray mb-3" style="font-size: 20px;">Initial Consultation</h5>
                                <p class="fs-15 mb-0" style="color: #6c757d; line-height: 1.6;">We schedule a consultation to understand your goals and discuss audit scope</p>

                                <!-- Hover Effect Border -->
                                <div class="position-absolute w-100 h-100 rounded-circle" style="top: 0; left: 0; border: 2px solid transparent; transition: border-color 0.3s ease; pointer-events: none;"></div>
                            </div>
                        </div>

                        <!-- Step 3: Expert Contact -->
                        <div class="col-lg-4 col-md-12">
                            <div class="modern-step-card position-relative h-100" style="background: rgba(255, 255, 255, 0.8); border-radius: 16px; padding: 35px 25px; text-align: center; transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1); border: 1px solid rgba(46, 204, 113, 0.1); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.04);">

                                <!-- Modern Step Number -->
                                <div class="modern-step-number position-relative mb-4" style="width: 70px; height: 70px; margin: 0 auto;">
                                    <div class="position-absolute w-100 h-100 rounded-circle" style="background: linear-gradient(135deg, #2ecc71, #27ae60); opacity: 0.1; animation: pulse-step 3s infinite 2s;"></div>
                                    <div class="position-absolute w-100 h-100 rounded-circle d-flex align-items-center justify-content-center" style="background: linear-gradient(135deg, #2ecc71, #27ae60); box-shadow: 0 8px 20px rgba(46, 204, 113, 0.3);">
                                        <span class="fw-700 text-white" style="font-size: 24px;">3</span>
                                    </div>
                                </div>

                                <!-- Step Icon -->
                                <div class="step-icon mb-3">
                                    <i class="bi bi-award" style="font-size: 28px; color: #2ecc71;"></i>
                                </div>

                                <!-- Step Content -->
                                <h5 class="alt-font fw-700 text-dark-gray mb-3" style="font-size: 20px;">Audit Delivery</h5>
                                <p class="fs-15 mb-0" style="color: #6c757d; line-height: 1.6;">We conduct the audit and deliver actionable insights to optimize your campaigns</p>

                                <!-- Hover Effect Border -->
                                <div class="position-absolute w-100 h-100 rounded-circle" style="top: 0; left: 0; border: 2px solid transparent; transition: border-color 0.3s ease; pointer-events: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Modern Call to Actions -->
                <div class="text-center mt-5 pt-4" data-anime='{ "opacity": [0, 1], "translateY": [20, 0], "duration": 300, "delay": 1800, "easing": "easeOutQuad" }'>
                    <div class="d-flex flex-column flex-md-row gap-4 justify-content-center align-items-center mb-5" data-anime='{ "el": "childs", "opacity": [0, 1], "translateY": [15, 0], "duration": 250, "delay": 2000, "staggervalue": 100, "easing": "easeOutQuad" }'>
                        <a href="/platform" class="btn btn-large position-relative overflow-hidden modern-btn-primary" style="background: linear-gradient(135deg, #e958a1, #f45888); color: white; border: none; border-radius: 16px; padding: 18px 36px; font-weight: 700; text-decoration: none; transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1); box-shadow: 0 10px 30px rgba(233, 88, 161, 0.3); font-size: 16px;">
                            <span class="position-relative" style="z-index: 2;">
                                <i class="bi bi-rocket-takeoff me-2"></i>Explore Our Platform
                            </span>
                            <div class="position-absolute w-100 h-100" style="top: 0; left: 0; background: linear-gradient(135deg, #f45888, #e958a1); opacity: 0; transition: opacity 0.3s ease; z-index: 1; border-radius: 16px;"></div>
                        </a>

                        <a href="/case-studies" class="btn btn-large position-relative modern-btn-secondary" style="background: rgba(255, 255, 255, 0.9); color: #2c2e3c; border: 2px solid rgba(233, 88, 161, 0.15); border-radius: 16px; padding: 16px 36px; font-weight: 700; text-decoration: none; transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.04); font-size: 16px;">
                            <i class="bi bi-graph-up-arrow me-2"></i>View Success Stories
                        </a>
                    </div>

                    <!-- Modern Contact Information -->
                    <div class="modern-contact-info position-relative" style="background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(15px); -webkit-backdrop-filter: blur(15px); border-radius: 20px; padding: 40px; border: 1px solid rgba(255, 255, 255, 0.4); box-shadow: 0 12px 30px rgba(0, 0, 0, 0.06); margin-top: 20px;" data-anime='{ "opacity": [0, 1], "translateY": [20, 0], "duration": 300, "delay": 2300, "easing": "easeOutQuad" }'>

                        <!-- Header Section -->
                        <div class="text-center mb-4" data-anime='{ "opacity": [0, 1], "translateY": [15, 0], "duration": 250, "delay": 2500, "easing": "easeOutQuad" }'>
                            <h5 class="alt-font fw-700 text-dark-gray mb-2" style="font-size: 20px; letter-spacing: -0.01em;">Need Immediate Assistance?</h5>
                            <p class="fs-15 mb-0" style="color: #6c757d;">Our team is here to help you with any questions</p>
                        </div>

                        <!-- Contact Cards -->
                        <div class="row g-3 justify-content-center" data-anime='{ "el": "childs", "opacity": [0, 1], "translateY": [15, 0], "duration": 250, "delay": 2700, "staggervalue": 100, "easing": "easeOutQuad" }'>
                            <!-- Email Card -->
                            <div class="col-md-6 col-lg-5">
                                <a href="mailto:<EMAIL>" class="contact-card d-block text-decoration-none" style="background: rgba(255, 255, 255, 0.8); border-radius: 16px; padding: 25px 20px; text-align: center; transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1); border: 1px solid rgba(233, 88, 161, 0.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.04);">
                                    <div class="contact-icon-large mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #e958a1, #f45888); border-radius: 16px; display: flex; align-items: center; justify-content: center; margin: 0 auto; box-shadow: 0 8px 20px rgba(233, 88, 161, 0.25);">
                                        <i class="bi bi-envelope text-white" style="font-size: 24px;"></i>
                                    </div>
                                    <h6 class="alt-font fw-700 text-dark-gray mb-2" style="font-size: 16px;">Email Us</h6>
                                    <p class="fs-14 mb-0" style="color: #6c757d; font-weight: 500;"><EMAIL></p>
                                </a>
                            </div>

                            <!-- Phone Card -->
                            <div class="col-md-6 col-lg-5">
                                <a href="tel:+16475727784" class="contact-card d-block text-decoration-none" style="background: rgba(255, 255, 255, 0.8); border-radius: 16px; padding: 25px 20px; text-align: center; transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1); border: 1px solid rgba(143, 118, 245, 0.1); box-shadow: 0 6px 20px rgba(0, 0, 0, 0.04);">
                                    <div class="contact-icon-large mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #8f76f5, #a084f7); border-radius: 16px; display: flex; align-items: center; justify-content: center; margin: 0 auto; box-shadow: 0 8px 20px rgba(143, 118, 245, 0.25);">
                                        <i class="bi bi-telephone text-white" style="font-size: 24px;"></i>
                                    </div>
                                    <h6 class="alt-font fw-700 text-dark-gray mb-2" style="font-size: 16px;">Call Us</h6>
                                    <p class="fs-14 mb-0" style="color: #6c757d; font-weight: 500;">+****************</p>
                                </a>
                            </div>
                        </div>

                        <!-- Additional Info -->
                        <div class="text-center mt-4" data-anime='{ "opacity": [0, 1], "duration": 250, "delay": 3000, "easing": "easeOutQuad" }'>
                            <p class="fs-13 mb-0" style="color: #8a8a8a;">We typically respond within 2 hours during business hours</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end section -->

<?php include 'footer.php'; ?>
