<?php

namespace AdZetaAdmin\API;

use AdZetaAdmin\Services\SEOAnalyzer;
use AdZetaAdmin\Services\SitemapGenerator;
use AdZetaAdmin\Services\RobotsManager;

/**
 * SEO API Controller
 * Handles SEO analysis and optimization features
 */
class SEOController extends BaseController
{
    private SEOAnalyzer $seoAnalyzer;
    private SitemapGenerator $sitemapGenerator;
    private RobotsManager $robotsManager;

    public function __construct()
    {
        parent::__construct();
        $this->seoAnalyzer = new SEOAnalyzer();
        $this->sitemapGenerator = new SitemapGenerator($this->db);
        $this->robotsManager = new RobotsManager();
    }
    
    /**
     * Analyze content for SEO
     */
    public function analyze()
    {
        $this->requireAuth();
        $data = $this->getRequestData();
        
        try {
            $analysis = $this->seoAnalyzer->analyze($data);
            
            return $this->success([
                'analysis' => $analysis,
                'score_color' => SEOAnalyzer::getScoreColor($analysis['score']),
                'score_description' => SEOAnalyzer::getScoreDescription($analysis['score'])
            ]);
            
        } catch (\Exception $e) {
            return $this->error('SEO analysis failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get SEO score for a specific post
     */
    public function getScore($id)
    {
        $this->requireAuth();
        
        try {
            $post = $this->db->fetch(
                "SELECT title, content, meta_description, focus_keyword, slug 
                 FROM blog_posts WHERE id = ?",
                [$id]
            );
            
            if (!$post) {
                return $this->error('Post not found', 404);
            }
            
            $analysis = $this->seoAnalyzer->analyze($post);
            
            // Update SEO score in database
            $this->db->update(
                'blog_posts',
                ['seo_score' => $analysis['score']],
                'id = ?',
                [$id]
            );
            
            return $this->success([
                'score' => $analysis['score'],
                'suggestions' => $analysis['suggestions'],
                'word_count' => $analysis['word_count'],
                'reading_time' => $analysis['reading_time'],
                'score_color' => SEOAnalyzer::getScoreColor($analysis['score']),
                'score_description' => SEOAnalyzer::getScoreDescription($analysis['score'])
            ]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to get SEO score: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate social media preview
     */
    public function socialPreview()
    {
        $this->requireAuth();
        $data = $this->getRequestData();
        
        try {
            $preview = [
                'facebook' => $this->generateFacebookPreview($data),
                'twitter' => $this->generateTwitterPreview($data),
                'linkedin' => $this->generateLinkedInPreview($data)
            ];
            
            return $this->success(['preview' => $preview]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to generate social preview: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate schema markup
     */
    public function generateSchema()
    {
        $this->requireAuth();
        $data = $this->getRequestData();
        
        try {
            $schema = [
                '@context' => 'https://schema.org',
                '@type' => 'BlogPosting',
                'headline' => $data['title'] ?? '',
                'description' => $data['meta_description'] ?? $data['excerpt'] ?? '',
                'author' => [
                    '@type' => 'Person',
                    'name' => $data['author_name'] ?? 'AdZeta'
                ],
                'datePublished' => $data['published_at'] ?? date('c'),
                'dateModified' => $data['updated_at'] ?? date('c'),
                'image' => $data['featured_image'] ?? $data['og_image'] ?? '',
                'publisher' => [
                    '@type' => 'Organization',
                    'name' => getSetting('site_name', 'AdZeta'),
                    'logo' => [
                        '@type' => 'ImageObject',
                        'url' => getSetting('site_logo', '')
                    ]
                ],
                'mainEntityOfPage' => [
                    '@type' => 'WebPage',
                    '@id' => $data['canonical_url'] ?? ''
                ]
            ];
            
            return $this->success(['schema' => $schema]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to generate schema markup: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate sitemap
     */
    public function generateSitemap()
    {
        $this->requirePermission('manage_settings');
        
        try {
            $posts = $this->db->fetchAll(
                "SELECT slug, updated_at, created_at 
                 FROM blog_posts 
                 WHERE status = 'published' 
                 ORDER BY updated_at DESC"
            );
            
            $pages = $this->db->fetchAll(
                "SELECT slug, updated_at, created_at 
                 FROM pages 
                 WHERE status = 'published' 
                 ORDER BY updated_at DESC"
            );
            
            $baseUrl = getSetting('site_url', 'http://localhost');
            $sitemap = $this->buildSitemapXML($baseUrl, $posts, $pages);
            
            // Save sitemap to file
            $sitemapPath = __DIR__ . '/../../sitemap.xml';
            file_put_contents($sitemapPath, $sitemap);
            
            return $this->success([
                'message' => 'Sitemap generated successfully',
                'url' => $baseUrl . '/adzeta-admin/sitemap.xml',
                'posts_count' => count($posts),
                'pages_count' => count($pages)
            ]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to generate sitemap: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate Facebook preview
     */
    private function generateFacebookPreview(array $data): array
    {
        return [
            'title' => $data['og_title'] ?? $data['title'] ?? '',
            'description' => $data['og_description'] ?? $data['meta_description'] ?? $data['excerpt'] ?? '',
            'image' => $data['og_image'] ?? $data['featured_image'] ?? '',
            'url' => $data['canonical_url'] ?? '',
            'site_name' => getSetting('site_name', 'AdZeta')
        ];
    }
    
    /**
     * Generate Twitter preview
     */
    private function generateTwitterPreview(array $data): array
    {
        return [
            'card' => $data['twitter_card_type'] ?? 'summary_large_image',
            'title' => $data['title'] ?? '',
            'description' => $data['meta_description'] ?? $data['excerpt'] ?? '',
            'image' => $data['featured_image'] ?? $data['og_image'] ?? '',
            'site' => getSetting('twitter_username', '')
        ];
    }
    
    /**
     * Generate LinkedIn preview
     */
    private function generateLinkedInPreview(array $data): array
    {
        return [
            'title' => $data['title'] ?? '',
            'description' => $data['meta_description'] ?? $data['excerpt'] ?? '',
            'image' => $data['featured_image'] ?? $data['og_image'] ?? '',
            'url' => $data['canonical_url'] ?? ''
        ];
    }
    
    /**
     * Build sitemap XML
     */
    private function buildSitemapXML(string $baseUrl, array $posts, array $pages): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        // Add homepage
        $xml .= "  <url>\n";
        $xml .= "    <loc>{$baseUrl}/</loc>\n";
        $xml .= "    <lastmod>" . date('c') . "</lastmod>\n";
        $xml .= "    <changefreq>daily</changefreq>\n";
        $xml .= "    <priority>1.0</priority>\n";
        $xml .= "  </url>\n";
        
        // Add blog posts
        foreach ($posts as $post) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>{$baseUrl}/blog/{$post['slug']}</loc>\n";
            $xml .= "    <lastmod>" . date('c', strtotime($post['updated_at'])) . "</lastmod>\n";
            $xml .= "    <changefreq>weekly</changefreq>\n";
            $xml .= "    <priority>0.8</priority>\n";
            $xml .= "  </url>\n";
        }
        
        // Add pages
        foreach ($pages as $page) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>{$baseUrl}/{$page['slug']}</loc>\n";
            $xml .= "    <lastmod>" . date('c', strtotime($page['updated_at'])) . "</lastmod>\n";
            $xml .= "    <changefreq>monthly</changefreq>\n";
            $xml .= "    <priority>0.6</priority>\n";
            $xml .= "  </url>\n";
        }
        
        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Generate sitemap using new service
     */
    public function generateSitemapNew()
    {
        $this->requireAuth();
        $data = $this->getRequestData();

        try {
            $settings = $data['settings'] ?? [];
            $result = $this->sitemapGenerator->generate($settings);

            if ($result['success']) {
                return $this->success($result);
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            return $this->error('Failed to generate sitemap: ' . $e->getMessage());
        }
    }

    /**
     * Get sitemap status
     */
    public function getSitemapStatus()
    {
        $this->requireAuth();

        try {
            $status = $this->sitemapGenerator->getStatus();
            return $this->success($status);

        } catch (\Exception $e) {
            return $this->error('Failed to get sitemap status: ' . $e->getMessage());
        }
    }

    /**
     * Get robots.txt content
     */
    public function getRobotsContent()
    {
        $this->requireAuth();

        try {
            $result = $this->robotsManager->getContent();

            if ($result['success']) {
                return $this->success($result['data']);
            } else {
                return $this->error('Failed to get robots.txt content');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to get robots.txt content: ' . $e->getMessage());
        }
    }

    /**
     * Save robots.txt content
     */
    public function saveRobotsContent()
    {
        $this->requireAuth();
        $data = $this->getRequestData();

        try {
            $content = $data['content'] ?? '';
            $result = $this->robotsManager->saveContent($content);

            if ($result['success']) {
                return $this->success($result);
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            return $this->error('Failed to save robots.txt: ' . $e->getMessage());
        }
    }

    /**
     * Validate robots.txt content
     */
    public function validateRobotsContent()
    {
        $this->requireAuth();
        $data = $this->getRequestData();

        try {
            $content = $data['content'] ?? '';
            $validation = $this->robotsManager->validateContent($content);

            return $this->success($validation);

        } catch (\Exception $e) {
            return $this->error('Failed to validate robots.txt: ' . $e->getMessage());
        }
    }

    /**
     * Generate robots.txt from template
     */
    public function generateRobotsTemplate()
    {
        $this->requireAuth();
        $data = $this->getRequestData();

        try {
            $templateType = $data['template'] ?? 'basic';
            $result = $this->robotsManager->generateFromTemplate($templateType);

            if ($result['success']) {
                return $this->success($result);
            } else {
                return $this->error($result['message']);
            }

        } catch (\Exception $e) {
            return $this->error('Failed to generate robots.txt template: ' . $e->getMessage());
        }
    }

    /**
     * Test robots.txt accessibility
     */
    public function testRobotsAccessibility()
    {
        $this->requireAuth();

        try {
            $result = $this->robotsManager->testAccessibility();
            return $this->success($result);

        } catch (\Exception $e) {
            return $this->error('Failed to test robots.txt accessibility: ' . $e->getMessage());
        }
    }
}
