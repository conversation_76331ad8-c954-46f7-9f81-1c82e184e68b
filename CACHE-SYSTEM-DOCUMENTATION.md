# 🚀 AdZeta Cache System Documentation

## **Overview**

The AdZeta cache system is a high-performance static HTML caching solution designed for blogs and content management systems. It generates static HTML files with gzip compression and automatic invalidation to dramatically improve website performance.

### **⚠️ Important: Database-Driven Settings**
All cache settings are stored in the database `settings` table, not in JSON files. This ensures consistency between admin panel and frontend caching behavior. The FrontendCacheManager reads settings directly from the database to respect admin panel toggles.

### **🔄 Auto-Save Behavior**
Cache settings use **real-time auto-save** - when you toggle any setting in the admin panel, it immediately updates the database. No "Save" button is needed, eliminating UX confusion and ensuring settings are never lost.

## **📁 File Structure**

```
adzeta-admin/
├── src/
│   ├── API/
│   │   └── CacheController.php          # Main cache API endpoint
│   ├── Cache/
│   │   ├── FrontendCacheManager.php     # Core cache management
│   │   └── CacheHooks.php               # Auto-invalidation hooks
│   └── Models/
│       └── BlogPost.php                 # Blog post data model
├── assets/js/modules/
│   └── simple-cache-settings.js         # Frontend cache UI
├── assets/css/
│   └── apple-cache-styles.css           # Cache UI styling
└── cache/
    └── static/
        └── router/                      # Cached HTML files storage
            ├── blog-post-{slug}.html    # Individual blog posts
            ├── blog-post-{slug}.html.gz # Gzipped versions
            ├── blog-list.html           # Blog listing page
            └── blog-list.html.gz        # Gzipped blog list
```

---

## **🔧 Core Components**

### **1. CacheController.php** - Main API Endpoint

**Location:** `adzeta-admin/src/API/CacheController.php`

**Purpose:** Handles all cache-related API requests from the admin panel.

#### **API Endpoints:**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/cache/stats` | Get cache statistics and cached URLs |
| `POST` | `/api/cache/clear` | Clear cache (all, blog, or specific post) |
| `POST` | `/api/cache/preload` | Pre-generate cache for important pages |
| `POST` | `/api/cache/settings` | Update cache optimization settings |

#### **Key Methods:**

```php
// Get cache statistics
public function stats()

// Clear cache based on type
public function clear()

// Pre-generate cache files
public function preload()

// Get real cached URLs from filesystem
private function getRealCachedUrls()

// Clear specific post cache
private function clearSpecificPost($postId)
```

---

### **2. FrontendCacheManager.php** - Core Cache Engine

**Location:** `adzeta-admin/src/Cache/FrontendCacheManager.php`

**Purpose:** Manages cache file generation, storage, and invalidation.

#### **Key Features:**

- ✅ **Static HTML Generation** - Converts dynamic pages to static files
- ✅ **Gzip Compression** - Reduces file sizes by 60-80%
- ✅ **HTML Minification** - Removes whitespace and comments
- ✅ **Smart Cache Keys** - Based on URL, pagination, filters
- ✅ **Auto-invalidation** - Clears cache when content updates

#### **Core Methods:**

```php
// Generate and store cache file
public function cacheContent($url, $content, $type = 'page')

// Clear all cache files
public function clearAllCache()

// Clear specific blog post cache
public function clearBlogPostCache($slug)

// Clear blog list cache
public function clearBlogListCache()

// Get cache statistics
public function getCacheStats()

// Check if URL should be cached
public function shouldCache($url)

// Minify HTML content
private function minifyHtml($html)

// Generate cache filename
private function generateCacheKey($url, $type)
```

---

### **3. CacheHooks.php** - Auto-Invalidation System

**Location:** `adzeta-admin/src/Cache/CacheHooks.php`

**Purpose:** Automatically clears relevant cache when content is updated.

#### **Hook Events:**

```php
// Triggered when blog post is saved/updated
public function onBlogPostSaved($postId, $postData)

// Triggered when blog post is deleted
public function onBlogPostDeleted($postId, $postData)

// Triggered when categories/tags change
public function onTaxonomyUpdated($type, $termId)
```

---

## **⚡ How It Works**

### **1. Cache Generation Process**

```mermaid
graph TD
    A[User Visits Page] --> B{Cache Exists?}
    B -->|Yes| C[Serve Cached HTML]
    B -->|No| D[Generate Dynamic Content]
    D --> E[Minify HTML]
    E --> F[Create Gzip Version]
    F --> G[Store Both Files]
    G --> H[Serve Content]
```

### **2. Cache Invalidation Process**

```mermaid
graph TD
    A[Content Updated] --> B[CacheHooks Triggered]
    B --> C[Identify Affected Cache]
    C --> D[Delete HTML Files]
    D --> E[Delete Gzip Files]
    E --> F[Clear Related Lists]
    F --> G[Update Stats]
```

---

## **🎯 Cache Types & Naming Convention**

### **Blog Posts**
- **Pattern:** `blog-post-{slug}.html`
- **Example:** `blog-post-marketing-guide.html`
- **Gzipped:** `blog-post-marketing-guide.html.gz`

### **Blog Lists**
- **Pattern:** `blog-list[-page-{number}].html`
- **Examples:**
  - `blog-list.html` (first page)
  - `blog-list-page-2.html` (pagination)

### **Case Studies**
- **Pattern:** `case-study-{slug}.html`
- **Example:** `case-study-luminous-skin-clinic.html`

### **Whitepapers**
- **Pattern:** `whitepaper-{slug}.html`
- **Example:** `whitepaper-digital-marketing-trends.html`

---

## **📊 Performance Optimizations**

### **HTML Minification**
```php
private function minifyHtml($html) {
    // Remove HTML comments (except IE conditional)
    $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);

    // Remove extra whitespace
    $html = preg_replace('/\s+/', ' ', $html);

    // Remove whitespace between tags
    $html = preg_replace('/>\s+</', '><', $html);

    return trim($html);
}
```

### **Gzip Compression**
```php
// Create gzipped version
$gzipFile = $cacheFile . '.gz';
file_put_contents($gzipFile, gzencode($minifiedContent, 9));
```

### **Performance Gains**
- **Static HTML:** 10-100x faster than dynamic generation
- **Gzip Compression:** 60-80% file size reduction
- **HTML Minification:** 20-30% additional size reduction
- **Browser Caching:** Instant repeat visits

---

## **🔌 API Usage Examples**

### **Get Cache Statistics**
```javascript
// Frontend JavaScript
const response = await fetch('/adzeta-admin/api/cache/stats');
const data = await response.json();

console.log(data);
// Output:
{
  "success": true,
  "data": {
    "total_cached_pages": 15,
    "cache_size": "2.4 MB",
    "performance_improvement": 85,
    "cached_urls": [...]
  }
}
```

### **Clear All Cache**
```javascript
const response = await fetch('/adzeta-admin/api/cache/clear', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ type: 'all' })
});
```

### **Clear Specific Post Cache**
```javascript
const response = await fetch('/adzeta-admin/api/cache/clear', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        type: 'post',
        post_id: 54
    })
});
```

### **Preload Important Pages**
```javascript
const response = await fetch('/adzeta-admin/api/cache/preload', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        urls: ['/blog/', '/blog/marketing-guide/', '/case-studies/']
    })
});
```

---

## **🎛️ Admin Panel Integration**

### **Cache Settings UI**
**File:** `adzeta-admin/assets/js/modules/simple-cache-settings.js`

#### **Features:**
- ✅ **Real-time Statistics** - Live cache stats and file counts
- ✅ **One-click Cache Control** - Enable/disable caching
- ✅ **Selective Clearing** - Clear all, blog only, or specific content
- ✅ **Optimization Settings** - Toggle gzip, minification, browser cache
- ✅ **Cached URLs List** - View all cached pages with sizes

#### **UI Components:**
```javascript
// Main cache toggle
toggleCache(enabled)

// Clear cache actions
clearCache(type) // 'all', 'blog', 'posts'

// Preload cache
preloadCache()

// Update optimization settings
updateSetting(setting, value)
```

---

## **🔄 Cache Lifecycle**

### **1. Cache Creation**
```php
// When a page is first visited
$cacheManager = new FrontendCacheManager();
$cacheManager->cacheContent($url, $htmlContent, 'blog-post');
```

### **2. Cache Serving**
```php
// Router checks for cached version
$cacheFile = '/cache/static/router/blog-post-' . $slug . '.html';
if (file_exists($cacheFile)) {
    // Serve cached version with proper headers
    header('Content-Type: text/html; charset=UTF-8');
    header('Cache-Control: public, max-age=3600');
    readfile($cacheFile);
    exit;
}
```

### **3. Cache Invalidation**
```php
// When content is updated
$hooks = new CacheHooks();
$hooks->onBlogPostSaved($postId, $postData);
// Automatically clears related cache files
```

---

## **⚙️ Configuration Options**

### **Cache Settings (Database-Driven)**
```sql
-- Cache settings are stored in the database settings table
SELECT setting_key, setting_value, setting_type FROM settings
WHERE setting_key LIKE 'cache_%' OR setting_key LIKE 'static_cache_%' OR setting_key LIKE 'browser_cache_%';

-- Key settings:
-- cache_enabled (boolean) - Master cache toggle
-- static_cache_gzip (boolean) - Gzip compression
-- static_cache_minify (boolean) - HTML minification
-- browser_cache_enabled (boolean) - Browser cache headers
-- static_cache_duration (number) - Cache TTL in seconds
```

### **Settings Architecture**
```php
// FrontendCacheManager reads from database
public function loadSettings() {
    // Connects to database and loads real-time settings
    $cacheSettings = $admin_db->fetchAll(
        "SELECT setting_key, setting_value, setting_type FROM settings WHERE setting_key LIKE 'cache_%'"
    );
    // Converts database values to proper types (boolean, number, etc.)
}
```

### **Cacheable Content Types**
```php
private $cacheableTypes = [
    'blog-post',
    'blog-list',
    'case-study',
    'whitepaper',
    'page'
];
```

---

## **🐛 Debugging & Troubleshooting**

### **Common Issues**

#### **1. Cache Not Clearing After Post Update**
**Problem:** Edited posts still show old content
**Solution:** Check cache hooks initialization
```php
// Ensure hooks are properly initialized
$hooks = new CacheHooks($cacheManager);
```

#### **2. Stats Don't Match Cached URLs**
**Problem:** Shows 10 pages but only 3 URLs listed
**Solution:** Stats now calculated from actual displayed URLs
```php
// Fixed in CacheController.php
$totalPages = count($cachedUrls); // Matches displayed list
```

#### **3. Cache Files Not Found**
**Problem:** Cache directory doesn't exist
**Solution:** Auto-create cache directories
```php
if (!is_dir($cacheDir)) {
    mkdir($cacheDir, 0755, true);
}
```

### **Debug Mode**
Enable detailed logging:
```php
// Add to FrontendCacheManager.php
error_log("Cache operation: {$operation} for {$url}");
```

---

## **📈 Performance Monitoring**

### **Cache Hit Rate**
Monitor cache effectiveness:
```javascript
// In admin panel
const hitRate = (cachedRequests / totalRequests) * 100;
console.log(`Cache hit rate: ${hitRate}%`);
```

### **File Size Optimization**
Track compression ratios:
```php
$originalSize = strlen($content);
$compressedSize = filesize($gzipFile);
$compressionRatio = (1 - $compressedSize / $originalSize) * 100;
```

---

## **🚀 Best Practices**

### **1. Cache Strategy**
- ✅ Cache static content aggressively
- ✅ Use short TTL for frequently updated content
- ✅ Implement smart invalidation
- ✅ Monitor cache hit rates

### **2. File Organization**
- ✅ Use consistent naming conventions
- ✅ Separate cache types into logical groups
- ✅ Implement automatic cleanup for old files

### **3. Performance Optimization**
- ✅ Enable gzip compression
- ✅ Minify HTML output
- ✅ Set proper browser cache headers
- ✅ Use CDN for static assets

---

## **🚫 Troubleshooting Guide**

### **Common Issues & Solutions**

#### **1. Cache Still Generating Despite Being Disabled**
```bash
# Problem: Frontend cache ignoring admin panel settings
# Cause: FrontendCacheManager reading from wrong source

# Solution: Verify database connection
php -r "require 'adzeta-admin/bootstrap.php'; var_dump($admin_db->fetchAll('SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE \"%cache%\"'));"

# Check error logs for:
# "FrontendCacheManager: Loaded settings from database"
```

#### **2. Admin Panel Toggles Not Updating Database**
```javascript
// Problem: JavaScript shows success but database unchanged
// Cause: API returning HTML errors instead of JSON

// Debug: Check browser console for:
// "SyntaxError: Unexpected token '<'"

// Solution: Check CacheController.saveSettings() for PHP errors
```

#### **3. UI Shows Wrong Toggle States**
```javascript
// Problem: Toggles show enabled when database shows disabled
// Cause: Wrong JavaScript logic

// Wrong: !== false (defaults to enabled)
${this.state.settings?.cache_enabled !== false ? 'checked' : ''}

// Correct: === true (only enabled when explicitly true)
${this.state.settings?.cache_enabled === true ? 'checked' : ''}
```

#### **4. Multiple Settings Modules Conflicting**
```javascript
// Problem: settings.js and simple-cache-settings.js both loading
// Solution: Disable cache loading in settings.js:

// In settings.js loadSettings():
console.log('Settings.js: Skipping cache settings load - delegating to simple-cache-settings.js');
// Don't load cache settings here
```

### **Architecture Principles**

#### **✅ Correct Architecture:**
1. **Single Source of Truth**: Database stores all settings
2. **Centralized API**: One endpoint handles all cache settings
3. **Consistent Reading**: All components read from database
4. **Proper Defaults**: Default to disabled, not enabled

#### **❌ Avoid These Patterns:**
1. **Multiple Storage**: JSON files + Database
2. **Conflicting Modules**: Multiple JavaScript modules loading same data
3. **Wrong Defaults**: Defaulting to enabled instead of disabled
4. **Mixed Responses**: HTML errors in JSON APIs

---

## **🔮 Future Enhancements**

### **Planned Features**
- 🔄 **CSS/JS Minification** - Optimize external assets
- 🖼️ **Image Optimization** - WebP conversion and compression
- ⚡ **Critical CSS Inlining** - Faster first paint
- 📊 **Advanced Analytics** - Detailed cache performance metrics
- 🌐 **CDN Integration** - Distribute cached files globally

---

## **📞 Support & Maintenance**

### **Regular Maintenance Tasks**
1. **Monitor cache directory size** - Prevent disk space issues
2. **Review cache hit rates** - Optimize caching strategy
3. **Clean up old cache files** - Implement automatic cleanup
4. **Update cache invalidation rules** - As content structure changes

### **Emergency Cache Clear**
If cache system becomes corrupted:
```bash
# Manual cache clear via filesystem
rm -rf /cache/static/router/*
```

---

**🎯 The AdZeta cache system provides enterprise-level performance optimization with WordPress-inspired ease of use, delivering 10-100x performance improvements for content-heavy websites.**
