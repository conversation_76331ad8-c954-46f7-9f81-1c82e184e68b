<?php

namespace AdZetaAdmin\Models;

use PDO;
use Exception;
use Ad<PERSON><PERSON>Admin\Cache\CacheInvalidationHooks;

class Category
{
    private PDO $db;
    private $cacheHooks;

    public function __construct(PDO $db)
    {
        $this->db = $db;
        $this->cacheHooks = new CacheInvalidationHooks();
    }

    /**
     * Get all categories
     */
    public function getAll(): array
    {
        $sql = "SELECT c.*, COUNT(p.id) as post_count
                FROM blog_categories c
                LEFT JOIN blog_posts p ON c.id = p.category_id AND p.status = 'published'
                GROUP BY c.id
                ORDER BY c.name ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get category by ID
     */
    public function getById(int $id): ?array
    {
        $sql = "SELECT c.*, COUNT(p.id) as post_count
                FROM blog_categories c
                LEFT JOIN blog_posts p ON c.id = p.category_id AND p.status = 'published'
                WHERE c.id = :id
                GROUP BY c.id";

        $stmt = $this->db->prepare($sql);
        $stmt->execute(['id' => $id]);

        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Get category by slug
     */
    public function getBySlug(string $slug): ?array
    {
        $sql = "SELECT c.*, COUNT(p.id) as post_count
                FROM blog_categories c
                LEFT JOIN blog_posts p ON c.id = p.category_id AND p.status = 'published'
                WHERE c.slug = :slug
                GROUP BY c.id";

        $stmt = $this->db->prepare($sql);
        $stmt->execute(['slug' => $slug]);

        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * Create a new category
     */
    public function create(array $data): int
    {
        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name']);
        }

        $sql = "INSERT INTO blog_categories (name, slug, description, meta_title, meta_description, parent_id)
                VALUES (:name, :slug, :description, :meta_title, :meta_description, :parent_id)";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            'name' => $data['name'],
            'slug' => $data['slug'],
            'description' => $data['description'] ?? '',
            'meta_title' => $data['meta_title'] ?? '',
            'meta_description' => $data['meta_description'] ?? '',
            'parent_id' => $data['parent_id'] ?? null
        ]);

        return $this->db->lastInsertId();
    }

    /**
     * Update a category
     */
    public function update(int $id, array $data): bool
    {
        // Generate new slug if name changed
        if (!empty($data['name']) && empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['name'], $id);
        }

        $fields = [];
        $params = ['id' => $id];

        $allowedFields = ['name', 'slug', 'description', 'meta_title', 'meta_description', 'parent_id'];

        foreach ($allowedFields as $field) {
            if (array_key_exists($field, $data)) {
                $fields[] = "{$field} = :{$field}";
                $params[$field] = $data[$field];
            }
        }

        if (empty($fields)) {
            return false;
        }

        $sql = "UPDATE blog_categories SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute($params);

        // Clear cache when category is updated
        if ($result) {
            try {
                $categorySlug = $data['slug'] ?? $this->getById($id)['slug'] ?? 'unknown';
                $this->cacheHooks->onCategoryUpdated($id, $categorySlug);
            } catch (Exception $e) {
                error_log('Cache invalidation failed after category update: ' . $e->getMessage());
            }
        }

        return $result;
    }

    /**
     * Delete a category
     */
    public function delete(int $id): bool
    {
        // Check if category has posts
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM blog_posts WHERE category_id = :id");
        $stmt->execute(['id' => $id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception("Cannot delete category with existing posts");
        }

        $stmt = $this->db->prepare("DELETE FROM blog_categories WHERE id = :id");
        return $stmt->execute(['id' => $id]);
    }

    /**
     * Get posts by category
     */
    public function getPosts(int $categoryId, array $options = []): array
    {
        $page = $options['page'] ?? 1;
        $limit = $options['limit'] ?? 10;
        $offset = ($page - 1) * $limit;

        $sql = "SELECT p.*, u.first_name, u.last_name
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                WHERE p.category_id = :category_id AND p.status = 'published'
                ORDER BY p.published_at DESC
                LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);
        $stmt->bindValue(':category_id', $categoryId, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get post count for category
     */
    public function getPostCount(int $categoryId): int
    {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM blog_posts WHERE category_id = :category_id AND status = 'published'");
        $stmt->execute(['category_id' => $categoryId]);

        return (int) $stmt->fetchColumn();
    }

    /**
     * Generate a unique slug
     */
    private function generateUniqueSlug(string $name, int $excludeId = null): string
    {
        $baseSlug = $this->slugify($name);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Simple slugify function
     */
    private function slugify(string $text): string
    {
        // Replace non-alphanumeric characters with hyphens
        $text = preg_replace('/[^a-zA-Z0-9]+/', '-', $text);
        // Convert to lowercase
        $text = strtolower($text);
        // Remove leading/trailing hyphens
        $text = trim($text, '-');

        return $text;
    }

    /**
     * Check if slug exists
     */
    private function slugExists(string $slug, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) FROM blog_categories WHERE slug = :slug";
        $params = ['slug' => $slug];

        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() > 0;
    }

    /**
     * Update post count for category
     */
    public function updatePostCount(int $categoryId): void
    {
        $stmt = $this->db->prepare("
            UPDATE blog_categories
            SET post_count = (
                SELECT COUNT(*)
                FROM blog_posts
                WHERE category_id = :category_id AND status = 'published'
            )
            WHERE id = :category_id
        ");

        $stmt->execute(['category_id' => $categoryId]);
    }

    /**
     * Get hierarchical categories (with parent-child relationships)
     */
    public function getHierarchical(): array
    {
        $sql = "SELECT * FROM blog_categories ORDER BY parent_id ASC, name ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();

        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize into hierarchy
        $hierarchy = [];
        $lookup = [];

        // First pass: create lookup array
        foreach ($categories as $category) {
            $category['children'] = [];
            $lookup[$category['id']] = $category;
        }

        // Second pass: build hierarchy
        foreach ($lookup as $id => $category) {
            if ($category['parent_id'] === null) {
                $hierarchy[] = &$lookup[$id];
            } else {
                $lookup[$category['parent_id']]['children'][] = &$lookup[$id];
            }
        }

        return $hierarchy;
    }
}
