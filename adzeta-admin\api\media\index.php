<?php
/**
 * AdZeta Admin Panel - Media Library API
 * Handles media listing, search, and pagination
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Database configuration
$host = 'localhost';
$dbname = 'adzetadb';
$username = 'adzetauser';
$password = 'Crazy1395#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

// Get parameters and ensure they are integers
$page = max(1, intval($_GET['page'] ?? 1));
$limit = max(1, min(50, intval($_GET['limit'] ?? 20))); // Max 50 items per page
$search = trim($_GET['search'] ?? '');
$offset = ($page - 1) * $limit;

// Debug logging
error_log("Media API - Page: $page, Limit: $limit, Offset: $offset, Search: '$search'");

try {
    // Create media table if it doesn't exist
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS media (
            id INT AUTO_INCREMENT PRIMARY KEY,
            original_filename VARCHAR(255) NOT NULL,
            filename VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_url VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            width INT DEFAULT 0,
            height INT DEFAULT 0,
            alt_text TEXT,
            caption TEXT,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_filename (filename),
            INDEX idx_uploaded_at (uploaded_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    $pdo->exec($createTableSQL);

    // Build WHERE clause for search
    $whereClause = '';
    $params = [];
    
    if (!empty($search)) {
        $whereClause = 'WHERE original_filename LIKE ? OR alt_text LIKE ? OR caption LIKE ?';
        $searchTerm = "%$search%";
        $params = [$searchTerm, $searchTerm, $searchTerm];
    }

    // Get total count
    $countSQL = "SELECT COUNT(*) FROM media $whereClause";
    $countStmt = $pdo->prepare($countSQL);
    $countStmt->execute($params);
    $totalItems = $countStmt->fetchColumn();
    $totalPages = ceil($totalItems / $limit);

    // Get media items (using direct values for LIMIT/OFFSET to avoid SQL syntax issues)
    $mediaSQL = "
        SELECT id, original_filename, filename, file_url, file_size, mime_type,
               width, height, alt_text, caption, uploaded_at
        FROM media
        $whereClause
        ORDER BY uploaded_at DESC
        LIMIT $limit OFFSET $offset
    ";

    $mediaStmt = $pdo->prepare($mediaSQL);
    $mediaStmt->execute($params);
    $media = $mediaStmt->fetchAll(PDO::FETCH_ASSOC);

    // Format media items
    $formattedMedia = array_map(function($item) {
        return [
            'id' => (int)$item['id'],
            'original_filename' => $item['original_filename'],
            'filename' => $item['filename'],
            'file_url' => $item['file_url'],
            'file_size' => (int)$item['file_size'],
            'mime_type' => $item['mime_type'],
            'width' => (int)$item['width'],
            'height' => (int)$item['height'],
            'alt_text' => $item['alt_text'],
            'caption' => $item['caption'],
            'uploaded_at' => $item['uploaded_at']
        ];
    }, $media);

    // Return response
    echo json_encode([
        'success' => true,
        'media' => $formattedMedia,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_items' => $totalItems,
            'items_per_page' => $limit,
            'has_next' => $page < $totalPages,
            'has_prev' => $page > 1
        ],
        'search' => $search
    ]);

} catch (PDOException $e) {
    error_log("Media API Database Error: " . $e->getMessage());
    error_log("SQL Query: " . ($mediaSQL ?? 'Unknown'));
    error_log("Parameters: " . json_encode($params ?? []));

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage(),
        'debug' => [
            'page' => $page ?? 'unknown',
            'limit' => $limit ?? 'unknown',
            'offset' => $offset ?? 'unknown',
            'search' => $search ?? 'unknown'
        ]
    ]);
}
?>
