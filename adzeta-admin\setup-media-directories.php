<?php
/**
 * Setup Media Directories - Create required directories for media uploads
 */

header('Content-Type: text/html');

echo "<h1>📁 Setup Media Directories</h1>";

$directories = [
    __DIR__ . '/uploads',
    __DIR__ . '/uploads/media',
    __DIR__ . '/cache',
    __DIR__ . '/cache/static',
    __DIR__ . '/cache/static/posts',
    __DIR__ . '/cache/static/categories'
];

echo "<h3>Creating Required Directories</h3>";

foreach ($directories as $dir) {
    $relativePath = str_replace(__DIR__, '', $dir);
    $relativePath = ltrim($relativePath, '/\\');
    
    if (file_exists($dir)) {
        if (is_dir($dir)) {
            echo "<div style='background: #d4edda; padding: 8px; margin: 5px 0; border-radius: 3px; color: #155724;'>";
            echo "✅ <strong>{$relativePath}/</strong> - Already exists";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 8px; margin: 5px 0; border-radius: 3px; color: #721c24;'>";
            echo "❌ <strong>{$relativePath}</strong> - Exists but is not a directory";
            echo "</div>";
        }
    } else {
        if (mkdir($dir, 0755, true)) {
            echo "<div style='background: #d4edda; padding: 8px; margin: 5px 0; border-radius: 3px; color: #155724;'>";
            echo "✅ <strong>{$relativePath}/</strong> - Created successfully";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 8px; margin: 5px 0; border-radius: 3px; color: #721c24;'>";
            echo "❌ <strong>{$relativePath}/</strong> - Failed to create";
            echo "</div>";
        }
    }
}

echo "<hr>";

echo "<h3>Directory Permissions Check</h3>";

foreach ($directories as $dir) {
    $relativePath = str_replace(__DIR__, '', $dir);
    $relativePath = ltrim($relativePath, '/\\');
    
    if (file_exists($dir) && is_dir($dir)) {
        $perms = fileperms($dir);
        $permsOctal = substr(sprintf('%o', $perms), -4);
        $isWritable = is_writable($dir);
        
        if ($isWritable) {
            echo "<div style='background: #d4edda; padding: 8px; margin: 5px 0; border-radius: 3px; color: #155724;'>";
            echo "✅ <strong>{$relativePath}/</strong> - Writable (Permissions: {$permsOctal})";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 8px; margin: 5px 0; border-radius: 3px; color: #856404;'>";
            echo "⚠️ <strong>{$relativePath}/</strong> - Not writable (Permissions: {$permsOctal})";
            echo "</div>";
        }
    }
}

echo "<hr>";

echo "<h3>Create Test .htaccess Files</h3>";

// Create .htaccess for uploads directory
$uploadsHtaccess = __DIR__ . '/uploads/.htaccess';
$htaccessContent = "# Allow access to uploaded files\n<Files ~ \"\\.(jpg|jpeg|png|gif|webp|pdf|doc|docx)$\">\n    Order allow,deny\n    Allow from all\n</Files>\n";

if (!file_exists($uploadsHtaccess)) {
    if (file_put_contents($uploadsHtaccess, $htaccessContent)) {
        echo "<div style='background: #d4edda; padding: 8px; margin: 5px 0; border-radius: 3px; color: #155724;'>";
        echo "✅ <strong>uploads/.htaccess</strong> - Created";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 8px; margin: 5px 0; border-radius: 3px; color: #721c24;'>";
        echo "❌ <strong>uploads/.htaccess</strong> - Failed to create";
        echo "</div>";
    }
} else {
    echo "<div style='background: #d4edda; padding: 8px; margin: 5px 0; border-radius: 3px; color: #155724;'>";
    echo "✅ <strong>uploads/.htaccess</strong> - Already exists";
    echo "</div>";
}

echo "<hr>";

echo "<h3>Test File Upload Capability</h3>";

$testFile = __DIR__ . '/uploads/media/test-upload-capability.txt';
$testContent = "Test file created at " . date('Y-m-d H:i:s') . "\nThis file tests upload directory write capability.";

if (file_put_contents($testFile, $testContent)) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
    echo "✅ <strong>File Upload Test Successful!</strong>";
    echo "<p>Created test file: uploads/media/test-upload-capability.txt</p>";
    
    // Clean up test file
    if (unlink($testFile)) {
        echo "<p>✅ Test file cleaned up successfully</p>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
    echo "❌ <strong>File Upload Test Failed!</strong>";
    echo "<p>Cannot write to uploads/media/ directory</p>";
    echo "<p>Check directory permissions and PHP configuration</p>";
    echo "</div>";
}

echo "<hr>";

echo "<h3>PHP Upload Configuration</h3>";

$uploadSettings = [
    'file_uploads' => ini_get('file_uploads'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'upload_tmp_dir' => ini_get('upload_tmp_dir') ?: 'Default system temp directory'
];

echo "<table style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'><th style='padding: 8px; border: 1px solid #ddd;'>Setting</th><th style='padding: 8px; border: 1px solid #ddd;'>Value</th></tr>";

foreach ($uploadSettings as $setting => $value) {
    $status = '';
    if ($setting === 'file_uploads') {
        $status = $value ? '✅' : '❌';
        $value = $value ? 'Enabled' : 'Disabled';
    } else {
        $status = '✅';
    }
    
    echo "<tr>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$setting}</td>";
    echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$status} {$value}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<hr>";

echo "<h3>✅ Setup Complete</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; color: #0c5460;'>";
echo "<p><strong>Media system setup completed!</strong></p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>Run the media database test: <a href='/adzeta-admin/test-media-database.php' target='_blank'>Test Media Database</a></li>";
echo "<li>Test the admin panel media: <a href='/adzeta-admin/?view=media' target='_blank'>Admin Panel - Media</a></li>";
echo "<li>Try uploading an image in the post editor</li>";
echo "</ol>";
echo "</div>";
?>
