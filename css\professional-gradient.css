/* Professional Gradient Container for Platform Page */
.platform-gradient-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

/* Corner Gradients */
.platform-gradient-container .corner-gradient {
    position: absolute;
    width: 40%;
    height: 40%;
    border-radius: 50%;
    opacity: 0.6;
    filter: blur(60px);
}

.platform-gradient-container .corner-gradient.top-left {
    top: -10%;
    left: -10%;
    background: radial-gradient(circle, rgba(143, 118, 245, 0.3) 0%, rgba(31, 33, 42, 0) 70%);
    animation: pulse-gradient 8s ease-in-out infinite alternate;
}

.platform-gradient-container .corner-gradient.top-right {
    top: -15%;
    right: -15%;
    background: radial-gradient(circle, rgba(233, 88, 161, 0.25) 0%, rgba(31, 33, 42, 0) 70%);
    animation: pulse-gradient 10s ease-in-out infinite alternate-reverse;
}

.platform-gradient-container .corner-gradient.bottom-left {
    bottom: -15%;
    left: -15%;
    background: radial-gradient(circle, rgba(233, 88, 161, 0.2) 0%, rgba(31, 33, 42, 0) 70%);
    animation: pulse-gradient 9s ease-in-out infinite alternate;
}

.platform-gradient-container .corner-gradient.bottom-right {
    bottom: -10%;
    right: -10%;
    background: radial-gradient(circle, rgba(143, 118, 245, 0.25) 0%, rgba(31, 33, 42, 0) 70%);
    animation: pulse-gradient 11s ease-in-out infinite alternate-reverse;
}

/* Diagonal Gradient */
.platform-gradient-container .diagonal-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 200%;
    background: linear-gradient(135deg, rgba(27, 11, 36, 0.8) 0%, rgba(45, 26, 59, 0.8) 100%);
    transform: rotate(-45deg) translate(-50%, -10%);
    z-index: -1;
}

/* Mesh Overlay */
body:not(.ecom-ppc) .platform-gradient-container .mesh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/subtle-mesh-pattern.png');
    background-size: cover;
    opacity: 0.05;
    z-index: -1;
}

/* Empty mesh overlay for ecom-ppc page */
.ecom-ppc .platform-gradient-container .mesh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: none;
    z-index: -1;
}

/* Vignette Overlay */
.platform-gradient-container .vignette-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(27, 11, 36, 0) 0%, rgba(27, 11, 36, 0.6) 100%);
    z-index: -1;
}

/* Animation for subtle gradient movement */
@keyframes pulse-gradient {
    0% {
        opacity: 0.4;
        transform: scale(1);
    }
    100% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Platform Hero Section Specific Styles */
.platform-hero {
    position: relative;
    overflow: hidden;
    padding: 120px 0 100px;
    color: white;
    background: #1B0B24;
}

.platform-hero .hero-title {
    font-size: 48px;
    line-height: 1.2;
    margin-bottom: 25px;
    color: white;
}

.platform-hero .hero-subtitle {
    font-size: 18px;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 35px;
    color: rgba(255, 255, 255, 0.9);
}

/* Apple-inspired form inputs for dark backgrounds */
.bg-dark .form-control,
.platform-hero .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.bg-dark .form-control:focus,
.platform-hero .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.bg-dark .form-control::placeholder,
.platform-hero .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Apple-inspired buttons for dark backgrounds */
.bg-dark .btn-white,
.platform-hero .btn-white {
    background-color: white;
    color: #1B0B24;
    border-radius: 10px;
    padding: 12px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.bg-dark .btn-white:hover,
.platform-hero .btn-white:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
