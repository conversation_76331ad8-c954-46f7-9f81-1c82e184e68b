/* Orbital Data Sources Layout */
.orbital-data-section {
    position: relative;
    padding: 80px 0;
    background: linear-gradient(180deg, #FFFFFF 0%, #F7F7F9 100%);
    overflow: hidden;
}

.orbital-data-wrapper {
    position: relative;
    width: 100%;
    max-width: 1200px;
    height: 700px;
    margin: 0 auto;
}

/* Central image */
.orbital-data-main-img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    border-radius: 50%;
    object-fit: cover;
    z-index: 1;
}

/* Data source cards */
.data-source-card {
    position: absolute;
    width: 280px;
    background-color: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    z-index: 2;
}

.data-source-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Card header with step indicator and icon */
.data-source-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.data-source-step {
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.data-source-icon {
    width: 38px;
    height: 38px;
}

/* Card content */
.data-source-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c2e3c;
    margin-bottom: 10px;
}

.data-source-description {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
}

/* Positioning for each card */
.data-source-card-1 {
    top: 50px;
    left: 50px;
}

.data-source-card-2 {
    top: 50px;
    right: 50px;
}

.data-source-card-3 {
    bottom: 50px;
    right: 50px;
}

.data-source-card-4 {
    bottom: 50px;
    left: 50px;
}

/* Center card for time indicator */
.data-source-card-center {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 280px;
    background-color: #ffffff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    text-align: center;
    z-index: 2;
}

.data-source-center-icon {
    width: 38px;
    height: 38px;
    margin-bottom: 15px;
}

/* Connection lines */
.data-source-line {
    position: absolute;
    background: linear-gradient(90deg, rgba(233, 88, 161, 0.2) 0%, rgba(143, 118, 245, 0.2) 100%);
    height: 2px;
    z-index: 0;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .orbital-data-wrapper {
        height: 600px;
    }
    
    .orbital-data-main-img {
        width: 300px;
        height: 300px;
    }
    
    .data-source-card {
        width: 240px;
    }
}

@media (max-width: 991px) {
    .orbital-data-wrapper {
        height: auto;
        padding-top: 50px;
    }
    
    .orbital-data-main-img {
        position: relative;
        top: 0;
        left: 0;
        transform: none;
        width: 100%;
        max-width: 300px;
        height: auto;
        margin: 0 auto 50px;
        display: block;
    }
    
    .data-source-card {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        width: 100%;
        max-width: 400px;
        margin: 0 auto 20px;
    }
    
    .data-source-card-center {
        position: relative;
        bottom: auto;
        left: auto;
        transform: none;
        width: 100%;
        max-width: 400px;
        margin: 0 auto;
    }
    
    .data-source-line {
        display: none;
    }
}
