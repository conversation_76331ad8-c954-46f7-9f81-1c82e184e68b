/**
 * Styled Card Tool for Editor.js
 * Preserves AI-generated styled content with background colors and formatting
 */

class StyledCard {
    static get toolbox() {
        return {
            title: 'Styled Card',
            icon: '<svg width="17" height="15" viewBox="0 0 336 276" xmlns="http://www.w3.org/2000/svg"><path d="M291 150V79c0-19-15-34-34-34H79c-19 0-34 15-34 34v42l67-44 81 72 56-29 42 30zm0 52l-43-30-56 30-81-67-67 49v62c0 19 15 34 34 34h178c19 0 34-15 34-34z" fill="#FF4081"/></svg>'
        };
    }

    static get isReadOnlySupported() {
        return true;
    }

    constructor({ data, config, api, readOnly }) {
        this.api = api;
        this.readOnly = readOnly;
        this.config = config || {};

        this.data = {
            content: data.content || '',
            style: data.style || 'default',
            backgroundColor: data.backgroundColor || '#E6D8F2',
            textColor: data.textColor || '#2B0B3A',
            borderColor: data.borderColor || '#FF4081',
            ...data
        };

        this.wrapper = undefined;
        this.contentElement = undefined;
    }

    render() {
        this.wrapper = document.createElement('div');
        this.wrapper.classList.add('styled-card-tool');

        // Create the styled card
        const cardElement = document.createElement('div');
        cardElement.classList.add('styled-card');
        cardElement.style.cssText = `
            background: ${this.data.backgroundColor};
            color: ${this.data.textColor};
            border-left: 4px solid ${this.data.borderColor};
            border-radius: 12px;
            padding: 24px;
            margin: 16px 0;
            box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);
            position: relative;
        `;

        // Content area
        this.contentElement = document.createElement('div');
        this.contentElement.classList.add('styled-card-content');
        this.contentElement.innerHTML = this.data.content;
        this.contentElement.contentEditable = !this.readOnly;
        this.contentElement.style.cssText = `
            outline: none;
            min-height: 40px;
            line-height: 1.6;
        `;

        if (!this.readOnly) {
            this.contentElement.addEventListener('input', () => {
                this.data.content = this.contentElement.innerHTML;
            });

            // Add style controls
            const controls = this.createControls();
            this.wrapper.appendChild(controls);
        }

        cardElement.appendChild(this.contentElement);
        this.wrapper.appendChild(cardElement);

        return this.wrapper;
    }

    createControls() {
        const controls = document.createElement('div');
        controls.classList.add('styled-card-controls');
        controls.style.cssText = `
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 8px;
            font-size: 12px;
        `;

        // Style presets
        const presets = [
            { name: 'Takeaway', bg: '#E6D8F2', text: '#2B0B3A', border: '#FF4081' },
            { name: 'Warning', bg: '#FFF3CD', text: '#856404', border: '#FFC107' },
            { name: 'Success', bg: '#D4EDDA', text: '#155724', border: '#28A745' },
            { name: 'Info', bg: '#D1ECF1', text: '#0C5460', border: '#17A2B8' }
        ];

        presets.forEach(preset => {
            const button = document.createElement('button');
            button.textContent = preset.name;
            button.style.cssText = `
                padding: 4px 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
                cursor: pointer;
                font-size: 11px;
            `;

            button.addEventListener('click', () => {
                this.data.backgroundColor = preset.bg;
                this.data.textColor = preset.text;
                this.data.borderColor = preset.border;
                this.updateCardStyle();
            });

            controls.appendChild(button);
        });

        return controls;
    }

    updateCardStyle() {
        const cardElement = this.wrapper.querySelector('.styled-card');
        if (cardElement) {
            cardElement.style.cssText = `
                background: ${this.data.backgroundColor};
                color: ${this.data.textColor};
                border-left: 4px solid ${this.data.borderColor};
                border-radius: 12px;
                padding: 24px;
                margin: 16px 0;
                box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);
                position: relative;
            `;
        }
    }

    save() {
        return {
            content: this.contentElement.innerHTML,
            backgroundColor: this.data.backgroundColor,
            textColor: this.data.textColor,
            borderColor: this.data.borderColor,
            style: this.data.style
        };
    }

    static get sanitize() {
        return {
            content: {
                br: true,
                strong: true,
                em: true,
                u: true,
                a: {
                    href: true,
                    target: '_blank',
                    rel: 'noopener noreferrer'
                },
                span: {
                    style: true,
                    class: true
                },
                div: {
                    style: true,
                    class: true
                }
            }
        };
    }

    // Paste config removed to avoid Editor.js conflicts
    // Custom paste handling is done through AI content processor

    // onPaste method removed to avoid conflicts
    // Paste handling is done through AI content processor
}

// Export for use in Editor.js
window.StyledCard = StyledCard;
