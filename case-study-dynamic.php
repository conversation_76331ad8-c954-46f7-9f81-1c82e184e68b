<?php
/**
 * Dynamic Individual Case Study Page
 * Uses the existing template but with dynamic data from database
 */

// Get the slug from URL parameter
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('HTTP/1.0 404 Not Found');
    echo '<h1>404 - Page Not Found</h1><p>The requested case study could not be found.</p>';
    exit;
}

// Initialize the application
require_once 'bootstrap.php';
require_once 'adzeta-admin/bootstrap.php';
require_once 'includes/CaseStudiesDatabase.php';

// Get case study from database
try {
    $caseStudy = getCaseStudyBySlug($slug);
    
    if (!$caseStudy) {
        header('HTTP/1.0 404 Not Found');
        echo '<h1>404 - Case Study Not Found</h1><p>The requested case study could not be found or is not published.</p>';
        exit;
    }
    
} catch (Exception $e) {
    error_log('Error loading case study: ' . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    echo '<h1>500 - Internal Server Error</h1><p>An error occurred while loading the case study. Please try again later.</p>';
    exit;
}

// Set page title and meta description for SEO
$pageTitle = $caseStudy['meta_title'] ?: $caseStudy['title'];
$metaDescription = $caseStudy['meta_description'] ?: $caseStudy['hero_description'];
$ogImage = $caseStudy['og_image'] ?: $caseStudy['featured_image'] ?: $caseStudy['hero_image'];

// Get related case studies
$relatedCaseStudies = []; // For now, we'll implement this later

// Determine which template to use
$template = $caseStudy['template'] ?? 'luminous-skin-clinic';

// Map template names to actual files
$templateFiles = [
    'luminous-skin-clinic' => 'luminous-skin-clinic-adzeta-predictive-ltv-targeting.php',
    'default' => 'luminous-skin-clinic-adzeta-predictive-ltv-targeting.php'
];

// Get the template file
$templateFile = $templateFiles[$template] ?? $templateFiles['default'];

// Check if template file exists
if (!file_exists($templateFile)) {
    // Fallback to default template
    $templateFile = $templateFiles['default'];
}

// Set global variables for the template
$GLOBALS['currentCaseStudy'] = $caseStudy;
$GLOBALS['relatedCaseStudies'] = $relatedCaseStudies;

// Helper functions for templates
function getCurrentCaseStudy() {
    return $GLOBALS['currentCaseStudy'] ?? null;
}

function getRelatedCaseStudiesForTemplate() {
    return $GLOBALS['relatedCaseStudies'] ?? [];
}

function formatMetric($key, $value) {
    // Format metric labels nicely
    $label = ucwords(str_replace('_', ' ', $key));
    
    // Add appropriate formatting based on metric type
    if (stripos($key, 'percentage') !== false || stripos($key, 'rate') !== false) {
        return $value . '%';
    } elseif (stripos($key, 'roi') !== false || stripos($key, 'increase') !== false) {
        return $value;
    } else {
        return $value;
    }
}

function getCaseStudyMetrics($resultsData) {
    if (empty($resultsData)) {
        return [];
    }
    
    $metrics = [];
    foreach ($resultsData as $key => $value) {
        $metrics[] = [
            'key' => $key,
            'value' => $value,
            'formatted_value' => formatMetric($key, $value),
            'label' => ucwords(str_replace('_', ' ', $key))
        ];
    }
    
    return $metrics;
}

// Include the template file
include $templateFile;
?>
