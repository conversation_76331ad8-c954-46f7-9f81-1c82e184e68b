<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Growth Scaling Visualization</title>
    <style>
        .growth-chart-container {
            width: 100%;
            height: 400px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
            margin: 20px 0;
        }

        .chart-area {
            position: absolute;
            top: 40px;
            left: 60px;
            right: 40px;
            bottom: 60px;
            border-left: 2px solid #e0e0e0;
            border-bottom: 2px solid #e0e0e0;
        }

        /* Grid Lines */
        .grid-line {
            position: absolute;
            background: #f0f0f0;
        }

        .grid-line.horizontal {
            width: 100%;
            height: 1px;
        }

        .grid-line.vertical {
            width: 1px;
            height: 100%;
        }

        /* Y-axis labels */
        .y-label {
            position: absolute;
            left: -50px;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
            font-size: 16px;
            color: #999;
            text-align: right;
            width: 40px;
            transform: translateY(-50%);
        }

        /* X-axis labels */
        .x-label {
            position: absolute;
            bottom: -40px;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
            font-size: 16px;
            color: #999;
            text-align: center;
            transform: translateX(-50%);
        }

        /* Growth Lines */
        .growth-line {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .line-path {
            fill: none;
            stroke-width: 3;
            stroke-linecap: round;
            stroke-linejoin: round;
        }

        .traditional-line {
            stroke: #aaaaaa;
            stroke-width: 2;
        }

        .ai-line {
            stroke: url(#pinkOrangeGradient);
            stroke-width: 3;
            filter: drop-shadow(0 0 4px rgba(244, 88, 136, 0.3));
        }

        /* Data Points */
        .data-point {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }

        .traditional-point {
            background: #aaaaaa;
            width: 6px;
            height: 6px;
        }

        .ai-point {
            background: linear-gradient(45deg, #f45888 0%, #ee5c46 100%);
            width: 8px;
            height: 8px;
            box-shadow: 0 2px 8px rgba(244, 88, 136, 0.4);
        }

        /* Line Labels */
        .line-label {
            position: absolute;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
            font-size: 14px;
            white-space: nowrap;
            z-index: 15;
        }

        .traditional-label {
            color: #aaaaaa;
            right: -180px;
            top: 75%;
        }

        .ai-label {
            color: #f45888;
            font-weight: 600;
            right: -160px;
            top: 10%;
        }

        /* Annotations */
        .annotation {
            position: absolute;
            z-index: 20;
        }

        .annotation-box {
            background: white;
            border: 1px solid #f0f0f0;
            border-radius: 15px;
            padding: 8px 16px;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, Arial, sans-serif;
            font-size: 12px;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .annotation.diminishing {
            top: 40%;
            right: 20%;
        }

        .annotation.diminishing .annotation-box {
            color: #aaaaaa;
        }

        .annotation.ai-advantage {
            top: 20%;
            right: 15%;
        }

        .annotation.ai-advantage .annotation-box {
            color: #f45888;
        }

        /* Responsive Design */
        @media (max-width: 767px) {
            .growth-chart-container {
                height: 450px;
                margin: 30px 0;
                border-radius: 12px;
            }

            .y-label, .x-label {
                font-size: 18px;
            }

            .line-label {
                font-size: 16px;
            }

            .annotation-box {
                font-size: 14px;
                padding: 10px 18px;
            }

            .chart-area {
                left: 70px;
                bottom: 70px;
            }

            .y-label {
                left: -60px;
                width: 50px;
            }

            .x-label {
                bottom: -50px;
            }
        }

        @media (max-width: 480px) {
            .growth-chart-container {
                height: 400px;
            }

            .y-label, .x-label {
                font-size: 20px;
            }

            .line-label {
                font-size: 18px;
            }

            .annotation {
                display: none; /* Hide annotations on very small screens for clarity */
            }
        }

        /* Animation */
        .line-path {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: drawLine 2s ease-out forwards;
        }

        .ai-line {
            animation-delay: 0.5s;
        }

        .data-point {
            opacity: 0;
            animation: fadeIn 0.3s ease-out forwards;
        }

        .data-point:nth-child(1) { animation-delay: 0.3s; }
        .data-point:nth-child(2) { animation-delay: 0.6s; }
        .data-point:nth-child(3) { animation-delay: 0.9s; }
        .data-point:nth-child(4) { animation-delay: 1.2s; }
        .data-point:nth-child(5) { animation-delay: 1.5s; }
        .data-point:nth-child(6) { animation-delay: 1.8s; }

        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        /* Gradient Definition */
        .gradient-defs {
            position: absolute;
            width: 0;
            height: 0;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="growth-chart-container">
        <!-- SVG for gradients only -->
        <svg class="gradient-defs">
            <defs>
                <linearGradient id="pinkOrangeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stop-color="#f45888" />
                    <stop offset="100%" stop-color="#ee5c46" />
                </linearGradient>
            </defs>
        </svg>

        <div class="chart-area">
            <!-- Grid Lines -->
            <div class="grid-line horizontal" style="top: 0%;"></div>
            <div class="grid-line horizontal" style="top: 16.67%;"></div>
            <div class="grid-line horizontal" style="top: 33.33%;"></div>
            <div class="grid-line horizontal" style="top: 50%;"></div>
            <div class="grid-line horizontal" style="top: 66.67%;"></div>
            <div class="grid-line horizontal" style="top: 83.33%;"></div>

            <div class="grid-line vertical" style="left: 20%;"></div>
            <div class="grid-line vertical" style="left: 40%;"></div>
            <div class="grid-line vertical" style="left: 60%;"></div>
            <div class="grid-line vertical" style="left: 80%;"></div>

            <!-- Y-axis Labels -->
            <div class="y-label" style="top: 100%;">0%</div>
            <div class="y-label" style="top: 83.33%;">50%</div>
            <div class="y-label" style="top: 66.67%;">100%</div>
            <div class="y-label" style="top: 50%;">150%</div>
            <div class="y-label" style="top: 33.33%;">200%</div>
            <div class="y-label" style="top: 16.67%;">250%</div>
            <div class="y-label" style="top: 0%;">300%</div>

            <!-- X-axis Labels -->
            <div class="x-label" style="left: 0%;">0</div>
            <div class="x-label" style="left: 20%;">Month 3</div>
            <div class="x-label" style="left: 40%;">Month 6</div>
            <div class="x-label" style="left: 60%;">Month 9</div>
            <div class="x-label" style="left: 80%;">Month 12</div>
            <div class="x-label" style="left: 100%;">Month 15</div>

            <!-- Growth Lines (SVG for smooth curves) -->
            <svg class="growth-line">
                <!-- Traditional Growth Line -->
                <path class="line-path traditional-line" 
                      d="M 0,100 Q 10,60 20,55 Q 30,52 40,50 Q 50,49 60,48.5 Q 70,48.2 80,48 Q 90,47.8 100,47.5" 
                      vector-effect="non-scaling-stroke" 
                      transform="scale(1, 1)" />
                
                <!-- AI Growth Line -->
                <path class="line-path ai-line" 
                      d="M 0,100 Q 10,60 20,55 Q 30,45 40,35 Q 50,28 60,22 Q 70,18 80,15 Q 90,12 100,10" 
                      vector-effect="non-scaling-stroke" 
                      transform="scale(1, 1)" />
            </svg>

            <!-- Data Points - Traditional -->
            <div class="data-point traditional-point" style="left: 0%; top: 100%;"></div>
            <div class="data-point traditional-point" style="left: 20%; top: 55%;"></div>
            <div class="data-point traditional-point" style="left: 40%; top: 50%;"></div>
            <div class="data-point traditional-point" style="left: 60%; top: 48.5%;"></div>
            <div class="data-point traditional-point" style="left: 80%; top: 48%;"></div>
            <div class="data-point traditional-point" style="left: 100%; top: 47.5%;"></div>

            <!-- Data Points - AI -->
            <div class="data-point ai-point" style="left: 0%; top: 100%;"></div>
            <div class="data-point ai-point" style="left: 20%; top: 55%;"></div>
            <div class="data-point ai-point" style="left: 40%; top: 35%;"></div>
            <div class="data-point ai-point" style="left: 60%; top: 22%;"></div>
            <div class="data-point ai-point" style="left: 80%; top: 15%;"></div>
            <div class="data-point ai-point" style="left: 100%; top: 10%;"></div>

            <!-- Line Labels -->
            <div class="line-label traditional-label">Traditional Scaling</div>
            <div class="line-label ai-label">Adzeta AI Scaling</div>

            <!-- Annotations -->
            <div class="annotation diminishing">
                <div class="annotation-box">Diminishing returns as ad spend increases</div>
            </div>
            <div class="annotation ai-advantage">
                <div class="annotation-box">Continuous improvement with AI-powered scaling</div>
            </div>
        </div>
    </div>
</body>
</html>
