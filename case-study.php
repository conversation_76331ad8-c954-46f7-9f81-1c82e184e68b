<?php
/**
 * Individual Case Study Detail Page
 * Displays a single case study based on slug parameter
 */

// Get the slug from URL parameter
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Include the admin bootstrap to get database connection
require_once 'adzeta-admin/bootstrap.php';

// Get case study from database
try {
    // Create CaseStudy model instance
    require_once 'adzeta-admin/src/Models/CaseStudy.php';
    $caseStudyModel = new \AdZetaAdmin\Models\CaseStudy($admin_db);
    
    // Get case study by slug
    $caseStudy = $caseStudyModel->getBySlug($slug);
    
    if (!$caseStudy || $caseStudy['status'] !== 'published') {
        header('HTTP/1.0 404 Not Found');
        include '404.php';
        exit;
    }
    
} catch (Exception $e) {
    error_log('Error loading case study: ' . $e->getMessage());
    header('HTTP/1.0 500 Internal Server Error');
    include '500.php';
    exit;
}

// Set page title and meta description for SEO
$pageTitle = $caseStudy['meta_title'] ?: $caseStudy['title'];
$metaDescription = $caseStudy['meta_description'] ?: $caseStudy['hero_description'];
$ogImage = $caseStudy['og_image'] ?: $caseStudy['featured_image'] ?: $caseStudy['hero_image'];

// Determine which template to use
$template = $caseStudy['template'] ?? 'luminous-skin-clinic';

// Include the appropriate template
$templateFile = $template . '.php';
if (file_exists($templateFile)) {
    include $templateFile;
} else {
    // Fallback to default template
    include 'luminous-skin-clinic-adzeta-predictive-ltv-targeting.php';
}
?>
