<?php

namespace AdZetaAdmin\API;

/**
 * Settings API Controller
 */
class SettingsController extends BaseController
{
    /**
     * Get all settings
     */
    public function index()
    {
        $this->requirePermission('manage_settings');
        
        try {
            $settings = $this->db->fetchAll(
                "SELECT setting_key, setting_value, setting_type, description 
                 FROM settings 
                 ORDER BY setting_key"
            );
            
            // Convert to key-value format
            $formattedSettings = [];
            foreach ($settings as $setting) {
                $formattedSettings[$setting['setting_key']] = [
                    'value' => $this->castSettingValue($setting['setting_value'], $setting['setting_type']),
                    'type' => $setting['setting_type'],
                    'description' => $setting['description']
                ];
            }
            
            return $this->success(['settings' => $formattedSettings]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to load settings: ' . $e->getMessage());
        }
    }
    
    /**
     * Get single setting
     */
    public function show($key)
    {
        $this->requireAuth();
        
        try {
            $setting = $this->db->fetch(
                "SELECT setting_key, setting_value, setting_type, description 
                 FROM settings 
                 WHERE setting_key = ?",
                [$key]
            );
            
            if (!$setting) {
                return $this->error('Setting not found', 404);
            }
            
            return $this->success([
                'setting' => [
                    'key' => $setting['setting_key'],
                    'value' => $this->castSettingValue($setting['setting_value'], $setting['setting_type']),
                    'type' => $setting['setting_type'],
                    'description' => $setting['description']
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to load setting: ' . $e->getMessage());
        }
    }
    
    /**
     * Update settings
     */
    public function update()
    {
        $this->requirePermission('manage_settings');
        $data = $this->getRequestData();
        
        if (empty($data)) {
            return $this->error('No settings data provided');
        }
        
        try {
            $updated = 0;
            
            foreach ($data as $key => $value) {
                // Check if setting exists
                $existing = $this->db->fetch(
                    "SELECT setting_type FROM settings WHERE setting_key = ?",
                    [$key]
                );
                
                if ($existing) {
                    // Convert value to string for storage
                    $stringValue = $this->convertToString($value, $existing['setting_type']);
                    
                    $success = $this->db->update(
                        'settings',
                        ['setting_value' => $stringValue],
                        'setting_key = ?',
                        [$key]
                    );
                    
                    if ($success) {
                        $updated++;
                    }
                }
            }
            
            return $this->success([
                'message' => "Updated {$updated} settings successfully",
                'updated_count' => $updated
            ]);
            
        } catch (\Exception $e) {
            return $this->error('Failed to update settings: ' . $e->getMessage());
        }
    }
    
    /**
     * Cast setting value to appropriate type
     */
    private function castSettingValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return (bool)$value;
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }
    
    /**
     * Convert value to string for storage
     */
    private function convertToString($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'number':
                return (string)$value;
            case 'json':
                return json_encode($value);
            default:
                return (string)$value;
        }
    }
}
