/* Bootstrap Icon Styling with Circular Background */
.feature-box-icon {
    margin-right: 20px;
    transition: all 0.3s ease;
}

.feature-box:hover .feature-box-icon {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(233, 88, 161, 0.15);
}

.feature-box-icon i {
    transition: all 0.3s ease;
}

.feature-box:hover .feature-box-icon i {
    transform: scale(1.1);
}

/* Ensure proper spacing and alignment */
.feature-box-left-icon {
    display: flex;
    align-items: flex-start;
}

.feature-box-content {
    flex: 1;
}

/* Text gradient color for icons */
.text-gradient-base-color {
    background: linear-gradient(to right, #e958a1, #ff5d74);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}
