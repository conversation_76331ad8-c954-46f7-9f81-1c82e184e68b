<?php include 'header.php'; ?>
        <!-- Safari iOS Accordion Fixes -->
      
        <!-- end header -->
        <!-- start hero section -->
        <section class="cover-background top-space-padding pb-0 overflow-hidden position-relative lg-pb-30px hero-section platform">
            <div class="professional-gradient-container">
                <div class="corner-gradient top-left"></div>
                <div class="corner-gradient top-right"></div>
                <div class="corner-gradient bottom-left"></div>
                <div class="corner-gradient bottom-right"></div>
                <div class="diagonal-gradient"></div>
                <div class="mesh-overlay"></div>
                <div class="vignette-overlay"></div>
            </div>
            <!--             <div id="particles-style-03" class="h-100 position-absolute left-0px top-0 w-100" data-particle="true" data-particle-options='{"particles": {"number": {"value": 70,"density": {"enable": true,"value_area": 1800}},"color": {"value": ["#e958a1", "#d15ec7", "#ff7042", "#8f76f5", "#ff5a5a"]},"shape": {"type": ["circle"],"stroke":{"width":0,"color":"#000000"}},"opacity": {"value": 0.5,"random": true,"anim": {"enable": true,"speed": 0.8,"sync": false}},"size": {"value": 3,"random": true,"anim": {"enable": true,"speed": 0.8,"sync": false}},"line_linked":{"enable":true,"distance":180,"color":"#e958a1","opacity":0.2,"width":1},"move": {"enable": true,"speed":1.0,"direction": "none","random": true,"straight": false,"out_mode": "out","bounce": false,"attract": {"enable": true,"rotateX": 600,"rotateY": 1200}}},"interactivity": {"detect_on": "canvas","events": {"onhover": {"enable": true,"mode": "grab"},"onclick": {"enable": true,"mode": "repulse"},"resize": true},"modes":{"grab":{"distance":180,"line_linked":{"opacity":0.4}},"repulse":{"distance":200,"duration":0.4}}},"retina_detect": true}'></div>
                -->
            <div class="container h-100">
                <!-- Removed distracting background elements for a more professional look -->
                <div class="row align-items-center h-100 md-mt-30px md-mb-10px pt-4">
                    <div class="col-xl-6 col-lg-6 mb-9 position-relative z-index-1 ps-lg-5" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                        <div class="d-flex align-items-center mb-15px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
                            <span class="fs-12 fw-light text-white opacity-90 primary-font ls-wide">
                                <span class="ai-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="pulse">
                                        <!-- Modern AI chip/processor shape -->
                                        <rect x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5"/>
                                        <!-- Circuit lines -->
                                        <path class="circuit1" d="M8 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit2" d="M12 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit3" d="M16 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit4" d="M8 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit5" d="M12 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit6" d="M16 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit7" d="M2 8H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit8" d="M2 12H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit9" d="M2 16H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit10" d="M20 8H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit11" d="M20 12H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <path class="circuit12" d="M20 16H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                        <!-- Inner processor grid -->
                                        <path class="grid" d="M8 8H16M8 12H16M8 16H16M8 8V16M12 8V16M16 8V16" stroke="url(#paint0_linear_ai_icon)" stroke-width="0.75" stroke-linecap="round" stroke-dasharray="1 2"/>
                                        <!-- Central core -->
                                        <rect class="core" x="10" y="10" width="4" height="4" fill="url(#paint0_linear_ai_icon)"/>
                                        <!-- Sparkle overlay -->
                                        <rect class="sparkle" x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint1_linear_ai_icon)" stroke-width="1" stroke-opacity="0.7"/>
                                        <defs>
                                            <linearGradient id="paint0_linear_ai_icon" x1="2" y1="2" x2="22" y2="22" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#e958a1"/>
                                                <stop offset="0.5" stop-color="#8f76f5"/>
                                                <stop offset="1" stop-color="#4a9eff"/>
                                            </linearGradient>
                                            <linearGradient id="paint1_linear_ai_icon" x1="20" y1="4" x2="4" y2="20" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#ffffff"/>
                                                <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                </span>
                                <span class="text-gradient-purple-blue ls-3px">ADZETA PLATFORM</span>
                                <span class="mx-2">|</span>
                                <span data-fancy-text='{
                                    "effect": "rotate",
                                    "string": [
                                    "AI-Powered Value Bidding",
                                    "Predictive LTV Modeling",
                                    "First-Party Data Activation",
                                    "Automated Profit Forecasting",
                                    "Omnichannel Ad Optimization",
                                    "Continuous Learning AI",
                                    "Customer Journey Insights"
                                    ],
                                    "speed": 50,
                                    "duration": 3500
                                    }'></span>
                            </span>
                        </div>
                        <h1 class="alt-font mb-15px fs-55 md-fs-65 sm-fs-65 xs-fs-50 fw-600 heading-gradient" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 1000, "delay": 300, "easing": "easeOutQuad" }'>
                            <span class="fw-300"> A growth powerhouse<br>
                            </span>
                            running <span class="highlight-text">on AI</span>
                        </h1>
                        <div class="alt-font fw-400 fs-16 w-90 sm-w-100 mb-25px xs-mb-20px text-white opacity-75 lh-1-5" data-anime='{ "translateY": [20, 0], "opacity": [0,75], "duration": 1000, "delay": 800, "easing": "easeOutQuint" }'>AdZeta predicts which customers will drive true lifetime value (LTV), then automatically optimizes your Google & Meta ads using intelligent Value-Based Bidding (VBB) to maximize profits and growth.</div>
                        <div class="d-flex flex-wrap" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 1000, "easing": "easeOutQuint" }'>
                            <a href="free-ad-audit.php" class="btn btn-large btn-hover-animation box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-gradient-pink-orange btn-rounded me-15px mt-15px fw-500 alt-font">
                            <span>
                            <span class="btn-text fs-16 md-fs-16">Consult a Growth Expert</span>
                            <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
                            </span>
                            </a>
                        </div>
                    </div>
                    <div class="col-xl-6 col-lg-6 align-self-center">
                        <div class="platform-animation-container pt-4" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 500 }'>
                            <!-- New Hero Animation Structure -->
                            <div class="hero-animation-container">
                                <!-- Center Element - Adzeta AI Core -->
                                <div class="animation-element adzeta-ai-center">
                                    <div class="position-relative">
                                        <img src="images/adzeta-ai-center.png" alt="Adzeta AI Core" class="w-100">
                                        <div class="element-label label-center">ADZETA AI</div>
                                    </div>
                                </div>
                                <!-- Left Element - Data Input -->
                                <div class="animation-element adzeta-ai-left">
                                    <img src="images/adzeta-ai-left.png" alt="Data Input" class="w-100">
                                    <div class="element-label label-left">DATA</div>
                                </div>
                                <!-- Right Element - Ad Platforms -->
                                <div class="animation-element adzeta-ai-right">
                                    <img src="images/adzeta-ai-right.png" alt="Ad Platforms" class="w-100">
                                    <div class="element-label label-right">AD PLATFORMS</div>
                                </div>
                                <!-- Bottom Element - Results -->
                                <div class="animation-element adzeta-ai-bottom">
                                    <img src="images/adzeta-ai-bottom.png" alt="Results" class="w-100">
                                    <div class="element-label label-bottom">RESULTS</div>
                                </div>
                                <!-- Connection Lines -->
                                <div class="connection-line connection-left"></div>
                                <div class="connection-line connection-right"></div>
                                <div class="connection-line connection-bottom"></div>
                                <!-- Data Flow Particles -->
                                <div class="data-particle particle-left-1"></div>
                                <div class="data-particle particle-left-2"></div>
                                <div class="data-particle particle-right-1"></div>
                                <div class="data-particle particle-right-2"></div>
                                <div class="data-particle particle-bottom-1"></div>
                                <div class="data-particle particle-bottom-2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end hero section -->
        <!-- start section -->
        <!-- start section: The Adzeta Journey -->
        <section class="position-relative overflow-hidden bg-white">
            <div class="container position-relative">
                <div class="row justify-content-center mb-3">
                    <div class="col-xxl-6 col-xl-7 col-lg-8 col-md-9 col-sm-10 text-center ">
                        <h2 class="alt-font fw-700 ls-minus-1px "><span class="modern-heading-gradient">Your Path to Predictable Growth</span></h2>
                        <p>Our AI-powered platform guides you through a proven journey to transform your e-commerce advertising strategy and achieve sustainable growth.</p>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="row" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                            <!-- start process step item -->
                            <div class="col-lg-4 col-md-4 text-center position-relative" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
                                <div class="mb-4">
                                    <a href="#analyze-section" class="section-link process-step-circle bg-white d-inline-block position-relative">
                                        <i class="bi bi-graph-up-arrow text-gradient-pink-orange"></i>
                                    </a>
                                </div>
                                <h5 class="alt-font fw-600 text-dark-gray mb-3">Analyze</h5>
                                <p class="md-pe-5 md-ps-5">Our AI connects to your data to understand the deep signals driving long-term customer profitability (LTV).</p>
                                <a href="#analyze-section" class="section-link btn btn-link-gradient thin text-gradient-pink-orange  md-mx-auto fw-600 p-0 mt-2 mb-5">
							
                                    Read More <i class="bi bi-arrow-right ms-1"></i>
                                </a>
                                <!-- Arrow right -->
                                <div class="d-none d-md-block position-absolute" style="top: 60px; right: -20px; z-index: 1;">
                                    <i class="bi bi-chevron-right text-gradient-pink-orange" style="font-size: 28px;"></i>
                                </div>
                            </div>
                            <!-- end process step item -->
                            <!-- start process step item -->
                            <div class="col-lg-4 col-md-4 text-center position-relative" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 400, "easing": "easeOutQuad" }'>
                                <div class="mb-4">
                                    <a href="#optimize-section" class="section-link process-step-circle bg-white d-inline-block position-relative">
                                        <i class="bi bi-bullseye text-gradient-pink-orange"></i>
                                    </a>
                                </div>
                                <h5 class="alt-font fw-600 text-dark-gray mb-3">Optimize</h5>
                                <p class="md-pe-5 md-ps-5">Predictive insights fuel automated Value-Based Bidding on key platforms, focusing budget on high-LTV acquisitions.</p>
                                <a href="#optimize-section" class="section-link btn btn-link-gradient thin text-gradient-pink-orange  md-mx-auto fw-600 p-0 mt-2 mb-5">
                                    Read More <i class="bi bi-arrow-right ms-1"></i>
                                </a>
                                <!-- Arrow right -->
                                <div class="d-none d-md-block position-absolute" style="top: 60px; right: -20px; z-index: 1;">
                                    <i class="bi bi-chevron-right text-gradient-pink-orange" style="font-size: 28px;"></i>
                                </div>
                            </div>
                            <!-- end process step item -->
                            <!-- start process step item -->
                            <div class="col-lg-4 col-md-4 text-center position-relative" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 500, "easing": "easeOutQuad" }'>
                                <div class="mb-4">
                                    <a href="#scale-section" class="section-link process-step-circle bg-white d-inline-block position-relative">
                                        <i class="bi bi-rocket-takeoff text-gradient-pink-orange"></i>
                                    </a>
                                </div>
                                <h5 class="alt-font fw-600 text-dark-gray mb-3">Scale</h5>
                                <p class="md-pe-5 md-ps-5">Move beyond limitations with reliable performance, enabling confident budget scaling and sustainable growth.</p>
                                <a href="#scale-section" class="section-link btn btn-link-gradient thin text-gradient-pink-orange  md-mx-auto fw-600 p-0 mt-2">
                                    Read More <i class="bi bi-arrow-right ms-1"></i>
                                </a>
                            </div>
                            <!-- end process step item -->
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section: The Adzeta Journey -->

   

        <!-- start section:Predictive AI Showcase with Responsive Tabs/Accordion -->
        <section id="analyze-section-tabs" class="predictive-ai-showcase-tabs platform">
            <style>      
/* Responsive Tabs/Accordion Styles */
 .predictive-ai-showcase-tabs {
     padding: 100px 0 80px;
     position: relative;
     overflow: hidden;
     background: linear-gradient(166deg, #F2F0EE 0%, #f6f4f9 25%, #f6f4f9 45%, #f6f4f9 70%, #f6f4f9 100%);
}
 .analyze-tabs-section {
     --base-color: #e958a1;
     --dark-gray: #232323;
     --extra-medium-gray: #e5e5e5;
     --very-light-gray: #f7f7f7;
     --transparent-dark-very-light: rgba(35, 35, 35, 0.1);
}
/* Tab styles for desktop */
 .analyze-tab-style .nav-tabs {
     border: none;
     justify-content: center;
}
 .analyze-tab-style .nav-link {
     position: relative;
     border: none;
     background: none;
     color: var(--dark-gray);
     transition: all 0.3s ease;
     cursor: pointer;
}
 .analyze-tab-style .nav-link:hover, .analyze-tab-style .nav-link.active {
     color: var(--base-color);
     background: none;
     border: none;
}
/* Accordion styles for mobile */
 .analyze-accordion-style .accordion-item {
     margin-bottom: 15px;
     border-radius: 8px;
     overflow: hidden;
     box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}
 .analyze-accordion-style .accordion-header button {
     background: white;
     border: none;
     color: var(--dark-gray);
     font-weight: 600;
     font-size: 16px;
     padding: 20px;
     position: relative;
     width: 100%;
     text-align: left;
}
 .analyze-accordion-style .accordion-header button:not(.collapsed) {
     color: var(--base-color);
     background: white;
     box-shadow: none;
}
 .analyze-accordion-style .accordion-body {
     padding: 0;
     border-top: 1px solid var(--extra-medium-gray);
}
/* Content item styling */
 .analyze-content-item {
}
/* Panel container styling to match original */
/* Responsive behavior */
 @media (min-width: 992px) {
    /* Desktop: Show tabs, hide accordion completely */
     .analyze-tab-navigation {
         display: block;
    }
     .analyze-accordion-style .accordion-item {
         border: none !important;
         margin-bottom: 0 !important;
         border-radius: 0 !important;
         box-shadow: none !important;
         background: transparent !important;
    }
     .analyze-accordion-style .accordion-header {
         display: none !important;
    }
     .analyze-accordion-style .accordion-collapse {
         display: block !important;
         border: none !important;
    }
     .analyze-accordion-style .accordion-body {
         padding: 0 !important;
         border: none !important;
         background: transparent !important;
    }
     .analyze-content-item:not(.active) {
         display: none;
    }
     .analyze-content-item.active {
         display: block;
    }
}
 @media (max-width: 991.98px) {
    /* Mobile: Hide tabs, show accordion */
     .analyze-tab-navigation {
         display: none;
    }
     .analyze-accordion-style .accordion-header {
         display: block;
    }
     .analyze-content-item {
         display: block;
    }
     .panel-container {
         flex-direction: column;
         gap: 20px;
    }
     .panel-visual {
         min-width: auto;
    }
     .panel-content {
         min-width: auto;
    }
     .svg-container {
         height: 200px;
    }
}
 @media (max-width: 575.98px) {
     .analyze-tab-style .nav-link {
         padding: 15px 20px;
         font-size: 14px;
    }
     .svg-container {
         height: 150px;
    }
     .panel-title {
         font-size: 20px;
    }
     .feature-item {
         padding: 12px;
    }
}

            </style>

            <div class="container position-relative analyze-tabs-section" style="z-index: 1;">
                <div class="row justify-content-center mb-50px">
                    <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                        <div class="scale-tag-container">
                            <div class="scale-tag">ANALYZE</div>
                        </div>
                        <h2 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Our Predictive AI</span></h2>
                        <p>We employ sophisticated machine learning trained for e-commerce to generate accurate LTV predictions, giving you the foresight to transform your ad strategy.</p>
                    </div>
                </div>

                <!-- Tab Navigation (Desktop only) -->
                <div class="analyze-tab-navigation">
                    <div class="analyze-tab-style tab-style-08 border-bottom border-color-extra-medium-gray bg-white" style="box-shadow: 0 5px 30px rgba(0,0,0,0.1); border-radius: 12px 12px 0 0;">
                      
                            <ul class="nav nav-tabs border-0 fw-500 text-center">
                                <li class="nav-item">
                                    <a class="nav-link active" data-target="data-content">
                                        <i class="bi bi-database me-2"></i>Leveraging Your Data
                                        <span class="tab-border bg-base-color"></span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-target="modeling-content">
                                        <i class="bi bi-diagram-3 me-2"></i>E-commerce Modeling
                                        <span class="tab-border bg-base-color"></span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-target="prediction-content">
                                        <i class="bi bi-graph-up-arrow me-2"></i>Predicting Profit
                                        <span class="tab-border bg-base-color"></span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-target="learning-content">
                                        <i class="bi bi-arrow-repeat me-2"></i>Continuous Learning
                                        <span class="tab-border bg-base-color"></span>
                                    </a>
                                </li>
                            </ul>
                       
                    </div>
                </div>

                <!-- Content Container -->
                <div class="mt-4">
                    <div class="accordion analyze-accordion-style" id="analyzeAccordion">

                        <!-- Leveraging Your Data -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-data-content" aria-expanded="true" aria-controls="collapse-data-content">
                                    <i class="bi bi-database me-2"></i>Leveraging Your Data
                                </button>
                            </h2>
                            <div id="collapse-data-content" class="accordion-collapse collapse show">
                                <div class="accordion-body">
                                    <div class="analyze-content-item active" id="data-content">
                                        <div class="panel-container">
                                            <div class="panel-visual">
                                                <div class="svg-container">
                                                    <object type="image/svg+xml" data="images/data-flow-animation.svg" class="svg-animation">
                                                    Your browser does not support SVG
                                                    </object>
                                                </div>
                                            </div>
                                            <div class="panel-content">
                                                <h3 class="panel-title alt-font fw-600">Leveraging Your First-Party Data</h3>
                                                <p class="panel-description">Our AI connects securely to analyze key first-party signals, creating a comprehensive view of your customer journey without relying solely on limited platform pixels.</p>
                                                <div class="panel-features">
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-cart-check"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Transactional History</strong>Purchase patterns, AOV, product preferences, and frequency
                                                        </div>
                                                    </div>
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-mouse2"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Website/App Behavior</strong>Engagement metrics, browsing patterns, and interaction signals
                                                        </div>
                                                    </div>
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-people"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>CRM Attributes</strong>Customer profiles, segments, and historical interactions
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- E-commerce Modeling -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-modeling-content" aria-expanded="false" aria-controls="collapse-modeling-content">
                                    <i class="bi bi-diagram-3 me-2"></i>E-commerce Modeling
                                </button>
                            </h2>
                            <div id="collapse-modeling-content" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <div class="analyze-content-item" id="modeling-content">
                                        <div class="panel-container">
                                            <div class="panel-visual">
                                                <div class="svg-container">
                                                    <object type="image/svg+xml" data="images/modeling-animation.svg" class="svg-animation">
                                                    Your browser does not support SVG
                                                    </object>
                                                </div>
                                            </div>
                                            <div class="panel-content">
                                                <h3 class="panel-title alt-font fw-600">Advanced E-commerce Modeling</h3>
                                                <p class="panel-description">We build bespoke machine learning models analyzing hundreds of signals to understand the nuanced drivers of LTV in your specific vertical and business context.</p>
                                                <div class="panel-features">
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-fingerprint"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Tailored to Your Business</strong>Custom models built for your specific industry and customer base
                                                        </div>
                                                    </div>
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-layers"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Multi-layered Analysis</strong>Deep neural networks that identify complex patterns humans can't see
                                                        </div>
                                                    </div>
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-shield-check"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Privacy-Focused</strong>Models work with aggregated data patterns, not individual identities
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Predicting Profit -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-prediction-content" aria-expanded="false" aria-controls="collapse-prediction-content">
                                    <i class="bi bi-graph-up-arrow me-2"></i>Predicting Profit
                                </button>
                            </h2>
                            <div id="collapse-prediction-content" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <div class="analyze-content-item" id="prediction-content">
                                        <div class="panel-container">
                                            <div class="panel-visual">
                                                <div class="svg-container">
                                                    <object type="image/svg+xml" data="images/prediction-animation.svg" class="svg-animation">
                                                    Your browser does not support SVG
                                                    </object>
                                                </div>
                                            </div>
                                            <div class="panel-content">
                                                <h3 class="panel-title alt-font fw-600">Predicting Key Profit Indicators</h3>
                                                <p class="panel-description">Our AI forecasts future purchase probability, expected revenue within key timeframes, and identifies segments with the highest profit potential (LTV), often before the first purchase.</p>
                                                <div class="panel-features">
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-calendar-check"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Time-based Forecasting</strong>Predictions for 30, 60, 90-day and lifetime value
                                                        </div>
                                                    </div>
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-bar-chart"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Confidence Intervals</strong>Understanding the range and reliability of predictions
                                                        </div>
                                                    </div>
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-segmented-nav"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Segment Analysis</strong>Identifying your most valuable customer groups
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Continuous Learning -->
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-learning-content" aria-expanded="false" aria-controls="collapse-learning-content">
                                    <i class="bi bi-arrow-repeat me-2"></i>Continuous Learning
                                </button>
                            </h2>
                            <div id="collapse-learning-content" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <div class="analyze-content-item" id="learning-content">
                                        <div class="panel-container">
                                            <div class="panel-visual">
                                                <div class="svg-container">
                                                    <object type="image/svg+xml" data="images/learning-animation.svg" class="svg-animation">
                                                    Your browser does not support SVG
                                                    </object>
                                                </div>
                                            </div>
                                            <div class="panel-content">
                                                <h3 class="panel-title alt-font fw-600">Continuous Learning & Adaptation</h3>
                                                <p class="panel-description">Adzeta's models constantly learn and adapt to changing user behaviors and market dynamics, ensuring your LTV predictions remain accurate and effective over time.</p>
                                                <div class="panel-features">
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Automatic Retraining</strong>Models update as new data becomes available
                                                        </div>
                                                    </div>
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-calendar3"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Seasonal Adaptation</strong>Adjusts to changing patterns during different times of year
                                                        </div>
                                                    </div>
                                                    <div class="feature-item">
                                                        <div class="feature-icon">
                                                            <i class="bi bi-graph-up"></i>
                                                        </div>
                                                        <div class="feature-text">
                                                            <strong>Performance Monitoring</strong>Continuous validation against real outcomes
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>


            </div>
        </section>
        <!-- end section: Duplicate ANALYZE with Responsive Tabs/Accordion -->

        <!-- start section 2: Value-Based Bidding Activated -->
        <section id="optimize-section" class="overflow-hidden valuebid-showcase bg-white">
            <div class="container position-relative" style="z-index: 1;">
                <div class="row justify-content-center mb-50px">
                    <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                        <div class="scale-tag-container">
                            <div class="scale-tag">OPTIMIZE</div>
                        </div>
                        <h2 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">ValueBid™ Framework:<br> Turning Predictions Into Action</span></h2>
                        <p>Accurate predictions are just the start. The crucial next step is translating that foresight into automated, intelligent action within ad platforms. We've engineered the ValueBid™ Framework to bridge that gap.</p>
                    </div>
                </div>
                <div class="row align-items-center" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                    <div class="col-xl-5 col-lg-6 md-mb-50px">
                        <div class="accordion accordion-style-01" id="accordion-style-04" data-active-icon="bi bi-chevron-up" data-inactive-icon="bi bi-chevron-down">
                            <!-- start accordion item -->
                            <div class="accordion-item active-accordion">
                                <div class="accordion-header">
                                    <a href="#" data-bs-toggle="collapse" data-bs-target="#accordion-style-04-01" aria-expanded="true" data-bs-parent="#accordion-style-04">
                                        <div class="accordion-title position-relative mb-0 pe-20px text-dark-gray fw-600 alt-font">
                                            <span class="feature-icon-wrapper"><i class="bi bi-code-square"></i></span>
                                            <span>Decoding Ad Platform Algorithms</span>
                                            <i class="bi bi-chevron-up icon-small"></i>
                                        </div>
                                    </a>
                                </div>
                                <div id="accordion-style-04-01" class="accordion-collapse collapse show" data-bs-parent="#accordion-style-04">
                                    <div class="accordion-body bg-white last-paragraph-no-margin">
                                        <p>We understand how Google's VBB and Meta's Value Optimization work. Our framework sends predictive signals at the right time and in the right format for their AI to learn from effectively and efficiently.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end accordion item -->
                            <!-- start accordion item -->
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <a href="#" data-bs-toggle="collapse" data-bs-target="#accordion-style-04-02" aria-expanded="false" data-bs-parent="#accordion-style-04">
                                        <div class="accordion-title position-relative mb-0 pe-20px text-dark-gray fw-600 alt-font">
                                            <span class="feature-icon-wrapper"><i class="bi bi-hdd-network"></i></span>
                                            <span>Seamless API Integration</span>
                                            <i class="bi bi-chevron-down icon-small"></i>
                                        </div>
                                    </a>
                                </div>
                                <div id="accordion-style-04-02" class="accordion-collapse collapse" data-bs-parent="#accordion-style-04">
                                    <div class="accordion-body bg-white last-paragraph-no-margin">
                                        <p>Adzeta integrates directly via secure APIs (like Google OCI, Meta CAPI) to feed LTV predictions as value signals, enhancing platform bidding without manual uploads or guesswork.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end accordion item -->
                            <!-- start accordion item -->
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <a href="#" data-bs-toggle="collapse" data-bs-target="#accordion-style-04-03" aria-expanded="false" data-bs-parent="#accordion-style-04">
                                        <div class="accordion-title position-relative mb-0 pe-20px text-dark-gray fw-600 alt-font">
                                            <span class="feature-icon-wrapper"><i class="bi bi-gear-wide-connected"></i></span>
                                            <span>Automated Bid Optimization</span>
                                            <i class="bi bi-chevron-down icon-small"></i>
                                        </div>
                                    </a>
                                </div>
                                <div id="accordion-style-04-03" class="accordion-collapse collapse" data-bs-parent="#accordion-style-04">
                                    <div class="accordion-body bg-white last-paragraph-no-margin">
                                        <p>Based on predicted value, bids are automatically adjusted – investing more to win high-LTV prospects and reducing or eliminating bids for low-value segments, maximizing your budget's profit potential.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end accordion item -->
                            <!-- start accordion item -->
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <a href="#" data-bs-toggle="collapse" data-bs-target="#accordion-style-04-04" aria-expanded="false" data-bs-parent="#accordion-style-04">
                                        <div class="accordion-title position-relative mb-0 pe-20px text-dark-gray fw-600 alt-font">
                                            <span class="feature-icon-wrapper"><i class="bi bi-shield-check"></i></span>
                                            <span>Ensuring Signal Quality & Confidence</span>
                                            <i class="bi bi-chevron-down icon-small"></i>
                                        </div>
                                    </a>
                                </div>
                                <div id="accordion-style-04-04" class="accordion-collapse collapse" data-bs-parent="#accordion-style-04">
                                    <div class="accordion-body bg-white last-paragraph-no-margin">
                                        <p>We monitor signal delivery and model performance to ensure ad platforms receive consistent, high-quality data, building confidence in value-based outcomes.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end accordion item -->
                        </div>
                    </div>
                    <div class="col-xl-7 offset-xl-0 col-lg-6 position-relative">
                        <div class="svg-container">
                            <object type="image/svg+xml" data="images/valuebid-framework.svg" class="svg-animation">
                            Your browser does not support SVG
                            </object>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section 2 -->
        <!-- start section 3: Scale Your Growth -->
        <section id="scale-section" class="scale-section pt-0 overflow-hidden position-relative">
            <div class="light-gradient-container position-absolute w-100 h-100"
                style="background: linear-gradient(to bottom, #f2f0ee 0%, #f5f3f1 50%, #ffffff 100%); z-index: 0;"></div>
            <div class="light-accent-gradient position-absolute w-100 h-100"
                style="background: radial-gradient(circle at center, rgba(233, 88, 161, 0.08) 0%, rgba(255, 255, 255, 0) 70%),
                radial-gradient(circle at top right, rgba(143, 118, 245, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
                z-index: 0;"></div>
            <div class="floating-element floating-2" style="top: 50%; left: -100px;"></div>
            <div class="container">
                <div class="row justify-content-center mb-50px mt-5">
                    <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                        <div class="scale-tag-container">
                            <div class="scale-tag">SCALE</div>
                        </div>
                        <h2 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Scale Your Growth with Confidence</span></h2>
                        <p class="scale-description">Break through traditional e-commerce growth plateaus with AI-powered scaling that eliminates guesswork. Our platform enables confident budget expansion with predictable ROAS, turning your ad spend into a self-reinforcing profit engine.</p>
                    </div>
                </div>
				  <!-- Apple-inspired minimal layout -->
                <div class="row mb-60px">
                    <div class="col-12" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                        <div class="dual-growth-comparison" id="growthComparisonContainer">
                            <!-- Comparison Header -->
                            <div class="comparison-header">
                                <h3 class="comparison-title">Growth Trajectory Comparison</h3>
                                <p class="comparison-subtitle">See how AI-powered scaling eliminates traditional growth plateaus</p>
                                <button class="animate-button" id="animateGrowth">
                                    <i class="bi bi-play-circle"></i>
                                    <span>Watch Both Journeys</span>
                                </button>
                            </div>

                            <!-- Dual Chart Container -->
                            <div class="dual-chart-container">
                                <!-- Traditional Scaling Chart -->
                                <div class="chart-section traditional-section">
                                    <div class="chart-header">
                                        <div class="chart-icon traditional-icon">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                <path d="M3 17L9 11L13 15L21 7" stroke="#ff6b6b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M14 7H21V14" stroke="#ff6b6b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                        <div class="chart-info">
                                            <h4>Traditional Scaling</h4>
                                            <p>Diminishing returns pattern</p>
                                        </div>
                                    </div>

                                    <div class="chart-canvas">
                                        <svg class="growth-chart" viewBox="0 0 400 200">
                                            <!-- Grid -->
                                            <defs>
                                                <pattern id="grid" width="40" height="20" patternUnits="userSpaceOnUse">
                                                    <path d="M 40 0 L 0 0 0 20" fill="none" stroke="#e8e8ed" stroke-width="1"/>
                                                </pattern>
                                                <linearGradient id="traditionalGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                    <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1"/>
                                                    <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.7"/>
                                                </linearGradient>
                                            </defs>
                                            <rect width="100%" height="100%" fill="url(#grid)" />

                                            <!-- Traditional Growth Curve -->
                                            <path id="traditionalPath"
                                                  d="M 40 160 Q 120 140 200 130 Q 280 125 360 128"
                                                  stroke="url(#traditionalGradient)"
                                                  stroke-width="4"
                                                  fill="none"
                                                  stroke-dasharray="400"
                                                  stroke-dashoffset="400"
                                                  stroke-linecap="round"/>

                                            <!-- Plateau indicator -->
                                            <circle id="plateauPoint" cx="280" cy="125" r="6" fill="#ff6b6b" opacity="0" style="filter: drop-shadow(0 2px 8px rgba(255,107,107,0.4));">
                                                <animate attributeName="r" values="6;8;6" dur="2s" repeatCount="indefinite" begin="indefinite"/>
                                            </circle>

                                            <!-- Labels -->
                                            <text x="40" y="185" font-size="12" fill="#6d6d70" text-anchor="middle" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">Month 1</text>
                                            <text x="200" y="185" font-size="12" fill="#6d6d70" text-anchor="middle" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">Month 6</text>
                                            <text x="360" y="185" font-size="12" fill="#6d6d70" text-anchor="middle" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">Month 12</text>

                                            <text x="12" y="165" font-size="12" fill="#6d6d70" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">$100K</text>
                                            <text x="12" y="125" font-size="12" fill="#6d6d70" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">$200K</text>
                                            <text x="12" y="85" font-size="12" fill="#6d6d70" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">$300K</text>
                                        </svg>

                                        <div class="chart-overlay traditional-overlay">
                                            <div class="growth-indicator">
                                                <div class="growth-rate">+15%</div>
                                                <div class="growth-label">Slowing Growth</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Adzeta AI Scaling Chart -->
                                <div class="chart-section adzeta-section">
                                    <div class="chart-header">
                                        <div class="chart-icon adzeta-icon">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="#30d158"/>
                                                <path d="M12 6L8 12H16L12 6Z" fill="#ffffff" opacity="0.3"/>
                                            </svg>
                                        </div>
                                        <div class="chart-info">
                                            <h4>Adzeta AI Scaling</h4>
                                            <p>Exponential growth pattern</p>
                                        </div>
                                    </div>

                                    <div class="chart-canvas">
                                        <svg class="growth-chart" viewBox="0 0 400 200">
                                            <!-- Grid -->
                                            <defs>
                                                <linearGradient id="adzetaGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                    <stop offset="0%" style="stop-color:#30d158;stop-opacity:0.8"/>
                                                    <stop offset="100%" style="stop-color:#30d158;stop-opacity:1"/>
                                                </linearGradient>
                                            </defs>
                                            <rect width="100%" height="100%" fill="url(#grid)" />

                                            <!-- Adzeta Growth Curve -->
                                            <path id="adzetaPath"
                                                  d="M 40 160 Q 80 150 160 100 Q 240 50 360 30"
                                                  stroke="url(#adzetaGradient)"
                                                  stroke-width="4"
                                                  fill="none"
                                                  stroke-dasharray="450"
                                                  stroke-dashoffset="450"
                                                  stroke-linecap="round"/>

                                            <!-- Breakthrough indicator -->
                                            <circle id="breakthroughPoint" cx="160" cy="100" r="6" fill="#30d158" opacity="0" style="filter: drop-shadow(0 2px 8px rgba(48,209,88,0.4));">
                                                <animate attributeName="r" values="6;8;6" dur="1.5s" repeatCount="indefinite" begin="indefinite"/>
                                            </circle>

                                            <!-- Labels -->
                                            <text x="40" y="185" font-size="12" fill="#6d6d70" text-anchor="middle" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">Month 1</text>
                                            <text x="200" y="185" font-size="12" fill="#6d6d70" text-anchor="middle" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">Month 6</text>
                                            <text x="360" y="185" font-size="12" fill="#6d6d70" text-anchor="middle" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">Month 12</text>

                                            <text x="12" y="165" font-size="12" fill="#6d6d70" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">$100K</text>
                                            <text x="12" y="125" font-size="12" fill="#6d6d70" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">$200K</text>
                                            <text x="12" y="85" font-size="12" fill="#6d6d70" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">$300K</text>
                                            <text x="12" y="45" font-size="12" fill="#6d6d70" font-family="-apple-system, BlinkMacSystemFont, sans-serif" font-weight="500">$400K</text>
                                        </svg>

                                        <div class="chart-overlay adzeta-overlay">
                                            <div class="growth-indicator">
                                                <div class="growth-rate">+127%</div>
                                                <div class="growth-label">Accelerating Growth</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Key Insights -->
                            <div class="growth-insights">
                                <div class="insight-item traditional-insight">
                                    <div class="insight-icon traditional-insight-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#ff6b6b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="insight-text">
                                        <strong>Traditional Challenge:</strong> Growth plateaus due to diminishing returns and targeting inefficiencies
                                    </div>
                                </div>

                                <div class="insight-item adzeta-insight">
                                    <div class="insight-icon adzeta-insight-icon">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                            <circle cx="12" cy="12" r="10" fill="#30d158" opacity="0.1"/>
                                            <path d="M9 12L11 14L15 10" stroke="#30d158" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="insight-text">
                                        <strong>AI Breakthrough:</strong> Predictive LTV targeting maintains ROAS while scaling exponentially
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Sources & Credibility Section -->
                        <div class="data-credibility-section" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 300 }'>
                            <div class="credibility-header">
                                <h4>Data Sources & Methodology</h4>
                                <p>Our growth projections are based on real client data and industry benchmarks</p>
                            </div>

                            <div class="credibility-grid">
                                <div class="credibility-item">
                                    <div class="credibility-metric">
                                        <i class="bi bi-graph-up icon"></i>
                                        <span class="value">127%</span>
                                        <span class="label">vs 15% industry avg</span>
                                    </div>
                                    <div class="credibility-description">Based on 50+ e-commerce clients over 18 months</div>
                                </div>

                                <div class="credibility-item">
                                    <div class="credibility-metric">
                                        <i class="bi bi-shield-check icon"></i>
                                        <span class="value">89%</span>
                                        <span class="label">LTV accuracy</span>
                                    </div>
                                    <div class="credibility-description">Validated against Google Ads & Meta performance standards</div>
                                </div>

                                <div class="credibility-item">
                                    <div class="credibility-metric">
                                        <i class="bi bi-cpu icon"></i>
                                        <span class="value">50+</span>
                                        <span class="label">client dataset</span>
                                    </div>
                                    <div class="credibility-description">Real performance data from diverse e-commerce businesses</div>
                                </div>
                            </div>

                            <!-- Case Study References -->
                            <div class="case-study-references">
                                <h5>Supporting Case Studies</h5>
                                <div class="reference-links">
                                    <a href="oakbrew-coffee-gains-vip-customers-with-ltv-predictions.php" class="reference-link">
                                        Oakbrew Coffee <i class="bi bi-arrow-right"></i>
                                    </a>
                                    <a href="luminova-boosts-customer-lifetime-value-with-ai-personalization.php" class="reference-link">
                                        LumiNova Home <i class="bi bi-arrow-right"></i>
                                    </a>
                                    <a href="airpro-supply-contractor-retention-predictive-ltv.php" class="reference-link">
                                        AirPro Supply <i class="bi bi-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Full-width accordion layout -->
                <div class="row">
                    <div class="col-12" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 100 }'>
                        <div class="accordion accordion-style-01" id="accordion-scale" data-active-icon="bi bi-chevron-up" data-inactive-icon="bi bi-chevron-down">
                            <!-- start accordion item -->
                            <div class="accordion-item active-accordion">
                                <div class="accordion-header">
                                    <a href="#" data-bs-toggle="collapse" data-bs-target="#accordion-scale-01" aria-expanded="true" data-bs-parent="#accordion-scale">
                                        <div class="accordion-title position-relative mb-0 pe-20px text-dark-gray fw-600 alt-font">
                                            <span>Eliminate Diminishing Returns</span>
                                            <i class="bi bi-chevron-up icon-small"></i>
                                        </div>
                                    </a>
                                </div>
                                <div id="accordion-scale-01" class="accordion-collapse collapse show" data-bs-parent="#accordion-scale">
                                    <div class="accordion-body bg-white last-paragraph-no-margin">
                                        <p>Traditional ad scaling hits efficiency walls as spend increases. Our AI maintains or improves ROAS even at higher budgets by continuously finding and targeting high-LTV customers.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end accordion item -->
                            <!-- start accordion item -->
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <a href="#" data-bs-toggle="collapse" data-bs-target="#accordion-scale-02" aria-expanded="false" data-bs-parent="#accordion-scale">
                                        <div class="accordion-title position-relative mb-0 pe-20px text-dark-gray fw-600 alt-font">
                                            <span>Risk-Free Budget Expansion</span>
                                            <i class="bi bi-chevron-down icon-small"></i>
                                        </div>
                                    </a>
                                </div>
                                <div id="accordion-scale-02" class="accordion-collapse collapse" data-bs-parent="#accordion-scale">
                                    <div class="accordion-body bg-white last-paragraph-no-margin">
                                        <p>Our AI forecasting predicts performance at higher spend levels before you commit, eliminating the guesswork and risk typically associated with increasing ad budgets.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end accordion item -->
                            <!-- start accordion item -->
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <a href="#" data-bs-toggle="collapse" data-bs-target="#accordion-scale-03" aria-expanded="false" data-bs-parent="#accordion-scale">
                                        <div class="accordion-title position-relative mb-0 pe-20px text-dark-gray fw-600 alt-font">
                                            <span>Compounding Growth Effects</span>
                                            <i class="bi bi-chevron-down icon-small"></i>
                                        </div>
                                    </a>
                                </div>
                                <div id="accordion-scale-03" class="accordion-collapse collapse" data-bs-parent="#accordion-scale">
                                    <div class="accordion-body bg-white last-paragraph-no-margin">
                                        <p>As our AI acquires more high-LTV customers, your customer base quality improves, creating a virtuous cycle of better data, smarter predictions, and increasingly efficient acquisition.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end accordion item -->
                            <!-- start accordion item -->
                            <div class="accordion-item">
                                <div class="accordion-header">
                                    <a href="#" data-bs-toggle="collapse" data-bs-target="#accordion-scale-04" aria-expanded="false" data-bs-parent="#accordion-scale">
                                        <div class="accordion-title position-relative mb-0 pe-20px text-dark-gray fw-600 alt-font">
                                            <span>Accelerated Time to Scale</span>
                                            <i class="bi bi-chevron-down icon-small"></i>
                                        </div>
                                    </a>
                                </div>
                                <div id="accordion-scale-04" class="accordion-collapse collapse" data-bs-parent="#accordion-scale">
                                    <div class="accordion-body bg-white last-paragraph-no-margin">
                                        <p>Traditional scaling requires cautious, incremental budget increases over many months. Our AI-powered approach reduces time-to-scale by up to 52%, allowing you to reach your growth goals faster.</p>
                                    </div>
                                </div>
                            </div>
                            <!-- end accordion item -->
                        </div>
                    </div>
                </div>

                <!-- Bottom Metrics Section -->
                <div class="row mt-60px">
                    <div class="col-12">
                        <div class="metrics-results-section" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 200 }'>
                            <div class="metrics-header text-center mb-40px">
                                <h3 class="alt-font fw-600 text-dark-gray mb-15px">Proven Scaling Results</h3>
                                <p class="text-gray">Real performance improvements achieved through AI-powered scaling</p>
                            </div>

                            <div class="row justify-content-center">
                                <div class="col-lg-3 col-md-6 mb-30px">
                                    <div class="apple-metric-card positive">
                                        <div class="metric-icon">
                                            <i class="bi bi-cash-coin"></i>
                                        </div>
                                        <div class="metric-label">ROAS at Scale</div>
                                        <div class="metric-value">+42%</div>
                                        <div class="metric-description">Maintain or improve return on ad spend even as budgets increase</div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-30px">
                                    <div class="apple-metric-card positive">
                                        <div class="metric-icon">
                                            <i class="bi bi-people"></i>
                                        </div>
                                        <div class="metric-label">Customer LTV</div>
                                        <div class="metric-value">+38%</div>
                                        <div class="metric-description">Acquire higher-quality customers who spend more over time</div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-30px">
                                    <div class="apple-metric-card reduction">
                                        <div class="metric-icon">
                                            <i class="bi bi-x-circle"></i>
                                        </div>
                                        <div class="metric-label">Ad Waste</div>
                                        <div class="metric-value">-36%</div>
                                        <div class="metric-description">Eliminate wasted spend on low-potential customers</div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-30px">
                                    <div class="apple-metric-card reduction">
                                        <div class="metric-icon">
                                            <i class="bi bi-clock-history"></i>
                                        </div>
                                        <div class="metric-label">Time to Scale</div>
                                        <div class="metric-value">-52%</div>
                                        <div class="metric-description">Accelerate your growth timeline with AI-powered insights</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Enhanced Full-Width Testimonial Section -->
                <div class="testimonial-section">
                    <div class="row justify-content-center">
                        <div class="col-lg-12">
                            <div class="simple-testimonial" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 400 }'>
                                <div class="testimonial-content">
                                    <!-- Left column with profile -->
                                    <div class="testimonial-left">
                                        <div class="profile-pic">
                                            <img src="images/case-studies/Olivia-Carter.jpg" alt="Sarah Chen, CMO at GrowthGear E-commerce">
                                        </div>
                                        <div class="profile-info">
                                            <div class="testimonial-author">Olivia Carter</div>
                                            <div class="testimonial-position">Chief Marketing Officer<br /><span class="badge badge-light bg-light-gray">Beauty & Wellness</span></div>
                                        </div>
                                        <a href="luminous-skin-clinic-adzeta-predictive-ltv-targeting.php" class="case-study-link">
                                        Customer story <img src="images/custom-arrow.svg" alt="Arrow" style="width: 11px; height: 11px; margin-left: 6px; transition: transform 0.2s ease;">
                                        </a>
                                    </div>
                                    <!-- Right column with quote and metrics -->
                                    <div class="testimonial-right">
                                        <div class="testimonial-quote">"With Adzeta's AI platform, we've been able to scale our ad spend by 64% while significantly increasing the acquisition of our most valuable long-term clients. The predictive LTV insights gave us the confidence to expand our premium service offerings and focus on high-value clientele."</div>
                                        <div class="testimonial-metrics">
                                            <div class="metric-item">
                                                <div class="metric-label">High-Value Client Growth</div>
                                                <div class="metric-value text-base-color">3.2X</div>
                                            </div>
                                            <div class="metric-item">
                                                <div class="metric-label">Customer LTV Increase</div>
                                                <div class="metric-value">+47%</div>
                                            </div>
                                            <div class="metric-item">
                                                <div class="metric-label">Client Retention Boost</div>
                                                <div class="metric-value">+56%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section 3 -->

        <!-- start section 4: AI Decision Engine -->
        <section class="ai-decision-section bg-dark-gray overflow-hidden position-relative">
            <!-- Background elements -->
            <div class="floating-element floating-1" style="top: 10%; left: -5%; opacity: 0.3;"></div>
            <div class="floating-element floating-2" style="bottom: 10%; right: -5%; opacity: 0.3;"></div>

            <div class="container">
                <div class="row justify-content-center mb-60px">
                    <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                        <div class="scale-tag-container">
                            <div class="scale-tag ai-section-gradient">LIVE AI TECHNOLOGY</div>
                        </div>
                        <h2 class="alt-font fw-700 ls-minus-1px mb-20px text-white">See Adzeta AI in Action</h2>
                        <p class="text-light-gray scale-description">Watch our predictive AI engine process real-time signals and make intelligent optimization decisions for maximum LTV and profit. Every data point, bid adjustment, and audience shift happens automatically—just like it will for your campaigns.</p>
                    </div>
                </div>

                <!-- AI Decision Engine Simulator -->
                <div class="row">
                    <div class="col-12" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                        <div class="ai-simulator-container">
                            <div class="simulator-header text-center mb-40px">
                              
                                <div class="status-indicator">
                                    <div class="status-dot pulsing"></div>
                                    <span class="status-text">Adzeta AI Engine Live</span>
                                </div>
                            </div>

                            <!-- AI Brain Visualization -->
                            <div class="ai-brain-container">
                                <div class="brain-core">
                                    <!-- AI Processing Center -->
                                    <div class="processing-center">
                                        <img src="images/adzeta-processing.png" alt="ADZETA AI Processing" class="ai-processing-image" width="350" height="350" data-anime='{ "scale": [0.8, 1], "opacity": [0, 1], "duration": 1200, "delay": 500, "easing": "easeOutElastic(1, .8)" }'>
                                        <div class="processing-speed" data-anime='{ "opacity": [0, 1], "translateY": [20, 0], "duration": 800, "delay": 1000 }'>
                                            <span class="processing-count live-processing-count" data-base="847">847</span>
                                            <span class="processing-unit">signals/sec</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Data Input Streams -->
                                <div class="data-streams">
                                    <div class="stream-container left-streams">
                                        <div class="data-stream" data-stream="demographics">
                                            <div class="stream-label">Demographics</div>
                                            <div class="stream-value changing-value">Age 25-34, Income $75K+</div>
                                            <div class="stream-indicator active"></div>
                                        </div>
                                        <div class="data-stream" data-stream="behavior">
                                            <div class="stream-label">Behavior</div>
                                            <div class="stream-value changing-value">High Intent, Mobile User</div>
                                            <div class="stream-indicator active"></div>
                                        </div>
                                        <div class="data-stream" data-stream="timing">
                                            <div class="stream-label">Timing</div>
                                            <div class="stream-value changing-value">Evening Peak, Weekday</div>
                                            <div class="stream-indicator active"></div>
                                        </div>
                                        <div class="data-stream" data-stream="ltv">
                                            <div class="stream-label">Predicted LTV</div>
                                            <div class="stream-value changing-value">$2,847</div>
                                            <div class="stream-indicator active"></div>
                                        </div>
                                    </div>

                                    <div class="stream-container right-streams">
                                        <div class="optimization-output" data-output="bid">
                                            <div class="output-label">Optimal Bid</div>
                                            <div class="output-value changing-value">$12.50</div>
                                            <div class="output-change positive">+15%</div>
                                        </div>
                                        <div class="optimization-output" data-output="audience">
                                            <div class="output-label">Audience Shift</div>
                                            <div class="output-value changing-value">Lookalike 3%</div>
                                            <div class="output-change positive">+23%</div>
                                        </div>
                                        <div class="optimization-output" data-output="creative">
                                            <div class="output-label">Creative</div>
                                            <div class="output-value changing-value">Video A</div>
                                            <div class="output-change positive">+8%</div>
                                        </div>
                                        <div class="optimization-output" data-output="roi">
                                            <div class="output-label">Predicted ROI</div>
                                            <div class="output-value changing-value">4.7x</div>
                                            <div class="output-change positive">+12%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Real-time Performance Dashboard -->
                            <div class="performance-dashboard mt-60px">
                                <div class="dashboard-header">
                                    <div>
                                        <h4 class="h5 fw-600 text-white mb-10px">Real-Time Performance Impact</h4>
                                        <p class="fs-13 text-white-transparent mb-0">See how each AI decision translates into measurable business results</p>
                                    </div>
                                    <div class="refresh-indicator">
                                        <div class="refresh-icon rotating"><i class="fas fa-sync-alt"></i></div>
                                        <span>Live Updates</span>
                                    </div>
                                </div>

                                <div class="metrics-grid">
                                    <div class="metric-tile">
                                        <div class="metric-icon"><i class="fas fa-dollar-sign"></i></div>
                                        <div class="metric-content">
                                            <div class="metric-label">Revenue Today</div>
                                            <div class="metric-value live-counter" data-target="8247">$0</div>
                                            <div class="metric-trend positive">+$127 last hour</div>
                                        </div>
                                    </div>

                                    <div class="metric-tile">
                                        <div class="metric-icon"><i class="fas fa-bullseye"></i></div>
                                        <div class="metric-content">
                                            <div class="metric-label">Conversion Rate</div>
                                            <div class="metric-value live-counter" data-target="3.4">0%</div>
                                            <div class="metric-trend positive">+0.3% vs yesterday</div>
                                        </div>
                                    </div>

                                    <div class="metric-tile">
                                        <div class="metric-icon"><i class="fas fa-bolt"></i></div>
                                        <div class="metric-content">
                                            <div class="metric-label">AI Optimizations</div>
                                            <div class="metric-value live-counter" data-target="1247">0</div>
                                            <div class="metric-trend positive">+23 this hour</div>
                                        </div>
                                    </div>

                                    <div class="metric-tile">
                                        <div class="metric-icon"><i class="fas fa-chart-line"></i></div>
                                        <div class="metric-content">
                                            <div class="metric-label">ROI Improvement</div>
                                            <div class="metric-value live-counter" data-target="127">0%</div>
                                            <div class="metric-trend positive">vs traditional methods</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- AI Decision Log -->
                            <div class="decision-log mt-40px">
                                <div class="log-header">
                                    <h5 class="h6 fw-600 text-white">Recent AI Decisions</h5>
                                    <div class="log-status">
                                        <div class="status-dot active"></div>
                                        <span>Live Feed</span>
                                    </div>
                                </div>
                                <div class="log-container">
                                    <div class="log-entry">
                                        <div class="log-time">14:23:47</div>
                                        <div class="log-action">Increased bid for high-LTV segment by 18%</div>
                                        <div class="log-result positive">+$247 revenue</div>
                                    </div>
                                    <div class="log-entry">
                                        <div class="log-time">14:23:32</div>
                                        <div class="log-action">Shifted budget from Audience A to Audience C</div>
                                        <div class="log-result positive">+12% CTR</div>
                                    </div>
                                    <div class="log-entry">
                                        <div class="log-time">14:23:15</div>
                                        <div class="log-action">Paused underperforming creative variant</div>
                                        <div class="log-result positive">-23% CPA</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Connection to Next Section -->
                            <div class="text-center mt-50px">
                                <div class="connection-summary p-30px">
                                    <p class="text-white-transparent fs-15 mb-20px">Adzeta will optimize your campaigns 24/7—making intelligent, data-driven decisions that maximize your profit while you focus on growing your business.</p>
                                    <a href="#next-cta" class="next-step-indicator section-link">
                                        <span class="fs-13 text-gradient-purple-blue fw-600 text-uppercase ls-1px">Ready to get started?</span>
                                        <i class="fas fa-arrow-down text-gradient-purple-blue mt-10px fs-16"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end section 4 -->

        <!-- AI Simulator JavaScript -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if mobile device
            function isMobile() {
                return window.innerWidth <= 767;
            }



            // Animate counters
            function animateCounter(element, target, suffix = '') {
                let current = 0;
                const increment = target / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current) + suffix;
                }, 20);
            }

            // Start counter animations
            setTimeout(() => {
                document.querySelectorAll('.live-counter').forEach(counter => {
                    const target = parseFloat(counter.dataset.target);
                    const suffix = counter.textContent.includes('$') ? '' :
                                  counter.textContent.includes('%') ? '%' : '';
                    const prefix = counter.textContent.includes('$') ? '$' : '';

                    animateCounter(counter, target, suffix);
                    if (prefix) {
                        // Use modern MutationObserver instead of deprecated DOMSubtreeModified
                        const observer = new MutationObserver(function(mutations) {
                            mutations.forEach(function(mutation) {
                                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                                    const element = mutation.target.nodeType === Node.TEXT_NODE ?
                                                  mutation.target.parentNode : mutation.target;
                                    if (element && element.textContent && !element.textContent.startsWith('$')) {
                                        element.textContent = '$' + element.textContent;
                                    }
                                }
                            });
                        });

                        observer.observe(counter, {
                            childList: true,
                            subtree: true,
                            characterData: true
                        });
                    }
                });
            }, 1000);

            // Real AI System State
            let systemState = {
                currentHour: new Date().getHours(),
                signalsPerSec: 847,
                totalRevenue: 8247,
                conversionRate: 3.4,
                optimizations: 1247,
                roiImprovement: 127,
                currentLTV: 2847,
                currentBid: 12.50,
                currentROI: 4.7,
                sessionCount: 0,
                lastOptimization: Date.now()
            };

            // Advanced realistic LTV and performance data generator
            function generateRealisticData() {
                const now = new Date();
                const hour = now.getHours();
                const day = now.getDay();
                const isBusinessHours = hour >= 9 && hour <= 17;
                const isPeakHours = hour >= 19 && hour <= 22;
                const isWeekend = day === 0 || day === 6;

                // Realistic signal processing based on actual traffic patterns
                let baseSignals = 450;
                if (isBusinessHours && !isWeekend) baseSignals = 780;
                if (isPeakHours) baseSignals = 1150;
                if (isWeekend && (hour >= 10 && hour <= 16)) baseSignals = 920; // Weekend shopping peak

                // Add realistic fluctuation with micro-trends
                const microTrend = Math.sin(Date.now() / 30000) * 50; // 30-second cycles
                const randomFluctuation = (Math.random() - 0.5) * 120;
                systemState.signalsPerSec = Math.round(baseSignals + microTrend + randomFluctuation);
                systemState.signalsPerSec = Math.max(320, Math.min(1450, systemState.signalsPerSec));

                // Generate realistic LTV based on customer segments and behavior patterns
                const contextData = getContextualData();
                const baseLTV = calculateRealisticLTV(contextData, hour, isWeekend);

                // Add realistic LTV variation based on AI learning
                const ltvTrend = Math.sin(Date.now() / 45000) * 150; // Longer trend cycles
                const ltvNoise = (Math.random() - 0.5) * 200;
                systemState.currentLTV = Math.round(baseLTV + ltvTrend + ltvNoise);
                systemState.currentLTV = Math.max(1800, Math.min(5200, systemState.currentLTV));

                // Realistic bid optimization based on LTV and market conditions
                const marketEfficiency = calculateMarketEfficiency(hour, isWeekend);
                const ltvMultiplier = systemState.currentLTV / 2800; // Base LTV reference
                const baseBid = 11.25;

                systemState.currentBid = baseBid * ltvMultiplier * marketEfficiency;
                systemState.currentBid += (Math.random() - 0.5) * 1.5; // Market noise
                systemState.currentBid = Math.round(systemState.currentBid * 100) / 100;
                systemState.currentBid = Math.max(7.50, Math.min(19.75, systemState.currentBid));

                // ROI calculation based on bid efficiency and market conditions
                const bidEfficiency = calculateBidEfficiency(systemState.currentBid, systemState.currentLTV);
                const baseROI = 4.2;
                systemState.currentROI = baseROI * bidEfficiency * marketEfficiency;
                systemState.currentROI += (Math.random() - 0.5) * 0.6; // Performance noise
                systemState.currentROI = Math.round(systemState.currentROI * 10) / 10;
                systemState.currentROI = Math.max(2.8, Math.min(6.8, systemState.currentROI));

                return systemState;
            }

            // Calculate realistic LTV based on customer context
            function calculateRealisticLTV(contextData, hour, isWeekend) {
                // Extract income from demographics string
                const incomeMatch = contextData.demographics.match(/Income \$(\d+)K/);
                const income = incomeMatch ? parseInt(incomeMatch[1]) * 1000 : 75000;

                // Base LTV calculation using realistic e-commerce ratios
                let baseLTV = income * 0.035; // 3.5% of annual income is typical for e-commerce LTV

                // Adjust based on behavior intent
                if (contextData.behavior.includes('Very High Intent')) baseLTV *= 1.4;
                else if (contextData.behavior.includes('High Intent')) baseLTV *= 1.2;
                else if (contextData.behavior.includes('Medium Intent')) baseLTV *= 1.0;
                else baseLTV *= 0.7;

                // Time-based adjustments (peak hours = higher value customers)
                if (hour >= 19 && hour <= 21) baseLTV *= 1.15; // Prime shopping time
                if (isWeekend && hour >= 10 && hour <= 16) baseLTV *= 1.1; // Weekend shopping
                if (hour >= 2 && hour <= 6) baseLTV *= 0.8; // Late night browsers

                // Device-based adjustments
                if (contextData.behavior.includes('Desktop')) baseLTV *= 1.1; // Desktop users spend more
                else if (contextData.behavior.includes('Tablet')) baseLTV *= 1.05;

                return Math.round(baseLTV);
            }

            // Calculate market efficiency factor
            function calculateMarketEfficiency(hour, isWeekend) {
                let efficiency = 1.0;

                // Competition is higher during peak hours
                if (hour >= 19 && hour <= 21) efficiency = 0.92; // High competition
                if (hour >= 9 && hour <= 17 && !isWeekend) efficiency = 0.95; // Business hours competition
                if (hour >= 2 && hour <= 7) efficiency = 1.08; // Low competition
                if (isWeekend && (hour >= 10 && hour <= 16)) efficiency = 0.88; // Weekend competition

                return efficiency;
            }

            // Calculate bid efficiency based on LTV and current bid
            function calculateBidEfficiency(bid, ltv) {
                const idealBidRatio = ltv / 250; // Ideal bid should be ~0.4% of LTV
                const currentRatio = bid / (ltv / 250);

                // Efficiency decreases as bid deviates from ideal
                if (currentRatio >= 0.8 && currentRatio <= 1.2) return 1.1; // Sweet spot
                if (currentRatio >= 0.6 && currentRatio <= 1.4) return 1.0; // Good range
                if (currentRatio >= 0.4 && currentRatio <= 1.6) return 0.9; // Acceptable
                return 0.8; // Suboptimal
            }

            // Advanced realistic customer data generator
            function getContextualData() {
                const now = new Date();
                const hour = now.getHours();
                const day = now.getDay();
                const minute = now.getMinutes();
                const isWeekend = day === 0 || day === 6;

                // Create realistic customer profiles based on actual e-commerce patterns
                const customerProfiles = [
                    // High-value professional segments
                    { ageRange: [28, 45], incomeRange: [75000, 150000], behaviors: ['Research Heavy', 'Price Conscious', 'Brand Loyal'], devices: ['Desktop', 'Tablet'] },
                    { ageRange: [32, 52], incomeRange: [85000, 200000], behaviors: ['Quick Decision', 'Premium Focused', 'Mobile First'], devices: ['Mobile', 'Desktop'] },
                    { ageRange: [25, 38], incomeRange: [65000, 120000], behaviors: ['Comparison Shopping', 'Review Reader', 'Social Influenced'], devices: ['Mobile', 'Tablet'] },

                    // Mid-value segments
                    { ageRange: [22, 35], incomeRange: [45000, 85000], behaviors: ['Deal Seeker', 'Impulse Buyer', 'Social Media'], devices: ['Mobile'] },
                    { ageRange: [35, 55], incomeRange: [55000, 95000], behaviors: ['Practical Buyer', 'Family Focused', 'Email Driven'], devices: ['Desktop', 'Mobile'] },

                    // Emerging segments
                    { ageRange: [18, 28], incomeRange: [35000, 65000], behaviors: ['Trend Follower', 'Video Influenced', 'App Native'], devices: ['Mobile'] },
                    { ageRange: [45, 65], incomeRange: [70000, 140000], behaviors: ['Quality Focused', 'Customer Service', 'Traditional'], devices: ['Desktop', 'Tablet'] }
                ];

                // Time-based behavior patterns
                const timePatterns = {
                    earlyMorning: { // 5-8 AM
                        behaviors: ['Quick Check', 'Commute Browse', 'News Reading'],
                        intent: ['Low', 'Medium'],
                        devices: ['Mobile']
                    },
                    workHours: { // 9-17
                        behaviors: isWeekend ? ['Leisure Shopping', 'Research Mode', 'Comparison'] : ['Work Break', 'Quick Browse', 'Research'],
                        intent: isWeekend ? ['High', 'Medium'] : ['Low', 'Medium'],
                        devices: isWeekend ? ['Desktop', 'Tablet', 'Mobile'] : ['Desktop', 'Mobile']
                    },
                    eveningPeak: { // 18-22
                        behaviors: ['Purchase Intent', 'Family Shopping', 'Entertainment'],
                        intent: ['High', 'Very High'],
                        devices: ['Mobile', 'Desktop', 'Tablet']
                    },
                    lateNight: { // 23-4
                        behaviors: ['Impulse Browse', 'Insomnia Shopping', 'Entertainment'],
                        intent: ['Medium', 'Low'],
                        devices: ['Mobile']
                    }
                };

                // Determine time period
                let timePeriod;
                if (hour >= 5 && hour <= 8) timePeriod = 'earlyMorning';
                else if (hour >= 9 && hour <= 17) timePeriod = 'workHours';
                else if (hour >= 18 && hour <= 22) timePeriod = 'eveningPeak';
                else timePeriod = 'lateNight';

                // Select realistic customer profile based on time and randomization
                const timeData = timePatterns[timePeriod];
                const profileIndex = Math.floor(Math.random() * customerProfiles.length);
                const profile = customerProfiles[profileIndex];

                // Generate realistic age within range
                const age = Math.floor(Math.random() * (profile.ageRange[1] - profile.ageRange[0] + 1)) + profile.ageRange[0];

                // Generate realistic income (rounded to nearest 5K)
                const baseIncome = Math.floor(Math.random() * (profile.incomeRange[1] - profile.incomeRange[0] + 1)) + profile.incomeRange[0];
                const income = Math.round(baseIncome / 5000) * 5000;

                // Select behavior and device based on profile and time
                const behavior = profile.behaviors[Math.floor(Math.random() * profile.behaviors.length)];
                const intent = timeData.intent[Math.floor(Math.random() * timeData.intent.length)];
                const device = profile.devices[Math.floor(Math.random() * profile.devices.length)];
                const timeBehavior = timeData.behaviors[Math.floor(Math.random() * timeData.behaviors.length)];

                // Create realistic demographic string
                const demographics = `Age ${age}, Income $${(income/1000)}K+`;

                // Create realistic behavior string
                const behaviorString = `${intent} Intent, ${device}`;

                // Create realistic timing string
                const timingOptions = {
                    earlyMorning: ['Morning Commute', 'Early Bird', 'Pre-Work'],
                    workHours: isWeekend ? ['Weekend Shopping', 'Leisure Time', 'Family Time'] : ['Work Break', 'Lunch Hour', 'Office Browse'],
                    eveningPeak: ['Evening Prime', 'After Work', 'Family Time'],
                    lateNight: ['Late Night', 'Night Owl', 'Insomnia Browse']
                };

                const timing = timingOptions[timePeriod][Math.floor(Math.random() * timingOptions[timePeriod].length)];

                return { demographics, behavior: behaviorString, timing };
            }

            // Generate realistic AI optimization decisions
            function getOptimizationDecision() {
                const state = systemState;
                const contextData = getContextualData();
                const hour = new Date().getHours();
                const isWeekend = new Date().getDay() === 0 || new Date().getDay() === 6;

                // Realistic audience targeting options
                const audienceOptions = {
                    highPerformance: [
                        'Lookalike 1% (Top 10% LTV)',
                        'Custom Intent (Purchase Ready)',
                        'Retargeting (High Engagers)',
                        'Lookalike 2% (Premium Segment)'
                    ],
                    mediumPerformance: [
                        'Lookalike 3% (Broad Quality)',
                        'Interest + Behavior (Qualified)',
                        'Similar Audiences (Expanding)',
                        'Custom Affinity (Category)'
                    ],
                    testingPhase: [
                        'Broad Interest (Discovery)',
                        'Demographic + Interest',
                        'Competitor Audiences',
                        'Cold Prospecting (Wide)'
                    ]
                };

                // Realistic creative options
                const creativeOptions = {
                    highIntent: [
                        'Video Ad (Product Demo)',
                        'Carousel (Multi-Product)',
                        'Collection (Category)',
                        'Video (Testimonial)'
                    ],
                    mediumIntent: [
                        'Single Image (Lifestyle)',
                        'Carousel (Features)',
                        'Video (Brand Story)',
                        'Collection (Seasonal)'
                    ],
                    discovery: [
                        'Video (Educational)',
                        'Single Image (Awareness)',
                        'Slideshow (Quick)',
                        'Story Ad (Native)'
                    ]
                };

                // Decision logic based on performance and context
                let audienceCategory, creativeCategory;

                if (state.currentROI > 5.2) {
                    audienceCategory = 'highPerformance';
                    creativeCategory = 'highIntent';
                } else if (state.currentROI > 4.0) {
                    audienceCategory = 'mediumPerformance';
                    creativeCategory = contextData.behavior.includes('High Intent') ? 'highIntent' : 'mediumIntent';
                } else {
                    audienceCategory = 'testingPhase';
                    creativeCategory = 'discovery';
                }

                // Time-based adjustments
                if (hour >= 19 && hour <= 21) { // Prime time
                    if (audienceCategory === 'testingPhase') audienceCategory = 'mediumPerformance';
                    if (creativeCategory === 'discovery') creativeCategory = 'mediumIntent';
                }

                // Weekend adjustments
                if (isWeekend && (hour >= 10 && hour <= 16)) {
                    creativeCategory = 'highIntent'; // Weekend shoppers are more ready to buy
                }

                // Select random option from appropriate category
                const audience = audienceOptions[audienceCategory][Math.floor(Math.random() * audienceOptions[audienceCategory].length)];
                const creative = creativeOptions[creativeCategory][Math.floor(Math.random() * creativeOptions[creativeCategory].length)];

                return { audience, creative };
            }

            function updateDataValues() {
                const newState = generateRealisticData();
                const contextData = getContextualData();
                const optimization = getOptimizationDecision();

                // Format LTV to look more realistic (rounded to nearest $50)
                const formattedLTV = Math.round(newState.currentLTV / 50) * 50;

                // Format bid to realistic precision
                const formattedBid = newState.currentBid.toFixed(2);

                // Update input streams with contextual data
                updateElement('[data-stream="demographics"] .changing-value', contextData.demographics);
                updateElement('[data-stream="behavior"] .changing-value', contextData.behavior);
                updateElement('[data-stream="timing"] .changing-value', contextData.timing);
                updateElement('[data-stream="ltv"] .changing-value', `$${formattedLTV.toLocaleString()}`);

                // Update output decisions
                updateElement('[data-output="bid"] .changing-value', `$${formattedBid}`);
                updateElement('[data-output="audience"] .changing-value', optimization.audience);
                updateElement('[data-output="creative"] .changing-value', optimization.creative);
                updateElement('[data-output="roi"] .changing-value', `${newState.currentROI}x`);

                // Update performance metrics with more realistic increments
                const revenueIncrement = Math.floor(Math.random() * 50) + 25; // $25-75 increments
                systemState.totalRevenue += revenueIncrement;

                const conversionFluctuation = (Math.random() - 0.5) * 0.2; // ±0.1% fluctuation
                systemState.conversionRate = Math.max(2.8, Math.min(4.2, systemState.conversionRate + conversionFluctuation));
                systemState.conversionRate = Math.round(systemState.conversionRate * 10) / 10;

                updateElement('.live-counter[data-target="8247"]', `$${systemState.totalRevenue.toLocaleString()}`);
                updateElement('.live-counter[data-target="3.4"]', `${systemState.conversionRate}%`);
                updateElement('.live-counter[data-target="1247"]', systemState.optimizations.toLocaleString());
                updateElement('.live-counter[data-target="127"]', `${systemState.roiImprovement}%`);
            }

            function updateElement(selector, value) {
                const element = document.querySelector(selector);
                if (element) {
                    element.style.color = '#F25EB0';
                    element.style.transform = 'scale(1.05)';

                    setTimeout(() => {
                        element.textContent = value;
                        element.style.color = 'white';
                        element.style.transform = 'scale(1)';
                    }, 150);
                }
            }

            // Generate realistic AI decisions based on current state
            function generateAIDecision() {
                const state = systemState;
                const hour = new Date().getHours();
                const decisions = [];

                // Bid adjustments based on LTV
                if (state.currentLTV > 3200) {
                    const increase = Math.floor((state.currentLTV - 3000) / 100 * 2);
                    decisions.push({
                        action: `Increased bid for high-LTV segment by ${increase}%`,
                        result: `+$${Math.floor(increase * 12)} revenue`,
                        type: 'positive'
                    });
                } else if (state.currentLTV < 2500) {
                    const decrease = Math.floor((2800 - state.currentLTV) / 100 * 1.5);
                    decisions.push({
                        action: `Reduced bid for low-LTV segment by ${decrease}%`,
                        result: `-${decrease}% CPA`,
                        type: 'positive'
                    });
                }

                // Time-based optimizations
                if (hour >= 19 && hour <= 22) {
                    decisions.push({
                        action: 'Shifted budget to mobile for evening peak',
                        result: `+${Math.floor(Math.random() * 15 + 10)}% CTR`,
                        type: 'positive'
                    });
                } else if (hour >= 9 && hour <= 17) {
                    decisions.push({
                        action: 'Optimized desktop targeting for work hours',
                        result: `+${Math.floor(Math.random() * 20 + 8)}% engagement`,
                        type: 'positive'
                    });
                }

                // ROI-based decisions
                if (state.currentROI > 5.0) {
                    decisions.push({
                        action: 'Expanded successful lookalike audience',
                        result: `+${Math.floor(Math.random() * 25 + 15)}% reach`,
                        type: 'positive'
                    });
                } else if (state.currentROI < 4.0) {
                    decisions.push({
                        action: 'Paused underperforming creative variant',
                        result: `+${Math.floor(Math.random() * 12 + 8)}% CVR`,
                        type: 'positive'
                    });
                }

                // Signal processing optimizations
                if (state.signalsPerSec > 1000) {
                    decisions.push({
                        action: 'Activated high-frequency signal processing',
                        result: `+${Math.floor(Math.random() * 18 + 12)}% accuracy`,
                        type: 'positive'
                    });
                }

                return decisions[Math.floor(Math.random() * decisions.length)] || {
                    action: 'Optimized bid strategy based on real-time data',
                    result: `+${Math.floor(Math.random() * 15 + 5)}% ROAS`,
                    type: 'positive'
                };
            }

            function addLogEntry() {
                const logContainer = document.querySelector('.log-container');
                if (!logContainer) return;

                const now = new Date();
                const timeString = now.toTimeString().split(' ')[0];
                const decision = generateAIDecision();

                const entry = document.createElement('div');
                entry.className = 'log-entry';
                entry.innerHTML = `
                    <div class="log-time">${timeString}</div>
                    <div class="log-action">${decision.action}</div>
                    <div class="log-result ${decision.type}">${decision.result}</div>
                `;

                logContainer.insertBefore(entry, logContainer.firstChild);

                // Remove old entries if too many
                if (logContainer.children.length > 5) {
                    logContainer.removeChild(logContainer.lastChild);
                }

                // Update optimization counter
                systemState.optimizations += Math.floor(Math.random() * 3 + 1);
                systemState.lastOptimization = Date.now();
            }



            // Start real-time updates with realistic intervals
            const updateInterval = isMobile() ? 8000 : 6000;  // Every 6-8 seconds
            const logInterval = isMobile() ? 18000 : 15000;   // Every 15-18 seconds

            setInterval(updateDataValues, updateInterval);
            setInterval(addLogEntry, logInterval);

            // Add first log entry and update immediately
            setTimeout(addLogEntry, 3000);
            setTimeout(updateDataValues, 1000);

            // Live processing counter connected to system state
            function updateProcessingCount() {
                const countElement = document.querySelector('.live-processing-count');
                if (!countElement) return;

                // Use actual system state for signals per second
                const currentSignals = systemState.signalsPerSec;

                // Add small real-time fluctuation (±2-5%)
                const fluctuation = Math.floor(Math.random() * (currentSignals * 0.05 - currentSignals * 0.02) + currentSignals * 0.02);
                const direction = Math.random() > 0.5 ? 1 : -1;
                const finalValue = Math.max(300, currentSignals + (fluctuation * direction));

                // Add flash effect during update
                countElement.style.transform = 'scale(1.1)';
                countElement.style.transition = 'transform 0.15s ease';

                setTimeout(() => {
                    countElement.textContent = finalValue.toLocaleString();
                    countElement.style.transform = 'scale(1)';
                }, 75);
            }

            // Update processing counter more frequently for live feel
            setInterval(updateProcessingCount, 800 + Math.random() * 400); // 0.8-1.2 seconds

            // Initial update after a short delay
            setTimeout(updateProcessingCount, 1500);

            // Optimize animations for mobile
            if (isMobile()) {
                // Simplify processing center animation
                const processingCenter = document.querySelector('.processing-center');
                if (processingCenter) {
                    processingCenter.style.animationDuration = '6s';
                }
            }
        });
        </script>

        <!-- Dual Growth Comparison JavaScript -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Namespace for Dual Growth Comparison to avoid conflicts
            const DualGrowthComparison = {
                // Check if GSAP is available
                init: function() {
                    if (typeof gsap === 'undefined') {
                        console.warn('GSAP not loaded - Dual growth comparison animation will not run');
                        return;
                    }
                    this.setupComparison();
                },

                isAnimating: false,
                animationTimeline: null,

                // Setup the dual comparison
                setupComparison: function() {
                    const container = document.getElementById('growthComparisonContainer');
                    if (!container) return;

                    this.initializeComparison();
                },

                // Initialize the comparison
                initializeComparison: function() {
                    const self = this;

                    // Set up animate button
                    const animateButton = document.getElementById('animateGrowth');
                    if (animateButton) {
                        animateButton.addEventListener('click', () => self.playDualAnimation());
                    }

                    // Initialize paths
                    this.resetPaths();
                },

                // Reset all paths to initial state
                resetPaths: function() {
                    gsap.set('#traditionalPath', { strokeDashoffset: 400 });
                    gsap.set('#adzetaPath', { strokeDashoffset: 450 });
                    gsap.set('#plateauPoint, #breakthroughPoint', { opacity: 0 });
                },

                // Play dual animation
                playDualAnimation: function() {
                    if (this.isAnimating) return;

                    this.isAnimating = true;
                    const animateButton = document.getElementById('animateGrowth');
                    const originalText = animateButton.innerHTML;
                    animateButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i><span>Animating...</span>';

                    const self = this;

                    // Create animation timeline
                    this.animationTimeline = gsap.timeline({
                        onComplete: () => {
                            self.isAnimating = false;
                            animateButton.innerHTML = originalText;

                            // Auto-restart after 3 seconds
                            setTimeout(() => {
                                if (!self.isAnimating) {
                                    self.resetPaths();
                                    setTimeout(() => self.playDualAnimation(), 500);
                                }
                            }, 3000);
                        }
                    });

                    // Reset paths first
                    this.resetPaths();

                    // Animate both paths simultaneously
                    this.animationTimeline
                        // Start both paths at the same time
                        .to('#traditionalPath', {
                            strokeDashoffset: 0,
                            duration: 3,
                            ease: "power2.out"
                        }, "start")
                        .to('#adzetaPath', {
                            strokeDashoffset: 0,
                            duration: 3,
                            ease: "power2.out"
                        }, "start")

                        // Show plateau point for traditional
                        .to('#plateauPoint', {
                            opacity: 1,
                            duration: 0.5,
                            ease: "power2.out"
                        }, "start+=2")

                        // Show breakthrough point for Adzeta
                        .to('#breakthroughPoint', {
                            opacity: 1,
                            duration: 0.5,
                            ease: "power2.out"
                        }, "start+=1.5")

                        // Animate chart sections
                        .to('.traditional-section', {
                            scale: 1.02,
                            duration: 0.3,
                            yoyo: true,
                            repeat: 1,
                            ease: "power2.inOut"
                        }, "start+=2")
                        .to('.adzeta-section', {
                            scale: 1.02,
                            duration: 0.3,
                            yoyo: true,
                            repeat: 1,
                            ease: "power2.inOut"
                        }, "start+=1.5")

                        // Highlight insights
                        .to('.traditional-insight', {
                            scale: 1.05,
                            duration: 0.4,
                            yoyo: true,
                            repeat: 1,
                            ease: "power2.inOut"
                        }, "start+=2.5")
                        .to('.adzeta-insight', {
                            scale: 1.05,
                            duration: 0.4,
                            yoyo: true,
                            repeat: 1,
                            ease: "power2.inOut"
                        }, "start+=3");
                },

                // Auto-start animation on scroll
                startAutoAnimation: function() {
                    // Start animation automatically after a short delay
                    setTimeout(() => {
                        if (!this.isAnimating) {
                            this.playDualAnimation();
                        }
                    }, 1000);
                }
            };

            // Initialize on scroll trigger
            if (typeof ScrollTrigger !== 'undefined') {
                gsap.registerPlugin(ScrollTrigger);

                ScrollTrigger.create({
                    trigger: "#growthComparisonContainer",
                    start: "top 80%",
                    onEnter: () => {
                        DualGrowthComparison.init();
                        DualGrowthComparison.startAutoAnimation();
                    },
                    once: true
                });
            } else {
                // Fallback initialization
                setTimeout(() => {
                    DualGrowthComparison.init();
                    DualGrowthComparison.startAutoAnimation();
                }, 1000);
            }
        });
        </script>

        <!-- start section 5: Getting Started -->
        <section id="next-cta" class="getting-started-section overflow-hidden ">
            <div class="floating-element floating-2" style="bottom: -100px; right: -100px;"></div>
            <div class="container">
                <div class="row justify-content-center mb-50px">
                    <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                        <div class="scale-tag-container">
                            <div class="scale-tag">GETTING STARTED</div>
                        </div>
                        <h2 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Activate Your E-commerce Profit Engine</span></h2>
                        <p class="scale-description">Getting started with Adzeta is simple. Our team will guide you through each step of the process to ensure a smooth implementation and quick time-to-value.</p>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-lg-10">
                        <div class="getting-started-journey" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                            <!-- Step 1 -->
                            <div class="journey-step" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 100 }'>
                                <div class="step-icon-container">
                                    <div class="step-number">1</div>
                                    <i class="bi bi-chat-dots"></i>
                                </div>
                                <div class="step-content">
                                    <h4 class="alt-font fw-600 text-dark-gray">Consultation</h4>
                                    <p>We analyze your business goals and current advertising approach to identify high-impact opportunities for improvement.</p>
                                </div>
                            </div>
                            <!-- Step 2 -->
                            <div class="journey-step" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 200 }'>
                                <div class="step-icon-container">
                                    <div class="step-number">2</div>
                                    <i class="bi bi-database-check"></i>
                                </div>
                                <div class="step-content">
                                    <h4 class="alt-font fw-600 text-dark-gray">Data Connect</h4>
                                    <p>Our team sets up secure connections to your data sources, ensuring we have the information needed for accurate predictive models.</p>
                                </div>
                            </div>
                            <!-- Step 3 -->
                            <div class="journey-step" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 300 }'>
                                <div class="step-icon-container">
                                    <div class="step-number">3</div>
                                    <i class="bi bi-rocket-takeoff"></i>
                                </div>
                                <div class="step-content">
                                    <h4 class="alt-font fw-600 text-dark-gray">Launch</h4>
                                    <p>We implement the ValueBid™ Framework, connecting our predictive models to your advertising platforms for immediate optimization.</p>
                                </div>
                            </div>
                            <!-- Step 4 -->
                            <div class="journey-step" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 400 }'>
                                <div class="step-icon-container">
                                    <div class="step-number">4</div>
                                    <i class="bi bi-graph-up-arrow"></i>
                                </div>
                                <div class="step-content">
                                    <h4 class="alt-font fw-600 text-dark-gray">Grow</h4>
                                    <p>With the system in place, we continuously monitor and optimize performance, helping you scale profitably with sustainable growth.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row justify-content-center mt-60px">
                    <div class="col-lg-6 col-md-8 text-center" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay": 500 }'>
                        <a href="/free-audit" class="btn btn-large btn-gradient-pink-orange btn-rounded fs-16 md-fs-18 sm-fs-18">Request Your Free Profit & LTV Analysis</a>
                        <p class="mt-20px fs-15 opacity-7">No commitment required. See how Adzeta can transform your e-commerce growth.</p>
                    </div>
                </div>
            </div>
        </section>
		
		 <!-- Debug script for accordion issues (remove in production) -->
	
        <!-- end section 5 -->
        <!-- start enhanced footer -->
      <?php include 'footer.php'; ?>