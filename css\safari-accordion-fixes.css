/* Safari iOS Accordion Fixes */

/* Prevent scroll-to-top behavior on accordion clicks */
@supports (-webkit-touch-callout: none) {
    /* iOS Safari specific styles */
    
    /* Fix for accordion content opening upward in ANALYZE section */
    .predictive-ai-showcase .accordion-header {
        position: relative;
        z-index: 1;
    }
    
    .predictive-ai-showcase .accordion-panel {
        position: relative;
        z-index: 0;
        transform: translateZ(0); /* Force hardware acceleration */
    }
    
    /* Prevent Bootstrap accordion scroll-to-top behavior */
    .accordion-collapse {
        scroll-margin-top: 0 !important;
        scroll-behavior: auto !important;
    }
    
    /* Fix for accordion headers in OPTIMIZE and SCALE sections */
    .accordion-header a {
        display: block;
        text-decoration: none;
        position: relative;
    }
    
    /* Prevent unwanted scrolling on accordion toggle */
    .accordion-item {
        scroll-margin: 0 !important;
        scroll-behavior: auto !important;
    }
    
    /* Ensure smooth accordion expansion without scroll jumps */
    .accordion-collapse.collapsing {
        scroll-behavior: auto !important;
        overflow: hidden;
    }
    
    /* Fix for iOS Safari viewport issues */
    .accordion-body {
        -webkit-overflow-scrolling: touch;
        transform: translateZ(0); /* Force hardware acceleration */
    }
    
    /* Prevent zoom on accordion interaction */
    .accordion-header a,
    .accordion-title {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
    
    /* Fix for custom accordion panels in ANALYZE section */
    .process-flow .accordion-panel {
        position: relative;
        overflow: hidden;
        transform: translateZ(0);
    }
    
    /* Ensure proper stacking context */
    .process-flow {
        position: relative;
        z-index: 1;
    }
    
    /* Fix for accordion animation issues */
    .accordion-collapse.show {
        animation: none !important;
        transition: none !important;
    }
    
    /* Prevent content jumping during accordion operations */
    .accordion {
        contain: layout style;
    }
    
    /* Fix for Bootstrap accordion scroll behavior */
    .accordion .accordion-item .accordion-header a[data-bs-toggle="collapse"] {
        scroll-behavior: auto !important;
    }
    
    /* Ensure accordion content doesn't cause layout shifts */
    .accordion-body {
        contain: layout;
        will-change: auto;
    }
}

/* Additional fixes for all mobile Safari versions */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
    /* Target all WebKit browsers on mobile */
    
    /* Prevent scroll restoration issues */
    .accordion-collapse {
        overflow-anchor: none;
    }
    
    /* Fix for accordion header positioning */
    .accordion-header {
        position: relative;
        isolation: isolate;
    }
    
    /* Ensure proper rendering of accordion content */
    .accordion-body {
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
    }
}

/* Specific fixes for platform.php accordion sections */
.platform .accordion-style-01 .accordion-item {
    scroll-margin: 0 !important;
}

.platform .accordion-style-01 .accordion-collapse {
    scroll-behavior: auto !important;
}

/* Fix for ANALYZE section custom accordions */
.predictive-ai-showcase .accordion-header[data-target] {
    cursor: pointer;
    touch-action: manipulation;
}

.predictive-ai-showcase .accordion-panel {
    overflow: hidden;
    backface-visibility: hidden;
}

/* Prevent scroll jumps in OPTIMIZE section */
#optimize-section .accordion-collapse {
    scroll-margin-top: 0 !important;
}

/* Prevent scroll jumps in SCALE section */
#scale-section .accordion-collapse {
    scroll-margin-top: 0 !important;
}

/* Fix for accordion icon rotation without scroll issues */
.accordion-title i.icon-small {
    transition: transform 0.2s ease;
    will-change: transform;
}

/* Ensure accordion animations don't trigger scroll */
.accordion-collapse.collapsing {
    overflow: hidden !important;
    scroll-behavior: auto !important;
}

/* Fix for iOS Safari address bar changes affecting accordion position */
@supports (height: 100dvh) {
    .accordion {
        min-height: 0;
    }
}

/* Additional stability fixes */
.accordion-item.active-accordion {
    scroll-margin: 0 !important;
}

/* Prevent accordion content from causing scroll restoration */
.accordion-body * {
    scroll-margin: 0 !important;
}

/* Specific fix for Bootstrap data-bs-toggle behavior on iOS Safari */
@supports (-webkit-touch-callout: none) {
    /* Target OPTIMIZE and SCALE section accordions specifically */
    #accordion-style-04 [data-bs-toggle="collapse"],
    #accordion-scale [data-bs-toggle="collapse"] {
        scroll-behavior: auto !important;
        scroll-margin: 0 !important;
        scroll-snap-align: none !important;
    }

    /* Prevent scroll jumps when accordion state changes */
    #accordion-style-04 .accordion-collapse,
    #accordion-scale .accordion-collapse {
        scroll-margin-top: 0 !important;
        scroll-margin-bottom: 0 !important;
        scroll-behavior: auto !important;
        overflow-anchor: none !important;
    }

    /* Fix for iOS Safari address bar behavior affecting accordion */
    #accordion-style-04 .accordion-header a[data-bs-target],
    #accordion-scale .accordion-header a[data-bs-target] {
        position: relative;
        display: block;
        scroll-behavior: auto !important;
        scroll-margin: 0 !important;
    }

    /* Prevent unwanted scroll restoration for OPTIMIZE and SCALE sections */
    #accordion-style-04,
    #accordion-scale {
        scroll-behavior: auto !important;
        overflow-anchor: none !important;
        scroll-margin: 0 !important;
    }

    /* Additional fix for accordion items */
    #accordion-style-04 .accordion-item,
    #accordion-scale .accordion-item {
        scroll-margin: 0 !important;
        scroll-behavior: auto !important;
    }

    /* Fix for custom accordion in ANALYZE section */
    .predictive-ai-showcase .accordion-header[data-target] {
        scroll-behavior: auto !important;
        scroll-margin: 0 !important;
    }

    /* Specific fixes for ANALYZE section mobile accordion upward opening issue */
    .predictive-ai-showcase .accordion-panel {
        position: relative !important;
        top: 0 !important;
        left: 0 !important;
        transform: translateZ(0) translateY(0) !important;
        -webkit-transform: translateZ(0) translateY(0) !important;
        will-change: height, opacity !important;
        overflow: hidden !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        backface-visibility: hidden !important;
        -webkit-backface-visibility: hidden !important;
    }

    /* Force proper stacking order for ANALYZE accordion */
    .predictive-ai-showcase .process-flow {
        position: relative !important;
        z-index: 1 !important;
        transform: translateZ(0) !important;
        -webkit-transform: translateZ(0) !important;
        isolation: isolate !important;
    }

    /* Ensure accordion headers stay in place */
    .predictive-ai-showcase .accordion-header {
        position: relative !important;
        z-index: 2 !important;
        transform: translateZ(0) !important;
        -webkit-transform: translateZ(0) !important;
        backface-visibility: hidden !important;
        -webkit-backface-visibility: hidden !important;
    }

    /* Prevent content jumping during accordion animation */
    .predictive-ai-showcase .accordion-panel.active {
        position: relative !important;
        top: 0 !important;
        bottom: auto !important;
        transform: translateZ(0) translateY(0) !important;
        -webkit-transform: translateZ(0) translateY(0) !important;
    }

    /* Fix panel container positioning */
    .predictive-ai-showcase .accordion-panel .panel-container {
        position: relative !important;
        transform: translateZ(0) !important;
        -webkit-transform: translateZ(0) !important;
        backface-visibility: hidden !important;
        -webkit-backface-visibility: hidden !important;
        contain: layout style paint !important;
    }
}

/* Mobile Safari specific fixes for ANALYZE section accordion */
@media screen and (max-width: 768px) {
    @supports (-webkit-touch-callout: none) {

        /* Mobile Safari accordion fixes for ANALYZE section */
        .predictive-ai-showcase .accordion-panel {
            /* Prevent viewport jumping */
            position: relative !important;
            top: auto !important;
            bottom: auto !important;
            margin-top: 0 !important;
            margin-bottom: 0 !important;

            /* Force proper rendering */
            -webkit-transform: translate3d(0, 0, 0) !important;
            transform: translate3d(0, 0, 0) !important;
            -webkit-overflow-scrolling: touch !important;
            isolation: isolate !important;
        }

        /* Prevent iOS rubber band effect interference */
        .predictive-ai-showcase .process-flow {
            position: relative !important;
            overflow: visible !important;
            -webkit-overflow-scrolling: auto !important;
        }

        /* Fix mobile Safari touch events for ANALYZE accordion */
        .predictive-ai-showcase .accordion-header {
            cursor: pointer !important;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
            -webkit-touch-callout: none !important;
            -webkit-user-select: none !important;
            user-select: none !important;
            touch-action: manipulation !important;
        }

        /* Ensure proper mobile rendering for SVG containers */
        .predictive-ai-showcase .accordion-panel .svg-container {
            /* Prevent SVG rendering issues on mobile Safari */
            -webkit-transform: translateZ(0) !important;
            transform: translateZ(0) !important;
            will-change: auto !important;
            backface-visibility: hidden !important;
            -webkit-backface-visibility: hidden !important;
        }

        /* Force proper layout calculation on mobile */
        .predictive-ai-showcase .accordion-panel .panel-container {
            min-height: 0 !important;
            height: auto !important;
            box-sizing: border-box !important;
            -webkit-box-sizing: border-box !important;
        }

        /* Prevent accordion content from causing scroll issues */
        .predictive-ai-showcase .accordion-panel .panel-content {
            position: relative !important;
            z-index: 1 !important;
            transform: translateZ(0) !important;
            -webkit-transform: translateZ(0) !important;
        }
    }
}

/* PRODUCTION: Ensure no debug borders are visible */
.predictive-ai-showcase .accordion-panel,
.predictive-ai-showcase .accordion-header,
.predictive-ai-showcase .panel-container,
.predictive-ai-showcase .panel-content,
.predictive-ai-showcase .svg-container {
    border: none !important;
    outline: none !important;
}

/* Remove any potential debug styling */
.debug-safari-accordion,
.safari-debug,
.accordion-debug {
    border: none !important;
    outline: none !important;
    background: transparent !important;
}

/* Ensure clean appearance on iOS Safari */
@supports (-webkit-touch-callout: none) {
    .predictive-ai-showcase * {
        border: none !important;
        outline: none !important;
    }

    /* Exception: Keep intended borders for design elements */
    .predictive-ai-showcase .process-step,
    .predictive-ai-showcase .accordion-header {
        border: 1px solid rgba(255, 255, 255, 0.1) !important; /* Restore intended design border */
    }
}
