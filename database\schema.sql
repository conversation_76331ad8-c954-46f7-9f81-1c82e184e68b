-- AdZeta Website Database Schema
-- Run this SQL to create the necessary tables for the dynamic website system

-- Create database (uncomment if needed)
-- CREATE DATABASE adzeta_website CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE adzeta_website;

-- Pages table for dynamic page management
CREATE TABLE IF NOT EXISTS pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    content LONGTEXT,
    excerpt TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    og_image VARCHAR(500),
    canonical_url VARCHAR(500),
    template VARCHAR(100) DEFAULT 'default',
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    featured_image VARCHAR(500),
    author_id INT,
    parent_id INT DEFAULT NULL,
    menu_order INT DEFAULT 0,
    page_type ENUM('page', 'landing', 'service', 'product') DEFAULT 'page',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_page_type (page_type),
    INDEX idx_parent (parent_id)
);

-- Blog posts table (Enhanced for Editor.js and SEO)
CREATE TABLE IF NOT EXISTS blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    content LONGTEXT,
    content_blocks JSON,
    excerpt TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    focus_keyword VARCHAR(100),
    canonical_url VARCHAR(500),
    og_image VARCHAR(500),
    og_title VARCHAR(255),
    og_description TEXT,
    twitter_card_type ENUM('summary', 'summary_large_image', 'app', 'player') DEFAULT 'summary_large_image',
    featured_image VARCHAR(500),
    author_id INT,
    category_id INT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    reading_time INT DEFAULT 0,
    word_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    seo_score DECIMAL(3,1) DEFAULT 0.0,
    noindex BOOLEAN DEFAULT FALSE,
    nofollow BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_published (published_at),
    INDEX idx_category (category_id),
    INDEX idx_author (author_id),
    INDEX idx_focus_keyword (focus_keyword),
    INDEX idx_seo_score (seo_score)
);

-- Blog categories table (Enhanced)
CREATE TABLE IF NOT EXISTS blog_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    parent_id INT DEFAULT NULL,
    post_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_parent (parent_id)
);

-- Blog tags table
CREATE TABLE IF NOT EXISTS blog_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    post_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_name (name)
);

-- Post tags junction table
CREATE TABLE IF NOT EXISTS post_tags (
    post_id INT NOT NULL,
    tag_id INT NOT NULL,
    PRIMARY KEY (post_id, tag_id),
    FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES blog_tags(id) ON DELETE CASCADE
);

-- Media library table
CREATE TABLE IF NOT EXISTS media_library (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    alt_text TEXT,
    caption TEXT,
    width INT DEFAULT NULL,
    height INT DEFAULT NULL,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_file_type (file_type),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_filename (filename)
);

-- Reusable content blocks table
CREATE TABLE IF NOT EXISTS reusable_blocks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    block_type VARCHAR(50) NOT NULL,
    block_data JSON NOT NULL,
    description TEXT,
    created_by INT,
    usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_block_type (block_type),
    INDEX idx_created_by (created_by)
);

-- Users/Authors table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    bio TEXT,
    avatar VARCHAR(500),
    role ENUM('admin', 'editor', 'author') DEFAULT 'author',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- Settings table for site configuration
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value LONGTEXT,
    setting_type ENUM('string', 'text', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key)
);

-- Menu items table for dynamic navigation
CREATE TABLE IF NOT EXISTS menu_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    menu_location VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    url VARCHAR(500),
    page_id INT DEFAULT NULL,
    parent_id INT DEFAULT NULL,
    menu_order INT DEFAULT 0,
    css_class VARCHAR(100),
    icon VARCHAR(100),
    target VARCHAR(20) DEFAULT '_self',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_location (menu_location),
    INDEX idx_parent (parent_id),
    INDEX idx_order (menu_order),
    INDEX idx_status (status)
);

-- Contact form submissions
CREATE TABLE IF NOT EXISTS contact_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    company VARCHAR(255),
    phone VARCHAR(50),
    subject VARCHAR(255),
    message TEXT,
    form_type VARCHAR(50) DEFAULT 'contact',
    ip_address VARCHAR(45),
    user_agent TEXT,
    status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_form_type (form_type),
    INDEX idx_created (created_at)
);

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'AdZeta', 'string', 'Website name'),
('site_tagline', 'AI-Powered Value-Based Bidding for E-commerce Growth', 'string', 'Website tagline'),
('site_description', 'Maximize e-commerce profitability with AdZeta\'s AI-powered Value-Based Bidding. Our predictive algorithms target high-LTV customers for sustainable growth and lower CAC.', 'text', 'Default site description'),
('contact_email', '<EMAIL>', 'string', 'Contact email address'),
('google_analytics_id', '', 'string', 'Google Analytics tracking ID'),
('google_search_console_id', '', 'string', 'Google Search Console verification'),
('facebook_pixel_id', '', 'string', 'Facebook Pixel ID'),
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode'),
('posts_per_page', '10', 'number', 'Number of blog posts per page'),
('blog_title', 'AdZeta Blog', 'string', 'Blog section title'),
('blog_description', 'Insights and strategies for e-commerce growth and AI-powered advertising', 'text', 'Blog meta description'),
('default_og_image', '', 'string', 'Default Open Graph image URL'),
('twitter_username', '', 'string', 'Twitter username for cards'),
('enable_sitemap', '1', 'boolean', 'Enable XML sitemap generation'),
('enable_robots_txt', '1', 'boolean', 'Enable robots.txt generation'),
('seo_separator', '|', 'string', 'Title separator for SEO'),
('enable_schema_markup', '1', 'boolean', 'Enable JSON-LD schema markup'),
('compress_images', '1', 'boolean', 'Enable automatic image compression'),
('webp_conversion', '1', 'boolean', 'Enable WebP image conversion'),
('cache_enabled', '1', 'boolean', 'Enable content caching'),
('cache_duration', '3600', 'number', 'Cache duration in seconds')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- Insert default admin user (password: admin123 - CHANGE THIS!)
INSERT INTO users (username, email, password_hash, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin')
ON DUPLICATE KEY UPDATE email = VALUES(email);
