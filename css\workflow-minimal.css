/* Minimal Workflow Styles - Clean, Modern Design */

.workflow-section {
    padding: 80px 0;
    background: linear-gradient(to bottom, #F7F7F9 0%, #FFFFFF 100%);
    position: relative;
    overflow: hidden;
}

.workflow-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
}

.workflow-minimal-container {
    position: relative;
    width: 100%;
    height: 350px;
    margin: 40px auto;
    overflow: visible;
}

/* Clean, minimal card styles */
.workflow-card-minimal {
    position: absolute;
    width: 180px;
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    padding: 20px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.workflow-card-minimal:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

/* Card header with colored bar */
.card-header {
    width: 100%;
    height: 4px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    border-radius: 12px 12px 0 0;
}

.client-header {
    background: linear-gradient(90deg, #FF6A8E 0%, #FF4775 100%);
}

.adzeta-header {
    background: linear-gradient(90deg, #7D7AFF 0%, #5E5CE6 100%);
}

.google-header {
    background: linear-gradient(90deg, #4285F4 0%, #3372DB 100%);
}

.results-header {
    background: linear-gradient(90deg, #30D158 0%, #27AE60 100%);
}

/* Card content */
.card-icon-minimal {
    width: 60px;
    height: 60px;
    margin: 15px 0;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.workflow-card-minimal:hover .card-icon-minimal {
    transform: scale(1.1);
}

.card-title-minimal {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.card-subtitle-minimal {
    font-size: 13px;
    color: #666;
    font-weight: 400;
    line-height: 1.4;
}

/* Connection lines */
.connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
}

/* Data flow particles */
.data-dot {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    z-index: 6;
    opacity: 0;
    filter: blur(0.5px);
}

.data-dot::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: inherit;
    filter: blur(2px);
    opacity: 0.5;
    z-index: -1;
}

.client-dot {
    background: #FF6A8E;
    box-shadow: 0 0 8px rgba(255, 106, 142, 0.8);
}

.adzeta-dot {
    background: #7D7AFF;
    box-shadow: 0 0 8px rgba(125, 122, 255, 0.8);
}

.google-dot {
    background: #4285F4;
    box-shadow: 0 0 8px rgba(66, 133, 244, 0.8);
}

.results-dot {
    background: #30D158;
    box-shadow: 0 0 8px rgba(48, 209, 88, 0.8);
}

.feedback-dot {
    background: linear-gradient(90deg, #FF6A8E, #7D7AFF, #30D158);
    box-shadow: 0 0 8px rgba(125, 122, 255, 0.8);
    animation: flowFeedback 6s infinite;
}

/* Feedback loop */
.feedback-loop-minimal {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #FFFFFF;
    border-radius: 30px;
    padding: 12px 30px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    z-index: 20;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feedback-loop-minimal:hover {
    transform: translateX(-50%) translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.feedback-loop-minimal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 30px;
    border: 1.5px solid transparent;
    background: linear-gradient(90deg, #FF6A8E, #7D7AFF, #30D158) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
    pointer-events: none;
}

.feedback-icon-minimal {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    animation: rotate 8s linear infinite;
}

.feedback-text-minimal {
    font-size: 14px;
    font-weight: 500;
    background: linear-gradient(90deg, #FF6A8E, #7D7AFF, #30D158);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Animations */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Top line animations */
@keyframes flowDot1Top {
    0% { left: 165px; top: 130px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 295px; top: 130px; opacity: 0; }
}

@keyframes flowDot2Top {
    0% { left: 345px; top: 130px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 475px; top: 130px; opacity: 0; }
}

@keyframes flowDot3Top {
    0% { left: 525px; top: 130px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 655px; top: 130px; opacity: 0; }
}

@keyframes flowDot4Top {
    0% { left: 705px; top: 130px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 835px; top: 130px; opacity: 0; }
}

@keyframes flowDot5Top {
    0% { left: 885px; top: 130px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 1015px; top: 130px; opacity: 0; }
}

/* Middle line animations */
@keyframes flowDot1 {
    0% { left: 165px; top: 150px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 295px; top: 150px; opacity: 0; }
}

@keyframes flowDot2 {
    0% { left: 345px; top: 150px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 475px; top: 150px; opacity: 0; }
}

@keyframes flowDot3 {
    0% { left: 525px; top: 150px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 655px; top: 150px; opacity: 0; }
}

@keyframes flowDot4 {
    0% { left: 705px; top: 150px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 835px; top: 150px; opacity: 0; }
}

@keyframes flowDot5 {
    0% { left: 885px; top: 150px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 1015px; top: 150px; opacity: 0; }
}

/* Bottom line animations */
@keyframes flowDot1Bottom {
    0% { left: 165px; top: 170px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 295px; top: 170px; opacity: 0; }
}

@keyframes flowDot2Bottom {
    0% { left: 345px; top: 170px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 475px; top: 170px; opacity: 0; }
}

@keyframes flowDot3Bottom {
    0% { left: 525px; top: 170px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 655px; top: 170px; opacity: 0; }
}

@keyframes flowDot4Bottom {
    0% { left: 705px; top: 170px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 835px; top: 170px; opacity: 0; }
}

@keyframes flowDot5Bottom {
    0% { left: 885px; top: 170px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 1015px; top: 170px; opacity: 0; }
}

@keyframes flowFeedback {
    0% { left: 1015px; top: 200px; opacity: 0; }
    5% { opacity: 1; }
    45% { left: 600px; top: 250px; opacity: 1; }
    50% { left: 600px; top: 250px; opacity: 1; }
    95% { opacity: 1; }
    100% { left: 165px; top: 200px; opacity: 0; }
}

/* Workflow legend */
.workflow-legend-minimal {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    flex-wrap: wrap;
}

.legend-item-minimal {
    display: flex;
    align-items: center;
    margin: 0 15px;
    font-size: 13px;
    color: #555;
}

.legend-color-minimal {
    width: 12px;
    height: 12px;
    border-radius: 3px;
    margin-right: 8px;
}

.legend-client-minimal {
    background: linear-gradient(90deg, #FF6A8E 0%, #FF4775 100%);
}

.legend-adzeta-minimal {
    background: linear-gradient(90deg, #7D7AFF 0%, #5E5CE6 100%);
}

.legend-google-minimal {
    background: linear-gradient(90deg, #4285F4 0%, #3372DB 100%);
}

.legend-results-minimal {
    background: linear-gradient(90deg, #30D158 0%, #27AE60 100%);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .workflow-minimal-container {
        transform: scale(0.9);
        transform-origin: top center;
    }
}

@media (max-width: 992px) {
    .workflow-minimal-container {
        height: 600px;
        transform: scale(0.8);
    }
}

@media (max-width: 768px) {
    .workflow-minimal-container {
        height: 800px;
        transform: scale(0.7);
    }
}

@media (max-width: 576px) {
    .workflow-minimal-container {
        height: 1000px;
        transform: scale(0.6);
    }
}
