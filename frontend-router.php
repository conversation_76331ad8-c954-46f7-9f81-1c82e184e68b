<?php
/**
 * WordPress-Inspired Frontend Router Entry Point
 * Handles all blog-related URLs with caching and template selection
 * 
 * This file replaces the need for .htaccess URL rewriting
 * and provides a more flexible, WordPress-style approach
 */

// Prevent direct access to this file from web if not intended
if (!defined('FRONTEND_ROUTER_ENABLED')) {
    define('FRONTEND_ROUTER_ENABLED', true);
}

// Initialize the application
require_once 'bootstrap.php';

// Load the router
require_once 'adzeta-admin/src/Frontend/Router.php';

// Create router instance
$router = new \AdZetaAdmin\Frontend\Router();

// Get the current URI
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';

// Handle the request
$handled = $router->handleRequest($requestUri);

// If not handled by router, continue with normal processing
if (!$handled) {
    // Check if this is a blog-related URL that should be handled
    if (strpos($requestUri, '/blog') === 0) {
        // This is a blog URL but wasn't handled - likely 404
        header('HTTP/1.1 404 Not Found');
        if (file_exists('404.php')) {
            include '404.php';
        } else {
            echo '<h1>404 - Blog Post Not Found</h1>';
        }
        exit;
    }
    
    // Not a blog URL - let normal processing continue
    // This allows other parts of your site to work normally
    return false;
}

// Request was handled successfully
exit;
