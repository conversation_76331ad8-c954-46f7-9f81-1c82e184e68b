<?php

namespace AdZetaAdmin\API;

use AdZetaAdmin\Models\BlogPost;
use AdZetaAdmin\Models\Category;
use AdZetaAdmin\Models\Tag;

/**
 * Posts API Controller
 */
class PostsController extends BaseController
{
    private $blogPost;
    private $category;
    private $tag;

    public function __construct()
    {
        parent::__construct();

        // Get PDO instance from the database wrapper
        global $admin_db;
        $pdo = $admin_db->getPdo();

        $this->blogPost = new BlogPost($pdo);
        $this->category = new Category($pdo);
        $this->tag = new Tag($pdo);
    }

    /**
     * Get all posts
     */
    public function index()
    {
        $this->requirePermission('view_posts');

        $params = $this->getQueryParams();
        $page = (int)($params['page'] ?? 1);
        $limit = min(50, (int)($params['limit'] ?? 20));
        $status = $params['status'] ?? null;
        $search = $params['search'] ?? null;
        $category_id = $params['category_id'] ?? null;

        try {
            $options = [
                'page' => $page,
                'limit' => $limit,
                'status' => $status,
                'search' => $search,
                'category_id' => $category_id
            ];

            $posts = $this->blogPost->getAll($options);
            $total = $this->blogPost->getCount($options);

            return $this->success([
                'posts' => $posts,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error('Failed to load posts: ' . $e->getMessage());
        }
    }

    /**
     * Get single post
     */
    public function show($id)
    {
        $this->requirePermission('view_posts');

        try {
            $post = $this->blogPost->getById($id);

            if (!$post) {
                return $this->error('Post not found', 404);
            }

            return $this->success(['post' => $post]);

        } catch (\Exception $e) {
            return $this->error('Failed to load post: ' . $e->getMessage());
        }
    }

    /**
     * Create new post
     */
    public function store()
    {
        $payload = $this->requirePermission('create_posts');
        $data = $this->getRequestData();

        $this->validateRequired($data, ['title']);

        try {
            // Sanitize data while preserving HTML in content fields
            $data = $this->sanitizePostData($data);
            $data['author_id'] = $payload['user_id'];
            $postId = $this->blogPost->create($data);

            $post = $this->blogPost->getById($postId);

            return $this->success([
                'message' => 'Post created successfully',
                'post' => $post
            ], 201);

        } catch (\Exception $e) {
            return $this->error('Failed to create post: ' . $e->getMessage());
        }
    }

    /**
     * Update post
     */
    public function update($id)
    {
        $payload = $this->requirePermission('manage_posts');
        $data = $this->getRequestData();

        try {
            $post = $this->blogPost->getById($id);

            if (!$post) {
                return $this->error('Post not found', 404);
            }

            // Check if user can edit this post
            if ($payload['role'] !== 'admin' && $post['author_id'] != $payload['user_id']) {
                return $this->error('You can only edit your own posts', 403);
            }

            // Sanitize data while preserving HTML in content fields
            $data = $this->sanitizePostData($data);

            $success = $this->blogPost->update($id, $data);

            if ($success) {
                $updatedPost = $this->blogPost->getById($id);
                return $this->success([
                    'message' => 'Post updated successfully',
                    'post' => $updatedPost
                ]);
            } else {
                return $this->error('No changes were made');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to update post: ' . $e->getMessage());
        }
    }

    /**
     * Delete post
     */
    public function destroy($id)
    {
        $payload = $this->requirePermission('manage_posts');

        try {
            $post = $this->blogPost->getById($id);

            if (!$post) {
                return $this->error('Post not found', 404);
            }

            // Check if user can delete this post
            if ($payload['role'] !== 'admin' && $post['author_id'] != $payload['user_id']) {
                return $this->error('You can only delete your own posts', 403);
            }

            $success = $this->blogPost->delete($id);

            if ($success) {
                return $this->success(['message' => 'Post deleted successfully']);
            } else {
                return $this->error('Failed to delete post');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to delete post: ' . $e->getMessage());
        }
    }

    /**
     * Publish post
     */
    public function publish($id)
    {
        $payload = $this->requirePermission('manage_posts');

        try {
            $post = $this->blogPost->getById($id);

            if (!$post) {
                return $this->error('Post not found', 404);
            }

            $success = $this->blogPost->update($id, [
                'status' => 'published',
                'published_at' => date('Y-m-d H:i:s')
            ]);

            if ($success) {
                return $this->success(['message' => 'Post published successfully']);
            } else {
                return $this->error('Failed to publish post');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to publish post: ' . $e->getMessage());
        }
    }

    /**
     * Unpublish post
     */
    public function unpublish($id)
    {
        $payload = $this->requirePermission('manage_posts');

        try {
            $post = $this->blogPost->getById($id);

            if (!$post) {
                return $this->error('Post not found', 404);
            }

            $success = $this->blogPost->update($id, [
                'status' => 'draft',
                'published_at' => null
            ]);

            if ($success) {
                return $this->success(['message' => 'Post unpublished successfully']);
            } else {
                return $this->error('Failed to unpublish post');
            }

        } catch (\Exception $e) {
            return $this->error('Failed to unpublish post: ' . $e->getMessage());
        }
    }

    /**
     * Auto-save post
     */
    public function autosave()
    {
        $payload = $this->requirePermission('create_posts');
        $data = $this->getRequestData();

        try {
            $postId = $data['id'] ?? null;

            // Sanitize data while preserving HTML in content fields
            $data = $this->sanitizePostData($data);
            $data['author_id'] = $payload['user_id'];

            if ($postId) {
                // Update existing post
                $success = $this->blogPost->update($postId, $data);
                $post = $this->blogPost->getById($postId);
            } else {
                // Create new draft
                $data['status'] = 'draft';
                $postId = $this->blogPost->create($data);
                $post = $this->blogPost->getById($postId);
            }

            return $this->success([
                'message' => 'Auto-saved successfully',
                'post' => $post,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return $this->error('Auto-save failed: ' . $e->getMessage());
        }
    }

    /**
     * Bulk operations on posts
     */
    public function bulk()
    {
        $payload = $this->requirePermission('manage_posts');
        $data = $this->getRequestData();

        $this->validateRequired($data, ['action', 'post_ids']);

        $action = $data['action'];
        $postIds = $data['post_ids'];

        if (!is_array($postIds) || empty($postIds)) {
            return $this->error('Invalid post IDs provided');
        }

        try {
            $results = [];

            foreach ($postIds as $postId) {
                switch ($action) {
                    case 'publish':
                        $success = $this->blogPost->update($postId, [
                            'status' => 'published',
                            'published_at' => date('Y-m-d H:i:s')
                        ]);
                        break;

                    case 'unpublish':
                        $success = $this->blogPost->update($postId, [
                            'status' => 'draft',
                            'published_at' => null
                        ]);
                        break;

                    case 'archive':
                        $success = $this->blogPost->update($postId, [
                            'status' => 'archived'
                        ]);
                        break;

                    case 'delete':
                        $success = $this->blogPost->delete($postId);
                        break;

                    default:
                        $success = false;
                }

                $results[$postId] = $success;
            }

            $successCount = count(array_filter($results));
            $totalCount = count($postIds);

            return $this->success([
                'message' => "Bulk {$action} completed: {$successCount}/{$totalCount} successful",
                'results' => $results,
                'success_count' => $successCount,
                'total_count' => $totalCount
            ]);

        } catch (\Exception $e) {
            return $this->error('Bulk operation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get categories
     */
    public function getCategories()
    {
        $this->requireAuth();

        try {
            $categories = $this->category->getAll();

            return $this->success(['categories' => $categories]);

        } catch (\Exception $e) {
            return $this->error('Failed to load categories: ' . $e->getMessage());
        }
    }

    /**
     * Create category
     */
    public function createCategory()
    {
        $payload = $this->requirePermission('manage_posts');
        $data = $this->getRequestData();

        $this->validateRequired($data, ['name']);

        try {
            $categoryId = $this->category->create($data);
            $category = $this->category->getById($categoryId);

            return $this->success([
                'message' => 'Category created successfully',
                'category' => $category
            ], 201);

        } catch (\Exception $e) {
            return $this->error('Failed to create category: ' . $e->getMessage());
        }
    }
}
