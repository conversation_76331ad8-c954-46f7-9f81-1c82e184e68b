/* Modern Metrics Section Styles */
.metrics-section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

/* Subtle background pattern */
.metrics-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at 10% 20%, rgba(143, 118, 245, 0.03) 0%, transparent 50%),
                      radial-gradient(circle at 90% 80%, rgba(233, 88, 161, 0.03) 0%, transparent 50%);
    z-index: 0;
}

/* Modern metrics card */
.metrics-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.03), 
                0 1px 3px rgba(0, 0, 0, 0.02);
    padding: 40px;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    z-index: 1;
}

.metrics-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.08), 
                0 3px 5px rgba(0, 0, 0, 0.04);
}

/* Subtle card accent */
.metrics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #e958a1, #8f76f5);
    opacity: 0.8;
}

/* Metrics heading with underline */
.metrics-heading {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

.metrics-heading-underline {
    position: absolute;
    bottom: -5px;
    left: 0;
    height: 3px;
    width: 80px;
    background: #ff6a8d;
    border-radius: 3px;
}

/* Metric value styling */
.metric-value {
    font-size: 64px;
    line-height: 1;
    font-weight: 700;
    margin-bottom: 10px;
    background-clip: text;
    -webkit-background-clip: text;
    position: relative;
    display: inline-block;
}

.metric-value-up {
    color: #2ecc71;
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.metric-value-down {
    color: #3498db;
    background: linear-gradient(135deg, #3498db, #2980b9);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Direction indicators */
.direction-indicator {
    position: relative;
    top: -5px;
    margin-left: 5px;
    font-size: 24px;
}

/* Metric label styling */
.metric-label {
    font-size: 18px;
    font-weight: 600;
    color: #2c2e3c;
    margin-bottom: 15px;
}

/* Metric description styling */
.metric-description {
    font-size: 15px;
    line-height: 1.6;
    color: #797a85;
    max-width: 90%;
}

/* Data source indicator */
.data-source {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(0,0,0,0.05);
    font-size: 13px;
    color: #797a85;
    display: flex;
    align-items: center;
}

.data-source-icon {
    margin-right: 8px;
    opacity: 0.7;
}

/* Verification badge */
.verification-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    background: rgba(46, 204, 113, 0.1);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: #2ecc71;
    margin-bottom: 15px;
}

.verification-badge i {
    margin-right: 5px;
    font-size: 10px;
}

/* Testimonial card */
.testimonial-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.03), 
                0 1px 3px rgba(0, 0, 0, 0.02);
    padding: 30px;
    margin-top: 40px;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.08), 
                0 3px 5px rgba(0, 0, 0, 0.04);
}

.testimonial-quote {
    position: relative;
    padding-left: 25px;
    font-size: 16px;
    line-height: 1.6;
    color: #2c2e3c;
}

.testimonial-quote::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, #e958a1, #8f76f5);
    border-radius: 3px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    margin-top: 20px;
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
    border: 2px solid #ffffff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.testimonial-info {
    display: flex;
    flex-direction: column;
}

.testimonial-name {
    font-size: 16px;
    font-weight: 600;
    color: #2c2e3c;
}

.testimonial-position {
    font-size: 14px;
    color: #797a85;
}

/* Animation for metrics */
@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-count-up {
    animation: countUp 1s ease-out forwards;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .metrics-card {
        padding: 30px;
        margin-bottom: 30px;
    }
    
    .metric-value {
        font-size: 48px;
    }
}

@media (max-width: 767px) {
    .metrics-section {
        padding: 60px 0;
    }
    
    .metric-value {
        font-size: 42px;
    }
    
    .metric-description {
        max-width: 100%;
    }
}
