/**
 * Comparison Section Animations
 * Creates engaging animations for the comparison section
 */

document.addEventListener('DOMContentLoaded', function() {
    // Animate elements when they come into view
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right, .scale-in');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementBottom = element.getBoundingClientRect().bottom;
            const windowHeight = window.innerHeight;
            
            // Check if element is in viewport
            if (elementTop < windowHeight * 0.9 && elementBottom > 0) {
                element.classList.add('active');
            }
        });
    };
    
    // Initial check for elements in viewport
    animateOnScroll();
    
    // Listen for scroll events
    window.addEventListener('scroll', animateOnScroll);
    
    // Animate benefit cards sequentially
    const animateBenefitCards = () => {
        const traditionalCards = document.querySelectorAll('.traditional .benefit-card');
        const adzetaCards = document.querySelectorAll('.adzeta .benefit-card');
        
        // Animate traditional cards
        traditionalCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('active');
            }, 300 * index);
        });
        
        // Animate Adzeta cards with a slight delay
        setTimeout(() => {
            adzetaCards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('active');
                }, 300 * index);
            });
        }, 500);
    };
    
    // Animate stats with counting effect
    const animateStats = () => {
        const stats = document.querySelectorAll('.stat-value');
        
        stats.forEach(stat => {
            const targetValue = parseInt(stat.getAttribute('data-value'));
            const duration = 2000; // 2 seconds
            const startTime = Date.now();
            const startValue = 0;
            
            const updateValue = () => {
                const currentTime = Date.now();
                const elapsed = currentTime - startTime;
                
                if (elapsed < duration) {
                    const value = Math.floor(easeOutQuad(elapsed, startValue, targetValue, duration));
                    stat.textContent = value + '%';
                    requestAnimationFrame(updateValue);
                } else {
                    stat.textContent = targetValue + '%';
                }
            };
            
            // Easing function for smoother animation
            const easeOutQuad = (t, b, c, d) => {
                t /= d;
                return -c * t * (t - 2) + b;
            };
            
            // Start animation when stat comes into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateValue();
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(stat);
        });
    };
    
    // Initialize animations when comparison section is in view
    const comparisonSection = document.querySelector('.comparison-section');
    if (comparisonSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateBenefitCards();
                    animateStats();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });
        
        observer.observe(comparisonSection);
    }
    
    // Add hover effects to benefit cards
    const benefitCards = document.querySelectorAll('.benefit-card');
    benefitCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.benefit-icon i');
            icon.style.transform = 'scale(1.2)';
            setTimeout(() => {
                icon.style.transform = 'scale(1)';
            }, 300);
        });
    });
    
    // Add pulse effect to VS badge
    const vsBadge = document.querySelector('.vs-badge');
    if (vsBadge) {
        setInterval(() => {
            vsBadge.classList.add('pulse');
            setTimeout(() => {
                vsBadge.classList.remove('pulse');
            }, 1000);
        }, 3000);
    }
});
