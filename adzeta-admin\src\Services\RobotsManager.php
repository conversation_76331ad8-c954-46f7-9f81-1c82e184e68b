<?php

namespace AdZetaAdmin\Services;

/**
 * Robots.txt Manager Service
 * Handles robots.txt file management and validation
 */
class RobotsManager
{
    private string $robotsPath;
    private string $baseUrl;

    public function __construct(string $baseUrl = '')
    {
        $this->robotsPath = $_SERVER['DOCUMENT_ROOT'] . '/robots.txt';
        $this->baseUrl = $baseUrl ?: $this->getBaseUrl();
    }

    /**
     * Get robots.txt content
     */
    public function getContent(): array
    {
        $exists = file_exists($this->robotsPath);
        $content = '';

        if ($exists) {
            $content = file_get_contents($this->robotsPath);
            if ($content === false) {
                $content = '';
                $exists = false;
            }
        }

        return [
            'success' => true,
            'data' => [
                'content' => $content,
                'exists' => $exists,
                'path' => $this->robotsPath,
                'url' => $this->baseUrl . '/robots.txt'
            ]
        ];
    }

    /**
     * Save robots.txt content
     */
    public function saveContent(string $content): array
    {
        try {
            // Ensure the content ends with a newline
            if (!empty($content) && !str_ends_with($content, "\n")) {
                $content .= "\n";
            }

            $success = file_put_contents($this->robotsPath, $content);
            
            if ($success === false) {
                throw new \Exception('Failed to write robots.txt file');
            }

            return [
                'success' => true,
                'message' => 'Robots.txt saved successfully',
                'data' => [
                    'file_size' => strlen($content),
                    'url' => $this->baseUrl . '/robots.txt'
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to save robots.txt: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate robots.txt content
     */
    public function validateContent(string $content): array
    {
        $issues = [];
        $warnings = [];
        $lines = explode("\n", $content);
        
        $hasUserAgent = false;
        $hasSitemap = false;
        $currentUserAgent = null;
        
        foreach ($lines as $lineNumber => $line) {
            $trimmedLine = trim($line);
            $lineNum = $lineNumber + 1;
            
            // Skip empty lines and comments
            if (empty($trimmedLine) || str_starts_with($trimmedLine, '#')) {
                continue;
            }
            
            // Check for basic syntax
            if (!str_contains($trimmedLine, ':')) {
                $issues[] = "Line {$lineNum}: Invalid syntax - missing colon";
                continue;
            }
            
            [$directive, $value] = array_map('trim', explode(':', $trimmedLine, 2));
            $directive = strtolower($directive);
            
            switch ($directive) {
                case 'user-agent':
                    $hasUserAgent = true;
                    $currentUserAgent = $value;
                    if (empty($value)) {
                        $issues[] = "Line {$lineNum}: User-agent value cannot be empty";
                    }
                    break;
                    
                case 'disallow':
                case 'allow':
                    if ($currentUserAgent === null) {
                        $issues[] = "Line {$lineNum}: {$directive} directive must come after User-agent";
                    }
                    // Value can be empty for disallow (means disallow nothing)
                    break;
                    
                case 'sitemap':
                    $hasSitemap = true;
                    if (empty($value)) {
                        $issues[] = "Line {$lineNum}: Sitemap URL cannot be empty";
                    } elseif (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $issues[] = "Line {$lineNum}: Invalid sitemap URL format";
                    }
                    break;
                    
                case 'crawl-delay':
                    if (!is_numeric($value) || $value < 0) {
                        $issues[] = "Line {$lineNum}: Crawl-delay must be a positive number";
                    }
                    break;
                    
                default:
                    $warnings[] = "Line {$lineNum}: Unknown directive '{$directive}'";
                    break;
            }
        }
        
        // Check for required elements
        if (!$hasUserAgent) {
            $issues[] = "Missing User-agent directive";
        }
        
        if (!$hasSitemap) {
            $warnings[] = "Consider adding a Sitemap directive";
        }
        
        return [
            'valid' => empty($issues),
            'issues' => $issues,
            'warnings' => $warnings,
            'has_user_agent' => $hasUserAgent,
            'has_sitemap' => $hasSitemap
        ];
    }

    /**
     * Generate robots.txt from template
     */
    public function generateFromTemplate(string $templateType): array
    {
        $templates = $this->getTemplates();
        
        if (!isset($templates[$templateType])) {
            return [
                'success' => false,
                'message' => 'Invalid template type'
            ];
        }
        
        $content = $templates[$templateType];
        
        // Replace placeholder with actual sitemap URL
        $content = str_replace('{SITEMAP_URL}', $this->baseUrl . '/sitemap.xml', $content);
        
        return [
            'success' => true,
            'content' => $content,
            'template' => $templateType
        ];
    }

    /**
     * Get available templates
     */
    public function getTemplates(): array
    {
        return [
            'basic' => "# Basic robots.txt for AdZeta
User-agent: *
Disallow: /adzeta-admin/
Disallow: /api/
Disallow: /private/
Allow: /

Sitemap: {SITEMAP_URL}",

            'marketing' => "# Marketing Agency robots.txt for AdZeta
User-agent: *
Disallow: /adzeta-admin/
Disallow: /api/
Disallow: /private/
Disallow: /temp/
Disallow: /cache/
Allow: /blog/
Allow: /case-studies/
Allow: /whitepapers/
Allow: /

# Allow Google to crawl everything
User-agent: Googlebot
Allow: /

# Block specific bots if needed
User-agent: AhrefsBot
Crawl-delay: 10

Sitemap: {SITEMAP_URL}",

            'strict' => "# Strict robots.txt for AdZeta
User-agent: *
Disallow: /adzeta-admin/
Disallow: /api/
Disallow: /private/
Disallow: /temp/
Disallow: /cache/
Disallow: /vendor/
Disallow: /*.pdf$
Disallow: /*.doc$
Disallow: /*.docx$
Disallow: /*.zip$
Allow: /blog/
Allow: /case-studies/
Allow: /whitepapers/
Allow: /

# Specific rules for search engines
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

# Block aggressive crawlers
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

Sitemap: {SITEMAP_URL}",

            'permissive' => "# Permissive robots.txt for AdZeta
User-agent: *
Allow: /

# Block only sensitive areas
Disallow: /adzeta-admin/
Disallow: /private/

Sitemap: {SITEMAP_URL}"
        ];
    }

    /**
     * Get robots.txt file status
     */
    public function getStatus(): array
    {
        $exists = file_exists($this->robotsPath);
        
        $data = [
            'exists' => $exists,
            'path' => $this->robotsPath,
            'url' => $this->baseUrl . '/robots.txt',
            'file_size' => 0,
            'last_modified' => null,
            'is_writable' => is_writable(dirname($this->robotsPath))
        ];

        if ($exists) {
            $data['file_size'] = filesize($this->robotsPath);
            $data['last_modified'] = date('Y-m-d H:i:s', filemtime($this->robotsPath));
            $data['is_readable'] = is_readable($this->robotsPath);
        }

        return $data;
    }

    /**
     * Test robots.txt accessibility
     */
    public function testAccessibility(): array
    {
        $url = $this->baseUrl . '/robots.txt';
        
        try {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'AdZeta-RobotsChecker/1.0'
                ]
            ]);
            
            $content = @file_get_contents($url, false, $context);
            
            if ($content === false) {
                return [
                    'accessible' => false,
                    'message' => 'Robots.txt is not accessible via HTTP'
                ];
            }
            
            return [
                'accessible' => true,
                'message' => 'Robots.txt is accessible',
                'content_length' => strlen($content)
            ];
            
        } catch (\Exception $e) {
            return [
                'accessible' => false,
                'message' => 'Error testing accessibility: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get base URL
     */
    private function getBaseUrl(): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }
}
