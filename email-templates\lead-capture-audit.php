<?php
/**
 * Lead Capture Form Handler for Free Audit
 * Modern PHP form processing with security and validation
 */

// Include configuration
require_once 'config.php';

// Security headers
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Rate limiting check
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
if (!checkRateLimit($clientIP)) {
    http_response_code(429);
    echo json_encode(['success' => false, 'message' => 'Too many submissions. Please try again later.']);
    exit;
}

// CSRF Protection (basic)
session_start();
if (!isset($_SESSION['form_token'])) {
    $_SESSION['form_token'] = bin2hex(random_bytes(32));
}

// Input validation and sanitization
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function validateUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

// Collect and validate form data
$errors = [];
$data = [];

// Required fields
$requiredFields = [
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'work_email' => 'Work Email',
    'monthly_ad_spend' => 'Monthly Ad Spend'
];

foreach ($requiredFields as $field => $label) {
    if (empty($_POST[$field])) {
        $errors[] = "$label is required";
    } else {
        $data[$field] = sanitizeInput($_POST[$field]);
    }
}

// Optional fields
$optionalFields = ['phone_number', 'marketing_challenge', 'lead_source', 'company_name', 'website_url'];
foreach ($optionalFields as $field) {
    $data[$field] = isset($_POST[$field]) ? sanitizeInput($_POST[$field]) : '';
}

// Specific validations
if (!empty($data['work_email']) && !validateEmail($data['work_email'])) {
    $errors[] = 'Please enter a valid email address';
}

// Only validate URL format if website_url is provided and not empty
if (!empty($data['website_url'])) {
    // More flexible URL validation - allow URLs without protocol
    $url = $data['website_url'];
    // Add protocol if missing
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'https://' . $url;
        $data['website_url'] = $url; // Update the data with the corrected URL
    }
    // Now validate the URL
    if (!validateUrl($url)) {
        $errors[] = 'Please enter a valid website URL';
    }
}

// Check for errors
if (!empty($errors)) {
    echo json_encode([
        'success' => false,
        'message' => 'Please fix the following errors: ' . implode(', ', $errors)
    ]);
    exit;
}

// Additional data
$data['ip_address'] = $clientIP;
$data['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
$data['submitted_at'] = date('Y-m-d H:i:s');
$data['form_type'] = 'free_audit';

// Email configuration
$companyName = !empty($data['company_name']) ? $data['company_name'] : $data['first_name'] . ' ' . $data['last_name'];
$subject = 'New Free Audit Request - ' . $companyName;

// Create email content using template
$emailContent = getEmailTemplate('free_audit', $data);

// Send email
$emailSent = sendFormEmail(ADMIN_EMAIL, $subject, $emailContent, $data['work_email']);

// Log submission
logSubmission($data);

// Return response
if ($emailSent) {
    echo json_encode([
        'success' => true,
        'message' => 'Thank you! Your audit request has been submitted successfully. We\'ll contact you within 24 hours.'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Sorry, there was an error submitting your request. Please try again or contact us directly.'
    ]);
}
?>
