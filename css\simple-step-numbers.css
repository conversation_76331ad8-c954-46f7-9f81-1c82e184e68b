/* Apple-inspired cutting-edge step number styling */
.homepage .process-step-icon {
    background: linear-gradient(135deg, #f8f8f8, #ffffff) !important;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.03), inset 0 0 0 1px rgba(255, 255, 255, 0.9) !important;
    border: none !important;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    position: relative;
    overflow: visible;
}

.homepage .process-step-icon::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.08);
    z-index: 0;
}

.homepage .process-step-icon .number {
    background: linear-gradient(135deg, #ff6a8d, #f45888);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    font-weight: 600 !important;
    font-size: 16px !important;
    position: relative;
    z-index: 2;
    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
}

.hover-box:hover .process-step-icon {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.08), 0 4px 10px rgba(0, 0, 0, 0.04), inset 0 0 0 1px rgba(255, 255, 255, 0.9) !important;
}

.hover-box:hover .process-step-icon .number {
    background: linear-gradient(135deg, #ff7c9b, #ff5c82);
    -webkit-background-clip: text;
    background-clip: text;
}

.homepage .progress-step-separator {
    background: linear-gradient(to bottom, #f45888 0%, rgba(244, 88, 136, 0.3) 100%) !important;
    width: 2px !important;
    opacity: 0.8 !important;
    height: calc(100% - 70px) !important;
    top: 70px !important;
    border-radius: 2px;
}

.box-overlay {
    display: none;
}

/* Process step content refinements */
.process-content {
    padding-left: 35px !important;
}

@media (max-width: 767px) {
    .homepage .process-step-icon {
        height: 50px !important;
        width: 50px !important;
    }

    .homepage .progress-step-separator {
        height: calc(100% - 60px) !important;
        top: 60px !important;
    }
}
