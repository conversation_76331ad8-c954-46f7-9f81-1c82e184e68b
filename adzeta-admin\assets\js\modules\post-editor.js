/**
 * AdZeta Admin Panel - Post Editor Module
 * Comprehensive blog post editor with Editor.js integration
 */

window.AdZetaPostEditor = {
    // Editor state
    state: {
        isEditing: false,
        currentPost: null,
        editor: null,
        hasChanges: false,
        autoSaveTimer: null,
        lastSaved: null,
        sourceViewActive: false
    },

    // Initialize post editor module
    init() {
        this.bindEvents();
        console.log('Post Editor module initialized');
    },

    // Bind event listeners
    bindEvents() {
        // Auto-save setup
        this.setupAutoSave();

        // Keyboard shortcuts
        this.setupKeyboardShortcuts();

        // Fullscreen ESC key handler
        this.setupFullscreenHandler();

        // Window beforeunload warning
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    },

    // Create new post
    createNew() {
        this.state.currentPost = {
            id: null,
            title: '',
            slug: '',
            content: '',
            content_blocks: {},
            excerpt: '',
            meta_title: '',
            meta_description: '',
            focus_keyword: '',
            featured_image: '',
            og_image: '',
            category_ids: [],
            tags: [],
            status: 'draft',
            visibility: 'public',
            published_at: '',
            template: 'professional-enhanced',
            author_id: null,
            allow_comments: true,
            password: '',
            canonical_url: '',
            noindex: false,
            nofollow: false,
            custom_fields: []
        };

        this.showEditor();
        // Delay editor initialization to ensure all scripts are loaded
        setTimeout(() => {
            this.initializeEditor();
        }, 100);
        this.updatePageHeader('New Post');
    },

    // Edit existing post
    edit(postId) {
        console.log(`Opening editor for post ID: ${postId}`);
        this.loadPost(postId).then(() => {
            this.showEditor();
            // Delay editor initialization to ensure all scripts are loaded
            setTimeout(() => {
                this.initializeEditor();
            }, 100);
            this.updatePageHeader('Edit Post');
        }).catch(error => {
            console.error('Failed to load post for editing:', error);
            // Fallback to posts view if post loading fails
            window.AdZetaNavigation.showView('posts');
        });
    },

    // Load post data
    async loadPost(postId) {
        try {
            console.log('🔍 DEBUG: Loading post from database, ID:', postId);
            const response = await window.AdZetaApp.apiRequest(`/posts/${postId}`);

            console.log('🔍 DEBUG: Raw API response:', response);

            if (response.success && response.post) {
                console.log('🔍 DEBUG: Post data from database:', response.post);
                console.log('🔍 DEBUG: Content field:', response.post.content);
                console.log('🔍 DEBUG: Content type:', typeof response.post.content);
                console.log('🔍 DEBUG: Content length:', response.post.content ? response.post.content.length : 'NULL');
                console.log('🔍 DEBUG: Content_blocks field:', response.post.content_blocks);
                console.log('🔍 DEBUG: Content_blocks type:', typeof response.post.content_blocks);

                this.state.currentPost = {
                    id: response.post.id,
                    title: response.post.title || '',
                    slug: response.post.slug || '',
                    content: response.post.content || '',
                    content_blocks: response.post.content_blocks || null,
                    excerpt: response.post.excerpt || '',
                    status: response.post.status || 'draft',
                    meta_title: response.post.meta_title || '',
                    meta_description: response.post.meta_description || '',
                    focus_keyword: response.post.focus_keyword || '',
                    featured_image: response.post.featured_image || '',
                    og_image: response.post.og_image || '',
                    category_id: response.post.category_id || 1,
                    tags: response.post.tags || [],
                    visibility: response.post.visibility || 'public',
                    published_at: response.post.published_at || '',
                    template: response.post.template || 'professional-enhanced',
                    author_id: response.post.author_id || null,
                    allow_comments: response.post.allow_comments !== false,
                    password: response.post.password || '',
                    canonical_url: response.post.canonical_url || '',
                    noindex: response.post.noindex || false,
                    nofollow: response.post.nofollow || false,
                    custom_fields: response.post.custom_fields || []
                };

                console.log('🔍 DEBUG: Final currentPost state:', this.state.currentPost);
                console.log('🔍 DEBUG: Final content field:', this.state.currentPost.content);
                console.log('🔍 DEBUG: Final content_blocks field:', this.state.currentPost.content_blocks);
            } else {
                throw new Error(response.message || 'Post not found');
            }
        } catch (error) {
            console.error('❌ DEBUG: Error loading post:', error);
            window.AdZetaApp.showNotification('Failed to load post: ' + error.message, 'danger');
            throw error;
        }
    },

    // Show post editor view
    showEditor() {
        // Hide other views
        document.querySelectorAll('.view-content').forEach(view => {
            view.style.display = 'none';
        });

        // Show editor view
        const editorView = document.getElementById('postEditorView');
        if (editorView) {
            editorView.style.display = 'block';
            this.renderEditor();
        }

        this.state.isEditing = true;

        // Update URL to reflect current action
        const action = this.state.currentPost.id ? 'edit' : 'new';
        const id = this.state.currentPost.id || null;
        window.AdZetaNavigation.updateURLWithAction('posts', action, id);

        // Update navigation state - show "Add Post" as active for both new and edit posts
        // Both creating and editing posts should show "Add Post" as active
        window.AdZetaNavigation.updateActiveNavLink('add-post');
        console.log(`Navigation: Set "Add Post" as active for ${action} post`);

        // Update current view to posts so the editor knows where to return
        window.AdZetaNavigation.currentView = 'posts';

        // Update navigation
        window.AdZetaNavigation.addBreadcrumb([
            { text: 'Posts', view: 'posts' },
            { text: this.state.currentPost.id ? 'Edit Post' : 'New Post' }
        ]);
    },

    // Hide post editor
    hideEditor() {
        if (this.state.hasChanges) {
            if (!confirm('You have unsaved changes. Are you sure you want to close the editor?')) {
                return false;
            }
        }

        // Destroy editor
        this.destroyEditor();

        // Hide editor view
        const editorView = document.getElementById('postEditorView');
        if (editorView) {
            editorView.style.display = 'none';
        }

        // Show posts view and reset URL
        window.AdZetaNavigation.updateURL('posts');
        window.AdZetaNavigation.updateActiveNavLink('posts');
        window.AdZetaNavigation.navigateTo('posts');

        this.state.isEditing = false;
        this.state.currentPost = null;
        this.state.hasChanges = false;

        // Clear auto-save timer
        this.clearAutoSave();

        // Remove breadcrumb
        window.AdZetaNavigation.removeBreadcrumb();

        return true;
    },

    // Render editor interface
    renderEditor() {
        const editorView = document.getElementById('postEditorView');
        if (!editorView) return;

        editorView.innerHTML = `
            <div class="row g-4">
                <!-- Main Editor Column (70%) -->
                <div class="col-lg-8">
                    <!-- Template Selection -->
                    ${this.renderTemplateSelection()}

                    <!-- Post Header -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <!-- Title -->
                            <div class="mb-4">
                                <div class="input-group">
                                    <input type="text" id="postTitle" class="form-control form-control-lg"
                                           placeholder="Enter your post title..."
                                           value="${this.state.currentPost.title}">
                                    <button class="btn btn-outline-primary" type="button"
                                            data-ai-action="generate-title" data-ai-target="post-editor"
                                            title="Generate AI title suggestions">
                                        <i class="fas fa-robot"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- URL Slug -->
                            <div class="mb-4">
                                <label class="form-label text-muted small">URL Slug</label>
                                <div class="input-group">
                                    <span class="input-group-text">/blog/</span>
                                    <input type="text" id="postSlug" class="form-control"
                                           value="${this.state.currentPost.slug}">
                                </div>
                                <small class="text-muted">Permalink: <span id="postPermalink">${this.getPostUrl()}</span></small>
                            </div>

                            <!-- Status and Settings -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-3">
                                    <label class="form-label text-muted small">Status</label>
                                    <select id="postStatus" class="form-select">
                                        <option value="draft" ${this.state.currentPost.status === 'draft' ? 'selected' : ''}>Draft</option>
                                        <option value="published" ${this.state.currentPost.status === 'published' ? 'selected' : ''}>Published</option>
                                        <option value="scheduled" ${this.state.currentPost.status === 'scheduled' ? 'selected' : ''}>Scheduled</option>
                                    </select>
                                </div>
                                <div class="col-md-5" id="publishDateContainer" style="display: none;">
                                    <label class="form-label text-muted small">Publish Date</label>
                                    <input type="datetime-local" id="publishDate" class="form-control"
                                           value="${this.state.currentPost.published_at}">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label text-muted small">Visibility</label>
                                    <select id="postVisibility" class="form-select">
                                        <option value="public" ${this.state.currentPost.visibility === 'public' ? 'selected' : ''}>Public</option>
                                        <option value="private" ${this.state.currentPost.visibility === 'private' ? 'selected' : ''}>Private</option>
                                        <option value="password" ${this.state.currentPost.visibility === 'password' ? 'selected' : ''}>Password Protected</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Excerpt -->
                            <div class="mb-3">
                                <label class="form-label text-muted small">
                                    Excerpt <span class="text-muted" id="excerptCounter">(0/150)</span>
                                </label>
                                <textarea id="postExcerpt" class="form-control" rows="3" maxlength="150"
                                          placeholder="Write a brief description of your post...">${this.state.currentPost.excerpt}</textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Content Editor -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Content</h6>
                            <div class="d-flex align-items-center gap-2">
                                <small class="text-muted" id="wordCount">0 words</small>
                                <small class="text-muted" id="readingTime">0 min read</small>
                                <button class="btn btn-outline-primary btn-sm" onclick="AdZetaPostEditor.showAIGenerationModal()" title="Generate content with AI">
                                    <i class="fas fa-robot"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="AdZetaPostEditor.toggleSourceView()" title="View HTML source">
                                    <i class="fas fa-code"></i>
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="AdZetaPostEditor.toggleFullscreen()" title="Toggle fullscreen">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div id="editorjs" style="min-height: 400px; padding: 2rem;"></div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar (30%) -->
                <div class="col-lg-4">
                    <!-- Featured Image -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-image me-2 text-warning"></i>
                                Featured Image
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="featured-image-container ${this.state.currentPost.featured_image ? 'has-image' : ''}"
                                 id="featuredImageContainer" onclick="AdZetaPostEditor.openMediaLibrary()">
                                ${this.state.currentPost.featured_image ? `
                                    <div class="featured-image-preview">
                                        <img src="${this.state.currentPost.featured_image}" alt="Featured Image" id="featuredImagePreview">
                                        <div class="featured-image-actions">
                                            <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); AdZetaPostEditor.openMediaLibrary()">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); AdZetaPostEditor.removeFeaturedImage()">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                ` : `
                                    <div class="text-center py-4">
                                        <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                        <h6>Set Featured Image</h6>
                                        <p class="text-muted small mb-0">Click to select an image from media library</p>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>

                    <!-- AI Assistant Panel -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-robot me-2 text-primary"></i>
                                AI Assistant
                            </h6>
                            <button class="btn btn-sm btn-outline-secondary" onclick="AdZetaAI.showAIAssistant()" title="Open AI Assistant">
                                <i class="fas fa-expand-alt"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- Apple-inspired AI Assistant Sidebar -->
                            <div class="ai-sidebar-modern">
                                <!-- Recommendation Banner -->
                                <div class="recommendation-compact mb-3">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="rec-icon">💡</div>
                                        <div class="rec-content">
                                            <div class="rec-title">Recommended</div>
                                            <div class="rec-text">Use "Generate Complete SEO" for best results</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Actions -->
                                <div class="main-actions-compact mb-3">
                                    <button class="action-btn primary" data-ai-action="analyze-seo" data-ai-target="post-editor">
                                        <div class="action-icon">📊</div>
                                        <div class="action-content">
                                            <div class="action-title">Analyze SEO</div>
                                            <div class="action-desc">Get actionable insights</div>
                                        </div>
                                    </button>

                                    <button class="action-btn secondary" data-ai-action="generate-all" data-ai-target="post-editor">
                                        <div class="action-icon">🚀</div>
                                        <div class="action-content">
                                            <div class="action-title">Generate Complete SEO</div>
                                            <div class="action-desc">Auto-create all SEO fields</div>
                                        </div>
                                    </button>
                                </div>

                                <!-- Quick Tools Grid -->
                                <div class="quick-tools-compact">
                                    <div class="tools-title">Quick Tools</div>
                                    <div class="tools-grid-compact">
                                        <button class="tool-btn" data-ai-action="generate-meta-title" data-ai-target="post-editor" title="Generate Meta Title">
                                            <span class="tool-icon">🏷️</span>
                                            <span class="tool-label">Meta Title</span>
                                        </button>
                                        <button class="tool-btn" data-ai-action="generate-meta" data-ai-target="post-editor" title="Generate Meta Description">
                                            <span class="tool-icon">📝</span>
                                            <span class="tool-label">Description</span>
                                        </button>
                                        <button class="tool-btn" data-ai-action="generate-tags" data-ai-target="post-editor" title="Generate Tags">
                                            <span class="tool-icon">🏷️</span>
                                            <span class="tool-label">Tags</span>
                                        </button>
                                        <button class="tool-btn" data-ai-action="suggest-keywords" data-ai-target="post-editor" title="Suggest Keywords">
                                            <span class="tool-icon">🎯</span>
                                            <span class="tool-label">Keywords</span>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <style>
                                .ai-sidebar-modern {
                                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                                }

                                .recommendation-compact {
                                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                                    border: 1px solid #dee2e6;
                                    border-radius: 8px;
                                    padding: 12px;
                                }
                                .rec-icon {
                                    font-size: 1.2rem;
                                    width: 28px;
                                    height: 28px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    background: #fff;
                                    border-radius: 6px;
                                    box-shadow: 0 1px 4px rgba(0,0,0,0.1);
                                    flex-shrink: 0;
                                }
                                .rec-title {
                                    font-weight: 600;
                                    color: #1d1d1f;
                                    font-size: 0.8rem;
                                    line-height: 1;
                                }
                                .rec-text {
                                    color: #86868b;
                                    font-size: 0.75rem;
                                    margin-top: 2px;
                                    line-height: 1.2;
                                }

                                .main-actions-compact {
                                    display: flex;
                                    flex-direction: column;
                                    gap: 8px;
                                }
                                .action-btn {
                                    display: flex;
                                    align-items: center;
                                    gap: 10px;
                                    padding: 12px;
                                    border: 1px solid #d2d2d7;
                                    border-radius: 8px;
                                    background: #fff;
                                    cursor: pointer;
                                    transition: all 0.2s ease;
                                    text-align: left;
                                    width: 100%;
                                }
                                .action-btn:hover {
                                    border-color: #007aff;
                                    box-shadow: 0 2px 8px rgba(0,122,255,0.15);
                                    transform: translateY(-1px);
                                }
                                .action-btn.primary {
                                    background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
                                    color: white;
                                    border-color: #007aff;
                                }
                                .action-btn.primary:hover {
                                    box-shadow: 0 4px 12px rgba(0,122,255,0.3);
                                }
                                .action-btn.secondary {
                                    background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
                                    color: white;
                                    border-color: #34c759;
                                }
                                .action-btn.secondary:hover {
                                    box-shadow: 0 4px 12px rgba(52,199,89,0.3);
                                }
                                .action-btn .action-icon {
                                    font-size: 1.3rem;
                                    width: 32px;
                                    height: 32px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    background: rgba(255,255,255,0.2);
                                    border-radius: 8px;
                                    flex-shrink: 0;
                                }
                                .action-btn .action-content {
                                    flex: 1;
                                }
                                .action-btn .action-title {
                                    font-weight: 600;
                                    font-size: 0.85rem;
                                    margin-bottom: 2px;
                                    line-height: 1;
                                }
                                .action-btn .action-desc {
                                    opacity: 0.8;
                                    font-size: 0.75rem;
                                    line-height: 1.2;
                                }

                                .quick-tools-compact {
                                    border-top: 1px solid #f0f0f0;
                                    padding-top: 12px;
                                }
                                .tools-title {
                                    font-weight: 600;
                                    color: #1d1d1f;
                                    margin-bottom: 8px;
                                    font-size: 0.8rem;
                                }
                                .tools-grid-compact {
                                    display: grid;
                                    grid-template-columns: repeat(2, 1fr);
                                    gap: 6px;
                                }
                                .tool-btn {
                                    display: flex;
                                    flex-direction: column;
                                    align-items: center;
                                    gap: 4px;
                                    padding: 8px 4px;
                                    border: 1px solid #e5e5e7;
                                    border-radius: 6px;
                                    background: #fff;
                                    cursor: pointer;
                                    transition: all 0.2s ease;
                                }
                                .tool-btn:hover {
                                    border-color: #007aff;
                                    background: #f0f8ff;
                                    transform: translateY(-1px);
                                    box-shadow: 0 2px 6px rgba(0,122,255,0.15);
                                }
                                .tool-btn .tool-icon {
                                    font-size: 1.1rem;
                                }
                                .tool-btn .tool-label {
                                    font-size: 0.7rem;
                                    font-weight: 500;
                                    color: #1d1d1f;
                                    text-align: center;
                                    line-height: 1;
                                }
                            </style>
                        </div>
                    </div>

                    <!-- SEO Panel -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-search-plus me-2 text-primary"></i>
                                SEO Optimization
                                <span class="badge ms-2 badge-secondary" id="seoScore">Click to Analyze</span>
                                <button class="btn btn-sm btn-outline-primary ms-auto" onclick="AdZetaAI.analyzeSEO('post-editor')" title="Analyze SEO with AI">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </h6>
                        </div>
                        <div class="card-body">
                            <!-- Focus Keyword -->
                            <div class="mb-3">
                                <label class="form-label">
                                    Focus Keyword
                                    <button class="btn btn-sm btn-outline-primary ms-2"
                                            data-ai-action="suggest-keywords" data-ai-target="post-editor"
                                            title="AI keyword suggestions">
                                        <i class="fas fa-robot"></i>
                                    </button>
                                </label>
                                <input type="text" id="focusKeyword" class="form-control"
                                       placeholder="Enter your target keyword..."
                                       value="${this.state.currentPost.focus_keyword || ''}"
                                       onchange="AdZetaPostEditor.handleFocusKeywordChange(this)">
                            </div>

                            <!-- Meta Title -->
                            <div class="mb-3">
                                <label class="form-label">
                                    Meta Title <span class="text-muted" id="metaTitleCounter">(0/60)</span>
                                    <button class="btn btn-sm btn-outline-primary ms-2"
                                            data-ai-action="generate-meta-title" data-ai-target="post-editor"
                                            title="Generate AI meta title">
                                        <i class="fas fa-robot"></i>
                                    </button>
                                </label>
                                <input type="text" id="metaTitle" class="form-control" maxlength="60"
                                       placeholder="SEO title for search engines..."
                                       value="${this.state.currentPost.meta_title || ''}"
                                       onchange="AdZetaPostEditor.handleMetaTitleChange(this)">
                            </div>

                            <!-- Meta Description -->
                            <div class="mb-3">
                                <label class="form-label">
                                    Meta Description <span class="text-muted" id="metaDescCounter">(0/160)</span>
                                    <button class="btn btn-sm btn-outline-primary ms-2"
                                            data-ai-action="generate-meta" data-ai-target="post-editor"
                                            title="Generate AI meta description">
                                        <i class="fas fa-robot"></i>
                                    </button>
                                </label>
                                <textarea id="metaDescription" class="form-control" rows="3" maxlength="160"
                                          placeholder="SEO description for search engines..."
                                          onchange="AdZetaPostEditor.handleMetaDescriptionChange(this)">${this.state.currentPost.meta_description || ''}</textarea>
                            </div>

                            <!-- Meta Keywords -->
                            <div class="mb-3">
                                <label class="form-label">
                                    Meta Keywords
                                    <button class="btn btn-sm btn-outline-primary ms-2"
                                            data-ai-action="generate-tags" data-ai-target="post-editor"
                                            title="Generate AI keywords">
                                        <i class="fas fa-robot"></i>
                                    </button>
                                </label>
                                <input type="text" id="metaKeywords" class="form-control"
                                       placeholder="keyword1, keyword2, keyword3"
                                       value="${this.state.currentPost.meta_keywords || ''}"
                                       onchange="AdZetaPostEditor.handleMetaKeywordsChange(this)">
                                <div class="form-text">Comma-separated keywords for SEO (optional)</div>
                            </div>

                            <!-- SEO Analysis Results (Handled by AI Assistant) -->

                            <!-- Search Preview -->
                            <div class="search-preview p-3 bg-light rounded">
                                <div class="search-title" id="searchPreviewTitle">${this.state.currentPost.meta_title || this.state.currentPost.title || 'Your Post Title'}</div>
                                <div class="search-url text-success small" id="searchPreviewUrl">${this.getPostUrl()}</div>
                                <div class="search-description text-muted small" id="searchPreviewDesc">${this.state.currentPost.meta_description || this.state.currentPost.excerpt || 'Your post description...'}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Author & Categories -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-user-edit me-2 text-primary"></i>
                                Author & Categories
                            </h6>
                        </div>
                        <div class="card-body">
                            <!-- Author -->
                            <div class="mb-3">
                                <label class="form-label">Author</label>
                                <select id="postAuthor" class="form-select">
                                    <option value="">Loading authors...</option>
                                </select>
                                <div class="form-text">Select the author for this post</div>
                            </div>

                            <!-- Categories -->
                            <div class="mb-3">
                                <label class="form-label">Category</label>
                                <select id="postCategory" class="form-select">
                                    <option value="">Loading categories...</option>
                                </select>
                            </div>

                            <!-- Tags -->
                            <div class="mb-3">
                                <label class="form-label">Tags</label>
                                <input type="text" id="postTags" class="form-control"
                                       placeholder="Add tags (press Enter or comma to add)"
                                       value="${this.state.currentPost.tags.join(', ')}">
                                <div id="selectedTags" class="mt-2">
                                    ${this.renderTags()}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Editor Footer -->
            <div class="editor-footer" id="editorFooter">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <span id="autoSaveStatus" class="text-muted me-3">
                            <i class="fas fa-clock me-1"></i>
                            Auto-save enabled
                        </span>
                        <small class="text-muted">
                            <kbd>Ctrl</kbd> + <kbd>S</kbd> Save • <kbd>Ctrl</kbd> + <kbd>P</kbd> Preview
                        </small>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary" onclick="AdZetaPostEditor.hideEditor()">
                            <i class="fas fa-times me-1"></i>
                            Close
                        </button>
                        <button class="btn btn-outline-secondary" onclick="AdZetaPostEditor.saveDraft()">
                            <i class="fas fa-save me-1"></i>
                            Save Draft
                        </button>
                        <button class="btn btn-outline-primary" onclick="AdZetaPostEditor.preview()">
                            <i class="fas fa-eye me-1"></i>
                            Preview
                        </button>
                        <button class="btn btn-primary" onclick="AdZetaPostEditor.publish()">
                            <i class="fas fa-globe me-1"></i>
                            ${this.state.currentPost.status === 'published' ? 'Update' : 'Publish'}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Footer Toggle Button -->
            <button class="footer-toggle" id="footerToggle" onclick="AdZetaPostEditor.toggleFooter()">
                <i class="fas fa-chevron-up"></i>
            </button>
        `;

        // Bind editor events
        this.bindEditorEvents();

        // Bind template selection events
        this.bindTemplateEvents();

        // Load WordPress-inspired template selector
        setTimeout(() => {
            this.loadPostTemplateSelector();
        }, 500);

        // Bind AI generation events
        this.bindAIGenerationEvents();

        // Load authors, categories and initialize tags
        this.loadAuthors();
        this.loadCategories();
        this.initTagsInput();

        // Ensure WordPress-style tag display (input field clean, tags as badges only)
        this.ensureCleanTagInput();

        // Initialize footer state
        setTimeout(() => {
            this.initFooterState();
        }, 100);
    },

    // Bind editor-specific events
    bindEditorEvents() {
        // Title input
        const titleInput = document.getElementById('postTitle');
        if (titleInput) {
            titleInput.addEventListener('input', this.handleTitleChange.bind(this));
        }

        // Slug input
        const slugInput = document.getElementById('postSlug');
        if (slugInput) {
            slugInput.addEventListener('input', this.handleSlugChange.bind(this));
        }

        // Status change
        const statusSelect = document.getElementById('postStatus');
        if (statusSelect) {
            statusSelect.addEventListener('change', this.handleStatusChange.bind(this));
        }

        // Excerpt counter
        const excerptTextarea = document.getElementById('postExcerpt');
        if (excerptTextarea) {
            excerptTextarea.addEventListener('input', this.updateExcerptCounter.bind(this));
            this.updateExcerptCounter(); // Initial count
        }

        // Meta title counter
        const metaTitleInput = document.getElementById('metaTitle');
        if (metaTitleInput) {
            metaTitleInput.addEventListener('input', this.updateMetaTitleCounter.bind(this));
            this.updateMetaTitleCounter(); // Initial count
        }

        // Meta description counter
        const metaDescTextarea = document.getElementById('metaDescription');
        if (metaDescTextarea) {
            metaDescTextarea.addEventListener('input', this.updateMetaDescCounter.bind(this));
            this.updateMetaDescCounter(); // Initial count
        }

        // Focus keyword input
        const focusKeywordInput = document.getElementById('focusKeyword');
        if (focusKeywordInput) {
            focusKeywordInput.addEventListener('input', this.handleFocusKeywordChange.bind(this));
        }

        // SEO analysis is now manual only - no automatic analysis on page load
        console.log('✅ Post editor initialized - SEO analysis available on demand');
    },

    // Bind template selection events
    bindTemplateEvents() {
        // Template category filters
        const categoryButtons = document.querySelectorAll('.template-categories .btn');
        categoryButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();

                // Update active state
                categoryButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // Filter templates
                const category = button.getAttribute('data-category');
                this.filterTemplates(category);
            });
        });

        // Template selection
        const selectButtons = document.querySelectorAll('.select-template');
        selectButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const templateId = button.getAttribute('data-template');
                this.handleTemplateSelection(templateId);
            });
        });

        // Template preview
        const previewButtons = document.querySelectorAll('.preview-template');
        previewButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const previewUrl = button.getAttribute('data-preview');
                window.open(previewUrl, '_blank');
            });
        });
    },

    // Filter templates by category
    filterTemplates(category) {
        const templateCards = document.querySelectorAll('.template-card');
        templateCards.forEach(card => {
            const cardCategory = card.getAttribute('data-category');
            if (category === 'all' || cardCategory === category) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    },

    // Initialize Editor.js with local files
    initializeEditor() {
        console.log('Initializing editor with local files...');

        // Wait for all scripts to load
        setTimeout(() => {
            this.tryInitializeEditorJS();
        }, 1000);
    },

    // Try to initialize Editor.js
    tryInitializeEditorJS() {
        // Check if Editor.js is available
        if (typeof EditorJS === 'undefined') {
            console.warn('Editor.js not loaded, using fallback');
            this.showTextareaFallback();
            return;
        }

        console.log('Editor.js found, checking tools...');

        // Check available tools
        const availableTools = {};

        // Header tool with HTML preservation (Official Editor.js approach)
        if (typeof Header !== 'undefined') {
            // Extend Header tool with custom sanitization rules
            class HTMLPreservingHeader extends Header {
                /**
                 * Automatic sanitization rules for Header tool
                 * Based on official Editor.js documentation
                 */
                static get sanitize() {
                    return {
                        text: {
                            // Basic formatting tags
                            strong: true,  // Allow <strong> tags
                            b: true,       // Allow <b> tags
                            em: true,      // Allow <em> tags
                            i: true,       // Allow <i> tags
                            u: true,       // Allow <u> tags

                            // Span with attributes
                            span: {
                                style: true,  // Allow style attribute
                                class: true   // Allow class attribute
                            },

                            // Links with attributes
                            a: {
                                href: true,   // Allow href attribute
                                target: true, // Allow target attribute
                                rel: true     // Allow rel attribute
                            },

                            // Other formatting tags
                            code: true,    // Allow <code> tags
                            mark: {
                                style: true,
                                class: true
                            },
                            small: true,   // Allow <small> tags
                            sub: true,     // Allow <sub> tags
                            sup: true,     // Allow <sup> tags
                            del: true,     // Allow <del> tags
                            ins: true      // Allow <ins> tags
                        }
                    };
                }
            }

            availableTools.header = {
                class: HTMLPreservingHeader,
                config: {
                    placeholder: 'Enter a heading...',
                    levels: [2, 3, 4, 5, 6],
                    defaultLevel: 2
                }
            };
            console.log('✅ Header tool with HTML preservation (Official Editor.js approach)');
        }

        // List tool with HTML preservation (Official Editor.js approach)
        if (typeof List !== 'undefined') {
            // Extend List tool with custom sanitization rules
            class HTMLPreservingList extends List {
                /**
                 * Automatic sanitization rules for List tool
                 * Based on official Editor.js documentation
                 */
                static get sanitize() {
                    return {
                        items: {
                            // Basic formatting tags
                            strong: true,  // Allow <strong> tags
                            b: true,       // Allow <b> tags
                            em: true,      // Allow <em> tags
                            i: true,       // Allow <i> tags
                            u: true,       // Allow <u> tags

                            // Span with attributes
                            span: {
                                style: true,  // Allow style attribute
                                class: true   // Allow class attribute
                            },

                            // Links with attributes
                            a: {
                                href: true,   // Allow href attribute
                                target: true, // Allow target attribute
                                rel: true     // Allow rel attribute
                            },

                            // Other formatting tags
                            code: true,    // Allow <code> tags
                            mark: {
                                style: true,
                                class: true
                            },
                            small: true,   // Allow <small> tags
                            sub: true,     // Allow <sub> tags
                            sup: true,     // Allow <sup> tags
                            del: true,     // Allow <del> tags
                            ins: true      // Allow <ins> tags
                        }
                    };
                }
            }

            availableTools.list = {
                class: HTMLPreservingList,
                inlineToolbar: ['bold', 'italic', 'underline']
            };
            console.log('✅ List tool with HTML preservation (Official Editor.js approach)');
        }

        // Use Paragraph tool with enhanced sanitization (Official Editor.js approach)
        if (typeof Paragraph !== 'undefined') {
            // Create a wrapper class that extends Paragraph with custom sanitization
            class HTMLPreservingParagraph extends Paragraph {
                /**
                 * Automatic sanitization - Editor.js will extend this with inline tools
                 * Based on official documentation: https://editorjs.io/sanitize-saved-data/
                 */
                static get sanitize() {
                    const sanitizeConfig = {
                        text: {} // Only tags from enabled Inline Tools (Bold, Italic, Underline, etc.)
                    };
                    console.log('🔍 DEBUG: Paragraph tool sanitize config:', sanitizeConfig);
                    return sanitizeConfig;
                }
            }

            const paragraphConfig = {
                class: HTMLPreservingParagraph,
                inlineToolbar: ['bold', 'italic', 'underline', 'highlight'],
                config: {
                    preserveBlank: true
                }
            };

            availableTools.paragraph = paragraphConfig;
            console.log('🔍 DEBUG: Paragraph tool configuration:', paragraphConfig);
            console.log('🔍 DEBUG: Paragraph inlineToolbar:', paragraphConfig.inlineToolbar);
            console.log('✅ Paragraph tool with HTML preservation configured');
        }

        if (typeof Quote !== 'undefined') {
            availableTools.quote = {
                class: Quote,
                inlineToolbar: ['bold', 'italic', 'underline']
            };
            console.log('Quote tool available');
        }

        if (typeof Delimiter !== 'undefined') {
            availableTools.delimiter = {
                class: Delimiter
            };
            console.log('Delimiter tool available');
        }

        // Add image tool if available
        if (typeof ImageTool !== 'undefined') {
            availableTools.image = {
                class: ImageTool,
                config: {
                    endpoints: {
                        byFile: '/adzeta-admin/api/media/upload.php',
                        byUrl: '/adzeta-admin/api/media/upload-by-url.php'
                    },
                    field: 'image',
                    types: 'image/*',
                    captionPlaceholder: 'Enter image caption...',
                    buttonContent: 'Select an image',
                    additionalRequestHeaders: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                }
            };
            console.log('Image tool available');
        }

        // Add custom AdZeta tools for styled content
        if (typeof StyledCard !== 'undefined') {
            availableTools.styledCard = {
                class: StyledCard,
                config: {
                    placeholder: 'Enter styled content...'
                },
                shortcut: 'CMD+SHIFT+C'
            };
            console.log('Styled Card tool available');
        }

        if (typeof StatisticsTool !== 'undefined') {
            availableTools.statisticsTool = {
                class: StatisticsTool,
                config: {},
                shortcut: 'CMD+SHIFT+S'
            };
            console.log('Statistics tool available');
        }

        if (typeof CTATool !== 'undefined') {
            availableTools.ctaTool = {
                class: CTATool,
                config: {},
                shortcut: 'CMD+SHIFT+A'
            };
            console.log('CTA tool available');
        }

        // Add inline formatting tools
        // 🔍 DEBUG: Inline Tools Registration
        console.log('🔍 DEBUG: Registering inline tools...');

        if (typeof BoldTool !== 'undefined') {
            availableTools.bold = {
                class: BoldTool,
                shortcut: 'CMD+B'
            };
            console.log('✅ DEBUG: Bold inline tool available');
            console.log('🔍 DEBUG: BoldTool.sanitize:', BoldTool.sanitize);
        } else {
            console.error('❌ DEBUG: BoldTool not found!');
        }

        if (typeof ItalicTool !== 'undefined') {
            availableTools.italic = {
                class: ItalicTool,
                shortcut: 'CMD+I'
            };
            console.log('✅ DEBUG: Italic inline tool available');
            console.log('🔍 DEBUG: ItalicTool.sanitize:', ItalicTool.sanitize);
        } else {
            console.error('❌ DEBUG: ItalicTool not found!');
        }

        if (typeof UnderlineTool !== 'undefined') {
            availableTools.underline = {
                class: UnderlineTool,
                shortcut: 'CMD+U'
            };
            console.log('✅ DEBUG: Underline inline tool available');
            console.log('🔍 DEBUG: UnderlineTool.sanitize:', UnderlineTool.sanitize);
        } else {
            console.error('❌ DEBUG: UnderlineTool not found!');
        }

        // Add highlight tool
        if (typeof HighlightTool !== 'undefined') {
            availableTools.highlight = {
                class: HighlightTool,
                config: {
                    colors: [
                        { name: 'Pink', background: '#FF4081', color: 'white' },
                        { name: 'Lavender', background: '#E6D8F2', color: '#2B0B3A' },
                        { name: 'Purple', background: '#2B0B3A', color: 'white' },
                        { name: 'Light Grey', background: '#F5F5F5', color: '#2B0B3A' }
                    ]
                },
                shortcut: 'CMD+SHIFT+H'
            };
            console.log('✅ Highlight tool available');
        }

        this.destroyEditor(); // Clean up existing editor

        try {
            console.log('Creating Editor.js instance with tools:', Object.keys(availableTools));

            // 🔍 DEBUG: Determine initial data for Editor.js
            let initialData = {};

            console.log('🔍 DEBUG: Raw post data from database:');
            console.log('  - currentPost.content:', this.state.currentPost.content ? this.state.currentPost.content.substring(0, 200) + '...' : 'NULL');
            console.log('  - currentPost.content_blocks:', this.state.currentPost.content_blocks);
            console.log('  - content type:', typeof this.state.currentPost.content);
            console.log('  - content_blocks type:', typeof this.state.currentPost.content_blocks);

            if (this.state.currentPost.content_blocks &&
                typeof this.state.currentPost.content_blocks === 'object' &&
                this.state.currentPost.content_blocks.blocks &&
                this.state.currentPost.content_blocks.blocks.length > 0) {
                // Use existing Editor.js blocks (no decoding needed - fixed at source)
                console.log('✅ DEBUG: Using existing Editor.js blocks:', this.state.currentPost.content_blocks.blocks.length, 'blocks');
                console.log('🔍 DEBUG: Block data:', this.state.currentPost.content_blocks.blocks[0]);

                initialData = {
                    time: this.state.currentPost.content_blocks.time || Date.now(),
                    blocks: this.state.currentPost.content_blocks.blocks,
                    version: this.state.currentPost.content_blocks.version || "2.8.1"
                };

                console.log('🔍 DEBUG: Final initialData:', initialData);
            } else if (this.state.currentPost.content && this.state.currentPost.content.trim()) {
                // Convert HTML content to simple paragraph blocks
                console.log('🔄 DEBUG: Converting HTML content to simple paragraph blocks...');
                console.log('🔍 DEBUG: Raw HTML content:', this.state.currentPost.content);

                const blocks = this.createSimpleParagraphBlocks(this.state.currentPost.content);
                initialData = {
                    time: Date.now(),
                    blocks: blocks,
                    version: "2.8.1"
                };
                console.log('✅ DEBUG: Created', blocks.length, 'simple paragraph blocks');
                console.log('🔍 DEBUG: Final initialData for Editor.js:', initialData);
            } else {
                // Empty content
                initialData = { blocks: [] };
                console.log('📝 DEBUG: Starting with empty content');
            }

            console.log('🔍 DEBUG: Creating EditorJS instance with:');
            console.log('  - Tools:', Object.keys(availableTools));
            console.log('  - Initial data:', initialData);

            this.state.editor = new EditorJS({
                holder: 'editorjs',
                placeholder: 'Start writing your amazing content...',
                tools: availableTools,
                data: initialData,
                onChange: () => {
                    console.log('🔍 DEBUG: Editor content changed');
                    this.markAsChanged();
                    this.updateWordCount();
                },
                onReady: () => {
                    console.log('✅ DEBUG: Editor.js is ready!');

                    // Debug: Check if sanitizer is available
                    if (this.state.editor.sanitizer) {
                        console.log('🔍 DEBUG: Editor.js sanitizer available:', this.state.editor.sanitizer);
                    } else {
                        console.error('❌ DEBUG: Editor.js sanitizer NOT available!');
                    }

                    // Debug: Check current editor data
                    this.state.editor.save().then(outputData => {
                        console.log('🔍 DEBUG: Current editor data after ready:', outputData);
                        if (outputData.blocks && outputData.blocks.length > 0) {
                            console.log('🔍 DEBUG: First block data:', outputData.blocks[0]);
                            if (outputData.blocks[0].data && outputData.blocks[0].data.text) {
                                console.log('🔍 DEBUG: First block text content:', outputData.blocks[0].data.text);
                            }
                        }
                    }).catch(error => {
                        console.error('❌ DEBUG: Error getting editor data:', error);
                    });

                    this.updateWordCount();
                    // Suppress paste handler warnings only
                    this.suppressPasteWarnings();
                },
                // Standard Editor.js configuration
                minHeight: 300,
                logLevel: 'ERROR',
                defaultBlock: 'paragraph',
                autofocus: true
            });
        } catch (error) {
            console.error('Failed to initialize Editor.js:', error);
            this.showTextareaFallback();
        }
    },

    // Show textarea fallback if Editor.js fails
    showTextareaFallback() {
        const editorContainer = document.getElementById('editorjs');
        if (editorContainer) {
            editorContainer.innerHTML = `
                <div class="alert alert-warning mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Editor.js not available.</strong> Using basic text editor as fallback.
                </div>
                <textarea id="contentTextarea" class="form-control" rows="15"
                          placeholder="Start writing your content..."
                          style="min-height: 400px; border: none; resize: vertical; font-family: inherit; font-size: 16px; line-height: 1.6;">${this.state.currentPost.content || ''}</textarea>
            `;

            // Bind textarea events
            const textarea = document.getElementById('contentTextarea');
            if (textarea) {
                textarea.addEventListener('input', (e) => {
                    this.state.currentPost.content = e.target.value;
                    this.markAsChanged();
                    this.updateWordCountFromTextarea();
                });
            }
        }
    },

    // Update word count from textarea
    updateWordCountFromTextarea() {
        const content = this.state.currentPost.content || '';
        const wordCount = this.countWords(content);
        const readingTime = Math.ceil(wordCount / 200);

        const wordCountEl = document.getElementById('wordCount');
        const readingTimeEl = document.getElementById('readingTime');

        if (wordCountEl) wordCountEl.textContent = `${wordCount} words`;
        if (readingTimeEl) readingTimeEl.textContent = `${readingTime} min read`;
    },

    // Destroy Editor.js instance
    destroyEditor() {
        if (this.state.editor) {
            this.state.editor.destroy();
            this.state.editor = null;
        }
    },

    // Handle title change
    handleTitleChange(event) {
        this.state.currentPost.title = event.target.value;
        this.generateSlug();
        this.updateSearchPreview();
        this.markAsChanged();
    },

    // Handle slug change
    handleSlugChange(event) {
        this.state.currentPost.slug = event.target.value;
        this.updatePermalink();
        this.markAsChanged();
    },

    // Handle status change
    handleStatusChange(event) {
        this.state.currentPost.status = event.target.value;
        const publishDateContainer = document.getElementById('publishDateContainer');

        if (event.target.value === 'scheduled') {
            publishDateContainer.style.display = 'block';
        } else {
            publishDateContainer.style.display = 'none';
        }

        this.markAsChanged();
    },

    // Generate slug from title
    generateSlug(force = false) {
        if (this.state.currentPost.title && (!this.state.currentPost.slug || force)) {
            const slug = this.state.currentPost.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');

            this.state.currentPost.slug = slug;
            const slugInput = document.getElementById('postSlug');
            if (slugInput) {
                slugInput.value = slug;
            }
            this.updatePermalink();
            console.log('✅ Generated slug:', slug, 'for title:', this.state.currentPost.title);
        }
    },

    // Update permalink display
    updatePermalink() {
        const permalinkSpan = document.getElementById('postPermalink');
        if (permalinkSpan) {
            permalinkSpan.textContent = this.getPostUrl();
        }
        this.updateSearchPreview();
    },

    // Get post URL
    getPostUrl() {
        const baseUrl = window.location.origin;
        const slug = this.state.currentPost.slug || 'your-post-slug';
        return `${baseUrl}/blog/${slug}`;
    },

    // Update search preview
    updateSearchPreview() {
        const titleEl = document.getElementById('searchPreviewTitle');
        const urlEl = document.getElementById('searchPreviewUrl');
        const descEl = document.getElementById('searchPreviewDesc');

        if (titleEl) {
            titleEl.textContent = this.state.currentPost.meta_title || this.state.currentPost.title || 'Your Post Title';
        }
        if (urlEl) {
            urlEl.textContent = this.getPostUrl();
        }
        if (descEl) {
            descEl.textContent = this.state.currentPost.meta_description || this.state.currentPost.excerpt || 'Your post description...';
        }

        // SEO analysis is handled by AI Assistant
    },

    // Old SEO analysis methods removed - now handled by AI Assistant

    // Get content text for analysis
    getContentText() {
        if (this.state.editor) {
            // Get text from Editor.js blocks
            return this.extractTextFromBlocks(this.state.currentPost.content_blocks?.blocks || []);
        } else {
            // Get text from textarea
            return this.state.currentPost.content || '';
        }
    },

    // Check if content has headings
    hasHeadings() {
        if (this.state.editor && this.state.currentPost.content_blocks?.blocks) {
            return this.state.currentPost.content_blocks.blocks.some(block => block.type === 'header');
        }
        return false;
    },

    // Calculate readability score (simplified Flesch Reading Ease)
    calculateReadabilityScore(text) {
        if (!text) return 0;

        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
        const words = text.split(/\s+/).filter(w => w.length > 0).length;
        const syllables = this.countSyllables(text);

        if (sentences === 0 || words === 0) return 0;

        const avgSentenceLength = words / sentences;
        const avgSyllablesPerWord = syllables / words;

        const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
        return Math.max(0, Math.min(100, Math.round(score)));
    },

    // Count syllables in text (simplified)
    countSyllables(text) {
        return text.toLowerCase()
            .replace(/[^a-z]/g, '')
            .replace(/[aeiouy]+/g, 'a')
            .replace(/[^a]/g, '').length || 1;
    },

    // Old displaySEOAnalysis method removed - now handled by AI Assistant

    // Update excerpt counter
    updateExcerptCounter() {
        const textarea = document.getElementById('postExcerpt');
        const counter = document.getElementById('excerptCounter');

        if (textarea && counter) {
            const length = textarea.value.length;
            counter.textContent = `(${length}/150)`;
            counter.className = length > 150 ? 'text-danger' : 'text-muted';

            this.state.currentPost.excerpt = textarea.value;
            this.updateSearchPreview();
        }
    },

    // Update meta title counter
    updateMetaTitleCounter() {
        const input = document.getElementById('metaTitle');
        const counter = document.getElementById('metaTitleCounter');

        if (input && counter) {
            const length = input.value.length;
            counter.textContent = `(${length}/60)`;
            counter.className = length > 60 ? 'text-danger' : 'text-muted';

            this.state.currentPost.meta_title = input.value;
            this.updateSearchPreview();
        }
    },

    // Update meta description counter
    updateMetaDescCounter() {
        const textarea = document.getElementById('metaDescription');
        const counter = document.getElementById('metaDescCounter');

        if (textarea && counter) {
            const length = textarea.value.length;
            counter.textContent = `(${length}/160)`;
            counter.className = length > 160 ? 'text-danger' : 'text-muted';

            this.state.currentPost.meta_description = textarea.value;
            this.updateSearchPreview();
        }
    },

    // Update word count
    updateWordCount() {
        if (this.state.editor) {
            this.state.editor.save().then((outputData) => {
                const text = this.extractTextFromBlocks(outputData.blocks);
                const wordCount = this.countWords(text);
                const readingTime = Math.ceil(wordCount / 200);

                const wordCountEl = document.getElementById('wordCount');
                const readingTimeEl = document.getElementById('readingTime');

                if (wordCountEl) wordCountEl.textContent = `${wordCount} words`;
                if (readingTimeEl) readingTimeEl.textContent = `${readingTime} min read`;
            });
        }
    },

    // Extract text from Editor.js blocks
    extractTextFromBlocks(blocks) {
        if (!blocks || !Array.isArray(blocks)) return '';

        return blocks.map(block => {
            switch (block.type) {
                case 'header':
                case 'paragraph':
                    return block.data.text || '';
                case 'list':
                    return (block.data.items || []).join(' ');
                case 'quote':
                    return (block.data.text || '') + ' ' + (block.data.caption || '');
                default:
                    return '';
            }
        }).join(' ');
    },

    // Count words in text
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    },

    // Render tags
    renderTags() {
        return this.state.currentPost.tags.map(tag =>
            `<span class="badge bg-primary me-1 mb-1">${tag} <i class="fas fa-times ms-1" onclick="AdZetaPostEditor.removeTag('${tag}')"></i></span>`
        ).join('');
    },

    // Remove tag (WordPress style - keep input field clean)
    removeTag(tag) {
        this.state.currentPost.tags = this.state.currentPost.tags.filter(t => t !== tag);
        document.getElementById('selectedTags').innerHTML = this.renderTags();
        // DON'T sync tags to input - WordPress style keeps input clean
        this.markAsChanged();
    },

    // Load categories from API
    async loadCategories() {
        try {
            const response = await window.AdZetaApp.apiRequest('/categories');

            if (response.success) {
                const categorySelect = document.getElementById('postCategory');
                if (categorySelect) {
                    categorySelect.innerHTML = '<option value="">Select a category</option>';

                    response.categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;

                        // Select current category
                        if (this.state.currentPost.category_id == category.id) {
                            option.selected = true;
                        }

                        categorySelect.appendChild(option);
                    });

                    // Bind change event
                    categorySelect.addEventListener('change', (e) => {
                        this.state.currentPost.category_id = parseInt(e.target.value) || null;
                        this.markAsChanged();
                    });
                }
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            // Fallback to default categories
            const categorySelect = document.getElementById('postCategory');
            if (categorySelect) {
                categorySelect.innerHTML = `
                    <option value="">Select a category</option>
                    <option value="1">Technology</option>
                    <option value="2">Marketing</option>
                    <option value="3">Design</option>
                    <option value="4">Business</option>
                `;
            }
        }
    },

    // Load authors from API
    async loadAuthors() {
        try {
            const response = await window.AdZetaApp.apiRequest('/users');

            if (response.success) {
                const authorSelect = document.getElementById('postAuthor');
                if (authorSelect) {
                    authorSelect.innerHTML = '<option value="">Select an author</option>';

                    response.users.forEach(user => {
                        const option = document.createElement('option');
                        option.value = user.id;
                        option.textContent = user.name || `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.username;

                        // Select current author
                        if (this.state.currentPost.author_id == user.id) {
                            option.selected = true;
                        }

                        authorSelect.appendChild(option);
                    });

                    // Bind change event
                    authorSelect.addEventListener('change', (e) => {
                        this.state.currentPost.author_id = parseInt(e.target.value) || null;
                        this.markAsChanged();
                    });
                }
            }
        } catch (error) {
            console.error('Error loading authors:', error);
            // Fallback to default author
            const authorSelect = document.getElementById('postAuthor');
            if (authorSelect) {
                authorSelect.innerHTML = `
                    <option value="">Select an author</option>
                    <option value="1">AdZeta Team</option>
                `;
            }
        }
    },

    // Initialize WordPress-style tags input
    initTagsInput() {
        const tagsInput = document.getElementById('postTags');

        if (tagsInput) {
            // Clear input field on load - tags only show as badges
            tagsInput.value = '';
            tagsInput.placeholder = 'Add tags (press Enter or comma to add)';

            tagsInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    const tagText = e.target.value.trim();
                    if (tagText) {
                        this.addTagWordPressStyle(tagText);
                        e.target.value = '';
                    }
                }
            });

            tagsInput.addEventListener('blur', (e) => {
                const tagText = e.target.value.trim();
                if (tagText) {
                    this.addTagWordPressStyle(tagText);
                    e.target.value = '';
                }
            });
        }
    },

    // WordPress-style tag addition
    addTagWordPressStyle(tagText) {
        // Split by comma and process each tag
        const tags = tagText.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);

        tags.forEach(tag => {
            if (!this.state.currentPost.tags.includes(tag)) {
                this.state.currentPost.tags.push(tag);
            }
        });

        this.updateTagsDisplay();
        this.markAsChanged();
    },

    // WordPress-style tags display update
    updateTagsDisplay() {
        const selectedTagsContainer = document.getElementById('selectedTags');
        const tagsInput = document.getElementById('postTags');

        if (selectedTagsContainer) {
            selectedTagsContainer.innerHTML = this.renderTags();
        }

        // Keep input field clean (WordPress style - only for adding new tags)
        if (tagsInput && document.activeElement !== tagsInput) {
            tagsInput.value = '';
        }
    },

    // Add tag
    addTag(tagText) {
        if (!tagText || this.state.currentPost.tags.includes(tagText)) {
            return;
        }

        this.state.currentPost.tags.push(tagText);
        document.getElementById('selectedTags').innerHTML = this.renderTags();
        // DON'T sync tags to input - WordPress style keeps input clean for new tags only
        this.markAsChanged();
    },

    // WordPress-style: Input field stays clean, only shows badges
    // No need to sync tags back to input field

    // Ensure tag input field is always clean (WordPress style)
    ensureCleanTagInput() {
        const tagsInput = document.getElementById('postTags');
        if (tagsInput) {
            tagsInput.value = ''; // Always keep input clean
            tagsInput.placeholder = 'Add tags (press Enter or comma to add)';
        }

        // Update tags display to show existing tags as badges
        const selectedTagsContainer = document.getElementById('selectedTags');
        if (selectedTagsContainer) {
            selectedTagsContainer.innerHTML = this.renderTags();
        }
    },

    // Mark as changed
    markAsChanged() {
        this.state.hasChanges = true;
    },

    // Setup auto-save
    setupAutoSave() {
        this.state.autoSaveTimer = setInterval(() => {
            if (this.state.isEditing && this.state.hasChanges) {
                this.autoSave();
            }
        }, 30000); // Auto-save every 30 seconds
    },

    // Clear auto-save timer
    clearAutoSave() {
        if (this.state.autoSaveTimer) {
            clearInterval(this.state.autoSaveTimer);
            this.state.autoSaveTimer = null;
        }
    },

    // Auto-save
    async autoSave() {
        try {
            await this.savePost('draft', true);
            this.updateAutoSaveStatus('Auto-saved');
        } catch (error) {
            console.error('Auto-save failed:', error);
        }
    },

    // Update auto-save status
    updateAutoSaveStatus(message) {
        const statusEl = document.getElementById('autoSaveStatus');
        if (statusEl) {
            statusEl.innerHTML = `<i class="fas fa-check-circle text-success me-1"></i>${message} at ${new Date().toLocaleTimeString()}`;
        }
    },

    // Save draft
    async saveDraft() {
        await this.savePost('draft');
    },

    // Publish post
    async publish() {
        await this.savePost('published');
    },

    // Save post
    async savePost(status, isAutoSave = false) {
        try {
            // Get editor content
            let editorContent = '';
            let contentBlocks = null;

            if (this.state.editor) {
                try {
                    const outputData = await this.state.editor.save();
                    contentBlocks = outputData;

                    // Convert blocks to HTML for content field - PRESERVE HTML FORMATTING
                    if (outputData.blocks && outputData.blocks.length > 0) {
                        editorContent = outputData.blocks.map(block => {
                            switch (block.type) {
                                case 'paragraph':
                                    // UNESCAPE and preserve HTML formatting in paragraphs
                                    const paragraphText = this.unescapeHTML(block.data.text || '');
                                    return `<p>${paragraphText}</p>`;
                                case 'header':
                                    const level = block.data.level || 2;
                                    // UNESCAPE and preserve HTML formatting in headers
                                    const headerText = this.unescapeHTML(block.data.text || '');
                                    return `<h${level}>${headerText}</h${level}>`;
                                case 'list':
                                    const tag = block.data.style === 'ordered' ? 'ol' : 'ul';
                                    // UNESCAPE and preserve HTML formatting in list items
                                    const items = (block.data.items || []).map(item => {
                                        const unescapedItem = this.unescapeHTML(item);
                                        return `<li>${unescapedItem}</li>`;
                                    }).join('');
                                    return `<${tag}>${items}</${tag}>`;
                                case 'quote':
                                    // PRESERVE HTML formatting in quotes
                                    return `<blockquote><p>${block.data.text || ''}</p><cite>${block.data.caption || ''}</cite></blockquote>`;
                                case 'delimiter':
                                    return '<hr>';
                                case 'styledCard':
                                    // Handle custom styled cards
                                    return `<div class="styled-card" style="background-color: ${block.data.backgroundColor || '#E6D8F2'}; color: ${block.data.textColor || '#2B0B3A'}; border-left: 4px solid ${block.data.borderColor || '#FF4081'}; padding: 1rem; margin: 1rem 0; border-radius: 0.5rem;">${block.data.content || ''}</div>`;
                                default:
                                    // For any other block types, try to preserve content
                                    if (block.data && block.data.text) {
                                        return `<div class="custom-block custom-block-${block.type}">${block.data.text}</div>`;
                                    }
                                    return '';
                            }
                        }).join('\n');

                        console.log('✅ Converted Editor.js blocks to HTML with preserved formatting');
                        console.log('📝 HTML content preview:', editorContent.substring(0, 200) + '...');
                    }
                } catch (error) {
                    console.error('Error saving editor content:', error);
                }
            } else {
                // Fallback to textarea content
                const textarea = document.getElementById('contentTextarea');
                if (textarea) {
                    editorContent = textarea.value;
                }
            }

            // Collect form data
            this.collectFormData();

            // Ensure tags array exists (but don't add fake tags)
            if (!this.state.currentPost.tags) {
                this.state.currentPost.tags = [];
            }

            // Set status
            this.state.currentPost.status = status;

            // Prepare post data for API - only send non-empty fields
            const postData = {
                title: this.state.currentPost.title || 'Untitled Post',
                content: editorContent,
                status: status
            };

            // Add optional fields only if they have values
            if (this.state.currentPost.slug) postData.slug = this.state.currentPost.slug;
            if (contentBlocks) {
                // Ensure content_blocks is sent as JSON string
                postData.content_blocks = typeof contentBlocks === 'object' ? JSON.stringify(contentBlocks) : contentBlocks;
                console.log('🔍 DEBUG: content_blocks prepared for API:', typeof postData.content_blocks, postData.content_blocks.substring(0, 100) + '...');
            }
            if (this.state.currentPost.excerpt) postData.excerpt = this.state.currentPost.excerpt;
            if (this.state.currentPost.meta_title) postData.meta_title = this.state.currentPost.meta_title;
            if (this.state.currentPost.meta_description) postData.meta_description = this.state.currentPost.meta_description;
            if (this.state.currentPost.meta_keywords) postData.meta_keywords = this.state.currentPost.meta_keywords;
            if (this.state.currentPost.focus_keyword) postData.focus_keyword = this.state.currentPost.focus_keyword;
            if (this.state.currentPost.canonical_url) postData.canonical_url = this.state.currentPost.canonical_url;
            if (this.state.currentPost.og_image) postData.og_image = this.state.currentPost.og_image;
            if (this.state.currentPost.og_title) postData.og_title = this.state.currentPost.og_title;
            if (this.state.currentPost.og_description) postData.og_description = this.state.currentPost.og_description;
            if (this.state.currentPost.featured_image) postData.featured_image = this.state.currentPost.featured_image;
            if (this.state.currentPost.category_id) postData.category_id = parseInt(this.state.currentPost.category_id);
            // Clean and deduplicate tags before sending
            if (this.state.currentPost.tags && this.state.currentPost.tags.length > 0) {
                const cleanTags = [...new Set(this.state.currentPost.tags)]
                    .filter(tag => tag && tag.trim().length > 0)
                    .map(tag => tag.trim());
                postData.tags = cleanTags;
            }

            if (this.state.currentPost.noindex) postData.noindex = this.state.currentPost.noindex;
            if (this.state.currentPost.nofollow) postData.nofollow = this.state.currentPost.nofollow;
            if (this.state.currentPost.template) postData.template = this.state.currentPost.template;
            if (this.state.currentPost.author_id) postData.author_id = this.state.currentPost.author_id;

            console.log('Final postData being sent:', postData);
            console.log('Tags in postData:', postData.tags);
            console.log('Post ID for API call:', this.state.currentPost.id);
            console.log('API endpoint will be:', this.state.currentPost.id ? `/posts/${this.state.currentPost.id}` : '/posts');

            // Make API call
            let response;
            if (this.state.currentPost.id) {
                // Update existing post
                response = await window.AdZetaApp.apiRequest(`/posts/${this.state.currentPost.id}`, {
                    method: 'PUT',
                    body: JSON.stringify(postData)
                });
            } else {
                // Create new post
                response = await window.AdZetaApp.apiRequest('/posts', {
                    method: 'POST',
                    body: JSON.stringify(postData)
                });
            }

            if (response.success) {
                // Update post ID if it's a new post
                if (!this.state.currentPost.id && response.post) {
                    this.state.currentPost.id = response.post.id;
                }

                if (!isAutoSave) {
                    window.AdZetaApp.showNotification(
                        status === 'published' ? 'Post published successfully!' : 'Post saved as draft',
                        'success'
                    );
                }

                this.state.hasChanges = false;
                this.state.lastSaved = new Date();

                return { success: true };
            } else {
                throw new Error(response.message || 'Failed to save post');
            }
        } catch (error) {
            console.error('Error saving post:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack,
                postId: this.state.currentPost.id,
                isAutoSave: isAutoSave,
                currentPostState: this.state.currentPost
            });

            if (!isAutoSave) {
                window.AdZetaApp.showNotification('Failed to save post: ' + error.message, 'danger');
            }
            throw error;
        }
    },

    // Collect form data
    collectFormData() {
        const titleInput = document.getElementById('postTitle');
        const slugInput = document.getElementById('postSlug');
        const excerptTextarea = document.getElementById('postExcerpt');
        const metaTitleInput = document.getElementById('metaTitle');
        const metaDescTextarea = document.getElementById('metaDescription');
        const focusKeywordInput = document.getElementById('focusKeyword');
        const tagsInput = document.getElementById('postTags');

        if (titleInput) this.state.currentPost.title = titleInput.value;
        if (slugInput) this.state.currentPost.slug = slugInput.value;
        if (excerptTextarea) this.state.currentPost.excerpt = excerptTextarea.value;
        if (metaTitleInput) this.state.currentPost.meta_title = metaTitleInput.value;
        if (metaDescTextarea) this.state.currentPost.meta_description = metaDescTextarea.value;
        if (focusKeywordInput) this.state.currentPost.focus_keyword = focusKeywordInput.value;

        // WordPress-style: Tags are stored in state, NOT in input field
        // Input field is only for adding new tags

        // Ensure tags array exists
        if (!this.state.currentPost.tags) {
            this.state.currentPost.tags = [];
        }

        // Process any remaining text in input field
        if (tagsInput && tagsInput.value.trim()) {
            const inputText = tagsInput.value.trim();
            this.addTagWordPressStyle(inputText);
            tagsInput.value = ''; // Clear input after processing
        }

        // Clean up tags array
        this.state.currentPost.tags = [...new Set(this.state.currentPost.tags)]
            .filter(tag => tag && tag.trim().length > 0)
            .map(tag => tag.trim());

        console.log('Final tags for save:', this.state.currentPost.tags);
    },

    // Preview post
    async preview() {
        try {
            // Get current content
            let content = '';
            if (this.state.editor) {
                const outputData = await this.state.editor.save();
                content = this.convertBlocksToHTML(outputData.blocks || []);
            } else {
                const textarea = document.getElementById('contentTextarea');
                if (textarea) {
                    content = textarea.value;
                }
            }

            // Create preview HTML
            const previewHTML = this.generatePreviewHTML(content);

            // Open preview in new window
            const previewWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes');
            previewWindow.document.write(previewHTML);
            previewWindow.document.close();

            window.AdZetaApp.showNotification('Preview opened in new window', 'success');
        } catch (error) {
            console.error('Error previewing post:', error);
            window.AdZetaApp.showNotification('Failed to preview post: ' + error.message, 'danger');
        }
    },

    // Convert Editor.js blocks to HTML
    convertBlocksToHTML(blocks) {
        return blocks.map(block => {
            switch (block.type) {
                case 'paragraph':
                    return `<p>${block.data.text || ''}</p>`;
                case 'header':
                    const level = block.data.level || 2;
                    return `<h${level}>${block.data.text || ''}</h${level}>`;
                case 'list':
                    const tag = block.data.style === 'ordered' ? 'ol' : 'ul';
                    const items = (block.data.items || []).map(item => `<li>${item}</li>`).join('');
                    return `<${tag}>${items}</${tag}>`;
                case 'quote':
                    return `<blockquote class="blockquote"><p>${block.data.text || ''}</p><footer class="blockquote-footer">${block.data.caption || ''}</footer></blockquote>`;
                case 'delimiter':
                    return '<hr class="my-4">';
                default:
                    return '';
            }
        }).join('\n');
    },

    // Generate preview HTML
    generatePreviewHTML(content) {
        const title = this.state.currentPost.title || 'Untitled Post';
        const excerpt = this.state.currentPost.excerpt || '';
        const metaTitle = this.state.currentPost.meta_title || title;
        const metaDescription = this.state.currentPost.meta_description || excerpt;

        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${metaTitle}</title>
    <meta name="description" content="${metaDescription}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; line-height: 1.6; }
        .preview-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem 0; }
        .preview-badge { background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.875rem; }
        .content { max-width: 800px; margin: 0 auto; padding: 2rem 1rem; }
        .blockquote { border-left: 4px solid #667eea; padding-left: 1rem; margin: 1.5rem 0; }
        h1, h2, h3, h4, h5, h6 { color: #1f2937; margin-top: 2rem; margin-bottom: 1rem; }
        p { margin-bottom: 1.5rem; color: #374151; }
        hr { margin: 2rem 0; border-color: #e5e7eb; }
    </style>
</head>
<body>
    <div class="preview-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <span class="preview-badge">Preview Mode</span>
                    <h1 class="mt-3 mb-3">${title}</h1>
                    ${excerpt ? `<p class="lead">${excerpt}</p>` : ''}
                    <small>Published on ${new Date().toLocaleDateString()}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        ${content || '<p class="text-muted">No content available.</p>'}
    </div>

    <div class="text-center py-4">
        <small class="text-muted">This is a preview. The actual post may look different on your website.</small>
    </div>
</body>
</html>`;
    },

    // Toggle fullscreen
    toggleFullscreen() {
        const editorContainer = document.getElementById('editorjs');
        const fullscreenBtn = document.querySelector('button[onclick="AdZetaPostEditor.toggleFullscreen()"]');

        if (!editorContainer) {
            console.error('❌ Editor container #editorjs not found');
            return;
        }

        const isFullscreen = document.body.classList.contains('editor-fullscreen-active');

        if (isFullscreen) {
            // Exit fullscreen
            this.exitFullscreen(fullscreenBtn);
        } else {
            // Enter fullscreen
            this.enterFullscreen(editorContainer, fullscreenBtn);
        }
    },

    // Enter fullscreen mode - SIMPLE APPROACH
    enterFullscreen(editorContainer, fullscreenBtn) {
        console.log('✅ Entering fullscreen mode...');

        // Create fullscreen overlay
        const fullscreenOverlay = document.createElement('div');
        fullscreenOverlay.id = 'fullscreen-editor-overlay';
        fullscreenOverlay.className = 'fullscreen-editor-overlay';

        // Create floating toolbar
        const floatingToolbar = document.createElement('div');
        floatingToolbar.className = 'fullscreen-floating-toolbar';
        floatingToolbar.innerHTML = `
            <div class="d-flex align-items-center gap-2">
                <span class="text-muted small">Fullscreen Editor</span>
                <div class="d-flex gap-1">
                    <button class="btn btn-sm btn-outline-secondary" onclick="AdZetaPostEditor.showAIGenerationModal()" title="Generate content with AI">
                        <i class="fas fa-robot"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="AdZetaPostEditor.toggleSourceView()" title="View HTML source">
                        <i class="fas fa-code"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="AdZetaPostEditor.toggleFullscreen()" title="Exit fullscreen">
                        <i class="fas fa-compress"></i>
                    </button>
                </div>
            </div>
        `;

        // Create fullscreen content area
        const contentArea = document.createElement('div');
        contentArea.className = 'fullscreen-content-area';

        // Store original parent and position
        this.originalParent = editorContainer.parentNode;
        this.originalNextSibling = editorContainer.nextSibling;

        // Move the EXISTING editor container to fullscreen (don't clone!)
        contentArea.appendChild(editorContainer);

        // Assemble fullscreen overlay
        fullscreenOverlay.appendChild(floatingToolbar);
        fullscreenOverlay.appendChild(contentArea);

        // Add to body
        document.body.appendChild(fullscreenOverlay);
        document.body.classList.add('editor-fullscreen-active');
        document.body.style.overflow = 'hidden';

        // Update button
        if (fullscreenBtn) {
            fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
            fullscreenBtn.title = 'Exit fullscreen';
        }

        console.log('✅ Entered fullscreen mode - moved existing editor');
    },

    // Generate complete post with AI (NEW COHESIVE WORKFLOW)
    async generateCompletePostWithAI() {
        console.log('🤖 Starting complete post generation with AI...');

        // Check if called from modal or inline
        const isModal = document.getElementById('aiModalTopic');

        const topicInput = isModal ?
            document.getElementById('aiModalTopic') :
            document.getElementById('aiTopicInput');
        const generateBtn = document.getElementById('aiModalGenerate');
        const statusDiv = document.getElementById('aiModalStatus');
        const statusText = document.getElementById('aiModalStatusText');

        if (!topicInput || !topicInput.value.trim()) {
            window.AdZetaApp.showNotification('Please enter a topic for AI generation', 'warning');
            return;
        }

        const topic = topicInput.value.trim();
        const selectedTemplate = isModal ?
            document.getElementById('aiModalTemplate')?.value || 'professional-article' :
            this.state.currentPost.template || 'professional-article';

        // Get AI options from modal if available
        const temperature = isModal ?
            parseFloat(document.getElementById('aiModalTemperature')?.value || '0.7') : 0.7;
        const maxTokens = isModal ?
            parseInt(document.getElementById('aiModalLength')?.value || '2048') : 2048;

        try {
            // Update UI
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Complete Post...';
            statusDiv.style.display = 'block';
            statusText.textContent = 'Generating complete post with all SEO data...';

            console.log('📤 Sending complete post generation request...');

            // Call the enhanced API endpoint for complete post generation
            const response = await window.AdZetaApp.apiRequest('/ai/generate-complete-post', {
                method: 'POST',
                body: JSON.stringify({
                    topic: topic,
                    template: selectedTemplate,
                    options: {
                        temperature: temperature,
                        maxOutputTokens: maxTokens,
                        generateSEO: true,
                        generateTags: true,
                        generateKeywords: true,
                        generateSocialMedia: true
                    }
                })
            });

            console.log('📥 Complete post generation response:', response);

            if (response.success && response.post_data) {
                const postData = response.post_data;

                // Populate ALL form fields with generated content
                this.populateCompleteFormWithAIContent(postData);

                // Show success message
                window.AdZetaApp.showNotification(
                    `🎉 Complete post generated for "${topic}"! All fields populated including SEO, tags, and social media.`,
                    'success'
                );

                // Clear the topic input
                topicInput.value = '';

                // Close modal
                if (isModal) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('aiGenerationModal'));
                    if (modal) {
                        modal.hide();
                    }
                }

            } else {
                throw new Error(response.error || 'Failed to generate complete post');
            }

        } catch (error) {
            console.error('Complete post generation error:', error);
            window.AdZetaApp.showNotification(
                'Failed to generate complete post: ' + error.message,
                'danger'
            );
        } finally {
            // Reset button state
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Generate Complete Post';
            statusDiv.style.display = 'none';
        }
    },

    // Exit fullscreen mode - SIMPLE APPROACH
    exitFullscreen(fullscreenBtn) {
        console.log('✅ Exiting fullscreen mode...');

        // Get the editor container from fullscreen
        const editorContainer = document.getElementById('editorjs');

        // Move editor back to its original position
        if (this.originalParent && editorContainer) {
            if (this.originalNextSibling) {
                this.originalParent.insertBefore(editorContainer, this.originalNextSibling);
            } else {
                this.originalParent.appendChild(editorContainer);
            }
        }

        // Remove fullscreen overlay
        const overlay = document.getElementById('fullscreen-editor-overlay');
        if (overlay) {
            overlay.remove();
        }

        // Reset body
        document.body.classList.remove('editor-fullscreen-active');
        document.body.style.overflow = '';

        // Update button
        if (fullscreenBtn) {
            fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
            fullscreenBtn.title = 'Toggle fullscreen';
        }

        // Clean up references
        this.originalParent = null;
        this.originalNextSibling = null;

        console.log('✅ Exited fullscreen mode - moved editor back');
    },



    // Show AI Generation Modal
    showAIGenerationModal() {
        const modalHTML = `
            <div class="modal fade" id="aiGenerationModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-robot me-2 text-primary"></i>
                                AI Content Generation
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>Using your existing Gemini AI integration</strong><br>
                                AI will generate content optimized for the selected template.
                            </div>

                            <div class="mb-3">
                                <label for="aiModalTopic" class="form-label">Topic</label>
                                <input type="text"
                                       id="aiModalTopic"
                                       class="form-control"
                                       placeholder="e.g., 'Value-based bidding strategies for e-commerce'">
                                <div class="form-text">Enter the main topic for your blog post</div>
                            </div>

                            <div class="mb-3">
                                <label for="aiModalTemplate" class="form-label">Template</label>
                                <select id="aiModalTemplate" class="form-select">
                                    <option value="professional-article">Professional Article</option>
                                    <option value="modern-magazine">Modern Magazine</option>
                                    <option value="minimal-clean">Minimal Clean</option>
                                    <option value="case-study">Case Study</option>
                                </select>
                                <div class="form-text">Choose the template style for content generation</div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <label for="aiModalTemperature" class="form-label">Creativity Level</label>
                                    <select id="aiModalTemperature" class="form-select">
                                        <option value="0.3">Conservative (0.3)</option>
                                        <option value="0.7" selected>Balanced (0.7)</option>
                                        <option value="0.9">Creative (0.9)</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="aiModalLength" class="form-label">Content Length</label>
                                    <select id="aiModalLength" class="form-select">
                                        <option value="1024">Short (1024 tokens)</option>
                                        <option value="2048" selected>Medium (2048 tokens)</option>
                                        <option value="4096">Long (4096 tokens)</option>
                                    </select>
                                </div>
                            </div>

                            <div id="aiModalStatus" class="mt-3" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-spinner fa-spin me-2"></i>
                                    <span id="aiModalStatusText">Generating content with AI...</span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-outline-primary" id="aiModalGenerateContent">
                                <i class="fas fa-edit me-2"></i>
                                Generate Content Only
                            </button>
                            <button type="button" class="btn btn-primary" id="aiModalGenerate">
                                <i class="fas fa-magic me-2"></i>
                                Generate Complete Post
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('aiGenerationModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Set current template
        const templateSelect = document.getElementById('aiModalTemplate');
        if (templateSelect) {
            templateSelect.value = this.state.currentPost.template || 'professional-article';
        }

        // Bind events
        document.getElementById('aiModalGenerate').addEventListener('click', () => {
            this.generateCompletePostWithAI();
        });

        document.getElementById('aiModalGenerateContent').addEventListener('click', () => {
            this.generateContentWithAI();
        });

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('aiGenerationModal'));
        modal.show();
    },

    // Toggle Source View
    toggleSourceView() {
        if (this.state.sourceViewActive) {
            this.hideSourceView();
        } else {
            this.showSourceView();
        }
    },

    // Show HTML source view
    async showSourceView() {
        try {
            // Get content from Editor.js
            const editorData = await this.state.editor.save();

            // Convert to HTML
            const htmlContent = this.convertEditorDataToHTML(editorData);

            // Create source view
            const editorContainer = document.getElementById('editorjs');
            const sourceContainer = document.createElement('div');
            sourceContainer.id = 'sourceView';
            sourceContainer.innerHTML = `
                <div class="source-view-header">
                    <small class="text-muted">
                        <i class="fas fa-code me-1"></i>
                        HTML Source View - Edit carefully to avoid breaking content structure
                    </small>
                </div>
                <textarea id="sourceEditor" class="form-control" style="font-family: 'Courier New', monospace; min-height: 400px; font-size: 14px;">${htmlContent}</textarea>
            `;

            // Hide editor, show source
            editorContainer.style.display = 'none';
            editorContainer.parentNode.appendChild(sourceContainer);

            // Update button
            const sourceBtn = document.querySelector('button[onclick="AdZetaPostEditor.toggleSourceView()"]');
            if (sourceBtn) {
                sourceBtn.innerHTML = '<i class="fas fa-edit"></i>';
                sourceBtn.title = 'Switch to visual editor';
                sourceBtn.classList.remove('btn-outline-secondary');
                sourceBtn.classList.add('btn-warning');
            }

            this.state.sourceViewActive = true;

        } catch (error) {
            console.error('Error showing source view:', error);
            window.AdZetaApp.showNotification('Failed to show source view', 'danger');
        }
    },

    // Hide source view
    async hideSourceView() {
        const sourceContainer = document.getElementById('sourceView');
        const editorContainer = document.getElementById('editorjs');
        const sourceEditor = document.getElementById('sourceEditor');

        if (sourceContainer && sourceEditor) {
            // Get modified HTML
            const modifiedHTML = sourceEditor.value;

            // Convert back to Editor.js format using paste mechanism
            try {
                await this.convertHTMLToEditorBlocks(modifiedHTML);
            } catch (error) {
                console.error('Error converting HTML back to editor:', error);
                window.AdZetaApp.showNotification('Some HTML changes may not be preserved', 'warning');
            }

            // Remove source view
            sourceContainer.remove();
        }

        // Show editor
        if (editorContainer) {
            editorContainer.style.display = 'block';
        }

        // Update button
        const sourceBtn = document.querySelector('button[onclick="AdZetaPostEditor.toggleSourceView()"]');
        if (sourceBtn) {
            sourceBtn.innerHTML = '<i class="fas fa-code"></i>';
            sourceBtn.title = 'View HTML source';
            sourceBtn.classList.remove('btn-warning');
            sourceBtn.classList.add('btn-outline-secondary');
        }

        this.state.sourceViewActive = false;
    },

    // Convert Editor.js data to HTML
    convertEditorDataToHTML(editorData) {
        if (!editorData || !editorData.blocks) {
            return '';
        }

        let html = '';

        console.log('🔄 Converting Editor.js data to HTML:', editorData.blocks.length, 'blocks');
        console.log('📊 Block types:', editorData.blocks.map(b => b.type));

        editorData.blocks.forEach(block => {
            switch (block.type) {
                case 'paragraph':
                    html += `<p>${block.data.text || ''}</p>\n`;
                    break;
                case 'header':
                    const level = block.data.level || 2;
                    html += `<h${level}>${block.data.text || ''}</h${level}>\n`;
                    break;
                case 'list':
                    const listType = block.data.style === 'ordered' ? 'ol' : 'ul';
                    html += `<${listType}>\n`;
                    (block.data.items || []).forEach(item => {
                        html += `  <li>${item}</li>\n`;
                    });
                    html += `</${listType}>\n`;
                    break;
                case 'quote':
                    html += `<blockquote>${block.data.text || ''}</blockquote>\n`;
                    break;
                case 'code':
                    html += `<pre><code>${block.data.code || ''}</code></pre>\n`;
                    break;
                case 'delimiter':
                    html += `<hr>\n`;
                    break;
                case 'image':
                    if (block.data.file && block.data.file.url) {
                        html += `<img src="${block.data.file.url}" alt="${block.data.caption || ''}" style="max-width: 100%;">\n`;
                        if (block.data.caption) {
                            html += `<p><em>${block.data.caption}</em></p>\n`;
                        }
                    }
                    break;

                // Custom AdZeta blocks
                case 'styledCard':
                    const cardData = block.data;
                    html += `<div class="styled-card" style="background: ${cardData.backgroundColor || '#E6D8F2'}; color: ${cardData.textColor || '#2B0B3A'}; border-left: 4px solid ${cardData.borderColor || '#FF4081'}; border-radius: 12px; padding: 24px; margin: 16px 0; box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);">${cardData.content || ''}</div>\n`;
                    break;

                case 'statisticsTool':
                    const statData = block.data;
                    html += `<div class="statistics-display" style="text-align: center; padding: 24px; background: #F5F5F5; border-radius: 12px; margin: 16px 0; border: 2px solid ${statData.color || '#FF4081'};"><div class="stat-value" style="font-size: 3rem; font-weight: 700; color: ${statData.color || '#FF4081'}; line-height: 1; margin-bottom: 8px;">${statData.value || '100'}<span style="font-size: 2rem; margin-left: 4px;">${statData.unit || '%'}</span></div><div class="stat-label" style="font-size: 1rem; color: #2B0B3A; font-weight: 500;">${statData.label || 'Statistic'}</div></div>\n`;
                    break;

                case 'ctaTool':
                    const ctaData = block.data;
                    let ctaBackground = '';
                    let ctaTextColor = 'white';
                    let ctaDescColor = '#E6D8F2';

                    switch (ctaData.style || 'gradient') {
                        case 'gradient':
                            ctaBackground = 'background: linear-gradient(135deg, #2B0B3A, #FF4081);';
                            break;
                        case 'solid-purple':
                            ctaBackground = 'background: #2B0B3A;';
                            break;
                        case 'solid-pink':
                            ctaBackground = 'background: #FF4081;';
                            break;
                        case 'outline':
                            ctaBackground = 'background: white; border: 2px solid #FF4081;';
                            ctaTextColor = '#2B0B3A';
                            ctaDescColor = '#666';
                            break;
                    }

                    html += `<div class="cta-block" style="${ctaBackground} border-radius: 16px; padding: 48px; margin: 32px 0; text-align: ${ctaData.alignment || 'center'}; box-shadow: 0 8px 32px rgba(43, 11, 58, 0.2);"><h3 style="color: ${ctaTextColor}; font-size: 2rem; margin-bottom: 16px; margin-top: 0;">${ctaData.title || 'Ready to Get Started?'}</h3><p style="color: ${ctaDescColor}; font-size: 1.2rem; margin-bottom: 24px; max-width: 600px; margin-left: auto; margin-right: auto;">${ctaData.description || 'Transform your marketing today'}</p><a href="${ctaData.buttonUrl || '/demo'}" class="cta-button" style="display: inline-block; background: white; color: #2B0B3A; padding: 16px 32px; border-radius: 8px; text-decoration: none; font-weight: 700; font-size: 1.1rem; box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);">${ctaData.buttonText || 'Start Free Trial'}</a></div>\n`;
                    break;

                default:
                    // Handle unknown blocks as raw data
                    html += `<!-- ${block.type}: ${JSON.stringify(block.data)} -->\n`;
            }
        });

        return html;
    },

    // Setup keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            if (!this.state.isEditing) return;

            // Ctrl+S or Cmd+S - Save
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault();
                this.saveDraft();
            }

            // Ctrl+P or Cmd+P - Preview
            if ((event.ctrlKey || event.metaKey) && event.key === 'p') {
                event.preventDefault();
                this.preview();
            }
        });
    },

    // Setup fullscreen handler
    setupFullscreenHandler() {
        document.addEventListener('keydown', (e) => {
            // ESC key to exit fullscreen
            if (e.key === 'Escape') {
                const editorCard = document.querySelector('.fullscreen-editor');
                if (editorCard) {
                    e.preventDefault();
                    this.toggleFullscreen();
                    console.log('✅ Exited fullscreen via ESC key');
                }
            }
        });
    },

    // Suppress paste handler warnings
    suppressPasteWarnings() {
        // Override console.warn to filter out paste handler warnings
        const originalWarn = console.warn;
        console.warn = function(...args) {
            const message = args.join(' ');
            if (message.includes('Paste handler') && message.includes('is skipped')) {
                // Suppress paste handler conflict warnings
                return;
            }
            originalWarn.apply(console, args);
        };

        console.log('✅ Paste handler warnings suppressed for better UX');
    },

    // Removed aggressive toolbar manipulation - let Editor.js work naturally

    // Handle before unload
    handleBeforeUnload(event) {
        if (this.state.isEditing && this.state.hasChanges) {
            event.preventDefault();
            event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            return event.returnValue;
        }
    },

    // Render template selection
    renderTemplateSelection() {
        console.log('🎨 Rendering template selection section...');
        console.log('🔍 Checking window.AdZetaTemplates:', window.AdZetaTemplates);

        if (!window.AdZetaTemplates) {
            console.error('❌ window.AdZetaTemplates not found!');
            return '';
        }

        console.log('✅ window.AdZetaTemplates found, rendering template section...');

        const currentTemplate = this.state.currentPost.template || 'professional-article';
        const isCollapsed = this.state.currentPost.id ? 'collapsed' : '';

        return `
            <div class="card mb-4" id="templateSelectionCard">
                <div class="card-header">
                    <h6 class="mb-0">
                        <button class="btn btn-link p-0 text-decoration-none ${isCollapsed}"
                                type="button" data-bs-toggle="collapse"
                                data-bs-target="#templateCollapse"
                                aria-expanded="${!this.state.currentPost.id}">
                            <i class="fas fa-palette me-2 text-primary"></i>
                            Template: ${currentTemplate}
                            <i class="fas fa-chevron-down ms-2"></i>
                        </button>
                    </h6>
                </div>
                <div class="collapse ${isCollapsed ? '' : 'show'}" id="templateCollapse">
                    <div class="card-body">
                        <div class="alert alert-info mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-palette fa-2x text-primary me-3"></i>
                                <div>
                                    <h6 class="mb-1">WordPress-Inspired Templates</h6>
                                    <p class="mb-0">Choose a template to define how this post will look on the frontend.</p>
                                </div>
                            </div>
                        </div>
                        <div id="postTemplateSelector">
                            <!-- Template selector will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // Load WordPress-inspired template selector for posts
    async loadPostTemplateSelector() {
        console.log('🎯 Loading post template selector...');
        const container = document.getElementById('postTemplateSelector');
        if (!container) {
            console.error('❌ postTemplateSelector container not found!');
            return;
        }

        console.log('✅ Found postTemplateSelector container');

        try {
            // Load available templates for blog posts
            console.log('🔄 Fetching templates from API...');
            const data = await window.AdZetaApp.apiRequest('/templates/by-type?type=blog-post');

            console.log('📡 API Response:', data);

            if (data.success) {
                const templates = data.templates;
                const currentTemplate = this.state.currentPost.template || 'professional-enhanced';

                console.log('✅ Templates loaded:', templates);
                console.log('🎨 Current template:', currentTemplate);

                // Render template selector
                const selectorHTML = this.renderPostTemplateSelector(templates, currentTemplate);
                console.log('🖼️ Generated HTML:', selectorHTML);

                container.innerHTML = selectorHTML;

                // Bind template selection events
                this.bindPostTemplateEvents();

                console.log('✅ Template selector loaded successfully!');
            } else {
                console.error('❌ API returned error:', data.error);
                container.innerHTML = '<p class="text-muted">Failed to load templates: ' + (data.error || 'Unknown error') + '</p>';
            }
        } catch (error) {
            console.error('❌ Error loading post templates:', error);
            container.innerHTML = '<p class="text-muted">Error loading templates: ' + error.message + '</p>';
        }
    },

    // Render template selector for posts
    renderPostTemplateSelector(templates, currentTemplate) {
        if (!templates || Object.keys(templates).length === 0) {
            return '<p class="text-muted">No templates available.</p>';
        }

        let html = '<div class="row">';

        Object.entries(templates).forEach(([templateKey, template]) => {
            const isSelected = currentTemplate === templateKey;
            const selectedClass = isSelected ? 'border-primary bg-light' : '';

            html += `
                <div class="col-md-6 mb-3">
                    <div class="card template-option h-100 ${selectedClass}"
                         data-template="${templateKey}"
                         style="cursor: pointer; transition: all 0.3s ease;">
                        <div class="card-body text-center">
                            <div class="template-icon mb-2">
                                <i class="fas fa-file-alt fa-2x text-primary"></i>
                            </div>
                            <h6 class="card-title">${template.name}</h6>
                            <p class="card-text text-muted small">${template.description}</p>
                            ${isSelected ? '<div class="badge bg-primary">✓ Selected</div>' : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    },

    // Bind template selection events for posts
    bindPostTemplateEvents() {
        const templateOptions = document.querySelectorAll('#postTemplateSelector .template-option');

        console.log('🔗 Binding template events for', templateOptions.length, 'options');

        templateOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                console.log('🎯 Post template clicked:', e.currentTarget);
                const templateKey = e.currentTarget.dataset.template;
                console.log('🎨 Selected template key:', templateKey);

                if (templateKey) {
                    this.selectPostTemplate(templateKey);
                } else {
                    console.warn('⚠️ No template key found on clicked element');
                }
            });
        });

        // Also ensure the global template system can handle these
        // by adding the content-type data attribute if missing
        templateOptions.forEach(option => {
            if (!option.dataset.contentType) {
                option.dataset.contentType = 'blog-post';
            }
        });
    },

    // Select template for current post
    selectPostTemplate(templateKey) {
        // Update current post template
        this.state.currentPost.template = templateKey;

        // Update UI
        const templateOptions = document.querySelectorAll('#postTemplateSelector .template-option');
        templateOptions.forEach(option => {
            option.classList.remove('border-primary', 'bg-light');
            const badge = option.querySelector('.badge');
            if (badge) badge.remove();
        });

        // Highlight selected template
        const selectedOption = document.querySelector(`#postTemplateSelector .template-option[data-template="${templateKey}"]`);
        if (selectedOption) {
            selectedOption.classList.add('border-primary', 'bg-light');
            const cardBody = selectedOption.querySelector('.card-body');
            if (cardBody) {
                cardBody.insertAdjacentHTML('beforeend', '<div class="badge bg-primary">✓ Selected</div>');
            }
        }

        // Mark as changed
        this.markAsChanged();

        // Show notification
        window.AdZetaApp.showNotification(`Template changed to: ${templateKey}`, 'success');
    },

    // Bind AI generation events
    bindAIGenerationEvents() {
        // Generate with AI button
        document.addEventListener('click', (e) => {
            if (e.target.matches('#generateWithAI')) {
                this.generateContentWithAI();
            }
        });

        // Enter key in topic input
        document.addEventListener('keypress', (e) => {
            if (e.target.matches('#aiTopicInput') && e.key === 'Enter') {
                e.preventDefault();
                this.generateContentWithAI();
            }
        });
    },

    // Generate content with AI using existing Gemini integration
    async generateContentWithAI() {
        // Check if called from modal or inline
        const isModal = document.getElementById('aiModalTopic');

        const topicInput = isModal ?
            document.getElementById('aiModalTopic') :
            document.getElementById('aiTopicInput');
        const generateBtn = isModal ?
            document.getElementById('aiModalGenerate') :
            document.getElementById('generateWithAI');
        const statusDiv = isModal ?
            document.getElementById('aiModalStatus') :
            document.getElementById('aiGenerationStatus');
        const statusText = isModal ?
            document.getElementById('aiModalStatusText') :
            statusDiv?.querySelector('.alert');

        if (!topicInput || !topicInput.value.trim()) {
            window.AdZetaApp.showNotification('Please enter a topic for AI generation', 'warning');
            return;
        }

        const topic = topicInput.value.trim();
        const selectedTemplate = isModal ?
            document.getElementById('aiModalTemplate')?.value || 'professional-article' :
            this.state.currentPost.template || 'professional-article';

        // Get AI options from modal if available
        const temperature = isModal ?
            parseFloat(document.getElementById('aiModalTemperature')?.value || '0.7') : 0.7;
        const maxTokens = isModal ?
            parseInt(document.getElementById('aiModalLength')?.value || '2048') : 2048;

        try {
            // Show loading state
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
            statusDiv.style.display = 'block';

            // Call your existing AI API using authenticated request
            const data = await window.AdZetaApp.apiRequest('/ai/generate-post', {
                method: 'POST',
                body: JSON.stringify({
                    topic: topic,
                    template: selectedTemplate,
                    options: {
                        temperature: temperature,
                        maxOutputTokens: maxTokens
                    }
                })
            });

            if (data.success && data.post_data) {
                const postData = data.post_data;

                // Populate the form with generated content
                this.populateFormWithAIContent(postData);

                // Show success message
                window.AdZetaApp.showNotification(
                    `AI generated content for "${topic}" using ${selectedTemplate} template!`,
                    'success'
                );

                // Clear the topic input
                topicInput.value = '';

                // Close modal if it was opened from modal
                if (isModal) {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('aiGenerationModal'));
                    if (modal) {
                        modal.hide();
                    }
                }

            } else {
                throw new Error(data.error || 'Failed to generate content');
            }

        } catch (error) {
            console.error('AI generation error:', error);
            window.AdZetaApp.showNotification(
                'Failed to generate content: ' + error.message,
                'danger'
            );
        } finally {
            // Reset button state
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Generate with AI';
            statusDiv.style.display = 'none';
        }
    },

    // Populate form with AI-generated content
    populateFormWithAIContent(postData) {
        // Clean excerpt from AI markers before using it
        const cleanExcerpt = this.cleanAIMarkersFromText(postData.excerpt || '');

        // Update current post state
        this.state.currentPost = {
            ...this.state.currentPost,
            title: postData.title || '',
            excerpt: cleanExcerpt,
            content: postData.content || '',
            template: postData.template || 'professional-enhanced',
            // SEO data
            meta_title: postData.seo?.meta_title || '',
            meta_description: postData.seo?.meta_description || '',
            meta_keywords: postData.seo?.meta_keywords || '',
            focus_keyword: postData.seo?.focus_keyword || '',
            og_title: postData.seo?.og_title || '',
            og_description: postData.seo?.og_description || '',
            twitter_card_type: postData.seo?.twitter_card_type || 'summary_large_image'
        };

        // Update form fields
        const titleInput = document.getElementById('postTitle');
        if (titleInput) {
            titleInput.value = postData.title || '';
            // Trigger title change to generate slug
            titleInput.dispatchEvent(new Event('input', { bubbles: true }));
        }

        const excerptInput = document.getElementById('postExcerpt');
        if (excerptInput) excerptInput.value = cleanExcerpt;

        // Update Editor.js content
        if (this.state.editor && postData.content) {
            // Convert HTML content to Editor.js blocks
            this.convertHTMLToEditorBlocks(postData.content);
        }

        // Update SEO fields
        this.updateSEOFields(postData.seo || {});

        // Update template selection
        if (postData.template) {
            this.selectPostTemplate(postData.template);
        }

        // Mark as changed
        this.markAsChanged();

        // Update UI elements
        this.updateSearchPreview();
        this.updateWordCount();
    },

    // Populate ALL form fields with AI-generated content (ENHANCED VERSION)
    populateCompleteFormWithAIContent(postData) {
        console.log('🤖 Populating ALL form fields with complete AI-generated content...');
        console.log('📝 Post data received:', postData);

        // Clean excerpt from AI markers before using it
        const cleanExcerpt = this.cleanAIMarkersFromText(postData.excerpt || '');

        // Update current post state with ALL data
        this.state.currentPost = {
            ...this.state.currentPost,
            // Main content
            title: postData.title || '',
            excerpt: cleanExcerpt,
            content: postData.content || '',
            template: postData.template || 'professional-enhanced',
            // SEO data
            meta_title: postData.seo?.meta_title || '',
            meta_description: postData.seo?.meta_description || '',
            meta_keywords: postData.seo?.meta_keywords || '',
            focus_keyword: postData.seo?.focus_keyword || '',
            og_title: postData.seo?.og_title || '',
            og_description: postData.seo?.og_description || '',
            twitter_card_type: postData.seo?.twitter_card_type || 'summary_large_image',
            // Tags and keywords
            tags: postData.tags || [],
            keywords: postData.keywords || []
        };

        // 1. Update main content fields
        const titleInput = document.getElementById('postTitle');
        if (titleInput && postData.title) {
            titleInput.value = postData.title;
            titleInput.dispatchEvent(new Event('input', { bubbles: true }));
            console.log('✅ Title populated:', postData.title);
        }

        const excerptInput = document.getElementById('postExcerpt');
        if (excerptInput && cleanExcerpt) {
            excerptInput.value = cleanExcerpt;
            console.log('✅ Excerpt populated:', cleanExcerpt.substring(0, 100) + '...');
        }

        // 2. Update Editor.js content
        if (this.state.editor && postData.content) {
            this.convertHTMLToEditorBlocks(postData.content);
            console.log('✅ Editor content populated');
        }

        // 3. Update ALL SEO fields
        if (postData.seo) {
            this.updateSEOFields(postData.seo);
            console.log('✅ SEO fields populated:', Object.keys(postData.seo));
        }

        // 4. Update tags if provided
        if (postData.tags && Array.isArray(postData.tags)) {
            // Clear existing tags first
            this.state.currentPost.tags = [];

            // Add each tag
            postData.tags.forEach(tag => {
                this.addTagFromAI(tag);
            });
            console.log('✅ Tags populated:', postData.tags);
        }

        // 5. Update keywords if provided (using correct field ID)
        if (postData.keywords && Array.isArray(postData.keywords)) {
            const keywordsInput = document.getElementById('metaKeywords');
            if (keywordsInput) {
                keywordsInput.value = postData.keywords.join(', ');
                keywordsInput.dispatchEvent(new Event('input', { bubbles: true }));
                console.log('✅ Keywords populated:', postData.keywords);
            }
        }

        // 5b. Also update meta keywords from SEO data if available
        if (postData.seo?.meta_keywords) {
            const keywordsInput = document.getElementById('metaKeywords');
            if (keywordsInput && !keywordsInput.value) { // Only if not already set
                keywordsInput.value = postData.seo.meta_keywords;
                keywordsInput.dispatchEvent(new Event('input', { bubbles: true }));
                console.log('✅ Meta keywords populated:', postData.seo.meta_keywords);
            }
        }

        // 6. Update template selection
        if (postData.template) {
            this.selectPostTemplate(postData.template);
            console.log('✅ Template selected:', postData.template);
        }

        // 7. Update focus keyword if provided (using correct field ID)
        if (postData.seo?.focus_keyword) {
            const focusKeywordInput = document.getElementById('focusKeyword');
            if (focusKeywordInput) {
                focusKeywordInput.value = postData.seo.focus_keyword;
                focusKeywordInput.dispatchEvent(new Event('input', { bubbles: true }));
                console.log('✅ Focus keyword populated:', postData.seo.focus_keyword);
            }
        }

        // 8. Mark as changed
        this.markAsChanged();

        // 9. Update UI elements
        this.updateSearchPreview();
        this.updateWordCount();

        // 10. SEO analysis is now manual only
        console.log('📊 SEO analysis available - click "Analyze SEO" button when ready');

        console.log('🎉 ALL form fields populated successfully!');
        console.log('📊 Final post state:', this.state.currentPost);
    },

    // Clean AI markers from text content
    cleanAIMarkersFromText(text) {
        if (!text) return '';

        // Remove all AI custom block markers
        let cleaned = text;

        // Remove STYLED-CARD markers
        cleaned = cleaned.replace(/\[STYLED-CARD:[^\]]*\](.*?)\[\/STYLED-CARD\]/gs, '$1');

        // Remove STATISTIC markers
        cleaned = cleaned.replace(/\[STATISTIC:[^\]]*\]\[\/STATISTIC\]/gs, '');

        // Remove CTA-BLOCK markers
        cleaned = cleaned.replace(/\[CTA-BLOCK:[^\]]*\]\[\/CTA-BLOCK\]/gs, '');

        // Remove HIGHLIGHT markers but keep the content
        cleaned = cleaned.replace(/\[HIGHLIGHT:[^\]]*\](.*?)\[\/HIGHLIGHT\]/gs, '$1');

        // PRESERVE HTML formatting instead of stripping all tags
        cleaned = this.preserveInlineHTML(cleaned);

        // Clean up extra whitespace
        cleaned = cleaned.replace(/\s+/g, ' ').trim();

        return cleaned;
    },

    // Convert HTML content to Editor.js blocks (used by source view switching)
    async convertHTMLToEditorBlocks(htmlContent) {
        try {
            console.log('🔄 DEBUG: Converting HTML to Editor.js blocks for source view switching...');
            console.log('📝 DEBUG: Raw content received:', htmlContent.substring(0, 500) + '...');

            // IMPORTANT: Clear existing content first to prevent duplication
            console.log('🔍 DEBUG: Clearing existing editor content to prevent duplication...');
            await this.state.editor.clear();

            // Wait a bit for clear to complete
            await new Promise(resolve => setTimeout(resolve, 100));

            // Convert HTML to Editor.js blocks using comprehensive conversion
            const blocks = this.convertHTMLToEditorJSBlocks(htmlContent);

            if (blocks && blocks.length > 0) {
                const editorData = {
                    time: Date.now(),
                    blocks: blocks,
                    version: "2.8.1"
                };

                console.log('📊 DEBUG: Rendering new blocks:', editorData);
                await this.state.editor.render(editorData);

                console.log('✅ DEBUG: HTML content converted and rendered successfully');
            } else {
                console.warn('⚠️ DEBUG: No blocks generated from HTML content');
            }

        } catch (error) {
            console.error('❌ DEBUG: Error converting HTML to Editor.js blocks:', error);
            this.fallbackToSimpleContent(htmlContent);
        }
    },

    // Fallback method for simple content
    fallbackToSimpleContent(htmlContent) {
        // PRESERVE HTML formatting instead of stripping it
        const preservedContent = this.preserveInlineHTML(htmlContent.trim());
        if (preservedContent && this.state.editor) {
            this.state.editor.render({
                blocks: [{
                    type: 'paragraph',
                    data: {
                        text: preservedContent
                    }
                }]
            });
            console.log('✅ Fallback content with preserved HTML:', preservedContent.substring(0, 100) + '...');
        }
    },

    // Check if content has custom block markers
    hasCustomBlockMarkers(content) {
        return /\[(?:STYLED-CARD|STATISTIC|CTA-BLOCK|HIGHLIGHT):/.test(content);
    },

    // Process regular HTML content (fallback method)
    processRegularHTML(htmlContent) {
        const blocks = [];

        // Create a temporary DOM element to parse HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;

        // Process each child element
        const processElement = (element) => {
            const tagName = element.tagName?.toLowerCase();
            const textContent = element.textContent?.trim();

            if (!textContent) return;

            switch (tagName) {
                case 'h1':
                case 'h2':
                case 'h3':
                case 'h4':
                case 'h5':
                case 'h6':
                    blocks.push({
                        type: 'header',
                        data: {
                            text: this.preserveInlineHTML(element.innerHTML),
                            level: parseInt(tagName.charAt(1))
                        }
                    });
                    break;

                case 'p':
                    if (textContent) {
                        blocks.push({
                            type: 'paragraph',
                            data: {
                                text: this.preserveInlineHTML(element.innerHTML)
                            }
                        });
                    }
                    break;

                case 'ul':
                case 'ol':
                    const items = Array.from(element.querySelectorAll('li')).map(li =>
                        this.preserveInlineHTML(li.innerHTML)
                    ).filter(item => item.trim());

                    if (items.length > 0) {
                        blocks.push({
                            type: 'list',
                            data: {
                                style: tagName === 'ol' ? 'ordered' : 'unordered',
                                items: items
                            }
                        });
                    }
                    break;

                case 'blockquote':
                    blocks.push({
                        type: 'quote',
                        data: {
                            text: this.preserveInlineHTML(element.innerHTML),
                            caption: ''
                        }
                    });
                    break;

                case 'div':
                    // Handle div with special styling - convert to styled card if has background
                    if (element.style.background || element.style.backgroundColor) {
                        blocks.push({
                            type: 'styledCard',
                            data: {
                                content: this.preserveInlineHTML(element.innerHTML),
                                backgroundColor: element.style.backgroundColor || element.style.background || '#E6D8F2',
                                textColor: element.style.color || '#2B0B3A',
                                borderColor: this.extractBorderColor(element.style.border || element.style.borderLeft) || '#FF4081'
                            }
                        });
                    } else if (element.classList.contains('text-center')) {
                        // Preserve as paragraph with styling info
                        blocks.push({
                            type: 'paragraph',
                            data: {
                                text: this.preserveInlineHTML(element.innerHTML) +
                                      ` <small class="text-muted">[Centered: ${element.className}]</small>`
                            }
                        });
                    } else {
                        // Process children of div
                        Array.from(element.children).forEach(processElement);
                    }
                    break;

                default:
                    // For other elements, try to preserve as paragraph
                    if (textContent) {
                        blocks.push({
                            type: 'paragraph',
                            data: {
                                text: this.preserveInlineHTML(element.innerHTML)
                            }
                        });
                    }
            }
        };

        // Process all child elements
        Array.from(tempDiv.children).forEach(processElement);

        // If no blocks generated, split by line breaks and create paragraphs
        if (blocks.length === 0) {
            const lines = htmlContent.split(/\n\s*\n|<br\s*\/?>/i).filter(line => line.trim());
            lines.forEach(line => {
                // PRESERVE HTML formatting instead of stripping it
                const preservedText = this.preserveInlineHTML(line.trim());
                if (preservedText) {
                    blocks.push({
                        type: 'paragraph',
                        data: {
                            text: preservedText
                        }
                    });
                }
            });
        }

        return blocks;
    },

    // Extract border color from CSS border property
    extractBorderColor(borderStyle) {
        if (!borderStyle) return null;

        // Extract color from border style (e.g., "4px solid #FF4081")
        const colorMatch = borderStyle.match(/#[0-9A-Fa-f]{6}|#[0-9A-Fa-f]{3}|rgb\([^)]+\)|rgba\([^)]+\)/);
        return colorMatch ? colorMatch[0] : null;
    },

    // Convert HTML to Editor.js compatible format with proper inline formatting
    preserveInlineHTML(html) {
        if (!html) return '';

        // First, decode any HTML entities properly
        let text = this.unescapeHTML(html);

        console.log('🔧 Processing HTML for Editor.js inline format:');
        console.log('  Input:', html.substring(0, 100) + '...');
        console.log('  Decoded:', text.substring(0, 100) + '...');

        // Convert HTML formatting to Editor.js compatible inline formatting
        // Editor.js expects clean HTML tags for inline formatting

        // Convert <strong> to <b> (Editor.js standard)
        text = text.replace(/<strong[^>]*>(.*?)<\/strong>/gi, '<b>$1</b>');

        // Convert <em> to <i> (Editor.js standard)
        text = text.replace(/<em[^>]*>(.*?)<\/em>/gi, '<i>$1</i>');

        // Clean up <mark> tags - keep only essential attributes for Editor.js
        text = text.replace(/<mark[^>]*class=["']adzeta-highlight["'][^>]*>(.*?)<\/mark>/gi, '<mark class="cdx-marker">$1</mark>');
        text = text.replace(/<mark[^>]*>(.*?)<\/mark>/gi, '<mark class="cdx-marker">$1</mark>');

        // Handle links - preserve href attribute only
        text = text.replace(/<a\s+[^>]*href=["']([^"']*)["'][^>]*>(.*?)<\/a>/gi, '<a href="$1">$2</a>');

        // Clean up <code> tags
        text = text.replace(/<code[^>]*>(.*?)<\/code>/gi, '<code class="inline-code">$1</code>');

        // Remove any other HTML tags but keep their content
        // This preserves text while removing unsupported formatting
        text = text.replace(/<(?!\/?(b|i|u|code|mark|a)\b)[^>]*>/gi, '');

        // Clean up extra whitespace but preserve single spaces
        text = text.replace(/\s+/g, ' ').trim();

        console.log('  Final output:', text.substring(0, 100) + '...');

        return text;
    },

    // Import HTML content using paste mechanism (respects sanitization rules)
    async importHTMLContentViaPaste(htmlContent) {
        if (!this.state.editor || !htmlContent) return;

        try {
            console.log('📥 Importing HTML using paste mechanism with sanitization...');

            // Clean HTML using Editor.js sanitizer first
            const sanitizerConfig = {
                b: {},      // Allow <b> tags without attributes
                i: {},      // Allow <i> tags without attributes
                u: {},      // Allow <u> tags without attributes
                strong: {}, // Allow <strong> tags (will be converted)
                em: {},     // Allow <em> tags (will be converted)
                mark: {     // Allow <mark> tags with class
                    class: true,
                    style: true
                },
                a: {        // Allow links with href
                    href: true,
                    target: true
                },
                code: {},   // Allow inline code
                p: true     // Allow paragraphs
            };

            // Clean HTML using Editor.js sanitizer
            const cleanedHTML = this.state.editor.sanitizer.clean(htmlContent, sanitizerConfig);
            console.log('✅ Sanitized HTML:', cleanedHTML.substring(0, 200) + '...');

            // Wait for editor to be fully ready
            await new Promise(resolve => setTimeout(resolve, 100));

            // Create a paste event with the cleaned HTML
            const clipboardData = new DataTransfer();
            clipboardData.setData('text/html', cleanedHTML);
            clipboardData.setData('text/plain', this.stripHTML(cleanedHTML));

            const pasteEvent = new ClipboardEvent('paste', {
                clipboardData: clipboardData,
                bubbles: true,
                cancelable: true
            });

            // Get the editor container and dispatch paste event
            const editorContainer = document.getElementById('editorjs');
            if (editorContainer) {
                // Find the first editable element
                const editableElement = editorContainer.querySelector('[contenteditable="true"]');
                if (editableElement) {
                    editableElement.focus();
                    editableElement.dispatchEvent(pasteEvent);
                    console.log('✅ HTML content pasted successfully with sanitization');
                } else {
                    console.warn('⚠️ No editable element found, using fallback');
                    this.fallbackHTMLImport(htmlContent);
                }
            }

            // Update word count after content is loaded
            setTimeout(() => {
                this.updateWordCount();
            }, 1000);

        } catch (error) {
            console.error('❌ Error importing HTML content via paste:', error);
            this.fallbackHTMLImport(htmlContent);
        }
    },

    // Legacy method for backward compatibility
    async importHTMLContent(htmlContent) {
        return this.importHTMLContentViaPaste(htmlContent);
    },

    // Simulate paste event to import HTML with formatting
    async simulatePasteEvent(htmlContent) {
        try {
            console.log('📋 Simulating paste event for HTML import...');

            // Get the editor container
            const editorContainer = document.getElementById('editorjs');
            if (!editorContainer) {
                throw new Error('Editor container not found');
            }

            // Find the first editable element (paragraph)
            let editableElement = editorContainer.querySelector('[contenteditable="true"]');

            if (!editableElement) {
                // If no editable element, try to create a paragraph block first
                await this.state.editor.blocks.insert('paragraph');
                await new Promise(resolve => setTimeout(resolve, 100));
                editableElement = editorContainer.querySelector('[contenteditable="true"]');
            }

            if (editableElement) {
                // Focus the editable element
                editableElement.focus();

                // Create clipboard data
                const clipboardData = new DataTransfer();
                clipboardData.setData('text/html', htmlContent);
                clipboardData.setData('text/plain', this.stripHTML(htmlContent));

                // Create and dispatch paste event
                const pasteEvent = new ClipboardEvent('paste', {
                    clipboardData: clipboardData,
                    bubbles: true,
                    cancelable: true
                });

                editableElement.dispatchEvent(pasteEvent);
                console.log('✅ Paste event dispatched successfully');
            } else {
                throw new Error('No editable element found');
            }

        } catch (error) {
            console.error('❌ Error simulating paste event:', error);
            throw error;
        }
    },

    // Convert HTML to Editor.js blocks with proper inline formatting (following official format)
    convertHTMLToEditorJSBlocks(html) {
        if (!html) return [];

        console.log('🔄 Converting HTML to Editor.js blocks following official format...');

        // Clean and decode HTML first
        const cleanHTML = this.unescapeHTML(html);

        // Create a temporary container to parse HTML
        const tempContainer = document.createElement('div');
        tempContainer.innerHTML = cleanHTML;

        const blocks = [];

        // If no block-level elements, treat entire content as one paragraph
        if (tempContainer.children.length === 0) {
            const text = this.processInlineFormattingForEditorJS(cleanHTML);
            if (text.trim()) {
                blocks.push({
                    id: this.generateBlockId(),
                    type: 'paragraph',
                    data: {
                        text: text
                    }
                });
            }
        } else {
            // Process each child element
            for (let element of tempContainer.children) {
                const tagName = element.tagName?.toLowerCase();

                switch (tagName) {
                    case 'p':
                        const paragraphText = this.processInlineFormattingForEditorJS(element.innerHTML);
                        if (paragraphText.trim()) {
                            blocks.push({
                                id: this.generateBlockId(),
                                type: 'paragraph',
                                data: {
                                    text: paragraphText
                                }
                            });
                        }
                        break;

                    case 'h1':
                    case 'h2':
                    case 'h3':
                    case 'h4':
                    case 'h5':
                    case 'h6':
                        const headerText = this.processInlineFormattingForEditorJS(element.innerHTML);
                        if (headerText.trim()) {
                            blocks.push({
                                id: this.generateBlockId(),
                                type: 'header',
                                data: {
                                    text: headerText,
                                    level: parseInt(tagName.charAt(1))
                                }
                            });
                        }
                        break;

                    case 'ul':
                    case 'ol':
                        console.log('🔍 DEBUG: Processing', tagName, 'list with', element.children.length, 'items');
                        const listItems = Array.from(element.querySelectorAll('li')).map(li => {
                            const itemText = this.processInlineFormattingForEditorJS(li.innerHTML);
                            console.log('🔍 DEBUG: List item:', itemText);
                            return itemText;
                        }).filter(item => item.trim());

                        if (listItems.length > 0) {
                            blocks.push({
                                id: this.generateBlockId(),
                                type: 'list',
                                data: {
                                    style: tagName === 'ol' ? 'ordered' : 'unordered',
                                    items: listItems
                                }
                            });
                            console.log('✅ DEBUG: Created', tagName, 'list block with', listItems.length, 'items');
                        }
                        break;

                    case 'blockquote':
                        const quoteText = this.processInlineFormattingForEditorJS(element.innerHTML);
                        if (quoteText.trim()) {
                            blocks.push({
                                id: this.generateBlockId(),
                                type: 'quote',
                                data: {
                                    text: quoteText,
                                    caption: '',
                                    alignment: 'left'
                                }
                            });
                            console.log('✅ DEBUG: Created quote block');
                        }
                        break;

                    case 'hr':
                        blocks.push({
                            id: this.generateBlockId(),
                            type: 'delimiter',
                            data: {}
                        });
                        console.log('✅ DEBUG: Created delimiter block');
                        break;

                    case 'div':
                        // Handle div elements - check for special classes or treat as paragraph
                        if (element.classList.contains('styled-card') || element.classList.contains('custom-block')) {
                            // Preserve custom blocks as raw HTML in paragraph
                            const divHTML = element.outerHTML;
                            blocks.push({
                                id: this.generateBlockId(),
                                type: 'paragraph',
                                data: {
                                    text: divHTML
                                }
                            });
                            console.log('✅ DEBUG: Created custom div block (preserved as HTML)');
                        } else {
                            // Treat as paragraph with inline formatting
                            const divText = this.processInlineFormattingForEditorJS(element.innerHTML);
                            if (divText.trim()) {
                                blocks.push({
                                    id: this.generateBlockId(),
                                    type: 'paragraph',
                                    data: {
                                        text: divText
                                    }
                                });
                                console.log('✅ DEBUG: Created paragraph block from div content');
                            }
                        }
                        break;

                    default:
                        // Treat unknown elements as paragraphs with inline formatting
                        const defaultText = this.processInlineFormattingForEditorJS(element.innerHTML || element.textContent);
                        if (defaultText.trim()) {
                            blocks.push({
                                id: this.generateBlockId(),
                                type: 'paragraph',
                                data: {
                                    text: defaultText
                                }
                            });
                        }
                        break;
                }
            }
        }

        console.log('✅ Generated', blocks.length, 'Editor.js blocks following official format');
        console.log('📊 Sample block:', blocks[0]);
        return blocks;
    },

    // Note: HTML entity decoding methods removed - fixed at source in PHP backend

    // Generate unique block ID for Editor.js
    generateBlockId() {
        return Math.random().toString(36).substr(2, 10);
    },

    // Process inline formatting for Editor.js using sanitizer API
    processInlineFormattingForEditorJS(html) {
        if (!html) return '';

        console.log('🎨 DEBUG: Processing inline formatting:', html.substring(0, 100) + '...');

        // Manual cleaning to preserve inline formatting
        let text = html;

        // First, decode any HTML entities
        text = this.unescapeHTML(text);

        // Convert <strong> to <b> (Editor.js standard)
        text = text.replace(/<strong[^>]*>(.*?)<\/strong>/gi, '<b>$1</b>');

        // Convert <em> to <i> (Editor.js standard)
        text = text.replace(/<em[^>]*>(.*?)<\/em>/gi, '<i>$1</i>');

        // Preserve <mark> tags with class (for highlights)
        text = text.replace(/<mark[^>]*class=["']adzeta-highlight["'][^>]*>(.*?)<\/mark>/gi, '<mark class="cdx-marker">$1</mark>');
        text = text.replace(/<mark[^>]*class=["']cdx-marker["'][^>]*>(.*?)<\/mark>/gi, '<mark class="cdx-marker">$1</mark>');
        text = text.replace(/<mark[^>]*>(.*?)<\/mark>/gi, '<mark class="cdx-marker">$1</mark>');

        // Handle links - preserve href attribute only
        text = text.replace(/<a\s+[^>]*href=["']([^"']*)["'][^>]*>(.*?)<\/a>/gi, '<a href="$1">$2</a>');

        // Handle inline code
        text = text.replace(/<code[^>]*>(.*?)<\/code>/gi, '<code class="inline-code">$1</code>');

        // Keep <b>, <i>, <u> tags as-is (they're already in correct format)

        // Remove block-level tags but keep their content (these should be handled at block level)
        text = text.replace(/<\/?(?:div|p|h[1-6]|ul|ol|li|blockquote|hr)[^>]*>/gi, '');

        // Remove any other HTML tags but keep their content (except allowed inline tags)
        text = text.replace(/<(?!\/?(b|i|u|code|mark|a)\b)[^>]*>/gi, '');

        // Clean up extra whitespace but preserve single spaces
        text = text.replace(/\s+/g, ' ').trim();

        console.log('✅ DEBUG: Processed inline formatting result:', text.substring(0, 100) + '...');
        return text;
    },

    // Legacy method for backward compatibility
    processInlineFormatting(html) {
        return this.processInlineFormattingForEditorJS(html);
    },

    // Create simple paragraph blocks from HTML content
    createSimpleParagraphBlocks(htmlContent) {
        if (!htmlContent) {
            console.log('🔍 DEBUG: No HTML content provided');
            return [];
        }

        console.log('📝 DEBUG: Creating simple paragraph blocks from HTML...');
        console.log('🔍 DEBUG: Original HTML content:', htmlContent);

        // Clean and decode HTML
        const cleanHTML = this.unescapeHTML(htmlContent);
        console.log('🔍 DEBUG: After unescapeHTML:', cleanHTML);

        // Split content into paragraphs (simple approach)
        const paragraphs = cleanHTML
            .split(/<\/p>|<br\s*\/?>/gi)
            .map(p => p.replace(/<p[^>]*>/gi, '').trim())
            .filter(p => p.length > 0);

        console.log('🔍 DEBUG: Split into paragraphs:', paragraphs);

        // If no paragraph splits found, treat entire content as one paragraph
        if (paragraphs.length === 0 && cleanHTML.trim()) {
            paragraphs.push(cleanHTML.trim());
            console.log('🔍 DEBUG: No splits found, using entire content as one paragraph');
        }

        // Create Editor.js blocks
        const blocks = paragraphs.map((paragraph, index) => {
            // Keep HTML formatting in the text (Editor.js will handle sanitization)
            const text = paragraph.trim();

            const block = {
                id: this.generateBlockId(),
                type: 'paragraph',
                data: {
                    text: text // HTML tags will be sanitized by Editor.js automatically
                }
            };

            console.log(`🔍 DEBUG: Block ${index + 1}:`, block);
            return block;
        });

        console.log('✅ DEBUG: Created', blocks.length, 'paragraph blocks');
        console.log('📊 DEBUG: All blocks:', blocks);

        return blocks;
    },

    // Strip HTML tags to get plain text
    stripHTML(html) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        return tempDiv.textContent || tempDiv.innerText || '';
    },

    // Fallback method for HTML import
    async fallbackHTMLImport(htmlContent) {
        console.log('🔄 Using fallback HTML import method...');
        const blocks = this.processRegularHTML(htmlContent);
        if (blocks && blocks.length > 0) {
            await this.state.editor.render({ blocks: blocks });
        }
    },

    // Update SEO fields with generated data
    updateSEOFields(seoData) {
        console.log('🔍 Updating SEO fields with data:', seoData);

        // Correct field ID mapping based on actual HTML
        const seoFields = {
            'focusKeyword': seoData.focus_keyword,
            'metaTitle': seoData.meta_title,
            'metaDescription': seoData.meta_description,
            'metaKeywords': seoData.meta_keywords,
            // Social media fields (if they exist)
            'seoOgTitle': seoData.og_title,
            'seoOgDescription': seoData.og_description
        };

        Object.entries(seoFields).forEach(([fieldId, value]) => {
            const field = document.getElementById(fieldId);
            if (field && value) {
                field.value = value;
                // Trigger change event to update counters and state
                field.dispatchEvent(new Event('input', { bubbles: true }));
                field.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`✅ Updated ${fieldId}:`, value);
            } else if (!field) {
                console.log(`⚠️ Field not found: ${fieldId}`);
            }
        });

        // Update Twitter card type if field exists
        const twitterCardSelect = document.getElementById('seoTwitterCardType');
        if (twitterCardSelect && seoData.twitter_card_type) {
            twitterCardSelect.value = seoData.twitter_card_type;
            console.log('✅ Updated Twitter card type:', seoData.twitter_card_type);
        }

        console.log('✅ SEO fields update completed');
    },

    /**
     * Unescape HTML entities while preserving HTML tags (handles double escaping)
     */
    unescapeHTML(text) {
        if (!text) return '';

        // Handle double HTML entity escaping by decoding multiple times
        let unescaped = text;

        // First pass: decode double-escaped entities
        unescaped = unescaped
            .replace(/&amp;lt;/g, '<')     // &amp;lt; → <
            .replace(/&amp;gt;/g, '>')     // &amp;gt; → >
            .replace(/&amp;amp;/g, '&')   // &amp;amp; → &
            .replace(/&amp;quot;/g, '"')  // &amp;quot; → "
            .replace(/&amp;#39;/g, "'")   // &amp;#39; → '
            .replace(/&amp;nbsp;/g, ' '); // &amp;nbsp; → space

        // Second pass: decode remaining single-escaped entities
        unescaped = unescaped
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&amp;/g, '&')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'")
            .replace(/&nbsp;/g, ' ');

        console.log('🔧 Unescaping HTML entities (handling double escaping):');
        console.log('  Input:', text.substring(0, 100) + '...');
        console.log('  Output:', unescaped.substring(0, 100) + '...');

        return unescaped;
    },

    // Handle template selection
    handleTemplateSelection(templateId) {
        console.log('Template selected:', templateId);

        // Apply template to current post
        this.state.currentPost = window.AdZetaTemplates.applyTemplate(templateId, this.state.currentPost);

        // Update template display
        const templateCard = document.getElementById('templateSelectionCard');
        if (templateCard) {
            const template = window.AdZetaTemplates.getTemplate(templateId);
            const headerButton = templateCard.querySelector('.card-header button');
            if (headerButton && template) {
                headerButton.innerHTML = `
                    <i class="fas fa-palette me-2 text-primary"></i>
                    Template: ${template.name}
                    <i class="fas fa-chevron-down ms-2"></i>
                `;
            }

            // Collapse the template selector
            const collapse = templateCard.querySelector('#templateCollapse');
            if (collapse) {
                const bsCollapse = new bootstrap.Collapse(collapse, { toggle: false });
                bsCollapse.hide();
            }
        }

        // Reinitialize editor with template content
        this.initializeEditor();

        // Update form fields
        this.updateFormFields();

        // Mark as changed
        this.markAsChanged();

        window.AdZetaApp.showNotification(`Template "${templateId}" applied successfully!`, 'success');
    },

    // Update form fields with current post data
    updateFormFields() {
        const titleInput = document.getElementById('postTitle');
        const slugInput = document.getElementById('postSlug');
        const excerptTextarea = document.getElementById('postExcerpt');
        const metaTitleInput = document.getElementById('metaTitle');
        const metaDescTextarea = document.getElementById('metaDescription');

        if (titleInput) titleInput.value = this.state.currentPost.title || '';
        if (slugInput) slugInput.value = this.state.currentPost.slug || '';
        if (excerptTextarea) excerptTextarea.value = this.state.currentPost.excerpt || '';
        if (metaTitleInput) metaTitleInput.value = this.state.currentPost.meta_title || '';
        if (metaDescTextarea) metaDescTextarea.value = this.state.currentPost.meta_description || '';

        // Update counters
        this.updateExcerptCounter();
        this.updateMetaTitleCounter();
        this.updateMetaDescCounter();
        this.updateSearchPreview();
    },

    // Toggle footer visibility
    toggleFooter() {
        const footer = document.getElementById('editorFooter');
        const toggle = document.getElementById('footerToggle');

        if (footer && toggle) {
            const isHidden = footer.classList.contains('hidden');

            if (isHidden) {
                footer.classList.remove('hidden');
                toggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
                toggle.title = 'Hide footer';
            } else {
                footer.classList.add('hidden');
                toggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
                toggle.title = 'Show footer';
            }

            // Save state
            localStorage.setItem('editor-footer-hidden', (!isHidden).toString());
        }
    },

    // Initialize footer state
    initFooterState() {
        const footer = document.getElementById('editorFooter');
        const toggle = document.getElementById('footerToggle');

        if (footer && toggle) {
            const isHidden = localStorage.getItem('editor-footer-hidden') === 'true';

            if (isHidden) {
                footer.classList.add('hidden');
                toggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
                toggle.title = 'Show footer';
            } else {
                toggle.title = 'Hide footer';
            }
        }
    },

    // Update page header
    updatePageHeader(title) {
        const pageTitle = document.getElementById('pageTitle');
        const pageSubtitle = document.getElementById('pageSubtitle');
        const pageActions = document.getElementById('pageActions');

        if (pageTitle) {
            pageTitle.innerHTML = `<i class="fas fa-edit me-2"></i>${title}`;
        }
        if (pageSubtitle) {
            pageSubtitle.textContent = 'Create and optimize your blog content with our advanced editor.';
        }
        if (pageActions) {
            pageActions.innerHTML = '';
        }
    },

    // Open media library for featured image
    openMediaLibrary() {
        if (window.AdZetaMediaLibrary) {
            window.AdZetaMediaLibrary.open((selectedImage) => {
                this.setFeaturedImage(selectedImage);
            });
        } else {
            console.error('Media library not available');
            window.AdZetaApp.showNotification('Media library not available', 'danger');
        }
    },

    // Set featured image
    setFeaturedImage(image) {
        this.state.currentPost.featured_image = image.file_url;
        this.state.currentPost.featured_image_id = image.id;

        // Update UI
        this.updateFeaturedImageUI();
        this.markAsChanged();

        console.log('Featured image set:', image);
        window.AdZetaApp.showNotification('Featured image updated', 'success');
    },

    // Remove featured image
    removeFeaturedImage() {
        this.state.currentPost.featured_image = '';
        this.state.currentPost.featured_image_id = null;

        // Update UI
        this.updateFeaturedImageUI();
        this.markAsChanged();

        console.log('Featured image removed');
        window.AdZetaApp.showNotification('Featured image removed', 'info');
    },

    // Update featured image UI
    updateFeaturedImageUI() {
        const container = document.getElementById('featuredImageContainer');
        if (!container) return;

        if (this.state.currentPost.featured_image) {
            container.className = 'featured-image-container has-image';
            container.innerHTML = `
                <div class="featured-image-preview">
                    <img src="${this.state.currentPost.featured_image}" alt="Featured Image" id="featuredImagePreview">
                    <div class="featured-image-actions">
                        <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); AdZetaPostEditor.openMediaLibrary()">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); AdZetaPostEditor.removeFeaturedImage()">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        } else {
            container.className = 'featured-image-container';
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-image fa-3x text-muted mb-3"></i>
                    <h6>Set Featured Image</h6>
                    <p class="text-muted small mb-0">Click to select an image from media library</p>
                </div>
            `;
        }
    },

    // Handle focus keyword change
    handleFocusKeywordChange(input) {
        this.state.currentPost.focus_keyword = input.value;
        this.markAsChanged();
        // SEO analysis is now manual only
    },

    // Handle meta title change
    handleMetaTitleChange(input) {
        this.state.currentPost.meta_title = input.value;
        this.updateSearchPreview();
        this.markAsChanged();
        // SEO analysis is now manual only
    },

    // Handle meta description change
    handleMetaDescriptionChange(textarea) {
        this.state.currentPost.meta_description = textarea.value;
        this.updateSearchPreview();
        this.markAsChanged();
        // SEO analysis is now manual only
    },

    // Handle meta keywords change
    handleMetaKeywordsChange(input) {
        this.state.currentPost.meta_keywords = input.value;
        this.markAsChanged();
        // SEO analysis is now manual only
    },

    // Debounced SEO analysis
    debouncedAnalyzeSEO() {
        clearTimeout(this.seoAnalysisTimeout);
        this.seoAnalysisTimeout = setTimeout(() => {
            this.analyzeSEO();
        }, 1000);
    },

    // Analyze SEO
    async analyzeSEO() {
        if (!window.AdZetaAI) {
            console.warn('AI Assistant not available for SEO analysis');
            return;
        }

        try {
            // Show analyzing state
            const seoScoreBadge = document.getElementById('seoScore');
            if (seoScoreBadge) {
                seoScoreBadge.textContent = 'Analyzing...';
                seoScoreBadge.className = 'badge ms-2 badge-secondary';
            }

            console.log('🔍 Starting SEO analysis with proper field detection...');

            // Use AI assistant's proper field detection and analysis
            await window.AdZetaAI.analyzeSEO('post-editor');

        } catch (error) {
            console.error('SEO analysis failed:', error);

            const seoScoreBadge = document.getElementById('seoScore');
            if (seoScoreBadge) {
                seoScoreBadge.textContent = 'Error';
                seoScoreBadge.className = 'badge ms-2 badge-danger';
            }
        }
    },

    // Get current content from editor
    async getCurrentContent() {
        if (this.state.editor) {
            try {
                const outputData = await this.state.editor.save();
                return this.convertEditorJSToHTML(outputData);
            } catch (error) {
                console.error('Failed to get editor content:', error);
                return '';
            }
        } else {
            // Fallback to textarea
            const textarea = document.getElementById('contentTextarea');
            return textarea ? textarea.value : '';
        }
    },

    // Convert Editor.js output to HTML for analysis
    convertEditorJSToHTML(data) {
        if (!data || !data.blocks) return '';

        return data.blocks.map(block => {
            switch (block.type) {
                case 'header':
                    return `<h${block.data.level}>${block.data.text}</h${block.data.level}>`;
                case 'paragraph':
                    return `<p>${block.data.text}</p>`;
                case 'list':
                    const tag = block.data.style === 'ordered' ? 'ol' : 'ul';
                    const items = block.data.items.map(item => `<li>${item}</li>`).join('');
                    return `<${tag}>${items}</${tag}>`;
                case 'quote':
                    return `<blockquote>${block.data.text}</blockquote>`;
                case 'image':
                    return `<img src="${block.data.file.url}" alt="${block.data.caption || ''}" />`;

                // Custom AdZeta blocks for frontend
                case 'styledCard':
                    const cardData = block.data;
                    return `<div class="styled-card" style="background: ${cardData.backgroundColor || '#E6D8F2'}; color: ${cardData.textColor || '#2B0B3A'}; border-left: 4px solid ${cardData.borderColor || '#FF4081'}; border-radius: 12px; padding: 24px; margin: 16px 0; box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);">${cardData.content || ''}</div>`;

                case 'statisticsTool':
                    const statData = block.data;
                    return `<div class="statistics-display" style="text-align: center; padding: 24px; background: #F5F5F5; border-radius: 12px; margin: 16px 0; border: 2px solid ${statData.color || '#FF4081'};"><div class="stat-value" style="font-size: 3rem; font-weight: 700; color: ${statData.color || '#FF4081'}; line-height: 1; margin-bottom: 8px;">${statData.value || '100'}<span style="font-size: 2rem; margin-left: 4px;">${statData.unit || '%'}</span></div><div class="stat-label" style="font-size: 1rem; color: #2B0B3A; font-weight: 500;">${statData.label || 'Statistic'}</div></div>`;

                case 'ctaTool':
                    const ctaData = block.data;
                    let ctaBackground = '';
                    switch (ctaData.style || 'gradient') {
                        case 'gradient':
                            ctaBackground = 'background: linear-gradient(135deg, #2B0B3A, #FF4081);';
                            break;
                        case 'solid-purple':
                            ctaBackground = 'background: #2B0B3A;';
                            break;
                        case 'solid-pink':
                            ctaBackground = 'background: #FF4081;';
                            break;
                        case 'outline':
                            ctaBackground = 'background: white; border: 2px solid #FF4081;';
                            break;
                    }
                    const textColor = ctaData.style === 'outline' ? '#2B0B3A' : 'white';
                    const descColor = ctaData.style === 'outline' ? '#666' : '#E6D8F2';
                    return `<div class="cta-block" style="${ctaBackground} border-radius: 16px; padding: 48px; margin: 32px 0; text-align: ${ctaData.alignment || 'center'}; box-shadow: 0 8px 32px rgba(43, 11, 58, 0.2);"><h3 style="color: ${textColor}; font-size: 2rem; margin-bottom: 16px; margin-top: 0;">${ctaData.title || 'Ready to Get Started?'}</h3><p style="color: ${descColor}; font-size: 1.2rem; margin-bottom: 24px; max-width: 600px; margin-left: auto; margin-right: auto;">${ctaData.description || 'Transform your marketing today'}</p><a href="${ctaData.buttonUrl || '/demo'}" class="cta-button" style="display: inline-block; background: white; color: #2B0B3A; padding: 16px 32px; border-radius: 8px; text-decoration: none; font-weight: 700; font-size: 1.1rem; box-shadow: 0 2px 8px rgba(43, 11, 58, 0.1);">${ctaData.buttonText || 'Start Free Trial'}</a></div>`;

                default:
                    return block.data.text || '';
            }
        }).join('\n');
    },

    // SEO analysis is now handled by AI Assistant module

    // Get plain text content for AI processing
    async getPlainTextContent() {
        if (this.state.editor && typeof this.state.editor.save === 'function') {
            try {
                const outputData = await this.state.editor.save();
                let text = '';
                if (outputData.blocks) {
                    outputData.blocks.forEach(block => {
                        switch (block.type) {
                            case 'paragraph':
                                text += (block.data.text || '') + '\n\n';
                                break;
                            case 'header':
                                text += (block.data.text || '') + '\n\n';
                                break;
                            case 'list':
                                if (block.data.items) {
                                    block.data.items.forEach(item => {
                                        text += '• ' + item + '\n';
                                    });
                                    text += '\n';
                                }
                                break;
                            case 'quote':
                                text += '"' + (block.data.text || '') + '"\n\n';
                                break;
                            default:
                                if (block.data.text) {
                                    text += block.data.text + '\n\n';
                                }
                        }
                    });
                }
                return text.trim();
            } catch (error) {
                console.warn('Could not extract content from Editor.js:', error);
                return '';
            }
        }
        return '';
    },

    // Update field (for AI integration)
    updateField(fieldName, value) {
        this.state.currentPost[fieldName] = value;

        // Update the UI field if it exists
        const fieldMap = {
            'title': '#postTitle',
            'slug': '#postSlug',
            'excerpt': '#postExcerpt',
            'meta_title': '#metaTitle',
            'meta_description': '#metaDescription',
            'meta_keywords': '#metaKeywords',
            'focus_keyword': '#focusKeyword'
        };

        const selector = fieldMap[fieldName];
        if (selector) {
            const field = document.querySelector(selector);
            if (field) {
                field.value = value;
                field.dispatchEvent(new Event('input', { bubbles: true }));
                field.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }

        // Trigger specific updates based on field
        if (fieldName === 'title') {
            this.generateSlug();
            this.updateSearchPreview();
        } else if (fieldName === 'meta_title' || fieldName === 'meta_description') {
            this.updateSearchPreview();
        }

        this.markAsChanged();
    },

    // Add tag to post (AI integration method)
    addTagFromAI(tag) {
        console.log('Adding tag from AI:', tag);

        // Use the main addTag method to maintain consistency
        this.addTag(tag);

        // Also update the input field for immediate visual feedback
        const tagsInput = document.getElementById('postTags');
        if (tagsInput) {
            tagsInput.value = this.state.currentPost.tags.join(', ');
            tagsInput.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Re-render tags display
        this.renderTagsDisplay();
    },

    // Re-render tags display
    renderTagsDisplay() {
        const selectedTagsContainer = document.getElementById('selectedTags');
        if (selectedTagsContainer) {
            selectedTagsContainer.innerHTML = this.renderTags();
        }
    }
};
