<?php

namespace AdZetaAdmin\Models;

use PDO;
use Exception;
use Ad<PERSON>etaAdmin\Cache\CacheInvalidationHooks;

class BlogPost
{
    private PDO $db;
    private $cacheHooks;

    public function __construct($db)
    {
        // Handle both PDO objects and our database wrapper
        if ($db instanceof PDO) {
            $this->db = $db;
        } elseif (method_exists($db, 'getPdo')) {
            $this->db = $db->getPdo();
        } else {
            throw new InvalidArgumentException('Database object must be PDO or have getPdo() method');
        }

        // Initialize cache invalidation hooks
        $this->cacheHooks = new CacheInvalidationHooks();
    }

    /**
     * Get all blog posts with pagination and filtering
     */
    public function getAll(array $options = []): array
    {
        $page = $options['page'] ?? 1;
        $limit = $options['limit'] ?? 10;
        $status = $options['status'] ?? null;
        $category_id = $options['category_id'] ?? null;
        $search = $options['search'] ?? null;

        $offset = ($page - 1) * $limit;

        $sql = "SELECT p.*, u.first_name, u.last_name, c.name as category_name
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE 1=1";

        $params = [];

        if ($status) {
            $sql .= " AND p.status = :status";
            $params['status'] = $status;
        }

        if ($category_id) {
            $sql .= " AND p.category_id = :category_id";
            $params['category_id'] = $category_id;
        }

        if ($search) {
            $sql .= " AND (p.title LIKE :search OR p.content LIKE :search)";
            $params['search'] = "%{$search}%";
        }

        $sql .= " ORDER BY p.created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);

        foreach ($params as $key => $value) {
            $stmt->bindValue(":{$key}", $value);
        }

        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get total count for pagination
     */
    public function getCount(array $options = []): int
    {
        $status = $options['status'] ?? null;
        $category_id = $options['category_id'] ?? null;
        $search = $options['search'] ?? null;

        $sql = "SELECT COUNT(*) FROM blog_posts WHERE 1=1";
        $params = [];

        if ($status) {
            $sql .= " AND status = :status";
            $params['status'] = $status;
        }

        if ($category_id) {
            $sql .= " AND category_id = :category_id";
            $params['category_id'] = $category_id;
        }

        if ($search) {
            $sql .= " AND (title LIKE :search OR content LIKE :search)";
            $params['search'] = "%{$search}%";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return (int) $stmt->fetchColumn();
    }

    /**
     * Get a single blog post by ID or slug
     */
    public function getById($id): ?array
    {
        $sql = "SELECT p.*, u.first_name, u.last_name, c.name as category_name, c.slug as category_slug
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE p.id = :id";

        $stmt = $this->db->prepare($sql);
        $stmt->execute(['id' => $id]);

        $post = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($post) {
            // Decode JSON content blocks
            if ($post['content_blocks']) {
                $post['content_blocks'] = json_decode($post['content_blocks'], true);
            }

            // Get tags
            $post['tags'] = $this->getPostTags($post['id']);
        }

        return $post ?: null;
    }

    /**
     * Get a blog post by slug
     */
    public function getBySlug(string $slug): ?array
    {
        $sql = "SELECT p.*, u.first_name, u.last_name, c.name as category_name, c.slug as category_slug
                FROM blog_posts p
                LEFT JOIN users u ON p.author_id = u.id
                LEFT JOIN blog_categories c ON p.category_id = c.id
                WHERE p.slug = :slug AND p.status = 'published'";

        $stmt = $this->db->prepare($sql);
        $stmt->execute(['slug' => $slug]);

        $post = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($post) {
            // Decode JSON content blocks
            if ($post['content_blocks']) {
                $post['content_blocks'] = json_decode($post['content_blocks'], true);
            }

            // Get tags
            $post['tags'] = $this->getPostTags($post['id']);

            // Increment view count
            $this->incrementViewCount($post['id']);
        }

        return $post ?: null;
    }

    /**
     * Create a new blog post
     */
    public function create(array $data): int
    {
        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['title']);
        }

        // Calculate reading time and word count
        $content = $data['content'] ?? '';
        $data['word_count'] = str_word_count(strip_tags($content));
        $data['reading_time'] = max(1, ceil($data['word_count'] / 200)); // 200 words per minute

        // Encode content blocks as JSON (if not already a JSON string)
        if (isset($data['content_blocks'])) {
            if (is_array($data['content_blocks'])) {
                $data['content_blocks'] = json_encode($data['content_blocks']);
            } elseif (is_string($data['content_blocks'])) {
                // Already a JSON string, validate it
                $decoded = json_decode($data['content_blocks'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    // Invalid JSON, set to empty object
                    $data['content_blocks'] = '{}';
                }
                // Otherwise keep as-is
            } else {
                // Invalid type, set to empty object
                $data['content_blocks'] = '{}';
            }
        }

        $sql = "INSERT INTO blog_posts (
                    title, slug, content, content_blocks, excerpt, meta_title, meta_description,
                    meta_keywords, focus_keyword, canonical_url, og_image, og_title, og_description,
                    twitter_card_type, featured_image, author_id, category_id, status, published_at,
                    reading_time, word_count, noindex, nofollow, template
                ) VALUES (
                    :title, :slug, :content, :content_blocks, :excerpt, :meta_title, :meta_description,
                    :meta_keywords, :focus_keyword, :canonical_url, :og_image, :og_title, :og_description,
                    :twitter_card_type, :featured_image, :author_id, :category_id, :status, :published_at,
                    :reading_time, :word_count, :noindex, :nofollow, :template
                )";

        $stmt = $this->db->prepare($sql);

        // Set published_at if status is published
        if (($data['status'] ?? 'draft') === 'published' && empty($data['published_at'])) {
            $data['published_at'] = date('Y-m-d H:i:s');
        }

        $stmt->execute([
            'title' => $data['title'],
            'slug' => $data['slug'],
            'content' => $data['content'] ?? '',
            'content_blocks' => $data['content_blocks'] ?? null,
            'excerpt' => $data['excerpt'] ?? '',
            'meta_title' => $data['meta_title'] ?? '',
            'meta_description' => $data['meta_description'] ?? '',
            'meta_keywords' => $data['meta_keywords'] ?? '',
            'focus_keyword' => $data['focus_keyword'] ?? '',
            'canonical_url' => $data['canonical_url'] ?? '',
            'og_image' => $data['og_image'] ?? '',
            'og_title' => $data['og_title'] ?? '',
            'og_description' => $data['og_description'] ?? '',
            'twitter_card_type' => $data['twitter_card_type'] ?? 'summary_large_image',
            'featured_image' => $data['featured_image'] ?? '',
            'author_id' => $data['author_id'],
            'category_id' => $data['category_id'] ?? null,
            'status' => $data['status'] ?? 'draft',
            'published_at' => $data['published_at'] ?? null,
            'reading_time' => $data['reading_time'],
            'word_count' => $data['word_count'],
            'noindex' => $data['noindex'] ?? false,
            'nofollow' => $data['nofollow'] ?? false,
            'template' => $data['template'] ?? 'professional-enhanced'
        ]);

        $postId = $this->db->lastInsertId();

        // Handle tags
        if (!empty($data['tags'])) {
            $this->updatePostTags($postId, $data['tags']);
        }

        // Clear cache when new post is created
        try {
            $this->cacheHooks->onBlogPostSaved($postId, $data);
        } catch (Exception $e) {
            error_log('Cache invalidation failed after post creation: ' . $e->getMessage());
        }

        return $postId;
    }

    /**
     * Update an existing blog post
     */
    public function update(int $id, array $data): bool
    {
        // Generate new slug if title changed
        if (!empty($data['title']) && empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['title'], $id);
        }

        // Calculate reading time and word count
        if (isset($data['content'])) {
            $data['word_count'] = str_word_count(strip_tags($data['content']));
            $data['reading_time'] = max(1, ceil($data['word_count'] / 200));
        }

        // Encode content blocks as JSON (if not already a JSON string)
        if (isset($data['content_blocks'])) {
            if (is_array($data['content_blocks'])) {
                $data['content_blocks'] = json_encode($data['content_blocks']);
            } elseif (is_string($data['content_blocks'])) {
                // Already a JSON string, validate it
                $decoded = json_decode($data['content_blocks'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    // Invalid JSON, set to empty object
                    $data['content_blocks'] = '{}';
                }
                // Otherwise keep as-is
            } else {
                // Invalid type, set to empty object
                $data['content_blocks'] = '{}';
            }
        }

        // Get current post data BEFORE update for cache invalidation
        $currentPost = $this->getById($id);

        // Set published_at if status changed to published
        if (isset($data['status']) && $data['status'] === 'published') {
            if ($currentPost && $currentPost['status'] !== 'published') {
                $data['published_at'] = date('Y-m-d H:i:s');
            }
        }

        $fields = [];
        $params = ['id' => $id];

        $allowedFields = [
            'title', 'slug', 'content', 'content_blocks', 'excerpt', 'meta_title', 'meta_description',
            'meta_keywords', 'focus_keyword', 'canonical_url', 'og_image', 'og_title', 'og_description',
            'twitter_card_type', 'featured_image', 'category_id', 'status', 'published_at',
            'reading_time', 'word_count', 'noindex', 'nofollow', 'template'
        ];

        foreach ($allowedFields as $field) {
            if (array_key_exists($field, $data)) {
                $fields[] = "{$field} = :{$field}";
                $params[$field] = $data[$field];
            }
        }

        if (empty($fields)) {
            return false;
        }

        $sql = "UPDATE blog_posts SET " . implode(', ', $fields) . " WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute($params);

        // Handle tags
        if (array_key_exists('tags', $data)) {
            $this->updatePostTags($id, $data['tags'] ?? []);
        }

        // Clear cache when post is updated
        if ($result && $currentPost) {
            try {
                // Check if status changed (important for published posts)
                if (isset($data['status']) && $currentPost['status'] !== $data['status']) {
                    $this->cacheHooks->onBlogPostStatusChanged($id, $currentPost['status'], $data['status'], $currentPost['slug']);
                } else {
                    $this->cacheHooks->onBlogPostSaved($id, array_merge($currentPost, $data));
                }
            } catch (Exception $e) {
                error_log('Cache invalidation failed after post update: ' . $e->getMessage());
            }
        }

        return $result;
    }

    /**
     * Delete a blog post
     */
    public function delete(int $id): bool
    {
        // Get post data before deletion for cache invalidation
        $post = $this->getById($id);

        // Delete post tags first
        $stmt = $this->db->prepare("DELETE FROM post_tags WHERE post_id = :id");
        $stmt->execute(['id' => $id]);

        // Delete the post
        $stmt = $this->db->prepare("DELETE FROM blog_posts WHERE id = :id");
        $result = $stmt->execute(['id' => $id]);

        // Clear cache when post is deleted
        if ($result && $post) {
            try {
                $this->cacheHooks->onBlogPostDeleted($id, $post['slug']);
            } catch (Exception $e) {
                error_log('Cache invalidation failed after post deletion: ' . $e->getMessage());
            }
        }

        return $result;
    }

    /**
     * Generate a unique slug
     */
    private function generateUniqueSlug(string $title, int $excludeId = null): string
    {
        $baseSlug = $this->slugify($title);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Simple slugify function
     */
    private function slugify(string $text): string
    {
        // Replace non-alphanumeric characters with hyphens
        $text = preg_replace('/[^a-zA-Z0-9]+/', '-', $text);
        // Convert to lowercase
        $text = strtolower($text);
        // Remove leading/trailing hyphens
        $text = trim($text, '-');

        return $text;
    }

    /**
     * Check if slug exists
     */
    private function slugExists(string $slug, int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) FROM blog_posts WHERE slug = :slug";
        $params = ['slug' => $slug];

        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchColumn() > 0;
    }

    /**
     * Get tags for a post - returns array of tag names for frontend compatibility
     */
    private function getPostTags(int $postId): array
    {
        $sql = "SELECT t.name FROM blog_tags t
                INNER JOIN post_tags pt ON t.id = pt.tag_id
                WHERE pt.post_id = :post_id
                ORDER BY t.name";

        $stmt = $this->db->prepare($sql);
        $stmt->execute(['post_id' => $postId]);

        // Return array of tag names for frontend compatibility
        return array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'name');
    }

    /**
     * Update post tags - BULLETPROOF SIMPLE METHOD
     */
    private function updatePostTags(int $postId, array $tags): void
    {
        // Step 1: Delete existing tags (simple)
        $stmt = $this->db->prepare("DELETE FROM post_tags WHERE post_id = ?");
        $stmt->execute([$postId]);

        // Step 2: Insert new tags one by one (bulletproof)
        if (!empty($tags)) {
            foreach ($tags as $tag) {
                $tag = trim($tag);
                if (empty($tag)) continue;

                // Find or create tag
                $tagId = $this->findOrCreateTag($tag);
                if (!$tagId) continue;

                // Insert link (simple, one at a time)
                try {
                    $stmt = $this->db->prepare("INSERT INTO post_tags (post_id, tag_id) VALUES (?, ?)");
                    $stmt->execute([$postId, $tagId]);
                } catch (PDOException $e) {
                    // Skip if duplicate (already exists)
                    if ($e->getCode() !== '23000') {
                        throw $e;
                    }
                }
            }
        }
    }

    /**
     * Find existing tag by name or create new one
     */
    private function findOrCreateTag(string $tagName): ?int
    {
        if (empty($tagName)) {
            return null;
        }

        // First, try to find existing tag
        $stmt = $this->db->prepare("SELECT id FROM blog_tags WHERE name = :name");
        $stmt->execute(['name' => $tagName]);
        $existingTag = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingTag) {
            return (int)$existingTag['id'];
        }

        // Create new tag
        $slug = $this->slugify($tagName);
        $stmt = $this->db->prepare("INSERT INTO blog_tags (name, slug) VALUES (:name, :slug)");
        $stmt->execute([
            'name' => $tagName,
            'slug' => $slug
        ]);

        return (int)$this->db->lastInsertId();
    }

    /**
     * Increment view count
     */
    private function incrementViewCount(int $postId): void
    {
        $stmt = $this->db->prepare("UPDATE blog_posts SET view_count = view_count + 1 WHERE id = :id");
        $stmt->execute(['id' => $postId]);
    }
}
