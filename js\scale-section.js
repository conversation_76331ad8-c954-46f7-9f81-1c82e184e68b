// Scale Section Interactive Features - Apple-inspired Minimal Design
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Scale section accordions
    const scaleAccordion = document.getElementById('accordion-scale');

    if (scaleAccordion) {
        console.log('Scale accordion found, initializing...');

        // Get all accordion items
        const accordionItems = scaleAccordion.querySelectorAll('.accordion-item');
        console.log('Found accordion items:', accordionItems.length);

        // Add click event listeners to each accordion header
        accordionItems.forEach(function(item) {
            const header = item.querySelector('.accordion-header a');
            const collapse = item.querySelector('.accordion-collapse');
            const icon = item.querySelector('.accordion-title i.icon-small');

            if (header && collapse && icon) {
                header.addEventListener('click', function(e) {
                    // Don't prevent default to allow Bootstrap's collapse functionality to work
                    // e.preventDefault();

                    // Toggle active class
                    const isActive = item.classList.contains('active-accordion');

                    // Remove active class from all items
                    accordionItems.forEach(function(otherItem) {
                        otherItem.classList.remove('active-accordion');
                        const otherIcon = otherItem.querySelector('.accordion-title i.icon-small');

                        if (otherIcon) {
                            otherIcon.className = 'bi bi-chevron-down icon-small';
                        }
                    });

                    // If it wasn't active, make it active
                    if (!isActive) {
                        setTimeout(function() {
                            item.classList.add('active-accordion');
                            icon.className = 'bi bi-chevron-up icon-small';
                        }, 50);
                    }
                });
            }
        });

        // Initialize the first item as active
        const firstItem = accordionItems[0];
        if (firstItem) {
            firstItem.classList.add('active-accordion');
            const firstCollapse = firstItem.querySelector('.accordion-collapse');
            const firstIcon = firstItem.querySelector('.accordion-title i.icon-small');

            if (firstCollapse) {
                firstCollapse.classList.add('show');
            }

            if (firstIcon) {
                firstIcon.className = 'bi bi-chevron-up icon-small';
            }
        }
    } else {
        console.warn('Scale accordion not found in the document');
    }
    // Subtle hover effects for Apple-style metric cards
    const appleMetricCards = document.querySelectorAll('.apple-metric-card');

    appleMetricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const metricValue = this.querySelector('.metric-value');
            if (metricValue) {
                metricValue.style.transform = 'scale(1.05)';
                metricValue.style.transition = 'transform 0.2s ease';
            }
            // Subtle shadow increase on hover
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08)';
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            const metricValue = this.querySelector('.metric-value');
            if (metricValue) {
                metricValue.style.transform = 'scale(1)';
            }
            // Return to original shadow
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.04)';
            this.style.transform = 'translateY(0)';
        });
    });

    // Subtle hover effects for Apple-style capability cards
    const appleCapabilityCards = document.querySelectorAll('.apple-capability-card');

    appleCapabilityCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const capabilityNumber = this.querySelector('.capability-number');
            if (capabilityNumber) {
                capabilityNumber.style.transform = 'scale(1.05)';
                capabilityNumber.style.transition = 'transform 0.2s ease, color 0.2s ease';
                capabilityNumber.style.color = '#ee5c46'; // Change color on hover
            }
            // Subtle shadow increase on hover
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08)';
            this.style.transform = 'translateY(-2px)';
        });

        card.addEventListener('mouseleave', function() {
            const capabilityNumber = this.querySelector('.capability-number');
            if (capabilityNumber) {
                capabilityNumber.style.transform = 'scale(1)';
                capabilityNumber.style.color = '#f45888'; // Return to original color
            }
            // Return to original shadow
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.04)';
            this.style.transform = 'translateY(0)';
        });
    });

    // Add counter animation to metrics with Apple-style smooth animation
    function animateCounters() {
        const metrics = document.querySelectorAll('.metric-value');

        metrics.forEach(metric => {
            const targetValue = metric.textContent;
            const isPositive = targetValue.includes('+');
            const isNegative = targetValue.includes('-');
            const numericValue = parseFloat(targetValue.replace(/[^0-9.]/g, ''));

            // Only animate if in viewport
            const rect = metric.getBoundingClientRect();
            const isInViewport = (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );

            if (isInViewport && !metric.classList.contains('animated')) {
                metric.classList.add('animated');

                let startValue = 0;
                const duration = 1500; // 1.5 seconds - faster for Apple-style
                const startTime = performance.now();

                function updateCounter(currentTime) {
                    const elapsedTime = currentTime - startTime;
                    const progress = Math.min(elapsedTime / duration, 1);

                    // Apple-style easing function (smoother)
                    const easedProgress = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress);

                    const currentValue = Math.floor(easedProgress * numericValue);

                    let displayValue = currentValue;
                    if (isPositive) displayValue = '+' + displayValue;
                    if (isNegative) displayValue = '-' + displayValue;

                    // Add % or x if present in original
                    if (targetValue.includes('%')) displayValue += '%';
                    if (targetValue.includes('x')) displayValue += 'x';

                    metric.textContent = displayValue;

                    if (progress < 1) {
                        requestAnimationFrame(updateCounter);
                    } else {
                        metric.textContent = targetValue; // Ensure final value is exact
                    }
                }

                requestAnimationFrame(updateCounter);
            }
        }
    }

    // Run counter animation on scroll
    window.addEventListener('scroll', animateCounters);

    // Initial run for elements in viewport on load
    animateCounters();

    // Add subtle animation to testimonial metrics on hover
    const metricPills = document.querySelectorAll('.metric-pill');

    metricPills.forEach(pill => {
        pill.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.2s ease, background-color 0.2s ease';
            this.style.backgroundColor = '#f0f0f0';
        });

        pill.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.backgroundColor = '#f8f9fa';
        });
    });
});
