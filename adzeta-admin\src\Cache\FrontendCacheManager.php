<?php

namespace AdZetaAdmin\Cache;

/**
 * Frontend Cache Manager
 * Centralized caching for blog-list-dynamic.php and blog-post.php
 * Optimized for first-time visitors and SEO crawlers
 */
class FrontendCacheManager {
    private $cacheDir;
    private $settings;

    public function __construct() {
        $this->cacheDir = __DIR__ . '/../../cache/static/';
        $this->loadSettings();

        // Create cache directories if they don't exist
        $this->createCacheDirectories();
    }

    /**
     * Load cache settings from database
     */
    private function loadSettings() {
        try {
            // Load database connection
            require_once __DIR__ . '/../../bootstrap.php';
            global $admin_db;

            if ($admin_db) {
                // Get cache settings from database
                $cacheSettings = $admin_db->fetchAll(
                    "SELECT setting_key, setting_value, setting_type
                     FROM settings
                     WHERE setting_key LIKE 'cache_%' OR setting_key LIKE 'static_cache_%' OR setting_key LIKE 'browser_cache_%'
                     ORDER BY setting_key"
                );

                $this->settings = [];
                foreach ($cacheSettings as $setting) {
                    $this->settings[$setting['setting_key']] = $this->castSettingValue(
                        $setting['setting_value'],
                        $setting['setting_type']
                    );
                }

                error_log('FrontendCacheManager: Loaded settings from database: ' . json_encode($this->settings));
            } else {
                error_log('FrontendCacheManager: Database not available, using defaults');
                $this->settings = $this->getDefaultSettings();
            }
        } catch (Exception $e) {
            error_log('FrontendCacheManager: Error loading settings from database: ' . $e->getMessage());
            $this->settings = $this->getDefaultSettings();
        }

        // Ensure we have required settings
        if (empty($this->settings)) {
            $this->settings = $this->getDefaultSettings();
        }
    }

    /**
     * Get default settings (disabled by default)
     */
    private function getDefaultSettings() {
        return [
            'cache_enabled' => false,           // ✅ Default to disabled
            'static_cache_enabled' => false,    // ✅ Default to disabled
            'static_cache_duration' => 14400,   // 4 hours
            'static_cache_gzip' => false,       // ✅ Default to disabled
            'static_cache_minify' => false,     // ✅ Default to disabled
            'browser_cache_enabled' => false    // ✅ Default to disabled
        ];
    }

    /**
     * Cast setting value to appropriate type
     */
    private function castSettingValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'json':
                return json_decode($value, true) ?: [];
            case 'text':
            case 'string':
            default:
                return (string)$value;
        }
    }

    /**
     * Get cache settings
     */
    public function getSettings() {
        return $this->settings;
    }

    /**
     * Create necessary cache directories
     */
    private function createCacheDirectories() {
        $dirs = [
            $this->cacheDir,
            $this->cacheDir . 'posts/',
            $this->cacheDir . 'pages/',
            $this->cacheDir . 'categories/'
        ];

        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * Check if cache is enabled
     */
    public function isEnabled() {
        // Check both cache_enabled (master toggle) and static_cache_enabled (specific toggle)
        $masterEnabled = $this->settings['cache_enabled'] ?? true;
        $staticEnabled = $this->settings['static_cache_enabled'] ?? true;

        // Debug: Log cache status
        error_log('FrontendCacheManager.isEnabled(): master=' . ($masterEnabled ? 'true' : 'false') . ', static=' . ($staticEnabled ? 'true' : 'false') . ', result=' . (($masterEnabled && $staticEnabled) ? 'true' : 'false'));
        error_log('FrontendCacheManager settings: ' . json_encode($this->settings));

        $result = $masterEnabled && $staticEnabled;
        error_log('FrontendCacheManager.isEnabled() returning: ' . ($result ? 'TRUE' : 'FALSE'));

        return $result;
    }

    /**
     * Get cached content for blog post
     */
    public function getBlogPostCache($slug) {
        if (!$this->isEnabled()) {
            return false;
        }

        $cacheFile = $this->cacheDir . 'posts/' . $slug . '.html';

        if (!file_exists($cacheFile)) {
            return false;
        }

        // Check if cache is still valid
        $cacheAge = time() - filemtime($cacheFile);
        $maxAge = $this->settings['static_cache_duration'] ?? 14400;

        if ($cacheAge > $maxAge) {
            // Cache expired, delete it
            unlink($cacheFile);
            if (file_exists($cacheFile . '.gz')) {
                unlink($cacheFile . '.gz');
            }
            return false;
        }

        return $this->serveCachedFile($cacheFile);
    }

    /**
     * Get cached content for blog list
     */
    public function getBlogListCache($page = 1, $category = null, $tag = null) {
        if (!$this->isEnabled()) {
            return false;
        }

        // Generate cache key based on parameters
        $cacheKey = 'blog-list';
        if ($page > 1) {
            $cacheKey .= '-page-' . $page;
        }
        if ($category) {
            $cacheKey .= '-cat-' . $category;
        }
        if ($tag) {
            $cacheKey .= '-tag-' . $tag;
        }

        $cacheFile = $this->cacheDir . 'pages/' . $cacheKey . '.html';

        if (!file_exists($cacheFile)) {
            return false;
        }

        // Check if cache is still valid
        $cacheAge = time() - filemtime($cacheFile);
        $maxAge = $this->settings['static_cache_duration'] ?? 14400;

        if ($cacheAge > $maxAge) {
            // Cache expired, delete it
            unlink($cacheFile);
            if (file_exists($cacheFile . '.gz')) {
                unlink($cacheFile . '.gz');
            }
            return false;
        }

        return $this->serveCachedFile($cacheFile);
    }

    /**
     * Serve cached file with proper headers
     */
    private function serveCachedFile($cacheFile) {
        // Check if client accepts gzip and gzip version exists
        if ($this->acceptsGzip() && file_exists($cacheFile . '.gz')) {
            header('Content-Encoding: gzip');
            header('Content-Type: text/html; charset=UTF-8');
            header('Cache-Control: public, max-age=' . ($this->settings['browser_cache_duration'] ?? 86400));
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($cacheFile)) . ' GMT');

            // Add cache hit header for debugging
            header('X-Cache: HIT-GZIP');

            return file_get_contents($cacheFile . '.gz');
        }

        // Serve regular file
        header('Content-Type: text/html; charset=UTF-8');
        header('Cache-Control: public, max-age=' . ($this->settings['browser_cache_duration'] ?? 86400));
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($cacheFile)) . ' GMT');

        // Add cache hit header for debugging
        header('X-Cache: HIT');

        return file_get_contents($cacheFile);
    }

    /**
     * Cache blog post content
     */
    public function cacheBlogPost($slug, $content) {
        if (!$this->isEnabled()) {
            return false;
        }

        $cacheFile = $this->cacheDir . 'posts/' . $slug . '.html';

        // Minify HTML if enabled
        if ($this->settings['static_cache_minify'] ?? false) {
            $content = $this->minifyHtml($content);
        }

        // Add cache generation comment
        $content = $this->addCacheComment($content, 'blog-post', $slug);

        // Save regular file
        file_put_contents($cacheFile, $content);

        // Save gzipped version if enabled
        if ($this->settings['static_cache_gzip'] ?? false) {
            file_put_contents($cacheFile . '.gz', gzencode($content, 9));
        }

        return true;
    }

    /**
     * Cache blog list content
     */
    public function cacheBlogList($content, $page = 1, $category = null, $tag = null) {
        if (!$this->isEnabled()) {
            return false;
        }

        // Generate cache key based on parameters
        $cacheKey = 'blog-list';
        if ($page > 1) {
            $cacheKey .= '-page-' . $page;
        }
        if ($category) {
            $cacheKey .= '-cat-' . $category;
        }
        if ($tag) {
            $cacheKey .= '-tag-' . $tag;
        }

        $cacheFile = $this->cacheDir . 'pages/' . $cacheKey . '.html';

        // Minify HTML if enabled
        if ($this->settings['static_cache_minify'] ?? false) {
            $content = $this->minifyHtml($content);
        }

        // Add cache generation comment
        $content = $this->addCacheComment($content, 'blog-list', $cacheKey);

        // Save regular file
        file_put_contents($cacheFile, $content);

        // Save gzipped version if enabled
        if ($this->settings['static_cache_gzip'] ?? false) {
            file_put_contents($cacheFile . '.gz', gzencode($content, 9));
        }

        return true;
    }

    /**
     * Clear cache for specific blog post
     */
    public function clearBlogPostCache($slug) {
        // Clear from posts directory
        $cacheFile = $this->cacheDir . 'posts/' . $slug . '.html';
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
        }
        if (file_exists($cacheFile . '.gz')) {
            unlink($cacheFile . '.gz');
        }

        // Clear from router directory (where actual cached files are)
        $routerCacheFile = $this->cacheDir . 'router/blog-post-' . $slug . '.html';
        if (file_exists($routerCacheFile)) {
            unlink($routerCacheFile);
        }
        if (file_exists($routerCacheFile . '.gz')) {
            unlink($routerCacheFile . '.gz');
        }

        // Clear from static router directory
        $staticRouterCacheFile = __DIR__ . '/../../cache/static/router/blog-post-' . $slug . '.html';
        if (file_exists($staticRouterCacheFile)) {
            unlink($staticRouterCacheFile);
        }
        if (file_exists($staticRouterCacheFile . '.gz')) {
            unlink($staticRouterCacheFile . '.gz');
        }

        // Also clear blog list cache since it might contain this post
        $this->clearBlogListCache();

        error_log("Cache cleared for blog post: {$slug}");
        return true;
    }

    /**
     * Clear all blog list cache
     */
    public function clearBlogListCache() {
        // Clear blog list cache from pages directory
        $pattern = $this->cacheDir . 'pages/blog-list*.html';
        $files = glob($pattern);

        foreach ($files as $file) {
            unlink($file);
            if (file_exists($file . '.gz')) {
                unlink($file . '.gz');
            }
        }

        // Also clear router cache for blog list
        $routerPattern = $this->cacheDir . 'router/blog-list*.html';
        $routerFiles = glob($routerPattern);

        foreach ($routerFiles as $file) {
            unlink($file);
            if (file_exists($file . '.gz')) {
                unlink($file . '.gz');
            }
        }

        // Clear the old static cache file if it exists
        $oldCacheFile = $this->cacheDir . '../static/blog-list.html';
        if (file_exists($oldCacheFile)) {
            unlink($oldCacheFile);
        }

        // Clear static router cache for blog lists
        $staticRouterPattern = __DIR__ . '/../../cache/static/router/blog-list*.html';
        $staticRouterFiles = glob($staticRouterPattern);
        foreach ($staticRouterFiles as $file) {
            unlink($file);
            if (file_exists($file . '.gz')) {
                unlink($file . '.gz');
            }
        }

        error_log("Blog list cache cleared");
        return true;
    }

    /**
     * Clear all cache
     */
    public function clearAllCache() {
        $dirs = [
            $this->cacheDir . 'posts/',
            $this->cacheDir . 'pages/',
            $this->cacheDir . 'categories/',
            $this->cacheDir . 'router/'  // Add router cache directory
        ];

        foreach ($dirs as $dir) {
            if (is_dir($dir)) {
                $files = glob($dir . '*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
            }
        }

        // Also clear the static cache directory used by router
        $staticCacheDir = __DIR__ . '/../../cache/static/router/';
        if (is_dir($staticCacheDir)) {
            $files = glob($staticCacheDir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }

        return true;
    }

    /**
     * Get cache statistics
     */
    public function getStats() {
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'blog_posts_cached' => 0,
            'blog_lists_cached' => 0,
            'last_generated' => null
        ];

        // Count blog post cache files (check posts/, router/, and static router directories)
        $postPattern = $this->cacheDir . 'posts/*.html';
        $postFiles = glob($postPattern);

        $routerPostPattern = $this->cacheDir . 'router/blog-post-*.html';
        $routerPostFiles = glob($routerPostPattern);

        $staticPostPattern = __DIR__ . '/../../cache/static/router/blog-post-*.html';
        $staticPostFiles = glob($staticPostPattern);

        $stats['blog_posts_cached'] = count($postFiles) + count($routerPostFiles) + count($staticPostFiles);

        // Count blog list cache files (check pages/, router/, and static router directories)
        $listPattern = $this->cacheDir . 'pages/blog-list*.html';
        $listFiles = glob($listPattern);

        $routerListPattern = $this->cacheDir . 'router/blog-list*.html';
        $routerListFiles = glob($routerListPattern);

        $staticListPattern = __DIR__ . '/../../cache/static/router/blog-list*.html';
        $staticListFiles = glob($staticListPattern);

        $stats['blog_lists_cached'] = count($listFiles) + count($routerListFiles) + count($staticListFiles);

        // Also check for any HTML files in pages directory
        $allPagesPattern = $this->cacheDir . 'pages/*.html';
        $allPageFiles = glob($allPagesPattern);

        // Check router directory for all cache files
        $allRouterPattern = $this->cacheDir . 'router/*.html';
        $allRouterFiles = glob($allRouterPattern);

        // Also check static router cache directory
        $staticRouterPattern = __DIR__ . '/../../cache/static/router/*.html';
        $staticRouterFiles = glob($staticRouterPattern);

        // Calculate total files and size
        $allFiles = array_merge($postFiles, $allPageFiles, $routerPostFiles, $routerListFiles, $staticPostFiles, $staticListFiles, $staticRouterFiles);
        $stats['total_files'] = count($allFiles);

        $latestTime = 0;
        foreach ($allFiles as $file) {
            $stats['total_size'] += filesize($file);
            $time = filemtime($file);
            if ($time > $latestTime) {
                $latestTime = $time;
            }
        }

        $stats['last_generated'] = $latestTime > 0 ? $latestTime : null;
        $stats['total_size_formatted'] = $this->formatBytes($stats['total_size']);

        return $stats;
    }

    /**
     * Check if client accepts gzip encoding
     */
    private function acceptsGzip() {
        return isset($_SERVER['HTTP_ACCEPT_ENCODING']) &&
               strpos($_SERVER['HTTP_ACCEPT_ENCODING'], 'gzip') !== false;
    }

    /**
     * Add cache generation comment to HTML
     */
    private function addCacheComment($content, $type, $key) {
        $comment = sprintf(
            "<!-- Cached by AdZeta Frontend Cache Manager | Type: %s | Key: %s | Generated: %s -->",
            $type,
            $key,
            date('Y-m-d H:i:s')
        );

        // Insert comment after <html> tag if it exists
        if (strpos($content, '<html') !== false) {
            $content = preg_replace('/(<html[^>]*>)/', '$1' . "\n" . $comment, $content, 1);
        } else {
            $content = $comment . "\n" . $content;
        }

        return $content;
    }

    /**
     * Enhanced HTML minification for better performance
     */
    private function minifyHtml($html) {
        // Remove HTML comments (except IE conditional comments)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);

        // Remove unnecessary whitespace while preserving content
        $html = preg_replace('/\s+/', ' ', $html);

        // Remove whitespace between tags
        $html = preg_replace('/>\s+</', '><', $html);

        // Remove whitespace around specific tags
        $html = preg_replace('/\s*(<\/?(?:div|p|h[1-6]|section|article|header|footer|nav|main|aside)\b[^>]*>)\s*/', '$1', $html);

        // Remove empty lines and trim
        $html = preg_replace('/\n\s*\n/', '\n', $html);

        // Remove leading/trailing whitespace from lines
        $html = preg_replace('/^\s+|\s+$/m', '', $html);

        return trim($html);
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Get cache file path for debugging
     */
    public function getCacheFilePath($type, $key) {
        switch ($type) {
            case 'blog-post':
                return $this->cacheDir . 'posts/' . $key . '.html';
            case 'blog-list':
                return $this->cacheDir . 'pages/' . $key . '.html';
            default:
                return $this->cacheDir . 'pages/' . $key . '.html';
        }
    }

    /**
     * Check if specific cache file exists
     */
    public function cacheExists($type, $key) {
        $filePath = $this->getCacheFilePath($type, $key);
        return file_exists($filePath);
    }

    /**
     * Get cache file age in seconds
     */
    public function getCacheAge($type, $key) {
        $filePath = $this->getCacheFilePath($type, $key);

        if (!file_exists($filePath)) {
            return null;
        }

        return time() - filemtime($filePath);
    }
}
