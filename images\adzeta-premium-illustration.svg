<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 800 500" width="800" height="500">
  <defs>
    <!-- Gradients -->
    <linearGradient id="adzeta-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#e958a1" />
      <stop offset="50%" stop-color="#d15ec7" />
      <stop offset="100%" stop-color="#8f76f5" />
    </linearGradient>
    
    <linearGradient id="soft-purple-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8f76f5" />
      <stop offset="100%" stop-color="#6a4fd9" />
    </linearGradient>
    
    <linearGradient id="soft-pink-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#e958a1" />
      <stop offset="100%" stop-color="#d15ec7" />
    </linearGradient>
    
    <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4A90E2" />
      <stop offset="100%" stop-color="#6a4fd9" />
    </linearGradient>
    
    <linearGradient id="dark-bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1B0B24" />
      <stop offset="100%" stop-color="#2D1A3B" />
    </linearGradient>
    
    <!-- Filters -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="5" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
      <feOffset dx="0" dy="2" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
    
    <!-- Patterns -->
    <pattern id="grid-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
      <rect width="20" height="20" fill="none" />
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5" />
    </pattern>
    
    <pattern id="data-pattern" width="30" height="30" patternUnits="userSpaceOnUse">
      <rect width="30" height="30" fill="none" />
      <circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.2)" />
    </pattern>
    
    <!-- Clip paths -->
    <clipPath id="wave-clip">
      <path d="M0,0 L800,0 L800,400 C700,450 600,380 500,400 C400,420 300,380 200,400 C100,420 50,380 0,400 Z" />
    </clipPath>
  </defs>
  
  <!-- Background elements -->
  <rect width="800" height="500" fill="none" />
  
  <!-- Main illustration container -->
  <g class="main-illustration">
    <!-- Central flow path -->
    <path class="main-flow-path" d="M100,250 C200,150 300,350 400,250 C500,150 600,350 700,250" fill="none" stroke="url(#adzeta-gradient)" stroke-width="2" stroke-dasharray="5,5" opacity="0">
      <animate attributeName="opacity" values="0;0.7" dur="1s" begin="0.5s" fill="freeze" />
    </path>
    
    <!-- Data Source Illustration -->
    <g class="data-source" transform="translate(100, 250)" filter="url(#soft-shadow)">
      <!-- Stylized data source icon -->
      <circle cx="0" cy="0" r="50" fill="#1B0B24" stroke="#ffffff" stroke-width="1.5" />
      
      <!-- Data visualization elements -->
      <g transform="translate(0, 0)">
        <!-- Stylized database icon -->
        <path d="M-20,-20 L20,-20 L20,20 L-20,20 Z" fill="none" stroke="#ffffff" stroke-width="1.5" />
        <path d="M-20,-10 L20,-10 M-20,0 L20,0 M-20,10 L20,10" stroke="#ffffff" stroke-width="1" />
        
        <!-- Animated data points -->
        <circle cx="-10" cy="-15" r="3" fill="#4A90E2">
          <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" begin="1s" repeatCount="indefinite" />
        </circle>
        <circle cx="10" cy="-5" r="3" fill="#e958a1">
          <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" begin="1.3s" repeatCount="indefinite" />
        </circle>
        <circle cx="-10" cy="5" r="3" fill="#8f76f5">
          <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" begin="1.6s" repeatCount="indefinite" />
        </circle>
        <circle cx="10" cy="15" r="3" fill="#4A90E2">
          <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" begin="1.9s" repeatCount="indefinite" />
        </circle>
      </g>
      
      <!-- Label -->
      <text x="0" y="70" font-family="Arial, sans-serif" font-size="14" fill="#FFFFFF" text-anchor="middle" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="1s" fill="freeze" />
        Customer Data
      </text>
    </g>
    
    <!-- Data Flow Animation 1 -->
    <g class="data-flow-1">
      <!-- Flowing particles from Data Source to AI Engine -->
      <circle cx="100" cy="250" r="4" fill="#4A90E2" opacity="0">
        <animate attributeName="opacity" values="0;1;0" dur="2s" begin="2s" repeatCount="2" />
        <animate attributeName="cx" values="100;250;400" dur="2s" begin="2s" repeatCount="2" />
        <animate attributeName="cy" values="250;200;250" dur="2s" begin="2s" repeatCount="2" />
      </circle>
      <circle cx="100" cy="250" r="4" fill="#e958a1" opacity="0">
        <animate attributeName="opacity" values="0;1;0" dur="2s" begin="2.3s" repeatCount="2" />
        <animate attributeName="cx" values="100;250;400" dur="2s" begin="2.3s" repeatCount="2" />
        <animate attributeName="cy" values="250;200;250" dur="2s" begin="2.3s" repeatCount="2" />
      </circle>
      <circle cx="100" cy="250" r="4" fill="#8f76f5" opacity="0">
        <animate attributeName="opacity" values="0;1;0" dur="2s" begin="2.6s" repeatCount="2" />
        <animate attributeName="cx" values="100;250;400" dur="2s" begin="2.6s" repeatCount="2" />
        <animate attributeName="cy" values="250;200;250" dur="2s" begin="2.6s" repeatCount="2" />
      </circle>
    </g>
    
    <!-- AI Engine Illustration -->
    <g class="ai-engine" transform="translate(400, 250)" filter="url(#soft-shadow)">
      <!-- Stylized AI engine -->
      <circle cx="0" cy="0" r="60" fill="#1B0B24" stroke="url(#adzeta-gradient)" stroke-width="2" />
      
      <!-- Inner processing visualization -->
      <g transform="translate(0, 0)">
        <!-- Central core -->
        <circle cx="0" cy="0" r="30" fill="none" stroke="url(#adzeta-gradient)" stroke-width="1.5" opacity="0.7">
          <animate attributeName="opacity" values="0.7;1;0.7" dur="3s" begin="3s" repeatCount="indefinite" />
        </circle>
        
        <!-- Neural network visualization -->
        <g class="neural-network" opacity="0">
          <animate attributeName="opacity" values="0;1" dur="1s" begin="3.5s" fill="freeze" />
          
          <!-- Nodes -->
          <circle cx="-15" cy="-15" r="5" fill="#e958a1" />
          <circle cx="15" cy="-15" r="5" fill="#8f76f5" />
          <circle cx="-15" cy="0" r="5" fill="#4A90E2" />
          <circle cx="15" cy="0" r="5" fill="#e958a1" />
          <circle cx="0" cy="15" r="5" fill="#8f76f5" />
          
          <!-- Connections -->
          <line x1="-15" y1="-15" x2="15" y2="-15" stroke="#ffffff" stroke-width="0.5" />
          <line x1="-15" y1="-15" x2="-15" y2="0" stroke="#ffffff" stroke-width="0.5" />
          <line x1="-15" y1="-15" x2="15" y2="0" stroke="#ffffff" stroke-width="0.5" />
          <line x1="15" y1="-15" x2="-15" y2="0" stroke="#ffffff" stroke-width="0.5" />
          <line x1="15" y1="-15" x2="15" y2="0" stroke="#ffffff" stroke-width="0.5" />
          <line x1="-15" y1="0" x2="0" y2="15" stroke="#ffffff" stroke-width="0.5" />
          <line x1="15" y1="0" x2="0" y2="15" stroke="#ffffff" stroke-width="0.5" />
          
          <!-- Animated pulses along connections -->
          <circle cx="-15" cy="-15" r="2" fill="#ffffff">
            <animate attributeName="cx" values="-15;15" dur="1.5s" begin="4s" repeatCount="indefinite" />
            <animate attributeName="cy" values="-15;-15" dur="1.5s" begin="4s" repeatCount="indefinite" />
          </circle>
          <circle cx="-15" cy="-15" r="2" fill="#ffffff">
            <animate attributeName="cx" values="-15;-15" dur="1.5s" begin="4.2s" repeatCount="indefinite" />
            <animate attributeName="cy" values="-15;0" dur="1.5s" begin="4.2s" repeatCount="indefinite" />
          </circle>
          <circle cx="15" cy="-15" r="2" fill="#ffffff">
            <animate attributeName="cx" values="15;15" dur="1.5s" begin="4.4s" repeatCount="indefinite" />
            <animate attributeName="cy" values="-15;0" dur="1.5s" begin="4.4s" repeatCount="indefinite" />
          </circle>
          <circle cx="-15" cy="0" r="2" fill="#ffffff">
            <animate attributeName="cx" values="-15;0" dur="1.5s" begin="4.6s" repeatCount="indefinite" />
            <animate attributeName="cy" values="0;15" dur="1.5s" begin="4.6s" repeatCount="indefinite" />
          </circle>
          <circle cx="15" cy="0" r="2" fill="#ffffff">
            <animate attributeName="cx" values="15;0" dur="1.5s" begin="4.8s" repeatCount="indefinite" />
            <animate attributeName="cy" values="0;15" dur="1.5s" begin="4.8s" repeatCount="indefinite" />
          </circle>
        </g>
      </g>
      
      <!-- Rotating outer ring -->
      <circle cx="0" cy="0" r="45" fill="none" stroke="#ffffff" stroke-width="0.5" stroke-dasharray="3,6" opacity="0.5">
        <animateTransform attributeName="transform" type="rotate" from="0" to="360" dur="30s" repeatCount="indefinite" />
      </circle>
      
      <!-- Labels -->
      <text x="0" y="-10" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF" text-anchor="middle" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="3s" fill="freeze" />
        AdZeta
      </text>
      <text x="0" y="10" font-family="Arial, sans-serif" font-size="16" fill="#FFFFFF" text-anchor="middle" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="3.2s" fill="freeze" />
        AI Core
      </text>
      
      <!-- Pulsing effect -->
      <circle cx="0" cy="0" r="30" fill="url(#adzeta-gradient)" opacity="0">
        <animate attributeName="r" values="30;70;30" dur="2s" begin="3.5s" repeatCount="indefinite" />
        <animate attributeName="opacity" values="0.2;0;0.2" dur="2s" begin="3.5s" repeatCount="indefinite" />
      </circle>
    </g>
    
    <!-- Data Flow Animation 2 -->
    <g class="data-flow-2">
      <!-- Flowing particles from AI Engine to Optimization -->
      <circle cx="400" cy="250" r="4" fill="#e958a1" opacity="0">
        <animate attributeName="opacity" values="0;1;0" dur="2s" begin="6s" repeatCount="2" />
        <animate attributeName="cx" values="400;550;700" dur="2s" begin="6s" repeatCount="2" />
        <animate attributeName="cy" values="250;200;250" dur="2s" begin="6s" repeatCount="2" />
      </circle>
      <circle cx="400" cy="250" r="4" fill="#8f76f5" opacity="0">
        <animate attributeName="opacity" values="0;1;0" dur="2s" begin="6.3s" repeatCount="2" />
        <animate attributeName="cx" values="400;550;700" dur="2s" begin="6.3s" repeatCount="2" />
        <animate attributeName="cy" values="250;200;250" dur="2s" begin="6.3s" repeatCount="2" />
      </circle>
      <circle cx="400" cy="250" r="4" fill="#4A90E2" opacity="0">
        <animate attributeName="opacity" values="0;1;0" dur="2s" begin="6.6s" repeatCount="2" />
        <animate attributeName="cx" values="400;550;700" dur="2s" begin="6.6s" repeatCount="2" />
        <animate attributeName="cy" values="250;200;250" dur="2s" begin="6.6s" repeatCount="2" />
      </circle>
    </g>
    
    <!-- Optimization Illustration -->
    <g class="optimization" transform="translate(700, 250)" filter="url(#soft-shadow)">
      <!-- Stylized optimization icon -->
      <circle cx="0" cy="0" r="50" fill="#1B0B24" stroke="#ffffff" stroke-width="1.5" />
      
      <!-- Growth chart visualization -->
      <g transform="translate(0, 0)" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="1s" begin="7s" fill="freeze" />
        
        <!-- Chart axes -->
        <path d="M-25,-25 L-25,25 L25,25" stroke="#ffffff" stroke-width="1.5" />
        
        <!-- Growth line -->
        <path d="M-25,25 L-15,15 L-5,18 L5,5 L15,-5 L25,-20" fill="none" stroke="url(#soft-pink-gradient)" stroke-width="2" stroke-dasharray="50" stroke-dashoffset="50">
          <animate attributeName="stroke-dashoffset" values="50;0" dur="1.5s" begin="7.5s" fill="freeze" />
        </path>
        
        <!-- Data points -->
        <circle cx="-15" cy="15" r="0" fill="#e958a1">
          <animate attributeName="r" values="0;3" dur="0.2s" begin="7.7s" fill="freeze" />
        </circle>
        <circle cx="-5" cy="18" r="0" fill="#e958a1">
          <animate attributeName="r" values="0;3" dur="0.2s" begin="7.9s" fill="freeze" />
        </circle>
        <circle cx="5" cy="5" r="0" fill="#e958a1">
          <animate attributeName="r" values="0;3" dur="0.2s" begin="8.1s" fill="freeze" />
        </circle>
        <circle cx="15" cy="-5" r="0" fill="#e958a1">
          <animate attributeName="r" values="0;3" dur="0.2s" begin="8.3s" fill="freeze" />
        </circle>
        <circle cx="25" cy="-20" r="0" fill="#e958a1">
          <animate attributeName="r" values="0;3" dur="0.2s" begin="8.5s" fill="freeze" />
        </circle>
      </g>
      
      <!-- Label -->
      <text x="0" y="70" font-family="Arial, sans-serif" font-size="14" fill="#FFFFFF" text-anchor="middle" opacity="0">
        <animate attributeName="opacity" values="0;1" dur="0.5s" begin="7s" fill="freeze" />
        Profit Growth
      </text>
    </g>
    
    <!-- Results Visualization -->
    <g class="results" transform="translate(400, 400)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="1s" begin="9s" fill="freeze" />
      
      <!-- Results metrics -->
      <g transform="translate(-200, 0)">
        <circle cx="0" cy="0" r="40" fill="#1B0B24" stroke="url(#soft-pink-gradient)" stroke-width="1.5" />
        <text x="0" y="-5" font-family="Arial, sans-serif" font-size="12" fill="#FFFFFF" text-anchor="middle">LTV Increase</text>
        <text x="0" y="20" font-family="Arial, sans-serif" font-size="20" fill="#e958a1" text-anchor="middle" opacity="0">
          <animate attributeName="opacity" values="0;1" dur="0.5s" begin="9.5s" fill="freeze" />
          +37%
        </text>
      </g>
      
      <g transform="translate(0, 0)">
        <circle cx="0" cy="0" r="40" fill="#1B0B24" stroke="url(#adzeta-gradient)" stroke-width="1.5" />
        <text x="0" y="-5" font-family="Arial, sans-serif" font-size="12" fill="#FFFFFF" text-anchor="middle">ROAS</text>
        <text x="0" y="20" font-family="Arial, sans-serif" font-size="20" fill="#8f76f5" text-anchor="middle" opacity="0">
          <animate attributeName="opacity" values="0;1" dur="0.5s" begin="9.7s" fill="freeze" />
          4x
        </text>
      </g>
      
      <g transform="translate(200, 0)">
        <circle cx="0" cy="0" r="40" fill="#1B0B24" stroke="url(#blue-gradient)" stroke-width="1.5" />
        <text x="0" y="-5" font-family="Arial, sans-serif" font-size="12" fill="#FFFFFF" text-anchor="middle">CAC Reduction</text>
        <text x="0" y="20" font-family="Arial, sans-serif" font-size="20" fill="#4A90E2" text-anchor="middle" opacity="0">
          <animate attributeName="opacity" values="0;1" dur="0.5s" begin="9.9s" fill="freeze" />
          -36%
        </text>
      </g>
    </g>
    
    <!-- Final message -->
    <g transform="translate(400, 470)" opacity="0">
      <animate attributeName="opacity" values="0;1" dur="1s" begin="10.5s" fill="freeze" />
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="18" fill="#FFFFFF" text-anchor="middle">Maximize Growth with AI-Powered Value-Based Bidding</text>
    </g>
  </g>
  
  <!-- Restart animation button -->
  <g transform="translate(700, 470)" opacity="0" class="restart-button">
    <animate attributeName="opacity" values="0;1" dur="1s" begin="11s" fill="freeze" />
    <rect x="-50" y="-15" width="100" height="30" rx="15" fill="rgba(255,255,255,0.2)" />
    <text x="0" y="5" font-family="Arial, sans-serif" font-size="12" fill="#FFFFFF" text-anchor="middle">Replay</text>
    
    <!-- Add onclick event to restart animation -->
    <rect x="-50" y="-15" width="100" height="30" rx="15" fill="rgba(255,255,255,0)" opacity="0">
      <set attributeName="onclick" to="
        document.querySelectorAll('.main-flow-path, .data-source text, .neural-network, .ai-engine text, .optimization g, .optimization text, .results, .restart-button, .data-flow-1 circle, .data-flow-2 circle').forEach(el => {
          el.setAttribute('opacity', '0');
          Array.from(el.querySelectorAll('animate')).forEach(anim => {
            anim.setAttribute('begin', '0s');
            anim.beginElement();
          });
        });
      " />
    </rect>
  </g>
</svg>
