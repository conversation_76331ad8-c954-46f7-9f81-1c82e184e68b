/**
 * AdZeta Admin Panel - Media Library Module
 * WordPress-style media library for image management
 */

window.AdZetaMediaLibrary = {
    // State
    state: {
        isOpen: false,
        selectedImage: null,
        images: [],
        currentPage: 1,
        totalPages: 1,
        loading: false,
        uploadProgress: 0
    },

    // Initialize media library
    init() {
        this.createMediaLibraryModal();
        this.bindEvents();
        console.log('Media Library module initialized');
    },

    // Create media library modal
    createMediaLibraryModal() {
        const modalHTML = `
            <div class="modal fade media-library-modal" id="mediaLibraryModal" tabindex="-1" aria-labelledby="mediaLibraryModalLabel" aria-hidden="true" style="z-index: 1080 !important;">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="mediaLibraryModalLabel">
                                <i class="fas fa-images me-2"></i>Media Library
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <!-- Upload Area -->
                            <div class="upload-area p-4 border-bottom">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <div class="upload-dropzone" id="uploadDropzone">
                                            <div class="text-center py-4">
                                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                <h5>Drop files here or click to upload</h5>
                                                <p class="text-muted">Supports: JPG, PNG, GIF, WebP (Max: 10MB)</p>
                                                <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
                                                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                                    <i class="fas fa-plus me-2"></i>Select Files
                                                </button>
                                            </div>
                                            <div class="upload-progress" id="uploadProgress" style="display: none;">
                                                <div class="progress">
                                                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                                </div>
                                                <small class="text-muted">Uploading...</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="search-box">
                                            <input type="text" class="form-control" id="mediaSearch" placeholder="Search images...">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Media Grid -->
                            <div class="media-grid-container" style="height: 400px; overflow-y: auto;">
                                <div class="p-3">
                                    <div class="media-grid" id="mediaGrid">
                                        <!-- Media items will be loaded here -->
                                    </div>
                                    <div class="text-center py-4" id="mediaLoading" style="display: none;">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Loading media...</p>
                                    </div>
                                    <div class="text-center py-4" id="mediaEmpty" style="display: none;">
                                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                                        <h5>No images found</h5>
                                        <p class="text-muted">Upload your first image to get started.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Pagination -->
                            <div class="media-pagination p-3 border-top" id="mediaPagination" style="display: none;">
                                <!-- Pagination will be rendered here -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="selected-info me-auto" id="selectedInfo" style="display: none;">
                                <small class="text-muted">
                                    <span id="selectedImageName"></span> - 
                                    <span id="selectedImageSize"></span>
                                </small>
                            </div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="selectImageBtn" disabled>Select Image</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body if it doesn't exist
        if (!document.getElementById('mediaLibraryModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }
    },

    // Bind events
    bindEvents() {
        // File input change
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        }

        // Drag and drop
        const dropzone = document.getElementById('uploadDropzone');
        if (dropzone) {
            dropzone.addEventListener('dragover', this.handleDragOver.bind(this));
            dropzone.addEventListener('drop', this.handleDrop.bind(this));
        }

        // Search
        const searchInput = document.getElementById('mediaSearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(this.handleSearch.bind(this), 300));
        }

        // Select button
        const selectBtn = document.getElementById('selectImageBtn');
        if (selectBtn) {
            selectBtn.addEventListener('click', this.handleSelectImage.bind(this));
        }

        // Modal events
        const modal = document.getElementById('mediaLibraryModal');
        if (modal) {
            modal.addEventListener('shown.bs.modal', () => {
                this.state.isOpen = true;
                this.loadMedia();
            });
            modal.addEventListener('hidden.bs.modal', () => {
                this.state.isOpen = false;
                this.state.selectedImage = null;
            });
        }
    },

    // Open media library
    open(callback) {
        this.selectCallback = callback;

        const modalElement = document.getElementById('mediaLibraryModal');
        if (!modalElement) {
            console.error('Media library modal not found');
            return;
        }

        // Ensure media library modal has the highest z-index
        modalElement.classList.add('media-library-modal');
        modalElement.style.zIndex = '1080';
        modalElement.style.position = 'fixed';

        // Create modal instance with specific configuration
        this.currentModalInstance = new bootstrap.Modal(modalElement, {
            backdrop: 'static',  // Prevent closing by clicking backdrop
            keyboard: true,
            focus: true
        });

        // Handle backdrop z-index after modal is shown
        modalElement.addEventListener('shown.bs.modal', () => {
            // Ensure backdrop is below the modal
            const backdrop = document.querySelector('.modal-backdrop:last-child');
            if (backdrop) {
                backdrop.style.zIndex = '1075';
                backdrop.classList.add('media-library-backdrop');
            }

            // Load media
            this.loadMedia();
        }, { once: true });

        // Clean up when modal is hidden
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.style.zIndex = '';
            modalElement.style.position = '';
            this.currentModalInstance = null;

            // Clean up backdrop classes
            const backdrops = document.querySelectorAll('.media-library-backdrop');
            backdrops.forEach(backdrop => {
                backdrop.classList.remove('media-library-backdrop');
                backdrop.style.zIndex = '';
            });
        }, { once: true });

        this.currentModalInstance.show();
    },

    // Handle file select
    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        this.uploadFiles(files);
    },

    // Handle drag over
    handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('drag-over');
    },

    // Handle drop
    handleDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('drag-over');
        
        const files = Array.from(event.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        if (files.length > 0) {
            this.uploadFiles(files);
        }
    },

    // Upload files
    async uploadFiles(files) {
        const progressContainer = document.getElementById('uploadProgress');
        const progressBar = progressContainer.querySelector('.progress-bar');
        
        progressContainer.style.display = 'block';
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const progress = ((i + 1) / files.length) * 100;
            
            progressBar.style.width = `${progress}%`;
            progressBar.textContent = `${Math.round(progress)}%`;
            
            try {
                await this.uploadSingleFile(file);
            } catch (error) {
                console.error('Upload failed:', error);
                window.AdZetaApp.showNotification(`Failed to upload ${file.name}`, 'danger');
            }
        }
        
        progressContainer.style.display = 'none';
        this.loadMedia(); // Refresh media grid
    },

    // Upload single file
    async uploadSingleFile(file) {
        const formData = new FormData();
        formData.append('image', file);
        
        const response = await fetch('/adzeta-admin/api/media/upload.php', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (!response.ok) {
            throw new Error(`Upload failed: ${response.statusText}`);
        }
        
        const result = await response.json();
        if (!result.success && result.success !== 1) {
            throw new Error(result.message || 'Upload failed');
        }
        
        return result;
    },

    // Load media
    async loadMedia(page = 1, search = '') {
        this.state.loading = true;
        this.showLoading();
        
        try {
            const params = new URLSearchParams({
                page: page,
                limit: 20,
                search: search
            });
            
            const response = await fetch(`/adzeta-admin/api/media/index.php?${params}`);

            // Check if response is ok
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Get response text first to debug JSON issues
            const responseText = await response.text();

            // Try to parse JSON
            let result;
            try {
                result = JSON.parse(responseText);
            } catch (jsonError) {
                console.error('Media Library JSON Parse Error:', jsonError);
                console.error('Response Text:', responseText.substring(0, 500));
                throw new Error(`Invalid JSON response from media API. Response: ${responseText.substring(0, 100)}...`);
            }
            
            if (result.success) {
                this.state.images = result.media || [];
                this.state.currentPage = result.pagination?.current_page || 1;
                this.state.totalPages = result.pagination?.total_pages || 1;
                
                this.renderMediaGrid();
                this.renderPagination();
            } else {
                throw new Error(result.message || 'Failed to load media');
            }
        } catch (error) {
            console.error('Failed to load media:', error);
            this.showEmpty();
        } finally {
            this.state.loading = false;
            this.hideLoading();
        }
    },

    // Render media grid
    renderMediaGrid() {
        const grid = document.getElementById('mediaGrid');
        const emptyState = document.getElementById('mediaEmpty');
        
        if (this.state.images.length === 0) {
            grid.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }
        
        emptyState.style.display = 'none';
        
        grid.innerHTML = this.state.images.map(image => `
            <div class="media-item ${this.state.selectedImage?.id === image.id ? 'selected' : ''}" 
                 data-id="${image.id}" onclick="AdZetaMediaLibrary.selectImage(${image.id})">
                <div class="media-thumbnail">
                    <img src="${image.file_url}" alt="${image.original_filename}" loading="lazy">
                    <div class="media-overlay">
                        <i class="fas fa-check"></i>
                    </div>
                </div>
                <div class="media-info">
                    <div class="media-name" title="${image.original_filename}">${image.original_filename}</div>
                    <div class="media-size">${this.formatFileSize(image.file_size)}</div>
                </div>
            </div>
        `).join('');
    },

    // Select image
    selectImage(imageId) {
        const image = this.state.images.find(img => img.id === imageId);
        if (image) {
            this.state.selectedImage = image;
            
            // Update UI
            document.querySelectorAll('.media-item').forEach(item => {
                item.classList.remove('selected');
            });
            document.querySelector(`[data-id="${imageId}"]`).classList.add('selected');
            
            // Update selected info
            const selectedInfo = document.getElementById('selectedInfo');
            const selectedName = document.getElementById('selectedImageName');
            const selectedSize = document.getElementById('selectedImageSize');
            const selectBtn = document.getElementById('selectImageBtn');
            
            selectedInfo.style.display = 'block';
            selectedName.textContent = image.original_filename;
            selectedSize.textContent = this.formatFileSize(image.file_size);
            selectBtn.disabled = false;
        }
    },

    // Handle select image
    handleSelectImage() {
        if (this.state.selectedImage && this.selectCallback) {
            this.selectCallback(this.state.selectedImage);

            // Close modal properly without affecting other modals
            if (this.currentModalInstance) {
                this.currentModalInstance.hide();
            } else {
                const modal = bootstrap.Modal.getInstance(document.getElementById('mediaLibraryModal'));
                if (modal) {
                    modal.hide();
                }
            }

            // Reset state
            this.state.selectedImage = null;
            this.selectCallback = null;
        }
    },

    // Handle search
    handleSearch(event) {
        const query = event.target.value.trim();
        this.loadMedia(1, query);
    },

    // Show loading
    showLoading() {
        document.getElementById('mediaLoading').style.display = 'block';
        document.getElementById('mediaGrid').style.display = 'none';
    },

    // Hide loading
    hideLoading() {
        document.getElementById('mediaLoading').style.display = 'none';
        document.getElementById('mediaGrid').style.display = 'grid';
    },

    // Show empty state
    showEmpty() {
        document.getElementById('mediaEmpty').style.display = 'block';
        document.getElementById('mediaGrid').style.display = 'none';
    },

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Render pagination
    renderPagination() {
        const pagination = document.getElementById('mediaPagination');
        
        if (this.state.totalPages <= 1) {
            pagination.style.display = 'none';
            return;
        }
        
        pagination.style.display = 'block';
        
        let paginationHTML = '<nav><ul class="pagination pagination-sm justify-content-center mb-0">';
        
        // Previous button
        if (this.state.currentPage > 1) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="AdZetaMediaLibrary.loadMedia(${this.state.currentPage - 1})">Previous</a></li>`;
        }
        
        // Page numbers
        for (let i = 1; i <= this.state.totalPages; i++) {
            if (i === this.state.currentPage) {
                paginationHTML += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
            } else {
                paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="AdZetaMediaLibrary.loadMedia(${i})">${i}</a></li>`;
            }
        }
        
        // Next button
        if (this.state.currentPage < this.state.totalPages) {
            paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="AdZetaMediaLibrary.loadMedia(${this.state.currentPage + 1})">Next</a></li>`;
        }
        
        paginationHTML += '</ul></nav>';
        pagination.innerHTML = paginationHTML;
    }
};
