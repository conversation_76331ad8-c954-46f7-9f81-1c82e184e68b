/* Value-Based Bidding Section Styles - Apple-inspired design */

.vbb-section {
    background: #f5f5f7;
    padding: 100px 0 30px;
    overflow: hidden;
    position: relative;
}

.vbb-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at 10% 10%, rgba(233, 88, 161, 0.02) 0%, transparent 30%),
                      radial-gradient(circle at 90% 20%, rgba(143, 118, 245, 0.02) 0%, transparent 30%),
                      radial-gradient(circle at 50% 80%, rgba(74, 158, 255, 0.02) 0%, transparent 30%);
    z-index: 0;
}

.unified-value-card,.vbb-content-container {
    position: relative;
    z-index: 2;
    background: #fffdfc;
    border-radius: 24px;
 /*    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.04); */
    padding: 50px;
    margin-top: 30px;
 /*    border: 1px solid rgba(0, 0, 0, 0.03); */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.vbb-content-container::before {
    content: '';
    position: absolute;
    top: -15%;
    left: 35%;
    width: 30%;
    height: 100%;
    background-image: url('../images/google-bg-modified.svg');
    background-repeat: no-repeat;
    background-position: top center;
    background-size: contain;
    z-index: 1;
    pointer-events: none;
}


.vo-section .vbb-content-container::before {
    content: '';
    background-image: url('../images/facebook-bg-modified.svg');
   

}

.vbb-content-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 70%);
    z-index: 0;
    pointer-events: none;
}

.vbb-content-container:hover,.unified-value-card:hover {
    transform: translateY(-5px);
 /*    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.06); */
}

.vbb-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    padding-right: 30px;
    position: relative;
    z-index: 2;
}

.vbb-content h2 {
    font-size: 32px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 30px;
    line-height: 1.2;
    letter-spacing: -0.5px;
}

.vbb-content p {
    font-size: 15px;
    line-height: 1.7;
    color: #555;
    margin-bottom: 24px;
    font-weight: 400;
    letter-spacing: -0.2px;
}

.vbb-visual {
    height: 100%;
    position: relative;
    overflow: hidden;
    z-index: 2;
}

@keyframes cardAppear {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.98);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.vbb-comparison {
    width: 100%;
    position: relative;
    z-index: 1;
}

/* Google logo - repositioned for Apple-style */
.google-logo {
    position: absolute;
    top: 30px;
    left: 40px;
    width: 28px;
    height: 28px;
    opacity: 0.9;
}

/* Bidding strategies layout */
.bidding-strategies {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.strategy-row {
    display: flex;
    margin-bottom: 20px;
    align-items: center;
}

.strategy-label {
    width: 180px;
    font-size: 14px;
    color: #333;
    font-weight: 600;
    text-align: right;
    padding-right: 10px;
    letter-spacing: -0.3px;
    position: relative;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 40px;
}

.strategy-label:after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    transform: translateY(-50%);
    opacity: 1;
}

.strategy-label.tcpa {
    color: #4a9eff;
    font-weight: 600;
}

.strategy-label.tcpa:after {
    background: #4a9eff;
}

.strategy-label.vbb {
    color: #e958a1;
    font-weight: 600;
}

.strategy-label.vbb:after {
    background: #e958a1;
}

.customers-row {
    display: flex;
    justify-content: space-between;
    width: calc(100% - 180px);
    margin-left: auto;
}

.customer-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 8px;
    position: relative;
}

.customer-photo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 8px;
    background: #fffdfc;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 2px solid #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.customer-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.customer-info:hover .customer-photo img {
    transform: scale(1.05);
}

.customer-info.highlight .customer-photo {
    border: 2px solid rgba(233, 88, 161, 0.3);
}

.customer-photo i {
    font-size: 24px;
    color: #666;
}

.customer-info {
    text-align: center;
    margin-bottom: 15px;
    background: #f2f0ee;
    border-radius: 12px;
    padding: 16px 10px 14px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.02);
    width: 100%;
    position: relative;
    overflow: hidden;
}

.customer-info:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.04);
}

.customer-info.highlight {
    background: #fffdfc;
    box-shadow: 0 6px 16px rgba(243, 90, 119, 0.08);
    border: 1px solid rgba(243, 90, 119, 0.1);
}

.customer-info.highlight:hover {
    box-shadow: 0 10px 20px rgba(243, 90, 119, 0.12);
}

.customer-column-highlight .bid-box {
    background: #fffdfc;
    box-shadow: 0 4px 12px rgba(243, 90, 119, 0.06);
    border: 1px solid rgba(243, 90, 119, 0.1);
}

.customer-column-highlight:before {
    content: '';
    position: absolute;
    top: 20px;
    right: 15px;
    width: 24px;
    height: 24px;
    background: #e958a1;
    border-radius: 50%;
    box-shadow: 0 4px 8px rgba(233, 88, 161, 0.3);
    z-index: 2;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 14px;
    transform: translateY(-50%);
}

.customer-title {
    font-size: 15px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 0;
    letter-spacing: -0.3px;
    line-height: 1.2;
}

.customer-value {
    font-size: 14px;
    font-weight: 500;
    color: #666;
    letter-spacing: -0.2px;
    line-height: 1.2;
    margin-top: 1px;
}

/* Bid amounts */
.bid-row {
    display: flex;
    justify-content: space-between;
    width: calc(100% - 180px);
    margin-left: auto;
}

.bid-box {
    flex: 1;
    background: #f2f0ee;
    border-radius: 12px;
    padding: 16px 10px;
    text-align: center;
    margin: 0 8px;
    font-size: 15px;
    font-weight: 500;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.02);
    width: 100%;
    position: relative;
    overflow: hidden;
}

/* Add subtle gradient to bid boxes */
.bid-box:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
    opacity: 0.8;
    z-index: 0;
    pointer-events: none;
}

/* Make bid label and amount appear above the gradient */
.bid-label, .bid-amount {
    position: relative;
    z-index: 1;
}

.bid-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.04);
}

.bid-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
    margin-right: 6px;
    letter-spacing: -0.2px;
}

.bid-amount {
    font-weight: 600;
    letter-spacing: -0.3px;
    position: relative;
}

/* Strategy-specific bid box styles */
.bid-box.lower {
    background: #f2f0ee;
    color: #e958a1;
}

.bid-box.lower:hover {
    box-shadow: 0 8px 16px rgba(233, 88, 161, 0.08);
}

.bid-box.lower .bid-amount:after {
    content: '↓';
    display: inline-block;
    margin-left: 3px;
    font-size: 14px;
    animation: pulse 2s infinite;
}

.bid-box.same {
    background: #f2f0ee;
    color: #1a1a1a;
}

.bid-box.higher {
    background: #f2f0ee;
    color: #4a9eff;
}

.bid-box.higher:hover {
    box-shadow: 0 8px 16px rgba(74, 158, 255, 0.08);
}

.bid-box.higher .bid-amount:after {
    content: '↑';
    display: inline-block;
    margin-left: 3px;
    font-size: 14px;
    animation: pulse 2s infinite;
}

.bid-box.highlight {
    background: #fffdfc;
    box-shadow: 0 6px 16px rgba(243, 90, 119, 0.06);
    border: 1px solid rgba(243, 90, 119, 0.1);
}




.bid-box.highlight:hover {
    box-shadow: 0 10px 20px rgba(243, 90, 119, 0.1);
}

.bid-arrow {
    font-size: 10px;
    margin-left: 5px;
    display: inline-block;
    animation: pulse 2s infinite;
    position: relative;
}



@keyframes pulse {
    0% {
        opacity: 0.6;
        transform: scale(1) translateY(0);
    }
    50% {
        opacity: 1;
        transform: scale(1.1) translateY(-2px);
    }
    100% {
        opacity: 0.6;
        transform: scale(1) translateY(0);
    }
}



/* Responsive styles */
@media (max-width: 991px) {
    .vbb-section {
        padding: 80px 0 40px;
    }

    .vbb-content-container {
        padding: 30px;
    }

    .vbb-content-container::before {
        top: -5%;
        left: 25%;
        width: 45%;
    }

    .vbb-content-container::after {
        background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 60%);
    }

    .vbb-content {
        margin-bottom: 40px;
    }

    .vbb-content h2 {
        font-size: 28px;
    }

    .strategy-label {
        width: 140px;
        font-size: 13px;
    }

    .customers-row, .bid-row {
        width: calc(100% - 140px);
    }
}

@media (max-width: 767px) {
    .vbb-section {
        padding: 50px 0 30px;
    }

    .vbb-content-container {
        padding: 25px;
        margin-top: 20px;
    }

    .vbb-content-container::before {
        top: -5%;
        left: 20%;
        width: 60%;
    }

    .vbb-content-container::after {
        background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 50%);
    }

    .vbb-content h2 {
        font-size: 22px;
    }

    .vbb-content p {
        font-size: 15px;
    }

    .strategy-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .strategy-label {
        width: 100%;
        margin-bottom: 15px;
        text-align: left;
        justify-content: flex-start;
        padding-right: 0;
        padding-left: 15px;
    }

    .strategy-label:after {
        left: 0;
        right: auto;
    }

    .customers-row, .bid-row {
        width: 100%;
    }

    .customer-photo {
        width: 50px;
        height: 50px;
    }

    .customer-title {
        font-size: 13px;
    }

    .customer-value {
        font-size: 12px;
    }

    .bid-box {
        padding: 12px 10px;
        font-size: 13px;
        margin: 0 5px;
    }

    .bid-label {
        font-size: 12px;
    }

    .customer-column-highlight:before {
        top: 0;
        right: 5px;
        width: 20px;
        height: 20px;
        background-size: 12px;
    }
}

@media (max-width: 575px) {
    .unified-value-card,.vbb-content-container {
        padding: 20px;
        border-radius: 16px;
    }
	

    .customer-photo {
        width: 45px;
        height: 45px;
        margin-bottom: 6px;
    }

    .customer-photo i {
        font-size: 18px;
    }

    .customer-info {
        padding: 12px 8px 10px;
        border-radius: 10px;
    }

    .customer-title {
        font-size: 12px;
    }

    .customer-value {
        font-size: 11px;
    }

    .bid-box {
        padding: 12px 8px;
        font-size: 12px;
        margin: 0 3px;
        border-radius: 10px;
    }

    .bid-label {
        font-size: 10px;
        margin-right: 3px;
    }

    .customer-column {
        padding: 0 4px;
    }
}
