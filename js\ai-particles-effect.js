/**
 * AI Particles Effect
 * Creates an interactive particle network with AI-themed animations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if particles container exists
    const particlesContainer = document.getElementById('particles-style-03');
    if (!particlesContainer) return;

    // Initialize particles.js if available
    if (typeof particlesJS !== 'undefined') {
        const options = particlesContainer.getAttribute('data-particle-options');
        if (options) {
            try {
                const parsedOptions = JSON.parse(options);
                particlesJS('particles-style-03', parsedOptions);
            } catch (e) {
                console.error('Error parsing particle options:', e);
            }
        }
    }

    // Add data flow lines for AI effect - DISABLED to remove horizontal lines
    const addDataFlowEffect = () => {
        // Function disabled to remove horizontal lines
        // We're keeping the empty function to avoid breaking any code that calls it
    };

    // Add particle burst effect on click
    const addParticleBurst = () => {
        particlesContainer.addEventListener('click', function(e) {
            const rect = particlesContainer.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Create multiple burst particles for more dramatic effect
            for (let i = 0; i < 8; i++) {
                setTimeout(() => {
                    // Create burst particle
                    const burst = document.createElement('div');
                    burst.className = 'particle-burst';

                    // Add slight randomness to position
                    const offsetX = (Math.random() - 0.5) * 40;
                    const offsetY = (Math.random() - 0.5) * 40;
                    burst.style.left = `${x + offsetX}px`;
                    burst.style.top = `${y + offsetY}px`;

                    // Random color from theme
                    const colors = ['#e958a1', '#ff7042', '#d15ec7', '#8f76f5'];
                    const randomColor = colors[Math.floor(Math.random() * colors.length)];
                    burst.style.background = randomColor;
                    burst.style.boxShadow = `0 0 15px ${randomColor}, 0 0 30px ${randomColor}`;

                    // Random size for variety
                    const size = 10 + Math.random() * 20;
                    burst.style.width = `${size}px`;
                    burst.style.height = `${size}px`;

                    particlesContainer.appendChild(burst);

                    // Remove after animation completes (faster animation)
                    setTimeout(() => {
                        burst.remove();
                    }, 1500 + Math.random() * 500);
                }, i * 50); // Stagger the creation of particles
            }
        });

        // Also add random auto-bursts for more dynamic effect
        const addRandomBurst = () => {
            if (!document.hidden) {
                const x = Math.random() * particlesContainer.offsetWidth;
                const y = Math.random() * particlesContainer.offsetHeight;

                // Create burst particle
                const burst = document.createElement('div');
                burst.className = 'particle-burst';
                burst.style.left = `${x}px`;
                burst.style.top = `${y}px`;

                // Random color from theme
                const colors = ['#e958a1', '#ff7042', '#d15ec7', '#8f76f5'];
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                burst.style.background = randomColor;
                burst.style.boxShadow = `0 0 10px ${randomColor}`;

                // Smaller size for auto-bursts
                const size = 5 + Math.random() * 10;
                burst.style.width = `${size}px`;
                burst.style.height = `${size}px`;

                particlesContainer.appendChild(burst);

                // Remove after animation completes
                setTimeout(() => {
                    burst.remove();
                }, 1000 + Math.random() * 500);
            }

            // Schedule next burst with longer delay for more subtle effect
            setTimeout(addRandomBurst, 4000 + Math.random() * 5000);
        };

        // Start random bursts
        addRandomBurst();
    };

    // Add enhanced glow effect to particles
    const addGlowEffect = () => {
        // Add glow class to particles container
        particlesContainer.classList.add('ai-particles-glow');

        // Add pulsating glow effect
        const addPulsatingGlow = () => {
            // Create glow overlay
            const glowOverlay = document.createElement('div');
            glowOverlay.className = 'particles-glow-overlay';
            glowOverlay.style.position = 'absolute';
            glowOverlay.style.top = '0';
            glowOverlay.style.left = '0';
            glowOverlay.style.width = '100%';
            glowOverlay.style.height = '100%';
            glowOverlay.style.pointerEvents = 'none';
            glowOverlay.style.zIndex = '1';

            // Add radial gradient with theme colors
            const colors = ['#e958a1', '#ff7042', '#d15ec7', '#8f76f5'];
            const randomColor1 = colors[Math.floor(Math.random() * colors.length)];
            const randomColor2 = colors[Math.floor(Math.random() * colors.length)];

            glowOverlay.style.background = `radial-gradient(circle at ${Math.random() * 100}% ${Math.random() * 100}%,
                                            ${randomColor1}22 0%,
                                            ${randomColor2}11 30%,
                                            transparent 70%)`;

            glowOverlay.style.animation = 'pulse-glow 8s infinite alternate ease-in-out';
            glowOverlay.style.mixBlendMode = 'screen';
            glowOverlay.style.opacity = '0.4';

            particlesContainer.appendChild(glowOverlay);

            // Create style for animation if it doesn't exist
            if (!document.getElementById('glow-animation-style')) {
                const style = document.createElement('style');
                style.id = 'glow-animation-style';
                style.textContent = `
                    @keyframes pulse-glow {
                        0% { opacity: 0.2; transform: scale(0.95); }
                        50% { opacity: 0.5; transform: scale(1.05); }
                        100% { opacity: 0.3; transform: scale(1); }
                    }
                `;
                document.head.appendChild(style);
            }

            // Remove and recreate periodically for variety
            setTimeout(() => {
                glowOverlay.remove();
                if (document.contains(particlesContainer)) {
                    addPulsatingGlow();
                }
            }, 8000);
        };

        // Start pulsating glow
        addPulsatingGlow();
    };

    // Initialize effects
    addDataFlowEffect();
    addParticleBurst();
    addGlowEffect();

    // Refresh data flow on window resize
    window.addEventListener('resize', addDataFlowEffect);
});
