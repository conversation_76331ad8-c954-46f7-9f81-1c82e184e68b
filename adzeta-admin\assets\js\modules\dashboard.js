/**
 * AdZeta Admin Panel - Dashboard Module
 * Handles dashboard data loading and display
 */

window.AdZetaDashboard = {
    // Dashboard data
    data: {
        stats: null,
        recentPosts: null,
        loading: false
    },

    // Initialize dashboard module
    init() {
        console.log('Dashboard module initialized');
    },

    // Load dashboard data
    async load() {
        if (this.data.loading) return;

        try {
            this.data.loading = true;
            this.showLoading();

            // Load stats and recent posts
            await Promise.all([
                this.loadStats(),
                this.loadRecentPosts()
            ]);

            this.render();
        } catch (error) {
            console.error('Error loading dashboard:', error);
            this.showError('Failed to load dashboard data');
        } finally {
            this.data.loading = false;
            this.hideLoading();
        }
    },

    // Load dashboard statistics
    async loadStats() {
        try {
            console.log('Loading dashboard stats...');
            const response = await window.AdZetaApp.apiRequest('/dashboard/stats');
            console.log('Dashboard stats response:', response);

            if (response.success) {
                this.data.stats = response.stats;
                this.data.recentPosts = response.recent_posts || [];
                console.log('Dashboard data loaded successfully:', this.data);
            } else {
                throw new Error(response.message || 'Failed to load dashboard stats');
            }
        } catch (error) {
            console.error('Error loading stats:', error);
            throw error;
        }
    },

    // Load recent posts (now combined with stats)
    async loadRecentPosts() {
        // Recent posts are now loaded with stats
        return Promise.resolve();
    },

    // Render dashboard content
    render() {
        this.renderStats();
        this.renderRecentPosts();
    },

    // Render statistics cards
    renderStats() {
        const container = document.getElementById('dashboardView');
        if (!container || !this.data.stats) return;

        const statsHTML = `
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stats-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="h2 mb-0 text-primary">${this.data.stats.posts.total}</h3>
                                    <p class="text-muted mb-0">Blog Posts</p>
                                    <small class="text-success">
                                        <i class="fas fa-arrow-up me-1"></i>
                                        ${this.data.stats.posts.published} published
                                    </small>
                                </div>
                                <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                                    <i class="fas fa-edit"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stats-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="h2 mb-0 text-success">${this.data.stats.tags || 0}</h3>
                                    <p class="text-muted mb-0">Tags</p>
                                    <small class="text-success">
                                        <i class="fas fa-tags me-1"></i>
                                        Content tags
                                    </small>
                                </div>
                                <div class="stats-icon bg-success bg-opacity-10 text-success">
                                    <i class="fas fa-tags"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stats-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="h2 mb-0 text-info">${this.data.stats.categories}</h3>
                                    <p class="text-muted mb-0">Categories</p>
                                    <small class="text-muted">
                                        <i class="fas fa-folder me-1"></i>
                                        Blog categories
                                    </small>
                                </div>
                                <div class="stats-icon bg-info bg-opacity-10 text-info">
                                    <i class="fas fa-folder"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="card stats-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="h2 mb-0 text-warning">${this.data.stats.media?.count || 0}</h3>
                                    <p class="text-muted mb-0">Media Files</p>
                                    <small class="text-muted">
                                        <i class="fas fa-hdd me-1"></i>
                                        ${this.data.stats.media?.size_formatted || '0 B'}
                                    </small>
                                </div>
                                <div class="stats-icon bg-warning bg-opacity-10 text-warning">
                                    <i class="fas fa-images"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = statsHTML + (container.innerHTML.includes('recent-posts-card') ? container.innerHTML.split('recent-posts-card')[1] : '');
    },

    // Render recent posts
    renderRecentPosts() {
        const container = document.getElementById('dashboardView');
        if (!container || !this.data.recentPosts) return;

        const recentPostsHTML = `
            <div class="card recent-posts-card">
                <div class="card-header">
                    <h3 class="card-title">Recent Posts</h3>
                    <button class="btn btn-primary btn-sm" onclick="AdZetaNavigation.navigateTo('posts')">
                        <i class="fas fa-arrow-right me-1"></i>
                        View All Posts
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>SEO Score</th>
                                    <th>Date</th>
                                    <th width="100">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${this.data.recentPosts.map(post => `
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>${post.title || 'Untitled'}</strong>
                                                <div class="text-muted small">${post.excerpt || 'No excerpt available'}</div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge ${this.getStatusBadgeClass(post.status)}">
                                                ${post.status || 'draft'}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="progress me-2" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar ${this.getSEOProgressClass(post.seo_score || 0)}"
                                                         style="width: ${post.seo_score || 0}%"></div>
                                                </div>
                                                <small class="text-muted">${post.seo_score || 0}%</small>
                                            </div>
                                        </td>
                                        <td class="text-muted">
                                            ${window.AdZetaApp.formatDate(post.created_at)}
                                        </td>
                                        <td>
                                            <button class="btn btn-outline-primary btn-sm"
                                                    onclick="AdZetaDashboard.editPost(${post.id})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        // Append recent posts to existing content
        const existingContent = container.innerHTML;
        if (existingContent.includes('recent-posts-card')) {
            // Replace existing recent posts
            container.innerHTML = existingContent.split('recent-posts-card')[0] + recentPostsHTML;
        } else {
            // Add recent posts
            container.innerHTML += recentPostsHTML;
        }
    },

    // Get status badge class
    getStatusBadgeClass(status) {
        const classes = {
            published: 'badge-success',
            draft: 'badge-warning',
            archived: 'badge-secondary'
        };
        return classes[status] || 'badge-secondary';
    },

    // Get SEO progress bar class
    getSEOProgressClass(score) {
        if (score >= 80) return 'bg-success';
        if (score >= 60) return 'bg-info';
        if (score >= 40) return 'bg-warning';
        return 'bg-danger';
    },

    // Show loading state
    showLoading() {
        const container = document.getElementById('dashboardView');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner mb-3"></div>
                    <p class="text-muted">Loading dashboard...</p>
                </div>
            `;
        }
    },

    // Hide loading state
    hideLoading() {
        // Loading state is replaced by actual content in render()
    },

    // Show error message
    showError(message) {
        const container = document.getElementById('dashboardView');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                    <button class="btn btn-outline-danger btn-sm ms-3" onclick="AdZetaDashboard.load()">
                        <i class="fas fa-sync me-1"></i>
                        Retry
                    </button>
                </div>
            `;
        }
    },

    // Refresh dashboard data
    async refresh() {
        await this.load();
        window.AdZetaApp.showNotification('Dashboard refreshed successfully', 'success');
    },

    // Get dashboard stats
    getStats() {
        return this.data.stats;
    },

    // Get recent posts
    getRecentPosts() {
        return this.data.recentPosts;
    },

    // Edit post with proper navigation handling
    editPost(postId) {
        console.log(`Dashboard: Editing post ${postId} - updating navigation state to Add Post`);

        // Navigate to posts view first to ensure posts module is loaded
        window.AdZetaNavigation.showView('posts');

        // Delay to ensure posts view is loaded, then open editor
        setTimeout(() => {
            // Update URL to reflect the edit action
            window.AdZetaNavigation.updateURLWithAction('posts', 'edit', postId);

            // Set "Add Post" as active when editing (since editing is similar to creating)
            window.AdZetaNavigation.updateActiveNavLink('add-post');

            // Open the post editor
            if (window.AdZetaPostEditor) {
                window.AdZetaPostEditor.edit(postId);
            } else {
                console.error('AdZetaPostEditor not available');
            }
        }, 200);
    }
};
