<?php
/**
 * Homepage CTA Form Handler
 * Modern PHP form processing for "Talk to an Expert" form
 */

// Include configuration
require_once 'config.php';

// Security headers
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Rate limiting check
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
if (!checkRateLimit($clientIP)) {
    http_response_code(429);
    echo json_encode(['success' => false, 'message' => 'Too many submissions. Please try again later.']);
    exit;
}

// CSRF Protection (basic)
session_start();
if (!isset($_SESSION['form_token'])) {
    $_SESSION['form_token'] = bin2hex(random_bytes(32));
}

// Input validation and sanitization
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Collect and validate form data
$errors = [];
$data = [];

// Required fields
$requiredFields = [
    'email' => 'Email Address'
];

foreach ($requiredFields as $field => $label) {
    if (empty($_POST[$field])) {
        $errors[] = "$label is required";
    } else {
        $data[$field] = sanitizeInput($_POST[$field]);
    }
}

// Optional fields
$optionalFields = ['name', 'cta_location'];
foreach ($optionalFields as $field) {
    $data[$field] = isset($_POST[$field]) ? sanitizeInput($_POST[$field]) : '';
}

// Specific validations
if (!empty($data['email']) && !validateEmail($data['email'])) {
    $errors[] = 'Please enter a valid email address';
}

// Check for errors
if (!empty($errors)) {
    echo json_encode([
        'success' => false,
        'message' => 'Please fix the following errors: ' . implode(', ', $errors)
    ]);
    exit;
}

// Additional data
$data['ip_address'] = $clientIP;
$data['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
$data['submitted_at'] = date('Y-m-d H:i:s');
$data['form_type'] = 'homepage_cta';
$data['cta_location'] = $data['cta_location'] ?: 'homepage_final_cta';

// Email configuration
$subject = 'New Expert Consultation Request - ' . ($data['name'] ?: 'Website Visitor');

// Create email content using template
$emailContent = getEmailTemplate('homepage_cta', $data);

// Send email
$emailSent = sendFormEmail(ADMIN_EMAIL, $subject, $emailContent, $data['email']);

// Log submission
logSubmission($data);

// Return response
if ($emailSent) {
    echo json_encode([
        'success' => true,
        'message' => 'Thank you! We\'ve received your request and will contact you shortly to discuss how we can help grow your business.'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Sorry, there was an error submitting your request. Please try again or contact us directly.'
    ]);
}
?>
