-- Migration: Add Challenge and Solution editable fields
-- Date: 2025-06-30
-- Description: Add funnel content, challenge points, and A/B test fields

-- Challenge funnel fields
ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_funnel_without_title VARCHAR(255) DEFAULT NULL 
AFTER challenge_description;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_funnel_without_description VARCHAR(255) DEFAULT NULL 
AFTER challenge_funnel_without_title;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_funnel_without_result VARCHAR(255) DEFAULT NULL 
AFTER challenge_funnel_without_description;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_funnel_with_title VARCHAR(255) DEFAULT NULL 
AFTER challenge_funnel_without_result;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_funnel_with_description VARCHAR(255) DEFAULT NULL 
AFTER challenge_funnel_with_title;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_funnel_with_result VARCHAR(255) DEFAULT NULL 
AFTER challenge_funnel_with_description;

-- Challenge points fields
ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_point_1_title VARCHAR(255) DEFAULT NULL 
AFTER challenge_funnel_with_result;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_point_1_description LONGTEXT DEFAULT NULL 
AFTER challenge_point_1_title;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_point_2_title VARCHAR(255) DEFAULT NULL 
AFTER challenge_point_1_description;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_point_2_description LONGTEXT DEFAULT NULL 
AFTER challenge_point_2_title;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_point_3_title VARCHAR(255) DEFAULT NULL 
AFTER challenge_point_2_description;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS challenge_point_3_description LONGTEXT DEFAULT NULL 
AFTER challenge_point_3_title;

-- Solution A/B test fields
ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS solution_ab_test_title VARCHAR(255) DEFAULT NULL 
AFTER solution_point_4_description;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS solution_ab_test_control_title VARCHAR(255) DEFAULT NULL 
AFTER solution_ab_test_title;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS solution_ab_test_control_description LONGTEXT DEFAULT NULL 
AFTER solution_ab_test_control_title;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS solution_ab_test_experiment_title VARCHAR(255) DEFAULT NULL 
AFTER solution_ab_test_control_description;

ALTER TABLE case_studies 
ADD COLUMN IF NOT EXISTS solution_ab_test_experiment_description LONGTEXT DEFAULT NULL 
AFTER solution_ab_test_experiment_title;

-- Verify the columns were added
SELECT 'Migration completed. Challenge and Solution fields added to case_studies table.' as status;
