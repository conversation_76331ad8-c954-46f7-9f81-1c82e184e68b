/* Clean Card Metrics Styles - Based on the provided image */
.metrics-section {
    padding: 80px 0;
    background-color: #fafafa;
    position: relative;
    overflow: hidden;
}

/* Clean metric card styles */
.metric-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
    padding: 25px;
    height: 100%;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

/* Card header */
.metric-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.metric-title {
    font-size: 14px;
    font-weight: 600;
    color: #555;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-badge {
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 20px;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
}

.metric-badge i {
    margin-right: 4px;
    font-size: 10px;
}

/* Metric value styling */
.metric-value-container {
    margin-bottom: 20px;
}

.metric-value {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.counter-number {
    font-size: 48px;
    line-height: 1;
    font-weight: 700;
}

.trend {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.trend i {
    margin-right: 3px;
}

.trend-up {
    color: #2ecc71;
}

.trend-down {
    color: #3498db;
}

.metric-description {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
    margin-bottom: 0;
}

/* Mini chart container */
.mini-chart {
    height: 60px;
    position: relative;
    margin-top: auto;
}

/* Color variations - matching the image */
.col-lg-3:nth-child(1) .metric-card {
 
}

.col-lg-3:nth-child(2) .metric-card {

}

.col-lg-3:nth-child(3) .metric-card {

}

.col-lg-3:nth-child(4) .metric-card {
   
}

.col-lg-3:nth-child(1) .counter-number {
    color: #8f76f5;
}

.col-lg-3:nth-child(2) .counter-number {
    color: #e958a1;
}

.col-lg-3:nth-child(3) .counter-number {
    color: #8f76f5;
}

.col-lg-3:nth-child(4) .counter-number {
    color: #e958a1;
}

.col-lg-3:nth-child(1) .metric-badge,
.col-lg-3:nth-child(3) .metric-badge {
    background-color: rgba(143, 118, 245, 0.1);
    color: #8f76f5;
}

.col-lg-3:nth-child(2) .metric-badge,
.col-lg-3:nth-child(4) .metric-badge {
    background-color: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .metrics-section {
        padding: 60px 0;
    }

    .metric-card {
        margin-bottom: 20px;
    }

    .counter-number {
        font-size: 42px;
    }
}

@media (max-width: 767px) {
    .metrics-section {
        padding: 40px 0;
    }

    .counter-number {
        font-size: 36px;
    }
}
