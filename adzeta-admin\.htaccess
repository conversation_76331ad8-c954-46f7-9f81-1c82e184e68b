RewriteEngine On

# Security: Block access to sensitive files
<Files "bootstrap.php">
    Order allow,deny
    Deny from all
</Files>

<FilesMatch "\.(log|sql|md)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes

# Route legacy media API URLs to modern system
RewriteRule ^api/media/upload\.php$ api/index.php [QSA,L]
RewriteRule ^api/media/upload-by-url\.php$ api/index.php [QSA,L]
RewriteRule ^api/media/index\.php$ api/index.php [QSA,L]

# Allow direct access to users API PHP files (keep for now)
RewriteRule ^api/users/index\.php$ api/users/index.php [L]

# Route all other API requests to api/index.php
RewriteCond %{REQUEST_URI} ^/adzeta-admin/api/
RewriteCond %{REQUEST_URI} !^/adzeta-admin/api/users/.*\.php$
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# Route all other requests to index.php (except existing files/directories)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/adzeta-admin/api/
RewriteRule ^(.*)$ index.php [QSA,L]

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
