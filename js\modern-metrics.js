// Modern Metrics Charts and Animations
document.addEventListener('DOMContentLoaded', function() {
    // Function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // Initialize mini charts
    function initMiniCharts() {
        // ROAS Chart - Upward trend
        const roasChartEl = document.getElementById('roas-chart');
        if (roasChartEl) {
            const roasChart = new Chart(roasChartEl, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'ROAS',
                        data: [1.2, 1.8, 2.3, 2.9, 3.5, 4.0],
                        borderColor: '#2ecc71',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 3,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return context.parsed.y + 'x';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false,
                            min: 0
                        }
                    }
                }
            });
        }

        // CAC Chart - Downward trend
        const cacChartEl = document.getElementById('cac-chart');
        if (cacChartEl) {
            const cacChart = new Chart(cacChartEl, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'CAC',
                        data: [100, 90, 82, 75, 65, 64],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 3,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return '$' + context.parsed.y;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false,
                            reverse: true
                        }
                    }
                }
            });
        }

        // Revenue Chart - Upward trend
        const revenueChartEl = document.getElementById('revenue-chart');
        if (revenueChartEl) {
            const revenueChart = new Chart(revenueChartEl, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Revenue',
                        data: [100, 110, 115, 122, 125, 128],
                        borderColor: '#2ecc71',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 3,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return '+' + context.parsed.y + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false,
                            min: 95
                        }
                    }
                }
            });
        }

        // Ad Waste Chart - Downward trend
        const adWasteChartEl = document.getElementById('ad-waste-chart');
        if (adWasteChartEl) {
            const adWasteChart = new Chart(adWasteChartEl, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Ad Waste',
                        data: [100, 90, 80, 70, 60, 58],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 2,
                        pointRadius: 0,
                        pointHoverRadius: 3,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return '-' + context.parsed.y + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false,
                            reverse: true
                        }
                    }
                }
            });
        }
    }

    // Initialize comparison chart
    function initComparisonChart() {
        const comparisonChartEl = document.getElementById('comparison-chart');
        if (comparisonChartEl) {
            const comparisonChart = new Chart(comparisonChartEl, {
                type: 'bar',
                data: {
                    labels: ['Traditional', 'Adzeta AI'],
                    datasets: [{
                        label: 'ROAS',
                        data: [1, 4],
                        backgroundColor: [
                            'rgba(149, 165, 166, 0.7)',
                            'rgba(143, 118, 245, 0.7)'
                        ],
                        borderColor: [
                            'rgba(149, 165, 166, 1)',
                            'rgba(143, 118, 245, 1)'
                        ],
                        borderWidth: 1,
                        borderRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.parsed.y + 'x ROAS';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value + 'x';
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    // Initialize donut chart
    function initDonutChart() {
        const donutChartEl = document.getElementById('donut-chart');
        if (donutChartEl) {
            const donutChart = new Chart(donutChartEl, {
                type: 'doughnut',
                data: {
                    labels: ['Wasted Spend', 'Effective Spend'],
                    datasets: [{
                        data: [42, 58],
                        backgroundColor: [
                            'rgba(231, 76, 60, 0.7)',
                            'rgba(46, 204, 113, 0.7)'
                        ],
                        borderColor: [
                            'rgba(231, 76, 60, 1)',
                            'rgba(46, 204, 113, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    // Animate counter numbers
    function animateCounters() {
        const counters = document.querySelectorAll('.counter-number');
        
        counters.forEach(counter => {
            const target = +counter.getAttribute('data-target');
            const duration = 1500;
            const startTime = performance.now();
            const unit = counter.getAttribute('data-unit') || '';
            
            function updateCounter(currentTime) {
                const elapsedTime = currentTime - startTime;
                const progress = Math.min(elapsedTime / duration, 1);
                const easedProgress = easeOutQuad(progress);
                const currentValue = Math.floor(easedProgress * target);
                
                counter.textContent = currentValue + unit;
                
                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target + unit;
                }
            }
            
            requestAnimationFrame(updateCounter);
        });
    }
    
    // Easing function
    function easeOutQuad(t) {
        return t * (2 - t);
    }

    // Handle testimonial navigation
    function setupTestimonialNav() {
        const prevBtn = document.querySelector('.testimonial-prev');
        const nextBtn = document.querySelector('.testimonial-next');
        const testimonials = document.querySelectorAll('.testimonial-item');
        let currentIndex = 0;
        
        if (!prevBtn || !nextBtn || testimonials.length === 0) return;
        
        // Hide all testimonials except the first one
        testimonials.forEach((testimonial, index) => {
            if (index !== 0) {
                testimonial.style.display = 'none';
            }
        });
        
        prevBtn.addEventListener('click', () => {
            testimonials[currentIndex].style.display = 'none';
            currentIndex = (currentIndex - 1 + testimonials.length) % testimonials.length;
            testimonials[currentIndex].style.display = 'block';
        });
        
        nextBtn.addEventListener('click', () => {
            testimonials[currentIndex].style.display = 'none';
            currentIndex = (currentIndex + 1) % testimonials.length;
            testimonials[currentIndex].style.display = 'block';
        });
    }

    // Check if metrics section is in viewport and initialize
    function checkAndInitialize() {
        const metricsSection = document.querySelector('.metrics-section');
        
        if (metricsSection && isInViewport(metricsSection)) {
            // Initialize all charts
            initMiniCharts();
            initComparisonChart();
            initDonutChart();
            
            // Animate counters
            animateCounters();
            
            // Remove scroll listener once initialized
            window.removeEventListener('scroll', checkAndInitialize);
        }
    }

    // Initialize testimonial navigation
    setupTestimonialNav();
    
    // Check on scroll and on load
    window.addEventListener('scroll', checkAndInitialize);
    window.addEventListener('load', checkAndInitialize);
});
