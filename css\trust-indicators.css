/* Trust Indicators Section Styles */
.trust-indicators-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    background: #ffffff;
    padding: 30px 20px;
    border-radius: 16px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(143, 118, 245, 0.1);
}

.trust-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 260px;
    text-align: center;
}

.partner-badge-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.partner-badge-container {
    position: relative;
    text-align: center;
    margin-bottom: 10px;
}

.partner-badge {
    height: 80px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.15));
}

.partner-badge:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 8px 16px rgba(0,0,0,0.2));
}

.badge-tooltip {
    position: absolute;
    bottom: 120%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.85);
    color: #fff;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    width: 220px;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s ease;
    z-index: 10;
}

.badge-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 8px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.85);
}

.partner-badge-container:hover .badge-tooltip {
    opacity: 1;
    visibility: visible;
}

.verification-text {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.partner-badge-wrapper .verification-text {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(39, 174, 96, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid rgba(39, 174, 96, 0.2);
}

.verification-icon {
    color: #27AE60;
    margin-right: 5px;
    font-size: 16px;
}

.certification-badge,
.verification-badge {
    background: #fff;
    padding: 14px 20px;
    border-radius: 16px;
    border: 1px solid rgba(143, 118, 245, 0.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
}

.certification-badge:hover,
.verification-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(0,0,0,0.1);
}

.certification-icon,
.verification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    color: white;
    font-size: 20px;
    box-shadow: 0 6px 15px rgba(244, 88, 136, 0.25);
}

.certification-text,
.verification-text {
    display: flex;
    flex-direction: column;
}

.certification-title,
.verification-title {
    font-weight: 600;
    font-size: 16px;
    color: #333;
    margin-bottom: 3px;
    letter-spacing: -0.3px;
}

.certification-detail,
.verification-detail {
    font-size: 14px;
    color: #666;
    font-weight: 400;
}

.results-disclaimer {
    font-size: 13px;
    color: #777;
    font-style: italic;
    margin-top: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
    padding: 0 15px;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .trust-indicators-container {
        padding: 15px;
        gap: 20px;
    }

    .certification-badge,
    .verification-badge {
        padding: 10px 15px;
    }
}

@media (max-width: 767px) {
    .trust-indicators-container {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .trust-item {
        max-width: 100%;
        width: 100%;
    }
}
