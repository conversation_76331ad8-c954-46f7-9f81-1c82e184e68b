<?php
/**
 * Add Gemini Model Setting for Existing Installations
 * Run this script once to add the gemini model selection setting
 */

require_once __DIR__ . '/bootstrap.php';

try {
    $db = $GLOBALS['admin_db'];

    // Check if setting already exists
    $existing = $db->fetch(
        "SELECT setting_key FROM settings WHERE setting_key = ?",
        ['ai_gemini_model']
    );

    if (!$existing) {
        // Insert the new setting
        $db->insert('settings', [
            'setting_key' => 'ai_gemini_model',
            'setting_value' => 'gemini-1.5-flash',
            'setting_type' => 'string',
            'description' => 'Selected Gemini model for AI content generation'
        ]);

        echo "✅ Successfully added Gemini model setting (default: gemini-1.5-flash)\n";
    } else {
        echo "ℹ️  Gemini model setting already exists\n";
    }

    // Show current AI settings
    echo "\n📋 Current AI Settings:\n";
    $aiSettings = $db->fetchAll(
        "SELECT setting_key, setting_value, description
         FROM settings
         WHERE setting_key LIKE 'ai_%' OR setting_key = 'gemini_api_keys'
         ORDER BY setting_key"
    );

    foreach ($aiSettings as $setting) {
        echo "   • {$setting['setting_key']}: {$setting['setting_value']}\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
