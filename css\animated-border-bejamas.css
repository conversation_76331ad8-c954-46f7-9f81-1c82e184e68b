/**
 * Animated Gradient Border
 * Based on the Bejamas Experiments implementation
 */

/* High-tech animated border with masking technique */
.high-tech-border {
  --border-width: 1px;
  --radius: 20px;
  --angle: 0turn;

  position: relative;
  border-radius: var(--radius);
  border: var(--border-width) solid transparent;
  isolation: isolate;
}

.high-tech-border::before {
  content: "";
  position: absolute;
  inset: calc(var(--border-width) * -1);
  z-index: -1;
  border: inherit;
  border-radius: inherit;
  background-image: conic-gradient(from var(--angle), #e958a1, #ff5d74, #8f76f5, #e958a1);
  background-origin: border-box;
  -webkit-mask: linear-gradient(#000, #000),
                linear-gradient(#000, #000);  
  mask: linear-gradient(#000, #000),
        linear-gradient(#000, #000);
  -webkit-mask-clip: content-box, border-box;
  mask-clip: content-box, border-box;
  -webkit-mask-composite: xor;  
  mask-composite: exclude;
  animation: rotate-gradient 4s linear infinite;
}

/* Animation for rotating gradient */
@keyframes rotate-gradient {
  to {
    --angle: 1turn;
  }
}

/* Fallback for browsers that don't support @property */
@supports not (background: conic-gradient(from var(--angle), #e958a1, #ff5d74)) {
  .high-tech-border::before {
    background-image: conic-gradient(#e958a1, #ff5d74, #8f76f5, #e958a1);
    animation: rotate-fallback 4s linear infinite;
  }
  
  @keyframes rotate-fallback {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

/* Define the --angle property for browsers that support CSS Houdini */
@property --angle {
  syntax: "<angle>";
  inherits: true;
  initial-value: 0turn;
}

/* Ensure content is positioned correctly */
.high-tech-border > * {
  position: relative;
  z-index: 1;
}

/* Add subtle glow effect */
.high-tech-border {
  box-shadow: 0 0 15px rgba(233, 88, 161, 0.2);
}

/* Solution card specific styles */
.solution-card {
  background: rgba(255, 255, 255, 0.30);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  z-index: 1;
}

.solution-card:hover {
  transform: translateY(-4px);
}
