// AI Steps Animation and Interaction
document.addEventListener('DOMContentLoaded', function() {
    // Get all step items and step content elements
    const stepItems = document.querySelectorAll('.ai-step-item');
    const stepContents = document.querySelectorAll('.step-content');

    // Set first step as active by default
    if (stepItems.length > 0) {
        activateStep(1);
        animateFactorBars();
    }

    // Add event listeners to step items
    stepItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const stepNumber = this.getAttribute('data-step');
            activateStep(stepNumber);
        });
    });

    // Function to activate a step
    function activateStep(stepNumber) {
        // Remove active class from all step contents
        stepContents.forEach(content => {
            content.classList.remove('active');
        });

        // Add active class to the corresponding step content
        const activeContent = document.getElementById(`step-content-${stepNumber}`);
        if (activeContent) {
            activeContent.classList.add('active');

            // Animate factor bars if step 2 is active
            if (stepNumber === '2') {
                setTimeout(animateFactorBars, 300);
            }
        }

        // Highlight the active step item
        stepItems.forEach(item => {
            if (item.getAttribute('data-step') === stepNumber) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    // Function to animate factor bars in step 2
    function animateFactorBars() {
        const factorFills = document.querySelectorAll('.factor-fill');
        factorFills.forEach(fill => {
            const width = fill.style.width;
            fill.style.width = '0';
            setTimeout(() => {
                fill.style.width = width;
            }, 100);
        });
    }

    // Intersection Observer for animation on scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // When the section comes into view, activate the first step
                setTimeout(() => {
                    activateStep('1');
                }, 300);

                // Add animation class to the section
                entry.target.classList.add('animated');

                // Animate step items sequentially
                stepItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('animate');
                    }, index * 300 + 500);
                });

                // Auto-cycle through steps for initial presentation
                let currentStep = 1;
                const totalSteps = stepItems.length;

                const cycleInterval = setInterval(() => {
                    currentStep = currentStep % totalSteps + 1;
                    activateStep(currentStep.toString());

                    // Continue cycling indefinitely
                    if (currentStep === totalSteps) {
                        setTimeout(() => {
                            activateStep('1');
                        }, 5000);
                    }
                }, 5000); // Change step every 5 seconds

                // Store the interval ID in a data attribute so it can be cleared if needed
                entry.target.dataset.cycleInterval = cycleInterval;

                // Unobserve after animation is triggered
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.2 });

    // Observe the AI steps section
    const aiStepsSection = document.querySelector('.ai-steps-section');
    if (aiStepsSection) {
        observer.observe(aiStepsSection);
    }

    // Add hover effect to device frame
    const deviceFrame = document.querySelector('.device-frame');
    if (deviceFrame) {
        deviceFrame.addEventListener('mouseenter', function() {
            this.classList.add('hover');
        });

        deviceFrame.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });
    }
});
