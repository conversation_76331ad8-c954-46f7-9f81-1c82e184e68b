/**
 * Professional Comparison Section
 * Subtle, professional animations and interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Animate elements when they come into view with subtle timing
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.fade-up, .fade-left, .fade-right, .scale-up');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            // Check if element is in viewport
            if (elementTop < windowHeight * 0.85) {
                element.classList.add('active');
            }
        });
    };
    
    // Initial check for elements in viewport
    setTimeout(animateOnScroll, 100);
    
    // Listen for scroll events with throttling for performance
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        if (!scrollTimeout) {
            scrollTimeout = setTimeout(() => {
                animateOnScroll();
                scrollTimeout = null;
            }, 50);
        }
    });
    
    // Animate stats with counting effect and easing
    const animateStats = () => {
        const stats = document.querySelectorAll('.pro-stat-value');
        
        stats.forEach(stat => {
            const targetValue = parseFloat(stat.getAttribute('data-value'));
            const suffix = stat.getAttribute('data-suffix') || '';
            const duration = 2000; // 2 seconds
            const startTime = Date.now();
            const startValue = 0;
            const decimalPlaces = (targetValue % 1 !== 0) ? 1 : 0;
            
            const updateValue = () => {
                const currentTime = Date.now();
                const elapsed = currentTime - startTime;
                
                if (elapsed < duration) {
                    const value = easeOutQuart(elapsed, startValue, targetValue, duration);
                    stat.textContent = value.toFixed(decimalPlaces) + suffix;
                    requestAnimationFrame(updateValue);
                } else {
                    stat.textContent = targetValue + suffix;
                }
            };
            
            // Easing function for smoother animation
            const easeOutQuart = (t, b, c, d) => {
                t /= d;
                t--;
                return -c * (t * t * t * t - 1) + b;
            };
            
            // Start animation when stat comes into view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateValue();
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(stat);
        });
    };
    
    // Add subtle hover effects to cards
    const proCards = document.querySelectorAll('.pro-card');
    proCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Add hover effect to CTA button
    const ctaButton = document.querySelector('.pro-cta');
    if (ctaButton) {
        ctaButton.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'translateX(5px)';
            }
        });
        
        ctaButton.addEventListener('mouseleave', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.style.transform = 'translateX(0)';
            }
        });
    }
    
    // Initialize animations when comparison section is in view
    const comparisonSection = document.querySelector('.professional-comparison');
    if (comparisonSection) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateStats();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });
        
        observer.observe(comparisonSection);
    }
});
