// Lead Form Validation

document.addEventListener('DOMContentLoaded', function() {
    const leadForm = document.getElementById('lead-capture-form');
    const appointmentLeadForm = document.getElementById('appointment-lead-form');

    if (leadForm) {
        leadForm.addEventListener('submit', function(event) {
            // Prevent the form from submitting
            event.preventDefault();

            // Reset previous error messages
            clearErrors();

            // Validate all required fields
            let isValid = validateRequiredFields();

            // Validate work email (business domain)
            if (isValid) {
                isValid = validateWorkEmail();
            }

            // Validate website URL
            if (isValid) {
                isValid = validateWebsiteURL();
            }

            // If all validations pass, submit the form
            if (isValid) {
                // Show loading state on button
                const submitButton = leadForm.querySelector('button[type="submit"]');
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="bi bi-arrow-repeat spin me-2"></i>Processing...';
                submitButton.disabled = true;

                // Simulate form submission (replace with actual submission)
                setTimeout(function() {
                    // Reset button
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;

                    // Show success message
                    showSuccessMessage();

                    // In a real implementation, you would submit the form here
                    // leadForm.submit();
                }, 1500);
            }
        });
    }

    // Function to validate required fields
    function validateRequiredFields() {
        const requiredFields = leadForm.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(function(field) {
            if (!field.value.trim()) {
                showError(field, 'This field is required');
                isValid = false;
            }
        });

        return isValid;
    }

    // Function to validate work email (business domain)
    function validateWorkEmail() {
        const emailField = leadForm.querySelector('[name="work_email"]');
        const email = emailField.value.trim();

        // Basic email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showError(emailField, 'Please enter a valid email address');
            return false;
        }

        // Check for common personal email domains
        const personalDomains = [
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'aol.com', 'icloud.com', 'mail.com', 'protonmail.com',
            'zoho.com', 'yandex.com', 'gmx.com', 'live.com'
        ];

        const domain = email.split('@')[1].toLowerCase();

        if (personalDomains.includes(domain)) {
            showError(emailField, 'Please use your work email address');
            return false;
        }

        return true;
    }

    // Function to validate website URL
    function validateWebsiteURL() {
        const urlField = leadForm.querySelector('[name="website_url"]');
        const url = urlField.value.trim();

        // Check if URL has a valid format
        try {
            new URL(url);
            return true;
        } catch (e) {
            showError(urlField, 'Please enter a valid website URL (include https://)');
            return false;
        }
    }

    // Function to show error message
    function showError(field, message) {
        // Create error element
        const errorElement = document.createElement('div');
        errorElement.className = 'invalid-feedback d-block';
        errorElement.textContent = message;

        // Add error class to field
        field.classList.add('is-invalid');

        // Insert error message after the field
        field.parentNode.appendChild(errorElement);

        // Focus on the first field with error
        if (!leadForm.querySelector('.is-invalid:focus')) {
            field.focus();
        }
    }

    // Function to clear all error messages
    function clearErrors() {
        // Remove error messages
        const errorMessages = leadForm.querySelectorAll('.invalid-feedback');
        errorMessages.forEach(function(error) {
            error.remove();
        });

        // Remove error class from fields
        const invalidFields = leadForm.querySelectorAll('.is-invalid');
        invalidFields.forEach(function(field) {
            field.classList.remove('is-invalid');
        });
    }

    // Function to show success message
    function showSuccessMessage() {
        // Hide the form
        const formContent = leadForm.querySelector('fieldset') || leadForm;
        formContent.style.display = 'none';

        // Show success message
        const formResults = leadForm.querySelector('.form-results');
        formResults.classList.remove('d-none');
        formResults.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-check-circle-fill text-success fs-1 mb-3"></i>
                <h3 class="fw-600 mb-2">Thank You!</h3>
                <p class="mb-4">Your information has been submitted successfully. One of our experts will contact you shortly to schedule your free profit analysis.</p>
                <p class="fs-14 text-muted">Please check your email for confirmation.</p>
            </div>
        `;
    }
});

// Add spinning animation for loading state
document.head.insertAdjacentHTML('beforeend', `
    <style>
        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
`);
