/* Modern Metrics Section - Apple/Samsung Inspired */
.metrics-section {
    position: relative;
    background: linear-gradient(180deg, rgba(255,255,255,1) 0%, rgba(248,249,250,1) 100%);
    padding: 80px 0;
    overflow: hidden;
}

/* Subtle background pattern */
.metrics-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 10% 20%, rgba(143, 118, 245, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(233, 88, 161, 0.03) 0%, transparent 50%);
    z-index: 0;
}

/* Section heading styles */
.metrics-heading {
    position: relative;
    margin-bottom: 60px;
}

.metrics-heading h2 {
    font-weight: 700;
    margin-bottom: 15px;
}

.metrics-heading p {
    font-size: 18px;
    line-height: 1.6;
    color: #555;
    max-width: 700px;
    margin: 0 auto;
}

/* Metrics grid container */
.metrics-grid {
    position: relative;
    z-index: 1;
}

/* Modern metric card styles */
.metric-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 30px;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

/* Card header */
.metric-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.metric-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.metric-badge {
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 20px;
    background: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    display: inline-flex;
    align-items: center;
}

.metric-badge i {
    margin-right: 4px;
    font-size: 10px;
}

/* Metric value styling */
.metric-value-container {
    margin-bottom: 15px;
}

.metric-value {
    font-size: 42px;
    line-height: 1;
    font-weight: 700;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.metric-value .unit {
    font-size: 24px;
    margin-left: 2px;
}

.metric-value .trend {
    margin-left: 10px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.trend-up {
    color: #2ecc71;
}

.trend-down {
    color: #3498db;
}

.trend i {
    margin-right: 3px;
}

.metric-description {
    font-size: 14px;
    line-height: 1.5;
    color: #666;
    margin-bottom: 20px;
}

/* Mini chart container */
.mini-chart {
    margin-top: auto;
    height: 60px;
    position: relative;
}

.mini-chart canvas {
    width: 100%;
    height: 100%;
}

/* Trend line SVG */
.trend-line {
    width: 100%;
    height: 40px;
    margin-top: auto;
}

.trend-line-up path {
    stroke: rgba(46, 204, 113, 0.5);
    fill: rgba(46, 204, 113, 0.1);
}

.trend-line-down path {
    stroke: rgba(52, 152, 219, 0.5);
    fill: rgba(52, 152, 219, 0.1);
}

/* Data source footer */
.metrics-footer {
    margin-top: 40px;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 10px;
    font-size: 12px;
    color: #777;
}

.metrics-footer a {
    color: #8f76f5;
    text-decoration: none;
    font-weight: 500;
}

.metrics-footer a:hover {
    text-decoration: underline;
}

/* Testimonial card */
.testimonial-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 30px;
    margin-top: 60px;
    position: relative;
}

.testimonial-card:hover {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.testimonial-content {
    position: relative;
    padding-left: 25px;
    margin-bottom: 20px;
}

.testimonial-content::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(to bottom, #e958a1, #8f76f5);
    border-radius: 3px;
}

.testimonial-quote {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
    border: 2px solid #fff;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.testimonial-info {
    display: flex;
    flex-direction: column;
}

.testimonial-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.testimonial-position {
    font-size: 14px;
    color: #666;
}

.testimonial-nav {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.testimonial-nav-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.testimonial-nav-btn:hover {
    background: #e9ecef;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .metrics-section {
        padding: 60px 0;
    }
    
    .metric-card {
        margin-bottom: 20px;
    }
    
    .metric-value {
        font-size: 36px;
    }
}

@media (max-width: 767px) {
    .metrics-heading {
        margin-bottom: 40px;
    }
    
    .metric-card {
        padding: 20px;
    }
    
    .metric-value {
        font-size: 32px;
    }
}

/* Apple-inspired chart styles */
.apple-chart {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chart-container {
    flex-grow: 1;
    position: relative;
    min-height: 200px;
}

.chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
    font-size: 12px;
    color: #666;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 5px;
}

/* Comparison chart specific styles */
.comparison-chart {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 30px;
    height: 100%;
}

.comparison-chart-header {
    margin-bottom: 20px;
}

.comparison-chart-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.comparison-chart-subtitle {
    font-size: 14px;
    color: #666;
}

/* Animated bar chart */
.bar-chart {
    height: 250px;
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
    padding: 0 10px;
}

.bar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 80px;
}

.bar {
    width: 40px;
    background: linear-gradient(180deg, #e958a1 0%, #8f76f5 100%);
    border-radius: 6px 6px 0 0;
    transition: height 1s ease;
}

.bar-label {
    margin-top: 10px;
    font-size: 12px;
    color: #666;
    text-align: center;
}

.bar-value {
    position: absolute;
    top: -25px;
    font-size: 12px;
    font-weight: 600;
    color: #333;
}

/* Gauge chart */
.gauge-chart {
    position: relative;
    width: 100%;
    height: 150px;
}

.gauge-value {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.gauge-label {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14px;
    color: #666;
    white-space: nowrap;
}

/* Donut chart */
.donut-chart {
    position: relative;
    width: 100%;
    height: 200px;
}

.donut-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.donut-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    line-height: 1;
}

.donut-label {
    font-size: 14px;
    color: #666;
}

/* Sparkline */
.sparkline {
    height: 40px;
    width: 100%;
}

/* Tooltip */
.chart-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.chart-tooltip::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px 5px 0;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.8) transparent transparent;
}
