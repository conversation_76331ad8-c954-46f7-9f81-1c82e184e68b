# API Directory Configuration
# Handle requests to API endpoints

RewriteEngine On

# Handle lead-capture-audit requests (with or without .php)
RewriteRule ^lead-capture-audit/?$ lead-capture-audit.php [L,QSA]

# Ensure PHP files are accessible
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# Set proper headers for API responses
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, X-Requested-With"
    Header always set Content-Type "application/json"
</IfModule>

# Handle OPTIONS requests for CORS
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
