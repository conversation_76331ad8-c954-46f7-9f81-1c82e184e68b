<?php
/**
 * Simple PHP Template Engine
 * Inspired by Plates but simplified for your needs
 * No dependencies, pure PHP, shared hosting compatible
 */

class TemplateEngine {
    private $templateDir;
    private $cacheDir;
    private $enableCache;
    private $globalVars = [];
    
    public function __construct($templateDir = 'templates/', $cacheDir = 'cache/templates/', $enableCache = true) {
        // Convert relative paths to absolute paths
        $this->templateDir = rtrim($templateDir, '/') . '/';
        $this->cacheDir = rtrim($cacheDir, '/') . '/';
        $this->enableCache = $enableCache;
        
        // Create directories if they don't exist
        if (!is_dir($this->templateDir)) {
            mkdir($this->templateDir, 0755, true);
        }
        if ($this->enableCache && !is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Set default global variables
        $this->globalVars = [
            'site_name' => 'AdZeta Blog',
            'site_url' => 'http://' . ($_SERVER['HTTP_HOST'] ?? 'localhost'),
            'current_year' => date('Y'),
            'current_url' => $_SERVER['REQUEST_URI'] ?? '/'
        ];
    }
    
    /**
     * Render a template with data
     */
    public function render($template, $data = []) {
        $templateFile = $this->templateDir . $template . '.php';

        if (!file_exists($templateFile)) {
            // Enhanced error message with debugging info
            $debugInfo = [
                'template_name' => $template,
                'template_file' => $templateFile,
                'template_dir' => $this->templateDir,
                'current_dir' => getcwd(),
                'file_exists' => file_exists($templateFile),
                'dir_exists' => is_dir($this->templateDir),
                'dir_contents' => is_dir($this->templateDir) ? scandir($this->templateDir) : 'Directory not found'
            ];

            throw new Exception("Template not found: {$template}. Debug info: " . json_encode($debugInfo));
        }
        
        // Check cache first
        if ($this->enableCache) {
            $cacheFile = $this->getCacheFile($template, $data);
            if ($this->isCacheValid($cacheFile, $templateFile)) {
                return file_get_contents($cacheFile);
            }
        }
        
        // Render template
        $output = $this->renderTemplate($templateFile, array_merge($this->globalVars, $data));
        
        // Cache the output
        if ($this->enableCache) {
            file_put_contents($cacheFile, $output);
        }
        
        return $output;
    }
    
    /**
     * Add global variables available to all templates
     */
    public function addGlobal($key, $value) {
        $this->globalVars[$key] = $value;
    }
    
    /**
     * Add multiple global variables
     */
    public function addGlobals($vars) {
        $this->globalVars = array_merge($this->globalVars, $vars);
    }
    
    /**
     * Include a partial template
     */
    public function partial($template, $data = []) {
        return $this->render($template, $data);
    }
    
    /**
     * Escape HTML for security
     */
    public function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Format date helper
     */
    public function formatDate($date, $format = 'j F Y') {
        return date($format, strtotime($date));
    }
    
    /**
     * Truncate text helper
     */
    public function truncate($text, $length = 150, $suffix = '...') {
        if (strlen($text) <= $length) return $text;
        return substr($text, 0, $length) . $suffix;
    }
    
    /**
     * Generate URL helper
     */
    public function url($path = '') {
        return $this->globalVars['site_url'] . '/' . ltrim($path, '/');
    }
    
    /**
     * Asset URL helper
     */
    public function asset($path) {
        return $this->url('assets/' . ltrim($path, '/'));
    }

    /**
     * Render post content with enhanced formatting
     */
    public function renderPostContent($content) {
        if (empty($content)) {
            return '<p>No content available.</p>';
        }

        // Enhanced content rendering with better formatting
        $content = htmlspecialchars($content);
        $content = nl2br($content);

        // Add paragraph formatting
        $content = '<p>' . str_replace('<br /><br />', '</p><p>', $content) . '</p>';

        // Enhanced markdown-like formatting
        $content = preg_replace('/^# (.+)$/m', '<h2 class="mt-4 mb-3">$1</h2>', $content);
        $content = preg_replace('/^## (.+)$/m', '<h3 class="mt-4 mb-3">$1</h3>', $content);
        $content = preg_replace('/^### (.+)$/m', '<h4 class="mt-3 mb-2">$1</h4>', $content);

        // Bold and italic
        $content = preg_replace('/\*\*(.+?)\*\*/', '<strong>$1</strong>', $content);
        $content = preg_replace('/\*(.+?)\*/', '<em>$1</em>', $content);

        // Links
        $content = preg_replace('/\[(.+?)\]\((.+?)\)/', '<a href="$2" target="_blank">$1</a>', $content);

        // Code blocks (simple)
        $content = preg_replace('/`(.+?)`/', '<code>$1</code>', $content);

        return $content;
    }
    
    /**
     * Clear template cache
     */
    public function clearCache() {
        if (!$this->enableCache) return false;
        
        $files = glob($this->cacheDir . '*.cache');
        $cleared = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $cleared++;
            }
        }
        
        return $cleared;
    }
    
    /**
     * Get cache statistics
     */
    public function getCacheStats() {
        if (!$this->enableCache) {
            return ['enabled' => false];
        }
        
        $files = glob($this->cacheDir . '*.cache');
        $totalSize = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
        }
        
        return [
            'enabled' => true,
            'total_files' => count($files),
            'total_size' => $totalSize,
            'cache_dir' => $this->cacheDir
        ];
    }
    
    // Private methods
    private function renderTemplate($templateFile, $data) {
        // Extract variables for template
        extract($data);
        
        // Make template engine available in templates as $this
        $template = $this;
        
        // Start output buffering
        ob_start();
        
        try {
            // Include the template file
            include $templateFile;
            
            // Get the output
            $output = ob_get_contents();
            
        } catch (Exception $e) {
            // Clean the buffer and re-throw
            ob_end_clean();
            throw $e;
        }
        
        // Clean the buffer
        ob_end_clean();
        
        return $output;
    }
    
    private function getCacheFile($template, $data) {
        $hash = md5($template . serialize($data));
        return $this->cacheDir . $template . '_' . $hash . '.cache';
    }
    
    private function isCacheValid($cacheFile, $templateFile) {
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        // Check if template is newer than cache
        return filemtime($templateFile) <= filemtime($cacheFile);
    }
}

/**
 * Template Helper Functions
 * These are available globally in templates
 */

// Safe echo with escaping
function e($string) {
    // Handle null values and non-strings safely
    if ($string === null) {
        return '';
    }
    return htmlspecialchars((string)$string, ENT_QUOTES, 'UTF-8');
}

// Raw echo (no escaping)
function raw($string) {
    return $string;
}

// Include partial template
function partial($template, $data = [], $templateEngine = null) {
    global $template;
    if ($templateEngine) {
        return $templateEngine->partial($template, $data);
    }
    return '';
}

// Format date
function formatTemplateDate($date, $format = 'j F Y') {
    return date($format, strtotime($date));
}

// Truncate text
function truncateText($text, $length = 150, $suffix = '...') {
    if (strlen($text) <= $length) return $text;
    return substr($text, 0, $length) . $suffix;
}

// Generate URL
function templateUrl($path = '') {
    $baseUrl = 'http://' . ($_SERVER['HTTP_HOST'] ?? 'localhost');
    return $baseUrl . '/' . ltrim($path, '/');
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Get current user
function currentUser() {
    return $_SESSION['user_name'] ?? 'Guest';
}
?>
