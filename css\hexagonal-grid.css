/* Hexagonal Grid Layout for Ad Platform Connections */
.hexagonal-grid-section {
    position: relative;
    padding: 80px 0;
    background: linear-gradient(180deg, #FFFFFF 0%, #F7F7F9 100%);
    overflow: hidden;
}

.hexagonal-grid-container {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 auto;
    max-width: 1200px;
    padding: 20px 0;
}

/* Hexagon styling */
.hexagon {
    position: relative;
    width: 280px;
    height: 320px;
    margin: 25px 15px;
    background-color: #ffffff;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    z-index: 1;
    overflow: hidden;
}

/* Hexagon shape using pseudo-elements */
.hexagon:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 10px;
    z-index: -1;
}

/* Hexagon border gradient */
.hexagon:after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.2) 0%, rgba(143, 118, 245, 0.2) 100%);
    border-radius: 12px;
    z-index: -2;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.hexagon:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.hexagon:hover:after {
    opacity: 1;
}

/* Platform-specific styling */
.hexagon.google {
    border-top: 3px solid #4285F4;
}

.hexagon.meta {
    border-top: 3px solid #1877F2;
}

.hexagon.tiktok {
    border-top: 3px solid #000000;
}

.hexagon.programmatic {
    border-top: 3px solid #8f76f5;
}

/* Hexagon content */
.hexagon-content {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.platform-logo {
    height: 50px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.platform-logo img {
    max-height: 100%;
    max-width: 120px;
}

.platform-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c2e3c;
}

.platform-description {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    flex-grow: 1;
    margin-bottom: 20px;
}

.platform-link {
    font-size: 14px;
    font-weight: 500;
    color: #e958a1;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.platform-link:hover {
    color: #8f76f5;
}

.platform-link i {
    margin-left: 5px;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.platform-link:hover i {
    transform: translateX(3px);
}

/* Connection lines */
.connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .hexagonal-grid-container {
        justify-content: center;
    }
    
    .hexagon {
        width: 260px;
        margin: 20px 10px;
    }
}

@media (max-width: 767px) {
    .hexagon {
        width: 100%;
        max-width: 320px;
        margin: 15px auto;
    }
}
