// Metrics Animation Script
document.addEventListener('DOMContentLoaded', function() {
    // Function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // Function to animate counting up
    function animateValue(obj, start, end, duration) {
        let startTimestamp = null;
        const step = (timestamp) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            const value = Math.floor(progress * (end - start) + start);
            
            // For ROAS (4x), we want to show the whole number
            if (obj.classList.contains('metric-value-up')) {
                obj.innerHTML = value + '<span class="fs-40">x</span><span class="direction-indicator"><i class="feather icon-feather-trending-up"></i></span>';
            } 
            // For CAC reduction (36%), we want to show the percentage
            else if (obj.classList.contains('metric-value-down')) {
                obj.innerHTML = value + '<span class="fs-40">%</span><span class="direction-indicator"><i class="feather icon-feather-trending-down"></i></span>';
            }
            
            if (progress < 1) {
                window.requestAnimationFrame(step);
            }
        };
        window.requestAnimationFrame(step);
    }

    // Get all metric values
    const metricValues = document.querySelectorAll('.metric-value');
    let animated = false;

    // Function to start animation if metrics are in viewport
    function checkAndAnimate() {
        if (!animated && metricValues.length > 0 && isInViewport(metricValues[0])) {
            metricValues.forEach(metric => {
                if (metric.classList.contains('metric-value-up')) {
                    animateValue(metric, 0, 4, 1500); // Animate from 0 to 4 for ROAS
                } else if (metric.classList.contains('metric-value-down')) {
                    animateValue(metric, 0, 36, 1500); // Animate from 0 to 36 for CAC reduction
                }
            });
            animated = true;
        }
    }

    // Check on scroll and on load
    window.addEventListener('scroll', checkAndAnimate);
    window.addEventListener('load', checkAndAnimate);
    
    // Simple testimonial navigation
    const prevBtn = document.querySelector('.testimonial-nav-prev');
    const nextBtn = document.querySelector('.testimonial-nav-next');
    
    if (prevBtn && nextBtn) {
        const testimonials = [
            {
                quote: "The predictive insights helped us scale profitably past plateaus we couldn't break before. Seeing a 4x ROAS lift was incredible.",
                name: "Lalit Rai",
                position: "Principal, Atria Development Corp.",
                image: "images/avtar-29.jpg"
            },
            {
                quote: "Adzeta's focus on LTV was a game-changer. We cut our reliance on discounts and saw a 36% drop in CAC while scaling revenue.",
                name: "Justin Ritchie",
                position: "CEO, Arc Compute",
                image: "images/avtar-29.jpg" // Use appropriate image
            }
        ];
        
        let currentIndex = 0;
        
        function updateTestimonial() {
            const testimonialQuote = document.querySelector('.testimonial-quote p');
            const testimonialName = document.querySelector('.testimonial-name');
            const testimonialPosition = document.querySelector('.testimonial-position');
            const testimonialAvatar = document.querySelector('.testimonial-avatar');
            
            // Fade out
            testimonialQuote.style.opacity = 0;
            testimonialName.style.opacity = 0;
            testimonialPosition.style.opacity = 0;
            testimonialAvatar.style.opacity = 0;
            
            setTimeout(() => {
                // Update content
                testimonialQuote.textContent = testimonials[currentIndex].quote;
                testimonialName.textContent = testimonials[currentIndex].name;
                testimonialPosition.textContent = testimonials[currentIndex].position;
                testimonialAvatar.src = testimonials[currentIndex].image;
                
                // Fade in
                testimonialQuote.style.opacity = 1;
                testimonialName.style.opacity = 1;
                testimonialPosition.style.opacity = 1;
                testimonialAvatar.style.opacity = 1;
            }, 300);
        }
        
        prevBtn.addEventListener('click', function(e) {
            e.preventDefault();
            currentIndex = (currentIndex === 0) ? testimonials.length - 1 : currentIndex - 1;
            updateTestimonial();
        });
        
        nextBtn.addEventListener('click', function(e) {
            e.preventDefault();
            currentIndex = (currentIndex === testimonials.length - 1) ? 0 : currentIndex + 1;
            updateTestimonial();
        });
    }
});
