<?php include 'header.php'; ?>
<link rel="stylesheet" href="css/google-ads-ppc.css?v=1.0" />
<style>
  .unified-value-card {
	background: white;
	border-radius: 16px;
	padding: 30px;
	box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(143, 118, 245, 0.1);
}

.challenge-highlight-box {
	background: rgba(244, 88, 136, 0.05);
	border-radius: 12px;
	padding: 20px;
	display: flex;
	align-items: flex-start;
	gap: 15px;
	border-left: 4px solid #f45888;
}

.highlight-icon-wrapper {
	flex-shrink: 0;
	font-size: 24px;
	color: #f45888;
	margin-top: 2px;
}

.highlight-content h5 {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.highlight-content p {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
}

/* Target High Value Users Section - Enhanced Flow Diagram Styles */
.target-high-value-section {
	position: relative;
	overflow: hidden;
}

/* Custom background for cards */
.custom-bg {
	background: rgba(255, 255, 255, 0.12);
	backdrop-filter: blur(20px);
	border: 1px solid rgba(255, 255, 255, 0.25);
	transition: all 0.3s ease;
	min-height: 200px;
	position: relative;
	padding: 1.5rem !important;
}

.custom-bg:hover {
	background: rgba(255, 255, 255, 0.18);
	border-color: rgba(244, 88, 136, 0.4);
	transform: translateY(-3px);
}

/* Card shadow effects */
.card-shadow {
	box-shadow: 0 10px 30px rgba(244, 88, 136, 0.2);
}

.card-shadow:hover {
	box-shadow: 0 15px 40px rgba(244, 88, 136, 0.3);
}

/* Icon buttons with theme colors */
.icon-btn {
	background: linear-gradient(135deg, #f45888, #8f76f5) !important;
	border: 2px solid rgba(255, 255, 255, 0.4) !important;
	color: #ffffff !important;
	font-size: 12px;
	font-weight: 600;
	box-shadow: 0 6px 20px rgba(244, 88, 136, 0.4);
	transition: all 0.3s ease;
	white-space: nowrap;
	z-index: 10;
}

.icon-btn:hover {
	background: linear-gradient(135deg, #e94d7a, #7d68e8) !important;
	box-shadow: 0 8px 25px rgba(244, 88, 136, 0.5);
	transform: translateY(-2px);
	color: #ffffff !important;
	border-color: rgba(255, 255, 255, 0.6) !important;
}

/* SVG path styling with theme colors */
.path-color {
	stroke: url(#flowGradient);
	opacity: 0.8;
}

.dashed-path {
	stroke-dasharray: 8, 4;
	opacity: 0.6;
}

/* Solid signal line styling */
.signal-line-solid {
	animation: signal-glow 2s ease-in-out infinite;
}

.signal-path-main {
	animation: signal-pulse 1.5s ease-in-out infinite alternate;
}

@keyframes signal-glow {

	0%,
	100% {
		opacity: 0.8;
		stroke-width: 6;
	}

	50% {
		opacity: 1;
		stroke-width: 10;
	}
}

@keyframes signal-pulse {
	0% {
		opacity: 0.9;
		stroke-width: 4;
	}

	100% {
		opacity: 1;
		stroke-width: 6;
	}
}

.signal-glow-line {
	animation: glow-pulse 1.2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
	0% {
		opacity: 0.6;
		stroke-width: 3;
	}

	100% {
		opacity: 1;
		stroke-width: 5;
	}
}

/* TOP 10% circle styling */
.target-high-value-section svg circle {
	transition: all 0.3s ease;
}

/* Profile image enhancements */
.target-high-value-section img {
	border-color: rgba(244, 88, 136, 0.5) !important;
	transition: all 0.3s ease;
}

.target-high-value-section img:hover {
	border-color: rgba(244, 88, 136, 0.8) !important;
	transform: scale(1.05);
}

/* LTV text styling */
.text-success {
	color: #f45888 !important;
	font-weight: 700;
	font-size: 1.1em;
}

/* Text color adjustments */
.text-secondary {
	color: rgba(255, 255, 255, 0.8) !important;
	font-size: 0.85rem;
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.text-white {
	color: #ffffff !important;
	font-weight: 600;
}

/* Profile image enhancements */
.target-high-value-section img {
	border-width: 3px !important;
	transition: all 0.3s ease;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.target-high-value-section img:hover {
	transform: scale(1.05);
	box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Enhanced gradient for SVG paths */
.target-high-value-section svg defs {
	display: none;
}

/* Add gradient definition */
/* Ensure content is above the gradient overlay */
.target-high-value-section .container {
	position: relative;
	z-index: 2;
}

/* Main card specific styling */
.target-high-value-section .col-md-8 .custom-bg {
	min-height: 180px;
	padding: 1.5rem !important;
}

/* Bottom cards specific styling */
.target-high-value-section .row.g-4 .custom-bg {
	min-height: 220px;
	padding: 1.5rem !important;
	margin-top: 1rem;
}

/* Button positioning adjustments */
.target-high-value-section .icon-btn {
	z-index: 20;
	position: relative;
}

/* Send Signal button specific styling */
.target-high-value-section .col-md-4 .icon-btn {
	box-shadow: 0 8px 25px rgba(244, 88, 136, 0.5);
}

/* Typography improvements */
.target-high-value-section h2 {
	font-family: inherit;
	line-height: 1.1;
}

/* Signal line positioning improvements */
.signal-line-svg {
	overflow: visible;
}

/* Flow line from Mid tier to Top 10% */
.flow-line-svg {
	overflow: visible;
	pointer-events: none;
}

/* Flow line styling - solid continuous line */
.flow-line-svg path {
	transition: all 0.3s ease;
}

.flow-line-svg path:hover {
	stroke-width: 6;
	filter: drop-shadow(0 0 8px rgba(244, 88, 136, 0.6));
}

/* Responsive adjustments */
@media (max-width: 1200px) {

	/* Adjust flow line for smaller desktop screens */
	.flow-line-svg {
		transform: scale(0.9);
		transform-origin: left top;
	}
}

@media (max-width: 992px) {

	/* Tablet adjustments */
	.target-high-value-section .row.g-4 {
		gap: 1.5rem !important;
		margin-top: 3rem !important;
	}

	.flow-line-svg {
		display: none !important;
	}

	.target-high-value-section .col-md-7 {
		margin-bottom: 2rem;
	}

	.target-high-value-section .col-md-5 .rounded-4 {
		position: static !important;
		top: auto !important;
	}

	.icon-btn {
		font-size: 12px;
		padding: 10px 16px !important;
	}

	.custom-bg {
		padding: 1.25rem !important;
		min-height: auto !important;
	}
}

@media (max-width: 768px) {

	/* Mobile adjustments - centered cards with large spacing */
	.target-high-value-section .container {
		max-width: 400px !important;
		padding-left: 2rem !important;
		padding-right: 2rem !important;
	}

	.target-high-value-section .row.g-4 {
		gap: 2rem !important;
		margin-top: 2rem !important;
		justify-content: center !important;
	}

	.target-high-value-section .col-md-7,
	.target-high-value-section .col-md-5 {
		flex: 0 0 100% !important;
		max-width: 100% !important;
	}

	.icon-btn {
		font-size: 11px;
		padding: 8px 12px !important;
		white-space: nowrap;
	}

	.custom-bg {
		padding: 1rem !important;
		min-height: auto !important;
		margin: 0 auto !important;
		max-width: 350px !important;
	}

	.target-high-value-section .col-md-7 .custom-bg {
		padding: 1.5rem !important;
		min-height: 160px !important;
	}

	.target-high-value-section .col-md-5 .rounded-4 {
		position: static !important;
		top: auto !important;
		margin-top: 1rem;
		text-align: center !important;
	}

	/* Adjust main card content for mobile */
	.target-high-value-section h2,
	.target-description {
		text-align: center;
	}

	.target-high-value-section .position-relative[style*="width: 120px"] {
		width: 100px !important;
		height: 100px !important;
	}

	/* Bottom cards mobile layout - centered with spacing */
	.target-high-value-section .row.g-4 .col-sm-6,
	.target-high-value-section .row.g-4 .col-md-4 {
		flex: 0 0 100% !important;
		max-width: 100% !important;
		margin-bottom: 2rem !important;
		display: flex !important;
		justify-content: center !important;
	}

	.target-high-value-section .row.g-4 .custom-bg {
		max-width: 300px !important;
		width: 100% !important;
	}

	.target-high-value-section .row.g-4 .col-md-4:last-child .icon-btn {
		position: static !important;
		top: auto !important;
		right: auto !important;
		margin-top: 1rem;
		margin-bottom: 0.5rem;
	}

	/* Hide desktop flow line but show mobile flow line */
	.flow-line-svg {
		display: none !important;
	}

	.signal-line-svg {
		display: none !important;
	}

	/* Mobile flow line with 90-degree bends */
	.target-high-value-section::after {
		content: '';
		position: absolute;
		left: 0;
		top: 200px;
		width: 100%;
		height: 100%;
		pointer-events: none;
		z-index: 1;
		background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' preserveAspectRatio='none'%3E%3Cpath d='M 50 25 L 50 31 C 50 33 50 35 52 35 L 88 35 C 90 35 92 35 92 37 L 92 73 C 92 75 92 77 90 77 L 60 77' stroke='%23f45888' stroke-width='2' fill='none' stroke-linecap='round' vector-effect='non-scaling-stroke'/%3E%3C/svg%3E") no-repeat;
		background-size: 100% 100%;
		opacity: 0.8;
		filter: drop-shadow(0 0 8px rgba(244, 88, 136, 0.4));
	}

	/* Mobile flow line animated dot */
	.target-high-value-section::before {
		content: '';
		position: absolute;
		left: 50%;
		top: 25%;
		transform: translateX(-50%);
		width: 8px;
		height: 8px;
		background: #f45888;
		border-radius: 50%;
		z-index: 2;
		box-shadow: 0 0 12px rgba(244, 88, 136, 0.6);
		animation: mobile-flow-dot 4s ease-in-out infinite;
	}

	@keyframes mobile-flow-dot {
		0% {
			left: 50%;
			top: 25%;
			transform: translateX(-50%);
			opacity: 0.8;
		}

		20% {
			left: 50%;
			top: 48%;
			transform: translateX(-50%) scale(1.1);
			opacity: 1;
		}

		40% {
			left: 92%;
			top: 48%;
			transform: translateX(-50%) scale(1.2);
			opacity: 1;
		}

		70% {
			left: 92%;
			top: 90%;
			transform: translateX(-50%) scale(1.1);
			opacity: 1;
		}

		100% {
			left: 60%;
			top: 90%;
			transform: translateX(-50%);
			opacity: 0.8;
		}
	}
}

@media (max-width: 576px) {

	/* Extra small mobile adjustments - maintain centered layout */
	.target-high-value-section .container {
		max-width: 350px !important;
		padding-left: 1.5rem !important;
		padding-right: 1.5rem !important;
	}

	.target-high-value-section .row.g-4 {
		gap: 1.5rem !important;
	}

	.icon-btn {
		font-size: 10px;
		padding: 6px 10px !important;
	}

	.custom-bg {
		padding: 0.75rem !important;
		max-width: 320px !important;
	}

	.target-high-value-section .col-md-7 .custom-bg {
		padding: 1.25rem !important;
		min-height: 140px !important;
	}

	.target-high-value-section .row.g-4 .custom-bg {
		max-width: 280px !important;
	}

	.target-high-value-section h2,
	.target-description {
		text-align: center;
	}

	.target-high-value-section .position-relative[style*="width: 120px"] {
		width: 80px !important;
		height: 80px !important;
	}

	/* Adjust circle text for smaller size */
	.target-high-value-section .position-absolute span[style*="font-size: 16px"] {
		font-size: 12px !important;
	}

	.target-high-value-section .position-absolute span[style*="font-size: 26px"] {
		font-size: 20px !important;
	}

	/* Profile images smaller on mobile */
	.target-high-value-section img[style*="width: 80px"] {
		width: 60px !important;
		height: 60px !important;
	}
}
</style>
<!-- end header -->
<!-- start hero section -->
<section class="cover-background top-space-padding pb-0 overflow-hidden position-relative lg-pb-30px hero-section ecom-ppc">
   <link rel="stylesheet" href="css/ppc-hero-animation.css?v=1.0" />
   <div class="professional-gradient-container">
      <div class="corner-gradient top-left"></div>
      <div class="corner-gradient top-right"></div>
      <div class="corner-gradient bottom-left"></div>
      <div class="corner-gradient bottom-right"></div>
      <div class="diagonal-gradient"></div>
      <div class="mesh-overlay"></div>
      <div class="vignette-overlay"></div>
   </div>
   <div class="container h-100">
      <!-- Removed distracting background elements for a more professional look -->
      <div class="row align-items-center h-100 md-mt-30px md-mb-10px pt-4">
         <div class="col-xl-6 col-lg-6 mb-9 position-relative z-index-1 ps-lg-5" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
            <div class="d-flex align-items-center mb-15px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
               <span class="fs-12 fw-light text-white opacity-90 primary-font ls-wide">
                  <span class="ai-icon">
                     <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="pulse">
                        <!-- Modern AI chip/processor shape -->
                        <rect x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5"/>
                        <!-- Circuit lines -->
                        <path class="circuit1" d="M8 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit2" d="M12 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit3" d="M16 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit4" d="M8 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit5" d="M12 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit6" d="M16 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit7" d="M2 8H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit8" d="M2 12H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit9" d="M2 16H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit10" d="M20 8H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit11" d="M20 12H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <path class="circuit12" d="M20 16H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                        <!-- Inner processor grid -->
                        <path class="grid" d="M8 8H16M8 12H16M8 16H16M8 8V16M12 8V16M16 8V16" stroke="url(#paint0_linear_ai_icon)" stroke-width="0.75" stroke-linecap="round" stroke-dasharray="1 2"/>
                        <!-- Central core -->
                        <rect class="core" x="10" y="10" width="4" height="4" fill="url(#paint0_linear_ai_icon)"/>
                        <!-- Sparkle overlay -->
                        <rect class="sparkle" x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint1_linear_ai_icon)" stroke-width="1" stroke-opacity="0.7"/>
                        <defs>
                           <linearGradient id="paint0_linear_ai_icon" x1="2" y1="2" x2="22" y2="22" gradientUnits="userSpaceOnUse">
                              <stop offset="0" stop-color="#e958a1"/>
                              <stop offset="0.5" stop-color="#8f76f5"/>
                              <stop offset="1" stop-color="#4a9eff"/>
                           </linearGradient>
                           <linearGradient id="paint1_linear_ai_icon" x1="20" y1="4" x2="4" y2="20" gradientUnits="userSpaceOnUse">
                              <stop offset="0" stop-color="#ffffff"/>
                              <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
                           </linearGradient>
                        </defs>
                     </svg>
                  </span>
                  <span class="text-gradient-purple-blue ls-3px">META ADS x ADZETA AI</span>
               </span>
            </div>
            <h1 class="alt-font mb-15px fs-50 md-fs-60 sm-fs-60 xs-fs-45 fw-600 lh-2-5 heading-gradient" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 1000, "delay": 300, "easing": "easeOutQuad" }'>
               <span class="fw-300">Maximize<br>Meta Ads Profit with<br></span>Predictive LTV.
            </h1>
            <div class="alt-font fw-400 fs-16 w-90 sm-w-100 mb-25px xs-mb-20px text-white opacity-75 lh-1-5" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 800, "easing": "easeOutQuint" }'>Meta’s Value Optimization helps find high-value customers, but its native reliance on pixel data and short-term signals limits long-term impact. Adzeta solves this by using Predictive AI to forecast customer lifetime value from your first-party data—then sends these powerful signals directly to Meta via the Conversions API, unlocking smarter targeting and higher profits.</div>
            <div class="d-flex flex-wrap" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 1000, "easing": "easeOutQuint" }'>
               <a href="free-ad-audit.php" class="btn btn-large btn-hover-animation box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-gradient-pink-orange btn-rounded me-15px mt-15px fw-600 alt-font">
               <span>
               <span class="btn-text">Free Meta Ads Profit Analysis</span>
               <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
               </span>
               </a>
               <!--  <a href="case-studies.html" class="btn btn-large  box-shadow-medium-bottom box-shadow-quadruple-large-hover  btn-transparent-white-light btn-rounded border-1 mt-20px fw-600 alt-font">
                  <span>
                  <span class="btn-text">Free VBB Guide</span>
                  <span class="btn-icon"><i class="fa-solid fa-download"></i></span>
                  </span>
                  </a> -->
            </div>
         </div>
         <div class="col-xl-6 col-lg-6 align-self-center">
            <div class="platform-animation-container pt-0" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 500 }'>
               <!-- New Hero Animation Structure with Semicircle Layout -->
               <div class="hero-animation-container">
                  <!-- Center Element - Adzeta AI Core -->
                  <div class="animation-element adzeta-ai-center">
                     <div class="position-relative">
                        <img src="images/adzeta-ai-center.png" alt="Adzeta AI Core" class="w-100">
                        <div class="element-label label-center">ADZETA AI</div>
                     </div>
                  </div>
                  <!-- Left Side Elements - Data Input (Semicircle) -->
                  <div class="animation-element adzeta-ai-left-1">
                     <img src="images/data-source-1.png" alt="Customer Data" class="w-100">
                     <div class="element-label label-left-1">PRODUCT FEED</div>
                  </div>
                  <div class="animation-element adzeta-ai-left-2">
                     <img src="images/data-source-2.png" alt="Website Data" class="w-100">
                     <div class="element-label label-left-2">WEBSITE SIGNALS</div>
                  </div>
                  <div class="animation-element adzeta-ai-left-3">
                     <img src="images/data-source-3.png" alt="CRM Data" class="w-100">
                     <div class="element-label label-left-3">CONVERSION DATA</div>
                  </div>
                  <!-- Right Side Elements - Ad Platforms (Semicircle) -->
                  <div class="animation-element adzeta-ai-right-1">
                     <img src="images/platform-1.png" alt="Predictive LTV Generated" class="w-100">
                     <div class="element-label label-right-1">Predictive LTV</div>
                  </div>
                  <div class="animation-element adzeta-ai-right-2">
                     <img src="images/platform-2.png" alt="VBB Enhanced" class="w-100">
                     <div class="element-label label-right-2">VBB Enhanced</div>
                  </div>
                  <div class="animation-element adzeta-ai-right-3">
                     <img src="images/platform-3.png" alt="Optimized Profit / ROAS" class="w-100">
                     <div class="element-label label-right-3">Optimized ROAS</div>
                  </div>
                  <!-- Connection Lines - Left Side -->
                  <div class="connection-line connection-left-1"></div>
                  <div class="connection-line connection-left-2"></div>
                  <div class="connection-line connection-left-3"></div>
                  <!-- Connection Lines - Right Side -->
                  <div class="connection-line connection-right-1"></div>
                  <div class="connection-line connection-right-2"></div>
                  <div class="connection-line connection-right-3"></div>
                  <!-- Data Flow Particles - Left Side -->
                  <div class="data-particle particle-left-1"></div>
                  <div class="data-particle particle-left-1-delay"></div>
                  <div class="data-particle particle-left-2"></div>
                  <div class="data-particle particle-left-2-delay"></div>
                  <div class="data-particle particle-left-3"></div>
                  <div class="data-particle particle-left-3-delay"></div>
                  <!-- Data Flow Particles - Right Side -->
                  <div class="data-particle particle-right-1"></div>
                  <div class="data-particle particle-right-1-delay"></div>
                  <div class="data-particle particle-right-2"></div>
                  <div class="data-particle particle-right-2-delay"></div>
                  <div class="data-particle particle-right-3"></div>
                  <div class="data-particle particle-right-3-delay"></div>
               </div>
            </div>
         </div>
      </div>
   </div>
</section>
<!-- end hero section -->
<!-- start section -->
<!-- start section: Value-Based Bidding -->
<section class="vbb-section vo-section position-relative overflow-hidden">
   <link rel="stylesheet" href="css/value-based-bidding.css?v=1.0" />
   <!-- Light section background with subtle gradient that fades to white at bottom -->
   <div class="position-absolute" style="
      inset: 0;
      background: linear-gradient(to bottom,
      rgba(248, 249, 250, 1) 0%,
      rgba(242, 240, 238, 0.8) 40%,
      rgba(255, 255, 255, 1) 100%);
      z-index: 0;
      pointer-events: none;
      "></div>
   <!-- Subtle mesh texture -->
   <div class="position-absolute" style="
      inset: 0;
      background:
      linear-gradient(90deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px),
      linear-gradient(0deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px);
      background-size: 28px 28px;
      opacity: 0.5;
      z-index: 0;
      pointer-events: none;
      "></div>
   <div class="container position-relative" style="z-index: 1;">
      <div class="row justify-content-center mb-50px">
         <div class="col-xl-8 col-lg-9 col-md-10 text-center">
            <div class="scale-tag-container">
               <div class="scale-tag">THE FOUNDATION</div>
            </div>
            <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Why Value Optimization Matters for Social Ads?</span></h3>
         </div>
      </div>
   </div>
   <div class="container position-relative" style="z-index: 1;">
      <div class="vbb-content-container box-shadow-extra-large box-shadow-extra-large-hover">
         <div class="row align-items-center">
            <!-- Left Column: Text Content -->
            <div class="col-lg-5 mb-5 mb-lg-0" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
               <div class="vbb-content">
                   <p class="md-mt-30">Not all clicks or purchases on Facebook & Instagram deliver the same long-term business value. Optimizing your Meta Ads solely for basic conversions (like 'Purchase' with equal value for all) often means you're paying the same to acquire a $50 LTV customer as a $500 LTV one. This limits your true e-commerce profit and scaling potential.</p>
                 <p><span class="fw-600">Meta's Value Optimization bidding</span> aims to find users likely to generate higher ROAS, but its effectiveness relies heavily on the quality and timeliness of value signals.
                     Adzeta's Predictive AI provides the crucial missing piece: accurately forecasting LTV from day one, so Meta's algorithms can optimize bids intelligently to acquire your most valuable future customers, not just any customer.
                  </p>
               </div>
            </div>
            <!-- Right Column: Visual Comparison (Apple-style) -->
            <div class="col-lg-7" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 500, "easing": "easeOutQuad" }'>
               <div class="vbb-visual">
                  <!-- Google logo -->
                  <div class="bidding-strategies">
                     <!-- Customer profiles row -->
                     <div class="strategy-row">
                        <div class="strategy-label"></div>
                        <div class="customers-row">
                           <div class="customer-column">
                              <div class="customer-info">
                                 <div class="customer-photo">
                                    <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Customer Photo">
                                 </div>
                                 <div class="customer-title">Casual Buyer</div>
                                 <div class="customer-value">LTV $50</div>
                              </div>
                           </div>
                           <div class="customer-column">
                              <div class="customer-info">
                                 <div class="customer-photo">
                                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Customer Photo">
                                 </div>
                                 <div class="customer-title">Repeat Purchaser</div>
                                 <div class="customer-value">LTV $150</div>
                              </div>
                           </div>
                           <div class="customer-column customer-column-highlight">
                              <div class="customer-info highlight">
                                 <div class="customer-photo">
                                    <img src="https://randomuser.me/api/portraits/men/65.jpg" alt="Customer Photo">
                                 </div>
                                 <div class="customer-title">VIP Subscriber</div>
                                 <div class="customer-value">LTV $500</div>
                              </div>
                           </div>
                        </div>
                     </div>
                     <!-- tCPA bidding row -->
                     <div class="strategy-row">
                        <div class="strategy-label tcpa">Standard 'Purchase' Optimization</div>
                        <div class="bid-row">
                           <div class="bid-box">
                              <span class="bid-label">Bid:</span>
                              <span class="bid-amount">$10</span>
                           </div>
                           <div class="bid-box">
                              <span class="bid-label">Bid:</span>
                              <span class="bid-amount">$10</span>
                           </div>
                           <div class="bid-box highlight">
                              <span class="bid-label">Bid:</span>
                              <span class="bid-amount">$10</span>
                           </div>
                        </div>
                     </div>
                     <!-- Value-based bidding row -->
                     <div class="strategy-row">
                        <div class="strategy-label vbb">Adzeta AI + Value Optimization</div>
                        <div class="bid-row">
                           <div class="bid-box lower">
                              <span class="bid-label">Bid:</span>
                              <span class="bid-amount">$5</span>
                           </div>
                           <div class="bid-box same">
                              <span class="bid-label">Bid:</span>
                              <span class="bid-amount">$10</span>
                           </div>
                           <div class="bid-box higher highlight">
                              <span class="bid-label">Bid:</span>
                              <span class="bid-amount">$15</span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</section>
<!-- end section: Value-Based Bidding -->
<!-- start section: Overcoming Google's Value Signal Limitations -->
<section class="google-value-challenges overflow-hidden position-relative">
   <!-- Light section background with subtle gradient that matches THE FOUNDATION section -->
   <div class="position-absolute" style="
      inset: 0;
      background: linear-gradient(to bottom,
      rgba(255, 255, 255, 1) 0%,
      rgba(248, 249, 250, 0.8) 20%,
      rgba(242, 240, 238, 0.8) 50%,
      rgba(249, 249, 255, 0.8) 80%,
      rgba(255, 255, 255, 1) 100%);
      z-index: 0;
      pointer-events: none;
      "></div>
   <!-- Subtle mesh texture -->
   <div class="position-absolute" style="
      inset: 0;
      background:
      linear-gradient(90deg, rgba(143, 118, 245, 0.02) 1px, transparent 1px),
      linear-gradient(0deg, rgba(143, 118, 245, 0.02) 1px, transparent 1px);
      background-size: 28px 28px;
      opacity: 0.5;
      z-index: 0;
      pointer-events: none;
      "></div>
   <div class="container position-relative" style="z-index: 1;">
      <div class="row justify-content-center mb-50px">
         <div class="col-xl-8 col-lg-9 col-md-10 text-center">
            <div class="scale-tag-container">
               <div class="scale-tag">OVERCOMING LIMITATIONS</div>
            </div>
            <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Why Meta's Value Optimization Falls Short</span></h3>
            <p class="mb-0">Meta's Value Optimization faces critical limitations that prevent you from achieving true profit optimization. Here's how Adzeta's predictive approach solves these fundamental challenges.</p>
         </div>
      </div>
      <!-- Meta Value Optimization Challenges Overview -->
      <div class="unified-value-card mb-50px box-shadow-extra-large">
         <div class="row">
            <div class="col-12 mb-4">
               <h4 class="alt-font fw-600 fs-20 mb-3">Bidding Based on Immediate Revenue Signals</h4>
               <p class="text-gray mb-4">Meta's Value Optimization considers revenue generated within a short attribution window (1-7 days), depending on your conversion event settings. This approach misses customers whose true value emerges at day 30, 60, or even 180—limiting your campaign's profit potential.</p>
               <div class="challenge-highlight-box mb-4">
                  <div class="highlight-icon-wrapper">
                     <i class="feather icon-feather-clock text-warning"></i>
                  </div>
                  <div class="highlight-content">
                     <h5 class="mb-2">Early Conversions Don't Predict Future Value</h5>
                     <p class="mb-0">High early conversion volume often doesn't correlate with later conversions that drive actual value, such as subscription renewals, repeat purchases, or customer upgrades.</p>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <!-- Two content blocks explaining challenges with Meta Value Optimization -->
      <div class="row justify-content-center mt-80px mb-50px">
         <div class="col-lg-6 col-md-6 mb-30px">
            <div class="challenge-card h-100" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 600, "delay": 200 }'>
               <div class="challenge-icon">
                  <i class="line-icon-Data-Settings icon-medium"></i>
               </div>
               <h4 class="challenge-title">Forecasting Future Value is Complex</h4>
               <p class="challenge-description">Meta requires high-quality data to train its algorithms effectively. While the Conversions API (CAPI) helps send server-side data without signal loss, Meta still misses critical signals about future conversion events. Forecasting becomes challenging with minimal early events or significant mismatches between early conversions and long-term user value.</p>
               <div class="challenge-highlight">
                  <div class="highlight-icon">
                     <i class="feather icon-feather-alert-circle"></i>
                  </div>
                  <p>Without predictive insights, Meta's algorithms optimize based on incomplete data, missing the customers who will drive your highest long-term profits.</p>
               </div>
            </div>
         </div>
         <div class="col-lg-6 col-md-6 mb-30px">
            <div class="challenge-card h-100" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 600, "delay": 300 }'>
               <div class="challenge-icon">
                  <i class="line-icon-Target icon-medium"></i>
               </div>
               <h4 class="challenge-title">Predictive Values Aren't Enough</h4>
               <p class="challenge-description">Having accurate predictions is just the beginning. Effective Value Optimization requires strategic considerations: optimal prediction accuracy before sending signals (since Meta only allows upward value adjustments), handling extreme values like high-spending customers, backup models for data drift, and monitoring match rates. These complexities make implementation challenging.</p>
               <div class="challenge-highlight">
                  <div class="highlight-icon">
                     <i class="feather icon-feather-alert-circle"></i>
                  </div>
                  <p>You need an experienced Meta partner who understands both predictive modeling and Meta's algorithm nuances to maximize your Value Optimization success.</p>
               </div>
            </div>
         </div>
      </div>
      <!-- Add CSS for the challenge cards and highlight boxes -->
   </div>
</section>
<!-- end section: Overcoming Google's Value Signal Limitations -->
<!-- start section: Data to Decisions -->
<section class="data-decisions-section position-relative overflow-hidden py-150px">
   <link rel="stylesheet" href="css/animated-border-bejamas.css" />
   <!-- Modern dark base gradient -->
   <div class="position-absolute" style="
      inset: 0;
      background: linear-gradient(135deg, #0A0913 0%, #1A142A 40%, #271E3D 75%, #341F53 100%);
      z-index: 0;
      pointer-events: none;
      "></div>
   <!-- Top-right soft purple glow -->
   <div class="position-absolute" style="
      inset: 0;
      background: radial-gradient(circle at 85% 20%, rgba(190, 130, 255, 0.18) 0%, transparent 60%);
      z-index: 1;
      pointer-events: none;
      "></div>
   <!-- Bottom-left pink accent glow -->
   <div class="position-absolute" style="
      inset: 0;
      background: radial-gradient(circle at 15% 85%, rgba(255, 105, 180, 0.15) 0%, transparent 60%);
      z-index: 1;
      pointer-events: none;
      "></div>
   <!-- Subtle mesh texture -->
   <div class="position-absolute" style="
      inset: 0;
      background:
      linear-gradient(90deg, rgba(255, 160, 255, 0.03) 1px, transparent 1px),
      linear-gradient(0deg, rgba(255, 160, 255, 0.03) 1px, transparent 1px);
      background-size: 28px 28px;
      opacity: 0.25;
      z-index: 1;
      pointer-events: none;
      "></div>
   <div class="container position-relative" style="z-index: 2;">
      <div class="row justify-content-center mb-80px">
         <div class="col-xl-8 col-lg-9 col-md-10 text-center">
            <div class="mb-10px">
               <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">ADZETA'S SOLUTION</span>
            </div>
            <h3 class="alt-font fw-700 ls-minus-1px mb-20px text-white">Predictive LTV: The Key to Unlocking Meta's Full Potential</h3>
            <p class="mb-10 text-white-transparent">Adzeta bridges the critical information gap in Google Ads. Our Predictive AI engine transforms your rich first-party e-commerce data into highly accurate LTV forecasts from a customer's very first interaction. We don't just predict value; we translate that foresight into the precise, actionable signals needed to make Google's Value-Based Bidding truly effective for your profit goals.</p>
         </div>
      </div>
      <div class="data-decisions-items-wrapper">
         <!-- Row 1: AI Predictions For Google -->
         <div class="row justify-content-start mb-4">
            <div class="col-lg-7 col-md-9 col-sm-11">
               <a href="#" class="data-decisions-item">
                  <div class="data-decisions-item-header">
                     <div class="data-decisions-item-icon">
                        <i class="line-icon-Brain icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                     </div>
                     <h3 class="data-decisions-item-h3">Day-One LTV Prediction</h3>
                  </div>
                  <p class="data-decisions-item-p">Adzeta's AI analyzes your first-party data to generate precise LTV forecasts, identifying high-potential users even before they've made their first purchase via a Meta ad.</p>
                  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                     <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                     <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
               </a>
            </div>
         </div>
         <!-- Row 2: Prescriptive AI Automation -->
         <div class="row justify-content-center mb-4">
            <div class="col-lg-7 col-md-9 col-sm-11">
               <div class="animated-card">
                  <a href="#" class="data-decisions-item is-dark" style="background: linear-gradient(135deg, #1E1A33 0%, #2D1E4A 100%); border: none;">
                     <div class="data-decisions-item-header">
                        <div class="data-decisions-item-icon">
                           <i class="line-icon-Data-Transfer icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        </div>
                        <h3 class="data-decisions-item-h3 is-white">Seamless Integration via Conversions API (CAPI)</h3>
                     </div>
                     <p class="data-decisions-item-p is-white">We securely send these predictive LTV scores (as conversion values) directly to Meta Ads via the Conversions API (CAPI). This provides Meta's algorithms with richer, more accurate data than the pixel alone.</p>
                     <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                        <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                     </svg>
                  </a>
               </div>
            </div>
         </div>
         <!-- Row 3: 24-7 Always On Optimization -->
         <div class="row justify-content-end mb-4">
            <div class="col-lg-7 col-md-9 col-sm-11">
               <a href="#" class="data-decisions-item">
                  <div class="data-decisions-item-header">
                     <div class="data-decisions-item-icon">
                        <i class="line-icon-Shopping-Cart icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                     </div>
                     <h3 class="data-decisions-item-h3">Training Meta's AI for Profit</h3>
                  </div>
                  <p class="data-decisions-item-p">By feeding Meta's Value Optimization with predictive LTV from the start, Adzeta helps:<br />
                     More accurately target users likely to become high-value customers.<br />
                     Improve the efficiency of your ad spend allocation.<br />
                     Scale Value Optimization campaigns with greater confidence and better ROAS.
                  </p>
                  <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                     <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                     <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
               </a>
            </div>
         </div>
      </div>
   </div>
   <!-- Background elements -->
   <img src="images/ltv-signal.png" loading="lazy" width="300" alt="Google" class="data-decisions-img-bg is-google">
   <img src="images/adzeta-bg-circle.svg" loading="lazy" width="538" height="538" alt="AdZeta" class="data-decisions-img-bg is-adzeta">
   <!-- Small decorative dots -->
   <div class="data-decisions-dots"></div>
   <!-- Add CSS for this section -->
   <!-- end section: Data to Decisions -->
   <!-- start section: Target High Value Users -->
   <div class="target-high-value-section py-150px mt-15">
      <div class="container position-relative" style="z-index: 2;">
         <div class="row align-items-center">
            <!-- Left Column: Content -->
            <div class="col-lg-5 mb-5 mb-lg-0" data-anime='{ "translateX": [-50, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
               <div class="target-content">
                  <h2 class="alt-font fw-700 0 text-white mb-30px fs-45 md-fs-40 sm-fs-45 lh-1-2">Target High<br>Value Users</h2>
                  <div class="target-description mb-40px">
                     <p class="text-white-transparent fs-16 lh-1-6 mb-20px">
                        Adzeta helps identify high-value users and optimize by sending signals only for those who meet a specific threshold of probability for activation or exceed a defined value.
                     </p>
                     <p class="text-white-transparent fs-16 lh-1-6 mb-0">
                        For example, you can target just the top 10% of users in the mid-tier value segment, ensuring your efforts are focused on those most likely to drive results while balancing scale.
                     </p>
                  </div>
               </div>
            </div>
            <!-- Right Column: Enhanced Animated Flow Diagram -->
            <div class="col-lg-7" data-anime='{ "translateX": [50, 0], "opacity": [0,1], "duration": 800, "delay": 500, "easing": "easeOutQuad" }'>
               <div class="row g-4 position-relative">
                  <!-- Background SVG Path -->
                  <svg class="position-absolute top-0 start-0 w-100 h-100" preserveAspectRatio="xMidYMid meet" viewBox="0 0 1000 600" style="pointer-events:none;">
                     <defs>
                        <linearGradient id="flowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                           <stop offset="0%" style="stop-color:#f45888;stop-opacity:0.9" />
                           <stop offset="50%" style="stop-color:#e958a1;stop-opacity:0.8" />
                           <stop offset="100%" style="stop-color:#8f76f5;stop-opacity:0.7" />
                        </linearGradient>
                        <filter id="glow">
                           <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                           <feMerge>
                              <feMergeNode in="coloredBlur"/>
                              <feMergeNode in="SourceGraphic"/>
                           </feMerge>
                        </filter>
                     </defs>
                     <path class="path-color" d="M220 100 Q 280 100, 350 100 L 680 100" fill="none" stroke-width="3" filter="url(#glow)"></path>
                     <path class="path-color" d="M820 100 Q 820 180, 820 260 L 820 480" fill="none" stroke-width="3" filter="url(#glow)"></path>
                     <path class="path-color dashed-path" d="M150 250 Q 150 350, 150 400" fill="none" stroke-width="2"></path>
                     <path class="path-color dashed-path" d="M150 250 Q 350 250, 450 400" fill="none" stroke-width="2"></path>
                     <path class="path-color" d="M150 250 Q 500 250, 750 400" fill="none" stroke-width="3" filter="url(#glow)"></path>
                  </svg>
                  <!-- Main Card and Button -->
                  <div class="col-md-7 position-relative z-1">
                     <div class="custom-bg rounded-4 p-4 card-shadow position-relative">
                        <div class="d-flex flex-column">
                           <!-- Set Target Button at top -->
                           <div class="mb-3">
                              <button class="icon-btn btn rounded-pill px-3 py-2">
                              <i class="fas fa-crosshairs me-2"></i> Set Target
                              </button>
                           </div>
                           <!-- Main content -->
                           <div class="d-flex justify-content-between align-items-center">
                              <div>
                                 <h2 class="text-white fw-bold mb-1" style="font-size: 2.1rem; letter-spacing: -0.5px;">Mid tier</h2>
                                 <p class="text-secondary mb-0" style="font-size: 0.9rem; font-weight: 500; text-transform: uppercase; letter-spacing: 0.8px;">value segment</p>
                              </div>
                              <div class="position-relative" style="width: 120px; height: 120px;">
                                 <svg class="position-absolute top-0 start-0 w-100 h-100" viewBox="0 0 100 100">
                                    <defs>
                                       <filter id="mainCircleGlow">
                                          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                          <feMerge>
                                             <feMergeNode in="coloredBlur"/>
                                             <feMergeNode in="SourceGraphic"/>
                                          </feMerge>
                                       </filter>
                                    </defs>
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="rgba(244, 88, 136, 0.2)" stroke-width="6"></circle>
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f45888" stroke-width="6" stroke-dasharray="251.2" stroke-dashoffset="125.6" transform="rotate(-90 50 50)" filter="url(#mainCircleGlow)"></circle>
                                 </svg>
                                 <svg class="position-absolute top-0 start-0 w-100 h-100 opacity-60" viewBox="0 0 100 100">
                                    <circle cx="50" cy="50" r="30" fill="none" stroke="rgba(143, 118, 245, 0.2)" stroke-width="4"></circle>
                                    <circle cx="50" cy="50" r="30" fill="none" stroke="#8f76f5" stroke-width="4" stroke-dasharray="188.4" stroke-dashoffset="94.2" transform="rotate(-90 50 50)" filter="url(#mainCircleGlow)"></circle>
                                 </svg>
                                 <div class="position-absolute top-0 start-0 w-100 h-100 d-flex flex-column justify-content-center align-items-center text-white">
                                    <span class="fw-semibold" style="font-size: 16px; letter-spacing: 1.2px;">TOP</span>
                                    <span class="fw-bold" style="font-size: 26px; line-height: 1; margin-top: -2px; color: #f45888;">10%</span>
                                 </div>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <!-- Right Button Card -->
                  <div class="col-md-5 pt-md-0 pt-4 z-1">
                     <div class=" rounded-4 p-4 text-left" style="top:140px;position:absolute;">
                        <button class="icon-btn btn rounded-pill px-3 py-2">
                        <i class="fas fa-play-circle me-2"></i> Run model
                        </button>
                     </div>
                  </div>
                  <!-- Flow Line SVG from Mid tier to Top 10% -->
                  <svg class="flow-line-svg position-absolute" style="top: 0; left: 100; width: 100%; height: 100%; pointer-events: none;" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
                     <defs>
                        <linearGradient id="flowLineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                           <stop offset="0%" style="stop-color:#8f76f5;stop-opacity:0.8" />
                           <stop offset="50%" style="stop-color:#e958a1;stop-opacity:0.9" />
                           <stop offset="100%" style="stop-color:#f45888;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="animatedFlowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                           <stop offset="0%" style="stop-color:#8f76f5;stop-opacity:0">
                              <animate attributeName="stop-opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
                           </stop>
                           <stop offset="50%" style="stop-color:#e958a1;stop-opacity:0">
                              <animate attributeName="stop-opacity" values="0;1;0" dur="3s" begin="0.5s" repeatCount="indefinite"/>
                           </stop>
                           <stop offset="100%" style="stop-color:#f45888;stop-opacity:0">
                              <animate attributeName="stop-opacity" values="0;1;0" dur="3s" begin="1s" repeatCount="indefinite"/>
                           </stop>
                        </linearGradient>
                        <filter id="flowLineGlow">
                           <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                           <feMerge>
                              <feMergeNode in="coloredBlur"/>
                              <feMergeNode in="SourceGraphic"/>
                           </feMerge>
                        </filter>
                     </defs>
                     <!-- Solid continuous flow line: Start from end of mid tier card, extend horizontally, equal radius bends -->
                     <path d="M 520 180
                        L 880 180
                        Q 920 180 920 220
                        L 920 380
                        Q 920 420 880 420
                        L 740 420
                        Q 700 420 700 460
                        L 700 480"
                        stroke="#f45888"
                        stroke-width="2.5"
                        fill="none"
                        filter="url(#flowLineGlow)"
                        stroke-linecap="round"/>
                     <!-- Subtle glow overlay for depth -->
                     <path d="M 520 180 L 680 180 Q 720 180 720 220 L 720 380 L 580 380 Q 540 380 540 420 L 540 480"
                        stroke="url(#flowLineGradient)"
                        stroke-width="1.5"
                        fill="none"
                        opacity="0.6"
                        stroke-linecap="round"/>
                     <!-- Single animated dot flowing along the line -->
                     <circle r="8" fill="#f45888" filter="url(#flowLineGlow)">
                        <animateMotion dur="3s" repeatCount="indefinite"
                           path="M 520 180
                           L 880 180
                           Q 920 180 920 220
                           L 920 380
                           Q 920 420 880 420
                           L 740 420
                           Q 700 420 700 460
                           L 700 480" />
                        <animate attributeName="opacity" values="0.8;1;0.8" dur="3s" repeatCount="indefinite" />
                     </circle>
                  </svg>
                  <!-- Bottom Cards -->
                  <div class="row g-4 mt-5 pt-5 z-1">
                     <div class="col-sm-6 col-md-4 text-center">
                        <div class="custom-bg rounded-4 p-4 card-shadow d-flex flex-column align-items-center">
                           <p class="text-secondary small mb-2">Segment: Top 50%</p>
                           <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuBMJhglGveLilVYV_rcMA3H8HXqOl9Qj1aTVU0b0_ti2nWvN0Y-r_dzNIh8ZR-8PUVtvyI17mVkXxIwUxbrDZdOKjX9S2yPvXwVmWx_arLrlloMZ0DYIK-CKnFHN4E9aJMKo7vS-bjraDx0dgj9rorWbbq87V59b3_y1IaFimaMhqPUsEwQn8LjHkLaNkaMa33i29x0JgItG0VBGXQo-g8sF82IMylEkWQ4KVLyycqW_IE5Rf26HfM9unQagOktYF2my4GGLsttXHld" alt="Blonde woman" class="rounded-circle object-fit-cover border border-3 mb-2" style="width: 80px; height: 80px; border-color: #f45888 !important;">
                           <p class="text-white fw-semibold">LTV: <span class="text-success">$1296</span></p>
                        </div>
                     </div>
                     <div class="col-sm-6 col-md-4 text-center">
                        <div class="custom-bg rounded-4 p-4 card-shadow d-flex flex-column align-items-center">
                           <p class="text-secondary small mb-2">Segment: Top 20%</p>
                           <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuDQfX1-SPQNEofdWRkMPcmNkP77joCycxwFHItT5NP2cS-Scchxuc-C-SafpnorZU3MS8EeksVpvditLwJrU7HMxrUlyUR-pXrtvHii-TT6QUD035DcAiSksp7FkBIJVXlRxSx49Jp5DkGl1P57_m9ZItFQKeVG8T3aLNJ2MojsuR6kdZMUu0KOCCFqKYFW7Ze6cOsqExrWkfs3qjPRYNwbS9P_bHh_q2ThN7Fen3QZ3kpDf32YjJYkkRxvV70iKuKp-leS8MtzehKa" alt="Brunette woman" class="rounded-circle object-fit-cover border border-3 mb-2" style="width: 80px; height: 80px; border-color: #e958a1 !important;">
                           <p class="text-white fw-semibold">LTV: <span class="text-success">$1728</span></p>
                        </div>
                     </div>
                     <div class="col-sm-6 col-md-4 text-center position-relative">
                        <div class="custom-bg rounded-4 p-4 card-shadow d-flex flex-column align-items-center position-relative">
                           <!-- Send Signal Button at top right -->
                           <button class="icon-btn btn rounded-pill px-3 py-2 position-absolute " style="top: -25px; right: -70px;">
                           <i class="fas fa-broadcast-tower me-2"></i> Send Signal
                           </button>
                           <p class="text-secondary small mb-2">Segment: Top 10%</p>
                           <img src="https://lh3.googleusercontent.com/aida-public/AB6AXuD9p7nd51ggvejTHR_B9F4fp44QJLvHOMMIVcWt4M6X_iU4MOeub0d0QuP1LAV935hNaQdS9wH2bxthE05TvBvp5aFfab8xMBnPq-mYozIXQbEz4NdTg7QoT-IXCb5rcjQfxDGKe-kHGzICLkg9DvdUW9QIX2faw7DBn8_0jrElLFocJVD6uxBO9dDSRHlkxMTLVYUCjU-k-7UpSn822TGyaOMRcu9GPQCWBwofZ-yoTUHuB-tMS_Ln2WverxIwuFK3buTPP7OsFC5x" alt="Man with glasses" class="rounded-circle object-fit-cover border border-3 mb-2" style="width: 80px; height: 80px; border-color: #8f76f5 !important;">
                           <p class="text-white fw-semibold mb-3">LTV: <span class="text-success">$2016</span></p>
                           <!-- Signal Line SVG positioned relative to this card -->
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</section>
<!-- end section: Target High Value Users -->
<!-- SECTION : Proven Results on Google Ads - Start -->
<section class="proven-results-section overflow-hidden position-relative pb-0">
   <!-- Light section background with subtle gradient that matches previous sections -->
   <div class="position-absolute" style="
      inset: 0;
      background: linear-gradient(to bottom,
      rgba(255, 255, 255, 1) 0%,
      rgba(249, 249, 255, 0.8) 20%,
      rgba(248, 249, 250, 0.8) 50%,
      rgba(242, 240, 238, 0.8) 80%,
      rgba(255, 255, 255, 1) 100%);
      z-index: 0;
      pointer-events: none;
      "></div>
   <!-- Subtle mesh texture -->
   <div class="position-absolute" style="
      inset: 0;
      background:
      linear-gradient(90deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px),
      linear-gradient(0deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px);
      background-size: 28px 28px;
      opacity: 0.5;
      z-index: 0;
      pointer-events: none;
      "></div>
   <div class="container position-relative" style="z-index: 1;">
      <div class="row justify-content-center mb-60px">
         <div class="col-xl-8 col-lg-9 col-md-10 text-center">
            <div class="scale-tag-container">
               <div class="scale-tag">PROVEN RESULTS</div>
            </div>
            <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Real Results from Meta Value Optimization</span></h3>
            <p class="mb-0">Authentic performance improvements our e-commerce clients achieve when Adzeta's Predictive LTV enhances Meta's Value Optimization campaigns.</p>
         </div>
      </div>
      <!-- Key Meta Ads Performance Metrics with Adzeta -->
      <div class="row justify-content-center mb-0" data-anime='{ "el": "childs", "translateY": [30, 0], "opacity": [0,1], "duration": 600, "delay":0, "staggervalue": 300, "easing": "easeOutQuad" }'>
         <!-- Metric Card 1: Meta ROAS Improvement -->
         <div class="col-lg-3 col-md-6 col-sm-6 mb-30px">
            <div class="compact-metric-card h-100 text-center">
               <div class="metric-icon">
                  <i class="line-icon-Arrow-Up icon-medium" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
               </div>
               <h2 class="counter d-inline-flex fw-700 ls-minus-2px mb-10px" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" data-speed="2000" data-to="2.8">2.8</h2>
               <span class="counter-suffix">x</span>
               <h4 class="metric-title">Meta ROAS Improvement</h4>
               <p class="metric-description">Average ROAS increase when Meta's Value Optimization uses Adzeta's predictive LTV signals.</p>
            </div>
         </div>
         <!-- Metric Card 2: Social CAC Reduction -->
         <div class="col-lg-3 col-md-6 col-sm-6 mb-30px">
            <div class="compact-metric-card h-100 text-center">
               <div class="metric-icon">
                  <i class="line-icon-Arrow-Down icon-medium" style="background: linear-gradient(135deg, #8f76f5 0%, #4a9eff 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
               </div>
               <h2 class="counter d-inline-flex fw-700 ls-minus-2px mb-10px" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" data-speed="2000" data-to="42">42</h2>
               <span class="counter-suffix">%</span>
               <h4 class="metric-title">Social CAC Reduction</h4>
               <p class="metric-description">Lower customer acquisition costs by targeting users with higher predicted lifetime value on social platforms.</p>
            </div>
         </div>
         <!-- Metric Card 3: Value Optimization Performance -->
         <div class="col-lg-3 col-md-6 col-sm-6 mb-30px">
            <div class="compact-metric-card h-100 text-center">
               <div class="metric-icon">
                  <i class="line-icon-Target icon-medium" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
               </div>
               <h2 class="counter d-inline-flex fw-700 ls-minus-2px mb-10px" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" data-speed="2000" data-to="65">65</h2>
               <span class="counter-suffix">%</span>
               <h4 class="metric-title">Faster Value Optimization</h4>
               <p class="metric-description">Quicker campaign optimization with predictive signals vs. waiting for organic value data.</p>
            </div>
         </div>
         <!-- Metric Card 4: Customer Quality Lift -->
         <div class="col-lg-3 col-md-6 col-sm-6 mb-30px">
            <div class="compact-metric-card h-100 text-center">
               <div class="metric-icon">
                  <i class="line-icon-User icon-medium" style="background: linear-gradient(135deg, #8f76f5 0%, #4a9eff 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
               </div>
               <h2 class="counter d-inline-flex fw-700 ls-minus-2px mb-10px" style="background: linear-gradient(to right, #e958a1, #ff5d74); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" data-speed="2000" data-to="3.2">3.2</h2>
               <span class="counter-suffix">x</span>
               <h4 class="metric-title">Higher LTV Customers</h4>
               <p class="metric-description">Increase in average customer lifetime value from Meta campaigns using predictive targeting.</p>
            </div>
         </div>
      </div>
      <!-- Trust Indicators Section -->
      <!-- Trust Indicators Row -->
      <div class="row justify-content-center mb-5">
         <div class="col-12">
            <p class="results-disclaimer text-center mt-3">
               Results based on average performance across Adzeta clients using Meta Value Optimization with predictive LTV signals via CAPI. Updated December 2024.
            </p>
         </div>
      </div>
   </div>
</section>
<section class="proven-results-section overflow-hidden position-relative py-100px">
   <div class="container">
      <div class="row justify-content-center mb-60px">
         <div class="col-xl-8 col-lg-9 col-md-10 text-center">
            <div class="scale-tag-container">
               <div class="scale-tag">CLIENT SUCCESS</div>
            </div>
            <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">How to make it work</span></h3>
            <p class="mb-0">See how leading brands are leveraging AdZeta's Predictive LTV to transform their social advertising performance and customer acquisition strategies.</p>
         </div>
      </div>
      <!-- Client Success Snapshots -->
      <div class="row justify-content-center">
         <!-- Predictive Modeling Guide Card -->
         <div class="col-lg-6 col-md-6 mb-30px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 600 }'>
            <div class="case-study-card h-100 box-shadow-medium-hover" style="background: linear-gradient(135deg, #f8f9ff, #fff5f8); border: 1px solid #e3e8ff;">
               <div class="case-study-header">
                  <div class="industry-tag" style="background: linear-gradient(135deg, #de347f, #ff5d74); color: white;">Ultimate Guide • Predictive AI</div>
                  <h4 class="case-study-title" style="text-align: left; margin-top: 25px; margin-bottom: 0; padding-top: 10px;">Mastering Predictive Modeling: Insights and Techniques for LTV Revolution</h4>
               </div>
               <div class="case-study-content">
                  <div class="result-highlight">
                     <span class="highlight-number" style="background: linear-gradient(135deg, #de347f, #ff5d74); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">2025</span>
                     <span class="highlight-text">Complete Guide</span>
                  </div>
                  <p class="case-study-text">Master predictive modeling with our comprehensive guide. Learn the three essential AI layers, real-world success stories from Monday.com and HoneyBook, and how to revolutionize your customer LTV with predictive insights.</p>
                  <div class="case-study-attribution">
                     <div class="attribution-photo">
                        <img src="images/case-studies/Natalie-Brooks-adzeta.jpg" alt="Natalie Brooks">
                     </div>
                     <div class="attribution-info">
                        <span class="attribution-name">Natalie Brooks</span>
                        <span class="attribution-title">Growth Marketing Lead</span>
                     </div>
                  </div>
               </div>
               <a href="/mastering-predictive-modeling-insights-techniques.php" class="case-study-link">
               <span>Read Complete Guide</span>
               <span style="margin-left: 8px;">
                   <svg width="12" height="12" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                       <path d="M1.36406 10.3334L0.664062 9.63338L8.96406 1.33337H4.06406V0.333374H10.6641V6.93337H9.66406V2.03337L1.36406 10.3334Z" fill="currentColor"></path>
                   </svg>
               </span>
               </a>
            </div>
         </div>
         <!-- Beauty Clinic Case Study Card -->
         <div class="col-lg-6 col-md-6 mb-30px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 600, "delay": 700 }'>
            <div class="case-study-card h-100 box-shadow-medium-hover">
               <div class="case-study-header">
                  <div class="industry-tag">Beauty & Wellness • E-commerce</div>
                  <h4 class="case-study-title" style="text-align: left; margin-top: 25px; margin-bottom: 0; padding-top: 10px;">Beauty Clinic Multiplies High-Value Clientele 3.2X Through Predictive LTV</h4>
               </div>
               <div class="case-study-content">
                  <div class="result-highlight">
                     <span class="highlight-number">3.2x</span>
                     <span class="highlight-text">ROI Increase</span>
                  </div>
                  <p class="case-study-text">"With AdZeta's AI platform, we've been able to scale our ad spend by 3.2X while significantly increasing the acquisition of our most valuable long-term clients. The predictive LTV insights gave us the confidence to expand our premium service offerings."</p>
                  <div class="case-study-attribution">
                     <div class="attribution-photo">
                        <img src="images/case-studies/Dr-Sophia-Williams.jpg" alt="Dr. Sophia Williams">
                     </div>
                     <div class="attribution-info">
                        <span class="attribution-name">Dr. Sophia Williams</span>
                        <span class="attribution-title">Founder & Lead Esthetician, Luminous Skin Clinic</span>
                     </div>
                  </div>
               </div>
               <a href="/luminous-skin-clinic-adzeta-predictive-ltv-targeting.php" class="case-study-link">
               <span>Read Full Case Study</span>
               <span style="margin-left: 8px;">
                   <svg width="12" height="12" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                       <path d="M1.36406 10.3334L0.664062 9.63338L8.96406 1.33337H4.06406V0.333374H10.6641V6.93337H9.66406V2.03337L1.36406 10.3334Z" fill="currentColor"></path>
                   </svg>
               </span>
               </a>
            </div>
         </div>
      </div>
   </div>
</section>
<!-- SECTION : Proven Results on Google Ads - End -->
<section class="ad-networks-section  position-relative overflow-hidden  pb-0">
   <div class="light-gradient-container position-absolute w-100 h-100"
      style="background: linear-gradient(to bottom, #eae8e6 0%, #ffffff 100%); z-index: 0;"></div>
   <div class="light-accent-gradient position-absolute w-100 h-100"
      style="background:
      radial-gradient(circle at center, rgba(233,88,161,0.05) 0%, rgba(255,255,255,0) 70%),
      radial-gradient(circle at bottom right, rgba(143,118,245,0.03) 0%, rgba(255,255,255,0) 70%);
      z-index: 0;"></div>
   <div id="particles-style-06" class="h-100 position-absolute left-0px top-0 w-100" style="z-index: 1;" data-particle="true" data-particle-options='{"particles": {"number": {"value": 10,"density": {"enable": true,"value_area": 800}},"color": {"value": ["#f7afbd", "#e958a1", "#c5d8f8", "#8f76f5"]},"shape": {"type": "triangle","stroke":{"width":0,"color":"#000000"}},"opacity": {"value": 0.3,"random": false,"anim": {"enable": false,"speed": 1,"sync": false}},"size": {"value": 20,"random": true,"anim": {"enable": false,"sync": true}},"line_linked":{"enable":false,"distance":0,"color":"#ffffff","opacity":0.4,"width":1},"move": {"enable": true,"speed":1,"direction": "top","random": false,"straight": false}},"interactivity": {"detect_on": "canvas","events": {"onhover": {"enable": false,"mode": "repulse"},"onclick": {"enable": false,"mode": "push"},"resize": true}},"retina_detect": false}'></div>
   <div class="container position-relative" style="z-index: 1;">
      <div class="row justify-content-center mb-0 mt-60px">
         <div class="col-xl-8 col-lg-9 col-md-10 text-center">
            <div class="scale-tag-container">
               <div class="scale-tag">YOUR STRATEGIC PARTNER</div>
            </div>
            <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Expertise Driving Your Paid Social Success</span></h3>
            <p>Maximizing profit on platforms like Meta and TikTok requires more than just AI – it demands deep platform knowledge and a strategic partnership. Adzeta brings you both, ensuring our Predictive LTV translates into impactful results for your social ad campaigns.</p>
         </div>
      </div>
      <!-- Ad Platform Blocks with Central Image -->
      <div class="ad-networks-wrapper position-relative" style="height: 1000px;">
         <!-- Central Image -->
         <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: -1;">
            <img class="central-image" src="images/bg-ecom-meta-platforms.png" alt="Ad Networks Integration" style="max-width: 550px; animation: fadeInAnimation 1.5s ease-out forwards, floatAnimation 6s ease-in-out infinite 1.5s;">
         </div>
         <!-- Block 1: Google Ads - Top Left -->
         <div class="ad-platform-card position-absolute" style="top: 100px; left: 20px;" data-anime='{ "translateY": [-30, 0], "opacity": [0,1], "duration": 800, "delay": 200 }'>
            <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
               <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
               <div class="d-flex align-items-center mb-20px position-relative">
                  <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                     <img src="images/meata-ai.png" alt="Google Ads" height="40">
                  </div>
                  <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Meta Platform Mastery</h3>
               </div>
               <p class="mb-20px position-relative fs-15 lh-24 text-left">  In-depth expertise in Value Optimization, Conversions API (CAPI), and audience segmentation for maximizing e-commerce profit on Facebook & Instagram.</p>
               <!--  <div class="text-left position-relative">
                  <a href="/services/ecommerce-ppc" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Google Ads →</a>
                  </div> -->
            </div>
         </div>
         <!-- Block 2: Meta Ads - Top Right -->
         <div class="ad-platform-card position-absolute" style="top: 180px; right: 20px;" data-anime='{ "translateY": [-30, 0], "opacity": [0,1], "duration": 800, "delay": 300 }'>
            <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
               <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
               <div class="d-flex align-items-center mb-20px position-relative">
                  <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                     <img src="images/e-com-focused.png" alt="E-commerce-Focused" height="60" >
                  </div>
                  <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">E-commerce First Social Strategy</h3>
               </div>
               <p class="mb-20px position-relative fs-15 lh-24 text-left">AI models and campaign strategies designed specifically for the unique sales cycles and customer journeys of D2C and online retail brands on social media.</p>
               <!--   <div class="text-left position-relative">
                  <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Meta Ads →</a>
                  </div> -->
            </div>
         </div>
         <!-- Block 3: TikTok Ads - Bottom Left -->
         <div class="ad-platform-card position-absolute" style="bottom: 180px; left: 20px;" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 400 }'>
            <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
               <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
               <div class="d-flex align-items-center mb-20px position-relative">
                  <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                     <img src="images/precision-ai.png" alt="TikTok Ads" height="40" style="filter: drop-shadow(0px 2px 4px rgba(0,0,0,0.1));">
                  </div>
                  <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Predictive LTV for Social Audiences</h3>
               </div>
               <p class="mb-20px position-relative fs-15 lh-24 text-left"> Go beyond surface-level engagement. Our AI accurately forecasts future customer value from social signals to drive smarter ad investments.</p>
               <!--      <div class="text-left position-relative">
                  <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about TikTok Ads →</a>
                  </div> -->
            </div>
         </div>
         <!-- Block 4: Programmatic DSPs - Bottom Right -->
         <div class="ad-platform-card position-absolute" style="bottom: 100px; right: 10px;" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 500 }'>
            <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
               <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
               <div class="d-flex align-items-center mb-20px position-relative">
                  <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                     <img src="images/st-partner.png" alt="Strategic-Partner" height="40" >
                  </div>
                  <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Your Strategic Partner</h3>
               </div>
               <p class="mb-20px position-relative fs-15 lh-24 text-left">Ongoing analysis, transparent reporting, and collaborative support to ensure continuous Google Ads success.</p>
               <!--  <div class="text-left position-relative">
                  <a href="/services/programmatic" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Programmatic →</a>
                  </div> -->
            </div>
         </div>
      </div>
      <!-- Mobile Version (will be shown only on small screens) -->
      <div class="d-block d-lg-none">
         <div class="text-center mb-30px">
            <img class="central-image" src="images/bg-ecom-meta-platforms.png" alt="Ad Networks Integration" style="max-width: 320px;">
         </div>
         <div class="row">
            <div class="col-md-6 col-12 mb-30px">
               <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                  <div class="d-flex align-items-center mb-20px">
                     <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                        <img src="images/meata-ai.png" alt="Meta Ads" height="40">
                     </div>
                     <h3 class="alt-font fw-600 fs-20 mb-0">Meta Platform Mastery</h3>
                  </div>
                  <p class="mb-20px text-left"> In-depth expertise in Value Optimization, Conversions API (CAPI), and audience segmentation for maximizing e-commerce profit on Facebook & Instagram.</p>
                  <!-- <div class="text-left">
                     <a href="/services/ecommerce-ppc" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Google Ads →</a>
                     </div> -->
               </div>
            </div>
            <div class="col-md-6 col-12 mb-30px">
               <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                  <div class="d-flex align-items-center mb-20px">
                     <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                        <img src="images/e-com-focused.png" alt="Meta Ads" height="40">
                     </div>
                     <h3 class="alt-font fw-600 fs-20 mb-0">E-commerce Focused</h3>
                  </div>
                  <p class="mb-20px text-left"> AI models and campaign strategies designed specifically for the unique sales cycles and customer journeys of D2C and online retail brands on social media.</p>
                  <!-- <div class="text-left">
                     <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Meta Ads →</a>
                     </div> -->
               </div>
            </div>
            <div class="col-md-6 col-12 mb-30px">
               <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                  <div class="d-flex align-items-center mb-20px">
                     <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                        <img src="images/precision-ai.png" alt="TikTok Ads" height="40">
                     </div>
                     <h3 class="alt-font fw-600 fs-20 mb-0">Precision AI Integration</h3>
                  </div>
                  <p class="mb-20px text-left">Go beyond surface-level engagement. Our AI accurately forecasts future customer value from social signals to drive smarter ad investments.</p>
                  <!--      <div class="text-left">
                     <a href="/services/paid-social" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about TikTok Ads →</a>
                     </div> -->
               </div>
            </div>
            <div class="col-md-6 col-12 mb-30px">
               <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                  <div class="d-flex align-items-center mb-20px">
                     <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                        <img src="images/st-partner.png" alt="Programmatic Advertising" height="40">
                     </div>
                     <h3 class="alt-font fw-600 fs-20 mb-0">Your Strategic Partner</h3>
                  </div>
                  <p class="mb-20px text-left">Ongoing analysis, transparent reporting, and collaborative support to ensure continuous Google Ads success.</p>
                  <!--  <div class="text-left">
                     <a href="/services/programmatic" class="text-small text-gradient-pink-orange text-decoration-line-bottom fw-500">Learn more about Programmatic →</a>
                     </div> -->
               </div>
            </div>
         </div>
      </div>
      <div class=" p-40px lg-p-30px md-p-25px border-radius-20px">
         <div class="row justify-content-center">
            <div class="col-12 col-xl-7 col-lg-8 col-md-8 text-center margin-5-rem-bottom md-margin-3-rem-bottom ">
               <h2 class="alt-font fw-600 text-dark-gray margin-20px-bottom fs-40 lg-fs-32 md-fs-30 sm-fs-28 ls-minus-1px">Ready to Maximize Your Meta & Social Ads Profit?</h2>
               <p class="w-80 mx-auto md-w-100 md-fs-15 sm-fs-14">See the hidden LTV in your social campaigns. Get a free analysis showing how Adzeta's AI can boost your profitability.</p>
            </div>
         </div>
         <!-- CTA Buttons -->
         <div class="row justify-content-center mb-5">
            <div class="col-12 col-lg-10 text-center">
               <a href="free-ad-audit.php" class="btn btn-large btn-gradient-pink-orange btn-round-edge margin-15px-right sm-margin-15px-bottom">
                  Request Your Free Social Ads Analysis 
                  <span style="margin-left:10px;">
                     <svg width="12" height="12" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.36406 10.3334L0.664062 9.63338L8.96406 1.33337H4.06406V0.333374H10.6641V6.93337H9.66406V2.03337L1.36406 10.3334Z" fill="#fff"></path>
                     </svg>
                  </span>
               </a>
            </div>
         </div>
      </div>
   </div>
</section>
<!-- start enhanced footer -->
<?php include 'footer.php'; ?>