<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600" width="800" height="600">
  <style>
    /* Base styles */
    .st0 {fill:none;}
    .label {font-family:Arial, sans-serif; font-size:14px; font-weight:bold; fill:#1C0D26; text-shadow: 0 0 3px rgba(255,255,255,0.5);}

    /* Color definitions */
    .pink-primary {fill:#F25C84;}
    .pink-hot {fill:#F25EA3;}
    .pink-light {fill:#F25EB0;}
    .purple-dark {fill:#1C0D26;}
    .purple-medium {fill:#401E3D;}
    .cyan-accent {fill:#00E1FF;}

    /* Gradient definitions */
    .pink-gradient {fill:url(#pinkGradient);}
    .purple-gradient {fill:url(#purpleGradient);}
    .cyan-gradient {fill:url(#cyanGradient);}

    /* Glow effects */
    .glow-effect {filter:url(#glow);}

    /* Animation definitions */
    @keyframes pulse {
      0% {opacity: 0.7; filter: brightness(0.9);}
      50% {opacity: 1; filter: brightness(1.2);}
      100% {opacity: 0.7; filter: brightness(0.9);}
    }

    @keyframes flow {
      0% {stroke-dashoffset: 300;}
      100% {stroke-dashoffset: 0;}
    }

    @keyframes grow {
      0% {transform: scaleY(0.1);}
      100% {transform: scaleY(1);}
    }

    @keyframes fadeIn {
      0% {opacity: 0;}
      100% {opacity: 1;}
    }

    /* Apply animations */
    .pulse {
      animation: pulse 3s infinite ease-in-out;
    }

    .data-flow {
      stroke-dasharray: 10;
      stroke-dashoffset: 300;
      animation: flow 3s infinite linear;
    }

    .grow-bar {
      transform-origin: bottom;
      animation: grow 2s ease-out forwards;
    }

    .fade-in {
      opacity: 0;
      animation: fadeIn 1s ease-out forwards;
    }

    .delay-1 {animation-delay: 0.2s;}
    .delay-2 {animation-delay: 0.4s;}
    .delay-3 {animation-delay: 0.6s;}
    .delay-4 {animation-delay: 0.8s;}
    .delay-5 {animation-delay: 1s;}
  </style>

  <defs>
    <!-- Gradients -->
    <linearGradient id="pinkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#F25C84"/>
      <stop offset="50%" stop-color="#F25EA3"/>
      <stop offset="100%" stop-color="#F25EB0"/>
    </linearGradient>

    <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1C0D26"/>
      <stop offset="100%" stop-color="#401E3D"/>
    </linearGradient>

    <linearGradient id="cyanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00E1FF" stop-opacity="0.7"/>
      <stop offset="100%" stop-color="#00E1FF" stop-opacity="0.3"/>
    </linearGradient>

    <!-- Glow filter -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
  </defs>

  <!-- Transparent background -->

  <!-- 1. DATA INPUTS (Left) -->
  <g transform="translate(100, 300)" class="fade-in delay-1">
    <text x="0" y="-120" class="label">DATA INPUTS</text>

    <!-- Server towers -->
    <g class="glow-effect">
      <!-- Server 1 -->
      <g transform="translate(0, -30)">
        <!-- Isometric cube -->
        <path d="M0,0 L40,-20 L80,0 L40,20 Z" class="pink-gradient pulse"/>
        <path d="M0,0 L0,60 L40,80 L40,20 Z" class="purple-gradient"/>
        <path d="M40,20 L40,80 L80,60 L80,0 Z" class="purple-medium"/>

        <!-- Server details -->
        <rect x="10" y="10" width="20" height="5" fill="#00E1FF" opacity="0.7" class="pulse"/>
        <rect x="10" y="20" width="20" height="5" fill="#00E1FF" opacity="0.5"/>
        <rect x="10" y="30" width="20" height="5" fill="#00E1FF" opacity="0.7" class="pulse"/>
      </g>

      <!-- Server 2 -->
      <g transform="translate(20, 20)">
        <!-- Isometric cube -->
        <path d="M0,0 L40,-20 L80,0 L40,20 Z" class="pink-gradient pulse"/>
        <path d="M0,0 L0,60 L40,80 L40,20 Z" class="purple-gradient"/>
        <path d="M40,20 L40,80 L80,60 L80,0 Z" class="purple-medium"/>

        <!-- Server details -->
        <rect x="10" y="10" width="20" height="5" fill="#00E1FF" opacity="0.5"/>
        <rect x="10" y="20" width="20" height="5" fill="#00E1FF" opacity="0.7" class="pulse"/>
        <rect x="10" y="30" width="20" height="5" fill="#00E1FF" opacity="0.5"/>
      </g>
    </g>

    <!-- Data flow -->
    <path d="M90,0 C150,0 150,0 200,-50" stroke="#F25EA3" stroke-width="4" fill="none" class="data-flow" stroke-linecap="round"/>
    <path d="M90,20 C150,20 150,20 200,-30" stroke="#00E1FF" stroke-width="3" fill="none" class="data-flow" stroke-linecap="round"/>
  </g>

  <!-- 2. ADZETA AI CORE (Center) -->
  <g transform="translate(400, 250)" class="fade-in delay-2">
    <text x="-40" y="-100" class="label">ADZETA AI CORE</text>

    <!-- AI Core -->
    <g class="glow-effect pulse">
      <!-- Isometric base -->
      <path d="M-60,-30 L0,-60 L60,-30 L0,0 Z" class="pink-gradient"/>
      <path d="M-60,-30 L-60,30 L0,60 L0,0 Z" class="purple-gradient"/>
      <path d="M0,0 L0,60 L60,30 L60,-30 Z" class="purple-medium"/>

      <!-- Core details -->
      <path d="M-30,-15 L0,-30 L30,-15 L0,0 Z" fill="#00E1FF" opacity="0.7" class="pulse"/>

      <!-- Circuit lines -->
      <path d="M-40,0 L-50,0" stroke="#F25EA3" stroke-width="2" class="pulse"/>
      <path d="M40,0 L50,0" stroke="#F25EA3" stroke-width="2" class="pulse"/>
      <path d="M0,-40 L0,-50" stroke="#F25EA3" stroke-width="2" class="pulse"/>
      <path d="M0,40 L0,50" stroke="#F25EA3" stroke-width="2" class="pulse"/>

      <path d="M-30,-30 L-40,-40" stroke="#00E1FF" stroke-width="2" class="pulse"/>
      <path d="M30,-30 L40,-40" stroke="#00E1FF" stroke-width="2" class="pulse"/>
      <path d="M-30,30 L-40,40" stroke="#00E1FF" stroke-width="2" class="pulse"/>
      <path d="M30,30 L40,40" stroke="#00E1FF" stroke-width="2" class="pulse"/>
    </g>

    <!-- Data flows to platforms -->
    <path d="M60,0 C100,0 100,0 140,-50" stroke="#F25EA3" stroke-width="4" fill="none" class="data-flow" stroke-linecap="round"/>
    <path d="M60,10 C100,10 100,10 140,0" stroke="#F25EA3" stroke-width="4" fill="none" class="data-flow" stroke-linecap="round"/>
    <path d="M60,20 C100,20 100,20 140,50" stroke="#F25EA3" stroke-width="4" fill="none" class="data-flow" stroke-linecap="round"/>
  </g>

  <!-- 3. AD PLATFORMS (Right) -->
  <g transform="translate(600, 250)" class="fade-in delay-3">
    <text x="-20" y="-120" class="label">AD PLATFORMS</text>

    <!-- Google Ads Platform -->
    <g transform="translate(0, -50)" class="glow-effect">
      <!-- Platform base -->
      <path d="M-40,-20 L0,-40 L40,-20 L0,0 Z" class="pink-gradient"/>
      <path d="M-40,-20 L-40,20 L0,40 L0,0 Z" class="purple-gradient"/>
      <path d="M0,0 L0,40 L40,20 L40,-20 Z" class="purple-medium"/>

      <!-- Google Ads logo -->
      <g transform="translate(0, 0) scale(0.5)">
        <!-- Google G -->
        <path d="M-25,0 A25,25 0 1,1 -25,0.1 Z" fill="#4285F4" class="pulse"/>
        <path d="M-25,0 L-25,0 L0,0 L0,25 A25,25 0 0,1 -25,0" fill="#34A853"/>
        <path d="M0,25 A25,25 0 0,1 -25,0 L0,0 Z" fill="#FBBC05"/>
        <path d="M0,0 L0,-25 A25,25 0 0,1 25,0 L0,0 Z" fill="#EA4335"/>
        <circle cx="0" cy="0" r="10" fill="white"/>

        <!-- Ads text -->
        <rect x="10" y="-10" width="25" height="20" rx="2" fill="#FBBC05" class="pulse"/>
        <text x="22" y="3" font-family="Arial" font-size="12" font-weight="bold" fill="white">Ads</text>
      </g>
    </g>

    <!-- Meta Platform -->
    <g transform="translate(0, 0)" class="glow-effect">
      <!-- Platform base -->
      <path d="M-40,-20 L0,-40 L40,-20 L0,0 Z" class="pink-gradient"/>
      <path d="M-40,-20 L-40,20 L0,40 L0,0 Z" class="purple-gradient"/>
      <path d="M0,0 L0,40 L40,20 L40,-20 Z" class="purple-medium"/>

      <!-- Meta logo -->
      <g transform="translate(0, 0) scale(0.5)">
        <!-- Meta infinity symbol -->
        <path d="M-30,0 C-30,-15 -15,-15 0,0 C15,15 30,15 30,0 C30,-15 15,-15 0,0 C-15,15 -30,15 -30,0 Z" fill="#0081FB" class="pulse"/>
        <path d="M-30,0 C-30,15 -15,15 0,0 C15,-15 30,-15 30,0 C30,15 15,15 0,0 C-15,-15 -30,-15 -30,0 Z" fill="#1877F2"/>

        <!-- Meta text -->
        <rect x="-25" y="15" width="50" height="15" rx="2" fill="#0081FB" class="pulse"/>
        <text x="0" y="27" font-family="Arial" font-size="12" font-weight="bold" fill="white" text-anchor="middle">Meta</text>
      </g>
    </g>

    <!-- TikTok Platform -->
    <g transform="translate(0, 50)" class="glow-effect">
      <!-- Platform base -->
      <path d="M-40,-20 L0,-40 L40,-20 L0,0 Z" class="pink-gradient"/>
      <path d="M-40,-20 L-40,20 L0,40 L0,0 Z" class="purple-gradient"/>
      <path d="M0,0 L0,40 L40,20 L40,-20 Z" class="purple-medium"/>

      <!-- TikTok logo -->
      <g transform="translate(0, 0) scale(0.5)">
        <!-- TikTok note icon -->
        <rect x="-25" y="-25" width="50" height="50" rx="10" fill="#000000"/>

        <!-- TikTok musical note -->
        <path d="M-5,-15 L5,-15 L5,5 C5,10 -5,10 -5,5 Z" fill="#00f2ea" class="pulse"/>
        <path d="M5,-15 L5,-5 L15,-5 L15,5 C15,10 5,10 5,5 Z" fill="#ff0050" class="pulse"/>
        <circle cx="-5" cy="5" r="3" fill="white"/>
        <circle cx="5" cy="5" r="3" fill="white"/>
        <circle cx="15" cy="5" r="3" fill="white"/>

        <!-- TikTok text -->
        <rect x="-25" y="15" width="50" height="15" rx="2" fill="#000000" class="pulse"/>
        <text x="0" y="27" font-family="Arial" font-size="12" font-weight="bold" fill="white" text-anchor="middle">TikTok</text>
      </g>
    </g>

    <!-- Data flow to outcomes -->
    <path d="M40,0 C80,0 80,0 120,50" stroke="#F25EA3" stroke-width="4" fill="none" class="data-flow" stroke-linecap="round"/>
  </g>

  <!-- 4. BUSINESS OUTCOMES (Bottom Right) -->
  <g transform="translate(700, 400)" class="fade-in delay-4">
    <text x="-60" y="-100" class="label">BUSINESS OUTCOMES</text>

    <!-- 3D Bar Chart -->
    <g class="glow-effect">
      <!-- Chart base -->
      <path d="M-80,-20 L0,-60 L80,-20 L0,20 Z" class="pink-gradient" opacity="0.3"/>

      <!-- Bars -->
      <g transform="translate(-60, -10)">
        <path d="M0,0 L0,-20 L15,-27 L15,-7 Z" class="pink-gradient grow-bar delay-1"/>
        <path d="M0,0 L15,-7 L30,0 L15,7 Z" class="pink-hot" opacity="0.7"/>
        <path d="M15,-7 L15,-27 L30,-20 L30,0 Z" class="pink-light" opacity="0.7"/>
      </g>

      <g transform="translate(-30, -10)">
        <path d="M0,0 L0,-30 L15,-37 L15,-7 Z" class="pink-gradient grow-bar delay-2"/>
        <path d="M0,0 L15,-7 L30,0 L15,7 Z" class="pink-hot" opacity="0.7"/>
        <path d="M15,-7 L15,-37 L30,-30 L30,0 Z" class="pink-light" opacity="0.7"/>
      </g>

      <g transform="translate(0, -10)">
        <path d="M0,0 L0,-40 L15,-47 L15,-7 Z" class="pink-gradient grow-bar delay-3"/>
        <path d="M0,0 L15,-7 L30,0 L15,7 Z" class="pink-hot" opacity="0.7"/>
        <path d="M15,-7 L15,-47 L30,-40 L30,0 Z" class="pink-light" opacity="0.7"/>
      </g>

      <g transform="translate(30, -10)">
        <path d="M0,0 L0,-50 L15,-57 L15,-7 Z" class="pink-gradient grow-bar delay-4"/>
        <path d="M0,0 L15,-7 L30,0 L15,7 Z" class="pink-hot" opacity="0.7"/>
        <path d="M15,-7 L15,-57 L30,-50 L30,0 Z" class="pink-light" opacity="0.7"/>
      </g>

      <g transform="translate(60, -10)">
        <path d="M0,0 L0,-60 L15,-67 L15,-7 Z" class="pink-gradient grow-bar delay-5"/>
        <path d="M0,0 L15,-7 L30,0 L15,7 Z" class="pink-hot" opacity="0.7"/>
        <path d="M15,-7 L15,-67 L30,-60 L30,0 Z" class="pink-light" opacity="0.7"/>
      </g>

      <!-- Growth arrow -->
      <path d="M0,-70 L20,-80 L40,-70" stroke="#00E1FF" stroke-width="3" fill="none" class="pulse"/>
      <path d="M20,-80 L20,-60" stroke="#00E1FF" stroke-width="3" fill="none" class="pulse"/>
    </g>
  </g>
</svg>
