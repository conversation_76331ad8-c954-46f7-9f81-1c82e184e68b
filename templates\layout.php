<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= e($page_title ?? 'Blog') ?> | <?= e($site_name) ?></title>
    <meta name="description" content="<?= e($page_description ?? 'Read our latest blog posts') ?>">
    
    <!-- SEO Meta Tags -->
    <?php if (isset($canonical_url)): ?>
        <link rel="canonical" href="<?= e($canonical_url) ?>">
    <?php endif; ?>
    
    <?php if (isset($noindex) && $noindex): ?>
        <meta name="robots" content="noindex<?= isset($nofollow) && $nofollow ? ', nofollow' : '' ?>">
    <?php endif; ?>
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= e($og_title ?? $page_title ?? 'Blog') ?>">
    <meta property="og:description" content="<?= e($og_description ?? $page_description ?? '') ?>">
    <meta property="og:type" content="<?= e($og_type ?? 'website') ?>">
    <meta property="og:url" content="<?= e($current_url) ?>">
    <?php if (isset($og_image)): ?>
        <meta property="og:image" content="<?= e($og_image) ?>">
    <?php endif; ?>
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="<?= e($twitter_card_type ?? 'summary_large_image') ?>">
    <meta name="twitter:title" content="<?= e($og_title ?? $page_title ?? 'Blog') ?>">
    <meta name="twitter:description" content="<?= e($og_description ?? $page_description ?? '') ?>">
    <?php if (isset($og_image)): ?>
        <meta name="twitter:image" content="<?= e($og_image) ?>">
    <?php endif; ?>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }
        
        .blog-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
        }
        
        .post-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .post-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .post-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
        }
        
        .post-content h1, .post-content h2, .post-content h3 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }
        
        .post-content img {
            max-width: 100%;
            height: auto;
            margin: 2rem 0;
            border-radius: 8px;
        }
        
        .author-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 2rem;
            margin: 3rem 0;
        }
        
        .related-post {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: box-shadow 0.3s ease;
        }
        
        .related-post:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .back-link {
            display: inline-block;
            margin: 2rem 0;
            padding: 0.5rem 1rem;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        
        .back-link:hover {
            background: #0056b3;
            color: white;
        }
        
        .performance-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
    
    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?= raw($additional_css) ?>
    <?php endif; ?>
</head>
<body>
    <!-- Performance Indicator -->
    <?php if (isset($show_debug) && $show_debug): ?>
        <div class="performance-indicator">
            <?= e($performance_info ?? 'Template Engine') ?>
        </div>
    <?php endif; ?>

    <!-- Header -->
    <header class="blog-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <?php if (isset($breadcrumbs)): ?>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb justify-content-center">
                                <?php foreach ($breadcrumbs as $crumb): ?>
                                    <li class="breadcrumb-item">
                                        <?php if (isset($crumb['url'])): ?>
                                            <a href="<?= e($crumb['url']) ?>" class="text-white"><?= e($crumb['title']) ?></a>
                                        <?php else: ?>
                                            <span class="text-white opacity-75"><?= e($crumb['title']) ?></span>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ol>
                        </nav>
                    <?php endif; ?>
                    
                    <h1 class="display-4 mb-3"><?= e($header_title ?? $page_title ?? 'Blog') ?></h1>
                    
                    <?php if (isset($header_subtitle)): ?>
                        <p class="lead opacity-90"><?= e($header_subtitle) ?></p>
                    <?php endif; ?>
                    
                    <?php if (isset($header_meta)): ?>
                        <div class="text-white opacity-75 mb-3">
                            <?= raw($header_meta) ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="py-5">
        <div class="container">
            <!-- Content Block -->
            <?= raw($content ?? '') ?>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; <?= e($current_year) ?> <?= e($site_name) ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="<?= templateUrl() ?>" class="text-white me-3">Home</a>
                    <a href="<?= templateUrl('blog-templated.php') ?>" class="text-white me-3">Blog</a>
                    <?php if (isLoggedIn()): ?>
                        <a href="<?= templateUrl('adzeta-admin/') ?>" class="text-white">Admin</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </footer>

    <!-- Admin Quick Access -->
    <?php if (isLoggedIn()): ?>
        <div style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
            <div class="btn-group-vertical">
                <a href="<?= templateUrl('adzeta-admin/') ?>" class="btn btn-primary btn-sm mb-2" style="border-radius: 50px; padding: 10px 15px;">
                    <i class="fas fa-cog"></i> Admin
                </a>
                <?php if (isset($clear_cache_url)): ?>
                    <button class="btn btn-info btn-sm" onclick="clearTemplateCache()" style="border-radius: 50px; padding: 10px 15px;">
                        <i class="fas fa-sync"></i> Clear Cache
                    </button>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Cache clearing script -->
    <?php if (isset($clear_cache_url)): ?>
        <script>
            function clearTemplateCache() {
                if (confirm('Clear template cache?')) {
                    fetch('<?= e($clear_cache_url) ?>', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                        body: 'action=clear_template_cache'
                    })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message || 'Cache cleared!');
                        location.reload();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Error clearing cache');
                    });
                }
            }
        </script>
    <?php endif; ?>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additional_js)): ?>
        <?= raw($additional_js) ?>
    <?php endif; ?>
</body>
</html>
