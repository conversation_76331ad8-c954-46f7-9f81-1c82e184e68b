<?php include 'header.php'; ?>
<!-- end header -->
<!-- start hero section -->
<section class="cover-background top-space-padding pb-0 overflow-hidden position-relative lg-pb-30px hero-section ecom-ppc">
   <div class="professional-gradient-container">
      <div class="corner-gradient top-left"></div>
      <div class="corner-gradient top-right"></div>
      <div class="corner-gradient bottom-left"></div>
      <div class="corner-gradient bottom-right"></div>
      <div class="diagonal-gradient"></div>
      <div class="mesh-overlay"></div>
      <div class="vignette-overlay"></div>
   </div>
   <div id="particles-style-03" class="h-100 position-absolute left-0px top-0 w-100" data-particle="true" data-particle-options='{"particles": {"number": {"value": 30,"density": {"enable": true,"value_area": 2000}},"color": {"value": ["#ffffff", "#e958a1", "#d15ec7", "#8f76f5", "#6a4fd9"]},"shape": {"type": ["circle"],"stroke":{"width":0,"color":"#000000"}},"opacity": {"value": 0.12,"random": true,"anim": {"enable": true,"speed": 0.15,"sync": false}},"size": {"value": 1.0,"random": true,"anim": {"enable": true,"speed": 0.15,"sync": false}},"line_linked":{"enable":true,"distance":200,"color":"#8f76f5","opacity":0.06,"width":0.4},"move": {"enable": true,"speed":0.2,"direction": "none","random": true,"straight": false,"out_mode": "out","bounce": false,"attract": {"enable": true,"rotateX": 600,"rotateY": 1200}}},"interactivity": {"detect_on": "canvas","events": {"onhover": {"enable": true,"mode": "grab"},"onclick": {"enable": true,"mode": "repulse"},"resize": true},"modes":{"grab":{"distance":180,"line_linked":{"opacity":0.12}},"repulse":{"distance":150,"duration":0.4}}},"retina_detect": true}'></div>
   <div class="container h-100">
      <!-- Removed distracting background elements for a more professional look -->
      <div class="row align-items-center justify-content-center  h-80 md-mt-30px md-mb-10px pt-4">
         <div class="col-xl-8 col-lg-8 mb-9 position-relative z-index-1 ps-lg-5 text-center" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
            <div class=" mb-15px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
               <span class="fs-12 fw-light text-white align-items-center  opacity-90 primary-font ls-wide">
                  <span class="ai-icon ">
                     <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <title>Security and Privacy Icon</title>
                        <defs>
                           <linearGradient id="paint0_linear_security_icon" x1="2" y1="2" x2="22" y2="22" gradientUnits="userSpaceOnUse">
                              <stop offset="0" stop-color="#e958a1"/>
                              <stop offset="0.5" stop-color="#8f76f5"/>
                              <stop offset="1" stop-color="#4a9eff"/>
                           </linearGradient>
                           <linearGradient id="paint1_linear_security_icon" x1="20" y1="4" x2="4" y2="20" gradientUnits="userSpaceOnUse">
                              <stop offset="0" stop-color="#ffffff"/>
                              <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
                           </linearGradient>
                        </defs>
                        <path d="M12 3.5 L5 7.5 V14.5 C5 19.5 8 21.5 12 21.5 C16 21.5 19 19.5 19 14.5 V7.5 L12 3.5 Z"
                           fill="url(#paint0_linear_security_icon)"
                           fill-opacity="0.1"
                           stroke="url(#paint0_linear_security_icon)"
                           stroke-width="1.5"
                           stroke-linejoin="round" />
                        <circle cx="12" cy="9.5" r="2.5" fill="url(#paint0_linear_security_icon)"/>
                        <rect x="10.75" y="11" width="2.5" height="5" rx="0.5" fill="url(#paint0_linear_security_icon)"/>
                        <path d="M12 3.5 L5 7.5 V14.5 C5 19.5 8 21.5 12 21.5 C16 21.5 19 19.5 19 14.5 V7.5 L12 3.5 Z"
                           stroke="url(#paint1_linear_security_icon)"
                           stroke-width="1"
                           stroke-opacity="0.7"
                           stroke-linejoin="round"
                           fill="none"/>
                     </svg>
                  </span>
                  <span class="text-gradient-purple-blue">SECURITY & PRIVACY STANDARDS</span>
               </span>
            </div>
            <h1 class="alt-font mb-15px fs-50 md-fs-60 sm-fs-60 xs-fs-45 fw-600  heading-gradient" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 1000, "delay": 300, "easing": "easeOutQuad" }'>
               <span class="fw-300">Get enterprise-grade </span><br><span class="heading-gradient">security and compliance</span>
            </h1>
            <div class="alt-font fw-400 fs-16 w-90 sm-w-100 mb-5px xs-mb-5px text-white opacity-75 lh-1-5" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 800, "easing": "easeOutQuint" }'>AdZeta's data handling and protection practices guarantee the highest compliance for your sensitive e-commerce information. We're committed to transparent and secure AI, giving you peace of mind.</div>
            <!--  <div class="d-flex flex-wrap" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 1000, "easing": "easeOutQuint" }'>
               <a href="#get-started" class="btn btn-large btn-hover-animation box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-gradient-pink-orange btn-rounded me-15px mt-15px fw-500 alt-font">
               <span>
               <span class="btn-text fs-16 md-fs-16">Consult a Growth Expert</span>
               <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
               </span>
               </a>
               </div> -->
            <img src="images/security-hero.png" class="w-400px md-w-300px h-400px md-h-300px">
         </div>
      </div>
   </div>
</section>
<!-- end hero section -->
<!-- start section -->
<section class="bg-white pt-20px pb-20px sm-pt-40px">
   <div class="container overlap-section">
      <div class="row justify-content-center overlap-section border-radius-6px overflow-hidden g-0 box-shadow-extra-large">
         <div class="col-lg-12 text-center fs-18  lg-fs-18 ls-minus-05px  bg-white p-30px md-p-20px">At Adzeta, safeguarding your data isn't just a feature – it's the foundation of our AI platform. We partner with leading <span class="fw-600">e-commerce brands</span>, like yours, who demand the highest standards of security and compliance. Discover how we protect your information and empower your growth with trust.</div>
      </div>
   </div>
</section>
<!-- end section -->
<!-- start section: The Adzeta Journey -->
<section>
   <div class="container">
      <div class="row justify-content-center mb-3">
         <div class="col-xxl-6 col-xl-7 col-lg-8 col-md-9 col-sm-10 text-center" data-anime='{"opacity": [0,1], "duration": 800, "delay": 200, "easing": "easeOutQuad"}'>
            <h2 class="alt-font fw-700 ls-minus-1px" data-anime='{"translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 400, "easing": "easeOutQuad"}'><span class="modern-heading-gradient">Securing Your Data with Confidence</span></h2>
            <p data-anime='{"translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 600, "easing": "easeOutQuad"}'> Our multi-layered security approach ensures your data is protected at every stage. Your trust is our priority. </p>
         </div>
      </div>
      <div class="row row-cols-1 row-cols-lg-3 row-cols-md-2 justify-content-center">
         <!-- start features box item -->
         <div class="col icon-with-text-style-04 transition-inner-all md-mb-30px" data-anime='{"translateY": [50, 0], "opacity": [0,1], "duration": 800, "delay": 800, "easing": "easeOutQuad"}'>
            <div class="feature-box border-radius-20px p-15 lg-pt-15 lg-pb-15 lg-ps-10 lg-pe-10 last-paragraph-no-margin">
               <div class="feature-box-icon">
                  <img class="mb-25px w-50" src="images/line-icon-Medal-2.png">
               </div>
               <div class="feature-box-content">
                  <span class="d-inline-block alt-font text-dark-gray fw-700 fs-19 mb-10px">Compliance</span>
                  <p>We meet strict global data protection laws, including GDPR and CCPA, and follow rigorous industry standards like SOC 2. Regular audits and policy reviews keep us aligned with evolving compliance requirements.</p>
               </div>
               <div class="feature-box-overlay bg-solitude-light border-radius-10px"></div>
            </div>
         </div>
         <!-- end features box item -->
         <!-- start features box item -->
         <div class="col icon-with-text-style-04 transition-inner-all md-mb-30px" data-anime='{"translateY": [50, 0], "opacity": [0,1], "duration": 800, "delay": 1000, "easing": "easeOutQuad"}'>
            <div class="feature-box border-radius-20px  p-15 lg-pt-15 lg-pb-15 lg-ps-10 lg-pe-10 last-paragraph-no-margin">
               <div class="feature-box-icon">
                  <img class="mb-25px w-60" src="images/line-icon-Knight.png">
               </div>
               <div class="feature-box-content">
                  <span class="d-inline-block alt-font text-dark-gray fw-700 fs-19 mb-10px">Data Handling</span>
                  <p>Your data is encrypted in transit and at rest, hosted on trusted cloud platforms. Access is strictly limited to authorized personnel, ensuring it’s used only for its intended, permitted purpose.</p>
               </div>
               <div class="feature-box-overlay bg-solitude-light border-radius-10px"></div>
            </div>
         </div>
         <!-- end features box item -->
         <!-- start features box item -->
         <div class="col icon-with-text-style-04 transition-inner-all" data-anime='{"translateY": [50, 0], "opacity": [0,1], "duration": 800, "delay": 1200, "easing": "easeOutQuad"}'>
            <div class="feature-box border-radius-20px p-15 lg-pt-15 lg-pb-15 lg-ps-10 lg-pe-10 last-paragraph-no-margin">
               <div class="feature-box-icon">
                  <img class="mb-25px w-60" src="images/line-icon-Gear-2.png">
               </div>
               <div class="feature-box-content">
                  <span class="d-inline-block alt-font text-dark-gray fw-700 fs-19 mb-10px">Privacy</span>
                  <p>Built with privacy at its core, AdZeta’s AI leverages anonymized and pseudonymized data where possible. This approach limits PII exposure while optimizing prediction accuracy and user safety.</p>
               </div>
               <div class="feature-box-overlay bg-solitude-light border-radius-10px"></div>
            </div>
         </div>
         <!-- end features box item -->
      </div>
   </div>
</section>
<!-- end section: The Adzeta Journey -->
<!-- start section: compliance -->
<section class="overflow-hidden" style="background-color:#f7f5f3;">
   <div class="container position-relative">
      <!-- Using the exact structure from the example -->
      <div class="security-compliance-wrapper is-visible" data-anime='{"translateY": [50, 0], "opacity": [0,1], "duration": 1000, "delay": 200, "easing": "easeOutQuad"}'>
         <!-- Text wrapper -->
         <div class="security-compliance-text-wrapper">
            <h2 class=" alt-font ls-minus-1px mb-3 md-mt-10" data-anime='{"translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 400, "easing": "easeOutQuad"}'><span class="modern-heading-gradient">Verified Security: Our Certifications & Standards</span></h2>
            <p class="security-compliance-h2-p" data-anime='{"translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 600, "easing": "easeOutQuad"}'>Achieving and maintaining industry-recognized security certifications is a core part of our commitment to data protection. These independent verifications demonstrate AdZeta's adherence to stringent controls for security, availability, processing integrity, confidentiality, and privacy</p>
         </div>
         <!-- Certification logos wrapper -->
         <div class="security-compliance-img-wrapper" data-anime='{"scale": [0.8, 1], "opacity": [0,1], "duration": 800, "delay": 800, "easing": "easeOutQuad"}'>
            <img src="images/logos/aicpa-soc.png" loading="lazy" alt="AICPA SOC tag" class="security-compliance-img mb-4">
            <!--          <div class="security-compliance-label">SOC 2</div> -->
            <img src="images/logos/iso-20022.png" loading="lazy" alt="ISO 20022 tag" class="security-compliance-img">
            <!--  <div class="security-compliance-label">ISO 20022</div> -->
         </div>
         <!-- Absolutely positioned main image with exact CSS from reference -->
         <img class="security-compliance-abaolute-img is-visible" src="images/Data-Security-Certifications-Visual.webp" alt="Data Security Certifications Visual" loading="lazy" data-anime='{"scale": [0.9, 1], "opacity": [0,1], "duration": 1000, "delay": 1000, "easing": "easeOutQuad"}'>
         <!-- Anchor point for navigation -->
         <div id="compliance" class="security-anchor-point"></div>
      </div>
      <div class="row justify-content-center">
         <div class="col-lg-12">
            <h2 class="alt-font fw-700 ls-minus-1px mb-5 text-dark-gray text-center" data-anime='{"translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 200, "easing": "easeOutQuad"}'><span class="modern-heading-gradient">What Type Of Data We Handle?</span></h2>
            <div class="row g-4 d-flex align-items-stretch">
               <div class="col-12 col-lg-6" data-anime='{"translateX": [-50, 0], "opacity": [0,1], "duration": 800, "delay": 400, "easing": "easeOutQuad"}'>
                  <div class="bg-white border-radius-24px p-40px sm-p-25px last-paragraph-no-margin box-shadow-medium text-left data-inner-wrapper h-100 d-flex flex-column position-relative">
                     <div class="text-center mb-30px">
                        <img src="images/tick-data.jpg" class="w-25 d-block mx-auto mb-20px" alt="Data We Analyze">
                     </div>
                     <h4 class="alt-font text-dark-gray fw-700 fs-22 sm-fs-20 mb-25px text-center lh-30">Data We Analyze for LTV Prediction</h4>
                     <div class="flex-grow-1">
                        <p class="fs-16 lh-30 mb-25px text-center sm-text-start">To build accurate Predictive AI models, AdZeta primarily utilizes your first-party e-commerce data:</p>
                        <div class="data-category mb-20px">
                           <h6 class="fw-600 text-dark-gray mb-1 fs-16">Transactional Data:</h6>
                           <p class="fs-15 lh-28 text-medium-gray mb-0">Order history, purchase values, product details</p>
                        </div>
                        <div class="data-category mb-20px">
                           <h6 class="fw-600 text-dark-gray mb-1 fs-16">Behavioral Data:</h6>
                           <p class="fs-15 lh-28 text-medium-gray mb-0">Website/app engagement (views, clicks, add-to-carts)</p>
                        </div>
                        <div class="data-category mb-25px">
                           <h6 class="fw-600 text-dark-gray mb-1 fs-16">Customer Attributes:</h6>
                           <p class="fs-15 lh-28 text-medium-gray mb-0">Relevant segments, subscription status from CRM/CDP</p>
                        </div>
                        <div class="position-absolute start-0 end-0 d-flex align-items-center justify-content-center" style="bottom: 20px;">
                           <i class="bi bi-shield-check text-success me-2 fs-14"></i>
                           <p class="fs-13 text-medium-gray mb-0 fw-500 lh-20">GDPR/CCPA Compliant Processing</p>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col-12 col-lg-6" data-anime='{"translateX": [50, 0], "opacity": [0,1], "duration": 800, "delay": 600, "easing": "easeOutQuad"}'>
                  <div class="bg-white border-radius-24px p-40px sm-p-25px last-paragraph-no-margin box-shadow-medium text-left data-inner-wrapper h-100 d-flex flex-column position-relative">
                     <div class="text-center mb-30px">
                        <img src="images/cross-data.jpg" class="w-25 d-block mx-auto mb-20px" alt="Data We Don't Require">
                     </div>
                     <h4 class="alt-font text-dark-gray fw-700 fs-22 sm-fs-20 mb-25px text-center lh-30">Data We Typically Don't Require</h4>
                     <div class="flex-grow-1">
                        <p class="fs-16 lh-30 mb-25px text-center sm-text-start">AdZeta's LTV prediction models are designed to be effective without needing overly sensitive PII. We generally do not require access to:</p>
                        <div class="data-category mb-20px">
                           <h6 class="fw-600 text-dark-gray mb-1 fs-16">Payment Details:</h6>
                           <p class="fs-15 lh-28 text-medium-gray mb-0">Direct payment instrument details (credit card numbers)</p>
                        </div>
                        <div class="data-category mb-20px">
                           <h6 class="fw-600 text-dark-gray mb-1 fs-16">Sensitive Attributes:</h6>
                           <p class="fs-15 lh-28 text-medium-gray mb-0">Highly sensitive personal attributes unrelated to e-commerce behavior</p>
                        </div>
                        <div class="data-category mb-25px">
                           <h6 class="fw-600 text-dark-gray mb-1 fs-16">Unnecessary PII:</h6>
                           <p class="fs-15 lh-28 text-medium-gray mb-0">Information not relevant to purchase prediction</p>
                        </div>
                        <div class="position-absolute start-0 end-0 d-flex align-items-center justify-content-center" style="bottom: 20px;">
                           <i class="bi bi-lock-fill text-primary me-2 fs-14"></i>
                           <p class="fs-13 text-medium-gray mb-0 fw-500 lh-20">Privacy-First Data Protection</p>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <div class="row mt-15 mb-20 justify-content-left">
         <div class="col-lg-12">
            <div class="secure-data-storage bg-white border-radius-24px last-paragraph-no-margin box-shadow-small text-left">
               <!-- Inner Row: controls layout inside -->
               <div class="row">
                  <!-- Left side: content -->
                  <div class="col-lg-7 p-60px sm-p-40px">
                     <h2 class="alt-font ls-minus-1px mb-30px text-left"><span class="modern-heading-gradient">Secure & Segregated Data Storage</span></h2>
                     <p class="fs-17 lh-30 mb-35px text-medium-gray">Your data security is paramount. We employ industry-leading cloud infrastructure and best practices:</p>
                     <div class="security-feature mb-20px">
                        <h5 class="fw-600 text-dark-gray mb-5px fs-16">Cloud Infrastructure</h5>
                        <p class="fs-15 lh-26 text-medium-gray mb-0">Leveraging secure and scalable solutions from providers like AWS or Google Cloud</p>
                     </div>
                     <div class="security-feature mb-20px">
                        <h5 class="fw-600 text-dark-gray mb-5px fs-16">Encryption</h5>
                        <p class="fs-15 lh-26 text-medium-gray mb-0">Data is encrypted both in transit (SSL/TLS) and at rest (e.g., AES-256)</p>
                     </div>
                     <div class="security-feature mb-20px">
                        <h5 class="fw-600 text-dark-gray mb-5px fs-16">Data Segregation</h5>
                        <p class="fs-15 lh-26 text-medium-gray mb-0">Each client's data is stored in a logically isolated and secure environment to prevent unauthorized access or co-mingling</p>
                     </div>
                     <div class="security-feature mb-0">
                        <h5 class="fw-600 text-dark-gray mb-5px fs-16">Access Controls</h5>
                        <p class="fs-15 lh-26 text-medium-gray mb-0">Strict internal access controls and audit logs are maintained for all data processing activities</p>
                     </div>
                  </div>
                  <!-- Right side: empty for spacing -->
                  <div class="col-lg-5"></div>
               </div>
            </div>
         </div>
      </div>
      <div class="security-compliance-wrapper is-visible">
         <!-- Text wrapper -->
         <div class="security-compliance-text-wrapper">
            <h2 class="alt-font ls-minus-1px mb-3 text-dark-gray md-mt-10"><span class="modern-heading-gradient">Maximizing Insight While Prioritizing Privacy</span></h2>
            <p class="security-compliance-h2-p">Our AI is designed to extract powerful predictive insights from aggregated and behavioral patterns. We employ data minimization and anonymization/pseudonymization techniques where possible to deliver exceptional LTV forecasting while upholding the highest standards of customer data privacy. Your trust is integral to our partnership.</p>
         </div>
         <!-- Certification logos wrapper -->
         <!-- Absolutely positioned main image with exact CSS from reference -->
         <img class="security-compliance-abaolute-img is-visible" src="images/Data-Security-Certifications-Visual-1.webp" alt="Data Security Certifications Visual" loading="lazy">
         <!-- Anchor point for navigation -->
         <div id="compliance" class="security-anchor-point"></div>
      </div>
      <div>
         <!-- Text wrapper -->
         <div class="bg-white border-radius-24px p-40px sm-p-30px last-paragraph-no-margin box-shadow-small text-left data-inner-wrapper">
            <h2 class="fs-30 alt-font fw-600 ls-minus-1px mb-3 text-dark-gray">How to report a vulnerability?</h2>
            <p class="security-compliance-h2-p">If you believe you found a security breach, please report it here.Have more questions about our security? <a class="text-base-color" href="free-ad-audit.php">Contact Us</a></p>
         </div>
         <!-- Certification logos wrapper -->
         <!-- Absolutely positioned main image with exact CSS from reference -->
         <!-- Anchor point for navigation -->
         <div id="compliance" class="security-anchor-point"></div>
      </div>
   </div>
   <!-- end section: compliance -->
   <style>
      .bg-solitude-light{background-color:#f7f5f3;}
      @keyframes floatAnimation {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-15px); }
      100% { transform: translateY(0px); }
      }
      @keyframes fadeInAnimation {
      0% { opacity: 0; transform: scale(0.95); }
      100% { opacity: 1; transform: scale(1); }
      }
      @media (max-width: 991px) {
      .ad-networks-wrapper {
      height: auto !important;
      display: none;
      }
      }
      @media (min-width: 992px) {
      .ad-platform-card {
      width: 420px;
      z-index: 3;
      }
      }
      .card-inner {
      transition: transform 0.3s ease, border-color 0.3s ease;
      border: 1px solid rgba(0, 0, 0, 0.05);
      }
      .card-inner:hover {
      transform: translateY(-5px);
      border-color: rgba(233, 88, 161, 0.1);
      }
      .border-radius-24px {
      border-radius: 24px !important;
      }
      /* Timeline indicators */
      .timeline-indicator {
      display: inline-block;
      background: linear-gradient(to right, #e958a1, #ff5d74);
      color: white;
      font-size: 12px;
      padding: 4px 12px;
      border-radius: 20px;
      margin-top: 8px;
      font-weight: 500;
      }
      /* Journey steps styling */
      .onboarding-journey .journey-step {
      position: relative;
      margin-bottom: 30px;
      display: flex;
      align-items: flex-start;
      }
      /* Step icon container */
      .step-icon-container {
      position: relative;
      width: 60px;
      height: 60px;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      z-index: 2;
      flex-shrink: 0;
      }
      /* Step number */
      .step-number {
      position: absolute;
      top: -5px;
      right: -5px;
      width: 24px;
      height: 24px;
      background: linear-gradient(to right, #e958a1, #ff5d74);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      }
      /* Step icon */
      .step-icon-container i {
      font-size: 24px;
      background: linear-gradient(to right, #e958a1, #ff5d74);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
      }
      /* Step content */
      .step-content {
      flex-grow: 1;
      padding-top: 5px;
      }
      /* Connecting line between steps */
      .onboarding-journey .journey-step:not(:last-child):after {
      content: '';
      position: absolute;
      top: 60px;
      left: 30px;
      height: calc(100% - 30px);
      width: 2px;
      background: linear-gradient(to bottom, rgba(233, 88, 161, 0.7), rgba(255, 93, 116, 0.3));
      z-index: 1;
      }
      /* Enterprise Implementation Process */
      .enterprise-implementation-process {
      padding: 60px 0;
      max-width: 1200px;
      margin: 0 auto;
      position: relative;
      }
      .process-header {
      margin-bottom: 60px;
      }
      .process-header h4 {
      margin-bottom: 10px;
      font-size: 22px;
      letter-spacing: -0.5px;
      }
      .process-header p {
      color: #666;
      font-size: 17px;
      }
      /* Implementation Timeline Container */
      .implementation-timeline-container {
      position: relative;
      padding: 30px 0;
      margin-bottom: 40px;
      }
      /* Timeline Steps Wrapper */
      .timeline-steps-wrapper {
      position: relative;
      padding: 30px 0;
      margin-right: 30px;
      }
      /* Timeline Step */
      .timeline-step {
      position: relative;
      padding: 25px 0 25px 40px;
      margin-bottom: 20px;
      transition: all 0.3s ease;
      }
      .timeline-step:last-child {
      margin-bottom: 0;
      }
      .timeline-step:hover {
      cursor: pointer;
      }
      /* Timeline Step Connector */
      .timeline-step-connector {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 30px;
      }
      /* Create a continuous vertical line for all steps */
      .timeline-steps-wrapper::before {
      content: '';
      position: absolute;
      left: 14px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: linear-gradient(to bottom, rgba(233, 88, 161, 0.7), rgba(255, 93, 116, 0.3));
      z-index: 1;
      }
      /* Individual connector lines are now hidden since we use the continuous line */
      .connector-line {
      display: none;
      }
      .connector-dot {
      position: absolute;
      left: 10px;
      top: 65px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: white;
      border: 2px solid #e958a1;
      z-index: 2;
      transition: all 0.3s ease;
      box-shadow: 0 0 0 4px rgba(233, 88, 161, 0.1);
      }
      .timeline-step.active .connector-dot {
      background: #e958a1;
      box-shadow: 0 0 0 4px rgba(233, 88, 161, 0.2);
      }
      .timeline-step:hover .connector-dot {
      background: #e958a1;
      box-shadow: 0 0 0 4px rgba(233, 88, 161, 0.2);
      }
      /* Timeline Step Content */
      .timeline-step-content {
      display: flex;
      align-items: flex-start;
      background: white;
      border-radius: 24px;
      padding: 25px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.06);
      transition: all 0.4s ease;
      border: 1px solid rgba(233, 88, 161, 0.05);
      }
      .timeline-step.active .timeline-step-content {
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
      border-left: 4px solid #e958a1;
      background: linear-gradient(to right, rgba(233, 88, 161, 0.02), white);
      }
      .timeline-step:hover .timeline-step-content {
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
      border-left: 4px solid #e958a1;
      background: linear-gradient(to right, rgba(233, 88, 161, 0.02), white);
      }
      /* Step Number */
      .step-number {
      width: 56px;
      height: 56px;
      background: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 22px;
      font-weight: 700;
      color: #e958a1;
      margin-right: 22px;
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      flex-shrink: 0;
      border: 1px solid rgba(233, 88, 161, 0.1);
      position: relative;
      }
      .timeline-step.active .step-number {
      background: linear-gradient(to right, #e958a1, #ff5d74);
      color: white;
      box-shadow: 0 6px 15px rgba(233, 88, 161, 0.3);
      }
      .timeline-step:hover .step-number {
      background: linear-gradient(to right, #e958a1, #ff5d74);
      color: white;
      box-shadow: 0 6px 15px rgba(233, 88, 161, 0.3);
      }
      /* Step Details */
      .step-details {
      flex-grow: 1;
      }
      .step-details h5 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 10px 0;
      color: #333;
      }
      .step-details p {
      font-size: 15px;
      color: #666;
      line-height: 1.5;
      margin: 0 0 15px 0;
      }
      /* Timeline Indicator */
      .timeline-indicator {
      display: inline-block;
      background: #f8f8f8;
      color: #666;
      font-size: 13px;
      padding: 8px 18px;
      border-radius: 30px;
      font-weight: 500;
      margin-top: 5px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      }
      .timeline-indicator.highlight {
      background: linear-gradient(to right, #e958a1, #ff5d74);
      color: white;
      box-shadow: 0 5px 15px rgba(233, 88, 161, 0.3);
      border: none;
      }
      /* Implementation Illustration */
      .implementation-illustration {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      }
      .illustration-container {
      width: 100%;
      max-width: 500px;
      margin: 0 auto;
      }
      .implementation-svg-container {
      width: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 300px; /* Provide minimum height */
      }
      .implementation-svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      }
      /* For image content */
      .implementation-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      }
      /* SVG Animation Classes */
      .platform-circle {
      animation: gentlePulse 4s infinite ease-in-out;
      filter: drop-shadow(0 0 3px rgba(233, 88, 161, 0.3));
      }
      .connection-line {
      stroke-dasharray: 100;
      stroke-dashoffset: 100;
      animation: none;
      opacity: 0.4;
      transition: all 0.5s ease;
      }
      .connection-circle {
      opacity: 0.6;
      transition: all 0.5s ease;
      }
      .step-icon {
      transition: all 0.5s ease;
      }
      /* SVG Text Styling */
      .implementation-svg text {
      font-family: 'Inter', sans-serif;
      opacity: 0.9;
      }
      /* Timeline indicators styling */
      .timeline-indicators rect {
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      }
      /* Pulse circles for active elements */
      .pulse-circle {
      animation: none;
      transition: all 0.5s ease;
      }
      /* Active state styling */
      .connection-line.active {
      stroke-dashoffset: 0;
      opacity: 1;
      stroke-width: 3;
      filter: drop-shadow(0 0 3px rgba(233, 88, 161, 0.5));
      }
      .connection-circle.active {
      opacity: 1;
      filter: drop-shadow(0 0 5px rgba(233, 88, 161, 0.4));
      }
      .pulse-circle.active {
      animation: pulseFade 2s infinite ease-in-out;
      }
      /* Step-specific animations */
      .data-line.active, .data-circle.active, .step-1-icon.active {
      animation-delay: 0s;
      }
      .ai-line.active, .ai-circle.active, .step-2-icon.active {
      animation-delay: 0.1s;
      }
      .platform-line.active, .platform-circle.active, .step-3-icon.active {
      animation-delay: 0.2s;
      }
      .launch-line.active, .launch-circle.active, .step-4-icon.active {
      animation-delay: 0.3s;
      }
      /* Responsive SVG text adjustments */
      @media (max-width: 767px) {
      .implementation-svg-container {
      padding-bottom: 100%; /* Adjust aspect ratio for mobile */
      }
      .implementation-svg text {
      font-size: 90%; /* Slightly smaller text on mobile */
      }
      }
      @keyframes gentlePulse {
      0% {
      transform: scale(1);
      opacity: 0.9;
      }
      50% {
      transform: scale(1.02);
      opacity: 1;
      }
      100% {
      transform: scale(1);
      opacity: 0.9;
      }
      }
      @keyframes pulseFade {
      0% {
      opacity: 0;
      transform: scale(0.8);
      }
      50% {
      opacity: 0.5;
      transform: scale(1.1);
      }
      100% {
      opacity: 0;
      transform: scale(1.2);
      }
      }
      /* Process Footer */
      .process-footer {
      margin-top: 90px;
      text-align: center;
      }
      .support-badge {
      display: inline-flex;
      align-items: center;
      background: white;
      padding: 12px 25px;
      border-radius: 40px;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
      font-weight: 500;
      color: #333;
      margin-bottom: 15px;
      font-size: 16px;
      }
      .support-badge i {
      margin-right: 10px;
      color: #e958a1;
      font-size: 20px;
      }
      /* Responsive adjustments */
      @media (max-width: 1199px) {
      .implementation-step {
      width: 24%;
      }
      .step-card {
      padding: 20px 15px;
      }
      .step-header h5 {
      font-size: 17px;
      }
      }
      @media (max-width: 991px) {
      .implementation-timeline-container {
      padding: 20px 0;
      }
      .timeline-steps-wrapper {
      margin-right: 0;
      margin-bottom: 40px;
      }
      .implementation-illustration {
      padding-top: 30px;
      }
      }
      @media (max-width: 767px) {
      .enterprise-implementation-process {
      padding: 40px 0;
      }
      .process-header {
      margin-bottom: 40px;
      }
      .process-header h4 {
      font-size: 24px;
      }
      /* Adjust timeline wrapper for mobile */
      .timeline-steps-wrapper {
      padding-left: 10px;
      }
      /* Adjust the continuous line position for mobile */
      .timeline-steps-wrapper::before {
      left: 12px;
      }
      .timeline-step {
      padding: 15px 0 15px 30px;
      margin-bottom: 25px;
      }
      .timeline-step-content {
      padding: 18px;
      }
      .connector-dot {
      left: -2px;
      top: 45px;
      }
      .step-number {
      width: 45px;
      height: 45px;
      font-size: 18px;
      margin-right: 15px;
      border-radius: 50%;
      }
      .step-details h5 {
      font-size: 16px;
      }
      .step-details p {
      font-size: 14px;
      }
      .timeline-indicator {
      font-size: 12px;
      padding: 6px 14px;
      }
      .implementation-svg-container {
      padding-bottom: 100%; /* Adjust aspect ratio for mobile */
      margin-top: 20px;
      }
      /* Add some space between timeline and illustration on mobile */
      .implementation-illustration {
      margin-top: 20px;
      }
      }
      @media (max-width: 575px) {
      .process-header h4 {
      font-size: 20px;
      }
      .process-header p {
      font-size: 15px;
      }
      /* Adjust timeline wrapper for small screens */
      .timeline-steps-wrapper {
      padding-left: 5px;
      }
      /* Adjust the continuous line position for small screens */
      .timeline-steps-wrapper::before {
      left: 10px;
      }
      .timeline-step {
      padding: 12px 0 12px 25px;
      margin-bottom: 20px;
      }
      .connector-dot {
      left: 2px;
      top: 68px;
      top: 68px;
      width: 8px;
      height: 8px;
      }
      .timeline-step-content {
      padding: 15px;
      border-radius: 18px;
      }
      .step-number {
      width: 40px;
      height: 40px;
      font-size: 16px;
      margin-right: 12px;
      border-radius: 50%;
      }
      .step-details h5 {
      font-size: 15px;
      margin-bottom: 8px;
      }
      .step-details p {
      font-size: 13px;
      margin-bottom: 10px;
      line-height: 1.4;
      }
      .timeline-indicator {
      font-size: 11px;
      padding: 5px 12px;
      }
      /* Keep the horizontal layout for better readability */
      .timeline-step-content {
      flex-direction: row;
      align-items: center;
      }
      /* Adjust illustration for small screens */
      .implementation-svg-container {
      padding-bottom: 120%; /* Taller aspect ratio for small screens */
      }
      .support-badge {
      font-size: 14px;
      padding: 10px 20px;
      }
      }
   </style>
   <!-- Styles for compliance section -->
   <style>
      /* Base styles for desktop */
      .security-compliance-wrapper {
      opacity: 1;
      grid-column-gap: 6.875rem;
      grid-row-gap: 6.875rem;
      background-color: var(--white);
      border-radius: 30px;
      justify-content: flex-end;
      align-items: center;
      width: 57.375rem;
      margin-bottom: 14.75rem;
      margin-left: auto;
      margin-top:5rem;
      padding: 3.8125rem 4.75rem 3.8125rem 16.6875rem;
      display: flex;
      position: relative;
      right: -3.9375rem;
      }
      .security-compliance-text-wrapper {
      flex: 1;
      }
      .security-compliance-img-wrapper {
      position: absolute;
      top: 3.8125rem;
      right: 4.75rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      }
      .security-compliance-img {
      width: 80px;
      height: auto;
      display: block;
      }
      .security-compliance-label {
      text-align: center;
      font-size: 10px;
      color: #0077b5;
      font-weight: 600;
      margin: 0;
      }
      .security-compliance-abaolute-img {
      z-index: 1;
      width: 42rem;
      height: auto;
      position: absolute;
      inset: auto auto -13rem -22.75rem;
      opacity: 1;
      vertical-align: middle;
      max-width: 100%;
      display: inline-block;
      }
      /* Responsive styles for tablets and mobile */
      @media screen and (max-width: 991px) {
      .security-compliance-wrapper {
      width: 100% !important;
      max-width: 100% !important;
      right: 0 !important;
      margin-left: auto !important;
      margin-right: auto !important;
      padding: 8rem 2rem 4rem 2rem !important;
      margin-bottom: 8rem !important;
      margin-top:15rem !important;
      flex-direction: column !important;
      justify-content: flex-start !important;
      align-items: center !important;
      text-align: center !important;
      }
      .security-compliance-text-wrapper {
      max-width: 100% !important;
      margin-bottom: 2rem !important;
      order: 0 !important;
      }
      .security-compliance-img-wrapper {
      position: relative !important;
      top: auto !important;
      right: auto !important;
      flex-direction: row !important;
      justify-content: center !important;
      align-items: center !important;
      gap: 2.5rem !important;
      margin: 0 auto 2rem auto !important;
      order: 1 !important;
      }
      .security-compliance-img {
      width: 90px !important;
      }
      .security-compliance-abaolute-img {
      width: 25rem !important;
      position: absolute !important;
      top: -10rem !important;
      left: 54% !important;
      transform: translateX(-50%) !important;
      right: auto !important;
      bottom: auto !important;
      margin-left: auto !important;
      margin-right: auto !important;
      order: 2 !important;
      z-index: 1 !important;
      }
      }
      /* Additional responsive styles for smaller mobile */
      @media screen and (max-width: 576px) {
      .security-compliance-wrapper {
      max-width: 100% !important;
      padding: 6rem 1.5rem 3rem 1.5rem !important;
      margin-bottom: 6rem !important;
      margin-top: 15rem !important;
      }
      .security-compliance-img-wrapper {
      gap: 1.5rem !important;
      }
      .security-compliance-img {
      width: 80px !important;
      }
      .security-compliance-abaolute-img {
      width: 25rem !important;
      top: -12rem !important;
      left: 55% !important;
      transform: translateX(-50%) !important;
      position: absolute !important;
      right: auto !important;
      bottom: auto !important;
      }
      .security-compliance-h2-p {
      font-size: 1.2rem !important;
      }
      }
   </style>
   <!-- Secure Data Storage Simple Responsive Styles -->
   <style>
      /* Desktop - background image on the right */
      .secure-data-storage {
      background-image: url('images/data-adzeta.jpg');
      background-position: 100%;
      background-repeat: no-repeat;
      background-size: cover;
      }
      /* Mobile - background image at bottom */
      @media (max-width: 991.98px) {
      .secure-data-storage {
      background-image: url('images/data-adzeta-mobile.jpg');
      background-position: bottom center;
      background-size: 100%;
      padding-bottom: 35rem;
      }
      }
   </style>
   <!-- start enhanced footer -->
</section>
<?php include 'footer.php'; ?>