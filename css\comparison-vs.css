/* Minimal CSS for comparison section using existing pricing table styles */

.comparison-section {
    padding: 80px 0;
    position: relative;
    background-color: #fafafa;
}

/* VS Badge */
.vs-badge {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #e958a1, #8f76f5);
    color: white;
    font-weight: 700;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    z-index: 3;
    letter-spacing: 0.5px;
    animation: pulse-vs 2s infinite ease-in-out;
}

@keyframes pulse-vs {
    0% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        box-shadow: 0 12px 30px rgba(233, 88, 161, 0.4);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    }
}

/* Mobile VS Badge animation */
.vs-badge.d-lg-none {
    position: relative;
    top: auto;
    left: auto;
    transform: none;
    animation: pulse-vs-mobile 2s infinite ease-in-out;
}

@keyframes pulse-vs-mobile {
    0% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 12px 30px rgba(233, 88, 161, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(233, 88, 161, 0.3);
    }
}

/* Custom colors for comparison */
.traditional-badge {
    background: rgba(255, 90, 90, 0.1);
    color: #ff5a5a;
}

.adzeta-badge {
    background: rgba(233, 88, 161, 0.1);
    color: #e958a1;
}

/* Results section */
.results-section {
    margin-top: 60px;
    text-align: center;
}

.results-title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

.results-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, #e958a1, #8f76f5);
}

/* Stats */
.stats-container {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 48px;
    font-weight: 700;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 15px;
    color: #666;
    font-weight: 500;
}

/* Mobile adjustments */
@media (max-width: 991px) {
    .vs-badge {
        position: relative;
        margin: 20px auto;
        transform: none;
        left: auto;
        top: auto;
    }
}
