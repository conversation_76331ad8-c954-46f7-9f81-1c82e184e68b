/* Case Studies Styling */
.case-study-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s ease;
    background-color: #fff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    height: 100%;
}

.case-study-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.case-study-image {
    position: relative;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
}

.case-study-image img {
    width: 100%;
    height: 240px;
    object-fit: cover;
    transition: transform 0.7s cubic-bezier(0.61, 1, 0.88, 1);
}

.case-study-item:hover .case-study-image img {
    transform: scale(1.05);
}

.case-study-category {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    color: white;
    padding: 6px 12px;
    border-radius: 30px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    z-index: 2;
}

.case-study-content {
    padding: 25px;
    position: relative;
}

.case-study-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #2b2d3a;
    line-height: 1.4;
}

.case-study-description {
    font-size: 15px;
    line-height: 1.6;
    color: #6c6d80;
    margin-bottom: 20px;
}

.case-study-metrics {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.metric {
    text-align: center;
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.metric-label {
    font-size: 12px;
    color: #6c6d80;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.case-study-link {
    display: inline-flex;
    align-items: center;
    color: #e958a1;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
}

.case-study-link i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.case-study-link:hover {
    color: #ff5d74;
}

.case-study-link:hover i {
    transform: translateX(5px);
}

.case-study-logo {
    position: absolute;
    bottom: 25px;
    right: 25px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.case-study-logo img {
    width: 24px;
    height: auto;
}

.down-section{bottom:-50px !important;}

/* Responsive adjustments */
@media (max-width: 991px) {
    .case-study-image img {
        height: 200px;
    }
    
    .case-study-content {
        padding: 20px;
    }
    
    .case-study-title {
        font-size: 18px;
    }
}

@media (max-width: 767px) {
    .case-study-metrics {
        flex-wrap: wrap;
    }
    
    .metric {
        width: 50%;
        margin-bottom: 15px;
    }
}
