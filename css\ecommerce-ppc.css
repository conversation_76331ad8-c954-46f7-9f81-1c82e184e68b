/* E-commerce PPC Page Styles - Apple-inspired Design Methodology
   Clean, practical, sensible visuals with consistent brand theming */

/* Main Styles */
.ecommerce-ppc-page {
    overflow-x: hidden;
}

/* Hero Section */
.ecommerce-ppc-hero {
    background: linear-gradient(135deg, #F2F0EE 0%, #F5F5F7 100%);
    position: relative;
    overflow: hidden;
    padding: 120px 0 100px;
}

.ecommerce-ppc-hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.08) 0%, rgba(31, 33, 42, 0) 60%);
    z-index: 1;
}

.ecommerce-ppc-hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40%;
    height: 40%;
    background: radial-gradient(circle at bottom left, rgba(233, 88, 161, 0.08) 0%, rgba(31, 33, 42, 0) 60%);
    z-index: 1;
}

/* Section Tag Styling */
.section-tag {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    color: #e958a1;
    margin-bottom: 15px;
}

.section-tag i {
    margin-right: 6px;
    font-size: 14px;
}

/* Section Styles */
.ecommerce-ppc-section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.ecommerce-ppc-section.bg-light {
    background-color: #f8f9fc;
}

.ecommerce-ppc-section.bg-white {
    background-color: #ffffff;
}

.ecommerce-ppc-section.bg-gradient {
    background: linear-gradient(135deg, #f8f9fc 0%, #f0f1f7 100%);
}

/* Section Titles */
.section-title-wrapper {
    margin-bottom: 60px;
}

.section-title {
    font-size: 36px;
    line-height: 1.3;
    margin-bottom: 20px;
    font-weight: 700;
}

.section-description {
    font-size: 18px;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 800px;
    margin: 0 auto;
}

/* Challenge Blocks */
.challenge-block {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 30px;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;
}

.challenge-block:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.challenge-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    transition: all 0.3s ease;
}

.challenge-block:hover .challenge-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.15) 0%, rgba(143, 118, 245, 0.15) 100%);
}

.challenge-icon i {
    font-size: 24px;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.challenge-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
}

.challenge-text {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(27, 11, 36, 0.8);
}

/* Solution Steps */
.solution-step {
    display: flex;
    margin-bottom: 40px;
    position: relative;
}

.solution-step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 18px;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
}

.step-description {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(27, 11, 36, 0.8);
}

/* Comparison Section */
.comparison-container {
    display: flex;
    background-color: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    margin: 40px 0;
}

.comparison-column {
    flex: 1;
    padding: 40px;
    position: relative;
}

.comparison-column.standard {
    background-color: #f8f9fc;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
}

.comparison-column.enhanced {
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.03) 0%, rgba(143, 118, 245, 0.03) 100%);
}

.comparison-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.comparison-visual {
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.comparison-description {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(27, 11, 36, 0.8);
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
}

/* Metrics Cards */
.metrics-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
}

.metric-card {
    flex: 1;
    min-width: 220px;
    background-color: #ffffff;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #e958a1 0%, #8f76f5 100%);
    opacity: 1;
}

.metric-card.positive::before {
    background: linear-gradient(90deg, #e958a1 0%, #ff8a9e 100%);
}

.metric-card.reduction::before {
    background: linear-gradient(90deg, #8f76f5 0%, #6a8fff 100%);
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.metric-value {
    font-size: 42px;
    font-weight: 700;
    line-height: 1;
    margin: 10px 0;
}

.metric-card.positive .metric-value {
    background: linear-gradient(90deg, #e958a1 0%, #ff8a9e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.metric-card.reduction .metric-value {
    background: linear-gradient(90deg, #8f76f5 0%, #6a8fff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.metric-label {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.metric-description {
    font-size: 14px;
    line-height: 1.6;
    color: rgba(27, 11, 36, 0.7);
    margin-top: 10px;
}

/* Case Study Cards */
.case-studies-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.case-study-card {
    flex: 1;
    min-width: 300px;
    background-color: #ffffff;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    position: relative;
}

.case-study-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.case-study-logo {
    height: 40px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.case-study-logo img {
    max-height: 100%;
    max-width: 120px;
}

.case-study-text {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(27, 11, 36, 0.8);
    margin-bottom: 20px;
}

.case-study-link {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: #e958a1;
    text-decoration: none;
    transition: all 0.2s ease;
}

.case-study-link:hover {
    color: #d15ec7;
}

.case-study-link i {
    margin-left: 5px;
    transition: transform 0.2s ease;
}

.case-study-link:hover i {
    transform: translate(3px, -3px);
}

/* Expertise Section */
.expertise-container {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.03);
    margin: 40px 0;
}

.expertise-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.expertise-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.expertise-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.expertise-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.expertise-icon i {
    font-size: 18px;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.expertise-content {
    flex: 1;
}

.expertise-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.expertise-description {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(27, 11, 36, 0.8);
}

/* Partner Badge */
.partner-badge {
    display: flex;
    align-items: center;
    margin-top: 30px;
}

.partner-badge img {
    height: 60px;
    margin-right: 20px;
}

.partner-badge-text {
    font-size: 14px;
    color: rgba(27, 11, 36, 0.7);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #F2F0EE 0%, #F5F5F7 100%);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(143, 118, 245, 0.08) 0%, rgba(31, 33, 42, 0) 60%);
    z-index: 0;
}

.cta-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40%;
    height: 40%;
    background: radial-gradient(circle at bottom left, rgba(233, 88, 161, 0.08) 0%, rgba(31, 33, 42, 0) 60%);
    z-index: 0;
}

.cta-container {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 60px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 1;
}

.cta-title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
}

.cta-description {
    font-size: 18px;
    line-height: 1.6;
    color: rgba(27, 11, 36, 0.8);
    text-align: center;
    max-width: 700px;
    margin: 0 auto 40px;
}

.cta-form {
    max-width: 500px;
    margin: 0 auto;
}

.cta-form .form-group {
    margin-bottom: 20px;
}

.cta-form input,
.cta-form textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.cta-form input:focus,
.cta-form textarea:focus {
    outline: none;
    border-color: #e958a1;
    box-shadow: 0 0 0 3px rgba(233, 88, 161, 0.1);
}

.cta-form button {
    width: 100%;
    padding: 15px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #e958a1 0%, #8f76f5 100%);
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cta-form button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(233, 88, 161, 0.2);
}

/* Google Ads Visual Elements */
.google-ads-element {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.google-ads-element:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.google-ads-element img {
    width: 100%;
    height: auto;
    display: block;
}

/* Hero Animation */
.ecommerce-ppc-animation-container {
    position: relative;
    width: 100%;
    height: auto;
    margin: 30px auto 50px;
}

/* Scale Tag Styling */
.scale-tag-container {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.scale-tag {
    display: inline-block;
    padding: 6px 16px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    color: white;
    border-radius: 30px;
    box-shadow: 0 4px 10px rgba(233, 88, 161, 0.2);
}

/* Responsive Styles */
@media (max-width: 1199px) {
    .section-title {
        font-size: 32px;
    }

    .metric-value {
        font-size: 36px;
    }

    .cta-container {
        padding: 40px;
    }
}

@media (max-width: 991px) {
    .ecommerce-ppc-hero {
        padding: 100px 0 80px;
    }

    .ecommerce-ppc-section {
        padding: 80px 0;
    }

    .section-title {
        font-size: 28px;
    }

    .section-description {
        font-size: 16px;
    }

    .challenge-block {
        margin-bottom: 30px;
    }

    .comparison-container {
        flex-direction: column;
    }

    .comparison-column {
        padding: 30px;
    }

    .comparison-column.standard {
        border-right: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .metric-card {
        min-width: calc(50% - 20px);
        margin-bottom: 20px;
    }

    .case-study-card {
        min-width: calc(50% - 20px);
        margin-bottom: 20px;
    }

    .cta-title {
        font-size: 28px;
    }

    .cta-description {
        font-size: 16px;
    }

    .hero-animation-container {
        height: 400px;
    }
}

@media (max-width: 767px) {
    .ecommerce-ppc-hero {
        padding: 80px 0 60px;
    }

    .ecommerce-ppc-section {
        padding: 60px 0;
    }

    .section-title {
        font-size: 24px;
    }

    .section-description {
        font-size: 15px;
    }

    .challenge-block {
        padding: 20px;
    }

    .challenge-icon {
        width: 50px;
        height: 50px;
    }

    .challenge-icon i {
        font-size: 20px;
    }

    .challenge-title {
        font-size: 18px;
    }

    .challenge-text {
        font-size: 14px;
    }

    .solution-step {
        flex-direction: column;
    }

    .step-number {
        margin-bottom: 15px;
        margin-right: 0;
    }

    .comparison-column {
        padding: 20px;
    }

    .comparison-visual {
        height: 200px;
    }

    .comparison-description {
        padding: 15px;
        font-size: 14px;
    }

    .metric-card {
        min-width: 100%;
        padding: 20px;
    }

    .metric-value {
        font-size: 32px;
    }

    .metric-label {
        font-size: 16px;
    }

    .case-study-card {
        min-width: 100%;
        padding: 20px;
    }

    .expertise-container {
        padding: 20px;
    }

    .cta-container {
        padding: 30px 20px;
    }

    .cta-title {
        font-size: 24px;
    }

    .cta-description {
        font-size: 15px;
    }

    .hero-animation-container {
        height: 300px;
    }
}

/* Button Hover Effect */
.btn-gradient-pink-orange:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(233, 88, 161, 0.2);
}

/* Modern Heading Gradient */
.modern-heading-gradient {
    background: linear-gradient(166deg, #ffffff 0%, #f5f5f5 40%, #ff8cc6 60%, #df367d 75%, #ee5c46 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline;
    position: relative;
}

/* Hero Animation Styles */
.hero-animation-container {
    position: relative;
    width: 100%;
    height: 400px;
    margin: 0 auto;
}

.animation-element {
    position: absolute;
    transition: all 0.5s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    overflow: hidden;
}

.animation-element img {
    border-radius: 16px;
    transition: all 0.5s ease;
}

.adzeta-ai-center {
    width: 180px;
    height: 180px;
    top: 20%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
    animation: pulse 3s infinite ease-in-out;
}

.adzeta-ai-left {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 10%;
    transform: translateY(-50%);
    z-index: 3;
}

.adzeta-ai-right {
    width: 150px;
    height: 150px;
    top: 50%;
    right: 10%;
    transform: translateY(-50%);
    z-index: 3;
}

.adzeta-ai-bottom {
    width: 150px;
    height: 150px;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

.element-label {
    position: absolute;
    font-size: 10px;
    font-weight: 600;
    letter-spacing: 1px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

.label-center {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.label-left {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.label-right {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.label-bottom {
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.connection-line {
    position: absolute;
    background: linear-gradient(90deg, rgba(233, 88, 161, 0.5), rgba(143, 118, 245, 0.5));
    height: 2px;
    z-index: 2;
}

.connection-left {
    width: 30%;
    top: 50%;
    left: 20%;
    transform: translateY(-50%);
}

.connection-right {
    width: 30%;
    top: 50%;
    right: 20%;
    transform: translateY(-50%);
}

.connection-bottom {
    width: 2px;
    height: 30%;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(to bottom, rgba(233, 88, 161, 0.5), rgba(143, 118, 245, 0.5));
}

.data-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: linear-gradient(90deg, #e958a1, #8f76f5);
    z-index: 4;
}

.particle-left-1 {
    top: 50%;
    left: 25%;
    transform: translateY(-50%);
    animation: moveLeftToRight 3s infinite;
}

.particle-left-2 {
    top: 50%;
    left: 30%;
    transform: translateY(-50%);
    animation: moveLeftToRight 3s infinite 1s;
}

.particle-right-1 {
    top: 50%;
    right: 25%;
    transform: translateY(-50%);
    animation: moveRightToLeft 3s infinite;
}

.particle-right-2 {
    top: 50%;
    right: 30%;
    transform: translateY(-50%);
    animation: moveRightToLeft 3s infinite 1s;
}

.particle-bottom-1 {
    bottom: 25%;
    left: 50%;
    transform: translateX(-50%);
    animation: moveBottomToTop 3s infinite;
}

.particle-bottom-2 {
    bottom: 30%;
    left: 50%;
    transform: translateX(-50%);
    animation: moveBottomToTop 3s infinite 1s;
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        box-shadow: 0 15px 40px rgba(233, 88, 161, 0.3);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
}

@keyframes moveLeftToRight {
    0% {
        left: 25%;
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        left: 45%;
        opacity: 0;
    }
}

@keyframes moveRightToLeft {
    0% {
        right: 25%;
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        right: 45%;
        opacity: 0;
    }
}

@keyframes moveBottomToTop {
    0% {
        bottom: 25%;
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    80% {
        opacity: 1;
    }
    100% {
        bottom: 45%;
        opacity: 0;
    }
}

/* Platform CTA Section */
.platform-cta {
    background: linear-gradient(135deg, #1f1f2c 0%, #0A0A14 100%);
    position: relative;
    overflow: hidden;
}

.platform-cta .mesh-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/mesh-gradient-overlay.png');
    background-size: cover;
    background-position: center;
    opacity: 0.15;
    z-index: 0;
}

.platform-cta .cta-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.platform-cta .cta-description {
    font-size: 18px;
    line-height: 1.6;
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

/* Comparison Table Styling */
.comparison-table-style-02 {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.comparison-column {
    padding: 0;
}

.comparison-header {
    padding: 20px;
    text-align: center;
}

.comparison-header h3 {
    font-size: 20px;
}

.comparison-content {
    padding: 30px;
    background-color: white;
}

.comparison-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.comparison-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.comparison-item i {
    font-size: 24px;
    margin-right: 15px;
    flex-shrink: 0;
}

.highlight-column .comparison-header {
    color: white;
}

/* Apple-inspired Metric Cards */
.apple-metric-card {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 25px;
    height: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.apple-metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.apple-metric-card .metric-icon {
    font-size: 24px;
    margin-bottom: 15px;
}

.apple-metric-card.positive .metric-icon {
    color: #f45888;
}

.apple-metric-card.reduction .metric-icon {
    color: #8f76f5;
}

.apple-metric-card .metric-label {
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 10px;
    color: #666;
}

.apple-metric-card .metric-value {
    font-size: 42px;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 15px;
}

.apple-metric-card.positive .metric-value {
    color: #f45888;
}

.apple-metric-card.reduction .metric-value {
    color: #8f76f5;
}

.apple-metric-card .metric-description {
    font-size: 14px;
    line-height: 1.6;
    color: #666;
}
