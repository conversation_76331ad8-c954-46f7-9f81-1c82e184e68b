<?php include 'header.php'; ?>
<link rel="stylesheet" href="css/google-ads-ppc.css?v=1.0" />
<link rel="stylesheet" href="css/ppc-hero-animation.css?v=1.0" />
<style>
/* Programmatic Ads Page - Cutting-Edge Professional Styles */
/* Cohesive with google-ads-ppc.php design patterns */

/* Modern gradient definitions */


.dv360-bg::before {
    content: '';
    position: absolute;
    top: -20%;
    left: 35%;
    width: 30%;
    height: 100%;
    background-image: url(./images/dv360-bg.svg);
    background-repeat: no-repeat;
    background-position: top center;
    background-size: contain;
    z-index: 1;
    pointer-events: none;
}

.amazon-dsp-bg::before {
    content: '';
    position: absolute;
    top: -20%;
    left: 35%;
    width: 30%;
    height: 100%;
    background-image: url(./images/amazon-dsp-bg.svg);
    background-repeat: no-repeat;
    background-position: top center;
    background-size: contain;
    z-index: 1;
    pointer-events: none;
}

@media (max-width: 991px) {
    .dv360-bg::before,.amazon-dsp-bg::before {
        top: -15%;
        left: 25%;
        width: 45%;
    }
}

@media (max-width: 767px) {
    .dv360-bg::before,.amazon-dsp-bg::before {
        top: -10%;
        left: 20%;
        width: 60%;
    }
}


@media (max-width: 575px) {
	 .dv360-bg::before,.amazon-dsp-bg::before {
        top: -8%;
        left: 20%;
        width: 60%;
    }
	
	
}

.text-gradient-purple-blue {
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Scale tag styling - consistent with google-ads-ppc.php */
.scale-tag-container {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.scale-tag {
    display: inline-block;
    padding: 6px 16px;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    background: linear-gradient(to right, #e958a1, #ff5d74);
    color: white;
    border-radius: 30px;
    box-shadow: 0 4px 10px rgba(233, 88, 161, 0.2);
}

/* Modern heading gradient - consistent with google-ads-ppc.php */


/* Section backgrounds and layouts */
.programmatic-foundation-section {
    padding: 100px 0 30px;
    overflow: hidden;
    position: relative;
}

.google-value-challenges {
    padding: 100px 0 30px;
    overflow: hidden;
    position: relative;
}

.proven-results-section {
    padding: 100px 0 30px;
    overflow: hidden;
    position: relative;
}

/* Enhanced card styles - Apple-inspired */
.vbb-content-container,
.unified-value-card {
    position: relative;
    z-index: 2;
    background: #fffdfc;
    border-radius: 24px;
    padding: 50px;
    margin-top: 30px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;

}

.vbb-content-container::before {
    content: '';
    position: absolute;
    top: -15%;
    left: 35%;
    width: 30%;
    height: 100%;
    background-image: url('../images/programmatic-bg.svg');
    background-repeat: no-repeat;
    background-position: top center;
    background-size: contain;
    z-index: 1;
    pointer-events: none;
}

.vbb-content-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(255, 253, 252, 0.3) 0%, #fffdfc 70%);
    z-index: 0;
    pointer-events: none;
}

.vbb-content-container:hover,
.unified-value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.06);
}

/* Content styling */
.vbb-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    padding-right: 30px;
    position: relative;
    z-index: 2;
}

.vbb-content p {
    font-size: 15px;
    line-height: 1.7;
    color: #555;
    margin-bottom: 24px;
    font-weight: 400;
    letter-spacing: -0.2px;
}

/* Challenge cards - consistent with google-ads-ppc.css */
.challenge-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.08);
    border: 1px solid rgba(143, 118, 245, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.challenge-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.challenge-icon {
    margin-bottom: 20px;
}

.challenge-icon i {
    font-size: 32px;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.challenge-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.challenge-description {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 20px;
}

.challenge-highlight {
    background: rgba(233, 88, 161, 0.05);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.highlight-icon {
    flex-shrink: 0;
    color: #e958a1;
    font-size: 20px;
    margin-top: 2px;
}

.challenge-highlight p {
    margin: 0;
    font-size: 15px;
    color: #666;
    font-style: italic;
}

/* Modern Challenge-Solution Grid */
.programmatic-challenges-grid {
    display: flex;
    flex-direction: column;
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
}

.challenge-solution-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    overflow: hidden;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.challenge-solution-item:hover {
    transform: translateY(-2px);
    border-color: rgba(244, 88, 136, 0.1);
}

.challenge-side {
    padding: 40px 32px;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(242, 244, 247, 0.8) 100%);
    position: relative;
    text-align: left;
}

.solution-side {
    padding: 24px;
 background: linear-gradient(135deg, rgba(233, 88, 161, 0.015) 0%, rgba(255, 93, 116, 0.015) 100%);

    position: relative;
    text-align: left;
	
}

.challenge-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.challenge-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}

.challenge-platform {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 4px 8px;
    background: rgba(107, 114, 128, 0.1);
    border-radius: 8px;
}

.challenge-solution-item .challenge-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 16px;
    line-height: 1.3;
    letter-spacing: -0.01em;
}

.challenge-solution-item .challenge-description {
    font-size: 15px;
    color: #797a8c;
    line-height: 1.6;
    margin-bottom: 24px;
}

.challenge-impact {
    margin-top: auto;
}

.impact-metric {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.impact-number {
    font-size: 28px;
    font-weight: 700;
    color: #dc2626;
    line-height: 1;
    margin-bottom: 4px;
}

.impact-label {
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.solution-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.solution-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    color: #f45888;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    border: 1px solid rgba(244, 88, 136, 0.15);
}

.solution-label {
    font-size: 12px;
    font-weight: 600;
    color: #f45888;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.solution-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 16px;
    line-height: 1.3;
    letter-spacing: -0.01em;
}

.solution-description {
    font-size: 15px;
    color: #797a8c;
    line-height: 1.6;
    margin-bottom: 24px;
}

.solution-result {
    margin-top: auto;
}

.result-improvement {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
    line-height: 1;
    margin-bottom: 4px;
}

/* Core Problem Statement - Visual Before/After */
.core-problem-statement {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 40px;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
}

.problem-visual {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 32px;
    align-items: center;
    margin-bottom: 32px;
}

.problem-side {
    text-align: center;
    padding: 32px 24px;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(220, 38, 38, 0.06) 100%);
    border: 1px solid rgba(239, 68, 68, 0.15);
    border-radius: 16px;
    position: relative;
}

.problem-side::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ef4444, #dc2626);
    border-radius: 16px 16px 0 0;
}

/* Solution side styling for the core problem statement */
.core-problem-statement .solution-side {
    text-align: center;
    padding: 32px 24px;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.08) 0%, rgba(22, 163, 74, 0.06) 100%);
    border: 1px solid rgba(34, 197, 94, 0.15);
    border-radius: 16px;
    position: relative;
}

.core-problem-statement .solution-side::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #22c55e, #16a34a);
    border-radius: 16px 16px 0 0;
}

.problem-header,
.solution-header-alt {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.problem-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 38, 38, 0.15) 100%);
    color: #dc2626;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.1);
}

.solution-icon-alt {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(22, 163, 74, 0.15) 100%);
    color: #16a34a;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
}

.problem-label,
.solution-label-alt {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.problem-label {
    color: #dc2626;
}

.solution-label-alt {
    color: #16a34a;
}

.problem-stat,
.solution-stat {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1.2;
}

.problem-stat {
    color: #dc2626;
}

.solution-stat {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.problem-desc,
.solution-desc {
    font-size: 14px;
    color: #797a8c;
    line-height: 1.4;
    margin: 0;
}

.transformation-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;
}

.arrow-line {
    width: 2px;
    height: 40px;
    background: linear-gradient(to bottom, #dc2626, #22c55e);
    border-radius: 1px;
    position: relative;
}

.arrow-line::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #22c55e;
}

.arrow-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    box-shadow: 0 4px 12px rgba(233, 88, 161, 0.3);
}

.arrow-text {
    font-size: 11px;
    font-weight: 600;
    color: #e958a1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.key-insight {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.insight-highlight {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.08) 0%, rgba(143, 118, 245, 0.08) 100%);
    padding: 16px 24px;
    border-radius: 50px;
    border: 1px solid rgba(244, 88, 136, 0.15);
    font-size: 15px;
    font-weight: 600;
    color: #797a8c;
}

.insight-highlight i {
    color: #f45888;
    font-size: 18px;
}

/* Apple-Inspired Use Case Showcase */
.use-case-showcase {
    display: flex;
    flex-direction: column;
    gap: 48px;
    max-width: 1400px;
    margin: 0 auto;
}

.use-case-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 48px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    padding: 48px;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    align-items: center;
}

.use-case-container:hover {
    transform: translateY(-4px);
    border-color: rgba(244, 88, 136, 0.1);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
}

.use-case-main {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.use-case-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.use-case-category {
    display: flex;
    align-items: center;
    gap: 12px;
}

.category-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.1) 0%, rgba(255, 93, 116, 0.1) 100%);
    color: #e958a1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    border: 1px solid rgba(233, 88, 161, 0.15);
}

.category-label {
    font-size: 14px;
    font-weight: 600;
    color: #e958a1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.use-case-platform {
    font-size: 12px;
    font-weight: 600;
    color: #797a8c;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 6px 12px;
    background: rgba(121, 122, 140, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(121, 122, 140, 0.15);
}

.use-case-title {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 16px;
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.use-case-description {
    font-size: 16px;
    color: #797a8c;
    line-height: 1.6;
    margin-bottom: 24px;
}

.use-case-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 32px;
}

.metric-item {
    text-align: center;
    padding: 20px 16px;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.03) 0%, rgba(255, 93, 116, 0.03) 100%);
    border-radius: 16px;
    border: 1px solid rgba(233, 88, 161, 0.08);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 4px;
    line-height: 1;
}

.metric-label {
    font-size: 12px;
    font-weight: 600;
    color: #797a8c;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.use-case-implementation {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(242, 244, 247, 0.8) 100%);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(0, 0, 0, 0.04);
}

.implementation-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 600;
    color: #797a8c;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.implementation-header i {
    color: #e958a1;
    font-size: 16px;
}

.implementation-steps {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.step-item {
    display: flex !important;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: #797a8c;
    position: relative;
    width: 100%;
}

.step-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    color: white !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
    position: relative;
    z-index: 10;
    min-width: 24px;
    min-height: 24px;
}

.step-text {
    font-weight: 500;
}

.implementation-context {
    padding: 12px 16px;
    background: rgba(233, 88, 161, 0.02);
    border-radius: 8px;
    border-left: 3px solid rgba(233, 88, 161, 0.2);
    margin-top: 16px;
}

.implementation-context small {
    font-size: 12px;
    line-height: 1.5;
}

.use-case-visual {
    display: flex;
    align-items: center;
    justify-content: center;
}

.visual-container {
    width: 100%;
    max-width: 320px;
}

/* Mobile chart visibility fixes */
@media (max-width: 768px) {
    .visual-container {
        max-width: 100% !important;
        width: 100% !important;
    }

    .use-case-visual {
        width: 100%;
        margin-top: 20px;
    }

    .case-study-chart {
        width: 100% !important;
        max-width: 100% !important;
        padding: 8px !important;
        opacity: 1 !important;
        transform: none !important;
        visibility: visible !important;
    }

    .chart-animate-in {
        opacity: 1 !important;
        transform: none !important;
        visibility: visible !important;
    }

    .chart-container {
        width: 100%;
        overflow: visible;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .performance-chart {
        width: 100% !important;
        height: auto !important;
        max-width: 100% !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .svg-chart-container {
        width: 100% !important;
        height: 200px !important;
        padding: 4px !important;
        overflow: visible;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Ensure charts are visible on mobile */
    .ltv-chart-svg,
    .performance-chart {
        width: 100% !important;
        height: 100% !important;
        max-width: none !important;
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Mobile chart text sizing */
    .y-labels text,
    .x-labels text {
        font-size: 9px !important;
    }

    .value-labels text {
        font-size: 10px !important;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .case-study-chart {
        padding: 6px !important;
        margin-bottom: 15px !important;
    }

    .svg-chart-container {
        height: 180px !important;
        padding: 2px !important;
    }

    .chart-header h6 {
        font-size: 13px !important;
    }

    .chart-header small {
        font-size: 10px !important;
    }
}

.platform-integration {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 24px 20px;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.02) 0%, rgba(255, 93, 116, 0.02) 100%);
    border-radius: 20px;
    border: 1px solid rgba(233, 88, 161, 0.06);
    text-align: center;
}

.platform-logo,
.platform-logos {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
}

.platform-image {
    width: 64px;
    height: 64px;
    object-fit: contain;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
}

.dual-platform .platform-image {
    width: 48px;
    height: 48px;
}

.integration-flow {
    display: flex;
    align-items: center;
    gap: 12px;
}

.flow-arrow {
    color: #e958a1;
    font-size: 16px;
    opacity: 0.6;
}

.adzeta-ai-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    color: white;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(233, 88, 161, 0.3);
    white-space: nowrap;
    width: 120px;
    margin: 0 auto;
}

.adzeta-ai-badge img {
    width: auto;
    filter: brightness(1);
    flex-shrink: 0;
}

.outcome-indicator {
    text-align: center;
    padding: 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(233, 88, 161, 0.1);
}

.outcome-value {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 4px;
    line-height: 1;
}

.outcome-label {
    font-size: 11px;
    font-weight: 600;
    color: #797a8c;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Data decisions items - from google-ads-ppc.css */
.data-decisions-items-wrapper {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
}

.data-decisions-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0,0,0,0.05), 0 0 15px rgba(143, 118, 245, 0.1);
    overflow: hidden;
    width: 100%;
    display: block;
    text-decoration: none;
    color: inherit;
    border: 1px solid rgba(143, 118, 245, 0.1);
}

.data-decisions-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1), 0 0 20px rgba(143, 118, 245, 0.2);
    text-decoration: none;
    color: inherit;
}

.data-decisions-item.is-dark {
    background: linear-gradient(135deg, #1E1A33 0%, #2D1E4A 100%);
    border: 1px solid rgba(143, 118, 245, 0.2);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15), 0 0 20px rgba(143, 118, 245, 0.15);
}

.data-decisions-item.is-dark:hover {
    box-shadow: 0 10px 30px rgba(0,0,0,0.2), 0 0 25px rgba(143, 118, 245, 0.25);
}

.data-decisions-item-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.data-decisions-item-icon {
    flex-shrink: 0;
}

.data-decisions-item-h3 {
    font-size: 22px;
    font-weight: 600;
    color: #2e2e3c;
    margin: 0;
}

.data-decisions-item-h3.is-white {
    color: white;
}

.data-decisions-item-p {
    font-size: 16px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 20px;
}

.data-decisions-item-p.is-white {
    color: rgba(255, 255, 255, 0.8);
}

.data-decisions-item-arrow {
    position: absolute;
    top: 30px;
    right: 30px;
    color: #8f76f5;
    transition: transform 0.3s ease;
}

.data-decisions-item:hover .data-decisions-item-arrow {
    transform: translate(5px, -5px);
}

/* Animated card with gradient border */
.animated-card {
    --border-width: 2px;
    --radius: 16px;
    --angle: 0turn;
    --bg-color: transparent;
    position: relative;
    border-radius: var(--radius);
    border: var(--border-width) solid transparent;
    background-color: var(--bg-color);
    isolation: isolate;
    padding: 0;
    width: 100%;
}

.animated-card::before {
    content: " ";
    position: absolute;
    inset: calc(var(--border-width) * -1);
    z-index: -1;
    border: inherit;
    border-radius: inherit;
    background-image: conic-gradient(from var(--angle), #2D1E4A 80%, #f45888 88%, #8f76f5 92%, #2D1E4A 100%);
    background-origin: border-box;
    -webkit-mask: linear-gradient(black, black) content-box, linear-gradient(black, black);
    mask: linear-gradient(black, black) content-box, linear-gradient(black, black);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    animation: rotate-gradient 4s linear infinite;
}

@property --angle {
    syntax: " <angle> ";
    inherits: true;
    initial-value: 0turn;
}

@keyframes rotate-gradient {
    to {
        --angle: 1turn;
    }
}

/* Background elements positioning */
.data-decisions-img-bg {
    position: absolute;
    opacity: 0.8;
    pointer-events: none;
}

.data-decisions-img-bg.is-google {
    top: 20%;
    right: 10%;
    z-index: 1;
}

.data-decisions-img-bg.is-adzeta {
    bottom: 10%;
    left: 5%;
    z-index: 1;
}

/* Text utilities */
.text-white-transparent {
    color: rgba(255, 255, 255, 0.7);
}

/* Enhanced Industry-Focused Elements */
.industry-stat-callout {
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.08) 0%, rgba(143, 118, 245, 0.08) 100%);
    border-radius: 16px;
    padding: 24px;
    border-left: 4px solid #f45888;
    position: relative;
    overflow: hidden;
}

.industry-stat-callout::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    border-radius: 50%;
    transform: translate(20px, -20px);
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-text {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    margin-bottom: 6px;
}

.stat-source {
    font-size: 13px;
    color: #666;
    font-style: italic;
    font-weight: 500;
}

.industry-insight-box {
    background: rgba(143, 118, 245, 0.05);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(143, 118, 245, 0.1);
    position: relative;
}

.insight-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

/* Programmatic Evolution Chart */
.programmatic-evolution-chart {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
}

.chart-header h5 {
    color: #333;
    font-size: 20px;
}

.evolution-timeline {
    position: relative;
    padding: 20px 0;
}

.evolution-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e0e0e0 0%, #f45888 50%, #8f76f5 100%);
}

.evolution-era {
    position: relative;
    padding-left: 70px;
    margin-bottom: 30px;
}

.evolution-era:last-child {
    margin-bottom: 0;
}

.era-marker {
    position: absolute;
    left: 22px;
    top: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.era-past .era-marker {
    background: #e0e0e0;
}

.era-current .era-marker {
    background: #4285F4;
}

.era-future .era-marker {
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1), 0 0 0 0 rgba(244, 88, 136, 0.4);
    }
    50% {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1), 0 0 0 8px rgba(244, 88, 136, 0);
    }
}

.era-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.era-description {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
}

.era-efficiency {
    font-size: 13px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

.era-past .era-efficiency {
    background: rgba(224, 224, 224, 0.2);
    color: #999;
}

.era-current .era-efficiency {
    background: rgba(66, 133, 244, 0.1);
    color: #4285F4;
}

.era-future .era-efficiency {
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    color: #f45888;
}

/* Enhanced Metrics Comparison */
.metrics-comparison {
    background: rgba(248, 249, 250, 0.5);
    border-radius: 20px;
    padding: 30px 20px;
    border: 1px solid rgba(0,0,0,0.05);
    overflow: hidden;
}

.metric-comparison-row {
    margin-bottom: 40px;
}

.metric-comparison-row:last-child {
    margin-bottom: 0;
}

.metric-comparison-header {
    text-align: center;
    margin-bottom: 24px;
}

.comparison-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.comparison-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.metric-comparison-cards {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.metric-card {
    background: white;
    border-radius: 16px;
    padding: 24px 20px;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 180px;
    flex: 1;
    max-width: 220px;
}

.metric-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0,0,0,0.15);
}

.metric-card.highlight {
    border: 2px solid rgba(244, 88, 136, 0.2);
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.02) 0%, rgba(143, 118, 245, 0.02) 100%);
}

.metric-card.highlight:hover {
    border-color: rgba(244, 88, 136, 0.4);
    box-shadow: 0 12px 30px rgba(244, 88, 136, 0.2);
}

.metric-label {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-bottom: 12px;
    line-height: 1.2;
}

.traditional .metric-label {
    color: #999;
}

.predictive .metric-label {
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.metric-value {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1;
}

.traditional .metric-value {
    color: #666;
}

.predictive .metric-value {
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.metric-desc {
    font-size: 13px;
    color: #888;
    font-weight: 500;
    margin-bottom: 12px;
}

.metric-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 11px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    margin-top: 8px;
}

.metric-trend.positive {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.metric-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.metric-trend.neutral {
    background: rgba(156, 163, 175, 0.1);
    color: #9ca3af;
}

.vs-connector {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    min-width: 120px;
    height: 100px;
}

.vs-line {
    width: 2px;
    height: 40px;
    background: linear-gradient(to bottom, #e5e7eb, #f45888, #e5e7eb);
    margin-bottom: 8px;
}

.vs-badge {
    background: white;
    border: 2px solid #f45888;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    color: #f45888;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px rgba(244, 88, 136, 0.2);
}

.improvement-indicator {
    text-align: center;
}

.improvement-text {
    font-size: 11px;
    font-weight: 600;
    color: #f45888;
    margin-bottom: 4px;
}

.improvement-arrow {
    color: #f45888;
    font-size: 16px;
    animation: arrow-pulse 2s infinite;
}

@keyframes arrow-pulse {
    0%, 100% {
        transform: translateX(0);
        opacity: 0.7;
    }
    50% {
        transform: translateX(4px);
        opacity: 1;
    }
}

.improvement-badge {
    position: absolute;
    top: -12px;
    right: -12px;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 6px 10px;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(244, 88, 136, 0.4);
    border: 2px solid white;
}

/* ROI Summary */
.roi-summary {
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.08) 0%, rgba(143, 118, 245, 0.08) 100%);
    border-radius: 16px;
    padding: 24px;
    margin-top: 30px;
    border: 1px solid rgba(244, 88, 136, 0.1);
}

.roi-content {
    display: flex;
    align-items: center;
    gap: 20px;
    text-align: left;
}

.roi-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.roi-text {
    flex: 1;
}

.roi-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.roi-description {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

.roi-multiplier {
    text-align: center;
    flex-shrink: 0;
}

.multiplier-text {
    display: block;
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1;
}

.multiplier-label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Two-Section Layout */
.evolution-section,
.performance-section {
    margin-bottom: 40px;
}

.section-header {
    text-align: left;
    margin-bottom: 24px;
}

.section-header h5 {
    color: #1d1d1f;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: -0.02em;
    margin-bottom: 8px;
}

.section-header p {
    color: #6e6e73;
    font-size: 14px;
    font-weight: 400;
    margin: 0;
}

/* Apple-Inspired Evolution Timeline */
.evolution-timeline-compact {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 32px 24px;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
}

.evolution-path {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
}

.evolution-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.step-marker {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin: 0 auto 16px;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.evolution-step.past .step-marker {
    background: #d1d5db;
}

.evolution-step.current .step-marker {
    background: #007AFF;
}

.evolution-step.future .step-marker {
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    animation: pulse-glow 3s infinite ease-in-out;
}

.step-content {
    font-size: 14px;
    line-height: 1.4;
}

.step-year {
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 4px;
    font-size: 13px;
    letter-spacing: -0.01em;
}

.step-method {
    color: #6e6e73;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 400;
}

.step-efficiency {
    font-size: 11px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
    letter-spacing: 0.01em;
}

.evolution-step.past .step-efficiency {
    background: rgba(209, 213, 219, 0.2);
    color: #9ca3af;
}

.evolution-step.current .step-efficiency {
    background: rgba(0, 122, 255, 0.1);
    color: #007AFF;
}

.evolution-step.future .step-efficiency {
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.1) 0%, rgba(143, 118, 245, 0.1) 100%);
    color: #f45888;
}

.evolution-arrow {
    color: #d1d5db;
    font-size: 12px;
    flex-shrink: 0;
    opacity: 0.6;
}

/* Apple-Inspired Metrics Grid */
.key-metrics-grid {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 32px 24px;
    border: 0.5px solid rgba(0, 0, 0, 0.06);
}

/* Remove the old metrics header since we now use section-header */

.metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 24px;
}

.metric-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: transparent;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.metric-item:hover {
    transform: translateY(-1px);
    background: rgba(255, 255, 255, 0.3);
}

.metric-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.08) 0%, rgba(143, 118, 245, 0.08) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #f45888;
    font-size: 20px;
    flex-shrink: 0;
}

.metric-content {
    flex: 1;
    min-width: 0;
}

.metric-comparison {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    flex-wrap: wrap;
}

.old-value {
    font-size: 17px;
    font-weight: 600;
    color: #8e8e93;
    text-decoration: line-through;
    text-decoration-color: rgba(142, 142, 147, 0.3);
}

.new-value {
    font-size: 17px;
    font-weight: 700;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.01em;
}

.metric-comparison i {
    color: #f45888;
    font-size: 10px;
    opacity: 0.8;
}

.metric-label {
    font-size: 12px;
    color: #6e6e73;
    font-weight: 500;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.improvement-tag {
    font-size: 10px;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 10px;
    display: inline-block;
    letter-spacing: 0.02em;
}

.improvement-tag.positive {
    background: rgba(52, 199, 89, 0.1);
    color: #34c759;
}

.improvement-tag.negative {
    background: rgba(244, 88, 136, 0.1);
    color: #f45888;
}

/* Apple-Inspired ROI Impact */
.roi-impact {
    background: linear-gradient(135deg, rgba(244, 88, 136, 0.08) 0%, rgba(143, 118, 245, 0.08) 100%);
    border-radius: 18px;
    padding: 28px 24px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 0.5px solid rgba(244, 88, 136, 0.12);
}

.roi-content-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.roi-multiplier-large {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.multiplier-number {
    font-size: 42px;
    font-weight: 700;
    background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 0.9;
    letter-spacing: -0.02em;
}

.multiplier-text {
    font-size: 13px;
    font-weight: 600;
    color: #6e6e73;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin-top: 2px;
}

.roi-description {
    font-size: 14px;
    color: #6e6e73;
    font-weight: 400;
    line-height: 1.4;
    max-width: 240px;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .vbb-content-container,
    .unified-value-card {
        padding: 30px;
    }

    .challenge-card {
        margin-bottom: 30px;
    }

    .data-decisions-item {
        padding: 25px;
    }

    .programmatic-evolution-chart {
        padding: 20px;
    }

    .stat-number {
        font-size: 28px;
    }
}

/* Proven Results Redesign Styles */
.proven-results-redesign {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

/* Professional Case Study Card */
.professional-case-study-card {
    margin-bottom: 60px;
}

.case-study-professional-container {
    background: linear-gradient(145deg, #ffffff 0%, #fafbff 100%);
    border-radius: 24px;
    padding: 0;
    box-shadow:
        0 1px 2px rgba(0, 0, 0, 0.02),
        0 8px 32px rgba(0, 0, 0, 0.06),
        0 0 0 1px rgba(0, 0, 0, 0.02);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.case-study-professional-container:hover {
    transform: translateY(-4px);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.03),
        0 16px 48px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(0, 0, 0, 0.03);
}

.case-study-professional-header {
    padding: 40px 50px 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 255, 0.9) 100%);
}

.case-study-badges-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.professional-case-badge {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 700;
    letter-spacing: 0.8px;
    text-transform: uppercase;
    box-shadow: 0 2px 8px rgba(26, 26, 26, 0.15);
}

.professional-industry-badge {
    background: linear-gradient(135deg, rgba(115, 105, 241, 0.08) 0%, rgba(233, 88, 161, 0.08) 100%);
    color: #7369f1;
    padding: 8px 14px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid rgba(115, 105, 241, 0.12);
}

.professional-case-title {
    font-size: 36px;
    font-weight: 800;
    color: #1a1a1a;
    margin-bottom: 16px;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.professional-case-description {
    font-size: 18px;
    line-height: 1.6;
    color: #797a8c;
    margin-bottom: 0;
    font-weight: 400;
}

.case-study-trust-indicator {
    text-align: right;
}

.trust-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, rgba(32, 201, 151, 0.08) 0%, rgba(32, 201, 151, 0.12) 100%);
    color: #20c997;
    padding: 10px 16px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 600;
    border: 1px solid rgba(32, 201, 151, 0.15);
}

.trust-badge i {
    font-size: 16px;
}

/* iOS-Inspired Clean Campaign Overview */
.ios-campaign-overview {
    padding: 60px 50px;
    background: #ffffff;
}

/* Key Metrics Row - Clean & Minimal */
.key-metrics-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    margin-bottom: 60px;
    padding: 0;
}

.metric-item {
    text-align: center;
    position: relative;
}

.metric-item.highlight-metric {
    position: relative;
}

.metric-item.highlight-metric::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: #e958a1;
    border-radius: 50%;
}

.metric-value {
    font-size: 32px;
    font-weight: 800;
    color: #1a1a1a;
    line-height: 1;
    margin-bottom: 8px;
    letter-spacing: -0.02em;
}

.highlight-metric .metric-value {
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 36px;
}

.metric-label {
    font-size: 13px;
    color: #797a8c;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 8px;
}

.metric-badge {
    display: inline-block;
    background: #20c997;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Platform Results Section - Clean & Minimal */
.platform-results-section {
    margin-top: 20px;
}

.section-title {
    font-size: 17px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 32px;
    letter-spacing: -0.01em;
}

.platform-results-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
}

.platform-result {
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
}

.platform-header {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.platform-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    line-height: 1.2;
    margin-bottom: 4px;
    letter-spacing: -0.01em;
}

.platform-type {
    font-size: 13px;
    color: #797a8c;
    font-weight: 500;
}

.platform-performance {
    display: flex;
    gap: 24px;
}

.performance-metric {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.perf-value {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1;
    margin-bottom: 6px;
    letter-spacing: -0.02em;
}

.perf-label {
    font-size: 12px;
    color: #797a8c;
    font-weight: 500;
    line-height: 1.3;
}

/* Case Study Header Link Styles */
.case-study-professional-header:hover {
    transform: translateY(-2px);
}

.case-study-professional-header:hover .case-study-link-arrow {
    opacity: 1 !important;
    transform: translateX(2px);
}

.case-study-link-arrow {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .ios-campaign-overview {
        padding: 40px 20px;
    }

    .key-metrics-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        margin-bottom: 40px;
    }

    .metric-value {
        font-size: 24px;
    }

    .highlight-metric .metric-value {
        font-size: 28px;
    }

    .platform-results-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .platform-performance {
        gap: 16px;
    }

    .perf-value {
        font-size: 20px;
    }

    .case-study-link-arrow {
        margin-top: 8px !important;
    }
}

/* Case Study Results Section */
.case-study-results-section {
    padding: 30px 50px 40px;
    background: linear-gradient(135deg, rgba(248, 250, 255, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.result-metric-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px 16px;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.04);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.result-metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #e958a1 0%, #ff5d74 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.result-metric-card:hover::before {
    transform: scaleX(1);
}

.result-metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.result-number {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1;
    margin-bottom: 8px;
}

.result-label {
    font-size: 12px;
    color: #797a8c;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.3;
}

/* Professional Implementation Roadmap */
.professional-implementation-roadmap {
    position: relative;
    padding: 0 20px;
}

.roadmap-header {
    margin-bottom: 60px;
}

.implementation-phases-container {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.implementation-phase {
    position: relative;
    margin-bottom: 80px;
}

.implementation-phase:last-child {
    margin-bottom: 0;
}

.phase-content-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 40px;
    position: relative;
}

.phase-content-wrapper.reverse {
    flex-direction: row-reverse;
}

.phase-indicator {
    flex-shrink: 0;
    text-align: center;
    position: relative;
    z-index: 2;
}

.phase-icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    color: white;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Modern iOS 17 & Big Tech Inspired Phase Gradients */
.phase-foundation {
background: linear-gradient(145deg, #007AFF 0%, #34C7F8 100%);
    box-shadow:
        0 12px 40px rgba(0, 122, 255, 0.15),
        0 0 0 0.5px rgba(0, 122, 255, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.phase-foundation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(0, 122, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(88, 86, 214, 0.15) 0%, transparent 50%);
    border-radius: inherit;
}

.phase-learning {
    background: linear-gradient(145deg, #FF9500 0%, #FFB946 100%);
    box-shadow:
        0 12px 40px rgba(255, 59, 48, 0.15),
        0 0 0 0.5px rgba(255, 59, 48, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.phase-learning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(255, 59, 48, 0.25) 0%, transparent 60%),
        radial-gradient(circle at 70% 80%, rgba(255, 149, 0, 0.2) 0%, transparent 50%);
    border-radius: inherit;
}

.phase-scale {
   background: linear-gradient(145deg, #34C759 0%, #60E17F 100%);
    box-shadow:
        0 12px 40px rgba(48, 209, 88, 0.15),
        0 0 0 0.5px rgba(48, 209, 88, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.phase-scale::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 40% 10%, rgba(48, 209, 88, 0.25) 0%, transparent 60%),
        radial-gradient(circle at 60% 90%, rgba(0, 199, 190, 0.2) 0%, transparent 50%);
    border-radius: inherit;
}

.phase-peak {
 background: linear-gradient(145deg, #5856D6 0%, #AF52DE 100%);
    box-shadow:
        0 16px 50px rgba(191, 90, 242, 0.2),
        0 0 0 0.5px rgba(191, 90, 242, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.phase-peak::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(191, 90, 242, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 45, 146, 0.25) 0%, transparent 50%),
        radial-gradient(circle at 50% 90%, rgba(0, 122, 255, 0.2) 0%, transparent 40%);
    border-radius: inherit;
}

.phase-duration {
    font-size: 12px;
    color: #8e8e93;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.phase-details {
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.phase-title {
    font-size: 20px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 12px;
    line-height: 1.2;
}

.phase-description {
    font-size: 15px;
    color: #797a8c;
    margin-bottom: 24px;
    line-height: 1.5;
}

.phase-deliverables {
    margin-bottom: 24px;
}

.deliverable-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    font-size: 14px;
    color: #4a4a4a;
}

.deliverable-item:last-child {
    margin-bottom: 0;
}

.deliverable-item i {
    color: #20c997;
    font-size: 16px;
    flex-shrink: 0;
}

.phase-outcome {
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    padding-top: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.outcome-metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 100px;
}

.metric-value {
    font-size: 28px;
    font-weight: 800;
    background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1;
    margin-bottom: 4px;
}

.metric-label {
    font-size: 11px;
    color: #8e8e93;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.outcome-insight {
    flex: 1;
    font-size: 13px;
    color: #6b7280;
    font-style: italic;
}

/* Phase Connectors - Seamless Connection */
.phase-connector {
    position: relative;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: -40px auto;
    width: 100%;
    max-width: 1000px;
    z-index: 0;
}

.phase-connector::after {
    content: '';
    width: 2px;
    height: 80px;
    background: linear-gradient(180deg,
        rgba(0, 122, 255, 0.6) 0%,
        rgba(255, 59, 48, 0.5) 25%,
        rgba(48, 209, 88, 0.5) 75%,
        rgba(191, 90, 242, 0.6) 100%);
    position: relative;
    border-radius: 1px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.phase-connector::before {
    content: '';
    position: absolute;
    left: 50%;
    top: -50%;
    transform: translateX(-50%);
    width: 4px;
    height: 80px;
    background: linear-gradient(180deg,
        rgba(0, 122, 255, 0.4) 0%,
        rgba(255, 59, 48, 0.3) 25%,
        rgba(48, 209, 88, 0.3) 75%,
        rgba(191, 90, 242, 0.4) 100%);
    filter: blur(2px);
    z-index: 0;
    border-radius: 2px;
}

/* Success Guarantee */
.implementation-guarantee {
    margin-top: 60px;
}

.guarantee-card {
    background: linear-gradient(135deg,
        rgba(32, 201, 151, 0.05) 0%,
        rgba(23, 162, 184, 0.05) 100%);
    border: 1px solid rgba(32, 201, 151, 0.2);
    border-radius: 20px;
    padding: 32px;
    max-width: 500px;
    margin: 0 auto;
    text-align: center;
}

.guarantee-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 24px;
    color: white;
    box-shadow: 0 8px 32px rgba(32, 201, 151, 0.3);
}

.guarantee-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 12px;
}

.guarantee-text {
    font-size: 14px;
    color: #797a8c;
    margin-bottom: 0;
    line-height: 1.5;
}

/* Enhanced Animation for Phase Connectors */
@keyframes pulse-connector {
    0%, 100% {
        opacity: 0.4;
        transform: translateX(-50%) scaleY(1);
    }
    50% {
        opacity: 0.8;
        transform: translateX(-50%) scaleY(1.1);
    }
}

@keyframes pulse-connector-main {
    0%, 100% {
        opacity: 0.5;
        transform: scaleY(1);
    }
    50% {
        opacity: 0.9;
        transform: scaleY(1.05);
    }
}

.phase-connector::after {
    animation: pulse-connector-main 3s ease-in-out infinite;
    transform-origin: center;
}

.phase-connector::before {
    animation: pulse-connector 3s ease-in-out infinite 0.3s;
    transform-origin: center;
}

/* Responsive Styles for Proven Results Redesign */
@media (max-width: 991px) {
    .proven-results-redesign {
        padding: 80px 0;
    }

    .case-study-container {
        padding: 40px 30px;
    }

    .case-study-title {
        font-size: 28px;
    }

    .case-study-description {
        font-size: 16px;
    }

    .revenue-showcase {
        padding-left: 0;
        margin-top: 40px;
    }

    .revenue-amount {
        font-size: 3.5rem;
    }

    .premium-metric-card {
        padding: 28px 20px;
        margin-bottom: 24px;
    }

    .counter {
        font-size: 3rem;
    }

    .metric-suffix {
        font-size: 1.8rem;
    }

    /* Professional Case Study Card Responsive */
    .case-study-professional-header {
        padding: 30px 30px 25px;
    }

    .case-study-content-grid {
        padding: 30px;
    }

    .case-study-results-section {
        padding: 25px 30px 30px;
    }

    .professional-case-title {
        font-size: 28px;
    }

    .professional-case-description {
        font-size: 16px;
    }

    .trust-badge {
        font-size: 12px;
        padding: 8px 12px;
    }

    .professional-revenue-card {
        padding: 30px 24px;
        margin-top: 30px;
    }

    .professional-metric-card {
        margin-bottom: 20px;
        padding: 20px 16px;
    }

    .metric-icon-wrapper {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .result-number {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 14px;
        margin-bottom: 16px;
    }

    .campaign-overview-section {
        margin-bottom: 30px;
    }

    /* Professional Roadmap Mobile */
    .professional-implementation-roadmap {
        padding: 0 15px;
    }

    .phase-connector {
        display: none !important;
    }

    .phase-content-wrapper,
    .phase-content-wrapper.reverse {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .phase-icon-container {
        width: 64px;
        height: 64px;
        font-size: 20px;
    }

    .phase-details {
        padding: 24px 20px;
    }

    .phase-title {
        font-size: 18px;
    }

    .phase-description {
        font-size: 14px;
    }

    .phase-outcome {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .metric-value {
        font-size: 24px;
    }

    .guarantee-card {
        padding: 24px 20px;
    }
}

@media (max-width: 767px) {
    .proven-results-redesign {
        padding: 60px 0;
    }

    /* Professional Case Study Card Mobile */
    .case-study-professional-header {
        padding: 25px 20px 20px;
    }

    .case-study-content-grid {
        padding: 25px 20px;
    }

    .case-study-results-section {
        padding: 20px 20px 25px;
    }

    .professional-case-title {
        font-size: 24px;
    }

    .professional-case-description {
        font-size: 15px;
    }

    .case-study-badges-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .trust-badge {
        font-size: 11px;
        padding: 6px 10px;
    }

    .professional-revenue-card {
        padding: 25px 20px;
        margin-top: 25px;
    }

    .revenue-amount {
        font-size: 2.5rem;
    }

    .professional-metric-card {
        padding: 18px 14px;
        margin-bottom: 16px;
    }

    .metric-icon-wrapper {
        width: 36px;
        height: 36px;
        font-size: 16px;
        margin-bottom: 12px;
    }

    .metric-content .metric-value {
        font-size: 14px;
    }

    .result-number {
        font-size: 1.8rem;
    }

    .result-label {
        font-size: 11px;
    }

    .section-subtitle {
        font-size: 13px;
        margin-bottom: 14px;
    }

    .campaign-overview-section {
        margin-bottom: 25px;
    }

    .counter {
        font-size: 2.5rem;
    }

    .metric-suffix {
        font-size: 1.5rem;
    }

    .metric-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    /* Professional Roadmap Small Mobile */
    .implementation-phases-container {
        padding: 0 10px;
    }

    .implementation-phase {
        margin-bottom: 60px;
    }

    .phase-icon-container {
        width: 56px;
        height: 56px;
        font-size: 18px;
        margin-bottom: 12px;
    }

    .phase-details {
        padding: 20px 16px;
    }

    .phase-title {
        font-size: 16px;
    }

    .phase-description {
        font-size: 13px;
        margin-bottom: 20px;
    }

    .deliverable-item {
        font-size: 13px;
        margin-bottom: 10px;
    }

    .metric-value {
        font-size: 20px;
    }

    .metric-label {
        font-size: 10px;
    }

    .outcome-insight {
        font-size: 12px;
    }

    .guarantee-card {
        padding: 20px 16px;
    }

    .guarantee-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-bottom: 16px;
    }

    .guarantee-title {
        font-size: 16px;
    }

    .guarantee-text {
        font-size: 13px;
    }
}

@media (max-width: 767px) {
	.performance-section .metric-item{border-bottom:none !important}
    .vbb-content-container,
    .unified-value-card {
        padding: 20px;
        margin-top: 20px;
    }

    .challenge-card {
        padding: 20px;
    }

    /* Mobile Challenge-Solution Grid */
    .programmatic-challenges-grid {
        gap: 32px;
    }

    .challenge-solution-item {
        grid-template-columns: 1fr;
        border-radius: 20px;
    }

    .challenge-side,
    .solution-side {
        padding: 32px 24px;
    }

    .challenge-solution-item .challenge-title,
    .solution-title {
        font-size: 18px;
    }

    .challenge-solution-item .challenge-description,
    .solution-description {
        font-size: 14px;
    }

    .impact-number {
        font-size: 24px;
    }

    .result-improvement {
        font-size: 24px;
    }

    .insight-callout {
        padding: 32px 24px;
    }

    .insight-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-bottom: 16px;
    }

    .insight-title {
        font-size: 20px;
    }

    .insight-text {
        font-size: 15px;
    }

    .data-decisions-item {
        padding: 20px;
    }

    .data-decisions-item-h3 {
        font-size: 18px;
    }

    .challenge-title {
        font-size: 18px;
    }

    .programmatic-evolution-chart {
        padding: 16px;
    }

    .industry-stat-callout,
    .industry-insight-box {
        padding: 16px;
    }

    .stat-number {
        font-size: 24px;
    }

    /* Two-Section Mobile Layout */
    .evolution-section,
    .performance-section {
        margin-bottom: 32px;
    }

    .section-header {
        margin-bottom: 20px;
    }

    .section-header h5 {
        font-size: 18px;
    }

    .section-header p {
        font-size: 13px;
    }

    /* Apple-Inspired Mobile Evolution */
    .evolution-timeline-compact {
        padding: 24px 20px;
    }

    .evolution-path {
        flex-direction: column;
        gap: 20px;
    }

    .evolution-arrow {
        transform: rotate(90deg);
        margin: 8px 0;
        opacity: 0.4;
    }

    .evolution-step {
        padding: 20px 16px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16px;
        backdrop-filter: blur(10px);
        border: 0.5px solid rgba(0, 0, 0, 0.08);
    }

    .step-marker {
        width: 20px;
        height: 20px;
        margin-bottom: 12px;
    }

    .step-content {
        font-size: 15px;
    }

    .step-year {
        font-size: 14px;
        margin-bottom: 6px;
    }

    .step-method {
        font-size: 13px;
        margin-bottom: 10px;
    }

    .step-efficiency {
        font-size: 12px;
        padding: 6px 10px;
    }

    /* Apple-Inspired Mobile Metrics */
    .key-metrics-grid {
        padding: 24px 20px;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 24px;
    }

    .metric-item {
        padding: 20px 16px;
        gap: 12px;
    }

    .metric-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
        border-radius: 10px;
    }

    .metric-comparison {
        gap: 6px;
        margin-bottom: 8px;
    }

    .old-value, .new-value {
        font-size: 16px;
    }

    .metric-label {
        font-size: 11px;
        margin-bottom: 8px;
    }

    /* Apple-Inspired Mobile ROI */
    .roi-impact {
        padding: 24px 20px;
        border-radius: 16px;
    }

    .roi-content-compact {
        gap: 16px;
    }

    .multiplier-number {
        font-size: 36px;
    }

    .multiplier-text {
        font-size: 12px;
        letter-spacing: 0.6px;
    }

    .roi-description {
        font-size: 13px;
        max-width: 280px;
    }



    /* Mobile Challenge-Solution Grid */
    .programmatic-challenges-grid {
        gap: 32px;
    }

    .challenge-solution-item {
        grid-template-columns: 1fr;
        border-radius: 20px;
    }

    .challenge-side,
    .solution-side {
        padding: 32px 24px;
    }

    .challenge-title,
    .solution-title {
        font-size: 18px;
    }

    .challenge-description,
    .solution-description {
        font-size: 14px;
    }

    .impact-number {
        font-size: 24px;
    }

    .result-improvement {
        font-size: 24px;
    }

    /* Mobile Core Problem Statement */
    .core-problem-statement {
        padding: 32px 24px;
    }

    .problem-visual {
        grid-template-columns: 1fr;
        gap: 24px;
        text-align: center;
    }

    .transformation-arrow {
        order: 2;
        flex-direction: row;
        gap: 12px;
    }

    .arrow-line {
        width: 40px;
        height: 2px;
        background: linear-gradient(to right, #f45888, #8f76f5);
    }

    .arrow-line::after {
        bottom: 50%;
        left: 100%;
        transform: translateY(50%);
        border-left: 6px solid #8f76f5;
        border-right: none;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
    }

    .arrow-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .arrow-text {
        font-size: 10px;
    }

    .problem-stat,
    .solution-stat {
        font-size: 20px;
    }

    .problem-desc,
    .solution-desc {
        font-size: 13px;
    }

    .insight-highlight {
        flex-direction: column;
        gap: 8px;
        padding: 20px;
        font-size: 14px;
        text-align: center;
    }

    /* Mobile Use Case Showcase */
    .use-case-showcase {
        gap: 32px;
    }

    .use-case-container {
        grid-template-columns: 1fr;
        gap: 32px;
        padding: 32px 24px;
        border-radius: 20px;
    }

    .use-case-main {
        gap: 24px;
    }

    .use-case-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .use-case-title {
        font-size: 24px;
    }

    .use-case-description {
        font-size: 15px;
    }

    .use-case-metrics {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 24px;
    }

    .metric-item {
        padding: 16px 12px;
    }

    .metric-value {
        font-size: 20px;
    }

    .use-case-implementation {
        padding: 20px;
    }

    .implementation-steps {
        gap: 10px;
    }

    .step-item {
        font-size: 13px;
    }

    .step-number {
        width: 20px;
        height: 20px;
        font-size: 11px;
    }

    /* Proven Results Section Styles */
    .proven-results-section .metric-card {
        transition: all 0.3s ease;
    }

    .proven-results-section .metric-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(0,0,0,0.12) !important;
    }

    .proven-results-section .timeline-section {
        position: relative;
        overflow: hidden;
    }

    .proven-results-section .timeline-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(233, 88, 161, 0.02) 0%, rgba(255, 93, 116, 0.02) 100%);
        pointer-events: none;
    }

    .proven-results-section .timeline-item {
        position: relative;
        padding-left: 20px;
    }

    .proven-results-section .timeline-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 8px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);
    }

    .proven-results-section .revenue-highlight {
        position: relative;
        overflow: hidden;
    }

    .proven-results-section .revenue-highlight::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(233, 88, 161, 0.03) 0%, transparent 70%);
        pointer-events: none;
    }



    /* Case Study Chart Styles */
    .case-study-chart {
        transition: all 0.3s ease;
    }

    .case-study-chart:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 32px rgba(0,0,0,0.08) !important;
    }

    .chart-placeholder {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .chart-placeholder::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s ease;
    }

    .chart-placeholder:hover::before {
        left: 100%;
    }

    .chart-header h6 {
        color: #2d3748;
    }

    /* GSAP Animation Classes */
    .chart-animate-in {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }

    .chart-icon-pulse {
        animation: none; /* Will be controlled by GSAP */
    }

    .chart-data-point {
        opacity: 0;
        transform: scale(0);
    }

    .visual-container {
        max-width: 100% !important;
        width: 100% !important;
    }

    .svg-chart-container {
        width: 100% !important;
        max-width: 100% !important;
    }

    .platform-integration {
        padding: 20px;
        gap: 16px;
        margin: 0;
    }

    .platform-image {
        width: 48px;
        height: 48px;
    }

    .dual-platform .platform-image {
        width: 36px;
        height: 36px;
    }

    .adzeta-ai-badge {
        padding: 6px 12px;
        font-size: 11px;
        gap: 6px;
        width: 100px;
    }

    .outcome-indicator {
        padding: 12px;
    }

    .outcome-value {
        font-size: 18px;
    }

    .evolution-timeline::before {
        left: 20px;
    }

    .evolution-era {
        padding-left: 50px;
    }

    .era-marker {
        left: 12px;
    }

    /* Mobile-specific metrics comparison fixes */
    .metrics-comparison {
        padding: 20px 15px;
        margin: 0 -5px;
    }

    .metric-comparison-cards {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .metric-card {
        min-width: auto;
        max-width: none;
        padding: 20px 16px;
        margin: 0;
    }

    .metric-value {
        font-size: 28px;
    }

    .vs-connector {
        min-width: auto;
        height: 60px;
        flex-direction: row;
        justify-content: center;
        order: 2;
    }

    .vs-line {
        width: 60px;
        height: 2px;
        margin-bottom: 0;
        margin-right: 8px;
        background: linear-gradient(to right, #e5e7eb, #f45888, #e5e7eb);
    }

    .vs-badge {
        width: 32px;
        height: 32px;
        font-size: 10px;
        margin-bottom: 0;
        margin-right: 8px;
    }

    .improvement-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .improvement-text {
        font-size: 10px;
        white-space: nowrap;
    }

    .improvement-badge {
        top: -8px;
        right: -8px;
        font-size: 10px;
        padding: 4px 8px;
    }

    .roi-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .roi-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
        align-self: center;
    }

    .multiplier-text {
        font-size: 24px;
    }

    /* Ensure no horizontal overflow */
    .metric-comparison-row {
        overflow-x: hidden;
    }

    .comparison-title {
        font-size: 16px;
    }

    .comparison-subtitle {
        font-size: 13px;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .metrics-comparison {
        padding: 16px 10px;
        margin: 0 -10px;
    }

    .metric-card {
        padding: 16px 12px;
    }

    .metric-value {
        font-size: 24px;
    }

    .metric-label {
        font-size: 11px;
    }

    .metric-desc {
        font-size: 12px;
    }

    .vs-connector {
        height: 50px;
    }

    .vs-line {
        width: 40px;
    }

    .vs-badge {
        width: 28px;
        height: 28px;
        font-size: 9px;
    }

    .improvement-text {
        font-size: 9px;
    }

    .roi-summary {
        padding: 16px;
    }

    .multiplier-text {
        font-size: 20px;
    }
}
</style>

<section class="cover-background top-space-padding pb-0 overflow-hidden position-relative lg-pb-30px hero-section ecom-ppc">
    <div class="professional-gradient-container">
        <div class="corner-gradient top-left"></div>
        <div class="corner-gradient top-right"></div>
        <div class="corner-gradient bottom-left"></div>
        <div class="corner-gradient bottom-right"></div>
        <div class="diagonal-gradient"></div>
        <div class="mesh-overlay"></div>
        <div class="vignette-overlay"></div>
    </div>
    <div class="container h-100">
        <!-- Removed distracting background elements for a more professional look -->
        <div class="row align-items-center h-100 md-mt-30px md-mb-10px pt-4">
            <div class="col-xl-6 col-lg-6 mb-9 position-relative z-index-1 ps-lg-5" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 200 }'>
                <div class="d-flex align-items-center mb-15px" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
                    <span class="fs-12 fw-light text-white opacity-90 primary-font ls-wide">
                        <span class="ai-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="pulse">
                                <!-- Modern AI chip/processor shape -->
                                <rect x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5"/>
                                <!-- Circuit lines -->
                                <path class="circuit1" d="M8 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit2" d="M12 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit3" d="M16 4V2" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit4" d="M8 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit5" d="M12 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit6" d="M16 22V20" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit7" d="M2 8H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit8" d="M2 12H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit9" d="M2 16H4" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit10" d="M20 8H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit11" d="M20 12H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <path class="circuit12" d="M20 16H22" stroke="url(#paint0_linear_ai_icon)" stroke-width="1.5" stroke-linecap="round"/>
                                <!-- Inner processor grid -->
                                <path class="grid" d="M8 8H16M8 12H16M8 16H16M8 8V16M12 8V16M16 8V16" stroke="url(#paint0_linear_ai_icon)" stroke-width="0.75" stroke-linecap="round" stroke-dasharray="1 2"/>
                                <!-- Central core -->
                                <rect class="core" x="10" y="10" width="4" height="4" fill="url(#paint0_linear_ai_icon)"/>
                                <!-- Sparkle overlay -->
                                <rect class="sparkle" x="4" y="4" width="16" height="16" rx="2" stroke="url(#paint1_linear_ai_icon)" stroke-width="1" stroke-opacity="0.7"/>
                                <defs>
                                    <linearGradient id="paint0_linear_ai_icon" x1="2" y1="2" x2="22" y2="22" gradientUnits="userSpaceOnUse">
                                        <stop offset="0" stop-color="#e958a1"/>
                                        <stop offset="0.5" stop-color="#8f76f5"/>
                                        <stop offset="1" stop-color="#4a9eff"/>
                                    </linearGradient>
                                    <linearGradient id="paint1_linear_ai_icon" x1="20" y1="4" x2="4" y2="20" gradientUnits="userSpaceOnUse">
                                        <stop offset="0" stop-color="#ffffff"/>
                                        <stop offset="1" stop-color="#ffffff" stop-opacity="0"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </span>
                        <span class="text-gradient-purple-blue ls-3px">PROGRAMMATIC x ADZETA AI</span>
                    </span>
                </div>
                <h1 class="alt-font mb-15px fs-50 md-fs-60 sm-fs-60 xs-fs-45 fw-600 lh-2-5 heading-gradient" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 1000, "delay": 300, "easing": "easeOutQuad" }'>
                    <span class="fw-300">Unlock Programmatic<br>Profit: AI-Powered<br></span>LTV for DV360 & Amazon Ads.
                </h1>
                <div class="alt-font fw-400 fs-16 w-90 sm-w-100 mb-25px xs-mb-20px text-white opacity-75 lh-1-5" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 800, "easing": "easeOutQuint" }'>Go Beyond Standard Programmatic. Target True Customer Value. Maximize programmatic ROI with Adzeta's Predictive LTV for profitable e-commerce customer acquisition.</div>
                <div class="d-flex flex-wrap" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 1000, "delay": 1000, "easing": "easeOutQuint" }'>
                    <a href="free-ad-audit.php" class="btn btn-large btn-hover-animation box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-gradient-pink-orange btn-rounded me-15px mt-15px fw-600 alt-font">
                    <span>
                    <span class="btn-text">Get Free Programmatic Analysis</span>
                    <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
                    </span>
                    </a>
                    <a href="#" onclick="alert('Download currently unavailable. Please try again later.'); return false;"
                        class="btn btn-large box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-transparent-white-light btn-rounded border-1 mt-20px fw-600 alt-font">
                    <span>
                    <span class="btn-text">DV360 & Amazon Guide</span>
                    <span class="btn-icon"><i class="fa-solid fa-download"></i></span>
                    </span>
                    </a>
                </div>
            </div>
            <div class="col-xl-6 col-lg-6 align-self-center">
                <div class="platform-animation-container pt-0 programmatic-ads" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 500 }'>
				<style>
					.programmatic-ads .label-left-2{ bottom: -25px;}
				</style>
                    <!-- New Hero Animation Structure with Semicircle Layout -->
                    <div class="hero-animation-container">
                        <!-- Center Element - Adzeta AI Core -->
                        <div class="animation-element adzeta-ai-center">
                            <div class="position-relative">
                                <img src="images/adzeta-ai-center.png" alt="Adzeta AI Core" class="w-100">
                                <div class="element-label label-center">ADZETA AI</div>
                            </div>
                        </div>
                        <!-- Left Side Elements - Data Input (Semicircle) -->
                        <div class="animation-element adzeta-ai-left-1">
                            <img src="images/pa-customer-data.png" alt="Customer Data" class="w-100">
                            <div class="element-label label-left-1">CUSTOMER DATA</div>
                        </div>
                        <div class="animation-element adzeta-ai-left-2">
                            <img src="images/pa-behavioral-signals.png" alt="Behavioral Signals" class="w-100">
                            <div class="element-label label-left-2">BEHAVIORAL SIGNALS</div>
                        </div>
                        <div class="animation-element adzeta-ai-left-3">
                            <img src="images/pa-purchase-history.png" alt="Purchase History" class="w-100">
                            <div class="element-label label-left-3">PURCHASE HISTORY</div>
                        </div>
                        <!-- Right Side Elements - Ad Platforms (Semicircle) -->
                        <div class="animation-element adzeta-ai-right-1">
                            <img src="images/pa-adzeta-ltv.png" alt="Predictive LTV Generated" class="w-100">
                            <div class="element-label label-right-1">Predictive LTV</div>
                        </div>
                        <div class="animation-element adzeta-ai-right-2">
                            <img src="images/pa-google-ads.png" alt="DV360 Enhanced" class="w-100">
                            <div class="element-label label-right-2">DV360</div>
                        </div>
                        <div class="animation-element adzeta-ai-right-3">
                            <img src="images/pa-amazon.png" alt="Amazon DSP" class="w-150">
                            <div class="element-label label-right-3">Amazon DSP</div>
                        </div>
                        <!-- Connection Lines - Left Side -->
                        <div class="connection-line connection-left-1"></div>
                        <div class="connection-line connection-left-2"></div>
                        <div class="connection-line connection-left-3"></div>
                        <!-- Connection Lines - Right Side -->
                        <div class="connection-line connection-right-1"></div>
                        <div class="connection-line connection-right-2"></div>
                        <div class="connection-line connection-right-3"></div>
                        <!-- Data Flow Particles - Left Side -->
                        <div class="data-particle particle-left-1"></div>
                        <div class="data-particle particle-left-1-delay"></div>
                        <div class="data-particle particle-left-2"></div>
                        <div class="data-particle particle-left-2-delay"></div>
                        <div class="data-particle particle-left-3"></div>
                        <div class="data-particle particle-left-3-delay"></div>
                        <!-- Data Flow Particles - Right Side -->
                        <div class="data-particle particle-right-1"></div>
                        <div class="data-particle particle-right-1-delay"></div>
                        <div class="data-particle particle-right-2"></div>
                        <div class="data-particle particle-right-2-delay"></div>
                        <div class="data-particle particle-right-3"></div>
                        <div class="data-particle particle-right-3-delay"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end hero section -->




<!-- Section 1: Why Predictive LTV is Essential for Programmatic Success -->
<section class="programmatic-foundation-section overflow-hidden position-relative">
    <!-- Light section background with subtle gradient that fades to white at bottom -->
    <div class="position-absolute" style="
        inset: 0;
        background: linear-gradient(to bottom,
        rgba(248, 249, 250, 1) 0%,
        rgba(242, 240, 238, 0.8) 40%,
        rgba(255, 255, 255, 1) 100%);
        z-index: 0;
        pointer-events: none;
        "></div>
    <!-- Subtle mesh texture -->
    <div class="position-absolute" style="
        inset: 0;
        background:
        linear-gradient(90deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px),
        linear-gradient(0deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px);
        background-size: 28px 28px;
        opacity: 0.5;
        z-index: 0;
        pointer-events: none;
        "></div>
    <div class="container position-relative" style="z-index: 1;">
        <div class="row justify-content-center mb-50px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="scale-tag-container">
                    <div class="scale-tag">INDUSTRY EVOLUTION</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">The $500B Programmatic Shift: Why LTV Prediction Defines Winners</span></h3>
                <p class="mb-0">As programmatic advertising reaches $500+ billion globally, the competitive advantage no longer lies in reach or frequency—it's in precision. Leading brands are moving beyond demographic targeting to predictive customer value modeling, fundamentally reshaping how programmatic budgets drive sustainable growth.</p>
            </div>
        </div>
    </div>
    <div class="container position-relative" style="z-index: 1;">
        <div class="vbb-content-container box-shadow-extra-large box-shadow-extra-large-hover">
            <div class="row align-items-center">
                <!-- Left Column: Enhanced Industry Context -->
                <div class="col-lg-5 mb-5 mb-lg-0" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 300, "easing": "easeOutQuad" }'>
                    <div class="vbb-content">
                        <!-- Industry stat callout -->
                        <div class="industry-stat-callout mb-4">
                            <div class="stat-number">73%</div>
                            <div class="stat-text">of programmatic spend targets users who never become profitable customers</div>
                            <div class="stat-source">— Forrester Research, 2024</div>
                        </div>

                        <p class="md-mt-30">The programmatic ecosystem's sophistication has created a paradox: while targeting capabilities have advanced exponentially, most campaigns still optimize for immediate conversions rather than long-term customer value. This fundamental misalignment costs e-commerce brands billions annually in misdirected ad spend.</p>

                        <p><span class="fw-600">Next-Generation Programmatic Strategy</span> requires moving beyond traditional lookalike audiences and behavioral segments to <em>predictive customer lifetime value modeling</em>. Industry leaders are already implementing AI-driven LTV prediction to transform their programmatic ROI—achieving 40-60% improvements in customer acquisition efficiency.</p>

                        <!-- Enhanced insight box -->
                        <div class="industry-insight-box mt-4">
                            <div class="insight-header">
                                <div class="insight-icon">
                                    <i class="line-icon-Idea-5"></i>
                                </div>
                                <h6 class="fw-600 mb-2">The Competitive Reality</h6>
                            </div>
                            <p class="mb-0">While competitors chase volume metrics, forward-thinking brands are using predictive LTV to identify and acquire customers who will drive 3-5x higher lifetime value. This isn't just optimization—it's strategic repositioning for sustainable growth in an increasingly competitive landscape.</p>
                        </div>
                    </div>
                </div>
                <!-- Right Column: Two Distinct Sections -->
                <div class="col-lg-7" data-anime='{ "translateY": [20, 0], "opacity": [0,1], "duration": 800, "delay": 500, "easing": "easeOutQuad" }'>
                    <div class="vbb-visual">
                        <!-- Section 1: Evolution Timeline -->
                        <div class="evolution-section">
                            <div class="section-header">
                                <h5 class="fw-600 mb-2">Programmatic Evolution</h5>
                                <p class="text-muted mb-4">From broad targeting to predictive precision</p>
                            </div>

                            <div class="evolution-timeline-compact">
                                <div class="evolution-path">
                                    <div class="evolution-step past">
                                        <div class="step-marker"></div>
                                        <div class="step-content">
                                            <div class="step-year">2015-2019</div>
                                            <div class="step-method">Basic Targeting</div>
                                            <div class="step-efficiency">15% efficiency</div>
                                        </div>
                                    </div>

                                    <div class="evolution-arrow">
                                        <i class="fa-solid fa-arrow-right"></i>
                                    </div>

                                    <div class="evolution-step current">
                                        <div class="step-marker"></div>
                                        <div class="step-content">
                                            <div class="step-year">2020-2023</div>
                                            <div class="step-method">Behavioral Segments</div>
                                            <div class="step-efficiency">35% efficiency</div>
                                        </div>
                                    </div>

                                    <div class="evolution-arrow">
                                        <i class="fa-solid fa-arrow-right"></i>
                                    </div>

                                    <div class="evolution-step future">
                                        <div class="step-marker"></div>
                                        <div class="step-content">
                                            <div class="step-year">2024+</div>
                                            <div class="step-method">Predictive LTV</div>
                                            <div class="step-efficiency">65% efficiency</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section 2: Performance Impact -->
                        <div class="performance-section mt-5">
                            <div class="section-header">
                                <h5 class="fw-600 mb-2">Performance Impact</h5>
                                <p class="text-muted mb-4">Real results from LTV-driven targeting</p>
                            </div>

                            <div class="key-metrics-grid">
                                <div class="metrics-grid">
                                    <!-- CAC Metric -->
                                    <div class="metric-item" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 600, "delay": 200 }'>
                                        <div class="metric-icon">
                                            <i class="line-icon-Dollar"></i>
                                        </div>
                                        <div class="metric-content">
                                            <div class="metric-comparison">
                                                <span class="old-value">$127</span>
                                                <i class="fa-solid fa-arrow-right"></i>
                                                <span class="new-value">$78</span>
                                            </div>
                                            <div class="metric-label">Customer Acquisition Cost</div>
                                            <div class="improvement-tag negative">-39% CAC</div>
                                        </div>
                                    </div>

                                    <!-- LTV Metric -->
                                    <div class="metric-item" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 600, "delay": 400 }'>
                                        <div class="metric-icon">
                                            <i class="line-icon-Coins"></i>
                                        </div>
                                        <div class="metric-content">
                                            <div class="metric-comparison">
                                                <span class="old-value">$284</span>
                                                <i class="fa-solid fa-arrow-right"></i>
                                                <span class="new-value">$467</span>
                                            </div>
                                            <div class="metric-label">Customer Lifetime Value</div>
                                            <div class="improvement-tag positive">+64% LTV</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ROI Impact -->
                                <div class="roi-impact mt-4" data-anime='{ "opacity": [0,1], "scale": [0.95,1], "duration": 600, "delay": 600 }'>
                                    <div class="roi-content-compact">
                                        <div class="roi-multiplier-large">
                                            <span class="multiplier-number">4.3x</span>
                                            <span class="multiplier-text">ROI Improvement</span>
                                        </div>
                                        <div class="roi-description">
                                            Lower costs + Higher value = Exponential growth
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section 2: Challenges in Maximizing Profit from DV360 & Amazon Ads -->
<section class="google-value-challenges overflow-hidden position-relative">
    <!-- Light section background with subtle gradient that matches previous sections -->
    <div class="position-absolute" style="
        inset: 0;
        background: linear-gradient(to bottom,
        rgba(255, 255, 255, 1) 0%,
        rgba(248, 249, 250, 0.8) 20%,
        rgba(242, 240, 238, 0.8) 50%,
        rgba(249, 249, 255, 0.8) 80%,
        rgba(255, 255, 255, 1) 100%);
        z-index: 0;
        pointer-events: none;
        "></div>
    <!-- Subtle mesh texture -->
    <div class="position-absolute" style="
        inset: 0;
        background:
        linear-gradient(90deg, rgba(143, 118, 245, 0.02) 1px, transparent 1px),
        linear-gradient(0deg, rgba(143, 118, 245, 0.02) 1px, transparent 1px);
        background-size: 28px 28px;
        opacity: 0.5;
        z-index: 0;
        pointer-events: none;
        "></div>
    <div class="container position-relative" style="z-index: 1;">
        <!-- Modern Header Section -->
        <div class="row justify-content-center mb-60px">
            <div class="col-xl-10 col-lg-11 text-center">
                <div class="scale-tag-container mb-20px">
                    <div class="scale-tag">THE CHALLENGE</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-25px">
                    <span class="modern-heading-gradient">Why 73% of Programmatic Spend Targets Unprofitable Customers</span>
                </h3>
                <p class="fs-18 mb-0 text-dark-gray w-85 mx-auto">
                    Most programmatic campaigns optimize for immediate conversions, missing the customers who drive real long-term value. Here's what's holding back your DV360 and Amazon Ads performance.
                </p>
            </div>
        </div>

        <!-- Interactive Challenge-Solution Grid -->
        <div class="programmatic-challenges-grid">
            <!-- Challenge 1: DV360 Custom Bidding -->
            <div class="challenge-solution-item box-shadow-large" data-anime='{ "opacity": [0,1], "translateY": [30,0], "duration": 800, "delay": 200 }'>
                <div class="challenge-side">
                    <div class="challenge-header">
                        <div class="challenge-number">01</div>
                        <div class="challenge-platform">DV360</div>
                    </div>
                    <h4 class="challenge-title text-dark-gray">Custom Bidding Without LTV Intelligence</h4>
                    <p class="challenge-description">DV360's algorithms optimize for conversion signals, but without predictive LTV data, they target users who convert once and never return.</p>
                    <div class="challenge-impact">
                        <div class="impact-metric">
                            <span class="impact-number">-45%</span>
                            <span class="impact-label">Wasted Ad Spend</span>
                        </div>
                    </div>
                </div>
                <div class="solution-side">
                    <div class="solution-header">
                        <div class="solution-icon">
                            <i class="line-icon-Brain"></i>
                        </div>
                        <span class="solution-label">Adzeta Solution</span>
                    </div>
                    <h5 class="solution-title text-dark-gray">LTV-Powered Custom Bidding</h5>
                    <p class="solution-description">Feed DV360's algorithms with predictive LTV scores, enabling them to bid higher for users likely to become valuable long-term customers.</p>
                    <div class="solution-result">
                        <span class="result-improvement">+67% ROAS</span>
                    </div>
                </div>
            </div>

            <!-- Challenge 2: Amazon DSP Audience Targeting -->
            <div class="challenge-solution-item box-shadow-large" data-anime='{ "opacity": [0,1], "translateY": [30,0], "duration": 800, "delay": 400 }'>
                <div class="challenge-side">
                    <div class="challenge-header">
                        <div class="challenge-number">02</div>
                        <div class="challenge-platform">Amazon DSP</div>
                    </div>
                    <h4 class="challenge-title text-dark-gray">Audience Targeting Based on Past Behavior</h4>
                    <p class="challenge-description">Amazon's rich shopper data shows what customers bought, but not which prospects will become high-value repeat purchasers.</p>
                    <div class="challenge-impact">
                        <div class="impact-metric">
                            <span class="impact-number">-38%</span>
                            <span class="impact-label">Customer LTV</span>
                        </div>
                    </div>
                </div>
                <div class="solution-side">
                    <div class="solution-header">
                        <div class="solution-icon">
                            <i class="line-icon-Target"></i>
                        </div>
                        <span class="solution-label">Adzeta Solution</span>
                    </div>
                    <h5 class="solution-title text-dark-gray">Predictive Audience Segmentation</h5>
                    <p class="solution-description">Combine Amazon's shopper insights with our LTV predictions to target prospects most likely to become loyal, high-value customers.</p>
                    <div class="solution-result">
                        <span class="result-improvement">+52% LTV</span>
                    </div>
                </div>
            </div>

            <!-- Challenge 3: Prospecting Scale -->
            <div class="challenge-solution-item box-shadow-large" data-anime='{ "opacity": [0,1], "translateY": [30,0], "duration": 800, "delay": 600 }'>
                <div class="challenge-side">
                    <div class="challenge-header">
                        <div class="challenge-number">03</div>
                        <div class="challenge-platform">Both Platforms</div>
                    </div>
                    <h4 class="challenge-title text-dark-gray">Prospecting Campaigns Hit Profitability Walls</h4>
                    <p class="challenge-description">Scaling beyond retargeting becomes expensive when you can't distinguish between one-time buyers and future loyal customers.</p>
                    <div class="challenge-impact">
                        <div class="impact-metric">
                            <span class="impact-number">3x</span>
                            <span class="impact-label">Higher CAC</span>
                        </div>
                    </div>
                </div>
                <div class="solution-side">
                    <div class="solution-header">
                        <div class="solution-icon">
                            <i class="line-icon-Rocket"></i>
                        </div>
                        <span class="solution-label">Adzeta Solution</span>
                    </div>
                    <h5 class="solution-title text-dark-gray">Profitable Prospecting at Scale</h5>
                    <p class="solution-description">Scale prospecting campaigns confidently by targeting new users with the highest predicted lifetime value across both platforms.</p>
                    <div class="solution-result">
                        <span class="result-improvement">+89% Scale</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Impact Statement -->
        <div class="row justify-content-center mt-60px">
            <div class="col-xl-10">
                <div class="core-problem-statement" data-anime='{ "opacity": [0,1], "translateY": [30,0], "duration": 800, "delay": 800 }'>
                    <div class="problem-visual">
                        <div class="problem-side">
                            <div class="problem-header">
                                <div class="problem-icon">
                                    <i class="line-icon-Danger"></i>
                                </div>
                                <span class="problem-label">Current Reality</span>
                            </div>
                            <h4 class="problem-stat">73% Wasted Spend</h4>
                            <p class="problem-desc">Programmatic platforms target users who convert once but never return</p>
                        </div>

                        <div class="transformation-arrow">
                            <div class="arrow-line"></div>
                            <div class="arrow-icon">
                                <i class="line-icon-Brain"></i>
                            </div>
                            <div class="arrow-text">Adzeta AI</div>
                        </div>

                        <div class="solution-side">
                            <div class="solution-header-alt">
                                <div class="solution-icon-alt">
                                    <i class="line-icon-Target"></i>
                                </div>
                                <span class="solution-label-alt">With LTV Intelligence</span>
                            </div>
                            <h4 class="solution-stat">Profitable Growth</h4>
                            <p class="solution-desc">Target prospects who become valuable long-term customers</p>
                        </div>
                    </div>

                    <div class="key-insight">
                        <div class="insight-highlight">
                            <i class="line-icon-Idea-5"></i>
                            <span>The missing piece isn't better targeting—it's predictive intelligence</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section 3: Adzeta's Solution: Predictive AI for DV360 & Amazon Ads -->
<section class="data-decisions-section position-relative overflow-hidden py-150px">
    <!-- Modern dark base gradient -->
    <div class="position-absolute" style="
        inset: 0;
        background: linear-gradient(135deg, #0A0913 0%, #1A142A 40%, #271E3D 75%, #341F53 100%);
        z-index: 0;
        pointer-events: none;
        "></div>
    <!-- Top-right soft purple glow -->
    <div class="position-absolute" style="
        inset: 0;
        background: radial-gradient(circle at 85% 20%, rgba(190, 130, 255, 0.18) 0%, transparent 60%);
        z-index: 1;
        pointer-events: none;
        "></div>
    <!-- Bottom-left pink accent glow -->
    <div class="position-absolute" style="
        inset: 0;
        background: radial-gradient(circle at 15% 85%, rgba(255, 105, 180, 0.15) 0%, transparent 60%);
        z-index: 1;
        pointer-events: none;
        "></div>
    <!-- Subtle mesh texture -->
    <div class="position-absolute" style="
        inset: 0;
        background:
        linear-gradient(90deg, rgba(255, 160, 255, 0.03) 1px, transparent 1px),
        linear-gradient(0deg, rgba(255, 160, 255, 0.03) 1px, transparent 1px);
        background-size: 28px 28px;
        opacity: 0.25;
        z-index: 1;
        pointer-events: none;
        "></div>
    <div class="container position-relative" style="z-index: 2;">
        <div class="row justify-content-center mb-80px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="mb-10px">
                    <span class="text-gradient-purple-blue fs-12 alt-font fw-600 ls-05px text-uppercase d-inline-block align-middle">ADZETA'S SOLUTION</span>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px text-white">LTV-Powered Precision for Your Programmatic Campaigns</h3>
                <p class="mb-10 text-white-transparent">Adzeta bridges the critical information gap in programmatic advertising. Our Predictive AI engine transforms your rich first-party e-commerce data into highly accurate LTV forecasts from a customer's very first interaction.</p>
            </div>
        </div>
        <div class="data-decisions-items-wrapper">
            <!-- Row 1: Forecasting LTV for Programmatic Audiences -->
            <div class="row justify-content-start mb-4">
                <div class="col-lg-7 col-md-9 col-sm-11">
                    <a href="#" class="data-decisions-item">
                        <div class="data-decisions-item-header">
                            <div class="data-decisions-item-icon">
                                <i class="line-icon-Brain icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                            </div>
                            <h3 class="data-decisions-item-h3">Forecasting LTV for Programmatic Audiences</h3>
                        </div>
                        <p class="data-decisions-item-p">Adzeta's AI analyzes your first-party data to build powerful LTV models. We then identify signals and characteristics across the programmatic ecosystem (including Amazon's retail data signals when applicable) that correlate with high predicted value for your brand. Our models continuously learn and adapt to maintain the highest prediction accuracy.</p>
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                            <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                </div>
            </div>
            <!-- Row 2: Enhancing DV360 Custom Bidding -->
            <div class="row justify-content-center mb-4">
                <div class="col-lg-7 col-md-9 col-sm-11">
                    <div class="animated-card">
                        <a href="#" class="data-decisions-item is-dark" style="background: linear-gradient(135deg, #1E1A33 0%, #2D1E4A 100%); border: none;">
                            <div class="data-decisions-item-header">
                                <div class="data-decisions-item-icon">
                                    <i class="line-icon-Gear-2 icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                                </div>
                                <h3 class="data-decisions-item-h3 is-white">Enhancing DV360 Custom Bidding</h3>
                            </div>
                            <p class="data-decisions-item-p is-white">We provide predictive LTV scores and custom audience segments directly to your DV360 account. This allows you to create sophisticated custom bidding algorithms in DV360 that optimize bids based on predicted future value, going far beyond standard optimization goals. Transform DV360's power with true value-based intelligence.</p>
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                                <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <!-- Row 3: Optimizing Amazon Ads for Lifetime Value -->
            <div class="row justify-content-end mb-4">
                <div class="col-lg-7 col-md-9 col-sm-11">
                    <a href="#" class="data-decisions-item">
                        <div class="data-decisions-item-header">
                            <div class="data-decisions-item-icon">
                                <i class="line-icon-Shopping-Cart icon-double-large" style="background: linear-gradient(135deg, #f45888 0%, #8f76f5 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                            </div>
                            <h3 class="data-decisions-item-h3">Optimizing Amazon Ads for Lifetime Value</h3>
                        </div>
                        <p class="data-decisions-item-p">Adzeta's insights help refine audience targeting and bidding strategies within Amazon DSP. Focus your Amazon ad spend on acquiring new customers predicted to have high repeat purchase rates and overall LTV, not just those making an initial purchase. Leverage Amazon's rich shopper data combined with our predictive intelligence.</p>
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none" class="data-decisions-item-arrow">
                            <path d="M21.1875 8.8125L8.8125 21.1875" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M12.3438 8.81625H21.185L21.1837 17.6562" stroke="currentcolor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Background elements -->
    <img src="images/ltv-signal-pa.png" loading="lazy" width="300" alt="LTV Signal" class="data-decisions-img-bg is-google">
    <img src="images/adzeta-bg-circle.svg" loading="lazy" width="538" height="538" alt="AdZeta" class="data-decisions-img-bg is-adzeta">
    <!-- Small decorative dots -->
    <div class="data-decisions-dots"></div>
</section>

<!-- Section 4: Use Cases: Driving E-commerce Success with Programmatic AI -->
<section class="proven-results-section overflow-hidden position-relative pb-0">
    <!-- Light section background with subtle gradient that matches previous sections -->
    <div class="position-absolute" style="
        inset: 0;
        background: linear-gradient(to bottom,
        rgba(255, 255, 255, 1) 0%,
        rgba(249, 249, 255, 0.8) 20%,
        rgba(248, 249, 250, 0.8) 50%,
        rgba(242, 240, 238, 0.8) 80%,
        rgba(255, 255, 255, 1) 100%);
        z-index: 0;
        pointer-events: none;
        "></div>
    <!-- Subtle mesh texture -->
    <div class="position-absolute" style="
        inset: 0;
        background:
        linear-gradient(90deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px),
        linear-gradient(0deg, rgba(143, 118, 245, 0.03) 1px, transparent 1px);
        background-size: 28px 28px;
        opacity: 0.5;
        z-index: 0;
        pointer-events: none;
        "></div>
    <div class="container position-relative" style="z-index: 1;">
        <!-- Modern Header Section -->
        <div class="row justify-content-center mb-60px">
            <div class="col-xl-10 text-center">
                <div class="scale-tag-container mb-20px">
                    <div class="scale-tag">REAL-WORLD APPLICATIONS</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">How Industry Leaders Drive Growth with LTV Intelligence</span></h3>
                <p class="fs-18 mb-10 text-dark-gray w-85 mx-auto">
                    Explore proven strategies that transform programmatic advertising from cost centers into profit engines across DV360 and Amazon DSP.
                </p>
            </div>
        </div>

        <!-- Interactive Use Case Showcase -->
        <div class="use-case-showcase">
            <!-- Use Case 1: Enterprise Prospecting -->
            <div class="use-case-container dv360-bg md-mb-30 mb-15 box-shadow-quadruple-large box-shadow-quadruple-large-hover" data-anime='{ "opacity": [0,1], "translateY": [40,0], "duration": 800, "delay": 200 }'>
                <div class="use-case-main">
                    <div class="use-case-header md-mt-25">
                        <div class="use-case-category">
                            <div class="category-icon">
                                <i class="line-icon-Target"></i>
                            </div>
                            <span class="category-label">Enterprise Prospecting</span>
                        </div>
                     
                    </div>

                    <div class="use-case-content">
                        <h4 class="use-case-title text-dark-gray">Scale High-Value Customer Acquisition</h4>
                        <p class="use-case-description">
                            Move beyond demographic targeting to identify prospects with the highest predicted lifetime value. Our AI analyzes behavioral patterns to find customers with 2.1x higher predicted lifetime value over 18 months, validated against 2.3M transaction dataset.
                        </p>

                        <div class="use-case-metrics">
                            <div class="metric-item">
                                <div class="metric-value">+<span class="counter" data-speed="2000" data-to="118">0</span>%</div>
                                <div class="metric-label">Prospecting ROAS</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">-<span class="counter" data-speed="2000" data-to="38">0</span>%</div>
                                <div class="metric-label">CAC Reduction</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value"><span class="counter" data-speed="2000" data-to="4">0</span>.<span class="counter" data-speed="2000" data-to="7">0</span>x</div>
                                <div class="metric-label">Scale Efficiency</div>
                            </div>
                        </div>
                    </div>

                    <div class="use-case-implementation">
                        <div class="implementation-header">
                            <i class="line-icon-Gear-2"></i>
                            <span>Implementation Approach</span>
                        </div>
                        <div class="implementation-steps">
                            <div class="step-item">
                                <span class="step-number">1</span>
                                <span class="step-text">Historical purchase data analysis (18-month lookback)</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">2</span>
                                <span class="step-text">Behavioral segmentation across 47 touchpoints</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">3</span>
                                <span class="step-text">DV360 custom bidding algorithm deployment</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">4</span>
                                <span class="step-text">3-week optimization cycles with A/B testing</span>
                            </div>
                        </div>
                        <div class="implementation-context mt-3">
                            <small class="text-muted">
                                <strong>Client Profile:</strong> Mid-market fashion e-commerce ($15M annual revenue) •
                                <strong>Campaign Duration:</strong> 180 days •
                                <strong>Model Accuracy:</strong> 89% validated
                            </small>
                        </div>
                    </div>
                </div>

                <div class="use-case-visual">
                    <div class="visual-container w-100">
                        <!-- DV360 ROAS Growth Chart -->
                        <div class="case-study-chart mb-4" style="background: white; border-radius: 12px; padding: 12px; border: 1px solid rgba(0,0,0,0.06); box-shadow: 0 4px 20px rgba(0,0,0,0.04);">
                            <div class="chart-header mb-2">
                                <h6 class="fw-600 mb-1 text-dark-gray">DV360 ROAS Growth</h6>
                                <small class="text-muted">6-Month Campaign Performance</small>
                            </div>

                            <div class="chart-container">
                                <svg class="performance-chart" viewBox="0 0 400 280" preserveAspectRatio="xMidYMid meet" style="height: 280px;"
                                    <defs>
                                        <linearGradient id="dv360Gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#e958a1" />
                                            <stop offset="50%" style="stop-color:#ff5d74" />
                                            <stop offset="100%" style="stop-color:#C46CFA" />
                                        </linearGradient>
                                        <linearGradient id="dv360AreaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#e958a1;stop-opacity:0.3" />
                                            <stop offset="100%" style="stop-color:#e958a1;stop-opacity:0.05" />
                                        </linearGradient>
                                    </defs>

                                    <!-- Grid lines -->
                                    <g class="chart-grid" stroke="rgba(0,0,0,0.1)" stroke-width="0.5">
                                        <line x1="50" y1="40" x2="350" y2="40"></line>
                                        <line x1="50" y1="80" x2="350" y2="80"></line>
                                        <line x1="50" y1="120" x2="350" y2="120"></line>
                                        <line x1="50" y1="160" x2="350" y2="160"></line>
                                        <line x1="50" y1="200" x2="350" y2="200"></line>
                                        <line x1="50" y1="240" x2="350" y2="240"></line>
                                    </g>

                                    <!-- Y-axis labels -->
                                    <g class="y-labels" fill="#666" font-size="11" font-family="system-ui">
                                        <text x="45" y="245" text-anchor="end">0%</text>
                                        <text x="45" y="205" text-anchor="end">50%</text>
                                        <text x="45" y="165" text-anchor="end">100%</text>
                                        <text x="45" y="125" text-anchor="end">150%</text>
                                        <text x="45" y="85" text-anchor="end">200%</text>
                                        <text x="45" y="45" text-anchor="end">250%</text>
                                    </g>

                                    <!-- X-axis labels -->
                                    <g class="x-labels" fill="#666" font-size="10" font-family="system-ui">
                                        <text x="80" y="265" text-anchor="middle">Month 1</text>
                                        <text x="130" y="265" text-anchor="middle">Month 2</text>
                                        <text x="180" y="265" text-anchor="middle">Month 3</text>
                                        <text x="230" y="265" text-anchor="middle">Month 4</text>
                                        <text x="280" y="265" text-anchor="middle">Month 5</text>
                                        <text x="330" y="265" text-anchor="middle">Month 6</text>
                                    </g>

                                    <!-- Area under curve -->
                                    <path class="chart-area"
                                          d="M 80,220 L 130,200 L 180,170 L 230,130 L 280,90 L 330,60 L 330,240 L 80,240 Z"
                                          fill="url(#dv360AreaGradient)"></path>

                                    <!-- Main line -->
                                    <path class="chart-line"
                                          d="M 80,220 L 130,200 L 180,170 L 230,130 L 280,90 L 330,60"
                                          stroke="url(#dv360Gradient)"
                                          stroke-width="4"
                                          fill="none"
                                          stroke-linecap="round"></path>

                                    <!-- Data points -->
                                    <g class="data-points">
                                        <circle cx="80" cy="220" r="5" fill="#e958a1" stroke="white" stroke-width="2"></circle>
                                        <circle cx="130" cy="200" r="5" fill="#e958a1" stroke="white" stroke-width="2"></circle>
                                        <circle cx="180" cy="170" r="5" fill="#ff5d74" stroke="white" stroke-width="2"></circle>
                                        <circle cx="230" cy="130" r="5" fill="#ff5d74" stroke="white" stroke-width="2"></circle>
                                        <circle cx="280" cy="90" r="5" fill="#C46CFA" stroke="white" stroke-width="2"></circle>
                                        <circle cx="330" cy="60" r="5" fill="#C46CFA" stroke="white" stroke-width="2"></circle>
                                    </g>

                                    <!-- Value labels -->
                                    <g class="value-labels" fill="#333" font-size="12" font-weight="600" font-family="system-ui">
                                        <text x="80" y="210" text-anchor="middle">23%</text>
                                        <text x="130" y="190" text-anchor="middle">45%</text>
                                        <text x="180" y="160" text-anchor="middle">67%</text>
                                        <text x="230" y="120" text-anchor="middle">89%</text>
                                        <text x="280" y="80" text-anchor="middle">118%</text>
                                        <text x="330" y="50" text-anchor="middle">142%</text>
                                    </g>
                                </svg>
                            </div>
                        </div>

                        <div class="platform-integration">
                            <div class="adzeta-ai-badge">
                                <img src="images/adzeta-logo-icon-white.png" alt="Adzeta AI" class="w-20 h-20">
                                <span>Adzeta AI</span>
                            </div>
                            <div class="outcome-indicator">
                                <div class="outcome-value">+$1.87M Revenue</div>
                                <div class="outcome-label">6-Month Results*</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Use Case 2: Amazon DSP Optimization -->
            <div class="use-case-container amazon-dsp-bg box-shadow-quadruple-large box-shadow-quadruple-large-hover" data-anime='{ "opacity": [0,1], "translateY": [40,0], "duration": 800, "delay": 400 }'>
                <div class="use-case-main">
                    <div class="use-case-header md-mt-20">
                        <div class="use-case-category">
                            <div class="category-icon">
                                <i class="line-icon-Shopping-Cart"></i>
                            </div>
                            <span class="category-label">E-commerce Optimization</span>
                        </div>
                         <!--<div class="use-case-platform">Amazon DSP</div>-->
                    </div>

                    <div class="use-case-content">
                        <h4 class="use-case-title text-dark-gray">Maximize Customer Lifetime Value</h4>
                        <p class="use-case-description">
                            Combine Amazon's rich shopper data with predictive LTV intelligence to target customers who will make repeat purchases. Focus ad spend on building long-term customer relationships, not just one-time sales.
                        </p>

                        <div class="use-case-metrics">
                            <div class="metric-item">
                                <div class="metric-value">+<span class="counter" data-speed="2000" data-to="76">0</span>%</div>
                                <div class="metric-label">Customer LTV</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">+<span class="counter" data-speed="2000" data-to="142">0</span>%</div>
                                <div class="metric-label">Repeat Purchase Rate</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">-<span class="counter" data-speed="2000" data-to="31">0</span>%</div>
                                <div class="metric-label">Churn Rate</div>
                            </div>
                        </div>
                    </div>

                    <div class="use-case-implementation">
                        <div class="implementation-header">
                            <i class="line-icon-Gear-2"></i>
                            <span>Implementation Approach</span>
                        </div>
                        <div class="implementation-steps">
                            <div class="step-item">
                                <span class="step-number">1</span>
                                <span class="step-text">Amazon purchase history analysis (24-month dataset)</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">2</span>
                                <span class="step-text">High-LTV audience segmentation (top 15% value customers)</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">3</span>
                                <span class="step-text">DSP lookalike audience creation & testing</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">4</span>
                                <span class="step-text">Value-based bid optimization with 2-week cycles</span>
                            </div>
                        </div>
                        <div class="implementation-context mt-3">
                            <small class="text-muted">
                                <strong>Client Profile:</strong> Home & Garden e-commerce ($22M annual revenue) •
                                <strong>Campaign Duration:</strong> 240 days •
                                <strong>Audience Size:</strong> 1.8M qualified prospects
                            </small>
                        </div>
                    </div>
                </div>

                <div class="use-case-visual">
                    <div class="visual-container w-100">
                        <!-- Case Study Chart/Graph Placeholder -->
                        <div class="case-study-chart chart-animate-in mb-4" data-chart="amazon" style="background: white; border-radius: 12px; padding: 12px; border: 1px solid rgba(0,0,0,0.06); box-shadow: 0 4px 20px rgba(0,0,0,0.04);">
                            <div class="chart-header mb-2">
                                <h6 class="fw-600 mb-1">Amazon DSP LTV Growth</h6>
                                <small class="text-muted">8-Month Campaign Performance</small>
                            </div>
                            <!-- SVG Chart - Amazon DSP LTV -->
                            <div class="svg-chart-container" style="height: 260px; background: white; border-radius: 8px; padding: 8px;">
                                <svg width="100%" height="100%" viewBox="0 0 450 200" class="ltv-chart-svg" preserveAspectRatio="xMidYMid meet">
                                    <!-- Gradients -->
                                    <defs>
                                        <linearGradient id="ltvGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#7369f1;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#e958a1;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="ltvAreaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#7369f1;stop-opacity:0.3" />
                                            <stop offset="100%" style="stop-color:#7369f1;stop-opacity:0.05" />
                                        </linearGradient>
                                    </defs>

                                    <!-- Grid lines -->
                                    <g class="grid-lines" stroke="#f0f0f0" stroke-width="1">
                                        <line x1="50" y1="30" x2="400" y2="30"></line>
                                        <line x1="50" y1="55" x2="400" y2="55"></line>
                                        <line x1="50" y1="80" x2="400" y2="80"></line>
                                        <line x1="50" y1="105" x2="400" y2="105"></line>
                                        <line x1="50" y1="130" x2="400" y2="130"></line>
                                        <line x1="50" y1="155" x2="400" y2="155"></line>
                                        <line x1="50" y1="180" x2="400" y2="180"></line>
                                    </g>

                                    <!-- Y-axis labels -->
                                    <g class="y-labels" fill="#666" font-size="11" font-family="system-ui">
                                        <text x="45" y="185" text-anchor="end">$0</text>
                                        <text x="45" y="160" text-anchor="end">$50</text>
                                        <text x="45" y="135" text-anchor="end">$100</text>
                                        <text x="45" y="110" text-anchor="end">$150</text>
                                        <text x="45" y="85" text-anchor="end">$200</text>
                                        <text x="45" y="60" text-anchor="end">$250</text>
                                        <text x="45" y="35" text-anchor="end">$300</text>
                                    </g>

                                    <!-- X-axis labels -->
                                    <g class="x-labels" fill="#666" font-size="10" font-family="system-ui">
                                        <text x="70" y="195" text-anchor="middle">Month 1</text>
                                        <text x="120" y="195" text-anchor="middle">Month 2</text>
                                        <text x="170" y="195" text-anchor="middle">Month 3</text>
                                        <text x="220" y="195" text-anchor="middle">Month 4</text>
                                        <text x="270" y="195" text-anchor="middle">Month 5</text>
                                        <text x="320" y="195" text-anchor="middle">Month 6</text>
                                        <text x="370" y="195" text-anchor="middle">Month 7</text>
                                        <text x="420" y="195" text-anchor="middle">Month 8</text>
                                    </g>

                                    <!-- Area under curve -->
                                    <path class="chart-area" d="M 70,165 L 120,155 L 170,140 L 220,120 L 270,95 L 320,70 L 370,50 L 420,35 L 420,180 L 70,180 Z"
                                          fill="url(#ltvAreaGradient)" opacity="0"></path>

                                    <!-- Main line -->
                                    <path class="chart-line" d="M 70,165 L 120,155 L 170,140 L 220,120 L 270,95 L 320,70 L 370,50 L 420,35"
                                          stroke="url(#ltvGradient)" stroke-width="4" fill="none"
                                          stroke-dasharray="1200" stroke-dashoffset="1200"></path>

                                    <!-- Data points -->
                                    <g class="data-points">
                                        <circle class="data-point" cx="70" cy="165" r="5" fill="#7369f1" opacity="0"></circle>
                                        <circle class="data-point" cx="120" cy="155" r="5" fill="#7369f1" opacity="0"></circle>
                                        <circle class="data-point" cx="170" cy="140" r="5" fill="#7369f1" opacity="0"></circle>
                                        <circle class="data-point" cx="220" cy="120" r="5" fill="#8b7cf2" opacity="0"></circle>
                                        <circle class="data-point" cx="270" cy="95" r="5" fill="#a58ff3" opacity="0"></circle>
                                        <circle class="data-point" cx="320" cy="70" r="5" fill="#c4a2f4" opacity="0"></circle>
                                        <circle class="data-point" cx="370" cy="50" r="5" fill="#e3b5f5" opacity="0"></circle>
                                        <circle class="data-point" cx="420" cy="35" r="5" fill="#e958a1" opacity="0"></circle>
                                    </g>

                                    <!-- Value labels -->
                                    <g class="value-labels" fill="#333" font-size="12" font-weight="600" font-family="system-ui">
                                        <text class="value-label" x="70" y="155" text-anchor="middle" opacity="0">$18</text>
                                        <text class="value-label" x="120" y="145" text-anchor="middle" opacity="0">$32</text>
                                        <text class="value-label" x="170" y="130" text-anchor="middle" opacity="0">$58</text>
                                        <text class="value-label" x="220" y="110" text-anchor="middle" opacity="0">$89</text>
                                        <text class="value-label" x="270" y="85" text-anchor="middle" opacity="0">$124</text>
                                        <text class="value-label" x="320" y="60" text-anchor="middle" opacity="0">$167</text>
                                        <text class="value-label" x="370" y="40" text-anchor="middle" opacity="0">$198</text>
                                        <text class="value-label" x="420" y="25" text-anchor="middle" opacity="0">$231</text>
                                    </g>
                                </svg>
                            </div>
                        </div>

                        <div class="platform-integration">
                            <div class="adzeta-ai-badge">
                                <img src="images/adzeta-logo-icon-white.png" alt="Adzeta AI" class="w-20 h-20">
                                <span>Adzeta AI</span>
                            </div>
                            <div class="outcome-indicator">
                                <div class="outcome-value">+$1.43M LTV Growth</div>
                                <div class="outcome-label">8-Month Campaign*</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Use Case 3: Cross-Platform Strategy -->
            <div class="use-case-container box-shadow-quadruple-large box-shadow-quadruple-large-hover mb-5" data-anime='{ "opacity": [0,1], "translateY": [40,0], "duration": 800, "delay": 600 }'>
                <div class="use-case-main">
                    <div class="use-case-header">
                        <div class="use-case-category">
                            <div class="category-icon">
                                <i class="line-icon-Network"></i>
                            </div>
                            <span class="category-label">Omnichannel Strategy</span>
                        </div>
                        <div class="use-case-platform">DV360 + Amazon DSP</div>
                    </div>

                    <div class="use-case-content">
                        <h4 class="use-case-title text-dark-gray">Unified Cross-Platform Intelligence</h4>
                        <p class="use-case-description">
                            Orchestrate programmatic campaigns across both DV360 and Amazon DSP with unified LTV intelligence. Eliminate channel silos and optimize for total customer value across the entire programmatic ecosystem.
                        </p>

                        <div class="use-case-metrics">
                            <div class="metric-item">
                                <div class="metric-value">+<span class="counter" data-speed="2000" data-to="187">0</span>%</div>
                                <div class="metric-label">Total ROAS</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">-<span class="counter" data-speed="2000" data-to="44">0</span>%</div>
                                <div class="metric-label">Wasted Spend</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value"><span class="counter" data-speed="2000" data-to="6">0</span>.<span class="counter" data-speed="2000" data-to="3">0</span>x</div>
                                <div class="metric-label">Efficiency Gain</div>
                            </div>
                        </div>
                    </div>

                    <div class="use-case-implementation">
                        <div class="implementation-header">
                            <i class="line-icon-Gear-2"></i>
                            <span>Implementation Approach</span>
                        </div>
                        <div class="implementation-steps">
                            <div class="step-item">
                                <span class="step-number">1</span>
                                <span class="step-text">Cross-platform data unification (DV360 + Amazon DSP)</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">2</span>
                                <span class="step-text">Unified LTV scoring system (92% cross-platform accuracy)</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">3</span>
                                <span class="step-text">Coordinated bidding strategy deployment</span>
                            </div>
                            <div class="step-item">
                                <span class="step-number">4</span>
                                <span class="step-text">Real-time performance synchronization</span>
                            </div>
                        </div>
                        <div class="implementation-context mt-3">
                            <small class="text-muted">
                                <strong>Client Profile:</strong> Premium electronics retailer ($45M annual revenue) •
                                <strong>Campaign Duration:</strong> 365 days •
                                <strong>Data Points:</strong> 4.2M customer interactions
                            </small>
                        </div>
                    </div>
                </div>

                <div class="use-case-visual">
                    <div class="visual-container w-100">
                        <!-- Case Study Chart/Graph Placeholder -->
                        <div class="case-study-chart chart-animate-in mb-4" data-chart="multiplatform" style="background: white; border-radius: 12px; padding: 12px; border: 1px solid rgba(0,0,0,0.06); box-shadow: 0 4px 20px rgba(0,0,0,0.04);">
                            <div class="chart-header mb-2">
                                <h6 class="fw-600 mb-1">Multi-Platform Revenue Impact</h6>
                                <small class="text-muted">12-Month Campaign Performance</small>
                            </div>
                            <!-- Multi-Platform Revenue Impact Chart -->
                            <div class="chart-container">
                                <svg class="performance-chart" viewBox="0 0 400 260" preserveAspectRatio="xMidYMid meet" style="height: 260px;"
                                    <defs>
                                        <linearGradient id="multiPlatformGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#7369f1" />
                                            <stop offset="50%" style="stop-color:#e958a1" />
                                            <stop offset="100%" style="stop-color:#ff5d74" />
                                        </linearGradient>
                                        <linearGradient id="multiAreaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#7369f1;stop-opacity:0.3" />
                                            <stop offset="100%" style="stop-color:#7369f1;stop-opacity:0.05" />
                                        </linearGradient>
                                    </defs>

                                    <!-- Grid lines -->
                                    <g class="chart-grid" stroke="rgba(0,0,0,0.1)" stroke-width="0.5">
                                        <line x1="50" y1="30" x2="350" y2="30"></line>
                                        <line x1="50" y1="65" x2="350" y2="65"></line>
                                        <line x1="50" y1="100" x2="350" y2="100"></line>
                                        <line x1="50" y1="135" x2="350" y2="135"></line>
                                        <line x1="50" y1="170" x2="350" y2="170"></line>
                                        <line x1="50" y1="205" x2="350" y2="205"></line>
                                        <line x1="50" y1="240" x2="350" y2="240"></line>
                                    </g>

                                    <!-- Y-axis labels -->
                                    <g class="y-labels" fill="#666" font-size="11" font-family="system-ui">
                                        <text x="45" y="245" text-anchor="end">$0M</text>
                                        <text x="45" y="210" text-anchor="end">$1M</text>
                                        <text x="45" y="175" text-anchor="end">$2M</text>
                                        <text x="45" y="140" text-anchor="end">$3M</text>
                                        <text x="45" y="105" text-anchor="end">$4M</text>
                                        <text x="45" y="70" text-anchor="end">$5M</text>
                                        <text x="45" y="35" text-anchor="end">$6M</text>
                                    </g>

                                    <!-- X-axis labels -->
                                    <g class="x-labels" fill="#666" font-size="10" font-family="system-ui">
                                        <text x="80" y="255" text-anchor="middle">Quarter 1</text>
                                        <text x="140" y="255" text-anchor="middle">Quarter 2</text>
                                        <text x="200" y="255" text-anchor="middle">Quarter 3</text>
                                        <text x="260" y="255" text-anchor="middle">Quarter 4</text>
                                        <text x="320" y="255" text-anchor="middle">Quarter 5</text>
                                    </g>

                                    <!-- Area under curve -->
                                    <path class="chart-area"
                                          d="M 80,215 L 140,190 L 200,155 L 260,110 L 320,60 L 320,240 L 80,240 Z"
                                          fill="url(#multiAreaGradient)"></path>

                                    <!-- Main line -->
                                    <path class="chart-line"
                                          d="M 80,215 L 140,190 L 200,155 L 260,110 L 320,60"
                                          stroke="url(#multiPlatformGradient)"
                                          stroke-width="4"
                                          fill="none"
                                          stroke-linecap="round"></path>

                                    <!-- Data points -->
                                    <g class="data-points">
                                        <circle cx="80" cy="215" r="5" fill="#7369f1" stroke="white" stroke-width="2"></circle>
                                        <circle cx="140" cy="190" r="5" fill="#9b7cf2" stroke="white" stroke-width="2"></circle>
                                        <circle cx="200" cy="155" r="5" fill="#e958a1" stroke="white" stroke-width="2"></circle>
                                        <circle cx="260" cy="110" r="5" fill="#ff5d74" stroke="white" stroke-width="2"></circle>
                                        <circle cx="320" cy="60" r="5" fill="#ff5d74" stroke="white" stroke-width="2"></circle>
                                    </g>

                                    <!-- Value labels -->
                                    <g class="value-labels" fill="#333" font-size="12" font-weight="600" font-family="system-ui">
                                        <text x="80" y="205" text-anchor="middle">$0.8M</text>
                                        <text x="140" y="180" text-anchor="middle">$1.4M</text>
                                        <text x="200" y="145" text-anchor="middle">$2.3M</text>
                                        <text x="260" y="100" text-anchor="middle">$3.6M</text>
                                        <text x="320" y="50" text-anchor="middle">$4.9M</text>
                                    </g>
                                </svg>
                            </div>
                        </div>

                        <div class="platform-integration">
                            <div class="adzeta-ai-badge">
                                <img src="images/adzeta-logo-icon-white.png" alt="Adzeta AI" class="w-20 h-20">
                                <span>Adzeta AI</span>
                            </div>
                            <div class="outcome-indicator">
                                <div class="outcome-value">+$2.91M Revenue</div>
                                <div class="outcome-label">12-Month Impact*</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- Verification footnote -->
        <div class="row justify-content-center mt-4">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <small class="text-muted">
                    <i class="fa-solid fa-certificate me-2" style="background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                    *Results from verified client campaigns • Mid-market e-commerce brands • Performance tracked over specified periods
                </small>
            </div>
        </div>
    </div>
</section>

<!-- Section 5: Proven Results in Programmatic Advertising -->
<section class="proven-results-redesign overflow-hidden position-relative pt-5">
    <!-- Subtle cohesive background -->
    <div class="position-absolute w-100 h-100" style="
        background: linear-gradient(135deg,
            rgba(248, 249, 250, 0.6) 0%,
            rgba(242, 240, 238, 0.4) 30%,
            rgba(249, 249, 255, 0.3) 70%,
            rgba(248, 249, 250, 0.6) 100%);
        z-index: 0;
    "></div>

    <!-- Subtle mesh texture -->
    <div class="position-absolute w-100 h-100" style="
        background:
        linear-gradient(90deg, rgba(143, 118, 245, 0.015) 1px, transparent 1px),
        linear-gradient(0deg, rgba(143, 118, 245, 0.015) 1px, transparent 1px);
        background-size: 32px 32px;
        opacity: 0.6;
        z-index: 0;
        pointer-events: none;
    "></div>
	
<div class="container position-relative" style="z-index: 1;">
    <div class="row justify-content-center mb-60px pt-5">
        <div class="col-xl-8 col-lg-9 col-md-10 text-center">
            <div class="scale-tag-container">
                <div class="scale-tag">CLIENT SUCCESS STORY</div>
            </div>
            <h3 class="alt-font fw-700 ls-minus-1px mb-20px text-dark-gray">Driving Profitable Growth for "Everloom Weavers"</h3>
            <p class="mb-0" style="color: #797a8c;">Discover how our predictive analytics platform transformed Everloom Weavers' programmatic advertising, turning a loss-making channel into a primary driver of sustainable, high-LTV customer acquisition.</p>
        </div>
    </div>

    <div class="professional-case-study-card mb-60px" data-anime='{ "opacity": [0,1], "translateY": [30,0], "duration": 800, "delay": 200 }'>
        <div class="case-study-professional-container">
            <a href="#" class="case-study-professional-header" style="text-decoration: none; color: inherit; display: block; transition: all 0.3s ease;">
                <div class="row align-items-center">
                    <div class="col-lg-8">
                        <div class="case-study-badges-container">
                            <span class="professional-case-badge">PREDICTIVE ANALYTICS</span>
                            <span class="professional-industry-badge">E-COMMERCE & RETAIL</span>
                        </div>
                        <h3 class="professional-case-title">Client: Everloom Weavers</h3>
                        <p class="professional-case-description">Leveraged Adzeta's predictive LTV modeling to overhaul bidding strategies on DV360 and Amazon DSP, shifting focus from costly single-purchase conversions to acquiring high-value, repeat customers, and achieving a 182% increase in channel ROI.</p>
                    </div>
                    <div class="col-lg-4 text-lg-end">
                        <div class="case-study-trust-indicator">
                            <div class="trust-badge">
                                <i class="feather icon-feather-shield-check"></i>
                                <span>Q3 2023 - Q1 2024 Data</span>
                            </div>
                            <div class="case-study-link-arrow" style="margin-top: 12px; opacity: 0.6; transition: all 0.3s ease;">
                                <svg width="12" height="12" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1.36406 10.3334L0.664062 9.63338L8.96406 1.33337H4.06406V0.333374H10.6641V6.93337H9.66406V2.03337L1.36406 10.3334Z" fill="currentColor"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </a>

            <div class="ios-campaign-overview">
                <div class="key-metrics-row">
                    <div class="metric-item">
                        <div class="metric-value">9 Months</div>
                        <div class="metric-label">Partnership Duration</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">$850K</div>
                        <div class="metric-label">Managed Ad Spend</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">92%</div>
                        <div class="metric-label">LTV Prediction Accuracy</div>
                    </div>
                    <div class="metric-item highlight-metric">
                        <div class="metric-value">182%</div>
                        <div class="metric-label">ROI Uplift</div>
                        <div class="metric-badge">From -24% to +158%</div>
                    </div>
                </div>

                <div class="platform-results-section">
                    <h5 class="section-title">The Challenge: Scaling with Negative ROI</h5>
                    <div class="challenge-context">
                        <p style="color: #797a8c; font-size: 14px; margin-bottom: 24px;">
                            Everloom Weavers, a direct-to-consumer brand for premium, sustainable home textiles, was caught in a common programmatic trap. Their campaigns on DV360 and Amazon DSP were optimized for immediate conversions, attracting discount-driven, one-time buyers. This led to a high CPA and a channel-wide ROI of -24%, making it impossible to scale profitably.
                        </p>
                    </div>
                    <div class="platform-results-grid">
                        <div class="platform-result">
                            <div class="platform-header">
                                <div class="platform-name">DV360 Custom Bidding</div>
                                <div class="platform-type">Prospecting & Retargeting</div>
                            </div>
                            <div class="platform-performance">
                                <div class="performance-metric">
                                    <span class="perf-value">-31% → +145%</span>
                                    <span class="perf-label">ROAS Improvement</span>
                                </div>
                                <div class="performance-metric">
                                    <span class="perf-value">68%</span>
                                    <span class="perf-label">Higher-Value Audience Reach</span>
                                </div>
                            </div>
                        </div>

                        <div class="platform-result">
                            <div class="platform-header">
                                <div class="platform-name">Amazon DSP</div>
                                <div class="platform-type">In-Market Audiences</div>
                            </div>
                            <div class="platform-performance">
                                <div class="performance-metric">
                                    <span class="perf-value">-17% → +171%</span>
                                    <span class="perf-label">ROAS Improvement</span>
                                </div>
                                <div class="performance-metric">
                                    <span class="perf-value">3.8x</span>
                                    <span class="perf-label">Increase in Repeat Purchase Rate</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="case-study-results-section">
                <h4 class="section-subtitle">A Strategic Shift to Profitability</h4>
                <div class="transformation-story" style="margin-bottom: 32px;">
                    <p style="color: #797a8c; font-size: 14px; font-style: italic;">
                        "Before Adzeta, our programmatic efforts were a sunk cost. We were acquiring customers at a loss. The ability to predict future value and bid accordingly has fundamentally changed our growth trajectory. We now acquire customers who strengthen our brand and our bottom line."
                        <span style="font-weight: 600;">— Sarah Chen, Head of Growth, Everloom Weavers</span>
                    </p>
                </div>
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <div class="result-metric-card">
                            <div class="result-number">68%</div>
                            <div class="result-label">Reduction in Spend on Low-Value Audiences</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="result-metric-card">
                            <div class="result-number">92%</div>
                            <div class="result-label">Accuracy in 90-Day LTV Prediction</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="result-metric-card">
                            <div class="result-number">3.8x</div>
                            <div class="result-label">Higher Repeat Purchase Rate</div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="result-metric-card">
                            <div class="result-number">158%</div>
                            <div class="result-label">Net Return on Investment (ROI)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

	
	
	
	
    </div>
</section>

 <!-- Section PROVEN RESULTS -->
<section class="overflow-hidden position-relative">
	
    <div class="container position-relative" style="z-index: 1;">
       

      


        <!-- Professional Implementation Roadmap -->
        <div class="professional-implementation-roadmap" data-anime='{ "opacity": [0,1], "translateY": [30,0], "duration": 800, "delay": 400 }'>
            <div class="roadmap-header text-center mb-60px">
                <div class="scale-tag-container mb-3">
                    <div class="scale-tag">IMPLEMENTATION ROADMAP</div>
                </div>
                <h4 class="fw-700 text-dark-gray mb-3">Strategic Implementation & Value Realization</h4>
                <p style="color: #797a8c; max-width: 600px; margin: 0 auto;">A proven methodology that transforms your programmatic advertising from basic campaigns to AI-driven profit optimization, with measurable business impact at every stage.</p>
            </div>

            <div class="implementation-phases-container">
                <!-- Phase 1: Foundation & Integration -->
                <div class="implementation-phase" data-anime='{ "opacity": [0,1], "translateX": [-30,0], "duration": 800, "delay": 200 }'>
                    <div class="phase-content-wrapper">
                        <div class="phase-indicator">
                            <div class="phase-icon-container phase-foundation">
                                <i class="fa-solid fa-rocket"></i>
                            </div>
                            <div class="phase-duration">Weeks 1-4</div>
                        </div>
                        <div class="phase-details">
                            <h5 class="phase-title">Foundation & Integration</h5>
                            <p class="phase-description">Complete platform setup, data pipeline integration, and initial LTV model deployment across DV360 and Amazon DSP.</p>
                            <div class="phase-deliverables">
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Custom bidding algorithm deployment</span>
                                </div>
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Real-time data integration setup</span>
                                </div>
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Performance baseline establishment</span>
                                </div>
                            </div>
                            <div class="phase-outcome">
                                <div class="outcome-metric">
                                    <span class="metric-value">+28%</span>
                                    <span class="metric-label">ROAS Improvement</span>
                                </div>
                                <div class="outcome-insight">Initial optimization gains from LTV-based bidding</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Phase Connector -->
                <div class="phase-connector d-none d-lg-block"></div>

                <!-- Phase 2: Learning & Optimization -->
                <div class="implementation-phase" data-anime='{ "opacity": [0,1], "translateX": [30,0], "duration": 800, "delay": 400 }'>
                    <div class="phase-content-wrapper reverse">
                        <div class="phase-indicator">
                            <div class="phase-icon-container phase-learning">
                                <i class="fa-solid fa-brain"></i>
                            </div>
                            <div class="phase-duration">Months 2-4</div>
                        </div>
                        <div class="phase-details">
                            <h5 class="phase-title">AI Learning & Refinement</h5>
                            <p class="phase-description">Machine learning algorithms analyze customer behavior patterns, refine LTV predictions, and optimize bidding strategies based on real performance data.</p>
                            <div class="phase-deliverables">
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Advanced audience segmentation</span>
                                </div>
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Predictive model enhancement</span>
                                </div>
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Cross-platform optimization</span>
                                </div>
                            </div>
                            <div class="phase-outcome">
                                <div class="outcome-metric">
                                    <span class="metric-value">+94%</span>
                                    <span class="metric-label">Revenue Growth</span>
                                </div>
                                <div class="outcome-insight">AI-driven optimization delivers exponential returns</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Phase Connector -->
                <div class="phase-connector d-none d-lg-block"></div>

                <!-- Phase 3: Scale & Advanced Features -->
                <div class="implementation-phase" data-anime='{ "opacity": [0,1], "translateX": [-30,0], "duration": 800, "delay": 600 }'>
                    <div class="phase-content-wrapper">
                        <div class="phase-indicator">
                            <div class="phase-icon-container phase-scale">
                                <i class="fa-solid fa-chart-line"></i>
                            </div>
                            <div class="phase-duration">Months 5-8</div>
                        </div>
                        <div class="phase-details">
                            <h5 class="phase-title">Scale & Advanced Optimization</h5>
                            <p class="phase-description">Full-scale deployment with advanced features including dynamic creative optimization, lookalike modeling, and multi-touchpoint attribution.</p>
                            <div class="phase-deliverables">
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Dynamic creative optimization</span>
                                </div>
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Advanced lookalike modeling</span>
                                </div>
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Multi-platform attribution</span>
                                </div>
                            </div>
                            <div class="phase-outcome">
                                <div class="outcome-metric">
                                    <span class="metric-value">+156%</span>
                                    <span class="metric-label">Profit Increase</span>
                                </div>
                                <div class="outcome-insight">Advanced features unlock maximum profitability</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Phase Connector -->
                <div class="phase-connector d-none d-lg-block"></div>

                <!-- Phase 4: Peak Performance & Continuous Innovation -->
                <div class="implementation-phase" data-anime='{ "opacity": [0,1], "translateX": [30,0], "duration": 800, "delay": 800 }'>
                    <div class="phase-content-wrapper reverse">
                        <div class="phase-indicator">
                            <div class="phase-icon-container phase-peak">
                                <i class="fa-solid fa-trophy"></i>
                            </div>
                            <div class="phase-duration">Months 9-12</div>
                        </div>
                        <div class="phase-details">
                            <h5 class="phase-title">Peak Performance & Innovation</h5>
                            <p class="phase-description">Achieve maximum ROI with continuous model refinement, emerging platform integration, and strategic expansion opportunities.</p>
                            <div class="phase-deliverables">
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Continuous model optimization</span>
                                </div>
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>New platform integration</span>
                                </div>
                                <div class="deliverable-item">
                                    <i class="fa-solid fa-check-circle"></i>
                                    <span>Strategic expansion planning</span>
                                </div>
                            </div>
                            <div class="phase-outcome">
                                <div class="outcome-metric">
                                    <span class="metric-value">+218%</span>
                                    <span class="metric-label">Total ROI</span>
                                </div>
                                <div class="outcome-insight">Peak performance with sustainable growth trajectory</div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Success Guarantee 
            <div class="implementation-guarantee text-center mt-60px" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 800, "delay": 1000 }'>
                <div class="guarantee-card">
                    <div class="guarantee-icon">
                        <i class="fa-solid fa-shield-check"></i>
                    </div>
                    <h6 class="guarantee-title">Performance Guarantee</h6>
                    <p class="guarantee-text">We guarantee measurable improvement in your programmatic performance within the first 60 days, or we'll work for free until you see results.</p>
                </div>
            </div>-->
        </div>
    </div>
</section>

<!-- Section 6: Adzeta Expertise for Programmatic -->
<section class="proven-results-section overflow-hidden position-relative pb-0">
    <!-- Light section background with subtle gradient that matches previous sections -->
<div class="light-gradient-container position-absolute w-100 h-100"
        style="background: linear-gradient(to bottom, #eae8e6 0%, #ffffff 100%); z-index: 0;"></div>
    <div class="light-accent-gradient position-absolute w-100 h-100"
        style="background:
        radial-gradient(circle at center, rgba(233,88,161,0.05) 0%, rgba(255,255,255,0) 70%),
        radial-gradient(circle at bottom right, rgba(143,118,245,0.03) 0%, rgba(255,255,255,0) 70%);
        z-index: 0;"></div>
    <div class="container position-relative" style="z-index: 1;">
        <div class="row justify-content-center mb-0 mt-60px">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="scale-tag-container">
                    <div class="scale-tag">YOUR STRATEGIC PARTNER</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Expertise Driving Your Programmatic Success</span></h3>
                <p>Achieving peak profitability in programmatic advertising requires more than just technology – it demands deep expertise and a strategic partnership. Adzeta brings you both, ensuring our Predictive AI translates into real-world success for your DV360 and Amazon DSP campaigns.</p>
            </div>
        </div>
        <!-- Ad Platform Blocks with Central Image -->
        <div class="ad-networks-wrapper position-relative" style="height: 1000px;">
            <!-- Central Image -->
            <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: -1;">
                <img class="central-image" src="images/bg-programmatic-platforms.png" alt="Programmatic Networks Integration" style="max-width: 550px; animation: fadeInAnimation 1.5s ease-out forwards, floatAnimation 6s ease-in-out infinite 1.5s;">
            </div>
            <!-- Block 1: DV360 Expertise - Top Left -->
            <div class="ad-platform-card position-absolute" style="top: 100px; left: 20px;" data-anime='{ "translateY": [-30, 0], "opacity": [0,1], "duration": 800, "delay": 200 }'>
                <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                    <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                    <div class="d-flex align-items-center mb-20px position-relative">
                        <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 80px; height: 80px;">
                            <img src="images/DV360.png" alt="DV360" height="80">
                        </div>
                        <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">DV360 Custom Bidding</h3>
                    </div>
                    <p class="mb-20px position-relative fs-15 lh-24 text-left">Deep expertise in implementing and optimizing DV360 custom bidding strategies with predictive LTV signals for maximum programmatic efficiency.</p>
                </div>
            </div>
            <!-- Block 2: Amazon DSP - Top Right -->
            <div class="ad-platform-card position-absolute" style="top: 180px; right: 20px;" data-anime='{ "translateY": [-30, 0], "opacity": [0,1], "duration": 800, "delay": 300 }'>
                <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                    <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                    <div class="d-flex align-items-center mb-20px position-relative">
                        <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 80px; height: 80px;">
                            <img src="images/pa-amazon-ads.png" alt="Amazon DSP" height="80" >
                        </div>
                        <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Amazon DSP Mastery</h3>
                    </div>
                    <p class="mb-20px position-relative fs-15 lh-24 text-left">Specialized knowledge in leveraging Amazon's unique data ecosystem for maximum LTV-driven performance and customer acquisition.</p>
                </div>
            </div>
            <!-- Block 3: LTV AI Models - Bottom Left -->
            <div class="ad-platform-card position-absolute" style="bottom: 180px; left: 20px;" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 400 }'>
                <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                    <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                    <div class="d-flex align-items-center mb-20px position-relative">
                        <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 80px; height: 80px;">
                            <img src="images/precision-ai.png" alt="LTV AI Models" height="80" style="filter: drop-shadow(0px 2px 4px rgba(0,0,0,0.1));">
                        </div>
                        <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Advanced LTV Modeling</h3>
                    </div>
                    <p class="mb-20px position-relative fs-15 lh-24 text-left">Cutting-edge machine learning models specifically designed for programmatic environments and e-commerce customer behavior prediction.</p>
                </div>
            </div>
            <!-- Block 4: Strategic Partnership - Bottom Right -->
            <div class="ad-platform-card position-absolute" style="bottom: 100px; right: 10px;" data-anime='{ "translateY": [30, 0], "opacity": [0,1], "duration": 800, "delay": 500 }'>
                <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                    <div class="platform-gradient-overlay position-absolute" style="background: #ffffff; top: 0; left: 0; right: 0; bottom: 0; z-index: 0;"></div>
                    <div class="d-flex align-items-center mb-20px position-relative">
                        <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 80px; height: 80px;">
                            <img src="images/pa-st-partner.png" alt="Strategic Partner" height="80" >
                        </div>
                        <h3 class="alt-font fw-600 fs-20 mb-0 position-relative">Your Strategic Partner</h3>
                    </div>
                    <p class="mb-20px position-relative fs-15 lh-24 text-left">Ongoing analysis, transparent reporting, and collaborative support to ensure continuous programmatic advertising success.</p>
                </div>
            </div>
        </div>
        <!-- Mobile Version (will be shown only on small screens) -->
        <div class="d-block d-lg-none">
            <div class="text-center mb-30px">
                <img class="central-image" src="images/bg-programmatic-platforms.png" alt="Programmatic Networks Integration" style="max-width: 320px;">
            </div>
            <div class="row">
                <div class="col-md-6 col-12 mb-30px">
                    <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                        <div class="d-flex align-items-center mb-20px">
                            <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                <img src="images/pa-google-ads.png" alt="DV360" height="40">
                            </div>
                            <h3 class="alt-font fw-600 fs-20 mb-0">DV360 Custom Bidding</h3>
                        </div>
                        <p class="mb-20px text-left">Deep expertise in implementing and optimizing DV360 custom bidding strategies with predictive LTV signals for maximum programmatic efficiency.</p>
                    </div>
                </div>
                <div class="col-md-6 col-12 mb-30px">
                    <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                        <div class="d-flex align-items-center mb-20px">
                            <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                <img src="images/pa-amazon.png" alt="Amazon DSP" height="40">
                            </div>
                            <h3 class="alt-font fw-600 fs-20 mb-0">Amazon DSP Mastery</h3>
                        </div>
                        <p class="mb-20px text-left">Specialized knowledge in leveraging Amazon's unique data ecosystem for maximum LTV-driven performance and customer acquisition.</p>
                    </div>
                </div>
                <div class="col-md-6 col-12 mb-30px">
                    <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                        <div class="d-flex align-items-center mb-20px">
                            <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                <img src="images/precision-ai.png" alt="LTV AI Models" height="40">
                            </div>
                            <h3 class="alt-font fw-600 fs-20 mb-0">Advanced LTV Modeling</h3>
                        </div>
                        <p class="mb-20px text-left">Cutting-edge machine learning models specifically designed for programmatic environments and e-commerce customer behavior prediction.</p>
                    </div>
                </div>
                <div class="col-md-6 col-12 mb-30px">
                    <div class="card-inner border-radius-24px bg-white p-30px position-relative overflow-hidden box-shadow-double-large ">
                        <div class="d-flex align-items-center mb-20px">
                            <div class="platform-icon-container d-flex align-items-center justify-content-center me-15px" style="width: 60px; height: 60px;">
                                <img src="images/pa-st-partner.png" alt="Strategic Partner" height="40">  
                            </div>
                            <h3 class="alt-font fw-600 fs-20 mb-0">Your Strategic Partner</h3>
                        </div>
                        <p class="mb-20px text-left">Ongoing analysis, transparent reporting, and collaborative support to ensure continuous programmatic advertising success.</p>
                    </div>
                </div>
            </div>
        </div>

   



        <div class="row justify-content-center google-value-challenges">
            <div class="col-xl-8 col-lg-9 col-md-10 text-center">
                <div class="scale-tag-container">
                    <div class="scale-tag">GET STARTED</div>
                </div>
                <h3 class="alt-font fw-700 ls-minus-1px mb-20px"><span class="modern-heading-gradient">Ready to Maximize Your Programmatic Ad Profit?</span></h3>
                <p class="mb-5">Discover the untapped LTV potential within your DV360 and Amazon Ads campaigns. Get a free analysis from Adzeta's experts and see how Predictive LTV can transform your programmatic results.</p>

                <!-- Benefits card -->
                <div class="unified-value-card mb-50px box-shadow-extra-large" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 800, "delay": 200 }'>
                    <h4 class="fw-700 mb-4">What You'll Get in Your Free Analysis:</h4>
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-start">
                                <div class="rounded-circle d-inline-flex align-items-center justify-content-center text-white me-3 flex-shrink-0" style="width: 32px; height: 32px; background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);">
                                    <i class="fa-solid fa-check" style="font-size: 14px;"></i>
                                </div>
                                <div>
                                    <h6 class="fw-600 mb-1">DV360 Opportunity Assessment</h6>
                                    <small class="text-muted">Custom bidding optimization potential</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start">
                                <div class="rounded-circle d-inline-flex align-items-center justify-content-center text-white me-3 flex-shrink-0" style="width: 32px; height: 32px; background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);">
                                    <i class="fa-solid fa-check" style="font-size: 14px;"></i>
                                </div>
                                <div>
                                    <h6 class="fw-600 mb-1">Amazon DSP LTV Analysis</h6>
                                    <small class="text-muted">Audience value optimization insights</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start">
                                <div class="rounded-circle d-inline-flex align-items-center justify-content-center text-white me-3 flex-shrink-0" style="width: 32px; height: 32px; background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);">
                                    <i class="fa-solid fa-check" style="font-size: 14px;"></i>
                                </div>
                                <div>
                                    <h6 class="fw-600 mb-1">Programmatic ROI Projection</h6>
                                    <small class="text-muted">Expected performance improvements</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start">
                                <div class="rounded-circle d-inline-flex align-items-center justify-content-center text-white me-3 flex-shrink-0" style="width: 32px; height: 32px; background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%);">
                                    <i class="fa-solid fa-check" style="font-size: 14px;"></i>
                                </div>
                                <div>
                                    <h6 class="fw-600 mb-1">Implementation Roadmap</h6>
                                    <small class="text-muted">Step-by-step integration plan</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex flex-wrap justify-content-center gap-3" data-anime='{ "opacity": [0,1], "translateY": [20,0], "duration": 800, "delay": 400 }'>
                    <a href="free-ad-audit.php" class="btn btn-extra-large btn-hover-animation box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-gradient-pink-orange btn-rounded fw-600 alt-font">
                        <span>
                            <span class="btn-text">Request Free Programmatic Analysis</span>
                            <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
                        </span>
                    </a>
                    <a href="tel:+16475727784" class="btn btn-extra-large box-shadow-medium-bottom box-shadow-quadruple-large-hover btn-transparent-dark-gray btn-rounded border-1 fw-600 alt-font">
                        <span>
                            <span class="btn-text">Call Our Programmatic Experts</span>
                            <span class="btn-icon"><i class="fa-solid fa-phone"></i></span>
                        </span>
                    </a>
                </div>

                <div class="mt-4" data-anime='{ "opacity": [0,1], "duration": 800, "delay": 600 }'>
                    <small class="text-muted">
                        <i class="fa-solid fa-shield-alt me-2" style="background: linear-gradient(135deg, #e958a1 0%, #ff5d74 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;"></i>
                        No commitment required • Free consultation • Expert programmatic guidance
                    </small>
                </div>
            </div>
        </div>
   </div>
</section>

<style>
/* Additional styles for enhanced visual effects */
.bg-gradient-purple-blue {
    background: linear-gradient(135deg, #e958a1, #ff5d74) !important;
}

.text-gradient-purple-blue {
    background: linear-gradient(135deg, #e958a1, #ff5d74);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-white-transparent {
    color: rgba(255, 255, 255, 0.8);
}

/* Enhanced animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.platform-card.adzeta-ai {
    animation: float 3s ease-in-out infinite;
}

/* iOS-Inspired Chart Styles */
.ios-chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 1px 0 rgba(255, 255, 255, 0.5) inset,
        0 -1px 0 rgba(0, 0, 0, 0.05) inset;
    position: relative;
    overflow: hidden;
}

.ios-chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.chart-status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    font-weight: 500;
    color: #8E8E93;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e958a1, #ff5d74);
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.2); }
}

.interactive-chart-wrapper {
    position: relative;
    height: 220px;
    margin: 16px 0;
    border-radius: 12px;
    overflow: hidden;
}

.chart-background {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(249, 250, 251, 0.8) 0%,
        rgba(243, 244, 246, 0.6) 50%,
        rgba(249, 250, 251, 0.8) 100%);
    border-radius: 12px;
}

.chart-grid {
    position: absolute;
    inset: 0;
    background-image:
        linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.5;
}

.ios-chart-svg {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
}

.ios-data-point {
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.ios-data-point:hover {
    r: 8 !important;
    filter: url(#glow) !important;
}

.chart-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    pointer-events: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 10;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.chart-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.tooltip-month {
    font-size: 10px;
    opacity: 0.8;
    margin-bottom: 2px;
}

.tooltip-value {
    font-size: 14px;
    font-weight: 700;
    background: linear-gradient(135deg, #e958a1, #ff5d74);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.chart-insights {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    color: #6B7280;
    padding: 6px 12px;
    background: rgba(243, 244, 246, 0.5);
    border-radius: 20px;
    border: 1px solid rgba(229, 231, 235, 0.5);
}

.insight-icon {
    font-size: 14px;
}

/* Simple Performance Chart Styles */
.case-study-chart {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-study-chart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.08) !important;
}

.chart-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
}

.performance-chart {
    width: 100%;
    height: auto;
    max-height: 300px;
}

/* Initial states - hidden on desktop, visible on mobile */
.chart-area {
    opacity: 0;
}

.chart-line {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
}

.data-points circle {
    transform-origin: center;
    opacity: 0;
    transform: scale(0);
}

.value-labels text {
    opacity: 0;
    transform: translateY(10px);
}

/* Mobile override - show charts immediately */
@media (max-width: 768px) {
    .chart-area {
        opacity: 1 !important;
        animation: none !important;
    }

    .chart-line {
        stroke-dasharray: none !important;
        stroke-dashoffset: 0 !important;
        animation: none !important;
    }

    .data-points circle {
        opacity: 1 !important;
        transform: scale(1) !important;
        animation: none !important;
    }

    .value-labels text {
        opacity: 1 !important;
        transform: translateY(0) !important;
        animation: none !important;
    }

    /* Override any animation delays */
    .case-study-chart.animate .chart-area,
    .case-study-chart.animate .chart-line,
    .case-study-chart.animate .data-points circle,
    .case-study-chart.animate .value-labels text {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }
}

/* Animated states - when .animate class is added */
.case-study-chart.animate .chart-area {
    animation: fillArea 2s ease-out 0.5s both;
}

.case-study-chart.animate .chart-line {
    animation: drawLine 2s ease-out both;
}

.case-study-chart.animate .data-points circle {
    animation: popIn 0.5s ease-out both;
}

.case-study-chart.animate .data-points circle:nth-child(1) { animation-delay: 2.2s; }
.case-study-chart.animate .data-points circle:nth-child(2) { animation-delay: 2.4s; }
.case-study-chart.animate .data-points circle:nth-child(3) { animation-delay: 2.6s; }
.case-study-chart.animate .data-points circle:nth-child(4) { animation-delay: 2.8s; }
.case-study-chart.animate .data-points circle:nth-child(5) { animation-delay: 3.0s; }
.case-study-chart.animate .data-points circle:nth-child(6) { animation-delay: 3.2s; }

.case-study-chart.animate .value-labels text {
    animation: fadeInUp 0.5s ease-out both;
}

.case-study-chart.animate .value-labels text:nth-child(1) { animation-delay: 2.4s; }
.case-study-chart.animate .value-labels text:nth-child(2) { animation-delay: 2.6s; }
.case-study-chart.animate .value-labels text:nth-child(3) { animation-delay: 2.8s; }
.case-study-chart.animate .value-labels text:nth-child(4) { animation-delay: 3.0s; }
.case-study-chart.animate .value-labels text:nth-child(5) { animation-delay: 3.2s; }
.case-study-chart.animate .value-labels text:nth-child(6) { animation-delay: 3.4s; }

@keyframes drawLine {
    to { stroke-dashoffset: 0; }
}

@keyframes fillArea {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes popIn {
    from {
        transform: scale(0);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Chart hover effects */
.data-points circle {
    transition: all 0.3s ease;
    cursor: pointer;
}

.data-points circle:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 4px 8px rgba(233, 88, 161, 0.3));
}

/* Mobile responsiveness for charts */
@media (max-width: 768px) {
    .case-study-chart {
        padding: 8px !important;
        margin-bottom: 20px !important;
    }

    .chart-container {
        width: 100%;
        overflow-x: auto;
        overflow-y: visible;
    }

    .performance-chart {
        width: 100%;
        height: auto !important;
        min-height: 200px;
        max-width: none;
    }

    .svg-chart-container {
        height: 200px !important;
        padding: 4px !important;
        overflow: visible;
    }

    .chart-header {
        margin-bottom: 8px !important;
    }

    .chart-header h6 {
        font-size: 14px;
    }

    .chart-header small {
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .case-study-chart {
        padding: 6px !important;
        border-radius: 8px !important;
    }

    .performance-chart {
        min-height: 180px;
    }

    .svg-chart-container {
        height: 180px !important;
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .ios-chart-container {
        padding: 16px;
        border-radius: 16px;
    }

    .interactive-chart-wrapper {
        height: 200px;
    }

    .chart-insights {
        gap: 12px;
    }

    .insight-item {
        font-size: 11px;
        padding: 4px 8px;
    }

    .ios-chart-svg .ios-y-labels,
    .ios-chart-svg .ios-x-labels {
        font-size: 9px;
    }

    .ios-chart-svg .ios-value-labels {
        font-size: 10px;
    }
}

/* Improved hover effects */
.challenge-card:hover .challenge-icon,
.use-case-card:hover .use-case-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(233, 88, 161, 0.3);
}

.solution-card:hover .solution-number {
    transform: scale(1.2);
    box-shadow: 0 8px 25px rgba(233, 88, 161, 0.4);
}

/* Mobile optimizations */
@media (max-width: 576px) {
    .section-heading {
        font-size: 1.75rem;
    }

    .hero-subtitle {
        font-size: 16px;
    }

    .challenge-card,
    .solution-card,
    .use-case-card {
        padding: 24px;
    }

    .platform-integration {
        margin-top: 40px;
    }
}

/* Campaign Overview Mobile Responsive */
@media (max-width: 768px) {
    .campaign-details-container {
        padding-right: 0;
        margin-bottom: 30px;
    }

    .campaign-stats-row {
        flex-direction: column;
        gap: 20px;
        padding: 20px 16px;
    }

    .stat-item {
        justify-content: center;
        text-align: center;
    }

    .stat-divider {
        display: none;
    }

    .stat-value {
        font-size: 18px;
    }

    .platform-cards {
        gap: 12px;
    }

    .platform-card {
        padding: 16px;
    }

    .platform-metrics {
        gap: 16px;
    }

    .metric-number {
        font-size: 14px;
    }

    .integration-title {
        font-size: 14px;
        margin-bottom: 16px;
    }
}
</style>

<script>
// Simple Chart Animations - No Dependencies
document.addEventListener('DOMContentLoaded', function() {
    // Simple intersection observer for CSS animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2
    });

    // Observe chart sections
    document.querySelectorAll('.case-study-chart').forEach(chart => {
        observer.observe(chart);
    });
});
</script>

<?php include 'footer.php'; ?>