<?php
/**
 * Single Post Template
 * Variables available: $post, $related_posts, $performance_info
 */
?>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- Back to Blog -->
        <a href="blog-templated.php" class="back-link mb-4">
            <i class="fas fa-arrow-left me-2"></i>Back to Blog
        </a>
        
        <!-- Performance indicator -->
        <?php if (isset($show_debug) && $show_debug): ?>
            <div class="alert alert-info">
                <small>
                    <strong>Template Performance:</strong> 
                    <?= e($performance_info ?? 'Template rendered') ?> | 
                    Post: <?= e($post['title'] ?? 'Unknown') ?> | 
                    Template: single-post.php
                </small>
            </div>
        <?php endif; ?>
        
        <!-- Post Header -->
        <article class="mb-5">
            <!-- Post Meta -->
            <div class="text-muted mb-3">
                <?php if (!empty($post['category_name']) && $post['category_name'] !== 'Uncategorized'): ?>
                    <span class="badge bg-primary me-2"><?= e($post['category_name']) ?></span>
                <?php endif; ?>
                
                <i class="fas fa-calendar me-1"></i>
                <?= $template->formatDate($post['published_at']) ?>
                
                <?php if (!empty($post['reading_time']) && $post['reading_time'] > 0): ?>
                    <span class="mx-2">•</span>
                    <i class="fas fa-clock me-1"></i>
                    <?= e($post['reading_time']) ?> min read
                <?php endif; ?>
                
                <?php if (!empty($post['view_count'])): ?>
                    <span class="mx-2">•</span>
                    <i class="fas fa-eye me-1"></i>
                    <?= number_format($post['view_count']) ?> views
                <?php endif; ?>
            </div>
            
            <!-- Post Title -->
            <h1 class="display-5 mb-4"><?= e($post['title'] ?? 'Untitled Post') ?></h1>
            
            <!-- Post Excerpt -->
            <?php if (!empty($post['excerpt'])): ?>
                <p class="lead text-muted mb-4"><?= e($post['excerpt']) ?></p>
            <?php endif; ?>
            
            <!-- Author Info -->
            <div class="d-flex align-items-center mb-4">
                <img src="images/case-studies/Natalie-Brooks-adzeta.jpg" 
                     class="rounded-circle me-3" 
                     width="50" height="50" 
                     alt="<?= e($post['author_name'] ?? 'Author') ?>">
                <div>
                    <div class="fw-bold"><?= e($post['author_name'] ?? 'Unknown Author') ?></div>
                    <small class="text-muted">Author</small>
                </div>
            </div>
            
            <!-- Featured Image -->
            <?php if (!empty($post['featured_image'])): ?>
                <img src="<?= e($post['featured_image']) ?>" 
                     alt="<?= e($post['title']) ?>" 
                     class="img-fluid rounded mb-4 w-100">
            <?php endif; ?>
            
            <!-- Post Content -->
            <div class="post-content">
                <?= $template->renderPostContent($post['content'] ?? '') ?>
            </div>
            
            <!-- Post Tags (if available) -->
            <?php if (!empty($post['tags'])): ?>
                <div class="mt-4">
                    <h6>Tags:</h6>
                    <?php foreach ($post['tags'] as $tag): ?>
                        <span class="badge bg-secondary me-1"><?= e($tag) ?></span>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </article>
        
        <!-- Author Bio -->
        <div class="author-section">
            <div class="row align-items-center">
                <div class="col-auto">
                    <img src="images/case-studies/Natalie-Brooks-adzeta.jpg" 
                         class="rounded-circle" 
                         width="80" height="80" 
                         alt="<?= e($post['author_name'] ?? 'Author') ?>">
                </div>
                <div class="col">
                    <h5 class="mb-1"><?= e($post['author_name'] ?? 'Unknown Author') ?></h5>
                    <p class="text-muted mb-0">
                        <?= e($post['author_bio'] ?? 'Content creator and digital marketing expert.') ?>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Social Sharing -->
        <div class="card mt-4">
            <div class="card-body">
                <h6 class="card-title">Share this post:</h6>
                <div class="d-flex gap-2">
                    <a href="https://twitter.com/intent/tweet?text=<?= urlencode($post['title']) ?>&url=<?= urlencode($current_url) ?>" 
                       target="_blank" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fab fa-twitter"></i> Twitter
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?= urlencode($current_url) ?>" 
                       target="_blank" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fab fa-facebook"></i> Facebook
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?= urlencode($current_url) ?>" 
                       target="_blank" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fab fa-linkedin"></i> LinkedIn
                    </a>
                    <button onclick="copyToClipboard('<?= e($current_url) ?>')" 
                            class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-link"></i> Copy Link
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Related Posts -->
        <?php if (!empty($related_posts)): ?>
            <div class="mt-5">
                <h3 class="mb-4">Related Articles</h3>
                <div class="row">
                    <?php foreach ($related_posts as $related): ?>
                        <div class="col-md-4 mb-4">
                            <div class="related-post h-100">
                                <?php if (!empty($related['featured_image'])): ?>
                                    <img src="<?= e($related['featured_image']) ?>" 
                                         alt="<?= e($related['title']) ?>" 
                                         class="img-fluid rounded mb-3" 
                                         style="height: 150px; width: 100%; object-fit: cover;">
                                <?php endif; ?>
                                
                                <h6>
                                    <a href="post-templated.php?slug=<?= urlencode($related['slug']) ?>" 
                                       class="text-decoration-none">
                                        <?= e($related['title']) ?>
                                    </a>
                                </h6>
                                
                                <p class="small text-muted">
                                    <?= e($template->truncate($related['excerpt'] ?? '', 100)) ?>
                                </p>
                                
                                <small class="text-muted">
                                    <?= $template->formatDate($related['published_at']) ?>
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Navigation -->
        <div class="d-flex justify-content-between mt-5">
            <a href="blog-templated.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Blog
            </a>
            
            <?php if (isLoggedIn()): ?>
                <a href="adzeta-admin/index.php?view=posts&action=edit&id=<?= $post['id'] ?>" 
                   class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>Edit Post
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Copy to clipboard script -->
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Link copied to clipboard!');
    }, function(err) {
        console.error('Could not copy text: ', err);
        // Fallback for older browsers
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            alert('Link copied to clipboard!');
        } catch (err) {
            alert('Failed to copy link');
        }
        document.body.removeChild(textArea);
    });
}
</script>
