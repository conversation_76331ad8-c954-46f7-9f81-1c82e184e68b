<?php
/**
 * Page Management Class
 * Handles dynamic page content, templates, and routing
 */

class PageManager {
    private $db;
    private $twig;
    private $seo;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->initializeTwig();
    }
    
    private function initializeTwig() {
        // Initialize Twig if not already done
        if (!class_exists('Twig\Environment')) {
            // For now, we'll use a simple template system
            // Twig will be added via Composer later
            return;
        }
        
        $loader = new \Twig\Loader\FilesystemLoader(TEMPLATES_PATH);
        $this->twig = new \Twig\Environment($loader, [
            'cache' => TWIG_CACHE ? CACHE_PATH . '/twig' : false,
            'debug' => TWIG_DEBUG,
        ]);
        
        if (TWIG_DEBUG) {
            $this->twig->addExtension(new \Twig\Extension\DebugExtension());
        }
    }
    
    public function getPage($slug) {
        $sql = "SELECT * FROM pages WHERE slug = ? AND status = 'published'";
        return $this->db->fetch($sql, [$slug]);
    }
    
    public function getPageById($id) {
        $sql = "SELECT * FROM pages WHERE id = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    public function getAllPages($status = 'published') {
        $sql = "SELECT * FROM pages WHERE status = ? ORDER BY created_at DESC";
        return $this->db->fetchAll($sql, [$status]);
    }
    
    public function createPage($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert('pages', $data);
    }
    
    public function updatePage($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->update('pages', $data, 'id = ?', [$id]);
    }
    
    public function deletePage($id) {
        return $this->db->delete('pages', 'id = ?', [$id]);
    }
    
    public function renderPage($pageData, $templateData = []) {
        // Initialize SEO for this page
        $this->seo = new SEOManager($pageData);
        
        // Prepare template data
        $data = array_merge([
            'page' => $pageData,
            'site' => [
                'name' => SITE_NAME,
                'tagline' => SITE_TAGLINE,
                'url' => SITE_URL,
                'base_url' => BASE_URL
            ],
            'seo' => $this->seo
        ], $templateData);
        
        // If Twig is available, use it
        if ($this->twig) {
            $template = $pageData['template'] ?? 'default.twig';
            return $this->twig->render($template, $data);
        }
        
        // Fallback to PHP templates
        return $this->renderPhpTemplate($pageData, $data);
    }
    
    private function renderPhpTemplate($pageData, $data) {
        $template = $pageData['template'] ?? 'default';
        $templateFile = TEMPLATES_PATH . '/' . $template . '.php';
        
        if (!file_exists($templateFile)) {
            $templateFile = TEMPLATES_PATH . '/default.php';
        }
        
        // Extract data for use in template
        extract($data);
        
        ob_start();
        include $templateFile;
        return ob_get_clean();
    }
    
    public function getBlogPosts($limit = 10, $offset = 0) {
        $sql = "SELECT * FROM blog_posts WHERE status = 'published' 
                ORDER BY published_at DESC LIMIT ? OFFSET ?";
        return $this->db->fetchAll($sql, [$limit, $offset]);
    }
    
    public function getBlogPost($slug) {
        $sql = "SELECT * FROM blog_posts WHERE slug = ? AND status = 'published'";
        return $this->db->fetch($sql, [$slug]);
    }
    
    public function createBlogPost($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        if (!isset($data['published_at']) && $data['status'] === 'published') {
            $data['published_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->db->insert('blog_posts', $data);
    }
    
    public function updateBlogPost($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->update('blog_posts', $data, 'id = ?', [$id]);
    }
    
    public function getSettings() {
        $sql = "SELECT setting_key, setting_value FROM settings";
        $results = $this->db->fetchAll($sql);
        
        $settings = [];
        foreach ($results as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
    }
    
    public function updateSetting($key, $value) {
        $sql = "INSERT INTO settings (setting_key, setting_value, updated_at) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                updated_at = VALUES(updated_at)";
        
        return $this->db->query($sql, [$key, $value, date('Y-m-d H:i:s')]);
    }
}
