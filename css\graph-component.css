/* Enhanced Graph Component Styles - Professional UI with modern aesthetics */

/* Container */
.graph-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 550px;
  overflow: hidden;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  background: linear-gradient(135deg, rgba(25, 25, 35, 0.95) 0%, rgba(15, 15, 25, 0.98) 100%);
}

/* SVG Base */
.graph-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: visible;
}

/* Grid and Axes */
.grid-line {
  stroke: rgba(255, 255, 255, 0.06);
  stroke-width: 1;
  vector-effect: non-scaling-stroke;
}

.axis-label {
  fill: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.y-axis-title {
  fill: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.x-axis-title {
  fill: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Graph Lines */
.graph-line {
  fill: none;
  stroke-width: 3;
  stroke-linecap: round;
  stroke-linejoin: round;
  filter: url(#glow);
  stroke-dasharray: 1500;
  stroke-dashoffset: 1500;
}

.adzeta-line {
  stroke: #FF479C;
  stroke-width: 4;
}

.traditional-line {
  stroke: #3B82F6;
  stroke-width: 3;
}

/* Graph Areas */
.graph-area {
  opacity: 0;
  transition: opacity 0.8s ease;
}

.adzeta-area {
  fill: url(#adzetaGradient);
}

.traditional-area {
  fill: url(#traditionalGradient);
}

.profit-difference-area {
  fill: rgba(255, 71, 156, 0.2);
  opacity: 0;
  transition: opacity 0.8s ease;
}

/* Data Points */
.data-point {
  fill: #FF479C;
  stroke: rgba(255, 255, 255, 0.95);
  stroke-width: 2.5;
  r: 5;
  opacity: 0;
  filter: url(#glow);
  transition: r 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), fill 0.3s ease, filter 0.3s ease;
}

.data-point:hover {
  r: 8;
  fill: #FF7EB6;
  filter: url(#strongGlow);
}

/* Legend */
.legend {
  opacity: 0;
  transform: translateY(5px);
}

.legend-text {
  fill: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 600;
}

.legend-icon {
  stroke-width: 3;
}

/* Data Points */
.data-points-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.data-point-container {
  position: absolute;
  transform: translate(-50%, -50%);
  opacity: 0;
  pointer-events: auto;
  cursor: pointer;
  z-index: 5;
}

.data-point-label {
  position: absolute;
  top: -28px;
  left: 50%;
  transform: translateX(-50%) scale(0);
  color: #FF479C;
  font-size: 15px;
  font-weight: 700;
  white-space: nowrap;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s, color 0.3s ease;
}

.data-point-container:hover .data-point-label {
  color: #FF7EB6;
  transform: translateX(-50%) scale(1.2);
}

/* Value Unlocked Callout */
.value-unlocked {
  position: absolute;
  background: rgba(255, 71, 156, 0.15);
  border: 1.5px solid rgba(255, 71, 156, 0.6);
  border-radius: 12px;
  padding: 12px 16px;
  color: #FF479C;
  font-size: 16px;
  font-weight: 700;
  pointer-events: none;
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.5s ease, transform 0.5s ease;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2), 0 0 16px rgba(255, 71, 156, 0.15);
  z-index: 10;
  text-align: center;
  min-width: 180px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Tooltip */
.graph-tooltip {
  position: absolute;
  background: rgba(20, 20, 28, 0.95);
  border: 1.5px solid rgba(255, 71, 156, 0.3);
  border-radius: 12px;
  padding: 16px 20px;
  font-size: 14px;
  color: white;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.25s ease, transform 0.25s ease;
  z-index: 100;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  max-width: 260px;
  transform: translate(-50%, -110%);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.graph-tooltip.visible {
  opacity: 1;
  transform: translate(-50%, -100%);
}

.tooltip-title {
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.98);
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 8px;
}

.tooltip-value {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  align-items: center;
}

.tooltip-label {
  color: rgba(255, 255, 255, 0.75);
  font-weight: 500;
}

.tooltip-adzeta {
  color: #FF479C;
  font-weight: 700;
  margin-left: 16px;
  font-size: 15px;
}

.tooltip-traditional {
  color: #3B82F6;
  font-weight: 700;
  margin-left: 16px;
  font-size: 15px;
}

.tooltip-advantage {
  color: #FF479C;
  font-weight: 700;
  margin-left: 16px;
  font-size: 15px;
}

.tooltip-revenue {
  color: #10B981;
  font-weight: 700;
  margin-left: 16px;
  font-size: 15px;
}

.tooltip-note {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 10px;
  text-align: center;
  font-style: italic;
  padding-top: 6px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover Area */
.graph-hover-area {
  position: absolute;
  top: 50px;
  left: 50px;
  width: calc(100% - 100px);
  height: calc(100% - 100px);
  cursor: crosshair;
  z-index: 4;
}

/* Animations */
.graph-container.animate .traditional-line {
  animation: drawLine 2.2s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.graph-container.animate .adzeta-line {
  animation: drawLine 2.8s cubic-bezier(0.34, 1.56, 0.64, 1) 0.3s forwards;
}

.graph-container.animate .traditional-area {
  animation: fadeInArea 1.8s ease-in-out 1.8s forwards;
}

.graph-container.animate .adzeta-area {
  animation: fadeInArea 1.8s ease-in-out 2.3s forwards;
}

.graph-container.animate .profit-difference-area {
  animation: fadeInArea 1.8s ease-in-out 2.8s forwards;
}

.graph-container.animate .legend {
  animation: fadeInLegend 0.8s ease-in-out 0.8s forwards;
}

.graph-container.animate .data-point {
  animation: fadeInPoint 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.graph-container.animate .data-point-container {
  animation: fadeInContainer 0.6s ease-in-out forwards;
}

.graph-container.animate .value-unlocked {
  animation: fadeInCallout 0.7s cubic-bezier(0.34, 1.56, 0.64, 1) 3.8s forwards;
}

@keyframes drawLine {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes fadeInArea {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.4;
  }
}

@keyframes fadeInLegend {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInPoint {
  0% {
    opacity: 0;
    r: 0;
  }
  70% {
    r: 6;
  }
  100% {
    opacity: 1;
    r: 5;
  }
}

@keyframes fadeInContainer {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeInCallout {
  0% {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Data Point Animation Delays */
.graph-container.animate .data-point:nth-child(1) {
  animation-delay: 3.0s;
}

.graph-container.animate .data-point-container:nth-child(1) {
  animation-delay: 3.0s;
}

.graph-container.animate .data-point:nth-child(2) {
  animation-delay: 3.2s;
}

.graph-container.animate .data-point-container:nth-child(2) {
  animation-delay: 3.2s;
}

.graph-container.animate .data-point:nth-child(3) {
  animation-delay: 3.4s;
}

.graph-container.animate .data-point-container:nth-child(3) {
  animation-delay: 3.4s;
}

.graph-container.animate .data-point:nth-child(4) {
  animation-delay: 3.6s;
}

.graph-container.animate .data-point-container:nth-child(4) {
  animation-delay: 3.6s;
}

.graph-container.animate .data-point:nth-child(5) {
  animation-delay: 3.8s;
}

.graph-container.animate .data-point-container:nth-child(5) {
  animation-delay: 3.8s;
}

.graph-container.animate .data-point:nth-child(6) {
  animation-delay: 4.0s;
}

.graph-container.animate .data-point-container:nth-child(6) {
  animation-delay: 4.0s;
}

/* Hover Effects */
.data-point-container:hover .data-point {
  r: 8;
  fill: #FF7EB6;
  filter: url(#strongGlow);
}

.data-point-container:hover .data-point-label {
  transform: translateX(-50%) scale(1.2);
  color: #FF7EB6;
}

/* Header Style */
.graph-header {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 5;
  opacity: 0;
  transform: translateY(-10px);
  animation: fadeInHeader 0.8s ease-in-out 0.5s forwards;
}

.graph-title {
  font-size: 18px;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
}

.graph-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
}

@keyframes fadeInHeader {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse Animation for Value Callout */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 71, 156, 0.4);
  }
  70% {
    box-shadow: 0 0 0 12px rgba(255, 71, 156, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 71, 156, 0);
  }
}

.value-unlocked {
  animation-name: pulse;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-delay: 4.5s;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .graph-container {
    min-height: 500px;
  }
  
  .graph-title {
    font-size: 16px;
  }
  
  .graph-subtitle {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .graph-container {
    min-height: 400px;
  }

  .axis-label {
    font-size: 11px;
  }

  .y-axis-title, .x-axis-title {
    font-size: 12px;
  }

  .legend-text {
    font-size: 12px;
  }

  .data-point-label {
    font-size: 13px;
    top: -24px;
  }

  .graph-tooltip {
    font-size: 13px;
    padding: 12px 16px;
    max-width: 220px;
  }

  .tooltip-title {
    font-size: 14px;
  }
  
  .value-unlocked {
    font-size: 14px;
    padding: 10px 14px;
  }
}

@media (max-width: 576px) {
  .graph-container {
    min-height: 350px;
  }

  .axis-label {
    font-size: 10px;
  }

  .y-axis-title, .x-axis-title {
    font-size: 11px;
  }
  
  .data-point {
    r: 4;
  }
  
  .data-point:hover {
    r: 6;
  }
  
  .data-point-label {
    font-size: 12px;
    top: -20px;
  }
  
  .value-unlocked {
    font-size: 13px;
    padding: 8px 12px;
  }
}