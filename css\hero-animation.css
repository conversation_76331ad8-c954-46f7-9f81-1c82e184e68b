
.hero-animation-container {
    position: relative;
    width: 100%;
    height: 500px;
    margin: 30px auto 50px;
    padding-bottom: 30px;
}


.animation-element {
    position: absolute;
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: visible;
}


.adzeta-ai-center {
    top: 10%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(0deg);
    width: 180px;
    height: 180px;
    z-index: 10;
    animation: professional-glow 4s infinite ease-in-out;
    filter: drop-shadow(0 0 20px rgba(233, 88, 161, 0.5));
}


.adzeta-ai-left {
    top: 40%;
    left: 10%;
    transform: translate(-50%, -50%);
    width: 260px;
    height: 260px;
    z-index: 5;
}

.adzeta-ai-right {
    top: 45%;
    right: 5%;
    transform: translate(50%, -50%);
    width: 270px;
    height: 270px;
    z-index: 5;
}


.adzeta-ai-bottom {
    bottom: 22%;
    left: 50%;
    transform: translate(-50%, 0);
    width: 160px;
    height: 160px;
    z-index: 5;
}


.connection-line {
    position: absolute;
    z-index: 1;
    overflow: visible;
}


.connection-left, .connection-right, .connection-bottom {
    position: relative;
}


.connection-left::before, .connection-right::before, .connection-bottom::before {
    content: '';
    position: absolute;
    background: rgba(255, 255, 255, 0.15);
    height: 1px;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1;
}

.connection-left::after, .connection-right::after, .connection-bottom::after {
    content: '';
    position: absolute;
    background: linear-gradient(90deg, rgba(233, 88, 161, 0.8), rgba(143, 118, 245, 0.8));
    height: 2px;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 2;
    transform-origin: left center;
    animation: apple-circuit-flow 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);
    box-shadow: 0 0 8px rgba(233, 88, 161, 0.6);
    border-radius: 4px;
}

.connection-left {
    top: 10%;
    left: 25%;
    width: 30%;
    height: 2px;
    transform: rotate(-35deg);
    transform-origin: top right;
}

.connection-right {
    top: 13%;
    left: 50%;
    width: 30%;
    height: 2px;
    transform: rotate(35deg);
    transform-origin: top left;
}




.connection-bottom {
    top: 20%;
    left: 50%;
    height: 40%;
    width: 2px;
    transform: translateX(-50%);
}

.connection-bottom::before {
    width: 1px;
    height: 100%;
}

.connection-bottom::after {
    width: 2px;
    height: 100%;
    background: linear-gradient(to bottom, rgba(233, 88, 161, 0.8), rgba(143, 118, 245, 0.8));
    animation: apple-circuit-flow-vertical 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);
}


.data-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0);
    z-index: 3;
    border-radius: 50%;
    opacity: 0;
    mix-blend-mode: screen;
    box-shadow:
        0 0 0 1px rgba(255, 255, 255, 0.95),
        0 0 8px 2px rgba(233, 88, 161, 0.9),
        0 0 16px 4px rgba(143, 118, 245, 0.6);
    backdrop-filter: blur(1px);
    -webkit-backdrop-filter: blur(1px);
}

.data-particle::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0) 70%);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.7;
}

.particle-left-1 {
    top: 35%;
    left: 25%;
    animation: apple-particle-left 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);
}

.particle-left-2 {
    top: 35%;
    left: 25%;
    animation: apple-particle-left 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1) 1s;
}

.particle-right-1 {
    top: 35%;
    right: 15%;
    animation: apple-particle-right 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);
}

.particle-right-2 {
    top: 35%;
    right: 15%;
    animation: apple-particle-right 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1) 1s;
}

.particle-bottom-1 {
    top: 20%;
    left: 50%;
    animation: apple-particle-bottom 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1);
}

.particle-bottom-2 {
    top: 20%;
    left: 50%;
    animation: apple-particle-bottom 3s infinite cubic-bezier(0.645, 0.045, 0.355, 1) 1s;
}


.element-label {
    position: absolute;
    font-size: 11px;
    font-weight: 500;
    color: rgba(233, 88, 161, 0.85);
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    width: 100%;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    z-index: 6;
    opacity: 0.9;
}

.label-left {
     top: 180px;
    left: 25px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(-30deg);
}

.label-right {
        top: 160px;
    right: 55px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(35deg);
}


.label-center {
    top: 30%;
    left: 0;
    width: 100%;
    margin: 0;
    transform: rotate(0deg);
    color: rgba(255, 255, 255, 0.9);
    font-size: 10px;
    font-weight: 700;
    letter-spacing: 2px;
    text-shadow: 0 0 8px rgba(233, 88, 161, 0.9);
    mix-blend-mode: overlay;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(233, 88, 161, 0.7));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    animation: glow 2s infinite ease-in-out;
}

@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 8px rgba(233, 88, 161, 0.7);
    }
    50% {
        text-shadow: 0 0 15px rgba(233, 88, 161, 1);
    }
}

.label-bottom {
    bottom: -40px;
    left: 45px;
    width: 100%;
    margin: 0;
    font-size: 10px;
    opacity: 0.8;
    transform: rotate(-30deg);
}


@keyframes pulsate {
    0% {
        transform: translate(-50%, -50%) scale(1);
        filter: brightness(1) drop-shadow(0 0 10px rgba(233, 88, 161, 0.3));
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        filter: brightness(1.2) drop-shadow(0 0 20px rgba(233, 88, 161, 0.6));
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        filter: brightness(1) drop-shadow(0 0 10px rgba(233, 88, 161, 0.3));
    }
}

@keyframes professional-glow {
    0% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
        filter: brightness(1) drop-shadow(0 0 20px rgba(233, 88, 161, 0.4));
    }
    50% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1.04) translateY(-5px);
        filter: brightness(1.15) drop-shadow(0 0 30px rgba(233, 88, 161, 0.7));
    }
    100% {
        transform: translate(-50%, -50%) rotate(0deg) scale(1);
        filter: brightness(1) drop-shadow(0 0 20px rgba(233, 88, 161, 0.4));
    }
}


@media (max-width: 767px) {
    @keyframes professional-glow {
        0% {
            transform: translate(-50%, -50%) rotate(0deg) scale(1);
            filter: brightness(1) drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));
        }
        50% {
            transform: translate(-50%, -50%) rotate(0deg) scale(1.03) translateY(-3px);
            filter: brightness(1.12) drop-shadow(0 0 25px rgba(233, 88, 161, 0.7));
        }
        100% {
            transform: translate(-50%, -50%) rotate(0deg) scale(1);
            filter: brightness(1) drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));
        }
    }
}

@keyframes float-left {
    0% {
        transform: translate(-50%, -50%);
        filter: brightness(1);
    }
    25% {
        transform: translate(-50%, -55%) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(-45%, -50%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(-50%, -45%) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(-50%, -50%);
        filter: brightness(1);
    }
}

@keyframes float-right {
    0% {
        transform: translate(50%, -50%);
        filter: brightness(1);
    }
    25% {
        transform: translate(50%, -45%) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(45%, -50%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(50%, -55%) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(50%, -50%);
        filter: brightness(1);
    }
}

@keyframes float-bottom {
    0% {
        transform: translate(-50%, 0);
        filter: brightness(1);
    }
    25% {
        transform: translate(-55%, 0) scale(1.02);
        filter: brightness(1.1);
    }
    50% {
        transform: translate(-50%, -5%) scale(1.04);
        filter: brightness(1.15);
    }
    75% {
        transform: translate(-45%, 0) scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: translate(-50%, 0);
        filter: brightness(1);
    }
}

@keyframes apple-circuit-flow {
    0% {
        transform: scaleX(0);
        opacity: 0;
    }
    5% {
        opacity: 1;
    }
    50% {
        transform: scaleX(1);
        opacity: 1;
    }
    50.1% {
        transform: scaleX(1);
        transform-origin: right center;
    }
    100% {
        transform: scaleX(0);
        transform-origin: right center;
        opacity: 0;
    }
}

@keyframes apple-circuit-flow-vertical {
    0% {
        transform: scaleY(0);
        opacity: 0;
    }
    5% {
        opacity: 1;
    }
    50% {
        transform: scaleY(1);
        opacity: 1;
    }
    50.1% {
        transform: scaleY(1);
        transform-origin: center bottom;
    }
    100% {
        transform: scaleY(0);
        transform-origin: center bottom;
        opacity: 0;
    }
}

@keyframes apple-particle-left {
    0%, 15% {
        opacity: 0;
        transform: translate(0, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    20% {
        opacity: 1;
        transform: translate(30px, -30px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    30% {
        opacity: 1;
        transform: translate(60px, -60px) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    40% {
        opacity: 1;
        transform: translate(90px, -90px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    45%, 100% {
        opacity: 0;
        transform: translate(120px, -120px) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}

@keyframes apple-particle-right {
    0%, 15% {
        opacity: 0;
        transform: translate(0, 0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    20% {
        opacity: 1;
        transform: translate(-30px, -30px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    30% {
        opacity: 1;
        transform: translate(-60px, -60px) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    40% {
        opacity: 1;
        transform: translate(-90px, -90px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    45%, 100% {
        opacity: 0;
        transform: translate(-120px, -120px) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}

@keyframes apple-particle-bottom {
    0%, 15% {
        opacity: 0;
        transform: translateY(0) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
    20% {
        opacity: 1;
        transform: translateY(40px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    30% {
        opacity: 1;
        transform: translateY(80px) scale(1.2);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 1),
            0 0 12px 3px rgba(233, 88, 161, 1),
            0 0 24px 6px rgba(143, 118, 245, 0.8);
    }
    40% {
        opacity: 1;
        transform: translateY(120px) scale(0.8);
        box-shadow:
            0 0 0 1px rgba(255, 255, 255, 0.95),
            0 0 8px 2px rgba(233, 88, 161, 0.9),
            0 0 16px 4px rgba(143, 118, 245, 0.6);
    }
    45%, 100% {
        opacity: 0;
        transform: translateY(160px) scale(0.2);
        box-shadow:
            0 0 0 0 rgba(255, 255, 255, 0),
            0 0 0 0 rgba(233, 88, 161, 0),
            0 0 0 0 rgba(143, 118, 245, 0);
    }
}


@media (max-width: 991px) {
    .hero-animation-container {
        height: 450px;
    }

    .adzeta-ai-center {
        width: 180px;
        height: 180px;
        top: 20%;
        transform: translate(-50%, -50%) rotate(0deg);
        filter: drop-shadow(0 0 15px rgba(233, 88, 161, 0.4));
        animation: professional-glow 4s infinite ease-in-out;
    }

    .adzeta-ai-left,
    .adzeta-ai-right{
        width: 225px;
        height: 225px;
    }


    .adzeta-ai-bottom {
        width: 150px;
        height: 150px;
    }


    .adzeta-ai-left {
        left: 14%;
		top: 55%;
    }

    .adzeta-ai-right {
        right: 14%;
		top:60%;
    }

    .connection-left {
        top: 15%;
        left: 22%;
        width: 32%;
    }

    .connection-right {
        top: 15%;
        right: 22%;
        width: 32%;
    }

    .label-center {
        font-size: 10px;
        top: 30%;
        transform: rotate(0deg);
    }

    .label-left {
        top: 150px;
        left: 22px;
        font-size: 9px;
    }

    .label-right {
        top: 125px;
        right: 40px;
        font-size: 9px;
    }

    .label-bottom {
        bottom: -30px;
        left: 35px;
        font-size: 9px;
    }
}

@media (max-width: 767px) {
    .hero-animation-container {
        height: 380px;
        padding-bottom: 40px;
    }

    .adzeta-ai-bottom {
        bottom: 25%;
    }

    .adzeta-ai-center {
        width: 150px;
        height: 150px;
        top: 20%;
        transform: translate(-50%, -50%) rotate(0deg);
        filter: drop-shadow(0 0 12px rgba(233, 88, 161, 0.4));
        animation: professional-glow 4s infinite ease-in-out;
    }

    .adzeta-ai-left,
    .adzeta-ai-right {
        width: 140px;
        height: 140px;
    }
	
	
    .adzeta-ai-bottom {
        width: 110px;
        height: 110px;
    }

    .adzeta-ai-left {
        left: 15%;
		top:40%;
    }

    .adzeta-ai-right {
        right: 15%;
		top:40%;
    }

    .connection-left {
        top: 15%;
        left: 25%;
        width: 30%;
    }

    .connection-right {
        top: 15%;
        right: 25%;
        width: 30%;
    }

    .element-label {
        font-size: 8px;
        letter-spacing: 1px;
    }

    .label-center {
        font-size: 9px;
        letter-spacing: 1.5px;
        top: 30%;
        transform: rotate(0deg);
    }

    .label-left {
        top: 90px;
        left: 20px;
        font-size: 8px;
    }

    .label-right {
        top: 85px;
        right: 20px;
        font-size: 8px;
    }

    .label-bottom {
        bottom: -20px;
        left: 30px;
        font-size: 8px;
    }

    .data-particle {
        width: 8px;
        height: 8px;
    }

    .particle-left-1 {
        top: 48%;
        left: 20%;
    }

    .particle-left-2 {
        top: 40%;
        left: 25%;
    }

    .particle-right-1 {
        top: 33%;
        right: 20%;
    }

    .particle-right-2 {
        top: 30%;
        right: 25%;
    }
}
