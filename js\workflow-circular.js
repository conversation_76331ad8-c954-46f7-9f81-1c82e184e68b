/**
 * Circular Workflow Animation and Interaction
 * Apple-inspired design with sleek animations and interactive elements
 */

document.addEventListener('DOMContentLoaded', function() {
    initCircularWorkflow();
});

function initCircularWorkflow() {
    // Get all workflow cards
    const cards = document.querySelectorAll('.workflow-card');
    
    // Add hover effects and interactions
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Bring the card to the front
            this.style.zIndex = '30';
            
            // Add a subtle pulse animation
            this.style.animation = 'pulse 2s infinite';
            
            // Highlight the corresponding connection path
            highlightConnectionPath(this);
        });
        
        card.addEventListener('mouseleave', function() {
            // Reset z-index
            this.style.zIndex = '10';
            
            // Remove the pulse animation
            this.style.animation = '';
            
            // Reset connection path highlight
            resetConnectionPaths();
        });
    });
    
    // Create and position data particles dynamically
    createDataParticles();
    
    // Initialize the feedback loop animation
    initFeedbackLoop();
}

function highlightConnectionPath(card) {
    // Get the card's position in the workflow
    const cardIndex = Array.from(document.querySelectorAll('.workflow-card')).indexOf(card);
    
    // Get all connection paths
    const paths = document.querySelectorAll('.connection-container path');
    
    // Highlight the relevant path
    if (cardIndex < paths.length - 1) {
        paths[0].style.strokeOpacity = '0.3';
        paths[1].style.strokeOpacity = '0.3';
        
        // Determine which segment to highlight based on card index
        const segmentStart = cardIndex * (1 / (document.querySelectorAll('.workflow-card').length - 1));
        const segmentEnd = (cardIndex + 1) * (1 / (document.querySelectorAll('.workflow-card').length - 1));
        
        // Create a highlight effect using stroke-dasharray and stroke-dashoffset
        const pathLength = paths[0].getTotalLength();
        paths[0].style.strokeDasharray = pathLength;
        paths[0].style.strokeDashoffset = pathLength * (1 - segmentEnd);
        paths[0].style.strokeOpacity = '1';
        paths[0].style.strokeWidth = '3';
    } else if (cardIndex === paths.length - 1) {
        // Highlight the feedback loop path
        paths[1].style.strokeOpacity = '1';
        paths[1].style.strokeWidth = '2.5';
        paths[1].style.strokeDasharray = '6,3';
    }
}

function resetConnectionPaths() {
    // Get all connection paths
    const paths = document.querySelectorAll('.connection-container path');
    
    // Reset all paths to their original state
    paths.forEach(path => {
        path.style.strokeOpacity = '0.7';
        path.style.strokeDasharray = path.classList.contains('feedback') ? '4,4' : 'none';
        path.style.strokeDashoffset = '0';
        path.style.strokeWidth = path.classList.contains('feedback') ? '1.5' : '2';
    });
}

function createDataParticles() {
    // Define particle types and their animations
    const particleTypes = [
        { class: 'client-particle', animation: 'moveParticle1' },
        { class: 'adzeta-particle', animation: 'moveParticle2' },
        { class: 'adzeta-particle', animation: 'moveParticle3' },
        { class: 'google-particle', animation: 'moveParticle4' },
        { class: 'google-particle', animation: 'moveParticle5' },
        { class: 'results-particle', animation: 'moveParticle6' }
    ];
    
    // Create multiple particles for each type with staggered delays
    particleTypes.forEach((type, index) => {
        for (let i = 0; i < 3; i++) {
            const particle = document.createElement('div');
            particle.className = `data-particle ${type.class}`;
            particle.style.animation = `${type.animation} 2s infinite ${i * 0.7}s`;
            document.querySelector('.workflow-circular-container').appendChild(particle);
        }
    });
    
    // Create feedback loop particles
    const feedbackTypes = ['client-particle', 'adzeta-particle', 'results-particle'];
    feedbackTypes.forEach((type, index) => {
        const particle = document.createElement('div');
        particle.className = `data-particle ${type}`;
        particle.style.animation = `moveFeedback 6s infinite ${index * 2}s`;
        document.querySelector('.workflow-circular-container').appendChild(particle);
    });
}

function initFeedbackLoop() {
    // Add subtle animation to the feedback loop text
    const feedbackText = document.querySelector('.feedback-text');
    if (feedbackText) {
        feedbackText.style.animation = 'pulse 4s infinite';
    }
    
    // Make the feedback loop interactive
    const feedbackLoop = document.querySelector('.feedback-loop');
    if (feedbackLoop) {
        feedbackLoop.addEventListener('mouseenter', function() {
            // Highlight the feedback path
            const feedbackPath = document.querySelectorAll('.connection-container path')[1];
            feedbackPath.style.strokeOpacity = '1';
            feedbackPath.style.strokeWidth = '2.5';
            feedbackPath.style.strokeDasharray = '6,3';
            
            // Add a glow effect to the feedback loop
            this.style.boxShadow = '0 0 15px rgba(125, 122, 255, 0.3)';
        });
        
        feedbackLoop.addEventListener('mouseleave', function() {
            // Reset the feedback path
            const feedbackPath = document.querySelectorAll('.connection-container path')[1];
            feedbackPath.style.strokeOpacity = '0.7';
            feedbackPath.style.strokeWidth = '1.5';
            feedbackPath.style.strokeDasharray = '4,4';
            
            // Remove the glow effect
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        });
    }
}
