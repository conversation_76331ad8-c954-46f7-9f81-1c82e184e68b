/* Page Title Background CSS */

/* Override for the cover-background class */
.cover-background {
    position: relative;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    z-index: 0;
}

/* Specific style for the page title section */
.page-title-big-typography {
    background-color: transparent !important;
}

/* Make the gradient container more transparent */
.page-title-gradient-container {
    opacity: 0.3 !important;
}

/* Ensure the diagonal gradient is semi-transparent */
.diagonal-gradient {
    background: linear-gradient(135deg, rgba(31, 33, 42, 0.3) 0%, rgba(31, 33, 42, 0.2) 100%) !important;
}

/* Ensure the container with text has higher z-index */
.page-title-big-typography .container {
    position: relative;
    z-index: 10 !important;
}

/* Specific style for the free-ltv-bg.png background */
.free-ltv-bg {
    background-image: url('../images/free-ltv-bg.png') !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}
