<?php

namespace AdZetaAdmin\Controllers;

use AdZetaAdmin\Models\BlogPost;
use AdZetaAdmin\Models\Category;
use AdZetaAdmin\Models\Tag;
use PDO;

class BlogController
{
    private PDO $db;
    private BlogPost $blogPost;
    private Category $category;
    private Tag $tag;

    public function __construct(PDO $db)
    {
        $this->db = $db;
        $this->blogPost = new BlogPost($db);
        $this->category = new Category($db);
        $this->tag = new Tag($db);
    }

    /**
     * Display blog post list (admin)
     */
    public function index(): array
    {
        $page = (int) ($_GET['page'] ?? 1);
        $status = $_GET['status'] ?? null;
        $category_id = $_GET['category_id'] ?? null;
        $search = $_GET['search'] ?? null;

        $options = [
            'page' => $page,
            'limit' => 20,
            'status' => $status,
            'category_id' => $category_id,
            'search' => $search
        ];

        $posts = $this->blogPost->getAll($options);
        $totalPosts = $this->blogPost->getCount($options);
        $totalPages = ceil($totalPosts / $options['limit']);

        $categories = $this->category->getAll();

        return [
            'posts' => $posts,
            'categories' => $categories,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_posts' => $totalPosts,
                'per_page' => $options['limit']
            ],
            'filters' => [
                'status' => $status,
                'category_id' => $category_id,
                'search' => $search
            ]
        ];
    }

    /**
     * Show create post form
     */
    public function create(): array
    {
        $categories = $this->category->getAll();
        $tags = $this->tag->getAll();

        return [
            'categories' => $categories,
            'tags' => $tags,
            'post' => null
        ];
    }

    /**
     * Store new blog post
     */
    public function store(): array
    {
        try {
            $data = $this->validatePostData($_POST);
            
            // Get current user ID (you'll need to implement user session management)
            $data['author_id'] = $_SESSION['user_id'] ?? 1; // Default to admin for now
            
            $postId = $this->blogPost->create($data);
            
            return [
                'success' => true,
                'message' => 'Blog post created successfully',
                'post_id' => $postId,
                'redirect' => "/admin/blog/edit/{$postId}"
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error creating blog post: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Show edit post form
     */
    public function edit(int $id): array
    {
        $post = $this->blogPost->getById($id);
        
        if (!$post) {
            throw new \Exception('Blog post not found');
        }

        $categories = $this->category->getAll();
        $tags = $this->tag->getAll();

        return [
            'post' => $post,
            'categories' => $categories,
            'tags' => $tags
        ];
    }

    /**
     * Update blog post
     */
    public function update(int $id): array
    {
        try {
            $post = $this->blogPost->getById($id);
            
            if (!$post) {
                throw new \Exception('Blog post not found');
            }

            $data = $this->validatePostData($_POST);
            
            $success = $this->blogPost->update($id, $data);
            
            if ($success) {
                return [
                    'success' => true,
                    'message' => 'Blog post updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'No changes were made'
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error updating blog post: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete blog post
     */
    public function delete(int $id): array
    {
        try {
            $post = $this->blogPost->getById($id);
            
            if (!$post) {
                throw new \Exception('Blog post not found');
            }

            $success = $this->blogPost->delete($id);
            
            if ($success) {
                return [
                    'success' => true,
                    'message' => 'Blog post deleted successfully',
                    'redirect' => '/admin/blog'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to delete blog post'
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error deleting blog post: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Display single blog post (frontend)
     */
    public function show(string $slug): array
    {
        $post = $this->blogPost->getBySlug($slug);
        
        if (!$post) {
            throw new \Exception('Blog post not found', 404);
        }

        // Get related posts
        $relatedPosts = [];
        if ($post['category_id']) {
            $relatedPosts = $this->category->getPosts($post['category_id'], ['limit' => 3]);
            // Remove current post from related posts
            $relatedPosts = array_filter($relatedPosts, function($p) use ($post) {
                return $p['id'] !== $post['id'];
            });
        }

        return [
            'post' => $post,
            'related_posts' => array_slice($relatedPosts, 0, 3)
        ];
    }

    /**
     * Display blog index (frontend)
     */
    public function blogIndex(): array
    {
        $page = (int) ($_GET['page'] ?? 1);
        $category = $_GET['category'] ?? null;
        $tag = $_GET['tag'] ?? null;
        $search = $_GET['search'] ?? null;

        $options = [
            'page' => $page,
            'limit' => 10,
            'status' => 'published'
        ];

        // Filter by category
        if ($category) {
            $categoryData = $this->category->getBySlug($category);
            if ($categoryData) {
                $options['category_id'] = $categoryData['id'];
            }
        }

        // Filter by tag
        if ($tag) {
            $tagData = $this->tag->getBySlug($tag);
            if ($tagData) {
                // Get posts by tag
                $posts = $this->tag->getPosts($tagData['id'], $options);
                $totalPosts = $this->tag->getPostCount($tagData['id']);
            } else {
                $posts = [];
                $totalPosts = 0;
            }
        } else {
            // Filter by search
            if ($search) {
                $options['search'] = $search;
            }

            $posts = $this->blogPost->getAll($options);
            $totalPosts = $this->blogPost->getCount($options);
        }

        $totalPages = ceil($totalPosts / $options['limit']);

        // Get sidebar data
        $categories = $this->category->getAll();
        $popularTags = $this->tag->getPopular(10);

        return [
            'posts' => $posts,
            'categories' => $categories,
            'popular_tags' => $popularTags,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_posts' => $totalPosts,
                'per_page' => $options['limit']
            ],
            'filters' => [
                'category' => $category,
                'tag' => $tag,
                'search' => $search
            ],
            'current_category' => $category ? $this->category->getBySlug($category) : null,
            'current_tag' => $tag ? $this->tag->getBySlug($tag) : null
        ];
    }

    /**
     * Validate post data
     */
    private function validatePostData(array $data): array
    {
        $errors = [];

        // Required fields
        if (empty($data['title'])) {
            $errors[] = 'Title is required';
        }

        if (empty($data['content']) && empty($data['content_blocks'])) {
            $errors[] = 'Content is required';
        }

        if (!empty($errors)) {
            throw new \Exception('Validation failed: ' . implode(', ', $errors));
        }

        // Process tags
        if (!empty($data['tags'])) {
            if (is_string($data['tags'])) {
                // Convert comma-separated string to array
                $tagNames = array_map('trim', explode(',', $data['tags']));
                $tagIds = [];
                
                foreach ($tagNames as $tagName) {
                    if (!empty($tagName)) {
                        $tagIds[] = $this->tag->findOrCreate($tagName);
                    }
                }
                
                $data['tags'] = $tagIds;
            }
        }

        // Process content blocks
        if (!empty($data['content_blocks']) && is_string($data['content_blocks'])) {
            $data['content_blocks'] = json_decode($data['content_blocks'], true);
        }

        // Clean and validate data
        $cleanData = [
            'title' => trim($data['title']),
            'slug' => !empty($data['slug']) ? trim($data['slug']) : '',
            'content' => $data['content'] ?? '',
            'content_blocks' => $data['content_blocks'] ?? null,
            'excerpt' => trim($data['excerpt'] ?? ''),
            'meta_title' => trim($data['meta_title'] ?? ''),
            'meta_description' => trim($data['meta_description'] ?? ''),
            'meta_keywords' => trim($data['meta_keywords'] ?? ''),
            'focus_keyword' => trim($data['focus_keyword'] ?? ''),
            'canonical_url' => trim($data['canonical_url'] ?? ''),
            'og_image' => trim($data['og_image'] ?? ''),
            'og_title' => trim($data['og_title'] ?? ''),
            'og_description' => trim($data['og_description'] ?? ''),
            'twitter_card_type' => $data['twitter_card_type'] ?? 'summary_large_image',
            'featured_image' => trim($data['featured_image'] ?? ''),
            'category_id' => !empty($data['category_id']) ? (int) $data['category_id'] : null,
            'status' => $data['status'] ?? 'draft',
            'tags' => $data['tags'] ?? [],
            'noindex' => !empty($data['noindex']),
            'nofollow' => !empty($data['nofollow'])
        ];

        // Add author_id if provided
        if (isset($data['author_id'])) {
            $cleanData['author_id'] = (int) $data['author_id'];
        }

        return $cleanData;
    }
}
