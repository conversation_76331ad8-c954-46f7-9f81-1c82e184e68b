
.process-step-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #ffffff;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
    /* iOS-inspired shadow - subtle layered shadows for depth */
    box-shadow: 
        0 2px 5px rgba(0, 0, 0, 0.02),
        0 10px 30px rgba(0, 0, 0, 0.04),
        0 20px 40px rgba(0, 0, 0, 0.03);
    /* Subtle border for definition */
    border: 1px solid rgba(233, 88, 161, 0.05);
    overflow: hidden;
}


.process-step-circle i {
    font-size: 32px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
}


.process-step-circle:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 5px 15px rgba(0, 0, 0, 0.03),
        0 15px 35px rgba(0, 0, 0, 0.06),
        0 30px 60px rgba(0, 0, 0, 0.04);
}


.process-step-circle:hover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle at center, rgba(233, 88, 161, 0.15) 0%, rgba(143, 118, 245, 0) 70%);
    z-index: 1;
    opacity: 0;
    animation: subtle-glow 1.5s ease-in-out forwards;
}


.process-step-circle:hover i {
    transform: translate(-50%, -50%) scale(1.1);
    background: linear-gradient(135deg, #e958a1 0%, #df367d 50%, #ee5c46 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}


@keyframes subtle-glow {
    0% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.7;
    }
}


@media (max-width: 991px) {
    .process-step-circle {
        width: 100px;
        height: 100px;
    }
    
    .process-step-circle i {
        font-size: 28px;
    }
}

@media (max-width: 767px) {
    .process-step-circle {
        width: 90px;
        height: 90px;
    }
    
    .process-step-circle i {
        font-size: 24px;
    }
}
