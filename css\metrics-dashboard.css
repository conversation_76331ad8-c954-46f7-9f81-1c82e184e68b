/* Modern Metrics Dashboard Styles */

/* Pulse dot animation for verified results indicator */
.pulse-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    position: relative;
}

.pulse-dot::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: inherit;
    border-radius: inherit;
    animation: pulse-animation 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

@keyframes pulse-animation {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    50% {
        transform: scale(1.5);
        opacity: 0;
    }
    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

/* Highlight line animation for section titles */
.highlight-line {
    height: 3px;
    width: 0;
    background: linear-gradient(to right, #e958a1, #8f76f5);
    bottom: -5px;
    left: 0;
    animation: highlight-expand 1.5s cubic-bezier(0.19, 1, 0.22, 1) forwards 0.5s;
}

@keyframes highlight-expand {
    0% {
        width: 0;
    }
    100% {
        width: 100%;
    }
}

/* Metrics Dashboard Container */
.metrics-dashboard {
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metrics-dashboard:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Subtle background pattern */
.metrics-pattern {
    background-image: 
        radial-gradient(circle at 10% 20%, rgba(233, 88, 161, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(143, 118, 245, 0.05) 0%, transparent 50%),
        linear-gradient(45deg, rgba(233, 88, 161, 0.02) 25%, transparent 25%, transparent 75%, rgba(143, 118, 245, 0.02) 75%),
        linear-gradient(-45deg, rgba(233, 88, 161, 0.02) 25%, transparent 25%, transparent 75%, rgba(143, 118, 245, 0.02) 75%);
    background-size: 60% 60%, 60% 60%, 20px 20px, 20px 20px;
    background-position: 0 0, 0 0, 0 0, 10px 10px;
}

/* Metric Cards */
.metric-card {
    padding: 20px;
    border-radius: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
    border: 1px solid rgba(233, 88, 161, 0.05);
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    border-color: rgba(233, 88, 161, 0.1);
}

/* Metric Icons */
.metric-icon-wrapper {
    position: relative;
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.metric-icon i {
    font-size: 20px;
    position: relative;
    z-index: 2;
}

.metric-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(233, 88, 161, 0.2), rgba(143, 118, 245, 0.2));
    opacity: 0.5;
    z-index: 1;
}

/* Background colors for different metric icons */
.bg-light-pink {
    background-color: rgba(233, 88, 161, 0.1);
}

.bg-light-blue {
    background-color: rgba(74, 144, 226, 0.1);
}

.bg-light-purple {
    background-color: rgba(143, 118, 245, 0.1);
}

.bg-light-green {
    background-color: rgba(46, 204, 113, 0.1);
}

/* Counter Animation */
.counter-value {
    display: inline-block;
    position: relative;
}

/* Progress Bar Animation */
.progress-bar {
    position: relative;
    overflow: hidden;
    transition: width 1.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0) 0%, 
        rgba(255, 255, 255, 0.4) 50%, 
        rgba(255, 255, 255, 0) 100%);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Data Source Indicator */
.data-source-indicator {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 15px;
    margin-top: 15px;
}

.data-source-dot {
    width: 6px;
    height: 6px;
    display: inline-block;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .metric-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 767px) {
    .metric-icon {
        width: 40px;
        height: 40px;
    }
    
    .metric-icon i {
        font-size: 18px;
    }
    
    .metric-value {
        font-size: 32px !important;
    }
}

/* Animation for metrics to appear sequentially */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add this to your JavaScript to initialize counters */
/*
document.addEventListener('DOMContentLoaded', function() {
    // Initialize counters
    const counters = document.querySelectorAll('.counter-value');
    
    counters.forEach(counter => {
        const target = parseFloat(counter.getAttribute('data-count'));
        const duration = 2000; // 2 seconds
        const startTime = Date.now();
        const startValue = 0;
        
        function updateCounter() {
            const currentTime = Date.now();
            const elapsedTime = currentTime - startTime;
            
            if (elapsedTime < duration) {
                const progress = elapsedTime / duration;
                const easedProgress = 1 - Math.pow(1 - progress, 3); // Cubic ease-out
                const currentValue = startValue + (target - startValue) * easedProgress;
                
                // Format the number based on whether it's an integer or has decimals
                if (Number.isInteger(target)) {
                    counter.textContent = Math.round(currentValue);
                } else {
                    counter.textContent = currentValue.toFixed(1);
                }
                
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        }
        
        updateCounter();
    });
});
*/
