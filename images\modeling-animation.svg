<?xml version="1.0" encoding="UTF-8"?>
<svg id="modeling-svg" width="100%" height="100%" viewBox="0 0 900 600" xmlns="http://www.w3.org/2000/svg">
  <style>
    text {
      font-family: 'Proxima Nova', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .layer-title {
      font-size: 24px;
      font-weight: 700;
    }
    .node-label {
      font-size: 20px;
      font-weight: 600;
    }
    .output-text {
      font-size: 20px;
      font-weight: 700;
    }
  </style>
  <!-- Definitions for gradients and filters -->
  <defs>
    <!-- Gradient for nodes -->
    <linearGradient id="input-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8f76f5">
        <animate attributeName="stop-color" values="#8f76f5;#9f86ff;#8f76f5" dur="6s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#a58bff">
        <animate attributeName="stop-color" values="#a58bff;#b59bff;#a58bff" dur="6s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>

    <linearGradient id="hidden1-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#e958a1">
        <animate attributeName="stop-color" values="#e958a1;#f968b1;#e958a1" dur="6s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#ff68b1">
        <animate attributeName="stop-color" values="#ff68b1;#ff78c1;#ff68b1" dur="6s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>

    <linearGradient id="hidden2-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4a9eff">
        <animate attributeName="stop-color" values="#4a9eff;#5aaeFF;#4a9eff" dur="6s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#5aaeFF">
        <animate attributeName="stop-color" values="#5aaeFF;#6abeFF;#5aaeFF" dur="6s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>

    <linearGradient id="output-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2c2e3c">
        <animate attributeName="stop-color" values="#2c2e3c;#3c3e4c;#2c2e3c" dur="6s" repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#3c3e4c">
        <animate attributeName="stop-color" values="#3c3e4c;#4c4e5c;#3c3e4c" dur="6s" repeatCount="indefinite"/>
      </stop>
    </linearGradient>

    <!-- Glow filter for active elements -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>

    <!-- Particle filter -->
    <filter id="particle-blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="1.5" result="blur"/>
    </filter>
  </defs>

  <!-- Neural network layers -->
  <g class="network-layer input-layer">
    <!-- Input nodes -->
    <circle cx="180" cy="150" r="15" fill="#f5f5f7" stroke="#8f76f5" stroke-width="2" class="network-node input-node-1">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="0.5s"/>
      <animate attributeName="fill" values="#f5f5f7;#f0f4ff;#f5f5f7" dur="3s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="180" cy="250" r="15" fill="#f5f5f7" stroke="#8f76f5" stroke-width="2" class="network-node input-node-2">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="1s"/>
      <animate attributeName="fill" values="#f5f5f7;#f0f4ff;#f5f5f7" dur="3s" repeatCount="indefinite" begin="1s"/>
    </circle>
    <circle cx="180" cy="350" r="15" fill="#f5f5f7" stroke="#8f76f5" stroke-width="2" class="network-node input-node-3">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="1.5s"/>
      <animate attributeName="fill" values="#f5f5f7;#f0f4ff;#f5f5f7" dur="3s" repeatCount="indefinite" begin="1.5s"/>
    </circle>
    <circle cx="180" cy="450" r="15" fill="#f5f5f7" stroke="#8f76f5" stroke-width="2" class="network-node input-node-4">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="2s"/>
      <animate attributeName="fill" values="#f5f5f7;#f0f4ff;#f5f5f7" dur="3s" repeatCount="indefinite" begin="2s"/>
    </circle>
    <text x="180" y="80" text-anchor="middle" fill="#2c2e3c" class="layer-title">Input Layer</text>

    <!-- Input labels -->
    <text x="140" y="155" text-anchor="end" fill="#2c2e3c" class="node-label">Transactions</text>
    <text x="140" y="255" text-anchor="end" fill="#2c2e3c" class="node-label">Web Behavior</text>
    <text x="140" y="355" text-anchor="end" fill="#2c2e3c" class="node-label">Demographics</text>
    <text x="140" y="455" text-anchor="end" fill="#2c2e3c" class="node-label">Campaign Data</text>
  </g>

  <g class="network-layer hidden-layer-1">
    <!-- Hidden layer 1 -->
    <circle cx="380" cy="200" r="15" fill="#f5f5f7" stroke="#e958a1" stroke-width="2" class="network-node hidden1-node-1">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="3s"/>
      <animate attributeName="fill" values="#f5f5f7;#fff0f5;#f5f5f7" dur="3s" repeatCount="indefinite" begin="3s"/>
    </circle>
    <circle cx="380" cy="300" r="15" fill="#f5f5f7" stroke="#e958a1" stroke-width="2" class="network-node hidden1-node-2">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="3.5s"/>
      <animate attributeName="fill" values="#f5f5f7;#fff0f5;#f5f5f7" dur="3s" repeatCount="indefinite" begin="3.5s"/>
    </circle>
    <circle cx="380" cy="400" r="15" fill="#f5f5f7" stroke="#e958a1" stroke-width="2" class="network-node hidden1-node-3">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="4s"/>
      <animate attributeName="fill" values="#f5f5f7;#fff0f5;#f5f5f7" dur="3s" repeatCount="indefinite" begin="4s"/>
    </circle>
    <text x="380" y="80" text-anchor="middle" fill="#2c2e3c" class="layer-title">Hidden Layer</text>
  </g>

  <g class="network-layer hidden-layer-2">
    <!-- Hidden layer 2 -->
    <circle cx="580" cy="250" r="15" fill="#f5f5f7" stroke="#4a9eff" stroke-width="2" class="network-node hidden2-node-1">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="5s"/>
      <animate attributeName="fill" values="#f5f5f7;#f0faff;#f5f5f7" dur="3s" repeatCount="indefinite" begin="5s"/>
    </circle>
    <circle cx="580" cy="350" r="15" fill="#f5f5f7" stroke="#4a9eff" stroke-width="2" class="network-node hidden2-node-2">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="5.5s"/>
      <animate attributeName="fill" values="#f5f5f7;#f0faff;#f5f5f7" dur="3s" repeatCount="indefinite" begin="5.5s"/>
    </circle>
    <text x="580" y="80" text-anchor="middle" fill="#2c2e3c" class="layer-title">Hidden Layer</text>
  </g>

  <g class="network-layer output-layer">
    <!-- Output node -->
    <circle cx="780" cy="300" r="20" fill="#2c2e3c" stroke="#ff8cc6" stroke-width="2" class="network-node output-node">
      <animate attributeName="stroke-width" values="2;3;2" dur="3s" repeatCount="indefinite" begin="6.5s"/>
      <animate attributeName="r" values="20;22;20" dur="3s" repeatCount="indefinite" begin="6.5s"/>
    </circle>
    <text x="780" y="305" text-anchor="middle" fill="white" class="output-text">LTV</text>
    <text x="780" y="80" text-anchor="middle" fill="#2c2e3c" class="layer-title">Output</text>
  </g>

  <!-- Connections between layers -->
  <!-- Input to Hidden 1 -->
  <path d="M195 150 L 365 200" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i1-h1" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="0.5s" fill="freeze"/>
  </path>
  <path d="M195 150 L 365 300" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i1-h2" stroke-dasharray="250" stroke-dashoffset="250">
    <animate attributeName="stroke-dashoffset" from="250" to="0" dur="1.5s" begin="0.7s" fill="freeze"/>
  </path>
  <path d="M195 150 L 365 400" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i1-h3" stroke-dasharray="300" stroke-dashoffset="300">
    <animate attributeName="stroke-dashoffset" from="300" to="0" dur="1.5s" begin="0.9s" fill="freeze"/>
  </path>

  <path d="M195 250 L 365 200" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i2-h1" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="1.1s" fill="freeze"/>
  </path>
  <path d="M195 250 L 365 300" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i2-h2" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="1.3s" fill="freeze"/>
  </path>
  <path d="M195 250 L 365 400" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i2-h3" stroke-dasharray="250" stroke-dashoffset="250">
    <animate attributeName="stroke-dashoffset" from="250" to="0" dur="1.5s" begin="1.5s" fill="freeze"/>
  </path>

  <path d="M195 350 L 365 200" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i3-h1" stroke-dasharray="250" stroke-dashoffset="250">
    <animate attributeName="stroke-dashoffset" from="250" to="0" dur="1.5s" begin="1.7s" fill="freeze"/>
  </path>
  <path d="M195 350 L 365 300" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i3-h2" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="1.9s" fill="freeze"/>
  </path>
  <path d="M195 350 L 365 400" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i3-h3" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="2.1s" fill="freeze"/>
  </path>

  <path d="M195 450 L 365 200" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i4-h1" stroke-dasharray="300" stroke-dashoffset="300">
    <animate attributeName="stroke-dashoffset" from="300" to="0" dur="1.5s" begin="2.3s" fill="freeze"/>
  </path>
  <path d="M195 450 L 365 300" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i4-h2" stroke-dasharray="250" stroke-dashoffset="250">
    <animate attributeName="stroke-dashoffset" from="250" to="0" dur="1.5s" begin="2.5s" fill="freeze"/>
  </path>
  <path d="M195 450 L 365 400" fill="none" stroke="#8f76f5" stroke-width="1.5" class="network-connection i4-h3" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="2.7s" fill="freeze"/>
  </path>

  <!-- Hidden 1 to Hidden 2 -->
  <path d="M395 200 L 565 250" fill="none" stroke="#e958a1" stroke-width="1.5" class="network-connection h1-h21" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="3.0s" fill="freeze"/>
  </path>
  <path d="M395 200 L 565 350" fill="none" stroke="#e958a1" stroke-width="1.5" class="network-connection h1-h22" stroke-dasharray="250" stroke-dashoffset="250">
    <animate attributeName="stroke-dashoffset" from="250" to="0" dur="1.5s" begin="3.2s" fill="freeze"/>
  </path>

  <path d="M395 300 L 565 250" fill="none" stroke="#e958a1" stroke-width="1.5" class="network-connection h2-h21" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="3.4s" fill="freeze"/>
  </path>
  <path d="M395 300 L 565 350" fill="none" stroke="#e958a1" stroke-width="1.5" class="network-connection h2-h22" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="3.6s" fill="freeze"/>
  </path>

  <path d="M395 400 L 565 250" fill="none" stroke="#e958a1" stroke-width="1.5" class="network-connection h3-h21" stroke-dasharray="250" stroke-dashoffset="250">
    <animate attributeName="stroke-dashoffset" from="250" to="0" dur="1.5s" begin="3.8s" fill="freeze"/>
  </path>
  <path d="M395 400 L 565 350" fill="none" stroke="#e958a1" stroke-width="1.5" class="network-connection h3-h22" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="4.0s" fill="freeze"/>
  </path>

  <!-- Hidden 2 to Output -->
  <path d="M595 250 L 760 300" fill="none" stroke="#4a9eff" stroke-width="1.5" class="network-connection h21-o" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="5.0s" fill="freeze"/>
  </path>
  <path d="M595 350 L 760 300" fill="none" stroke="#4a9eff" stroke-width="1.5" class="network-connection h22-o" stroke-dasharray="200" stroke-dashoffset="200">
    <animate attributeName="stroke-dashoffset" from="200" to="0" dur="1.5s" begin="5.5s" fill="freeze"/>
  </path>

  <!-- Signal pulses (will be animated with SMIL) -->
  <circle cx="280" cy="175" r="4" fill="#8f76f5" class="signal-pulse pulse1" opacity="0">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="330" cy="250" r="4" fill="#e958a1" class="signal-pulse pulse2" opacity="0">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="480" cy="300" r="4" fill="#4a9eff" class="signal-pulse pulse3" opacity="0">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="680" cy="275" r="4" fill="#2c2e3c" class="signal-pulse pulse4" opacity="0">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="6s" repeatCount="indefinite"/>
  </circle>

  <!-- Data particles flowing through the network -->
  <!-- Input to Hidden Layer 1 particles -->
  <circle cx="0" cy="0" r="3" fill="#8f76f5" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M195 150 L 365 200" dur="1s" begin="1s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="1s"/>
  </circle>

  <circle cx="0" cy="0" r="3" fill="#8f76f5" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M195 250 L 365 300" dur="1s" begin="1.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="1.5s"/>
  </circle>

  <circle cx="0" cy="0" r="3" fill="#8f76f5" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M195 350 L 365 400" dur="1s" begin="2s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="2s"/>
  </circle>

  <circle cx="0" cy="0" r="3" fill="#8f76f5" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M195 450 L 365 300" dur="1s" begin="2.5s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="2.5s"/>
  </circle>

  <!-- Hidden Layer 1 to Hidden Layer 2 particles -->
  <circle cx="0" cy="0" r="3" fill="#e958a1" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M395 200 L 565 250" dur="1s" begin="3.2s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="3.2s"/>
  </circle>

  <circle cx="0" cy="0" r="3" fill="#e958a1" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M395 300 L 565 350" dur="1s" begin="3.7s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="3.7s"/>
  </circle>

  <circle cx="0" cy="0" r="3" fill="#e958a1" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M395 400 L 565 350" dur="1s" begin="4.2s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="4.2s"/>
  </circle>

  <!-- Hidden Layer 2 to Output particles -->
  <circle cx="0" cy="0" r="3" fill="#4a9eff" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M595 250 L 760 300" dur="1s" begin="5.2s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="5.2s"/>
  </circle>

  <circle cx="0" cy="0" r="3" fill="#4a9eff" class="data-particle" opacity="0" filter="url(#particle-blur)">
    <animateMotion path="M595 350 L 760 300" dur="1s" begin="5.7s" fill="freeze"/>
    <animate attributeName="opacity" values="0;1;0" dur="1s" begin="5.7s"/>
  </circle>

  <!-- Continuous animation for data flow -->
  <g class="continuous-flow" opacity="0">
    <animate attributeName="opacity" from="0" to="1" dur="1s" begin="7s" fill="freeze"/>

    <!-- Input to Hidden Layer 1 continuous flow -->
    <circle cx="0" cy="0" r="3" fill="#8f76f5" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M195 150 L 365 200" dur="1s" begin="8s;10s;12s;14s;16s;18s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="8s;10s;12s;14s;16s;18s" repeatCount="indefinite"/>
    </circle>

    <circle cx="0" cy="0" r="3" fill="#8f76f5" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M195 250 L 365 300" dur="1s" begin="8.5s;10.5s;12.5s;14.5s;16.5s;18.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="8.5s;10.5s;12.5s;14.5s;16.5s;18.5s" repeatCount="indefinite"/>
    </circle>

    <circle cx="0" cy="0" r="3" fill="#8f76f5" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M195 350 L 365 400" dur="1s" begin="9s;11s;13s;15s;17s;19s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="9s;11s;13s;15s;17s;19s" repeatCount="indefinite"/>
    </circle>

    <circle cx="0" cy="0" r="3" fill="#8f76f5" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M195 450 L 365 300" dur="1s" begin="9.5s;11.5s;13.5s;15.5s;17.5s;19.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="9.5s;11.5s;13.5s;15.5s;17.5s;19.5s" repeatCount="indefinite"/>
    </circle>

    <!-- Hidden Layer 1 to Hidden Layer 2 continuous flow -->
    <circle cx="0" cy="0" r="3" fill="#e958a1" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M395 200 L 565 250" dur="1s" begin="8.2s;10.2s;12.2s;14.2s;16.2s;18.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="8.2s;10.2s;12.2s;14.2s;16.2s;18.2s" repeatCount="indefinite"/>
    </circle>

    <circle cx="0" cy="0" r="3" fill="#e958a1" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M395 300 L 565 350" dur="1s" begin="8.7s;10.7s;12.7s;14.7s;16.7s;18.7s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="8.7s;10.7s;12.7s;14.7s;16.7s;18.7s" repeatCount="indefinite"/>
    </circle>

    <circle cx="0" cy="0" r="3" fill="#e958a1" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M395 400 L 565 350" dur="1s" begin="9.2s;11.2s;13.2s;15.2s;17.2s;19.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="9.2s;11.2s;13.2s;15.2s;17.2s;19.2s" repeatCount="indefinite"/>
    </circle>

    <!-- Hidden Layer 2 to Output continuous flow -->
    <circle cx="0" cy="0" r="3" fill="#4a9eff" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M595 250 L 760 300" dur="1s" begin="9s;11s;13s;15s;17s;19s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="9s;11s;13s;15s;17s;19s" repeatCount="indefinite"/>
    </circle>

    <circle cx="0" cy="0" r="3" fill="#4a9eff" opacity="0" filter="url(#particle-blur)">
      <animateMotion path="M595 350 L 760 300" dur="1s" begin="9.5s;11.5s;13.5s;15.5s;17.5s;19.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="1s" begin="9.5s;11.5s;13.5s;15.5s;17.5s;19.5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Output node activation effect -->
  <circle cx="780" cy="300" r="25" fill="none" stroke="#ff8cc6" stroke-width="1" opacity="0" class="output-activation">
    <animate attributeName="opacity" values="0;0.7;0" dur="3s" begin="6.5s;9.5s;12.5s;15.5s;18.5s" repeatCount="indefinite"/>
    <animate attributeName="r" values="20;40;20" dur="3s" begin="6.5s;9.5s;12.5s;15.5s;18.5s" repeatCount="indefinite"/>
    <animate attributeName="stroke-width" values="1;2;1" dur="3s" begin="6.5s;9.5s;12.5s;15.5s;18.5s" repeatCount="indefinite"/>
  </circle>

  <!-- Neural network processing animation -->
  <g class="processing-animation" opacity="0">
    <animate attributeName="opacity" from="0" to="0.5" dur="1s" begin="7s" fill="freeze"/>

    <!-- Processing indicators for hidden layers -->
    <circle cx="380" cy="300" r="40" fill="none" stroke="rgba(233,88,161,0.2)" stroke-width="1" stroke-dasharray="10,5">
      <animateTransform attributeName="transform" type="rotate" from="0 380 300" to="360 380 300" dur="20s" repeatCount="indefinite"/>
    </circle>

    <circle cx="580" cy="300" r="40" fill="none" stroke="rgba(74,158,255,0.2)" stroke-width="1" stroke-dasharray="15,10">
      <animateTransform attributeName="transform" type="rotate" from="360 580 300" to="0 580 300" dur="30s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Animation script -->
  <script type="text/javascript"><![CDATA[
    // This SVG uses SMIL animations and doesn't require JavaScript
    // The animations will play automatically when the SVG is loaded
  ]]></script>
</svg>